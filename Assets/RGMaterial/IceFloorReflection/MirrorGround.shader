Shader "Custom/MirrorGround"
{
    Properties {
        [PerRendererData] _MainTex ("Sprite Texture", 2D) = "white" {}
        _AddColor ("Add Color", Color) = (1,1,1,1)
        [MaterialToggle] PixelSnap ("Pixel snap", Float) = 0
        [Toggle] Gray ("Gray", Float) = 0
    }
    SubShader {
 
        Tags {
            "Queue"="Transparent"
            "IgnoreProjector"="True"
            "RenderType" = "Transparent"
            "PreviewType"="Plane"
            "CanUseSpriteAtlas"="True"
            }
        Cull Off
        Lighting Off
        ZWrite Off
        Fog { Mode Off }
        Blend One OneMinusSrcAlpha
 
        Pass {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile PIXELSNAP_ON GRAY_ON

            #include "UnityCG.cginc"
 
            struct v2f {
                float4 position : SV_POSITION;
                float2 uv : TEXCOORD0;
                fixed4 color : COLOR;
            };

            struct appdata{
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                fixed4 color : COLOR;
            };
            
            sampler2D _MainTex;
            float4 _MainTex_ST;
            fixed4 _AddColor;

            fixed3 rgb2hsv(fixed3 c)
            {
                fixed4 K = fixed4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);
                fixed4 p = lerp(fixed4(c.bg, K.wz), fixed4(c.gb, K.xy), step(c.b, c.g));
                fixed4 q = lerp(fixed4(p.xyw, c.r), fixed4(c.r, p.yzx), step(p.x, c.r));

                float d = q.x - min(q.w, q.y);
                float e = 1.0e-10;
                return fixed3(abs(q.z + (q.w - q.y) / (6.0 * d + e)), d / (q.x + e), q.x);
            }

            fixed3 hsv2rgb(fixed3 c)
            {
                fixed4 K = fixed4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);
                fixed3 p = abs(frac(c.xxx + K.xyz) * 6.0 - K.www);
                return c.z * lerp(K.xxx, clamp(p - K.xxx, 0.0, 1.0), c.y);
            }
 
            v2f vert (appdata v)
            {
                v2f o;
                o.position = UnityObjectToClipPos (v.vertex);
                o.uv = v.uv * _MainTex_ST.xy + _MainTex_ST.zw;
                o.color = v.color;
                #ifdef PIXELSNAP_ON
                o.position = UnityPixelSnap (o.position);
                #endif
                return o;
            }
 
            half4 frag(v2f i) : COLOR
            {
                half4 col = tex2D(_MainTex, i.uv) * i.color;
                #ifdef GRAY_ON
                float grey = dot(col.rgb, fixed3(0.22, 0.707, 0.071));
                col = half4(grey,grey,grey,col.a) + half4(_AddColor.rgb, 0);
                #endif
                col.rgb *= col.a;
                return col;
            }
            ENDCG
        }

        Pass{
            Stencil {
                Ref 1          //0-255
                Comp always     //default:always
                Pass IncrSat   //default:keep
                Fail keep      //default:keep
                ZFail keep     //default:keep
            }
            ColorMask 0

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile PIXELSNAP_ON
            #include "UnityCG.cginc"
 
            struct v2f {
                float4 position : SV_POSITION;
                float2 uv : TEXCOORD0;
                fixed4 color : COLOR;
            };

            struct appdata{
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                fixed4 color : COLOR;
            };
            
            sampler2D _MainTex;
            float4 _MainTex_ST;
 
            v2f vert (appdata v)
            {
                v2f o;
                o.position = UnityObjectToClipPos (v.vertex);
                o.uv = v.uv * _MainTex_ST.xy + _MainTex_ST.zw;
                o.color = v.color;
                #ifdef PIXELSNAP_ON
                o.position = UnityPixelSnap (o.position);
                #endif
                return o;
            }
 
            half4 frag(v2f i) : COLOR
            {
                half4 col = tex2D(_MainTex, i.uv) * i.color;
                clip(col.a - 0.5);
                return half4(0, 0, 0, 0);
            }
            ENDCG
        }
    }
    Fallback "Diffuse"
}
