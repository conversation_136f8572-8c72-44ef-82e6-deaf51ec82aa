// Upgrade NOTE: replaced 'mul(UNITY_MATRIX_MVP,*)' with 'UnityObjectToClipPos(*)'

Shader "Custom/WaterWave" {
    Properties {
        [PerRendererData] _MainTex ("Sprite Texture", 2D) = "white" {}
        [MaterialToggle] PixelSnap ("Pixel snap", Float) = 0
    }
    SubShader {
        Stencil {
                Ref 1          //0-255
                Comp Equal     //default:always
                Pass keep   //default:keep
                Fail keep      //default:keep
                ZFail keep     //default:keep
        }
        Tags {
            "Queue"="Transparent"
            "IgnoreProjector"="True"
            "RenderType" = "Transparent"
            "PreviewType"="Plane"
            "CanUseSpriteAtlas"="True"
            }
        Cull Off
        Lighting Off
        ZWrite Off
        Fog { Mode Off }
        Blend One OneMinusSrcAlpha
 
        Pass {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile PIXELSNAP_ON
            #include "UnityCG.cginc"
 
            struct v2f {
                float4 position : SV_POSITION;
                float2 uv : TEXCOORD0;
                fixed4 color : COLOR;
            };

            struct appdata{
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                fixed4 color : COLOR;
            };
           
            sampler2D _MainTex;
            float4 _MainTex_ST;
            float4 _MainTex_TexelSize;
 
            v2f vert (appdata v)
            {
                v2f o;
                o.position = UnityObjectToClipPos (v.vertex);
                o.uv = v.uv * _MainTex_ST.xy + _MainTex_ST.zw;
                o.color = v.color;
                #ifdef PIXELSNAP_ON
                o.position = UnityPixelSnap (o.position);
                #endif
                return o;
            }
 
            half4 frag(v2f i) : COLOR
            {
                half4 col = tex2D(_MainTex, i.uv) * i.color;
                col.rgb *= col.a;
                return col;
            }
            ENDCG
        }
    }
    Fallback "Diffuse"
}
