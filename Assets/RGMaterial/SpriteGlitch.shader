// Upgrade NOTE: replaced 'mul(UNITY_MATRIX_MVP,*)' with 'UnityObjectToClipPos(*)'

Shader "Custom/ShaderHit" {
Properties
{
    [PerRendererData] _MainTex ("Sprite Texture", 2D) = "white" {}
    _Color ("Tint", Color) = (1,1,1,1)
    _JumpDrift("Verticla Jump And Drift", Vector) = (0, 0, 0, 0)
    _Saturator ("Saturator", Float) = 1
    [MaterialToggle] PixelSnap ("Pixel snap", Float) = 0
   // [MaterialToggle] Hit ("Hit", Float) = 1
  //  [MaterialToggle] Gray ("Gray", Float) = 1
}
 
SubShader
{
    Tags
    { 
        "Queue"="Transparent" 
        "IgnoreProjector"="True" 
        "RenderType"="Transparent" 
        "PreviewType"="Plane"
        "CanUseSpriteAtlas"="True"
    }
 
    Cull Off
    Lighting Off
    ZWrite Off
    Fog { Mode Off }
    Blend One OneMinusSrcAlpha
 
    Pass
    {
    CGPROGRAM
        #pragma vertex vert
        #pragma fragment frag
        #pragma multi_compile PIXELSNAP_ON
        #include "UnityCG.cginc"
 
        struct appdata_t
        {
            float4 vertex   : POSITION;
            float4 color    : COLOR;
            float2 texcoord : TEXCOORD0;
        };
 
        struct v2f
        {
            float4 vertex   : SV_POSITION;
            fixed4 color    : COLOR;
            half2 texcoord  : TEXCOORD0;
        };
 
        fixed4 _Color;
        float4 _JumpDrift;
        float _Saturator;
 
        v2f vert(appdata_t IN)
        {
            v2f OUT;
            OUT.vertex = UnityObjectToClipPos(IN.vertex);
            OUT.texcoord = IN.texcoord;
            OUT.color = IN.color * _Color;
            #ifdef PIXELSNAP_ON
            OUT.vertex = UnityPixelSnap (OUT.vertex);
            #endif
 
            return OUT;
        }
 
        sampler2D _MainTex;
 
        fixed4 frag(v2f IN) : SV_Target
        {
            float u = IN.texcoord.x;
            float v = IN.texcoord.y;
            float jump = lerp(v, frac(v + _JumpDrift.y), _JumpDrift.x);
            float drift = sin(jump + _JumpDrift.z) * _JumpDrift.w;
        
            fixed4 c1 = tex2D(_MainTex, frac(float2(u + drift, jump))) * IN.color * _Saturator;
            fixed4 c2 = tex2D(_MainTex, frac(float2(u, jump))) * IN.color * _Saturator;
            c1.rb *= c1.a;
            c2.g *= c2.a;
            return half4(c1.r, c2.g, c1.b, c1.a);
        }
    ENDCG
    }
}
}
