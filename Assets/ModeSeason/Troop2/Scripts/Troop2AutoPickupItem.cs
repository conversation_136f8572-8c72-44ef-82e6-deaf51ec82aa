using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace ModeSeason.Troop2 {

    public class Troop2AutoPickupItem : MonoBehaviour {
        public enum ItemType { 
            Energy,
            Coin,
            Material
        }

        public ItemType itemType;
        public int value;
        public AudioClip audioClip;

        bool _picked = false;

        public bool OnPickup(RGBaseController controller) {
            if (controller != RGGameSceneManager.Inst.baseController) {
                return false;
            }

            if (itemType == ItemType.Energy) {
                (controller as Troop2CommanderController).AddEnergy(value);
                var fxObj = GameUtil.DisplayFx("ModeSeason/Troop2/Prefabs/AbilityEfx/commander_gain_energy", controller.transform.position + Vector3.up * 1);
                fxObj.transform.GetComponentInChildren<TMPro.TMP_Text>().text = "+" + value;
            }
            else if (itemType == ItemType.Coin) {
                DoPickup();
                var fxObj = GameUtil.DisplayFx("ModeSeason/Troop2/Prefabs/AbilityEfx/commander_gain_coin_1", controller.transform.position + Vector3.up * 1);
                fxObj.transform.GetComponentInChildren<TMPro.TMP_Text>().text = "+" + value;
            }

            if (audioClip != null) {
                RGMusicManager.Inst.PlayEffect(audioClip);
            }
            Destroy(gameObject);

            return true;
        }

        public void DoPickup() {
            if (_picked) {
                return;
            }
            _picked = true;
            if (itemType == ItemType.Coin) {
                Troop2BattleData.Data.AddCoin(value, "loot");
                Troop2GameModeProcess.Instance.LogPickUpItem("coin", value);
            }
        }

        public bool IsActive() {
            return true;
        }
    }
}