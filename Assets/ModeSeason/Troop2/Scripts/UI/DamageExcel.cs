using DG.Tweening;
using System.Collections.Generic;
using UnityEngine;

namespace ModeSeason.Troop2 {
    public class DamageExcel : MonoBehaviour {
        [SerializeField] private Vector2 showPosition;
        [SerializeField] private Vector2 hidePosition;
        [SerializeField]  private float XSpacing;
        [SerializeField]  private float YSpacing;
        
        [SerializeField] private CanvasGroup cg;
        [SerializeField] private Transform content;
        [SerializeField] private Transform item;

        private List<DamageExcelItem> items;
        private float itemHeight;

        public static bool NextShow;
        public static bool Show;
        public static DamageExcel Main { get; private set; }

        public FuncObj onShowStateChange;

        private void Awake() {
            Main = this;
            item.gameObject.SetActive(false);
            itemHeight = item.GetComponent<RectTransform>().sizeDelta.y;
            items = new List<DamageExcelItem>();
            Initialized();
            
            SetShow(NextShow);
            SimpleEventManager.AddEventListener<LevelRoomStartEvent>(StartListener);
        }
        
        public void StartListener(LevelRoomStartEvent levelRoomStartEvent) {
            Initialized();
            Troop2EnemyMaker.Instance.OnEnemyGetHurted += OnEnemyGetHurted;
        }

        private void OnEnemyGetHurted(EnemyGetHurt enemyGetHurt) {
            Refresh(0.3f);
        }

        public void ChangeShow() {
            SetShow(!Show);
        }

        public void SetShow(bool isShow) {
            bool move = Show != isShow;
            Show = isShow;
            Refresh(move);
            onShowStateChange?.Invoke(isShow);
        }

        private void Refresh(bool move = false) {
            transform.GetComponent<RectTransform>().anchoredPosition = !move ? (Show ? showPosition : hidePosition) : Show ? hidePosition : showPosition;
            cg.alpha = !move ? (Show ? 1 : 0) : Show ? 0 : 1;
            DOTween.Kill("cl" + gameObject.GetInstanceID());
            transform.GetComponent<RectTransform>().DOAnchorPos(Show ? showPosition : hidePosition, 0.25f).SetLink(gameObject).SetId("cl" + gameObject.GetInstanceID());
            cg.DOFade(Show ? 1 : 0, 0.25f).SetLink(gameObject).SetId("cl" + gameObject.GetInstanceID());
        }

        public void Initialized() {
            foreach (DamageExcelItem oldItem in items) {
                Destroy(oldItem.gameObject);
            }
            items.Clear();
            
            var dict = Troop2GameModeProcess.Instance.damageDict;
            foreach (var keyValue in dict) {
                var damageExcelItem = Instantiate(item.gameObject, content).GetComponent<DamageExcelItem>();
                damageExcelItem.gameObject.SetActive(true);
                damageExcelItem.GetComponent<RectTransform>().anchoredPosition = new Vector2(XSpacing, YSpacing - items.Count * itemHeight);
                damageExcelItem.Initialized(keyValue.Key);
                items.Add(damageExcelItem);
            }

            Refresh(0);
        }

        public void Refresh(float time) {
            var dict = Troop2GameModeProcess.Instance.damageDict;
            int fullValue = 0;
            foreach (var keyValue in dict) {
                fullValue = Mathf.Max(fullValue, (keyValue.Value.Item1 + keyValue.Value.Item2));
            }
            
            DOTween.Kill("item_" + gameObject.GetInstanceID());
            
            foreach (var keyValue in dict) {
                foreach (DamageExcelItem excelItem in items) {
                    if (excelItem.Mercenary == keyValue.Key) {
                        excelItem.Refresh(keyValue.Value, fullValue);
                        break;
                    }
                }
            }
            
            items.Sort((a,b) => -a.AllDamageValue.CompareTo(b.AllDamageValue));

            for (int i = 0; i < items.Count; i++) {
                items[i].GetComponent<RectTransform>().DOAnchorPosY(YSpacing - i * itemHeight, time).SetLink(items[i].gameObject).SetId("item_" + gameObject.GetInstanceID());
            }
        }

        private void OnDestroy() {
            SimpleEventManager.RemoveListener<LevelRoomStartEvent>(StartListener);
            if (Troop2EnemyMaker.Instance != null) {
                Troop2EnemyMaker.Instance.OnEnemyGetHurted -= OnEnemyGetHurted;
            }
        }
    }
}