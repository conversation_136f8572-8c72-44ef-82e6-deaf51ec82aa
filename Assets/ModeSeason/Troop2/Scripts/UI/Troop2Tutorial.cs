using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using RGScript.UI.Tutorial;

namespace ModeSeason.Troop2 {
    public class Troop2Tutorial {

        public const string StoreTutorialKey = "Troop2StoreTutorial";
        public const string BattleTutorialKey = "Troop2BattleTutorial";
        public const string StoreTutorialKey2 = "Troop2StoreTutorial2";

#if UNITY_EDITOR
        public static string[] TutorialKeys = new string[] {
            StoreTutorialKey,
            BattleTutorialKey,
            StoreTutorialKey2
        };

        public static void Reset() {
            foreach (var key in TutorialKeys) {
                StatisticData.data.RemoveRecordEventDebug(key, true);
            }
        }
#endif

        static Troop2Tutorial _instance;
        static public Troop2Tutorial Instance {
            get {
                if (_instance == null) {
                    _instance = new Troop2Tutorial();
                }
                return _instance;
            }
        }

        List<FuncObj> _cleanUps = new List<FuncObj>();

        TutorialStore tutorialStore;
        TutorialStore2 tutorialStore2;
        TutorialBattle tutorialBattle;

        public void Init() {
            if (Troop2BattleData.Data.gameState == 0) {
                if (!StatisticData.data.IsEventRecord(StoreTutorialKey)) {
                    tutorialStore = new TutorialStore();
                    tutorialStore.Init();
                    _cleanUps.Add(new FuncObj(tutorialStore, nameof(tutorialStore.CleanUp)));
                } 
                else if (!StatisticData.data.IsEventRecord(StoreTutorialKey2) && Troop2BattleData.Data.levelIdx > 0) {
                    tutorialStore2 = new TutorialStore2();
                    tutorialStore2.Init();
                    _cleanUps.Add(new FuncObj(tutorialStore2, nameof(tutorialStore2.CleanUp)));
                }
            } 
            else if (Troop2BattleData.Data.gameState == 1) {
                if (!StatisticData.data.IsEventRecord(BattleTutorialKey)) {
                    tutorialBattle = new TutorialBattle();
                    tutorialBattle.Init();
                    _cleanUps.Add(new FuncObj(tutorialBattle, nameof(tutorialBattle.CleanUp)));
                }
            }
        }

        public void CleanUp() {
            foreach (var cleanup in _cleanUps) {
                cleanup.Invoke();
            }
            _cleanUps.Clear();
            if (RGScript.UI.Tutorial.TutorialManager.RawInstance != null) {
                RGScript.UI.Tutorial.TutorialManager.Inst.ClearActiveActionGroup();
            }
        }

        class TutorialStore {
            public void Init() {
                Troop2GameModeProcess.Instance.eventHub.AddListener<EventGameStateChanged>(this, nameof(OnGameStateChanged));
                Troop2GameModeProcess.Instance.eventHub.AddListener<EventUIStorePanelChange>(this, nameof(OnUIStorePanelChange));
                Troop2GameModeProcess.Instance.eventHub.AddListener<EventBuyMercenaryProgressEnd>(this, nameof(OnBuyMercenary));
                Troop2GameModeProcess.Instance.eventHub.AddListener<EventPlaceMercenaryInScene>(this, nameof(OnPlaceMercenaryInScene));
                TutorialManager.Inst.eventHub.AddListener<EventTutorialStepExit>(this, nameof(OnTutorialStepExit));
            }

            public void CleanUp() {
                Troop2GameModeProcess.Instance.eventHub.RemoveListener<EventGameStateChanged>(this, nameof(OnGameStateChanged));
                Troop2GameModeProcess.Instance.eventHub.RemoveListener<EventUIStorePanelChange>(this, nameof(OnUIStorePanelChange));
                Troop2GameModeProcess.Instance.eventHub.RemoveListener<EventBuyMercenaryProgressEnd>(this, nameof(OnBuyMercenary));
                Troop2GameModeProcess.Instance.eventHub.RemoveListener<EventPlaceMercenaryInScene>(this, nameof(OnPlaceMercenaryInScene));
                if (TutorialManager.RawInstance != null) {
                    TutorialManager.Inst.eventHub.RemoveListener<EventTutorialStepExit>(this, nameof(OnTutorialStepExit));
                }
            }

            void OnGameStateChanged(EventGameStateChanged evData) {
                if (evData.state == 0) { // enter store
                    RGGameProcess.StartCommand(0.8f, this, nameof(StartTutorial));
                }
            }

            void StartTutorial() {
                StatisticData.data.RecordEvent(StoreTutorialKey, true);
                TutorialManager.Inst.ActiveActionGroup("Troop2Store", true); // step0
            }

            void OnUIStorePanelChange(EventUIStorePanelChange evData) {
                if (evData.state) {
                    var uiStoreTransform = GameObject.FindObjectOfType<UITroop2Store>().transform;
                    TutorialTarget.AddTag(uiStoreTransform.Find("goods"), "introduce_1"); // step1
                    TutorialManager.Inst.SwitchOn("introduce_1");

                    TutorialTarget.AddTag(uiStoreTransform.Find("goods/coins"), "introduce_2"); // step2
                    TutorialTarget.AddTag(uiStoreTransform.Find("goods/Viewport/Content").GetChild(0).Find("price"), "introduce_3"); // step3
                    TutorialTarget.AddTag(uiStoreTransform.Find("goods/Viewport/Content").GetChild(0), "buy_mercenary"); // step4
                }
            }

            void OnBuyMercenary(EventBuyMercenaryProgressEnd evData) {
                var uiStoreTransform = GameObject.FindObjectOfType<UITroop2Store>().transform;
                TutorialTarget.AddTag(uiStoreTransform.Find("goods/btnHide"), "close_shop");
                TutorialManager.Inst.SwitchOn("close_shop"); // step5

                RGGameProcess.StartCommand(0.5f, this, nameof(SendMercenaryTutorial));
            }

            void SendMercenaryTutorial() {
                var uiStoreTransform = GameObject.FindObjectOfType<UITroop2Store>().transform;
                TutorialTarget.AddTag(uiStoreTransform.Find("bottomBg/mercenaries/Content"), "send_mercenary_signal_receiver");
                TutorialTarget.AddTag(uiStoreTransform.Find("bottomBg/mercenaries/Content").GetChild(0), "send_mercenary_src");
                TutorialTarget.AddTag(Troop2GameModeProcess.Instance.mapGrids[0].transform, "send_mercenary_dst");
                TutorialManager.Inst.SwitchOn("send_mercenary"); // step6
            }

            void OnPlaceMercenaryInScene(EventPlaceMercenaryInScene evData) {
                var uiStoreTransform = GameObject.FindObjectOfType<UITroop2Store>().transform;
                TutorialTarget.Complete(uiStoreTransform.Find("bottomBg/mercenaries/Content"), "send_mercenary");

                TutorialTarget.AddTag(uiStoreTransform.Find("startBtn"), "btn_start");
                RGGameProcess.StartTimer(1, new FuncObj(
                    TutorialManager.Inst, new System.Type[] { typeof(string) },
                    nameof(TutorialManager.SwitchOn), "start_game")); // step7
            }

            void OnTutorialStepExit(EventTutorialStepExit evData) {
                if (evData.actionName.Contains("start_game")) {
                    CleanUp();
                }
            }
        }

        class TutorialStore2 {
            public void Init() {
                Troop2GameModeProcess.Instance.eventHub.AddListener<EventGameStateChanged>(this, nameof(OnGameStateChanged));
                Troop2GameModeProcess.Instance.eventHub.AddListener<EventUIStorePanelChange>(this, nameof(OnUIStorePanelChange));
                Troop2GameModeProcess.Instance.eventHub.AddListener<EventRefreshStore>(this, nameof(OnRefreshStore));
                Troop2GameModeProcess.Instance.eventHub.AddListener<EventBuyMercenaryProgressEnd>(this, nameof(OnBuyMercenary));
                Troop2GameModeProcess.Instance.eventHub.AddListener<EventBuyInventoryItemProgressEnd>(this, nameof(OnBuyInventoryItem));
                Troop2GameModeProcess.Instance.eventHub.AddListener<EventShowMercenaryInfo>(this, nameof(OnShowMercenaryInfo));
                Troop2GameModeProcess.Instance.eventHub.AddListener<EventSellItem>(this, nameof(OnSellItem));
                Troop2GameModeProcess.Instance.eventHub.AddListener<EventDragItemToMercenary>(this, nameof(OnDragItemToMercenary));
                TutorialManager.Inst.eventHub.AddListener<EventTutorialStepEnter>(this, nameof(OnTutorialStepEnter));
                TutorialManager.Inst.eventHub.AddListener<EventTutorialStepExit>(this, nameof(OnTutorialStepExit));
            }

            public void CleanUp() {
                Troop2GameModeProcess.Instance.eventHub.RemoveListener<EventGameStateChanged>(this, nameof(OnGameStateChanged));
                Troop2GameModeProcess.Instance.eventHub.RemoveListener<EventUIStorePanelChange>(this, nameof(OnUIStorePanelChange));
                Troop2GameModeProcess.Instance.eventHub.RemoveListener<EventRefreshStore>(this, nameof(OnRefreshStore));
                Troop2GameModeProcess.Instance.eventHub.RemoveListener<EventBuyMercenaryProgressEnd>(this, nameof(OnBuyMercenary));
                Troop2GameModeProcess.Instance.eventHub.RemoveListener<EventBuyInventoryItemProgressEnd>(this, nameof(OnBuyInventoryItem));
                Troop2GameModeProcess.Instance.eventHub.RemoveListener<EventShowMercenaryInfo>(this, nameof(OnShowMercenaryInfo));
                Troop2GameModeProcess.Instance.eventHub.RemoveListener<EventSellItem>(this, nameof(OnSellItem));
                Troop2GameModeProcess.Instance.eventHub.RemoveListener<EventDragItemToMercenary>(this, nameof(OnDragItemToMercenary));
                if (TutorialManager.RawInstance != null) {
                    TutorialManager.Inst.eventHub.RemoveListener<EventTutorialStepEnter>(this, nameof(OnTutorialStepEnter));
                    TutorialManager.Inst.eventHub.RemoveListener<EventTutorialStepExit>(this, nameof(OnTutorialStepExit));
                }
            }

            List<Troop2StoreItem> uniqueRarity0Mercenaries = new List<Troop2StoreItem>();
            List<Troop2StoreItem> rarity0Items = new List<Troop2StoreItem>();
            void OnGameStateChanged(EventGameStateChanged evData) {
                if (evData.state == 0) { // enter store
                    RGGameProcess.StartCommand(0.8f, this, nameof(StartTutorial));

                    var mercenaryName = Troop2BattleData.Data.mercenaries[0].name;
                    var goodsOfRarity0 = Troop2BattleData.Data.storeItems.FindGoodsOfRarity(0);
                    uniqueRarity0Mercenaries = new List<Troop2StoreItem>();
                    for (var i = 0; i < goodsOfRarity0.Count; ++i) {
                        if (goodsOfRarity0[i].Item1.itemType == Troop2ItemType.MER) {
                            if (goodsOfRarity0[i].Item1.itemId == mercenaryName) {
                                goodsOfRarity0.RemoveAt(i);
                            }
                            else {
                                var duplicate = false;
                                for (var j = 0; j < uniqueRarity0Mercenaries.Count; ++j) {
                                    if (uniqueRarity0Mercenaries[j].itemId == mercenaryName) {
                                        duplicate = true;
                                        break;
                                    }
                                }

                                if (!duplicate) {
                                    uniqueRarity0Mercenaries.Add(goodsOfRarity0[i].Item1);
                                }
                            }
                        }
                        else {
                            rarity0Items.Add(goodsOfRarity0[i].Item1);
                        }
                    }
                }
            }

            bool isTutorialStarted = false;
            bool isUseControlBlocked = false;
            void StartTutorial() {
                StatisticData.data.RecordEvent(StoreTutorialKey2, true);
                GameUtil.BlockUserControl(true);
                isUseControlBlocked = true;
            }

            void OnUIStorePanelChange(EventUIStorePanelChange evData) {
                if (evData.state && !isTutorialStarted) {
                    if (isUseControlBlocked) {
                        GameUtil.BlockUserControl(false);
                        isUseControlBlocked = false;
                    }

                    var uiStoreTransform = GameObject.FindObjectOfType<UITroop2Store>().transform;
                    TutorialTarget.AddTag(uiStoreTransform.Find("goods/teamLevelUp"), "teamlevelup");
                    TutorialManager.Inst.ActiveActionGroup("Troop2Store2", true); // step0
                    TutorialTarget.AddTag(uiStoreTransform.Find("goods/refreshBtn"), "refreshstore"); // step1
                    isTutorialStarted = true;
                }
            }

            void OnRefreshStore(EventRefreshStore evData) {
                // replace duplicate mercenaries in storeItems, in case mercenary level up in tutorial
                var mercenaryName = Troop2BattleData.Data.mercenaries[0].name;
                int mercenaryCountInStore = 0;
                for (var i = 0; i < Troop2BattleData.Data.storeItems.items.Count; ++i) {
                    if (Troop2BattleData.Data.storeItems.items[i].itemType == Troop2ItemType.MER) {
                        if (Troop2BattleData.Data.storeItems.items[i].itemId == mercenaryName && uniqueRarity0Mercenaries.Count > 0) {
                            Troop2BattleData.Data.storeItems.items[i] = uniqueRarity0Mercenaries[0];
                            uniqueRarity0Mercenaries.RemoveAt(0);
                        }
                        ++mercenaryCountInStore;
                    }
                }

                if (mercenaryCountInStore < 2 && uniqueRarity0Mercenaries.Count >= 2) {
                    Troop2BattleData.Data.storeItems.items[0] = uniqueRarity0Mercenaries[0];
                    Troop2BattleData.Data.storeItems.items[1] = uniqueRarity0Mercenaries[1];
                }
                else if (mercenaryCountInStore >= 4 && rarity0Items.Count > 0) {
                    rarity0Items[0].price = 1;
                    Troop2BattleData.Data.storeItems.items[3] = rarity0Items[0];
                    rarity0Items.RemoveAt(0);
                }

                for (var i = 0; i < Troop2BattleData.Data.storeItems.items.Count; ++i) {
                    var item = Troop2BattleData.Data.storeItems.items[i];
                    if (item.rarity > 0) {
                        if (item.itemType == Troop2ItemType.MER) {
                            if (uniqueRarity0Mercenaries.Count > 0) {
                                Troop2BattleData.Data.storeItems.items[i] = uniqueRarity0Mercenaries[0];
                                uniqueRarity0Mercenaries.RemoveAt(0);
                            }
                        }
                        else {
                            if (rarity0Items.Count > 0) {
                                Troop2BattleData.Data.storeItems.items[i] = rarity0Items[0];
                                rarity0Items.RemoveAt(0);
                            }
                        }
                    }
                }

                // 引导中强制调整商品价格
                var minPrice = -1;
                for (var i = 0; i < Troop2BattleData.Data.storeItems.items.Count; ++i) {
                    if (minPrice < 0 || Troop2BattleData.Data.storeItems.items[i].price < minPrice) {
                        minPrice = Troop2BattleData.Data.storeItems.items[i].price;
                    }
                }

                for (var i = 0; i < Troop2BattleData.Data.storeItems.items.Count; ++i) {
                    Troop2BattleData.Data.storeItems.items[i].price = minPrice;
                }

                RGGameProcess.StartCommand(0.1f, this, nameof(StartStep2));// step2
            }

            void StartStep2() {
                var uiStoreTransform = GameObject.FindObjectOfType<UITroop2Store>().transform;
                for (var i = 0; i < uiStoreTransform.Find("goods/Viewport/Content").childCount; ++i) {
                    var child = uiStoreTransform.Find("goods/Viewport/Content").GetChild(i);
                    if (child.GetComponentInChildren<UITroop2MercenaryProxy>() != null) {
                        TutorialTarget.AddTag(child, "buy_mercenary");
                        break;
                    }
                }
                RGGameProcess.StartCommand(0.1f, 
                    CommandUtilExt.CallFunc(new FuncObj(TutorialManager.Inst, 
                    nameof(TutorialManager.Inst.SwitchOn), "buy_mercenary")));// step2
            }

            void OnBuyMercenary(EventBuyMercenaryProgressEnd evData) {
                var uiStore = GameObject.FindObjectOfType<UITroop2Store>();

                if (TutorialManager.Inst.CurrentActionName != null && TutorialManager.Inst.CurrentActionName.Contains("inspect_mercenary")) {
                    TutorialTarget.AddTag(uiStore.mercenarySceneObjs[0].transform, "inspect_mercenary");
                    TutorialTarget.SetProperty(uiStore.mercenarySceneObjs[0].transform, "rectSize", 100);
                    TutorialTarget.AddTag(uiStore.transform.Find("bgEventDragReceiver"), "inspect_mercenary_event_receiver");
                    RGGameProcess.StartCommand(0.1f, 
                        CommandUtilExt.CallFunc(new FuncObj(TutorialManager.Inst, 
                        nameof(TutorialManager.Inst.SwitchOn), "inspect_mercenary")));// step3
                }
                else if (TutorialManager.Inst.CurrentActionName != null && TutorialManager.Inst.CurrentActionName.Contains("sell_item1")) {
                    TutorialTarget.AddTag(uiStore.transform.Find("bottomBg/mercenaries/Content").GetChild(1), "sell_item1");

                    TutorialTarget.AddTag(uiStore.transform.Find("bottomBg/mercenaries/Content"), "sell_item1_signal_receiver");
                    TutorialTarget.AddTag(uiStore.transform.Find("bottomBg/mercenaries/Content").GetChild(1), "sell_item1_src");
                    TutorialTarget.AddTag(uiStore.transform.Find("sell"), "sell_item1_dst");
                    RGGameProcess.StartCommand(0.1f, 
                        CommandUtilExt.CallFunc(new FuncObj(TutorialManager.Inst, 
                        nameof(TutorialManager.Inst.SwitchOn), "sell_item1")));// step11

                    uiStore.CloseStorePanel();
                    uiStore.OnDragSomething(true, false);
                    uiStore.canOnlyDragFromUIToSell = true;
                }
            }

            void OnBuyInventoryItem(EventBuyInventoryItemProgressEnd evData) {
                if (TutorialManager.Inst.CurrentActionName != null && TutorialManager.Inst.CurrentActionName.Contains("drag_item_to_mercenary")) {
                    var uiStore = GameObject.FindObjectOfType<UITroop2Store>();
                    TutorialTarget.AddTag(uiStore.transform, "drag_item0_signal_receiver");

                    var itemIdx = 0;
                    for (var i = 0; i < Troop2BattleData.Data.inventory.Count; i++) {
                        if (Troop2BattleData.Data.inventory[i].type != Troop2ItemType.OTHER) {
                            itemIdx = i;
                            break;
                        }
                    }

                    TutorialTarget.AddTag(uiStore.transform.Find("inventoryPanel/list/list/Viewport/Content").GetChild(itemIdx), "drag_item0_src");
                    TutorialTarget.AddTag(uiStore.transform.Find("bottomBg/mercenaries/Content").GetChild(0), "drag_item0_dst");
                    RGGameProcess.StartCommand(0.1f, 
                        CommandUtilExt.CallFunc(new FuncObj(TutorialManager.Inst, 
                        nameof(TutorialManager.Inst.SwitchOn), "drag_item_to_mercenary")));// step11

                    uiStore.canOnlyDragFromUIToMercenary = true;
                }
            }

            void OnShowMercenaryInfo(EventShowMercenaryInfo evData) {
                // start step 4
                RGGameProcess.StartCommand(0.1f, this, nameof(StartStep4));
            }

            void StartStep4() {

                var uiCommanderState = GameObject.FindObjectOfType<UICommanderState>();
                TutorialTarget.AddTag(uiCommanderState.transform.Find("traitsPos/traits").GetChild(0), "inspect_trait"); // step5

                var uiStore = GameObject.FindObjectOfType<UITroop2Store>();
                TutorialTarget.AddTag(uiStore.transform.Find("bottomBg/inventory"), "introduce_bag"); // step6
            }

            void OnSellItem(EventSellItem evData) {
                var uiStore = GameObject.FindObjectOfType<UITroop2Store>();
                TutorialTarget.Complete(uiStore.transform.Find("bottomBg/mercenaries/Content"), "sell_item1_finish");
            }

            void OnDragItemToMercenary(EventDragItemToMercenary evData) {
                var uiStore = GameObject.FindObjectOfType<UITroop2Store>();
                TutorialTarget.Complete(uiStore.transform, "drag_item0_finish");
            }

            void SendMercenaryTutorial() {
                var uiStoreTransform = GameObject.FindObjectOfType<UITroop2Store>().transform;
                TutorialTarget.AddTag(uiStoreTransform.Find("bottomBg/mercenaries/Content"), "send_mercenary_signal_receiver");
                TutorialTarget.AddTag(uiStoreTransform.Find("bottomBg/mercenaries/Content").GetChild(0), "send_mercenary_src");
                TutorialTarget.AddTag(Troop2GameModeProcess.Instance.mapGrids[0].transform, "send_mercenary_dst");
                TutorialManager.Inst.SwitchOn("send_mercenary"); // step6
            }

            void OnTutorialStepEnter(EventTutorialStepEnter evData) {
                if (evData.actionName.Contains("buy_item0")) {
                    var uiStoreTransform = GameObject.FindObjectOfType<UITroop2Store>().transform;
                    for (var i = 0; i < uiStoreTransform.Find("goods/Viewport/Content").childCount; ++i) {
                        var child = uiStoreTransform.Find("goods/Viewport/Content").GetChild(i);
                        if (child.GetComponentInChildren<UITroop2MercenaryProxy>() == null && child.transform.Find("bg").gameObject.activeSelf) {
                            TutorialTarget.AddTag(child, "buy_item0");
                            break;
                        }
                    }
                }
                else if (evData.actionName.Contains("buy_item1")) {
                    var uiStoreTransform = GameObject.FindObjectOfType<UITroop2Store>().transform;
                    for (var i = 0; i < uiStoreTransform.Find("goods/Viewport/Content").childCount; ++i) {
                        var child = uiStoreTransform.Find("goods/Viewport/Content").GetChild(i);
                        if (child.GetComponentInChildren<UITroop2MercenaryProxy>() != null) {
                            TutorialTarget.AddTag(child, "buy_item1");
                            break;
                        }
                    }
                }
            }


            void OnTutorialStepExit(EventTutorialStepExit evData) {
                if (evData.actionName.Contains("drag_item")) {
                    var uiStore = GameObject.FindObjectOfType<UITroop2Store>();
                    uiStore.canOnlyDragFromUIToMercenary = false;
                }
                else if (evData.actionName.Contains("sell_item1_do")) {
                    var uiStore = GameObject.FindObjectOfType<UITroop2Store>();
                    uiStore.canOnlyDragFromUIToSell = false;
                    CleanUp();
                }
            }
        }


        class TutorialBattle { 
            public void Init() {
                Troop2GameModeProcess.Instance.eventHub.AddListener<EventGameStateChanged>(this, nameof(OnGameStateChanged));
                TutorialManager.Inst.eventHub.AddListener<EventTutorialStepExit>(this, nameof(OnTutorialStepExit));
            }

            public void CleanUp() {
                Troop2GameModeProcess.Instance.eventHub.RemoveListener<EventGameStateChanged>(this, nameof(OnGameStateChanged));
                if (TutorialManager.RawInstance != null) {
                    TutorialManager.Inst.eventHub.RemoveListener<EventTutorialStepExit>(this, nameof(OnTutorialStepExit));
                }
            }

            void OnGameStateChanged(EventGameStateChanged evData) {
                if (evData.state == 1) { // enter battler
                    RGGameProcess.StartCommand(4, this, nameof(StartTutorial));
                }
            }

            void StartTutorial() {
                StatisticData.data.RecordEvent(BattleTutorialKey, true);
                var inputButton = GameObject.FindObjectOfType<GameInputButton>().transform;

                TutorialTarget.AddTag(inputButton.Find("btn_atk"), "btn_attack");
                TutorialTarget.AddTag(inputButton.Find("btn_skill"), "btn_skill");
                TutorialManager.Inst.ActiveActionGroup("Troop2Battle", true); // step0
            }

            void OnTutorialStepExit(EventTutorialStepExit evData) {
                if (evData.actionName.Contains("commander_skill")) {
                    CleanUp();
                }
            }
        }

        
    }
}
