using BattleSystem.Skill;
using BattleSystem.Strategy;
using ModeSeason.Troop2;
using System.Collections;
using UnityEngine;

namespace BattleSystem.SkillSystem {
    public class Troop2WeaponAttack : Troop2Ability {
        private ITargetingStrategy _targetingStrategy;
        private IMoveStrategy _moveStrategy;
        private IMoveStrategy _followStrategy;

        public Troop2WeaponAttack(AIController aiController, SkillData data) : base(aiController, data) {
            _targetingStrategy = StrategyManager.GetStrategy<ITargetingStrategy>(aiController,
                nameof(NearestEnemyIgnoreObstacleTargetingStrategy));
            _moveStrategy = StrategyManager.GetStrategy<IMoveStrategy>(aiController, nameof(KitingMoveStrategy));
            _followStrategy = StrategyManager.GetStrategy<IMoveStrategy>(aiController, nameof(FollowMoveStrategy));
        }

        public override bool DoSkill(bool isEnforce = false) {
            var result = base.DoSkill(isEnforce);
            if (result) {
                OnEffectiveStartHandler();
            }

            return result;
        }

        public override void OnEffectiveStartHandler() {
            _attackUpdateCoroutine = AttackUpdate();
            AIController.StartCoroutine(_attackUpdateCoroutine);
        }

        private IEnumerator _attackUpdateCoroutine;

        private IEnumerator AttackUpdate() {
            while (DuringUse) {
                if (!mercenary || !mercenary.Hand1) {
                    yield return new WaitForFixedUpdate();
                    continue;
                }

                if (!mercenary.Alive()) {
                    break;
                }

                if (AIController.SkillManager.GetSkill(3001).CanUseSkill()) {
                    ExpectedEnd();
                    break;
                }

                if (!mercenary.TargetObject.IsAliveTarget()) {
                    mercenary.TargetObject = null;
                    mercenary.TargetObject = _targetingStrategy.Execute();
                }

                AIController.Target = mercenary.TargetObject ? mercenary.TargetObject : mercenary.master;
                mercenary.SetAutoAttack(mercenary.TargetObject);
                yield return new WaitForFixedUpdate();
            }
        }

        public override void OnEffectiveEndHandler() {
        }

        protected override void OnExpectedEndHandler() {
            base.OnExpectedEndHandler();
            if (_attackUpdateCoroutine != null) {
                AIController.StopCoroutine(_attackUpdateCoroutine);
                _attackUpdateCoroutine = null;
                mercenary.StopAttack();
            }
        }

        protected override void OnUnexpectedEndHandler() {
            base.OnUnexpectedEndHandler();
            if (_attackUpdateCoroutine != null) {
                AIController.StopCoroutine(_attackUpdateCoroutine);
                _attackUpdateCoroutine = null;
                mercenary.StopAttack();
            }
        }

        public override Vector2 GetMoveDirection() {
            AIController.UpdateFacingAndAiming = true;
            if (!mercenary) {
                return mercenary.rigibody.position;
            }

            Vector3 targetPosition = mercenary.transform.position;
            if (mercenary.TargetObject != null && !Troop2BattleData.Data.followCommander) {
                targetPosition = mercenary.TargetObject.position;
            }
            else if (mercenary.master != null) {
                targetPosition = mercenary.master.position;
            }

            if (Vector2.Distance(mercenary.rigibody.position, targetPosition) < 2) {
                return mercenary.rigibody.position;
            }

            if (mercenary.TargetObject && !Troop2BattleData.Data.followCommander) {
                return _moveStrategy.Execute(targetPosition);
            }

            return _followStrategy.Execute(mercenary.master.position);
        }
    }
}