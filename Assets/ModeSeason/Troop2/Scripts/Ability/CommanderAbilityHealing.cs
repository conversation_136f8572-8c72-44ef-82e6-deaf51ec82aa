using UnityEngine;

namespace ModeSeason.Troop2 {
    public class CommanderAbilityHealing : Troop2CommanderAbility {

        public float radius = 100;


        public override void OnStart() {
            GameUtil.DisplayFx("ModeSeason/Troop2/Prefabs/AbilityEfx/CommanderAbilityHealing", commander.transform.position);
            CommandListExecution.Execute(commander.gameObject, 
                CommandUtilExt.Delay(0.35f).SetFinCallback(new FuncObj(this, nameof(DoSkillEffect))));
        }

        public override void OnEvent(int idx) {
        }

        public override void OnEnd() {
        }

        void DoSkillEffect() {
            RGMusicManager.Inst.PlayEffect(ResourcesUtil.Load<AudioClip>("RGSound/effect/fx_healthpot.mp3"));
            var healPercentage = abilityConfig.FloatArg0;
            for (var i = 0; i < Troop2GameModeProcess.Instance.battleMercenaries.Count; ++i) {
                var target = Troop2GameModeProcess.Instance.battleMercenaries[i];
                if (target != null && !target.dead) {
                    target.attribute.RestoreHealth(Mathf.RoundToInt(target.attribute.max_hp * healPercentage));
                    var pos = target.transform.position + Vector3.up * 0.6f;
                    var fxObj = GameUtil.DisplayFx("ModeSeason/Troop2/Prefabs/AbilityEfx/get_healing", pos, 1);
                    if (fxObj != null) {
                        ObjectSyncTransform.Sync(fxObj, target.transform);
                    }
                }
            }
        }
    }
}