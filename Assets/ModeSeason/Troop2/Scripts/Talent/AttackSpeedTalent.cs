using UnityEngine;

namespace ModeSeason.Troop2 {
    public class AttackSpeedTalent : Troop2Talent {
        private float atkSpeed;
        
        public override void TakeEffect() {
            SimpleEventManager.AddEventListener<GameStateChangeEvent>(OnGameStateChanged);
            Troop2EnemyMaker.Instance.OnEnemyDead += OnEnemyDead;

            atkSpeed = 0;
        }

        private void OnGameStateChanged(GameStateChangeEvent gameStateChangeEvent) {
            if (Troop2BattleData.Data.gameState == 0) {
                mercenary.AttributeProxy.atk_speed -= atkSpeed;
                atkSpeed = 0;
            }
        }

        private void OnEnemyDead(RGEController rgeController) {
            if (Troop2BattleData.Data.gameState == 1 && mercenary != null && rgeController.killer == mercenary.gameObject) {
                var descParams = DescParams;
                if (descParams[1] / 100f <= atkSpeed) return;
                
                mercenary.AttributeProxy.atk_speed += descParams[0] / 100f;
                atkSpeed += descParams[0] / 100f;
                mercenary.AttackSpeedChange();
            }
        }

        public override void OnFixedUpdate() {
        }

        public override void RemoveEffect() {
            SimpleEventManager.RemoveEventListener<GameStateChangeEvent>(OnGameStateChanged);

            if (Troop2EnemyMaker.Instance != null) {
                Troop2EnemyMaker.Instance.OnEnemyDead -= OnEnemyDead;
            }
        }
    }
}