using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace ModeSeason.Troop2 {
    public enum Troop2TraitName {
        // 势力
        ThreeKingdoms = 0,
        WestJourney,
        Civilization,
        Myths,
        AetherChurch,

        // 精通
        MultiCount = 100,
        ContinuousCount,
        Energy,
        AbilityDamage,
        BulletSize,
        Speed,
        Control,
    }

    public partial class Troop2TraitManager : MonoBehaviour {
        public static Troop2TraitManager Instance;

        Troop2Util.MercenaryTraitCollection traitCollection;

        Troop2Util.MercenaryTraitCollection GetTraitCollection() {
            var data = Troop2BattleData.Data;
            var mercenariesInBattle = data.SentMercenaries();
            var ret = Troop2Util.GetTraitCollection(mercenariesInBattle);
            return ret;
        }

        public int GetTraitCount(Troop2TraitName traitName) {
            if (traitCollection == null) {
                traitCollection = GetTraitCollection();
            }

            if (traitCollection.traitsCount.TryGetValue(traitName, out var count)) {
                return count;
            }
            return 0;
        }

        public int GetTraitEffectTier(Troop2TraitName traitName) {
            if (traitCollection == null) {
                traitCollection = GetTraitCollection();
            }

            if (traitCollection.tiers.TryGetValue(traitName, out var tier)) {
                return tier;
            }
            return 0;
        }

        public int GetTraitEffectActiveNumber(Troop2TraitName traitName, int tier) {
            if (traitCollection == null) {
                traitCollection = GetTraitCollection();
            }

            if (traitCollection.activeNumbers.TryGetValue(traitName, out var list)) {
                return list[Mathf.Clamp(tier, 0, list.Count - 1)];
            }
            return int.MaxValue;
        }

        public float GetTraitEffectParams(Troop2TraitName traitName, int tier, int idx) {
            if (traitCollection == null) {
                traitCollection = GetTraitCollection();
            }
            if (traitCollection.paramsList.TryGetValue(traitName, out var list)) {
                var l = list[Mathf.Clamp(tier - 1, 0, list.Length - 1)];
                return l[Mathf.Clamp(idx, 0, l.Count - 1)];
            }
            return 0;
        }

        public void OnBattleFieldChanged(bool battleStart) {
            var newTraitCollection = GetTraitCollection();
            if (battleStart) {
                traitCollection = null;
            }

            foreach (var kv in newTraitCollection.traitsCount) {
                var trait = kv.Key;
                var currentCount = kv.Value;
                var lastCount = 0;
                if (traitCollection != null && traitCollection.traitsCount.TryGetValue(trait, out var count)) {
                    lastCount = count;
                }

                if (currentCount != lastCount) {
                    var method = typeof(Troop2TraitManager).GetMethod($"On{trait}Changed");
                    method?.Invoke(this, new object[] { currentCount, lastCount });
                }
            }

            if (traitCollection != null) {
                foreach (var kv in traitCollection.traitsCount) {
                    var trait = kv.Key;
                    if (!newTraitCollection.traitsCount.ContainsKey(trait)) {
                        var lastCount = kv.Value;
                        var method = typeof(Troop2TraitManager).GetMethod($"On{trait}Changed");
                        method?.Invoke(this, new object[] { 0, lastCount });
                    }
                }
            }

            traitCollection = newTraitCollection;
        }
    }
}