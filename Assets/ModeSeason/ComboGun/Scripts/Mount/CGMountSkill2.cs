using UnityEngine;

namespace ModeSeason.ComboGun {
    public class CGMountSkill2 : CGMountSkill {
        public float offset = 1;

        public override void OnCast() {
            mount.anim.SetTrigger(Skill);
            driver.keepFacing = true;
            RGGameProcess.StartTimer(.2f, () => {
                var proj = CGFactory.CreateProjectile("proj5001");
                proj.damage.SetOriginalValue(1);
                proj.Launch(transform.position + Vector3.up * offset, Vector2.zero, null, new CGBlockAttackContext(),
                    false);
                if (level >= 3) {
                    proj.hitAction += (p, col) => {
                        if (col) {
                            CGBuffManager.BuffManager.AddBuff(driver.gameObject, col.gameObject, "stun0");
                        }
                    };
                }
            });
        }

        public override void Casting() {
        }

        public override void OnEnd() {
            driver.keepFacing = false;
        }
    }
}