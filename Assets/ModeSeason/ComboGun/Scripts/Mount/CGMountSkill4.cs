using DG.Tweening;
using System.Collections.Generic;
using UnityEngine;

namespace ModeSeason.ComboGun {
    public class CGMountSkill4 : CGMountSkill {
        private static readonly int Skill2 = Animator.StringToHash("skill2");
        public float speedFactor;

        public override void OnCast() {
            if (driver is C04Controller c04) {
                c04.anim.SetBool(Skill2, true);
            }

            var controller = driver;
            float deltaSpeed = speedFactor;
            //添加buff
            if (controller.awake) {
                if (controller.transform.Find("buff_stealth")) {
                    DestroyImmediate(controller.transform.Find("buff_stealth").gameObject);
                }

                //改变角色的一些属性
                controller.StartHitTrigger(this, data.duration); //隐身时无敌一段时间, 技能加强后隐身即无敌
                controller.attribute.speed_rate += deltaSpeed;

                //添加buff
                GameObject buffGo =
                    Instantiate(
                        ResourcesUtil.Load<GameObject>("RGPrefab/Bullet/Buff/buff_stealth.prefab"),
                        controller.transform, false);
                var buff = buffGo.GetComponent<BuffStealth>();
                buff.buff_time = data.duration;

                buff.BuffSetting(controller, true);
                BattleData.data.SetMark("buff_stealth", 1);
                buffGo.gameObject.name = "buff_stealth";

                buff.OnBuffDestroy += () => {
                    Timer.Register(0.5f, false, false, () => {
                        BattleData.data.SetMark("buff_stealth", 0);
                    });
                    controller.attribute.speed_rate -= deltaSpeed;
                    skillTime = Time.fixedTime;
                    casting = false;
                    OnEnd();
                };

                var clip = SkinAudioConfig.GetSkillClip(2, emHero.Assassin, 0, 0);
                RGMusicManager.GetInstance().PlayEffect(clip);
                var clip1 = SkinAudioConfig.GetSkillClip(2, emHero.Assassin, 0, 1);
                if (clip1 != null) {
                    buff.OnBuffDestroy += () => { RGMusicManager.GetInstance().PlayEffect(clip1); };
                }
            }

            //使敌人失去焦点
            if (CGMobSpawner.Spawner) {
                foreach (var mob in CGMobSpawner.Spawner.Mobs) {
                    if ((mob.Target != null && controller && mob.Target.GetGameObject() == controller.gameObject) ||
                        (mob.HateTarget != null && controller && mob.HateTarget.GetGameObject() == controller.gameObject)) {
                        mob.LoseTarget();
                    }
                }
            }

            foreach (var enemy in FindEnemies(28, true)) {
                if (enemy.target_obj && enemy.target_obj == driver.transform) {
                    enemy.LoseTarget();
                }
            }

            var sr = mount.transform.Find("img/body").GetComponent<SpriteRenderer>();
            if (sr) {
                sr.DOColor(Color.white.Alpha(.3f), .15f);
            }
        }

        public List<RGEController> FindEnemies(float range = 14, bool throughWall = false) {
            var ret = RGBaseController.FindEnemies(transform.position, range, throughWall);
            if (driver.has_target && driver.target_obj && driver.target_obj.GetComponent<RGEController>() is { } e &&
                !ret.Contains(e)) {
                ret.Insert(0, e);
            }

            return ret;
        }

        public override void Casting() {
        }

        public override void OnEnd() {
            var sr = mount.transform.Find("img/body").GetComponent<SpriteRenderer>();
            if (sr) {
                sr.DOColor(Color.white, .15f);
            }
        }
    }
}