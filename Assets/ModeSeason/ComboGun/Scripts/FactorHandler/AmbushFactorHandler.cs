using cfg.ComboGun;
using RGScript.Data;
using System.Collections.Generic;
using System.Globalization;
using UnityEngine;

namespace ModeSeason.ComboGun.FactorHandler {
    public class AmbushFactorHandler : AbstractFactorHandlerBase {
        private float _ambushInterval;

        private static readonly string[] AmbushMobGroups1 =
            { "mg_spear_T0", "mg_bow_T0", "mg_rider_T0", "mg_fan_T0", "mg_net_T0", "mg_mine_T0" };

        private static readonly string[] AmbushMobGroups0 = {
            "mg_spear_T2", "mg_spear_T1", "mg_bow_T1", "mg_bow_T2", "mg_rider_T1", "mg_rider_T2", "mg_fan_T1",
            "mg_fan_T2", "mg_net_T1", "mg_net_T2", "mg_mine_T1", "mg_mine_T2"
        };

        public override void SetInitData(KeyValuePair<string, string>[] dataDict) {
            foreach (KeyValuePair<string, string> jKeyValuePair in dataDict) {
                switch (jKeyValuePair.Key) {
                    // case "ambushInterval":
                    //     _ambushInterval = float.Parse(jKeyValuePair.Value, NumberStyles.Any,
                    //         CultureInfo.InvariantCulture);
                    //     break;
                }
            }

            _ambushInterval = 45;
        }

        public override void OnEnable() {
        }

        public override void OnDisable() {
        }

        private float _timer;

        public override void OnUpdate() {
            _timer += Time.deltaTime;
            if (_timer >= _ambushInterval) {
                _timer = 0;

                foreach (var p in CGGameProcess.Process.players) {
                    if (p && !p.dead) {
                        // TODO 基于流场寻路生成埋伏
                        // var pathFinder = CGTerrain.Terrain.GetFlowFieldPathFinder(p.transform);
                        var position = p.transform.position;
                        var group = (BattleData.data.levelIndex <= 1 ? AmbushMobGroups0 : AmbushMobGroups1)
                            .GetRandomObject();
                        var tbMobGroup = DataMgr.ConfigData.Tables.TbMobGroup;
                        var cgMobGroup = tbMobGroup[group];
                        foreach (var mobGroupComposition in cgMobGroup.MobList) {
                            var mobPosition = position + new Vector3(mobGroupComposition.RelativeX,
                                mobGroupComposition.RelativeY);
                            var targetEffect = Object.Instantiate(PrefabManager.GetPrefab(PrefabName.target),
                                mobPosition, Quaternion.identity, GameUtil.TempRoot).GetComponent<TargetEffect>();
                            targetEffect.PlayAnimation(1.2f, .9f);
                            targetEffect.callback += () => {
                                var mob = CGGameProcess.Process.CreateMob(mobGroupComposition.MobID, mobPosition);
                                mob.IsCloseStrategy = true;
                                mob.SetHateTarget(p);
                                mob.LandFromAir();
                            };
                        }
                    }
                }
            }
        }
    }
}