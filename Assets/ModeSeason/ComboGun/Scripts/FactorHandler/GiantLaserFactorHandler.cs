using GG.Extensions;
using System.Collections.Generic;
using UnityEngine;

namespace ModeSeason.ComboGun.FactorHandler {
    public class GiantLaserFactorHandler : AbstractFactorHandlerBase {
        public override void SetInitData(KeyValuePair<string, string>[] dataDict) {
        }

        public override void OnEnable() {
        }

        public override void OnDisable() {
        }

        private float _createTimer;
        private int _playerIndex;

        public override void OnUpdate() {
            _createTimer += Time.deltaTime;
            if (_createTimer > 16) {
                _createTimer = 0;
                if (++_playerIndex >= CGGameProcess.Process.players.Count) {
                    _playerIndex = 0;
                }

                var player = CGGameProcess.Process.players[_playerIndex];
                var playerPos = player.transform.position;
                Vector3 offset = Vector2.up.Rotate(CGRandom.Range(0, 360)) * 8;
                var targetEffect = Object.Instantiate(PrefabManager.GetPrefab(PrefabName.target),
                    playerPos + offset, Quaternion.identity, GameUtil.TempRoot).GetComponent<TargetEffect>();
                targetEffect.PlayAnimation(1.2f, .9f);
                targetEffect.callback += () => {
                    var laser = CGFactory.CreateProjectile("proj_gaint_laser");
                    laser.damage.SetOriginalValue(15);
                    laser.Launch(playerPos + offset, (player.transform.position - playerPos - offset).normalized, null,
                        new CGBlockAttackContext(), true);
                    laser.Target = player.transform;
                };
            }
        }
    }
}