using cfg.ComboGun;
using RGScript.Data;
using System;
using System.Collections.Generic;
using UI.Base;
using UnityEngine;
using UnityEngine.UI;

namespace ModeSeason.ComboGun.UI.Widgets {
    public class UICBSelectBattleDetailWidgetDesc {
        public string battleId;
        public int difficultyIndex;
    }

    public class UICBSelectBattleDetailWidget : BaseUIWidget {
        private UICBSelectBattleDetailWidgetDesc _desc;

        private Text _titleText;
        private Text _descText;
        private Text _titleText2;
        private Transform _content;
        private GridLayoutGroup _gridLayoutGroup;
        private Text _gridItemText;
        private GameObject _factorLineGo;
        private GridLayoutGroup _factorGridLayoutGroup;
        private GameObject _factorItemGo;
        private RGScript.UI.UILazyLoadList<Text> _lazyLoadList;
        private RGScript.UI.UILazyLoadList<UICBSelectBattleDetailFactorItem> _factorLazyLoadList;
        private int _selectedDifficulty;

        protected override void OnInit() {
            base.OnInit();

            InitUI();
        }

        private void InitUI() {
            _content = Root.Find("scroll_view/viewport/content");
            _titleText = _content.Find("title").GetComponent<Text>();
            _descText = _content.Find("desc").GetComponent<Text>();
            _titleText2 = _content.Find("title2").GetComponent<Text>();
            _factorLineGo = _content.Find("factor_line").gameObject;
            _factorGridLayoutGroup = _content.Find("factor_grid").GetComponent<GridLayoutGroup>();
            _gridLayoutGroup = _content.Find("grid").GetComponent<GridLayoutGroup>();
            _factorItemGo = _factorGridLayoutGroup.transform.Find("descProto").gameObject;
            var gridTf = _gridLayoutGroup.transform;
            _gridItemText = gridTf.Find("descProto").GetComponent<Text>();
            var itemProto = _gridItemText.gameObject;
            _lazyLoadList = new RGScript.UI.UILazyLoadList<Text>();
            _lazyLoadList.SetRootTf(gridTf);
            _lazyLoadList.SetProtoInScene(itemProto);

            _factorLazyLoadList = new RGScript.UI.UILazyLoadList<UICBSelectBattleDetailFactorItem>();
            _factorLazyLoadList.SetRootTf(_factorGridLayoutGroup.transform);
            _factorLazyLoadList.SetProtoInScene(_factorItemGo);
        }

        private void AddEvents() {
            SimpleEventManager.AddEventListener<RefreshComboGunSelectBattleDifficultyEvent>(
                OnRefreshComboGunSelectBattleDifficultyEvent);
        }

        private void RemoveEvents() {
            SimpleEventManager.RemoveListener<RefreshComboGunSelectBattleDifficultyEvent>(
                OnRefreshComboGunSelectBattleDifficultyEvent);
        }

        private void OnEnable() {
            AddEvents();
        }

        private void OnDisable() {
            RemoveEvents();
        }

        private void OnRefreshComboGunSelectBattleDifficultyEvent(RefreshComboGunSelectBattleDifficultyEvent e) {
            _selectedDifficulty = e.difficulty;
            RefreshGrid();
        }

        protected override void OnStartUp(params object[] args) {
            base.OnStartUp(args);
            if (args.Length == 0) {
                Debug.LogError("UICBSelectBattleDiffScrollWidget args length is zero");
                return;
            }

            _desc = (UICBSelectBattleDetailWidgetDesc)args[0];
            _selectedDifficulty = _desc.difficultyIndex;
            RefreshGrid();
            _titleText.text = CGDifficultyData.GetBattleTitle(_desc.battleId);
            _descText.text = CGDifficultyData.GetBattleDesc(_desc.battleId);
        }

        private void RefreshGrid() {
            var buffs = DataMgr.CGChampionTaskData.rankData.GetBuffs(_desc.battleId);
            RefreshBattleDescUI(buffs);

            List<string> factorIconList = new List<string>();
            List<string> factorDescList = new List<string>();
            var battleConfig = DataMgr.CGDifficultyData.battleDict[_desc.battleId];
            if (battleConfig is { Count: > 0 }) {
                for (int i = 0; i <= _selectedDifficulty; i++) {
                    var diffConfig = battleConfig[i];
                    factorIconList.AddRange(diffConfig.DifficultyIcons);
                    factorDescList.AddRange(diffConfig.DifficultyDescs);
                }
            }

            int showCount = Mathf.Min(factorDescList.Count, factorIconList.Count);
            bool hasFactorDesc = showCount > 0;

            _factorLineGo.gameObject.SetActive(hasFactorDesc);
            _factorGridLayoutGroup.gameObject.SetActive(hasFactorDesc);
            if (hasFactorDesc) {
                RefreshFactorUI(showCount, factorDescList, factorIconList);
            }
        }

        private void RefreshBattleDescUI(List<CGNobilityRank> buffs) {
            _lazyLoadList.UpdateList(buffs.Count, (index, text) => {
                var buff = buffs[index];
                text.text = GetBuffString(buff);
            });
        }

        private void RefreshFactorUI(int count, List<string> factorDescList, List<string> factorIconList) {
            _factorLazyLoadList.UpdateList(count, (index, item) => {
                var desc = factorDescList[index];
                var icon = factorIconList[index];
                var sprite = CGDifficultyData.GetFactorIcon(icon);
                item.SetData(desc, sprite);
            });
        }

        private string GetBuffString(CGNobilityRank buff) {
            var buffStr = CGNobilityRankData.BuffToString(buff.EffectID);
            return $"{buffStr}";
        }


        protected override void OnClear() {
            base.OnClear();
        }
    }
}