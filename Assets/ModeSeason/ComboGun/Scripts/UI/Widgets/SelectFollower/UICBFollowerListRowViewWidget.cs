using System.Collections.Generic;
using UI.Base;
using UnityEngine;

namespace ModeSeason.ComboGun.UI.Widgets {
    public class UICBFollowerListRowViewWidget : BaseUIWidget {
        public UICBFollowerListCellView cellProto;
        private List<UIScrollFollowerCellDesc> _rowCells;

        protected override void OnInit() {
            base.OnInit();
            _rowCells = new();
        }

        protected override void OnStartUp(params object[] args) {
            base.OnStartUp(args);
            if (args.Length == 0) {
                Debug.LogError("[UICBFollowerListItemWidget] args should not be empty");
                return;
            }

            _rowCells = (List<UIScrollFollowerCellDesc>)args[0];

            var rowCount = transform.childCount;
            for (var i = 0; i < rowCount; i++) {
                var child = transform.GetChild(i);
                if (i >= _rowCells.Count) {
                    child.gameObject.SetActive(false);
                    break;
                }
                
                var data = _rowCells[i];
                child.gameObject.SetActive(true);
                var cWidget = child.GetComponent<UICBFollowerListCellViewWidget>();
                cWidget.Clear();
                cWidget.StartUp(data);
            }
        }
        
        protected override void OnClear() {
            base.OnClear();
            _rowCells.Clear();
            for (var i = 0; i < transform.childCount; i++) {
                var child = transform.GetChild(i);
                child.gameObject.SetActive(false);
            }
        }
    }
}