using ModeSeason.ComboGun;
using RGScript.Data;
using RGScript.Data.GameItemData;
using UI.Base;
using UnityEngine;
using UnityEngine.UI;

namespace RGScript.UI {
    public class UIItemCountDesc {
        public string itemId;
        public int count;
    }

    public class UIItemCountWidget : BaseUIWidget {
        private UIItemWidget _itemWidget;
        private Widget.Item _item;
        private Text _countText;
        private UIItemCountDesc _desc;

        protected override void OnInit() {
            base.OnInit();
            _itemWidget = transform.Find("widget_item").GetComponent<UIItemWidget>();
            _itemWidget.gameObject.SetActive(false);
            _item = transform.Find("item").GetComponent<Widget.Item>();
            _countText = transform.Find("count").GetComponent<Text>();
        }

        protected override void OnStartUp(params object[] args) {
            base.OnStartUp(args);
            if (args.Length == 0) {
                Debug.LogError("UIItemWidget args is null");
                return;
            }

            _desc = args[0] as UIItemCountDesc;
            RefreshUI();
        }

        private void RefreshUI() {
            _item.SetItem(_desc.itemId);

            // _itemWidget.StartUp(new UIItemDesc() {
            //     itemId = _desc.itemId,
            //     count = _desc.count
            // });
            _countText.text = $"\u00d7 {_desc.count}";
        }

        protected override void OnClear() {
            base.OnClear();
        }
    }
}