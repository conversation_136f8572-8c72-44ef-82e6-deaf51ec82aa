using System;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace ModeSeason.ComboGun {
    public class UIEventHandler : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IBeginDragHandler, <PERSON>ragHand<PERSON>, IEndDragHandler, IPointerUp<PERSON>andler,
        IPointerDownHandler {
        public bool dragEnable;
        public int Index { get; set; }
        public static UIEventHandler currentDrag;

        public event Action OnBeginDragEvent;

        public void OnBeginDrag(PointerEventData data) {
            if (!dragEnable || currentDrag) {
                return;
            }

            currentDrag = this;
            _dragOffset = (Vector2)transform.position - data.position;
            SetDraggedPosition(data);
            OnBeginDragEvent?.Invoke();
        }

        private Vector2 _dragOffset;
        public event Action OnDragEvent;

        public void OnDrag(PointerEventData data) {
            if (!dragEnable || currentDrag != this) {
                return;
            }

            GetComponent<Image>().raycastTarget = false;
            SetDraggedPosition(data);
            OnDragEvent?.Invoke();
        }

        private void SetDraggedPosition(PointerEventData data) {
            transform.position = data.position + _dragOffset;
        }

        public event Action OnEndDragEvent;

        public void OnEndDrag(PointerEventData eventData) {
            if (!dragEnable || currentDrag != this) {
                return;
            }

            currentDrag = null;
            GetComponent<Image>().raycastTarget = true;
            OnEndDragEvent?.Invoke();
        }

        public event Action OnPointerUpEvent;

        public void OnPointerUp(PointerEventData eventData) {
            OnPointerUpEvent?.Invoke();
        }

        public event Action OnPointerDownEvent;

        public void OnPointerDown(PointerEventData eventData) {
            OnPointerDownEvent?.Invoke();
        }
    }
}