using UnityEngine;

namespace ModeSeason.ComboGun {
    public struct CGDamageInfo {
        public int damage;
        public int critic;
        public CGWeapon sourceWeapon;
        public RGController sourceController;
        public CGProjectile sourceProj;
        public GameObject sourceObject;
    }

    public struct CGHealingInfo {
        public int health;
        public CGProjectile sourceProj;
    }

    public enum HitableType {
        TerrainObject,
        Unit,
        ProjInterceptor,
        Wall,
    }
    
    public interface IHitable {
        public HitableType GetHitableType();
        
        public GameObject GetGameObject();
        
        public void TakeDamage(CGDamageInfo info);

        public void ReceiveHealing(CGHealingInfo info);
    }
}