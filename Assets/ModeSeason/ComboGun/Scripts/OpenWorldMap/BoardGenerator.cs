using cfg.ComboGun;
using ModeSeason.ComboGun.OpenWorldMap.Items;
using ModeSeason.ComboGun.OpenWorldMap.Tiles;
using Sirenix.OdinInspector;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.Tilemaps;

namespace ModeSeason.ComboGun.OpenWorldMap {
    [System.Serializable]
    public class BoardGenerator : IBoardGenerator {
        public const string InCity = "inCity";
        public const string Box = "box";
        
        [BoxGroup("ggo")] public bool generatedGObject;
        [BoxGroup("ggo")] public Transform content;
        [BoxGroup("Tilemap")] public bool writeToMap = true;
        [ShowIf("writeToMap"), BoxGroup("Tilemap")] public bool writeToMapArtStyle;
        [ShowIf("@writeToMapArtStyle == false && writeToMap"), BoxGroup("Tilemap")] public Tilemap[] tilemaps;

        private List<RoomInstantiateInfo> generatedRoomDrawInfoLists;
        private List<char[,]> boardGridAsCharacterLists;
        /// <summary>
        /// 深度（主要用于写入区块）
        /// </summary>
        private List<bool[,]> boardGridAsDepthLists;
        private List<Vector2Int> patrolPaths;

        private Dictionary<char, BoardLibraryEntry> libraryDictionary;
        private Dictionary<string, BoardLibraryEntry> idLibraryDictionary;
        private Dictionary<char, CharToLockLayerInfo> charToLockLayerDictionary;
        private Dictionary<string, string[,]> specialInfos;
        private object initGeneratorData;
        private System.Random random;

        public List<RoomInstantiateInfo> GeneratedRoomDrawInfoLists => generatedRoomDrawInfoLists;
        public TileBase DrawColliderTile {
            get {
                foreach (var entry in BoardGenerationProfile.boardLibrary.boardLibraryEntryList) {
                    if (entry.characterId == BoardGenerationProfile.groundChar) {
                        return entry.tile;
                    }
                }

                return null;
            }
        }
        public TileBase DrawFloorTile {
            get {
                foreach (var entry in BoardGenerationProfile.boardLibrary.boardLibraryEntryList) {
                    if (entry.characterId == BoardGenerationProfile.glassChar) {
                        return entry.tile;
                    }
                }

                return null;
            }
        }
        public int TileMapScale => BoardGenerationProfile.tileMapScale;
        public int TileMapSize =>  BoardGenerationProfile.tileMapSize;
        [ShowInInspector, ReadOnly] public Vector2Int StartPosition { get; private set;  }
        [ShowInInspector] public int RandomSeed { get; set; }
        [ShowInInspector] public BoardGenerationProfile BoardGenerationProfile { get; set; }

        public bool TestIfInGrid(int x, int y) {
            return x < BoardGenerationProfile.boardHorizontalSize && y < BoardGenerationProfile.boardVerticalSize && x >= 0 && y >= 0;
        }

        public char GetPositionCharacter(int x, int y, int layer = 3) {
            if (!TestIfInGrid(x, y)) {
                LogUtil.LogError($"访问了越界的标识值({x}, {y})在层{layer}");
                return BoardGenerationProfile.emptySpaceChar;
            }
            return boardGridAsCharacterLists[layer][x, y];
        }
        
        private void SetPositionCharacter(int x, int y, char value, int layer = 3) {
            if (!TestIfInGrid(x, y)) {
                LogUtil.LogError($"设置了越界的标识值({x}, {y})在层{layer}");
                return;
            }
            boardGridAsCharacterLists[layer][x, y] = value;
        }

        public bool GetPositionDepth(int x, int y, int layer = 3) {
            if (!TestIfInGrid(x, y)) {
                LogUtil.LogError($"访问了越界的深度值({x}, {y})在层{layer}");
                return true;
            }
            return boardGridAsDepthLists[layer][x, y];
        }
        
        public void WritePositionDepth(int x, int y, int layer = 3) {
            if (!TestIfInGrid(x, y)) {
                LogUtil.LogError($"设置了越界的深度值({x}, {y})在层{layer}");
                return;
            }
            boardGridAsDepthLists[layer][x, y] = true;
        }

        /// <summary>
        /// 写入数据到指定层级
        /// </summary>
        /// <param name="x">x</param>
        /// <param name="y">y</param>
        /// <param name="writeValue">写入值</param>
        /// <param name="overwriteFilledSpaces">覆盖已有数据</param>
        /// <param name="overwriteRoomSpaces">覆盖深度</param>
        /// <param name="drawDiedArea">在死区绘制</param>
        /// <param name="layer">绘制层级</param>
        /// <returns>绘制结果</returns>
        public bool WritePositionCharacter(int x, int y, char writeValue, bool overwriteFilledSpaces, bool overwriteRoomSpaces, bool drawDiedArea = false, int layer = 3) 
        {
            if (!TestIfInGrid(x, y)) return false;
            // 默认层重载瓦片层级
            if (layer == 3 && charToLockLayerDictionary.TryGetValue(writeValue, out var info))
            {
                layer = info.layer;
                if (info.clear) ClearGroundGridAtPos(x, y, layer);
            }
            if (!drawDiedArea && IsDiedArea(x, y)) return false;
            
            if (!overwriteFilledSpaces && GetPositionCharacter(x, y, layer) != BoardGenerationProfile.emptySpaceChar) return false;
            if (!overwriteRoomSpaces && GetPositionDepth(x, y, layer)) return false;
                
            SetPositionCharacter(x, y, writeValue, layer);
            return true;
        }
        
        /// <summary>
        /// 在指定区域获得一个随机位置
        /// </summary>
        /// <param name="start">区域左下角</param>
        /// <param name="end">区域右上角</param>
        /// <returns></returns>
        public Vector2Int GetRandomGridPosition(Vector2Int start, Vector2Int end) {
            if (start.x >= end.x || start.y >= end.y) {
                throw new Exception("终点应大于起点！");
            }

            start.x = Mathf.Clamp(start.x, 0, BoardGenerationProfile.boardHorizontalSize);
            end.x = Mathf.Clamp(end.x, 0, BoardGenerationProfile.boardHorizontalSize);
            
            start.y = Mathf.Clamp(start.y, 0, BoardGenerationProfile.boardVerticalSize);
            end.y = Mathf.Clamp(end.y, 0, BoardGenerationProfile.boardVerticalSize);
            
            Vector2Int randomPosition = new Vector2Int(random.Next(start.x, end.x), random.Next(start.y, end.y));
            return randomPosition;
        }
        
        public BoardLibraryEntry GetLibraryEntryByCharacterId(char charId) {
            return libraryDictionary.TryGetValue(charId, out BoardLibraryEntry value) ? value : BoardGenerationProfile.boardLibrary.defaultEntry;
        }
        
        public BoardLibraryEntry GetLibraryEntryById(string id) {
            return idLibraryDictionary.TryGetValue(id, out BoardLibraryEntry value) ? value : BoardGenerationProfile.boardLibrary.defaultEntry;
        }

        public GameObject GeneratedGameObject(RoomPrefabInfo prefabInfo, Transform transform = null) {
            return GeneratedGameObject(prefabInfo, random.Next(), transform);
        }
        
        public GameObject GeneratedGameObject(RoomPrefabInfo prefabInfo, int seed, Transform transform = null) {
            var libraryEntry = GetLibraryEntryByCharacterId(prefabInfo.prefabKeys);

            if (libraryEntry.prefabToSpawn == null) {
                LogUtil.LogError(prefabInfo.prefabKeys == BoardGenerationProfile.boardLibrary.defaultEntry.characterId
                    ? $"请注意，key为空，这是不应该出现的情况。"
                    : $"请注意，key：{prefabInfo.prefabKeys}(int){(int)prefabInfo.prefabKeys}对应的预制体已从路径中移除，请确保对象依旧存在");
                return null;
            }
            
            GameObject spawnedPrefab = GameObject.Instantiate(libraryEntry.prefabToSpawn, transform != null ? transform : content);
            spawnedPrefab.transform.localPosition = prefabInfo.position;
            
            // 处理字典中对象为静态的情况
            if (libraryEntry.isStatic) {
                foreach (var obj in spawnedPrefab.GetComponentsInChildren<Transform>()) obj.gameObject.isStatic = true;
            }

            // 从房间数据中拉取key值接口
            if (spawnedPrefab.GetComponent<IOpenWorldKey>() is { } ikey) ikey.Key = prefabInfo.inforKey;
            
            // 初始化种子接口
#if UNITY_EDITOR
            if (Application.isPlaying)
#endif
            {
                if (spawnedPrefab.GetComponent<IOpenWorldItem>() is { } itemObjects) {
                    InitializedItem(seed, itemObjects);
                }
            }
            
            return spawnedPrefab;
        }

        public void InitializedItem(int seed, IOpenWorldItem item) {
            item.Seed = seed;
            var rand = new System.Random(item.Seed);
            item.Initialized(this, rand);
        }

        public bool DrawTemplate(string id, int x, int y, RoomTemplate templateToSpawn, bool overwriteFilledCharacters, bool overwriteRoomSpaces) {
            return DrawTemplate(x, y, templateToSpawn, overwriteFilledCharacters, overwriteRoomSpaces, true, false, false, false, 3, id);
        }

        public bool DrawTemplate(int x, int y, RoomTemplate templateToSpawn, bool overwriteFilledCharacters, bool overwriteRoomSpaces, bool allDrawDepth = true, bool horizontalMirroring = false, bool verticalMirroring = false, bool drawDiedArea = false, int layer = 3, string id = "")
        {
            if (templateToSpawn == null) {
                LogUtil.LogError("请注意：有一个未被成功读取的房间！请策划检查配置表格是否有key对应不存在的本地文件！");
                return false;
            }

            bool inCity = GetMapSpecialInfo(InCity, x, y) == InCity;
            int charIndex = 0;

            for (int si = 0; si < templateToSpawn.roomSizeX; si++) 
            {
                int i = horizontalMirroring ? templateToSpawn.roomSizeX - si - 1 : si;
                for (int sj = 0; sj < templateToSpawn.roomSizeY; sj++)
                {
                    int j = verticalMirroring ? templateToSpawn.roomSizeY - sj - 1 : sj;
                    if (templateToSpawn.roomChars[charIndex] != BoardGenerationProfile.emptySpaceChar) {
                        WritePositionCharacter(x + i, y + j,
                            inCity ? BoardGenerationProfile.GetChangedFromInCityBeOverChars(templateToSpawn.roomChars[charIndex]) : templateToSpawn.roomChars[charIndex]
                            , overwriteFilledCharacters, overwriteRoomSpaces, drawDiedArea, layer);
                    }
                    charIndex++;
                }
            }
            DrawTemplateDepth(x, y, templateToSpawn, layer, !allDrawDepth);

            // 渲染房间内预制体
            if (templateToSpawn.prefabInfos == null) return true;
            
            Vector2Int roomStartPosition = new Vector2Int(x, y);
            List<RoomPrefabInfo> generatedRoomDrawInfos = templateToSpawn.prefabInfos.Select(prefabInfo => {
                
                Vector2 position = prefabInfo.position;
                Vector2 inf =new Vector2(templateToSpawn.roomSizeX, templateToSpawn.roomSizeY) * TileMapScale - position;

                position.x = horizontalMirroring ? inf.x : position.x;
                position.y = verticalMirroring ? inf.y : position.y;
                
                return new RoomPrefabInfo() {
                    prefabKeys = prefabInfo.prefabKeys,
                    position = GridToWorldPosition(roomStartPosition) + position,
                    inforKey = prefabInfo.inforKey,
                };
            }).ToList();

            // 查表追加的奖励
            if (!string.IsNullOrEmpty(id)) {
                var tbRoom = RGScript.Data.DataMgr.ConfigData.Tables.TbRoom;
                var cgRoom = tbRoom[id];
                            
                if (!string.IsNullOrEmpty(cgRoom.RoomReward)) {
                    var tbRoomReward = RGScript.Data.DataMgr.ConfigData.Tables.TbRoomReward;
                    var cgRoomReward = tbRoomReward[cgRoom.RoomReward];

                    foreach (var rewardGroupComposition in cgRoomReward.RewardList) {
                        Vector2 position = new Vector2(rewardGroupComposition.RelativeX, rewardGroupComposition.RelativeY);
                        Vector2 inf =new Vector2(templateToSpawn.roomSizeX, templateToSpawn.roomSizeY) * TileMapScale - position;

                        position.x = horizontalMirroring ? inf.x : position.x;
                        position.y = verticalMirroring ? inf.y : position.y;
                        
                        generatedRoomDrawInfos.Add(new RoomPrefabInfo() {
                            prefabKeys = GetLibraryEntryById(rewardGroupComposition.PrefabID).characterId,
                            inforKey = rewardGroupComposition.LootID,
                            position = GridToWorldPosition(roomStartPosition) + position,
                        });
                    }
                }
            }

            // 追加到生成列表
            if (generatedRoomDrawInfos.Count <= 0)  return true;

            var roomInstantiateInfo = new RoomInstantiateInfo() {
                ID = id,
                inCity = inCity,
                position = GridToWorldPosition(roomStartPosition),
                prefabInfos = generatedRoomDrawInfos,
                templateToSpawn = templateToSpawn,
            };
            generatedRoomDrawInfoLists.Add(roomInstantiateInfo);
            
            // 生成渲染
            if (generatedGObject) RoomInstantiate(roomInstantiateInfo);

            return true;
        }
        
        /// <summary>
        /// 测试用渲染
        /// </summary>
        private void RoomInstantiate(RoomInstantiateInfo roomInstantiateInfo) {
            List<RoomPrefabInfo> generatedRoomDrawInfos = roomInstantiateInfo.prefabInfos;
            
            foreach (var pInfo in generatedRoomDrawInfos) {
                var charToKeyInfo = GetLibraryEntryByCharacterId(pInfo.prefabKeys);
                switch (charToKeyInfo.keyType) {
                    case GobjectKeyType.None:
                    case GobjectKeyType.Enemy:
                    case GobjectKeyType.Obstacle: {
                        GeneratedGameObject(pInfo, 0);
                        break;
                    }
                    case GobjectKeyType.Mph: {
                        var infos = OpenWorldMph.Instantiate(this, roomInstantiateInfo, pInfo);
                        foreach (RoomPrefabInfo info in infos) {
                            RoomPrefabInfo tmpInfo = info;
                            if (!string.IsNullOrEmpty(tmpInfo.mobId)) 
                            {
                                var config = RGScript.Data.DataMgr.ConfigData.Tables.TbMob.Get((tmpInfo.mobId));
                                tmpInfo.prefabKeys = GetLibraryEntryById(config.Prefab).characterId;
                                    
                                if (tmpInfo.prefabKeys == BoardGenerationProfile.emptySpaceChar && !Application.isPlaying) {
                                    LogUtil.LogWarning($"perfab:{config.Prefab}不在表中，将尝试本地读取");
                                    var obj = GameObject.Instantiate(ResourcesUtil.Load<GameObject>($"ModeSeason/ComboGun/Prefabs/Mob/{config.Prefab}.prefab"), content);
                                    obj.transform.position = tmpInfo.position;
                                    continue;
                                }
                            }
                            var newObj = GeneratedGameObject(tmpInfo, 0);
                            if (newObj.GetComponent<CGMob>() is {} cgMob) {
                                cgMob.data = new CGMobData() {
                                    mobid = roomInstantiateInfo.templateToSpawn.name,
                                    roomId = roomInstantiateInfo.ID,
                                    x = tmpInfo.position.x,
                                    y = tmpInfo.position.y,
                                };
                            }
                        }
                        break;
                    }
                    default:
                        throw new Exception($"不存在的枚举值：{charToKeyInfo.keyType}");
                }
            }
        }
        
        public bool TestIfDrawTemplateEmpty(int x, int y, RoomTemplate templateToSpawn, int layer = 3)
        {
            for (int i = 0; i < templateToSpawn.roomSizeX; i++)
            {
                for (int j = 0; j < templateToSpawn.roomSizeY; j++)
                {
                    if (!TestIfInGrid(x + i, y + j) || 
                        GetPositionDepth(x + i, y + j, layer) || 
                        GetPositionCharacter(x + i, y + j, layer) != BoardGenerationProfile.emptySpaceChar || 
                        IsDiedArea(x + i, y + j)) {
                        return false;
                    }
                }
            }

            return true;
        }

        public void DrawTemplateDepth(int x, int y, RoomTemplate templateToSpawn, int layer = 3, bool onlyNotEmptyGround = false)
        {
            int charIndex = 0;

            for (int i = 0; i < templateToSpawn.roomSizeX; i++)
            {
                for (int j = 0; j < templateToSpawn.roomSizeY; j++)
                {
                    if (TestIfInGrid(x + i, y + j) && (templateToSpawn.roomChars[charIndex] != BoardGenerationProfile.emptySpaceChar || !onlyNotEmptyGround)) {
                        WritePositionDepth(x + i, y + j, layer);
                    }
                    charIndex++;
                }
            }
        }

        public string GetMapSpecialInfo(string key, int x, int y) {
            if (!TestIfInGrid(x, y)) {
                LogUtil.LogError($"访问了越界的数据值({x}, {y})在key: {key}");
                return string.Empty;
            }

            return specialInfos.TryGetValue(key, out string[,] info) ? info[x, y] : string.Empty;
        }

        public IReadOnlyList<Vector2Int> GetPatrolPaths() {
            return patrolPaths;
        }

        public Vector2 GridToWorldPosition(Vector2Int position) {
            return (position - new Vector2(BoardGenerationProfile.boardHorizontalSize, BoardGenerationProfile.boardVerticalSize) / 2) * TileMapScale;
        }

        public void SetMapSpecialInfo(string key, int x, int y, string value) {
            if (!TestIfInGrid(x, y)) {
                LogUtil.LogError($"设置了越界的数据值({x}, {y})在key: {key}");
                return;
            }
            
            specialInfos.TryAdd(key, new string[BoardGenerationProfile.boardHorizontalSize, BoardGenerationProfile.boardVerticalSize]);
            specialInfos[key][x, y] = value;
        }
        
        public void ClearGroundGridAtPos(int x, int y, int layer = 2) 
        {
            if (!TestIfInGrid(x, y))  return;

            for (int i = 1; i < layer; i++) {
                SetPositionCharacter(x, y, BoardGenerationProfile.emptySpaceChar, i);
            }
        }
        
        public void Clear()
        {
            if (tilemaps != null)
            {
                foreach (var tilemap in tilemaps) {
                    tilemap.ClearAllTiles();
                }
            }
            
            if (content != null) {
                for (int i = content.childCount - 1; i >= 0; i--)
                {
                    GameObject toDestroy = content.GetChild(i).gameObject;
#if UNITY_EDITOR
                    if (Application.isPlaying)
                    {
#endif
                        GameObject.Destroy(toDestroy);
#if UNITY_EDITOR
                    }
                    else
                    {
                        GameObject.DestroyImmediate(toDestroy);
                    }
#endif
                }
            }

            generatedRoomDrawInfoLists?.Clear();
            boardGridAsCharacterLists?.Clear();
            boardGridAsDepthLists?.Clear();
            patrolPaths?.Clear();
        }
        
        public async Task ClearAndRebuild(object initData)
        {
            Clear();
            
            initGeneratorData = initData;
            await BuildLevel();
        }

        private async Task BuildLevel() {
            // 索引表
            libraryDictionary = BoardGenerationProfile.boardLibrary.boardLibraryEntryList.ToDictionary(e => e.characterId);
            idLibraryDictionary = BoardGenerationProfile.boardLibrary.boardLibraryEntryList.ToDictionary(e => e.id);

            charToLockLayerDictionary = new Dictionary<char, CharToLockLayerInfo>();
            foreach (var t in BoardGenerationProfile.boardLibrary.charToLockLayerList)
            {
                charToLockLayerDictionary.Add(t.characterId, t);
            }
            
            // 随机数
            random = new System.Random(RandomSeed);
            StartPosition = BoardGenerationProfile.startPositions[random.Next(0, BoardGenerationProfile.startPositions.Length)];

            //  初始化地图
            specialInfos = new Dictionary<string, string[,]>();
            boardGridAsCharacterLists = new List<char[,]>();
            boardGridAsDepthLists = new List<bool[,]>();
            for (int layer = 0; layer < TileMapSize; layer++) {
                
                var characterGrid = new char[BoardGenerationProfile.boardHorizontalSize, BoardGenerationProfile.boardVerticalSize];
                for (int i = 0; i < BoardGenerationProfile.boardHorizontalSize; i++)
                {
                    for (int j = 0; j < BoardGenerationProfile.boardVerticalSize; j++)
                    {
                        characterGrid[i, j] = BoardGenerationProfile.emptySpaceChar;
                    }
                }
                    
                boardGridAsCharacterLists.Add(characterGrid);
                boardGridAsDepthLists.Add(new bool[BoardGenerationProfile.boardHorizontalSize, BoardGenerationProfile.boardVerticalSize]);
            }
            generatedRoomDrawInfoLists = new List<RoomInstantiateInfo>();

            // 遍历每个生成策略
            foreach (var generator in BoardGenerationProfile.generators)
            {
                generator.SetInitData(this, initGeneratorData);
                generator.Generate(this, random);
                await Task.Delay(1);
            }

            var startRoom = BoardGenerationProfile.startRooms[random.Next(0, BoardGenerationProfile.startRooms.Length)];
            DrawTemplate(StartPosition.x - startRoom.roomSizeX / 2, StartPosition.y - startRoom.roomSizeY / 2, startRoom,
                true, true, true, false, false, true);
            int time = 5;
            while (time-- > 0 && !RegisterPatrolPath()) {
                await Task.Delay(1);
            }
            
            // 生成到tilemap
            List<Vector3Int> positions = new List<Vector3Int>();
            List<TileBase> tiles = new List<TileBase>();

            for (int i = 0; i < TileMapSize; i++) 
            {
                positions.Clear();
                tiles.Clear();
                for (int x = 0; x < BoardGenerationProfile.boardHorizontalSize; x++)
                {
                    for (int y = 0; y < BoardGenerationProfile.boardVerticalSize; y++)
                    {
                        RememberMapEntryFromGrid(GetPositionCharacter(x, y, i), new Vector2Int(x, y), i, positions, tiles);
                    }
                }
                await WriteToTilemap(positions.ToArray(), tiles.ToArray(), i);
            }
        }

        public bool IsDiedArea(int x, int y) {
            return Vector2.Distance(new Vector2(x, y), StartPosition) < BoardGenerationProfile.diedArea;
        }
        
        public void RememberMapEntryFromGrid(char charId, Vector2Int location, int index, List<Vector3Int> positions, List<TileBase> tiles)
        {
            if (charId == BoardGenerationProfile.emptySpaceChar) return;
            
            BoardLibraryEntry inputEntry = GetLibraryEntryByCharacterId(charId);
            
            Vector3Int pos = new Vector3Int(location.x, location.y, 0)  - new Vector3Int(BoardGenerationProfile.boardHorizontalSize, BoardGenerationProfile.boardVerticalSize, 0) / 2;;
            positions.Add(pos);

            tiles.Add(inputEntry.tile != null ? inputEntry.tile : BoardGenerationProfile.boardLibrary.defaultEntry.tile);
        }

        public bool IsStopPosition(int x, int y) {
            foreach (var stopLayer in BoardGenerationProfile.stopLayers) {
                var character = GetPositionCharacter(x, y, stopLayer.layer);
                BoardLibraryEntry inputEntry = GetLibraryEntryByCharacterId(character);
                
                if (inputEntry.tile is Tile tile && tile.colliderType != Tile.ColliderType.None ||
                    inputEntry.tile is RandomTile randomTile && randomTile.colliderType != Tile.ColliderType.None ||
                    inputEntry.tile is DeprecatedRuleTile ruleTile && ruleTile.m_DefaultColliderType != Tile.ColliderType.None ||
                    inputEntry.tile is DeprecatedRuleObjectTile ruleObjectTile && ruleObjectTile.m_DefaultColliderType != Tile.ColliderType.None) return true;
            }

            return false;
        }
        
        private async Task  WriteToTilemap(Vector3Int[] positions, TileBase[] tiles, int index)
        {
            List<Vector3Int[]> positionLists = SplitArray<Vector3Int>(positions, 1000);
            List<TileBase[]> tileLists = SplitArray<TileBase>(tiles, 1000);
            for (int i = 0; i < positionLists.Count; i++) {
                WriteToTilemapOneStep(positionLists[i], tileLists[i], index);
                await Task.Delay(1);
            }
        }

        private void WriteToTilemapOneStep(Vector3Int[] positions, TileBase[] tiles, int index) 
        {
            if (!writeToMap) return;
            
            if (writeToMapArtStyle) 
            {
                MapArtStyleV2.Instance.WriteFloor(positions, tiles, index);
            } 
            else 
            {
                tilemaps[index].SetTiles(positions, tiles);
            }
        }
        
        private List<T[]> SplitArray<T>(T[] ary, int subSize)
        {
            int count = ary.Length % subSize == 0 ? ary.Length / subSize : ary.Length / subSize + 1;
            List<T[]> subAryList = new List<T[]>();
            for (int i = 0; i < count; i++)
            {
                int index = i * subSize;
                T[] subary = ary.Skip(index).Take(subSize).ToArray();
                subAryList.Add(subary);
            }
            return subAryList;
        }

        public Vector2Int GetStartPosition() {
            return (StartPosition - new Vector2Int(BoardGenerationProfile.boardHorizontalSize, BoardGenerationProfile.boardVerticalSize) / 2) * TileMapScale;
        }

        private bool RegisterPatrolPath() {
            patrolPaths = new List<Vector2Int>();

            List<Vector2Int> startPoints = new List<Vector2Int>();
            for (int i = 0; i < 2; i++) {
                Vector2Int position;
                float startDistance;
                float otherDistance = BoardGenerationProfile.minOtherPatrolDistance + 1;
                var time = 1;
                do {
                    position = GetRandomGridPosition(new Vector2Int(10, 10), new Vector2Int(BoardGenerationProfile.boardHorizontalSize - 10, BoardGenerationProfile.boardVerticalSize - 10));
                    startDistance = Vector2.Distance(position, StartPosition);
                    if (i == 1) {
                        otherDistance = Vector2.Distance(position, startPoints[0]);
                    }
                } while ((IsStopPosition(position.x, position.y) || GetMapSpecialInfo(Box, position.x, position.y) == Box || GetMapSpecialInfo(InCity, position.x, position.y) == InCity ||
                          otherDistance < BoardGenerationProfile.minOtherPatrolDistance || startDistance < BoardGenerationProfile.minPatrolDistance) && time++ < BoardGenerationProfile.patrolMaxTime);
                
                startPoints.Add(position);
            }
            
            if (startPoints.Count != 2) return false;
            
            LogUtil.Log($"({startPoints[0]}, {startPoints[1]})");
            var way = new AStarFindWay(this);
            var positions = way.Find(startPoints[0], startPoints[1], BoardGenerationProfile.maxPatrolWalkDistance);
            
            for (int i = positions.Count - 2; i > 0; i--) {
                if (Vector2.Distance(positions[i], positions[i + 1]) < BoardGenerationProfile.patrolSpace) {
                    positions.RemoveAt(i);
                }
            }
            
            if (positions.Count <= 2)  return false;
            
            patrolPaths.AddRange(positions);
            return true;
        }

        public static IBoardGenerator CreateBoardGenerator(bool writeToMap, Transform content, bool generatedGObject, bool writeToMapArtStyle) {
            var boardGenerator = new BoardGenerator {
                writeToMap = writeToMap,
                content = content,
                generatedGObject = generatedGObject,
                writeToMapArtStyle = writeToMapArtStyle
            };
            return boardGenerator;
        }

        public static IEnumerator ForEachElement<T>(List<T> objs, Action<T, int> callback, int waitNumber = 50) {
            for (int i = 0; i < objs.Count; i++) {
                callback?.Invoke(objs[i], i);
                if (i + 1 % waitNumber == 0) yield return 0;
            }
        }
    }
        
    [Serializable]
    public struct RoomInstantiateInfo {
        public string ID;
        public bool inCity;
        public List<RoomPrefabInfo> prefabInfos;
        public Vector2 position;
        public RoomTemplate templateToSpawn;
    }
}
