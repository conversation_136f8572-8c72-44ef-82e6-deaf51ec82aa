using I2.Loc;
using System;
using System.Collections.Generic;
using UnityEngine;
using Util.TAUtil;

namespace ModeSeason.ComboGun.OpenWorldMap.Items {
    [RequireComponent(typeof(RGNetBehaviour))]
    public abstract class ContainerItemBase : ItemBase, ItemInterface {
        public override bool LockByGroup => true;
        [SerializeField] private AudioClip audio_clip;

        protected ItemInterface the_item;
        protected string _id;
        protected string _containerShopType;
        protected int item_value = 0;
        protected bool can_use = true;

        public virtual void OnLocalPlayerEnter() {
        }

        public virtual void OnLocalPlayerExit() {
        }

        protected virtual void OnTriggerEnter2D(Collider2D other) {
            if (the_item == null) return;

            if (other.gameObject.CompareTag("Body_P")) {
                other.GetComponent<RGController>().SetItemtf(transform);
                if (other.GetComponent<RGController>().IsLocalPlayer()) {
                    Vector3 tempv3 = transform.position + new Vector3(1, 2, 0);
                    var n = the_item.GetItemName();
                    if (the_item is CGWeapon w) {
                        n = w.GetNameWithAtch();
                    }

                    UICanvas.GetInstance().ShowObjectInfo(tempv3,
                        RGItem.GetPaymentDialogText(n, GetItemValue(), ItemLevel.GoldCoin),
                        the_item.GetItemLevel());
                    if (sell_obj.GetComponent<RGWeapon>() is { } weapon)
                        weapon.ShowItemInfo();
                }
            }
        }

        private void OnTriggerExit2D(Collider2D other) {
            if (the_item == null) return;

            if (other.gameObject.CompareTag("Body_P")) {
                if (other.GetComponent<RGController>().CompareItem(transform)) {
                    other.GetComponent<RGController>().SetItemtf(null);
                    if (other.GetComponent<RGController>().IsLocalPlayer()) {
                        UICanvas.GetInstance().HideObjectInfo();
                        UICanvas.GetInstance().HideItemInfo();
                    }
                }
            }
        }

        public virtual void ItemTrigger(RGController controller) {
            if (the_item == null) return;

            if (controller.IsLocalPlayer()) {
                if (RGGameProcess.Inst.coin_value >= GetItemValue()) {
                    if (NetControllerManager.Inst.playerCount == 0) {
                        RGGameProcess.Inst.ConsumeCoin(GetItemValue(), emStatisticsType.ComboGunBuyProj);
                        if (GetItemValue() > 0) {
                            UICanvas.GetInstance().ShowTextTalk(controller.transform,
                                string.Format("-{0}", RGItem.GetStringWithColor(GetItemValue(), ItemLevel.GoldCoin)),
                                2.5f, 1f);
                        }

                        UICanvas.GetInstance().UpdateCoin();
                        transform.GetComponent<BoxCollider2D>().enabled = true;
                        the_item.ItemTrigger(controller);
                        UICanvas.GetInstance().HideObjectInfo();
                        UICanvas.GetInstance().HideItemInfo();
                        RGMusicManager.GetInstance().PlayEffect(audio_clip);
                        TrackData();

                        the_item = null;
                    } else {
                        MessageManager.Inst.SendItemTriggerMessage(RGGetPath.GetNetId(transform),
                            NetControllerManager.Inst.localNetId);
                    }
                } else {
                    UICanvas.GetInstance().ShowTextTalk(controller.transform, ScriptLocalization.Get("I_no_money"),
                        2.5f, 1f);
                    UICanvas.GetInstance().HideObjectInfo();
                    UICanvas.GetInstance().HideItemInfo();
                }
            }

            controller.SetItemtf(null);

            // 标识已经清除
            key = Int32.MaxValue;
            GridDatas![0].key = key;
        }

        public virtual void SyncItemTrigger(RGController controller, string extraInfo) {
            if (controller.IsLocalPlayer()) {
                RGGameProcess.Inst.ConsumeCoin(GetItemValue(), emStatisticsType.ComboGunBuyProj);
                if (GetItemValue() > 0) {
                    UICanvas.GetInstance().ShowTextTalk(controller.transform,
                        string.Format("-{0}", RGItem.GetStringWithColor(GetItemValue(), ItemLevel.GoldCoin)), 2.5f, 1f);
                }

                UICanvas.GetInstance().UpdateCoin();
                UICanvas.GetInstance().HideObjectInfo();
                UICanvas.GetInstance().HideItemInfo();
                the_item.ItemTrigger(controller);
                TrackData();

                the_item = null;
            } else {
                if (sell_obj) {
                    sell_obj.SetActive(false);
                }
            }

            transform.GetComponent<BoxCollider2D>().enabled = false;
            RGMusicManager.GetInstance().PlayEffect(audio_clip);
        }

        private void TrackData() {
            if (_containerShopType == "Weapon") {
                CGModeData.LocalData.buyWeaponTime++;
            }

            var dict = new Dictionary<string, object> {
                { "item_id", _id },
                { "item_type", _containerShopType },
                { "coin", RGGameProcess.Inst.coin_value },
            };
            BattleStatistics.AddCGWeapons(ref dict, CGModeData.LocalData);
            TAUtil.Track("combo_gun_buy_item", dict);
        }

        public virtual int GetItemValue() {
            int value = item_value;
            if (RGGameProcess.Inst.this_index > 0 && BattleData.data.CompareFactor(emBattleFactor.Inflation)) {
                value *= 2;
            }
            
            if (BattleData.data.HasBuff(emBuff.ExpertShop)) {
                value = Mathf.RoundToInt(value * 0.75f * DataUtil.CalBuffExpertShopExtraRate(0.75f));
                value = Mathf.Max(1, value);
            }

            return value;
        }

        public string GetItemName() {
            return "name";
        }

        public int GetItemLevel() {
            return 0;
        }

        public bool CanUse() {
            if (can_use) {
                can_use = false;
                return true;
            } else {
                return false;
            }
        }

        public override void SetItem(GameObject obj) {
            if (obj != sell_obj) // 被替换
                GameObject.Destroy(sell_obj);
            item = transform.Find("item");
            sell_obj = obj;
            sell_obj.transform.parent = item.transform;
            sell_obj.transform.localPosition = Vector3.zero;
            if (sell_obj.GetComponent<RGWeapon>()) {
                sell_obj.GetComponent<RGWeapon>().AdjustAnchor();
            }

            sell_obj.GetComponent<BoxCollider2D>().enabled = false;
            the_item = sell_obj.GetComponent<ItemInterface>();
            if (the_item == null) {
                Debug.LogWarning($"{sell_obj.name}没有继承接口 ItemInterface！");
                return;
            }

            item_value = the_item.GetItemValue();

            float item_rate = 0f;
            if (RGGameProcess.Inst.this_index > 3 && the_item.GetItemValue() < 199) {
                item_rate += (RGGameProcess.Inst.this_index - 2) * 0.12f;
            }

            if (BattleData.data.HasBuff(emBuff.ExpertShop)) {
                item_rate -= 0.5f;
            }

            item_value += (int)(item_rate * the_item.GetItemValue());

            var priceMod = GetComponent<RGContainer.IShopPriceModifier>();
            if (priceMod != null)
                item_value = priceMod.Modify(item_value);
        }
    }
}