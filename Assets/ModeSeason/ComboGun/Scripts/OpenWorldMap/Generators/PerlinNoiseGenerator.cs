using cfg.ComboGun;
using UnityEngine;

namespace ModeSeason.ComboGun.OpenWorldMap.Generators {
    [CreateAssetMenu(menuName = "OpenWorldMap/生成器/噪声")]
    public class PerlinNoiseGenerator : Generator {
        private float clip = .5f;
        private float noiseScaler = .5f;
        public char filledSpaceChar = 'm';

        public override void SetInitData(BoardGenerator boardGenerator, object initData) {
            cfg.ComboGun.CGMapLevel mapLevelData = (cfg.ComboGun.CGMapLevel)initData;
            clip = mapLevelData.Layer6Clip;
            noiseScaler = mapLevelData.Layer6NoiseScaler;
        }

        public override void Generate(BoardGenerator boardGenerator, System.Random random) {
            int randomOffsetX = random.Next(0, 100);
            int randomOffsetY = random.Next(0, 100);

            for (int x = 0; x < boardGenerator.BoardGenerationProfile.boardHorizontalSize; x++) {
                for (int y = 0; y < boardGenerator.BoardGenerationProfile.boardVerticalSize; y++) {
                    var point = Mathf.PerlinNoise(x * noiseScaler + randomOffsetX, y * noiseScaler + randomOffsetY);

                    if (!(point <= clip)) {
                        continue;
                    }

                    if (boardGenerator.WritePositionCharacter(x, y,
                            boardGenerator.BoardGenerationProfile.emptySpaceChar, overwriteFilledSpaces,
                            overwriteRoomSpaces)) {
                        boardGenerator.SetMapSpecialInfo(BoardGenerator.Box, x, y, BoardGenerator.Box);
                    }
                }
            }
        }
    }
}