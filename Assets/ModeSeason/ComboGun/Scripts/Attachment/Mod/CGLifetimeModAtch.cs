using System.Collections.Generic;

namespace ModeSeason.ComboGun {
    public class CGLifetimeModAtch : CGModifierAtch {
        public override CGProjectile SingleMod(CGProjectile proj, CGBlockAttackContext context) {
            if (proj.lifetime > 0) {
                proj.lifetime += 2;
            }

            if (proj.lifeDistance > 0) {
                proj.lifeDistance += 4;
            }

            return proj;
        }

        public override List<CGProjectile> MultiMod(List<CGProjectile> list, CGBlockAttackContext context) {
            return list;
        }
    }
}