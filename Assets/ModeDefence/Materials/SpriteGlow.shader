// Upgrade NOTE: replaced 'mul(UNITY_MATRIX_MVP,*)' with 'UnityObjectToClipPos(*)'

// Unity built-in shader source. Copyright (c) 2016 Unity Technologies. MIT license (see license.txt)

Shader "Sprites/SpriteGlow"
{
    Properties
    {
        [PerRendererData] _MainTex ("Sprite Texture", 2D) = "white" {}
        _Color ("Tint", Color) = (1,1,1,1)
        [MaterialToggle] PixelSnap ("Pixel snap", Float) = 0
        [HideInInspector] _RendererColor ("RendererColor", Color) = (1,1,1,1)
        [HideInInspector] _Flip ("Flip", Vector) = (1,1,1,1)
        [PerRendererData] _AlphaTex ("External Alpha", 2D) = "white" {}
        [PerRendererData] _EnableExternalAlpha ("Enable External Alpha", Float) = 0
        _GlowLevel ("_GlowLevel", int) = 0
        [HDR]_GlowColor ("GlowColor", Color) = (1, 1, 1, 1)
        _Rect ("_Rect", Vector) = (0, 0, 1, 1)
        _Streamer1 ("_Streamer1", 2D) = "white"{}
        _Streamer2 ("_Streamer2", 2D) = "white"{}
        _Streamer3 ("_Streamer3", 2D) = "white"{}
        _Streamer4 ("_Streamer4", 2D) = "white"{}
        _WaveLight ("_WaveLight", 2D) = "white"{}
    }

    SubShader
    {
        Tags
        {
            "Queue"="Transparent"
            "IgnoreProjector"="True"
            "RenderType"="Transparent"
            "PreviewType"="Plane"
            "CanUseSpriteAtlas"="True"
        }

        Cull Off
        Lighting Off
        ZWrite Off
        Blend SrcAlpha OneMinusSrcAlpha

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma target 2.0
            #pragma multi_compile_instancing
            #pragma multi_compile _ PIXELSNAP_ON
            #pragma multi_compile _ ETC1_EXTERNAL_ALPHA
            #include "UnitySprites.cginc"

            int _GlowLevel;
            fixed4 _Rect;
            fixed4 _GlowColor;
            sampler2D _Streamer1;
            sampler2D _Streamer2;
            sampler2D _Streamer3;
            sampler2D _Streamer4;
            sampler2D _WaveLight;

            struct vert2frag
            {
                float4 vertex : SV_POSITION;
                fixed4 color : COLOR;
                float2 texcoord : TEXCOORD0;
                UNITY_VERTEX_OUTPUT_STEREO
            };

            vert2frag vert(appdata_t IN)
            {
                vert2frag OUT;

                UNITY_SETUP_INSTANCE_ID(IN);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(OUT);

                OUT.vertex = UnityFlipSprite(IN.vertex, _Flip);
                OUT.vertex = UnityObjectToClipPos(OUT.vertex);
                OUT.texcoord = IN.texcoord;
                OUT.color = IN.color * _Color * _RendererColor;

                #ifdef PIXELSNAP_ON
                OUT.vertex = UnityPixelSnap (OUT.vertex);
                #endif

                return OUT;
            }

            float4 intensity(fixed4 pixel_color, float intensity) : COLOR
            {
                if (pixel_color.a == 0) return pixel_color;

                pixel_color.rgb /= pixel_color.a;

                // Apply brightness.
                pixel_color.rgb += pixel_color.rgb * intensity * 4.5;

                // Return final pixel color.
                pixel_color.rgb *= pixel_color.a;


                return pixel_color;
            }

            float4 brightness(fixed4 pixel_color, float brightness) : COLOR
            {
                if (pixel_color.a == 0) return pixel_color;

                pixel_color.rgb /= pixel_color.a;

                // Apply brightness.
                pixel_color.rgb += brightness;

                // Return final pixel color.
                pixel_color.rgb *= pixel_color.a;


                return pixel_color;
            }

            fixed4 streamer(fixed4 c, fixed2 uv, sampler2D _texture)
            {
                fixed4 white = fixed4(1, 1, 1, 1);
                if (all(white == tex2D(_texture, uv)))
                {
                    // // if not using gamma color space, convert from linear to gamma
                    // #ifndef UNITY_COLORSPACE_GAMMA
                    // c.rgb = LinearToGammaSpace(c.rgb);
                    // #endif
                    // // apply intensity exposure
                    // c.rgb *= pow(2.0, 1);
                    // // if not using gamma color space, convert back to linear
                    // #ifndef UNITY_COLORSPACE_GAMMA
                    // c.rgb = GammaToLinearSpace(c.rgb);
                    // #endif
                    return brightness(c, (_GlowLevel - 5) / 6);
                }
                return c;
            }

            fixed4 streamer(float2 uv, fixed4 c)
            {
                fixed glow_time = 0.6f;
                fixed frame_num = 4;
                fixed glow_interval = 12;
                fixed time_mod = fmod(_Time.y, glow_interval);
                if (time_mod < glow_time)
                {
                    fixed frame_time = glow_time / frame_num;
                    int current_frame = time_mod / frame_time;
                    switch (current_frame)
                    {
                    case 0: c = streamer(c, uv, _Streamer1);
                        break;
                    case 1: c = streamer(c, uv, _Streamer2);
                        break;
                    case 2: c = streamer(c, uv, _Streamer3);
                        break;
                    case 3: c = streamer(c, uv, _Streamer4);
                        break;
                    default: break;
                    }
                }
                return c;
            }

            fixed3 hue(fixed3 c, float hue)
            {
                float angle = radians(hue);
                float3 k = float3(0.57735, 0.57735, 0.57735);
                float cos_angle = cos(angle);
                //Rodrigues' rotation formula
                return c * cos_angle + cross(k, c) * sin(angle) + k * dot(k, c) * (1 - cos_angle);
            }

            fixed4 saturation(fixed4 c, fixed s)
            {
                s /= 2;
                float3 intensity = dot(c.rgb, float3(0.299, 0.587, 0.114));
                c.rgb = lerp(intensity, c.rgb, s);
                return c;
            }

            fixed4 wavelight(float2 uv, fixed4 c)
            {
                float2 offset = float2(_Time.y * 0.5f, 0);
                uv -= offset;
                fixed s = tex2D(_WaveLight, uv).a;
                c.rgb = hue(c.rgb, s * 5);
                fixed b = s - 0.8;
                return intensity(saturation(c, s * 2), b / 8);
            }

            fixed4 flash(fixed4 c)
            {
                fixed speed = 5;
                fixed hue_range = 0 + _GlowLevel / 3;
                fixed bright_range = 0.025f;
                c.rgb = hue(c.rgb, sin(_Time.y * speed) * hue_range);
                return intensity(c, sin(_Time.y * speed) * bright_range);
            }

            fixed4 frag(vert2frag i) : SV_Target
            {
                float2 uv = (i.texcoord - _Rect.xy) / (_Rect.zw - _Rect.xy);
                fixed4 c = SampleSpriteTexture(i.texcoord) * i.color;
                if (c.r + c.g + c.b > 0.6)
                {
                    if (_GlowLevel < 6)
                    {
                        c = intensity(c, -0.1 * _GlowLevel / 5.0);
                    }
                    else if (_GlowLevel < 11)
                    {
                        c = intensity(c, 0.2 * (_GlowLevel - 5) / 5.0);
                    }
                    else
                    {
                        c = intensity(c, 0.2);
                    }

                    if (_GlowLevel > 5)
                    {
                        // c = brightness(c, 0.1 * (_GlowLevel - 5) / 20.0);
                    }
                }
                c = wavelight(uv, c);
                c = flash(c);

                float c_max = 1.5;
                if (c.r > c_max) c.r = c_max;
                if (c.g > c_max) c.g = c_max;
                if (c.b > c_max) c.b = c_max;

                if (_GlowLevel > 5)
                {
                    c = streamer(uv, c);
                }
                
                c.rgb *= c.a;

                return c;
            }
            ENDCG
        }
    }
}