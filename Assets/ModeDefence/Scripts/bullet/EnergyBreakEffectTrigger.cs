using UnityEngine;

namespace ModeDefence
{
    public class EnergyBreakEffectTrigger : RGBulletEffectTrigger
    {
        public override void TriggerWith(Collider2D other, int damage, bool isCritic, bool canThrough) {
            var controller = other.GetComponent<RGController>();
            if (controller && carrier && carrier.energy_break) {
                controller.attribute.RestoreEnergy(-controller.attribute.max_energy / 20, breakEnergy:true);
                destroyState = emDestroyState.ForceDestroy;
            }
        }
    }
}
