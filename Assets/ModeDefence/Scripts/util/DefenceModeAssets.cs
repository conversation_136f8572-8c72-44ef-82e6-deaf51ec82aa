using Com.LuisPedroFonseca.ProCamera2D;
using Sirenix.OdinInspector;
using System;
using System.Collections.Generic;
using UnityEngine;
using Object = UnityEngine.Object;
// ReSharper disable InconsistentNaming
// ReSharper disable UnassignedField.Global
// ReSharper disable MemberCanBePrivate.Global

namespace ModeDefence
{
    [CreateAssetMenu(fileName = "defence_assets", menuName = "Data/DefenceAssets")]
    public class DefenceModeAssets : SerializedScriptableObject {
        private static DefenceModeAssets _Assets;

        public static DefenceModeAssets Assets =>
            _Assets
                ? _Assets
                : _Assets = ResourcesUtil.Load<DefenceModeAssets>("ModeDefence/Data/defence_assets.asset");

        /// <summary>
        /// 销毁资源
        /// </summary>
        public static void Unload() {
            if (null == _Assets) {
                return;
            }
            DestroyImmediate(_Assets, true);
        } 
        
        public List<DefenceTower> towers;

        public GameObject tutor, weapon_master, tower_master;

        public Material fast_bloom;

        public List<Object> temp;

        public Sprite square, emotion, eye_lock;

        public AudioClip drop_weapon;

        public GameObject ui_dialog;
    
        [Button]
        public void AddAssets() {
            foreach (var o in temp) {
                if (assets.ContainsKey(o.name)) assets[o.name] = o;
                else assets.Add(o.name, o);
            }
        }

        public Dictionary<string, Object> assets;

        public static T Load<T>(string name) where T : Object {
            return Assets.assets[name] as T;
        }
    
        public static GameObject Load(string name) {
            return Load<GameObject>(name);
        }

        public static UINarrativeDialog LoadDialog(string name, Transform target, Action on_finish,
            bool remove_target = true) {
            if (ModeDefenceData.Data.state == DefenceModeState.GAME_OVER) {
                return null;
            }

            UIDefence.Inst.Hide(false, false);
            DefenceMapManager.DefenceMap.enemy_manager.PauseWave();
            CameraTarget newCameraTarget = null;
            if (target != null) {
                newCameraTarget = ProCamera2D.Instance.AddCameraTarget(target, 1, 1, 1, Vector2.up * .5f);
            }
            var ui = Instantiate(Assets.ui_dialog, UICanvas.Inst.transform).GetComponent<UINarrativeDialog>();
            ui.editor = false;
            ui.StartDialog(Load<TextAsset>(name).text, () => {
                UIDefence.Inst.Show();
                if (remove_target && newCameraTarget != null) {
                    if (target != null) {
                        ProCamera2D.Instance.RemoveCameraTarget(target);
                    }
                    else {
                        ProCamera2D.Instance.CameraTargets.Remove(newCameraTarget);
                    }
                }
                if (on_finish != null) {
                    try {
                        on_finish();
                    } catch (Exception e) {
                        Debug.LogError(e);
                    }
                }

                DefenceMapManager.DefenceMap.enemy_manager.ContinueWave();
                foreach (var chat_log in ui.logs) {
                    UIDefence.Inst.AddHistoryMessage(
                        $"\n{chat_log.speaker}{(LanguageUtil.IsChineseI2() ? "：" : ":")}{chat_log.content}");
                }

                UIDefence.Inst.history.GetComponent<UITextContentSizeFilterOld>().UpdateSize();
                Destroy(ui.gameObject);
            });
            return ui;
        }
    }
}