using Sirenix.OdinInspector;
using UnityEngine;

namespace ModeDefence
{
    public class SwirlyAuraEffect : MonoBehaviour {
        public Gradient[] aura_gradients;
        public Gradient[] particle_gradients;
        public Material[] aura_mats;
        public Material[] particle_mats;

        private void Start() {
            Destroy(gameObject, 5);
        }

        [Button(ButtonSizes.Medium)]
        public void ChangeColor(int gradient_index) {
            var colt = GetComponent<ParticleSystem>().colorOverLifetime;
            colt.color = new ParticleSystem.MinMaxGradient(aura_gradients[gradient_index]);
            colt = transform.GetChild(0).GetComponent<ParticleSystem>().colorOverLifetime;
            colt.color = new ParticleSystem.MinMaxGradient(particle_gradients[gradient_index]);
            GetComponent<ParticleSystemRenderer>().material = aura_mats[gradient_index];
            transform.GetChild(0).GetComponent<ParticleSystemRenderer>().material = particle_mats[gradient_index];
        }

        public static SwirlyAuraEffect Create(Transform parent, int gradient_index) {
            GameObject effectGo = ResourcesUtil.Load<GameObject>("ModeDefence/Prefabs/effect/SwirlyAura.prefab");
            var effect = Instantiate(effectGo, parent)
                .GetComponent<SwirlyAuraEffect>();
            effect.ChangeColor(gradient_index);
            return effect;
        }
    }
}
