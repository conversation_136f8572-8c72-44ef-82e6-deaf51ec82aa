namespace ModeDefence
{
    public class Psionic : RGCoin {
        public EnergyDevice device;
        public DefenceTower target_tower => target.GetComponent<DefenceTower>();

        public void Start() { }

        public override void GetItem() {
            if (target_tower.rechargeable && !target_tower.dead) {
                target_tower.Recharge(value);
                UICanvas.GetInstance().ShowTextTokenCoin(target.position, value, 2, target_tower.GetInstanceID().ToString(),
                    DefenceModeConfig.Config.PSIONIC_COLOR);
            }
            PrefabPool.Inst.Store(gameObject);
        }

        public void SetDevice(EnergyDevice device) {
            this.device = device;
        }

        public override void FindTarget() {
            find_target = device.TryGetTarget(out target);
        }
    }
}