using RGScript.Data;
using RGScript.Weapon;
using System.Collections.Generic;
using UnityEngine;

namespace ModeDefence
{
    public class DefenceWeapon : MonoBehaviour {
        private RGWeapon _weapon;
        public RGWeapon weapon => _weapon ? _weapon : _weapon = GetComponent<RGWeapon>();
        public float current_speed_factor = 1f;
        public float weapon_intensity = 1f;
        public float light_intensity = 1f;
        public float particle_intensity = 1f;
        public float gun_point_intensity = 1f;
        public bool light_color_changed;

        public void OnDefenceBulletCreate(GameObject bullet) {
            DefenceBullet.DecorateBullet(bullet, Mathf.Clamp(weapon.level, 0, 15));
        }

        public void SetWeaponLevel(int level) {
            if (level == 0 || weapon == null) return;
            level = Mathf.Clamp(level, 0, 15);

            EnchantWeapon(level);

            if (UIDefence.BLOOM_LEVEL <= 0) return;

            var _renderers = GetComponentsInChildren<Renderer>(true);
            bool changed = false;
            float weapon_intensity, light_intensity, gun_point_intensity, particle_intensity;
            if (weapon.level <= 5) {
                particle_intensity = light_intensity =
                    gun_point_intensity = weapon_intensity = 1 - (weapon.level / 5f) * DefenceModeConfig.Config.DARKNESS_FACTOR;
            } else {
                weapon_intensity = 1 + (float)weapon.level / DefenceModeConfig.Config.DEFENCE_WEAPON_MAX_LEVEL *
                    DefenceModeConfig.Config.BRIGHTNESS_INTENSITY;
                gun_point_intensity = weapon_intensity * DefenceModeConfig.Config.GUN_POINT_INTENSITY;
                particle_intensity = weapon_intensity * DefenceModeConfig.Config.WEAPON_PARTICLE_INTENSITY;
                light_intensity = weapon_intensity * DefenceModeConfig.Config.LIGHT_MATERIAL_INTENSITY;
            }

            foreach (var _renderer in _renderers) {
                if (_renderer.material.name.StartsWith("light")) {
                    if (!light_color_changed) {
                        _renderer.material.TryChangeColorIntensity("_TintColor", 1,
                            DefenceModeConfig.Config.LIGHT_MATERIAL_INIT_INTENSITY);
                        changed = true;
                    }

                    _renderer.material.TryChangeColorIntensity("_TintColor", this.light_intensity, light_intensity);
                } else if (_renderer is SpriteRenderer sprite_renderer && sprite_renderer.sprite) {
                    if (_renderer.name == "gun_point") {
                        foreach (var color_property in ColorUtil.color_properties) {
                            if (_renderer.material.TryChangeColorIntensity(color_property, this.gun_point_intensity,
                                    gun_point_intensity))
                                break;
                        }
                    } else {
                        _renderer.material = DefenceModeAssets.Load<Material>("SpriteGlowMat");
                        var sprite = sprite_renderer.sprite;
                        Vector4 rect = new Vector4(sprite.textureRect.min.x / sprite.texture.width,
                            sprite.textureRect.min.y / sprite.texture.height,
                            sprite.textureRect.max.x / sprite.texture.width,
                            sprite.textureRect.max.y / sprite.texture.height);
                        _renderer.material.SetVector("_Rect", rect);
                        _renderer.material.SetInt("_GlowLevel", level);
                        if (_renderer.transform.localScale == Vector3.one ||
                            _renderer.transform.localScale == (Vector3)Vector2.one) {
                            if (level > 10) {
                                AddStarParticle(_renderer.gameObject);
                                weapon.has_effect = true;
                            } else {
                                ClearStarParticle(_renderer.gameObject);
                                weapon.has_effect = false;
                            }
                        }
                    }
                } else if (_renderer is ParticleSystemRenderer particle_renderer) {
                    particle_renderer.material.TryChangeColorIntensity("_Color", this.particle_intensity,
                        particle_intensity);
                } else {
                    foreach (var color_property in ColorUtil.color_properties) {
                        if (_renderer.material.TryChangeColorIntensity(color_property, this.weapon_intensity,
                                weapon_intensity))
                            break;
                    }
                }
            }

            if (changed) light_color_changed = true;
            this.weapon_intensity = weapon_intensity;
            this.light_intensity = light_intensity;
            this.gun_point_intensity = gun_point_intensity;
            this.particle_intensity = particle_intensity;
        }

        public void EnchantWeapon(int level) {
            EnchantWeaponSpeed(level);
        }

        public void EnchantWeaponSpeed(int level) {
            var speed_factor = 1f;
            if (level >= 5) speed_factor += DefenceModeConfig.Config.DEFENCE_WEAPON_SPEED_DELTA;
            if (level >= 10) speed_factor += DefenceModeConfig.Config.DEFENCE_WEAPON_SPEED_DELTA;
            if (level >= 15) speed_factor += DefenceModeConfig.Config.DEFENCE_WEAPON_SPEED_DELTA;
            weapon.SetWeaponSpeed(1 / current_speed_factor, -1);
            weapon.SetWeaponSpeed(speed_factor, -1);
            current_speed_factor = speed_factor;
        }

        public void AddStarParticle(GameObject obj) {
            if (obj.GetComponent<ParticleSystem>()) return;
            var particle = obj.AddComponent<ParticleSystem>();
            var main = particle.main;
            main.startSize = new ParticleSystem.MinMaxCurve(0.4f, 0.6f);
            main.startLifetime = new ParticleSystem.MinMaxCurve(1);
            main.startSpeed = new ParticleSystem.MinMaxCurve(1);
            var emission = particle.emission;
            emission.enabled = true;
            emission.rateOverTime = new ParticleSystem.MinMaxCurve(0.4f, 0.6f);
            var shape = particle.shape;
            shape.enabled = true;
            shape.shapeType = ParticleSystemShapeType.SpriteRenderer;
            shape.meshShapeType = ParticleSystemMeshShapeType.Triangle;
            shape.spriteRenderer = obj.GetComponent<SpriteRenderer>();
            var rotation_over_lifetime = particle.rotationOverLifetime;
            rotation_over_lifetime.enabled = true;
            rotation_over_lifetime.z = new ParticleSystem.MinMaxCurve(-2f, 2f);
            var size_over_lifetime = particle.sizeOverLifetime;
            size_over_lifetime.enabled = true;
            Keyframe[] keyframes = new Keyframe[3];
            keyframes[0] = new Keyframe(0, 0);
            keyframes[1] = new Keyframe(0.5f, 1f);
            keyframes[2] = new Keyframe(1, 0);
            size_over_lifetime.size = new ParticleSystem.MinMaxCurve(1, new AnimationCurve(keyframes));
            var particle_renderer = particle.GetComponent<ParticleSystemRenderer>();
            particle_renderer.material = DefenceModeAssets.Load<Material>("StarMat");
            particle_renderer.sortingLayerName = "Character";
            particle_renderer.sortingOrder = 100;
        }

        public void ClearStarParticle(GameObject obj) {
            var particle = obj.GetComponent<ParticleSystem>();
            if (particle) {
                var r = obj.GetComponent<ParticleSystemRenderer>();
                if (r && r.material && r.material.name.StartsWith("StarMat"))
                    Destroy(particle);
            }
        }

        public void OnDropWeapon(bool auto_store = true) {
            if (ModeDefenceData.Data.state != DefenceModeState.IN_BATTLE) return;
            if (IsStorable(weapon)) {
                if (auto_store) {
                    weapon.CancelInvoke("StoreWeapon");
                    weapon.Invoke("StoreWeapon", 6);
                }
            }
        }

        public void OnPickUpWeapon() {
            weapon.CancelInvoke("StoreWeapon");
        }

        public static bool IsBattlable(RGWeapon weapon) {
            return weapon
                   && weapon.weapon_type != emWeaponType.Token
                   && !weapon.HasTag(emWeaponTag.NotOnHand)
                   && !weapon.HasTag(emWeaponTag.MercenaryForbidden)
                   && !weapon.HasTag(emWeaponTag.NotOccupied);
        }

        public static bool IsStorable(RGWeapon weapon) {
            return weapon
                   && weapon.weapon_type != emWeaponType.Token
                   && !weapon.HasTag(emWeaponTag.NotOnHand)
                   && !weapon.HasTag(emWeaponTag.NotOccupied)
                   && !weapon.HasTag(emWeaponTag.Unstorable);
        }

        public static List<List<DefenceWeaponDropInfo>> drop_infos;
        public static int GenerateWeaponLevel(RGEController enemy) {
            if (drop_infos == null) {
                drop_infos = new List<List<DefenceWeaponDropInfo>>();
            
                // 0
                drop_infos.Add(new List<DefenceWeaponDropInfo> {
                    new DefenceWeaponDropInfo(0, 25),
                    new DefenceWeaponDropInfo(1, 15),
                });
            
                // 1 ~ 4
                for (int i = 1; i < 5; i++) {
                    drop_infos.Add(new List<DefenceWeaponDropInfo> {
                        new DefenceWeaponDropInfo(i - 1, 10),
                        new DefenceWeaponDropInfo(i, 25),
                        new DefenceWeaponDropInfo(i + 1, 25),
                        new DefenceWeaponDropInfo(i + 2, 15),
                    });
                }
            
                // 5 ~ 8
                for (int i = 5; i < 9; i++) {
                    drop_infos.Add(new List<DefenceWeaponDropInfo> {
                        new DefenceWeaponDropInfo(i - 1, 15),
                        new DefenceWeaponDropInfo(i, 25),
                        new DefenceWeaponDropInfo(i + 1, 25),
                        new DefenceWeaponDropInfo(i + 2, 15),
                    });
                }
                
                // 9 ~ 12
                for (int i = 9; i < 13; i++) {
                    drop_infos.Add(new List<DefenceWeaponDropInfo> {
                        new DefenceWeaponDropInfo(i - 1, 10),
                        new DefenceWeaponDropInfo(i, 25),
                        new DefenceWeaponDropInfo(i + 1, 25),
                        new DefenceWeaponDropInfo(i + 2, 20),
                        new DefenceWeaponDropInfo(i + 3, 15),
                    });
                }
            
                Debug.Log($"Create defence weapon drop infos:{drop_infos.Count}");
            }

            var index = enemy.enemy_level;
            if (enemy.isBoss) index++;
            index = Mathf.Clamp(index, 0, drop_infos.Count - 1);
            return drop_infos[index].GetRandomWeightObject(DefenceMapManager.DefenceMap.RgRandom).level;
        }

        public static RGWeapon DropWeapon(RGEController enemy, RGRandom rg_random) {
            var drop_prob = DefenceModeConfig.Config.DROP_WEAPON_PROBABILITY * (enemy.GetComponent<DefenceEnemyGene>() ? 2 : 1);
            if (enemy.isBoss && !(enemy is PsionicLauncher)) drop_prob = 1;
            if (rg_random.Range(0f, 1f) > drop_prob) return null;
            var weaponName = WeaponDropInfo.Instance.GetWeaponWithDropLevel(
                DefenceModeConfig.Config.DropLevel2Weight.Keys.GetRandomWeightObject(DefenceModeConfig.Config.DropLevel2Weight.Values, rg_random), rg_random);
            
            var rgWeapon = WeaponFactory.CreateWeapon(weaponName, emWeaponSource.DefenceEnemyDrop);
            rgWeapon.name = weaponName;
            rgWeapon.level = Mathf.Clamp(GenerateWeaponLevel(enemy), 0, 15);
            return rgWeapon;
        }

        public static RGWeapon CreateWeapon(WeaponData weapon_data) {
            var weaponName = weapon_data.weapon_name;
            var rgWeapon = WeaponFactory.CreateWeapon(weaponName, weapon_data.WeaponSource);
            var weaponDataTagArray = rgWeapon.GetComponents<IWeaponDataTags>();
            foreach (var weaponDataTags in weaponDataTagArray) {
                weaponDataTags.HandleTags(weapon_data.customTags);
            }
            rgWeapon.level = Mathf.Clamp(weapon_data.level, 0, DefenceModeConfig.Config.DEFENCE_WEAPON_MAX_LEVEL);
            rgWeapon.transform.position = weapon_data.position;
            return rgWeapon;
        }
    }

    public class DefenceWeaponDropInfo : IWeightObject {
        public int level;
        public int weight;

        public DefenceWeaponDropInfo(int level, int weight) {
            this.level = level;
            this.weight = weight;
        }

        public int Weight => weight;
    }
}