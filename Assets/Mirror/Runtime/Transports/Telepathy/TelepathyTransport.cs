// wraps Telepathy for use as HLAPI TransportLayer
using System;
using System.Net;
using System.Net.Sockets;
using UnityEngine;

// Replaced by Kcp November 2020
namespace Mirror
{
    [HelpURL("https://github.com/vis2k/Telepathy/blob/master/README.md")]
    [DisallowMultipleComponent]
    public class TelepathyTransport : Transport
    {
        // scheme used by this transport
        // "tcp4" means tcp with 4 bytes header, network byte order
        public const string Scheme = "tcp4";

        public ushort port = 7777;

        [Header("Common")]
        [Tooltip("Nagle Algorithm can be disabled by enabling NoDelay")]
        public bool NoDelay = true;

        [Tooltip("Send timeout in milliseconds.")]
        public int SendTimeout = 5000;

        [Tooltip("Receive timeout in milliseconds. High by default so users don't time out during scene changes.")]
        public int ReceiveTimeout = 30000;

        [NonSerialized]
        [Header("Server")]
        [Tooltip("Protect against allocation attacks by keeping the max message size small. Otherwise an attacker might send multiple fake packets with 2GB headers, causing the server to run out of memory after allocating multiple large packets.")]
        public int serverMaxMessageSize = 32 * 1024;

        [Tooltip("Server processes a limit amount of messages per tick to avoid a deadlock where it might end up processing forever if messages come in faster than we can process them.")]
        public int serverMaxReceivesPerTick = 10000;

        [Tooltip("Server send queue limit per connection for pending messages. Telepathy will disconnect a connection's queues reach that limit for load balancing. Better to kick one slow client than slowing down the whole server.")]
        public int serverSendQueueLimitPerConnection = 10000;

        [Tooltip("Server receive queue limit per connection for pending messages. Telepathy will disconnect a connection's queues reach that limit for load balancing. Better to kick one slow client than slowing down the whole server.")]
        public int serverReceiveQueueLimitPerConnection = 10000;

        [NonSerialized]
        [Header("Client")]
        [Tooltip("Protect against allocation attacks by keeping the max message size small. Otherwise an attacker host might send multiple fake packets with 2GB headers, causing the connected clients to run out of memory after allocating multiple large packets.")]
        public int clientMaxMessageSize = 32 * 1024;

        [Tooltip("Client processes a limit amount of messages per tick to avoid a deadlock where it might end up processing forever if messages come in faster than we can process them.")]
        public int clientMaxReceivesPerTick = 1000;

        [Tooltip("Client send queue limit for pending messages. Telepathy will disconnect if the connection's queues reach that limit in order to avoid ever growing latencies.")]
        public int clientSendQueueLimit = 10000;

        [Tooltip("Client receive queue limit for pending messages. Telepathy will disconnect if the connection's queues reach that limit in order to avoid ever growing latencies.")]
        public int clientReceiveQueueLimit = 10000;

#if ENABLE_REMOTE_MULTI_GAME
        //webrtc传输层
        public WebRTCClient webrtcClient;
        public WebRTCServer webrtcServer;
#endif
        
        //原传输层
        Telepathy.Client tcpClient;
        Telepathy.Server tcpServer;
        
        private ITransportLayerClient _client; 
        private ITransportLayerServer _server;
        
        protected ITransportLayerClient client {
            get {
                return _client;
            }
            set {
                _client = value;
            }
        }
        
        protected ITransportLayerServer server {
            get {
                return _server;
            }
            set {
                _server = value;
            }
        }
        
        // scene change message needs to halt  message processing immediately
        // Telepathy.Tick() has a enabledCheck parameter that we can use, but
        // let's only allocate it once.
        Func<bool> enabledCheck;

        void Awake()
        {
            // tell Telepathy to use Unity's Debug.Log
            Telepathy.Log.Info = Debug.Log;
            Telepathy.Log.Warning = Debug.LogWarning;
            Telepathy.Log.Error = Debug.LogError;
            
            

            // allocate enabled check only once
            enabledCheck = () => enabled;

            Debug.Log("TelepathyTransport initialized!");
        }

        public override bool Available()
        {
            // C#'s built in TCP sockets run everywhere except on WebGL
            return Application.platform != RuntimePlatform.WebGLPlayer;
        }

        // client
        private void CreateClient()
        {
            // create client
            //client = new Telepathy.Client(clientMaxMessageSize);
            // client hooks
            // other systems hook into transport events in OnCreate or
            // OnStartRunning in no particular order. the only way to avoid
            // race conditions where telepathy uses OnConnected before another
            // system's hook (e.g. statistics OnData) was added is to wrap
            // them all in a lambda and always call the latest hook.
            // (= lazy call)
            //client.OnConnected = () => OnClientConnected.Invoke();
            //client.OnData = (segment) => OnClientDataReceived.Invoke(segment, Channels.Reliable);
            //client.OnDisconnected = () => OnClientDisconnected.Invoke();
            
            tcpClient = new Telepathy.Client(clientMaxMessageSize)
            {
                OnConnected = () => OnClientConnected.Invoke(),
                OnData = (segment) => OnClientDataReceived.Invoke(segment, Channels.Reliable),
                OnDisconnected = () => OnClientDisconnected.Invoke(),
            };
#if ENABLE_REMOTE_MULTI_GAME
            webrtcClient = new WebRTCClient(clientMaxMessageSize)
            {
                OnConnected = () => OnClientConnected.Invoke(),
                OnData = (segment) => OnClientDataReceived.Invoke(segment, Channels.Reliable),
                OnDisconnected = () => OnClientDisconnected.Invoke(),
            };
#endif
            
            

            // client configuration
            // client.NoDelay = NoDelay;
            // client.SendTimeout = SendTimeout;
            // client.ReceiveTimeout = ReceiveTimeout;
            // client.SendQueueLimit = clientSendQueueLimit;
            // client.ReceiveQueueLimit = clientReceiveQueueLimit;
            var transportLayerData = new TransportLayerData
            {
                NoDelay = NoDelay,
                SendTimeout = SendTimeout,
                ReceiveTimeout = ReceiveTimeout,
                SendQueueLimit = clientSendQueueLimit,
                ReceiveQueueLimit = clientReceiveQueueLimit
            };
            
            tcpClient.transportLayerData = transportLayerData;
#if ENABLE_REMOTE_MULTI_GAME
            webrtcClient.transportLayerData = transportLayerData;
#endif
            
            if (_isLanGame)
            {
                client = tcpClient;
            }
            else
            {
#if ENABLE_REMOTE_MULTI_GAME
                client = webrtcClient;
#endif
            }
        }
        public override bool ClientConnected() => client != null && client.ConnectedEX;
        public override void ClientConnect(string address)
        {
            CreateClient();
            client.ConnectEX(address, port);
        }

        public override void ClientConnect(Uri uri)
        {
            CreateClient();
            if (uri.Scheme != Scheme)
                throw new ArgumentException($"Invalid url {uri}, use {Scheme}://host:port instead", nameof(uri));

            int serverPort = uri.IsDefaultPort ? port : uri.Port;
            client.ConnectEX(uri.Host, serverPort);
        }
        public override void ClientSend(ArraySegment<byte> segment, int channelId)
        {
            client?.SendEX(segment);

            // call event. might be null if no statistics are listening etc.
            OnClientDataSent?.Invoke(segment, Channels.Reliable);
        }
        public override void ClientDisconnect()
        {
            client?.DisconnectEX();
            client = null;
        }

        public void LateUpdate()
        {
            if (enabled)
            {
                client?.CheckTimeOutEX();
                server?.CheckTimeOutEX();
            }
        }

        // messages should always be processed in early update
        public override void ClientEarlyUpdate()
        {
            // note: we need to check enabled in case we set it to false
            // when LateUpdate already started.
            // (https://github.com/vis2k/Mirror/pull/379)
            if (!enabled) return;

            // process a maximum amount of client messages per tick
            // IMPORTANT: check .enabled to stop processing immediately after a
            //            scene change message arrives!
            client?.TickEX(clientMaxReceivesPerTick, enabledCheck);
        }

        // server
        public override Uri ServerUri()
        {
            UriBuilder builder = new UriBuilder();
            builder.Scheme = Scheme;
            builder.Host = Dns.GetHostName();
            builder.Port = port;
            return builder.Uri;
        }
        public override bool ServerActive() => server != null && server.ActiveEX;
        public override void ServerStart()
        {
            // create server
            //server = new Telepathy.Server(serverMaxMessageSize);

            // server hooks
            // other systems hook into transport events in OnCreate or
            // OnStartRunning in no particular order. the only way to avoid
            // race conditions where telepathy uses OnConnected before another
            // system's hook (e.g. statistics OnData) was added is to wrap
            // them all in a lambda and always call the latest hook.
            // (= lazy call)
            //server.OnConnected = (connectionId) => OnServerConnected.Invoke(connectionId);
            //server.OnData = (connectionId, segment) => OnServerDataReceived.Invoke(connectionId, segment, Channels.Reliable);
            //server.OnDisconnected = (connectionId) => OnServerDisconnected.Invoke(connectionId);
            tcpServer = new Telepathy.Server(serverMaxMessageSize)
            {
                OnConnected = (connectionId) => OnServerConnected?.Invoke(connectionId),
                OnData = (connectionId, segment) => OnServerDataReceived?.Invoke(connectionId, segment, Channels.Reliable),
                OnDisconnected = (connectionId) => OnServerDisconnected?.Invoke(connectionId),
            };
#if ENABLE_REMOTE_MULTI_GAME
            webrtcServer = new WebRTCServer(serverMaxMessageSize)
            {
                OnConnected = (connectionId) => OnServerConnected?.Invoke(connectionId),
                OnData = (connectionId, segment) => OnServerDataReceived?.Invoke(connectionId, segment, Channels.Reliable),
                OnDisconnected = (connectionId) => OnServerDisconnected?.Invoke(connectionId),
            };
#endif

            // server configuration
            // server.NoDelay = NoDelay;
            // server.SendTimeout = SendTimeout;
            // server.ReceiveTimeout = ReceiveTimeout;
            // server.SendQueueLimit = serverSendQueueLimitPerConnection;
            // server.ReceiveQueueLimit = serverReceiveQueueLimitPerConnection;

            var transportLayerData = new TransportLayerData
            {
                NoDelay = NoDelay,
                SendTimeout = SendTimeout,
                ReceiveTimeout = ReceiveTimeout,
                SendQueueLimit = clientSendQueueLimit,
                ReceiveQueueLimit = clientReceiveQueueLimit
            };
            tcpServer.transportLayerData = transportLayerData;
#if ENABLE_REMOTE_MULTI_GAME
            webrtcServer.transportLayerData = transportLayerData;
#endif
            
            if (_isLanGame)
            {
                server = tcpServer;
            }
            else
            {
#if ENABLE_REMOTE_MULTI_GAME
                server = webrtcServer;
#endif
            }
            server.StartEX(port);
        }

        public override void ServerSend(int connectionId, ArraySegment<byte> segment, int channelId)
        {
            server?.SendEX(connectionId, segment);

            // call event. might be null if no statistics are listening etc.
            OnServerDataSent?.Invoke(connectionId, segment, Channels.Reliable);
        }
        public override void ServerDisconnect(int connectionId) => server?.DisconnectEX(connectionId);
        public override string ServerGetClientAddress(int connectionId)
        {
            try
            {
                return server?.GetClientAddressEX(connectionId);
            }
            catch (SocketException)
            {
                // using server.listener.LocalEndpoint causes an Exception
                // in UWP + Unity 2019:
                //   Exception thrown at 0x00007FF9755DA388 in UWF.exe:
                //   Microsoft C++ exception: Il2CppExceptionWrapper at memory
                //   location 0x000000E15A0FCDD0. SocketException: An address
                //   incompatible with the requested protocol was used at
                //   System.Net.Sockets.Socket.get_LocalEndPoint ()
                // so let's at least catch it and recover
                return "unknown";
            }
        }
        public override void ServerStop()
        {
            server?.StopEX();
            server = null;
        }

        // messages should always be processed in early update
        public override void ServerEarlyUpdate()
        {
            // note: we need to check enabled in case we set it to false
            // when LateUpdate already started.
            // (https://github.com/vis2k/Mirror/pull/379)
            if (!enabled) return;

            // process a maximum amount of server messages per tick
            // IMPORTANT: check .enabled to stop processing immediately after a
            //            scene change message arrives!
            server?.TickEX(serverMaxReceivesPerTick, enabledCheck);
        }

        // common
        public override void Shutdown()
        {
            Debug.Log("TelepathyTransport Shutdown()");
            client?.DisconnectEX();
            client = null;
            server?.StopEX();
            server = null;
        }

        public override int GetMaxPacketSize(int channelId)
        {
            return serverMaxMessageSize;
        }

        public override string ToString()
        {
            if (server != null && server.ActiveEX && server.IsListenerNotNull)
            {
                // printing server.listener.LocalEndpoint causes an Exception
                // in UWP + Unity 2019:
                //   Exception thrown at 0x00007FF9755DA388 in UWF.exe:
                //   Microsoft C++ exception: Il2CppExceptionWrapper at memory
                //   location 0x000000E15A0FCDD0. SocketException: An address
                //   incompatible with the requested protocol was used at
                //   System.Net.Sockets.Socket.get_LocalEndPoint ()
                // so let's use the regular port instead.
                return $"Telepathy Server port: {port}";
            }
            
            if (client != null && (client.ConnectingEX || client.ConnectedEX))
            {
                return $"Telepathy Client port: {port}";
            }
            
            return "Telepathy (inactive/disconnected)";
        }
        
        private bool _isLanGame = true;
        public override void SelectTransportLayer(bool isLanGame) {
            base.SelectTransportLayer(isLanGame);
            _isLanGame = isLanGame;
            if (null == client || null == server)
            {
                return;
            }
            switch (isLanGame)
            {
                case true when (client != tcpClient && server != tcpServer):
                    client.DisconnectEX();
                    server.StopEX();
                    client = tcpClient;
                    server = tcpServer;
                    Telepathy.Log.Info("Telepathy 切换tcp socket传输层");
                    break;
#if ENABLE_REMOTE_MULTI_GAME
                case false when (client != webrtcClient && server != webrtcServer):
                    client.DisconnectEX();
                    server.StopEX();
                    client = webrtcClient;
                    server = webrtcServer;
                    Telepathy.Log.Info("Telepathy 切换webrtc传输层");
                    break;
#endif
            }
        }
        
        public override void OnApplicationQuit() {
            client?.CloseConnectionEX();
            server?.CloseConnectionEX();
            base.OnApplicationQuit();
        }
    }
}
