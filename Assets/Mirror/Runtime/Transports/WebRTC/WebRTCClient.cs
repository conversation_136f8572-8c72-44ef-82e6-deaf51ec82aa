#if ENABLE_REMOTE_MULTI_GAME
using System;
using System.Linq;
using Telepathy;
using ChillyRoom.WebRTC;
using Unity.WebRTC;
using System.Threading.Tasks;
using Mirror.Runtime.Transports.WebRTC.CustomTransportLayer;
using Mirror.WebRTC;
using UnityEngine;
using WebSocketSharp;
using Logger = Telepathy.Log;

public class WebRTCClient : Common, ITransportLayerClient
{
    public static readonly int SYC = 12345;
    private bool _connected = false;
    private string signalingSvr = "";
    public string turnConfig { get; set; }

    private string roomName;


    private static RoomConnector roomConnector;

    private Task<RTCDataChannel> dataChannelTask;

    private RTCDataChannel channel;

    volatile bool _Connecting;
    public bool Connecting => _Connecting;

    private int counter;

    public bool Connected => //_connected &&
        null != roomConnector &&
        null != dataChannelTask &&
        null != channel &&
        channel.ReadyState == RTCDataChannelState.Open;

    // SafeQueue<byte[]> sendQueue = new SafeQueue<byte[]>();
    private Task<RTCDataChannel> connTask = null;

    public double LastRecvTime = Double.MaxValue;

    public bool IsTimeout => Time.time - LastRecvTime > WebRTCInstance.TimeOutTime;

    public WebRTCClient(int maxMessageSize) : base(maxMessageSize)
    {
    }

    public void Connect(string remoteSvrInfoJson, int _)
    {
        RemoteSvrInfo remoteSvrInfo = null;
        try
        {
            remoteSvrInfo = JsonUtility.FromJson<RemoteSvrInfo>(remoteSvrInfoJson);
        }
        catch (Exception e)
        {
            Logger.Error("remoteSvrInfo json err: " + e);
        }

        if (null != remoteSvrInfo)
        {
            Connect(remoteSvrInfo.signalingSvr,
                remoteSvrInfo.turnSvrInfo,
                remoteSvrInfo.roomName,
                remoteSvrInfo.clientId);
        }
    }

    public void Connect(string signalingSvr, string turnConfig, string roomName, string clientId)
    {
        if (!string.IsNullOrWhiteSpace(signalingSvr))
        {
            this.signalingSvr = signalingSvr;
        }

        if (!string.IsNullOrWhiteSpace(turnConfig))
        {
            this.turnConfig = turnConfig;
        }

        if (!string.IsNullOrWhiteSpace(roomName))
        {
            this.roomName = roomName;
        }

        Logger.Info(
            $"Connecting signalingSvr: {this.signalingSvr}, turnCfg: {this.turnConfig}, roomName: {this.roomName}");

        if (Connecting || Connected) return;
        _Connecting = true;
        connTask = ConnectAsync(roomName, clientId);
    }

    private static RoomConnector lastRoomConnector;
    private bool _connectedEx;

    private async Task<RTCDataChannel> ConnectAsync(string inviteCode, string clientId)
    {
        lastRoomConnector = roomConnector;
        roomConnector?.disconnect();
        roomConnector = new RoomConnector(this.signalingSvr, this.turnConfig);
        // roomConnector.setTransportPolicy(true);
        Logger.Info("Client ConnectAsync");
        TrackUtil.WebRtcClientConnectStepTrack(inviteCode, "ConnectAsync_Start");
        this.dataChannelTask = roomConnector.connectToRoom(inviteCode, clientId);
        try
        {
            this.channel = await this.dataChannelTask;
        }
        catch (SignalingException e)
        {
            this.channel = null;
            WebRTCInstance.Inst.OnClientConnectError(e.ErrorCode);
            return null;
        }
        catch (TaskCanceledException e)
        {
            if (lastRoomConnector != roomConnector)
            {
                this.channel = null;
                WebRTCInstance.Inst.OnClientConnectError(-1); //timeout error    
            }

            return null;
        }
        catch (WebSocketException e)
        {
            if (lastRoomConnector != roomConnector)
            {
                this.channel = null;
                WebRTCInstance.Inst.OnClientConnectError(-3); //websocket error
            }

            return null;
        }
        catch (Exception e)
        {
            this.channel = null;
            WebRTCInstance.Inst.OnClientConnectError(-2); //unknow error
            Logger.Error("webrtc.client unknow exception: " + e);
            return null;
        }
        TrackUtil.WebRtcClientConnectStepTrack(inviteCode, "ConnectAsync_End");
        
        int connectionId = TransportLayerUtil.NextConnectionId(ref counter);
        _connected = false;
        Send(BitConverter.GetBytes(SYC));
        channel.OnMessage = bytes =>
        {
            //Logger.Info($"client recv msg len: {bytes.Length}");

            LastRecvTime = Time.time;
            if (!_connected)
            {
                int value = BitConverter.ToInt32(bytes, 0);
                if (value == WebRTCServer.ACK)
                {
                    Logger.Info("client recv ack data");
                    _connected = true;
                }

                if (_connected)
                {
                    OnConnected?.Invoke();
                    TrackUtil.WebRtcClientConnectStepTrack(inviteCode, "OnConnected");
                }
                // receiveQueue.Enqueue(new Message(connectionId, EventType.Connected, null));
                return;
            }

            if (_connected)
            {
                // receiveQueue.Enqueue(new Message(connId, EventType.Data, bytes));
                OnData?.Invoke(new ArraySegment<byte>(bytes));
            }
        };

        // this.channel.OnClose = (errType, errMsg) => {
        channel.OnClose = () =>
        {
            // receiveQueue.Enqueue(new Message(connectionId, EventType.Disconnected, null));
            OnDisconnected?.Invoke();
            Logger.Info("client OnClose");
        };
        return dataChannelTask.Result;
    }


    void OnReceive(byte[] bytes)
    {
    }

    void OnDisConnect()
    {
    }

    public void Disconnect()
    {
        LastRecvTime = Double.MaxValue;
        if (Connecting || Connected)
        {
            if (Connected)
            {
                channel.Close();
                _connected = false;
            }

            _Connecting = false;

            // roomConnector = null;
            // dataChannelTask = null;
        }
    }

    public bool Send(byte[] data)
    {
        if (Connected)
        {
            if (data.Length < MaxMessageSize)
            {
                //Logger.Info($"client send msg len: {data.Length}");

                channel.Send(data);
                return true;
            }

            Logger.Error("WebRTC Client.Send: message too big: " + data.Length + ". Limit: " + MaxMessageSize);
            return false;
        }

        //Logger.Warning("WebRTC Client.Send: not connected!");
        return false;
    }

    public void CloseConnection()
    {
        LastRecvTime = Double.MaxValue;
        _connected = false;
        if (null != connTask)
        {
            try
            {
                connTask.Dispose(); //避免task卡死Unity    
            }
            catch (Exception e)
            {
                Logger.Error("client connTask.Dispose : " + e);
            }
        }

        if (null != roomConnector)
        {
            try
            {
                roomConnector.disconnect();
            }
            catch (Exception e)
            {
                Logger.Error("client roomConnector disConn : " + e);
            }
        }
    }


    #region 实现传输层的统一接口

    public Action OnConnected { get; set; }
    public Action<ArraySegment<byte>> OnData { get; set; }
    public Action OnDisconnected { get; set; }

    bool ITransportLayerClient.ConnectedEX => _connectedEx;

    public bool ConnectingEX { get; }
    public TransportLayerData transportLayerData { get; set; }

    public bool ConnectedEX()
    {
        return Connected;
    }

    public void SetNoDelayEX(bool nodelay)
    {
        NoDelay = nodelay;
    }

    public bool SendEX(ArraySegment<byte> message)
    {
        return Send(message.ToArray());
    }

    public void ConnectEX(string address, int port)
    {
        this.Connect(address, port);
    }

    // public bool GetNextMessageEX(out Message msg) {
    //     return this.GetNextMessage(out msg);
    // }

    public void DisconnectEX()
    {
        this.Disconnect();
    }

    public void CloseConnectionEX()
    {
        this.CloseConnection();
    }

    public void CheckTimeOutEX()
    {
        if (IsTimeout && Connected)
        {
            Disconnect();
            //Logger.Info("webrtc.client timeout");
        }
    }

    public int TickEX(int processLimit, Func<bool> checkEnabled = null)
    {
        return 0;
    }

    #endregion
}
#endif