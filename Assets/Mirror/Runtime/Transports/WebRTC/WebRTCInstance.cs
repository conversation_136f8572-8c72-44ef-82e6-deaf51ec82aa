#if ENABLE_REMOTE_MULTI_GAME
using System;
using ChillyRoom.WebRTC;
using Mirror;
using UnityEngine;
using Logger = Telepathy.Log;

public class WebRTCInstance : USingleton<WebRTCInstance>
{
#if UNITY_EDITOR
    public static double TimeOutTime = 20f; //连接超时20s
#else
    public const double TimeOutTime = 20f; //连接超时20s
#endif

    #region SvrConfig

    public string SignalingSvrAddress = "";
    public string TurnSvrCfg;
    public string RoomId = "";
    public string Tokeon = "";

    public void SetSvrInfo(string signalingSvr, string turnSvrCfg, string roomId)
    {
        this.SignalingSvrAddress = signalingSvr;
        this.TurnSvrCfg = turnSvrCfg;
        this.RoomId = roomId;
    }

    #endregion


    #region Client

    public delegate void ClientConnectError(int errorCode);

    public event ClientConnectError OnClientConnectErrorCb;

    public void AddClientConnectErrorCallback(ClientConnectError cb)
    {
        OnClientConnectErrorCb += cb;
    }

    public void RemoveClientConnectErrorCallBack(ClientConnectError cb)
    {
        OnClientConnectErrorCb -= cb;
    }

    public void OnClientConnectError(int errorCode)
    {
        OnClientConnectErrorCb?.Invoke(errorCode);
    }

    #endregion

    #region Server

    public delegate void ServerConnectError(SignalingException.SignalingError errorCode);

    public event ServerConnectError OnServerConnectErrorCb;

    public void AddServerConnectErrorCallback(ServerConnectError cb)
    {
        OnServerConnectErrorCb += cb;
    }

    public void RemoveServerConnectErrorCallback(ServerConnectError cb)
    {
        OnServerConnectErrorCb -= cb;
    }

    public void OnServerConnectError(SignalingException.SignalingError errorCode)
    {
        OnServerConnectErrorCb?.Invoke(errorCode);
    }

    public RoomHost roomHost;

    public void ConnectSignalSvr(Action successCb, Action<int> failedCb, bool isServer = false)
    {
        Logger.Info($"Time: {Time.realtimeSinceStartup} RoomHost.connectToRoom");
        roomHost?.stopAcceptingClients();
        roomHost = new RoomHost(this.SignalingSvrAddress, this.TurnSvrCfg);
        roomHost.SignalingStopped += exception =>
        {
            if (null == exception)
            {
                return;
            }

            if (exception is SignalingException)
            {
                failedCb?.Invoke((exception as SignalingException).ErrorCode);
            }
            else
            {
                Debug.LogError("exception:" + exception);
                failedCb?.Invoke(-1);
            }
        };
        Debug.Log($"ConnectSignalSvr {isServer}");
        if (Transport.activeTransport is TelepathyTransport && isServer) {
            (Transport.activeTransport as TelepathyTransport)?.webrtcServer?.AddOnClientConnectCb(roomHost);
        }
        
        roomHost.OnConnected += host => { successCb?.Invoke(); };

        roomHost.connectToRoom(this.RoomId, "1");
    }

    public void StopSignalSvr()
    {
        roomHost?.stopAcceptingClients();
    }

    #endregion
}
#endif