
using System;
using System.Threading.Tasks;
using ChillyRoom.Agreement.V1;
using ChillyRoom.Privacy;
using Newtonsoft.Json;
using UnityEngine;

namespace Generated.ChillyRoomSdkClient
{
    public partial class ChillyRoomService
    {
        public AgreementClient Agreement;
        public ChillyRoomSdkClient.ChillyRoomService AddAgreementIntegration()
        {
            Agreement = new AgreementClient(
                "",//http://soulknight.api-gateway.chilly.room
                Core.Client.Client.AsHttpClient());
            PrivacyViewManager.Instance.SetCommonHttpClient(Agreement);
            PrivacyViewManager.Instance.SetWebClient(Core.WebViewClient);
            return this;
            
        }
    }
}

