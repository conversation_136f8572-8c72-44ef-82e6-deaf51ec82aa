using System;
using ChillyRoom.RealName.Client;
using ChillyRoom.RealName.Utils;
using UnityEngine;

namespace Generated.ChillyRoomSdkClient {
    public partial class ChillyRoomService {
        //实名的核心Client
        public RealNameClient RealName;

        //防沉迷的核心Client
        public AntiAddictionClient AntiAddiction;

        //内网：https://idcheck.chilly.room
        //新内网域名：https://aas.test.chilly.tech
        //外网：https://aas.chillyroom.com
        private String _realNameGateWay = "https://aas.chillyroom.com";

        public ChillyRoomSdkClient.ChillyRoomService AddRealNameIntegration() {
            if (Core.Client.Client.AsHttpClient().BaseAddress.Host.EndsWith(".room")) {
                _realNameGateWay = "https://idcheck.chilly.room";
            }

            if (Core.Client.Client.AsHttpClient().BaseAddress.Host.EndsWith(".tech")) {
                _realNameGateWay = "https://aas.test.chilly.tech";
            }

            Debug.Log(
                $"[MT] Detected the current game gateway as: {Core.Client.Client.AsHttpClient().BaseAddress.Host}, automatically adjusting the real-name gateway to: {_realNameGateWay}.");

            RealName = new RealNameClient(Core.Client.Client.AsHttpClient(), _realNameGateWay);
            AntiAddiction = new AntiAddictionClient(RealName);


            ServerTimePoller.SetAutoSyncServerTimeFunc(() => {
                return Core.GetUtcDateTimeFromRemoteServer();
            });
            return this;
        }
    }
}