using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace Activities.Christmas2024.Scripts{
    public class UIToggleActiveObject : MonoBehaviour {
        public GameObject activeObject;
        public GameObject disActiveObject;
    
        public Toggle toggle;
        private void OnEnable() {
            if (!toggle) {
                toggle = GetComponent<Toggle>();
            }
            toggle.onValueChanged.AddListener(OnToggle);
            OnToggle(toggle.isOn);
        }

        private void OnToggle(bool isOn) {
            if (activeObject) {
                activeObject.SetActive(isOn);
            }
            if (disActiveObject) {
                disActiveObject.SetActive(!isOn);
            }
        }
    }
}
