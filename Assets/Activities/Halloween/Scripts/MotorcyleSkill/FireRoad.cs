using Activities.Halloween.Scripts.Buff;
using UnityEngine;

namespace Activities.Halloween.Scripts.MotorcyleSkill {
    public class FireRoad {
        private FireMotorcycleController _fireMotorcycleController;
        private Transform _controller;
        private Transform _back;
        private RoadSkill _roadSkill;
        private Vector3 _lastPosition;
        private bool _isPet;

        public FireRoad(FireMotorcycleController fireMotorcycleController, Transform controller, Transform back, bool isPet) {
            _fireMotorcycleController = fireMotorcycleController;
            _controller = controller;
            _back = back;
            _roadSkill = (RoadSkill)fireMotorcycleController.GetSkill("MotorRoad");
            _isPet = isPet;
        }
        
        public void Update() {
            if (_controller == null || _fireMotorcycleController == null || _back == null) return;
            
            var p = _controller.position;
            if (Vector3.Distance(p, _lastPosition) > 1) {
                CreateExplodes((p - _lastPosition).normalized);
                _lastPosition = p;
            }
        }

        private void CreateExplodes(Vector3 dir) {
            var position = _back.position;
            float size = float.Parse(_roadSkill.Params2[0]) * _roadSkill.Level2 + 1;

            if (_isPet) {
                var skill = (CrossoverSkillBase)_fireMotorcycleController.GetSkill("MotorRoadCallCrossover");
                if (skill.Level == 1) {
                    size = float.Parse(skill.Params[1]);
                }
                else if (skill.Level == 2) {
                    size = float.Parse(skill.Params1[1]);
                }
            }
            
            Explode(position, size);
        }

        private void Explode(Vector3 position, float size) {
            var damage = int.Parse(_roadSkill.Params[1]);
            float duration = int.Parse(_roadSkill.Params[2]) + int.Parse(_roadSkill.Params0[0]) * _roadSkill.Level0;
            var hitInvert = float.Parse(_roadSkill.Params[0]);
            
            GameObject tempCreation = BulletFactory.TakeBullet(
                new BulletInfo().SetUp(_fireMotorcycleController.prefabs[0], _fireMotorcycleController.Controller.gameObject, 0, position, 0, _fireMotorcycleController.Controller.camp), 
                new DamageInfo().SetUp(_fireMotorcycleController.prefabs[0], damage, 0, 0, _fireMotorcycleController.Controller.camp));

            var bulletGas = tempCreation.GetComponent<BulletGas>();
            if (bulletGas) {
                bulletGas.damage = damage;
                bulletGas.duration = duration;
                if (_isPet) {
                    var skill = (CrossoverSkillBase)_fireMotorcycleController.GetSkill("MotorRoadCallCrossover");
                    if (skill.Level == 1) {
                        bulletGas.duration *= int.Parse(skill.Params[0]) / 100f;
                    }
                    else if (skill.Level == 2) {
                        bulletGas.duration *= int.Parse(skill.Params1[0]) / 100f;
                    }
                }
                bulletGas.hit_invert = hitInvert;
            }
            bulletGas.transform.localScale = Vector3.one * size;
            if (_fireMotorcycleController.HasSkill("MotorRoadFireCrossover")){
                bulletGas.OnHitEnemyAction -= OnHitEnemy;
                bulletGas.OnHitEnemyAction += OnHitEnemy;
                bulletGas.transform.GetChild(0).gameObject.SetActive(false);
                bulletGas.transform.GetChild(1).gameObject.SetActive(true);
            }

            var ppo = tempCreation.GetComponent<IPrefabPoolObject>();
            if (ppo != null) {
                ppo.OnTaken();
            }
        }
        

        private void OnHitEnemy(GameObject blade, RGEController enemy) {
            if (enemy.rg_random.Range(0, 100) < BuffDevilFire.GetSpreadRate()){
                TalentDevilFire.AddBuff(blade, enemy.transform);
            }
        }
    }
}