using RGScript.Data;

namespace Activities.Legend.Scripts.Buffs {
    /// <summary>
    /// 破军羽刃
    /// </summary>
    public class Buff_FeatherBlade : BaseActivityBuff {
        protected override int GetBuffCount() {
            return DataMgr.ActivityLegendData.GetBuffCount(buffId);
        }
        
        protected override void OnBuffStart() {
            base.OnBuffStart();
        }
        
        public override void BuffEnd() {
            base.BuffEnd();
        }
    }
}