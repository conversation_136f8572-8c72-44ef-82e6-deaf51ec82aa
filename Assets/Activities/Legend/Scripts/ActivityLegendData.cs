using cfg.Activity;
using cfg.ActivityLegendBuff;
using cfg.ActivityLegendConst;
using cfg.ActivityLegendEquipDrop;
using cfg.ActivityLegendRanking;
using cfg.ActivityLegendReward;
using I2.Loc;
using MapSystem;
using MycroftToolkit.QuickCode;
using Newtonsoft.Json;
using RGScript.Data;
using RGScript.Data.GameItemData;
using RGScript.Data.Mall;
using RGScript.UI;
using RGScript.Util.MathTool;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using UIFramework;
using UnityEngine;
using UnityEngine.U2D;
using Util.TAUtil;
using Random = UnityEngine.Random;

namespace Activities.Legend.Scripts {
    /// <summary>
    /// 2025 6月元气传奇活动管理数据
    /// </summary>
    public class ActivityLegendData : BaseData {
        public ActivityConfig Config => DataMgr.ActivityConfigData.GetActivityConfig(ActivityLegendManager.TAG);
        public TbActivityLegendRewardConfig RewardConfig => DataMgr.ConfigData.Tables.TbActivityLegendRewardConfig;
        public TbActivityLegendBuffConfig BuffConfig => DataMgr.ConfigData.Tables.TbActivityLegendBuffConfig;
        public TbActivityLegendEquipDropConfig EquipDropConfig => DataMgr.ConfigData.Tables.TbActivityLegendEquipDropConfig;
        public TbActivityLegendRankingConfig RankingConfig => DataMgr.ConfigData.Tables.TbActivityLegendRankingConfig;
        public TbActivityLegendConstConfig ConstConfig => DataMgr.ConfigData.Tables.TbActivityLegendConstConfig;
        public static string FOLLOWER_W_PATH = "Activities/Legend/Prefabs/Followers/follower_weapon_{0}.prefab";
        public static string HONOR_PATH = "Activities/Legend/Prefabs/legend_honor_title.prefab";

        public float ActiveExp = 100f; // 怒气值满则可开启一刀999

        private const string legendInAngry = "legend_legendInAngry";
        private const string currentAngryValue = "legend_currentAngryValue";
        private const string equipReturnValue = "legend_equipReturnValue"; // 装备回收进度值，满一定值可以选一次buff
        private const string killCountKey = "legend_killcount_key"; // 当局击杀数
        private const string addCoinInGameKey = "legend_addCoin_inGame"; // 局内新增的元宝(局外货币)
        private const string immunityKey = "legend_immunity";
        public int buffTarget = 100; // 装备回收值目标，达成一次选择一次buff
        private int _killCount = 0; // 当局击杀数 
        private static SpriteAtlas _atlas;

        /// <summary>
        /// 当前榜单数据，每天凌晨4点刷新
        /// </summary>
        private List<RankingEntry> _currentRankingData = new List<RankingEntry>();

        public static SpriteAtlas Atlas {
            get {
                if (_atlas == null) {
                    _atlas = ResourcesUtil.Load<SpriteAtlas>("Activities/Legend/Textures/atlas.spriteatlasv2");
                }

                return _atlas;
            }
        }

        /// <summary>
        /// 本局游戏是否已免疫过致命伤害
        /// </summary>
        public bool ImmunityDead {
            get {
                return BattleData.data.GetMark(immunityKey) == 1;
            }
            set {
                BattleData.data.SetMark(immunityKey, value ? 1 : 0);
            }
        }

        /// <summary>
        /// 按钮激活状态缓存，以便跳关继承
        /// </summary>
        public bool InAngryActive {
            get {
                return BattleData.data.GetMark(legendInAngry) == 1;
            }
            set {
                BattleData.data.SetMark(legendInAngry, value ? 1 : 0);
                if (value) {
                    ActivityLegendManager.Enter999Mode();
                } else {
                    ActivityLegendManager.Exit999Mode();
                }
            }
        }

        /// <summary>
        /// 局内新增的局外货币
        /// </summary>
        public int OutCoin {
            get {
                return BattleData.data.GetMark(addCoinInGameKey);
            }
            set {
                BattleData.data.SetMark(addCoinInGameKey, value);
            }
        }

        /// <summary>
        /// 当前怒气值
        /// </summary>
        public float CurrentAngryValue {
            get {
                return BattleData.data.GetFloatMark(currentAngryValue);
            }
            set {
                BattleData.data.SetFloatMark(currentAngryValue, value);
            }
        }

        /// <summary>
        /// 选择buff需要累计装备回收进度
        /// </summary>
        public int BuffProgress {
            get {
                return BattleData.data.GetMark(equipReturnValue);
            }
            set {
                BattleData.data.SetMark(equipReturnValue, value);
            }
        }

        /// <summary>
        /// 当局击杀数
        /// </summary>
        public int KillCount {
            get {
                return BattleData.data.GetMark(killCountKey);
            }
            set {
                BattleData.data.SetMark(killCountKey, value);
            }
        }

        public ActivityLegendData() {
            InitData();
        }

        private void InitData() {
            // 反序列化并加载缓存的排行榜数据
            string jsonData = PlayerSaveData.GetString("CurrentRankingData", "");
            if (!string.IsNullOrEmpty(jsonData)) {
                // RankingWrapper wrapper = JsonUtility.FromJson<RankingWrapper>(jsonData);
                // _currentRankingData = wrapper.List;

                var settings = new JsonSerializerSettings {
                    TypeNameHandling = TypeNameHandling.Auto
                };
                RankingWrapper wrapper = JsonConvert.DeserializeObject<RankingWrapper>(jsonData, settings);
                _currentRankingData = wrapper.List;
            }
        }

        /// <summary>
        /// 重置一些本层数据
        /// </summary>
        public void ResetDataLevel() {
        }

        public int GetRewardLeftCount(ActivityLegendRewardConfig reward) {
            if (ActivityUtil.CheckSkinUnlock(reward.Name)) {
                return 0;
            }

            var rewardCount = 0;
            foreach (var config in RewardConfig.DataList) {
                if (config.Id == reward.Id) {
                    rewardCount = config.Number;
                    break;
                }
            }

            //检查皮肤碎片
            var redeemCount = ActivityUtil.GetRedeemRecords(ActivityLegendManager.TAG, reward.Id);
            if (ActivityUtil.CheckSkinFragment(reward.Name, out int hasCount)) {
                redeemCount = hasCount;
            }

            var leftTime = rewardCount - redeemCount;
            if (leftTime < 0) {
                leftTime = 0;
            }

            return leftTime;
        }

        /// <summary>
        /// 是否触发橙色天赋保底
        /// </summary>
        /// <returns></returns>
        private bool ShouldHasBestBuff() {
            // 保底次数
            var bestGuarantee = GetConstValue("Pro3Guarantee").ToInt();
            if (BattleData.data.activityExtras.TryGetValue(ActivityLegendManager.TAG, out var buffs)) {
                if (buffs.Count == 0) {
                    return false;
                }
                
                // 检查上次选的天赋是否是:召唤欧皇，下次天赋必然出现橙色
                if (buffs[^1] == "buff_lucky") {
                    return true;
                }

                if (buffs.Count > bestGuarantee) {
                    var startIdx = buffs.Count - bestGuarantee;
                    var raritySubs = buffs.GetRange(startIdx, bestGuarantee);
                    var hasBestBuff = raritySubs.Any(buffId => GetBuffConfig(buffId).Level == 2);
                    return !hasBestBuff;
                }
            }

            return false;
        }

        /// <summary>
        /// 是否触发蓝色天赋保底
        /// </summary>
        /// <returns></returns>
        private bool ShouldHasGoodBuff() {
            // 保底次数
            var goodGuarantee = GetConstValue("Pro2Guarantee").ToInt();
            if (BattleData.data.activityExtras.TryGetValue(ActivityLegendManager.TAG, out var buffs)) {
                if (buffs.Count > goodGuarantee) {
                    var startIdx = buffs.Count - goodGuarantee;
                    var raritySubs = buffs.GetRange(startIdx, goodGuarantee);
                    var hasGoodBuff = raritySubs.Any(buffId => GetBuffConfig(buffId).Level == 1);
                    return !hasGoodBuff;
                }
            }

            return false;
        }

        public List<string> GetRandomBuffs() {
            var result = new List<string>();
            var allBuffs = GetAllBuffConfigs();

            // 根据 Type 对所有 buff 进行分类
            var categorizedBuffs = new Dictionary<int, List<ActivityLegendBuffConfig>>();
            foreach (var buff in allBuffs) {
                if (!categorizedBuffs.ContainsKey(buff.Type)) {
                    categorizedBuffs[buff.Type] = new List<ActivityLegendBuffConfig>();
                }

                categorizedBuffs[buff.Type].Add(buff);
            }

            // 每类 buff 随机选择一个，且不超过 Config.Upper 的拥有上限限制
            var rgRandom = new RGRandom();
            rgRandom.SetRandomSeed(GameUtil.GenerateRandomSeedNumber());

            // 可能的buff类型
            var selectedTypes = SelectTypesBasedOnProbability();
            var currBigLevel = BattleData.data.levelIndex / 5 + 1;

            // buff稀有度配表
            var dataRarity = GetConstValue($"Rarity{currBigLevel}").Split(";");
            var rarityRates = dataRarity.Select(float.Parse).ToList();

            var hasDoBestGuarantee = false;
            var hasDoGoodGuarantee = false;

            // 获取当前类别中所有可用的 buff（即未达到 Upper 限制的 buff）
            foreach (var type in selectedTypes) {
                var availableBuffs = categorizedBuffs[type]
                    .Where(buff => GetBuffCount(buff.Id) < buff.Upper)
                    .ToList();
                if (availableBuffs.Count == 0) {
                    // 当前类型已无可用天赋，从总池子随机一个保底
                    var selectedBuff = allBuffs.Where(buff => GetBuffCount(buff.Id) < buff.Upper && !result.Contains(buff.Id)).GetRandomObject(rgRandom);
                    if (selectedBuff != null) {
                        result.Add(selectedBuff.Id);
                    }
                } else {
                    // 随机到的稀有度
                    int rarity = WeightedRandom(rarityRates);

                    // 保底逻辑
                    if (ShouldHasBestBuff()) {
                        if (!hasDoBestGuarantee) {
                            rarity = 2;
                            hasDoBestGuarantee = true;
                        }
                    } else if (ShouldHasGoodBuff()) {
                        if (!hasDoGoodGuarantee) {
                            rarity = 1;
                            hasDoGoodGuarantee = true;
                        }
                    }

                    var availableBuffsInRarity = availableBuffs.Where(buff => buff.Level == rarity && !result.Contains(buff.Id));
                    var selectedBuff = availableBuffsInRarity.Count > 0
                        ? availableBuffsInRarity.GetRandomObject(rgRandom)
                        : availableBuffs.GetRandomObject(rgRandom);
                    if (selectedBuff != null) {
                        result.Add(selectedBuff.Id);
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 查询配表数值
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        private string GetConstValue(string key) {
            foreach (var item in ConstConfig.DataList) {
                if (item.Name == key) {
                    return item.Value;
                }
            }

            return "";
        }

        private List<int> SelectTypesBasedOnProbability() {
            var result = new List<int>();

            // 基础概率
            var baseProbabilities = new List<float> {
                1f / 3, // 羽翼
                1f / 3, // 角色
                1f / 3, // 屠龙刀
            };

            var dataArr = GetConstValue("BuffTypePro1").Split(";");
            var (buffType, continueCount) = GetContinueSelectBuffType();
            if (continueCount == 3) {
                dataArr = GetConstValue("BuffTypePro3").Split(";");
            } else if (continueCount >= 4) {
                dataArr = GetConstValue("BuffTypePro4").Split(";");
            }

            for (var i = 0; i < baseProbabilities.Count; i++) {
                if (i == buffType) {
                    baseProbabilities[i] = dataArr[0].ToFloat();
                } else {
                    baseProbabilities[i] = dataArr[1].ToFloat();
                }
            }

            for (int i = 0; i < 3; i++) {
                // 随机选择一种类型
                int selectedType = WeightedRandom(baseProbabilities);
                result.Add(selectedType);
            }

            return result;
        }

        private int WeightedRandom(List<float> probabilities) {
            float totalWeight = probabilities.Sum();
            float randomValue = Random.value * totalWeight;
            float cumulativeWeight = 0f;

            for (var i = 0; i < probabilities.Count; i++) {
                cumulativeWeight += probabilities[i];
                if (randomValue <= cumulativeWeight) {
                    return i;
                }
            }

            return 0;
        }

        /// <summary>
        /// 最近连续选择的天赋类型及次数
        /// </summary>
        /// <returns></returns>
        private (int, int) GetContinueSelectBuffType() {
            int buffType = -1;
            int continueCount = 0;
            if (BattleData.data.activityExtras.ContainsKey(ActivityLegendManager.TAG)) {
                var selectedBuffs = BattleData.data.activityExtras[ActivityLegendManager.TAG];
                for (var i = selectedBuffs.Count - 1; i >= 0; i--) {
                    var buffConfig = GetBuffConfig(selectedBuffs[i]);
                    if (buffType == -1 || buffConfig.Type == buffType) {
                        buffType = buffConfig.Type;
                        continueCount++;
                    } else {
                        break;
                    }
                }
            }

            return (buffType, continueCount);
        }

        /// <summary>
        /// 回收装备，随机获取局外货币元宝
        /// </summary>
        public int AddOutCoinAfterReturnEquipments() {
            var dataArr = GetConstValue("CoinRandomAmount").Split(";").Select(int.Parse).ToArray();
            var addCoin = Random.Range(dataArr[0], dataArr[1] + 1);
            OutCoin += addCoin;
            return addCoin;
        }

        /// <summary>
        /// 刷新天赋选项的金币价格
        /// </summary>
        /// <returns></returns>
        public int GetBuffRefreshPrice() {
            var price = GetConstValue("RefreshCoin").ToInt();
            return price;
        }

        /// <summary>
        /// 点击刷新天赋按钮调用此接口，根据返回值判断是否刷新UI
        /// </summary>
        /// <returns></returns>
        public bool RefreshBuffSelection() {
            var consume = GetBuffRefreshPrice();
            if (!RGGameProcess.Inst.ConsumeCoin(consume, emStatisticsType.LegendRefreshBuffSelection)) {
                UICanvasRoot.Inst.ShowMessage(ScriptLocalization.Get("I_no_money"), 1.5f);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 获取指定 buff 的当前拥有数量
        /// </summary>
        /// <param name="buffId">buff ID</param>
        /// <param name="bd">net battledata</param>
        /// <returns>当前拥有数量</returns>
        public int GetBuffCount(string buffId, BattleData bd = null) {
            var tmpData = bd ?? BattleData.data;
            if (buffId.StartsWith("buff_bonus_")) {
                return tmpData.legendRankingBuffs.Contains(buffId) ? 1 : 0;
            }
            return tmpData.legendBuffs.GetValueOrDefault(buffId, 0);
        }

        /// <summary>
        /// 获取局内获取的所有buffs
        /// </summary>
        /// <returns></returns>
        public Dictionary<string, int> GetAllMyBuffs() {
            return BattleData.data.legendBuffs;
        }

        /// <summary>
        /// 获取屠龙刀随从初始等级
        /// </summary>
        /// <returns></returns>
        public int GetFollowerInitLevel(BattleData bd = null) {
            if (DataUtil.IsHeroRoom()) {
                if (StatisticData.data.legendClaimOutBuffs.Contains("buff_bonus_11")) {
                    return 2;
                }

                if (StatisticData.data.legendClaimOutBuffs.Contains("buff_bonus_7")) {
                    return 1;
                }
            } else {
                BattleData tmpData = bd ?? BattleData.data;
                if (tmpData.legendRankingBuffs.Contains("buff_bonus_11")) {
                    return 2;
                }

                if (tmpData.legendRankingBuffs.Contains("buff_bonus_7")) {
                    return 1;
                }
            }

            return 0;
        }
        
        /// <summary>
        /// 获取羽翼初始等级
        /// </summary>
        /// <returns></returns>
        public int GetWingsInitLevel(BattleData bd = null) {
            if (DataUtil.IsHeroRoom()) {
                if (StatisticData.data.legendClaimOutBuffs.Contains("buff_bonus_11")) {
                    return 2;
                }

                if (StatisticData.data.legendClaimOutBuffs.Contains("buff_bonus_9")) {
                    return 1;
                }
            } else {
                BattleData tmpData = bd ?? BattleData.data;
                if (tmpData.legendRankingBuffs.Contains("buff_bonus_11")) {
                    return 2;
                }

                if (tmpData.legendRankingBuffs.Contains("buff_bonus_9")) {
                    return 1;
                }
            }

            return 0;
        }

        /// <summary>
        /// 获取局内屠龙刀类型的buff数量
        /// </summary>
        /// <returns></returns>
        public int GetBuffCountOfFollower(BattleData bd = null) {
            return (bd ?? BattleData.data).legendBuffs
                .Where(item => {
                    var config = GetBuffConfig(item.Key);
                    return config != null && config.Type == (int)emBuffType.Follower;
                })
                .Sum(item => item.Value);
        }

        /// <summary>
        /// 获取局内羽翼类型的buff数量
        /// </summary>
        /// <returns></returns>
        public int GetBuffCountOfWings(BattleData bd = null) {
            return (bd ?? BattleData.data).legendBuffs
                .Where(item => {
                    var config = GetBuffConfig(item.Key);
                    return config != null && config.Type == (int)emBuffType.Wings;
                })
                .Sum(item => item.Value);
        }

        /// <summary>
        /// 获取屠龙刀随从当前等级
        /// </summary>
        /// <returns></returns>
        public int GetFollowerLevel(BattleData bd = null) {
            var initLevel = GetFollowerInitLevel(bd);
            int followerBuffCount = GetBuffCountOfFollower(bd);
            return Mathf.Clamp(initLevel + followerBuffCount, 0, 6);
        }

        /// <summary>
        /// 获取羽翼当前等级
        /// </summary>
        /// <returns></returns>
        public int GetWingsLevel(BattleData bd = null) {
            var initLevel = GetWingsInitLevel(bd);
            int followerBuffCount = GetBuffCountOfWings(bd);
            return Mathf.Clamp(initLevel + followerBuffCount, 0, 6);
        }

        /// <summary>
        /// 获取羁绊配置
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public ActivityLegendBuffConfig GetBuffConfig(string id) {
            return BuffConfig.DataList.FindLast(item => item.Id == id);
        }

        /// <summary>
        /// 局外所有天赋，需要排除局外天赋
        /// </summary>
        /// <returns></returns>
        public List<ActivityLegendBuffConfig> GetAllBuffConfigs() {
            return BuffConfig.DataList.Where(item => item.Type != (int)emBuffType.Ranking);
        }
        
        public List<ActivityLegendBuffConfig> DebugGetAllBuffConfigs() {
            return BuffConfig.DataList;
        }

        public (ActivityLegendBuffConfig, int) GetBuffData(string buffId) {
            var buffConfig = DataMgr.ActivityLegendData.GetBuffConfig(buffId);
            var buffCount = DataMgr.ActivityLegendData.GetBuffCount(buffId);
            return (buffConfig, buffCount);
        }

        /// <summary>
        /// buff名称
        /// </summary>
        /// <param name="buffId"></param>
        /// <returns></returns>
        public string GetBuffName(string buffId) {
            var buffConfig = GetBuffConfig(buffId);
            return ScriptLocalization.Get($"activity/legend/{buffConfig.Id}_name", buffConfig.Name);
        }

        /// <summary>
        /// buff描述
        /// </summary>
        /// <param name="buffId"></param>
        /// <returns></returns>
        public string GetBuffDesc(string buffId) {
            var buffConfig = GetBuffConfig(buffId);
            var dataParams = buffConfig.Data.Split(";");
            var desc = ScriptLocalization.Get($"activity/legend/{buffConfig.Id}_desc", buffConfig.DescFallback);
            desc = string.Format(desc, dataParams);

            return desc;
        }
        /// <summary>
        /// buff类型
        /// </summary>
        /// <param name="buffId"></param>
        /// <returns></returns>
        public emBuffType GetBuffType(string buffId) {
            var buffConfig = GetBuffConfig(buffId);
            return (emBuffType)buffConfig.Type;
        }
        
        /// <summary>
        /// 羁绊icon
        /// </summary>
        /// <param name="buffConfigId"></param>
        /// <returns></returns>
        public Sprite GetBuffSprite(string buffConfigId) {
            return Atlas.GetSprite(buffConfigId);
        }

        /// <summary>
        /// buff品质
        /// 0：白
        /// 1：蓝
        /// 2：橙
        /// </summary>
        /// <param name="buffId"></param>
        /// <returns></returns>
        public int GetBuffLevel(string buffId) {
            var buffConfig = GetBuffConfig(buffId);
            return buffConfig.Level;
        }

        /// <summary>
        /// Track获得的天赋
        /// </summary>
        /// <returns></returns>
        public List<string> TrackGetAllGotBuffs() {
            List<string> result = new List<string>();
            var allBuffs = GetAllBuffConfigs();
            foreach (var buffConfig in allBuffs) {
                var buffCount = GetBuffCount(buffConfig.Id);
                result.Add($"{buffConfig.Id}:{buffCount}");
            }

            return result;
        }

        #region 装备掉落

        // 非boss敌人只有75%概率掉落装备
        // 一刀999期间必定掉落装备
        private int GetDropRate() {
            if (InAngryActive) return 100;
            
            var rate = BattleData.data.isBadass 
                ? GetConstValue("EXEquipmentDrop").ToInt()
                : GetConstValue("EquipmentDrop").ToInt();
            
            // 第四大关古战场，掉落减少
            if (LevelSelector.GetCurrentBigLevelBranch() == "4B") {
                var factor = GetConstValue("4BEquipmentDrop").ToInt() / 100f;
                rate = (int)(rate * factor);
            }

            return rate;
        }

        public int GetDropCount(RGEController rgeController) {
            var enemyType = GetEnemyType(rgeController);
            var config = EquipDropConfig.DataList.FirstOrDefault(c => c.Id == enemyType);
            if (config == null) {
                Debug.LogError($"未找到敌人类型为{enemyType}的掉落配置");
                return 0;
            }

            var tmpDrops = config.Count.Split(',');
            if (tmpDrops.Length != 3) {
                // boss
                var dataArr = config.Count.Split('~');
                int[] tmpCounts = dataArr.Select(int.Parse).ToArray();
                return UnityEngine.Random.Range(tmpCounts[0], tmpCounts[1]);
            }

            if (Random.Range(0, 100) > GetDropRate()) {
                return 0;
            }

            // 普通敌人和精英怪的掉落逻辑
            List<int> rates = new List<int>();
            List<int> counts = new List<int>();
            int totalWeight = 0;
            for (var i = tmpDrops.Length - 1; i >= 0; i--) {
                var dropItem = tmpDrops[i];
                int[] rateCount = dropItem.Split(":").Select(int.Parse).ToArray();
                rates.Add(rateCount[0]);
                counts.Add(rateCount[1]);
                totalWeight += rateCount[0];
            }

            int randomValue = RGGameSceneManager.Inst.rg_random.Range(0, totalWeight);
            int cumulativeWeight = 0;
            for (int i = 0; i < rates.Count; i++) {
                cumulativeWeight += rates[i];
                if (randomValue < cumulativeWeight) {
                    return counts[i];
                }
            }

            return 1;
        }

        private string GetEnemyType(RGEController rgeController) {
            if (rgeController.isBoss) return "boss";
            if (rgeController.intensive) return "intensive";
            return "normal";
        }

        /// <summary>
        /// 根据敌人类型，获取稀有度
        /// </summary>
        /// <returns></returns>
        public EquipmentRarity GetRarity(RGEController rgeController) {
            var enemyType = GetEnemyType(rgeController);
            var config = EquipDropConfig.DataList.FirstOrDefault(c => c.Id == enemyType);
            if (config == null) {
                return EquipmentRarity.Common;
            }

            string[] rarityProbabilities = config.Rarity.Split(';');
            float[] probabilities = rarityProbabilities.Select(float.Parse).ToArray();

            float randomValue = Random.Range(0, 100);
            if (randomValue <= probabilities[0]) {
                return EquipmentRarity.Common;
            } else if (randomValue <= probabilities[0] + probabilities[1]) {
                return EquipmentRarity.Good;
            } else {
                return EquipmentRarity.Rare;
            }
        }

        /// <summary>
        /// 获取不同稀有度的装备回收值
        /// </summary>
        /// <param name="rarity"></param>
        /// <returns></returns>
        public int GetRarityValue(EquipmentRarity rarity) {
            string rarityValues = EquipDropConfig.DataList.First().RarityValue;
            string[] values = rarityValues.Split(';');
            int[] intValue = values.Select(int.Parse).ToArray();

            switch (rarity) {
                case EquipmentRarity.Common:
                    return intValue[0];
                case EquipmentRarity.Good:
                    return intValue[1];
                case EquipmentRarity.Rare:
                    return intValue[2];
                default:
                    return 0;
            }
        }

        public List<EquipmentDrop> GetEquipmentDrops(RGEController rgeController) {
            List<EquipmentDrop> drops = new List<EquipmentDrop>();

            int dropCount = GetDropCount(rgeController);
            for (int i = 0; i < dropCount; i++) {
                EquipmentRarity rarity = GetRarity(rgeController);
                int value = GetRarityValue(rarity);
                drops.Add(new EquipmentDrop { Rarity = rarity, Value = value });
            }

            return drops;
        }

        /// <summary>
        /// 拾取装备
        /// </summary>
        /// <param name="equip"></param>
        public void AddEquip(EquipmentDrop equip) {
            BattleData.data.legendEquipmentDrops.Add(equip);
        }

        /// <summary>
        /// 拾取的所有装备
        /// </summary>
        /// <returns></returns>
        public List<EquipmentDrop> GetAllEquipments() {
            return BattleData.data.legendEquipmentDrops;
        }

        /// <summary>
        /// 回收装备
        /// </summary>
        /// <param name="equip"></param>
        public void ReturnEquipment(EquipmentDrop equip) {
            // 累计回收值
            BuffProgress += equip.Value;
            if (BattleData.data.legendEquipmentDrops.Contains(equip)) {
                BattleData.data.legendEquipmentDrops.Remove(equip);
            }
            // 进度更新事件，方便UI更新进度条
            SimpleEventManager.Raise(LegendBuffProgressEvent.UseCache(equip.Value));
        }

        /// <summary>
        /// 装备都回收后调用，以及事件循环调用以便选择buff后继续弹
        /// 检查buff进度值，弹天赋选择UI
        /// </summary>
        public void CheckAndShowBuffSelection() {
            if (BuffProgress >= buffTarget) {
                ShowBuffSelection();
            } else {
                ActivityLegendManager.CloseSelectBuffWindow();
            }
        }

        public Sprite GetEquipmentSprite(EquipmentRarity rarity) {
            return Atlas.GetSprite($"equipitem_level{(int)rarity}_{Random.Range(0, 3)}");
        }

        /// <summary>
        /// 创建装备实例
        /// </summary>
        /// <param name="equipmentDrop"></param>
        /// <param name="position"></param>
        /// <returns></returns>
        public EquipmentItem CreateEquipment(EquipmentDrop equipmentDrop, Vector3 position, bool createEffect = true) {
            var coinProto = ResourcesUtil.Load<GameObject>("Activities/Legend/Prefabs/equipment_item.prefab");
            var coin = UnityEngine.Object
                .Instantiate(coinProto, position, Quaternion.identity,
                    RGGameSceneManager.GetInstance().temp_objects_parent).GetComponent<EquipmentItem>();
            coin.InitData(equipmentDrop, createEffect);
            return coin;
        }

        /// <summary>
        /// 展示天赋选择UI
        /// </summary>
        private void ShowBuffSelection() {
            BuffProgress -= buffTarget;
            ActivityLegendManager.ShowSelectBuffWindow();
        }

        #endregion

        #region 排行榜

        /// <summary>
        /// 本地排行榜刷新时间key
        /// </summary>
        private const string CACHE_REFRESH_TIME_KEY = "legend_ranking_refresh_time";

        public string LastRefreshRankingTime {
            get {
                return PlayerSaveData.GetString(CACHE_REFRESH_TIME_KEY, "");
            }
            set {
                PlayerSaveData.SetString(CACHE_REFRESH_TIME_KEY, value);
            }
        }

        public string CACHE_DAY_KILLCOUNT_KEY = "legend_day_killcount";

        /// <summary>
        /// 每日杀敌数
        /// </summary>
        private List<int> EveryDayKillCount {
            get {
                return PlayerSaveData.GetIntList(CACHE_DAY_KILLCOUNT_KEY, string.Empty);
            }
            set {
                PlayerSaveData.SetList(CACHE_DAY_KILLCOUNT_KEY, value);
            }
        }

        public void AddNewTodayKillCount(int newCount) {
            var olds = EveryDayKillCount;
            olds.Add(newCount);
            EveryDayKillCount = olds;
        }

        public void UpdateTodayKillCount() {
            var cacheData = EveryDayKillCount;
            if (cacheData.Count > 0) {
                cacheData[^1] += KillCount;
            } else {
                cacheData.Add(KillCount);
            }
            EveryDayKillCount = cacheData;
        }

        public int GetYesterdayKillCount() {
            var cacheData = EveryDayKillCount;
            if (cacheData.Count <= 1) {
                return 0;
            }

            return cacheData[^2];
        }

        /// <summary>
        /// 活动开始以来总击杀数
        /// </summary>
        /// <returns></returns>
        public int GetTotalKillCount() {
            return EveryDayKillCount.Sum();
        }

        /// <summary>
        /// 前天的击杀数
        /// </summary>
        /// <returns></returns>
        public int GetBeforeYesterdayKillCount() {
            var cacheData = EveryDayKillCount;
            if (cacheData.Count <= 2) {
                return 0;
            }

            return cacheData[^3];
        }

        /// <summary>
        /// 没有刷新过的，刷新下数据，超过凌晨4点的也刷新下
        /// </summary>
        public void CheckAndRefreshRanking() {
            var lastRefreshTime = LastRefreshRankingTime;
            if (string.IsNullOrEmpty(lastRefreshTime) || _currentRankingData.Count == 0) {
                GenerateRanking();

                if (LogUtil.IsShowLog) {
                    LogUtil.Log($"没有刷新过排行榜，刷新一下", ActivityLegendManager.TAG);
                }

                return;
            }

            var lastTime = long.Parse(lastRefreshTime);
            DateTime lastRefreshDate = DateTimeOffset.FromUnixTimeSeconds(lastTime).ToLocalTime().DateTime;
            DateTime currentTime = DateTime.Now;
            if (currentTime > lastRefreshDate) {
                // 设置今天的凌晨4点
                DateTime tomorrow4AM = new DateTime(lastRefreshDate.Year, lastRefreshDate.Month, lastRefreshDate.Day, 4, 0, 0);
                tomorrow4AM = tomorrow4AM.AddDays(1);

                if (LogUtil.IsShowLog) {
                    LogUtil.Log($"当前时间：{currentTime}，上次刷新时间：{lastRefreshDate}，明天凌晨4点：{tomorrow4AM}", ActivityLegendManager.TAG);
                }

                // 比较当前时间是否已经过了凌晨4点
                if (currentTime >= tomorrow4AM) {
                    GenerateRanking();
                }
            }
        }

        /// <summary>
        /// 生成排行榜
        /// 凌晨4点统一刷新
        /// </summary>
        /// <param name="playerKillCount">玩家昨日击杀数</param>
        /// <returns>排行榜数据</returns>
        private void GenerateRanking() {
            // 先初始新的一天击杀数为0
            AddNewTodayKillCount(0);

            // 根据玩家击杀数，动态更新榜单
            UpdateRankingData();

            DateTime currentTime = DateTime.Now;
            LastRefreshRankingTime = new DateTimeOffset(currentTime).ToUnixTimeSeconds().ToString();
        }

        /// <summary>
        /// 前天是否达到过第一名
        /// </summary>
        /// <returns></returns>
        private bool IsBeforeYesterdayRearchTop1() {
            var dayStr = TimeUtil.GetDayStr(-2);
            return StatisticData.data.GetEventCount($"legend_top1_{dayStr}") > 0;
        }
        
        /// <summary>
        /// 是否抵达过第一名
        /// </summary>
        /// <returns></returns>
        private bool HasReachedTop1() {
            var startTime = ActivityUtil.StartTime(ActivityLegendManager.TAG);
            var startDate = TimeUtil.ChineseTimestampToLocalDateTime(startTime);
            var currentDate = DateTime.Now;
            var yesterday = currentDate.AddDays(-1);
            var days = Mathf.Abs((yesterday.Date - startDate.Date).Days);

            // 遍历每一天
            for (var i = 1; i <= days; i++) {
                var dayStr = TimeUtil.GetDayStr(-i);
                var reached = StatisticData.data.GetEventCount($"legend_top1_{dayStr}") > 0;
                if (reached) {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 抵达top1的时候，记录一下日期
        /// </summary>
        public void CheckAndRecordTop1() {
            var (num, item) = GetPlayerRanking();
            if (num == 1) {
                var dayStr = TimeUtil.GetDayStr();
                StatisticData.data.AddEventCount($"legend_top1_{dayStr}", 1, true);
            }
        }

        /// <summary>
        /// 检查，刷新排行榜奖励状态
        /// </summary>
        public void CheckAndUnlockRankingBonus() {
            var needSave = false;
            var totalKillCount = GetTotalKillCount();
            foreach (var ranking in RankingConfig.DataList) {
                if (IsRankingBonusClaimed(ranking.Id)) continue;
                if (IsRankingBonusAvailable(ranking.Id)) continue;

                if (ranking.KillCount < totalKillCount) {
                    MakeRankingBonusAvailable(ranking.Id);
                    needSave = true;
                }
            }

            if (needSave) {
                StatisticData.Save();
            }
        }

        /// <summary>
        /// 获取称号
        /// </summary>
        /// <param name="index">排行榜的次序</param>
        /// <returns></returns>
        public string GetRankingHonorTitle(int index) {
            ActivityLegendRankingConfig rankItem;
            if (RankingConfig.DataList.Count > index) {
                rankItem = RankingConfig.DataList[index - 1];
            } else {
                rankItem = RankingConfig.DataList[^1];
            }

            return ScriptLocalization.Get(rankItem.Honor, rankItem.HonorFallback);
        }

        /// <summary>
        /// 查询排行榜奖励领取状态
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        private emRankingBonusStatus GetRankingBonusStatus(int index) {
            if (IsRankingBonusClaimed(index)) {
                return emRankingBonusStatus.Claimed;
            }

            return IsRankingBonusAvailable(index)
                ? emRankingBonusStatus.Available
                : emRankingBonusStatus.Pending;
        }

        private bool IsRankingBonusClaimed(int index) {
            var claimKey = $"legend_ranking_bonus_claimed_{index}";
            return StatisticData.data.GetEventCount(claimKey) > 0;
        }

        /// <summary>
        /// 标记排行榜奖励领取状态
        /// </summary>
        /// <param name="index"></param>
        private void MakeRankingBonusClaimed(int index) {
            var claimKey = $"legend_ranking_bonus_claimed_{index}";
            StatisticData.data.SetEventCount(claimKey, 1, true);
        }

        private bool IsRankingBonusAvailable(int index) {
            var key = $"legend_ranking_bonus_available_{index}";
            return StatisticData.data.GetEventCount(key) > 0;
        }

        private void MakeRankingBonusAvailable(int index) {
            var key = $"legend_ranking_bonus_available_{index}";
            StatisticData.data.SetEventCount(key, 1, false);
        }
        
        public void DebugRemoveRankingBonusRecords() {
            foreach (var item in RankingConfig.DataList) {
                var index = item.Id;
                var key = $"legend_ranking_bonus_available_{index}";
                var claimKey = $"legend_ranking_bonus_claimed_{index}";
                StatisticData.data.SetEventCount(claimKey, 0, false);
                StatisticData.data.SetEventCount(key, 0, false);
            }
            StatisticData.Save();
        }

        /// <summary>
        /// 获取玩家当前排名
        /// int:名次，1开始
        /// </summary>
        /// <returns></returns>
        public (int, RankingEntry) GetPlayerRanking() {
            var currRanking = GetRankingData();
            var index = currRanking.FindIndex(item => item.IsPlayer) + 1;
            return (index, currRanking[index - 1]);
        }

        public int GetPlayerRankingInGame(BattleData bd = null) {
            if (DataUtil.IsGameScene()) {
                return (bd ?? BattleData.data).GetMark("legendRanking");
            }

            var rankingData = GetPlayerRanking();
            return rankingData.Item1;
        }

        /// <summary>
        /// 获取排行榜数据，包含玩家自己的
        /// </summary>
        /// <returns></returns>
        public List<RankingEntry> GetRankingData() {
            var result = new List<RankingEntry>(_currentRankingData);

            // 插入玩家的击杀数据
            var totalKillCount = GetTotalKillCount();
            var playerEntry = new RankingEntry {
                Name = GetPlayerName(),
                IsPlayer = true,
                KillCount = totalKillCount,
                Avatar = GetPlayerAvatar(),
            };
            result.Add(playerEntry);
            result.Sort((a, b) => b.KillCount.CompareTo(a.KillCount));

            // 插入对应排行的奖励
            for (var i = 0; i < result.Count; i++) {
                if (result[i].Avatar == null) {
                    result[i].Avatar = GetRankingOriginAvatar(result[i].Name);
                }

                result[i].BonusStatus = emRankingBonusStatus.Pending;
                result[i].Bonuses.Clear();

                var rConfig = i < RankingConfig.DataList.Count
                    ? RankingConfig.DataList[i]
                    : null;
                if (rConfig != null && !string.IsNullOrEmpty(rConfig.Bonus)) {
                    result[i].BonusStatus = GetRankingBonusStatus(rConfig.Id);
                    result[i].Bonuses = ParseRankingBonus(rConfig.Bonus);
                }
            }

            return result;
        }

        /// <summary>
        /// 查询是否有排行榜奖励可以领取
        /// </summary>
        /// <returns></returns>
        public bool HasAvailableBonus2Claim() {
            for (var i =  RankingConfig.DataList.Count - 1; i >= 0; i--) {
                var item = RankingConfig.DataList[i];
                if (!string.IsNullOrEmpty(item.Bonus) && GetRankingBonusStatus(item.Id) == emRankingBonusStatus.Available) {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 根据配表查找对应npc的头像
        /// </summary>
        /// <param name="rName"></param>
        /// <returns></returns>
        private Sprite GetRankingOriginAvatar(string rName) {
            var rankingItem = RankingConfig.DataList.FindLast(item => item.Name == rName);
            return Atlas.GetSprite($"avatar_{rankingItem.Id}");
        }

        /// <summary>
        /// 领取最近的排行榜奖励，从低级到高级顺序，
        /// 有多个可领取，需要点多次，以便刷新小骑士翅膀，屠龙刀UI特效
        /// </summary>
        public void ClaimRankingBonus() {
            for (var i = RankingConfig.DataList.Count - 1; i >= 0; i--) {
                var rConfig = RankingConfig.DataList[i];
                if (!string.IsNullOrEmpty(rConfig.Bonus)) {
                    var status = GetRankingBonusStatus(rConfig.Id);
                    if (status == emRankingBonusStatus.Available) {
                        ClaimRankingBonus(rConfig);
                        break;
                    }
                }
            }
        }

        private void ClaimRankingBonus(ActivityLegendRankingConfig rConfig) {
            MakeRankingBonusClaimed(rConfig.Id);
            var bonuses = ParseRankingBonus(rConfig.Bonus);
            var uIWindowShowObjects = UIManager.Inst.OpenUIView<UIWindowShowObjects>("window_show_objects");
            foreach (var bonus in bonuses) {
                float size = bonus.Name.Contains("wings_") ? 150f : 60f;
                if (bonus.Count > 1) {
                    uIWindowShowObjects.AddItem(null, bonus.Sprite, $"×{bonus.Count}", size);
                } else {
                    uIWindowShowObjects.AddItem(null, bonus.Sprite, "", size);
                }

                if (bonus.Name == ItemData.GemName ||
                    bonus.Name == ActivityLegendManager.MatName) {
                    var rewardable = ActivityUtil.GetRewardable(bonus.Name, bonus.Count);
                    ActivityUtil.AddReward(rewardable, GetItemSource.ActivityLegend, false);
                } else if (bonus.Name.StartsWith("buff_bonus_")) {
                    StatisticData.data.legendClaimOutBuffs.Add(bonus.Name);
                }
            }
            
            uIWindowShowObjects.SetUpWindow(Array.Empty<PickableInfo>(), null, false);
            uIWindowShowObjects.awake = true;
            RGMusicManager.GetInstance().PlayEffect(uIWindowShowObjects.audioClip);
            StatisticData.Save();
        }

        private List<RankingBonus> ParseRankingBonus(string bonusStr) {
            var result = new List<RankingBonus>();
            var bonusArr = bonusStr.Split(';');
            foreach (var bonusItem in bonusArr) {
                var tmpArr = bonusItem.Split(':');
                var bonus = new RankingBonus {
                    Name = tmpArr[0],
                    Sprite = GetBonusSprite(tmpArr[0]),
                    Count = int.Parse(tmpArr[1], CultureInfo.InvariantCulture),
                };
                result.Add(bonus);
            }

            return result;
        }

        private Sprite GetBonusSprite(string name) {
            if (name == ItemData.GemName) {
                return SpriteUtility.GetCurrencySprite(CurrencyType.Gem);
            }

            return Atlas.GetSprite(name);
        }

        /// <summary>
        /// 迁移分支可获取玩家好友昵称
        /// </summary>
        /// <returns></returns>
        private string GetPlayerName() {
            var myProfile = DataMgr.IMData.GetMyProfile();
            return myProfile != null ? myProfile.NickName : "";
        }

        /// <summary>
        /// 迁移分支可获取玩家好友头像
        /// </summary>
        /// <returns></returns>
        private Sprite GetPlayerAvatar() {
            return Atlas.GetSprite("avatar_player");
        }

        /// <summary>
        /// 根据玩家昨天，前天击杀情况刷新排行榜数据，
        /// 及插入玩家数据，组成20条
        /// </summary>
        private void UpdateRankingData() {
            var beforeYesterdayKillCount = GetBeforeYesterdayKillCount();
            var yesterdayKillCount = GetYesterdayKillCount();
            var hasReachedTop1 = HasReachedTop1();
            if ((beforeYesterdayKillCount == 0 || !hasReachedTop1) && 
                _currentRankingData.Count > 0) {
                // 若玩家前天未登录游戏，或未超越第一名，则npc战力不变
                return;
            }
            
            _currentRankingData.Clear();
            foreach (var item in RankingConfig.DataList) {
                var npc = new RankingEntry {
                    Name = item.Name,
                    KillCount = item.KillCount,
                    Avatar = Atlas.GetSprite($"avatar_{item.Id}")
                };
                _currentRankingData.Add(npc);
            }

            int addExtra = 200;
            for (var i = 0; i < _currentRankingData.Count; i++) {
                if (i is >= 0 and <= 1) {
                    _currentRankingData[i].KillCount += (int)((yesterdayKillCount * 0.5f + addExtra) * 1.2f);
                } else if (i is >= 2 and <= 4) {
                    _currentRankingData[i].KillCount += (int)((yesterdayKillCount * 0.5f + addExtra) * 1.15f);
                } else if (i is >= 5 and <= 12) {
                    _currentRankingData[i].KillCount += (int)((yesterdayKillCount * 0.5f + addExtra) * 1.1f);
                } else if (i is >= 13 and <= 16) {
                    _currentRankingData[i].KillCount += (int)((yesterdayKillCount * 0.5f + addExtra) * 1f);
                }
            }

            // 本地缓存起来
            var settings = new JsonSerializerSettings {
                TypeNameHandling = TypeNameHandling.Auto,
                Formatting = Formatting.Indented
            };
            string jsonData = JsonConvert.SerializeObject(new RankingWrapper { List = _currentRankingData }, settings);
            PlayerSaveData.SetString("CurrentRankingData", jsonData);
            PlayerSaveData.Save();
        }

        private List<ActivityLegendBuffConfig> GetRankingBuffConfigs() {
            var rankingBuffs = BuffConfig.DataList.Where(item => item.Type == (int)emBuffType.Ranking);
            return rankingBuffs;
        }

        /// <summary>
        /// 获取排行榜buff信息
        /// </summary>
        /// <returns></returns>
        public List<RankingBuff> GetRankingBuffs() {
            List<RankingBuff> result = new List<RankingBuff>();
            var rankingBuffs = GetRankingBuffConfigs();
            foreach (var rBuff in rankingBuffs) {
                var buffName = rBuff.Id;
                var rConfig = RankingConfig.DataList.FindLast(item => item.Bonus.Contains(buffName));
                var dataParams = rBuff.Data.Split(";");
                var desc = ScriptLocalization.Get($"activity/legend/{buffName}", rBuff.DescFallback);
                desc = string.Format(desc, dataParams);
                var buff = new RankingBuff {
                    Desc = desc,
                    Icon = Atlas.GetSprite(buffName),
                    Status = GetRankingBonusStatus(rConfig.Id),
                };
                result.Add(buff);
            }

            return result;
        }

        /// <summary>
        /// 获取稀有度边框颜色
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Color GetBuffColor(string id) {
            var buffLevel = GetBuffConfig(id).Level;
            Color result = Color.white;
            switch (buffLevel) {
                case 0:
                    result = Color.white;
                    break;
                case 1:
                    result = Color.cyan;
                    break;
                case 2:
                    result = new Color(1, 0.58f, 0, 1);
                    break;
            }

            return result;
        }

        public string GetRankStr(int rank) {
            var result = "";
            if (rank == 1) {
                result = "1st";
            } else if (rank == 2) {
                result = "2nd";
            } else if (rank == 3) {
                result = "3rd";
            } else {
                result = $"{rank}th";
            }

            return result;
        }

        #endregion

        private GameObject _displayHero;

        public GameObject GenHeroForView() {
            if (_displayHero != null) {
                GameObject.Destroy(_displayHero);
            }

            var heroIdx = BattleData.data.playerIndex;
            var path = "RGPrefab/Player/c" + string.Format("{0:D2}", heroIdx) + ".prefab";
            _displayHero = GameObject.Instantiate(ResourcesUtil.Load(path), new Vector3(-10000f, -10000f), Quaternion.identity) as GameObject;
            _displayHero!.transform.SetParent(RGGameSceneManager.Inst.temp_objects_parent);
            _displayHero.GetComponent<RGController>().BindToCameraOnStart = false;
            RGGameSceneManager.Inst.StartCoroutine(NextFrameSetupWeapon());
            GameObject.Instantiate(ResourcesUtil.Load("Activities/Legend/Prefabs/effect_show_hero.prefab"), _displayHero.transform);
             _displayHero.GetComponent<RGController>().transform.Find("img").localScale = new Vector3(-1f, 1f);
            ShowHonorTitle(false, _displayHero.GetComponent<RGController>());

            return _displayHero;
        }

        private IEnumerator NextFrameSetupWeapon() {
            yield return null;
            if (_displayHero == null) {
                yield break;
            }

            var heroCtrl = _displayHero.GetComponent<RGController>();
            
            if (heroCtrl is C31Controller) {
                // 领主删除timescale的特效
                foreach (Transform child in _displayHero.transform.Find("img/body")) {
                    child.gameObject.SetActive(false);
                }
            }

            // 羽翼
            var wingObj = ActivityLegendManager.GenWings(heroCtrl);
            var wingImg = wingObj.transform.Find("img");
            wingImg.localPosition = new Vector3(-wingImg.localPosition.x, wingImg.localPosition.y);

            // 屠龙刀
            var swordLevel = DataMgr.ActivityLegendData.GetFollowerLevel();
            var wPath = string.Format(FOLLOWER_W_PATH, swordLevel);
            var wProto = ResourcesUtil.Load<GameObject>(wPath);
            var wObj = GameObject.Instantiate(wProto);

            var weapons = heroCtrl.transform.Find("img").GetComponentsInChildren<RGWeapon>();
            foreach (var w in weapons) {
                GameObject.Destroy(w.gameObject);
            }

            heroCtrl.hand.SetWeaponFront(wObj.GetComponent<RGWeapon>());
        }

        /// <summary>
        /// 显示排行称号
        /// </summary>
        public void ShowHonorTitle(bool autoHide = false, RGController controller = null) {
            var theCtrl = controller ?? RGGameSceneManager.Inst.controller;
            if (theCtrl == null) return;

            var obj = theCtrl.transform.Find("honor_title");
            if (obj != null) {
                return;
            }

            var proto = ResourcesUtil.Load<GameObject>(HONOR_PATH);
            var tmpObj = GameObject.Instantiate(proto, theCtrl.transform);
            tmpObj.transform.localPosition = new Vector3(0, 1.5f, 0);
            tmpObj.name = "honor_title";

            if (autoHide) {
                HideHonorTitleAfter(5f, theCtrl);
            }
        }

        public void HideHonorTitleAfter(float delay = 5f, RGController ctrl = null) {
            RGGameSceneManager.Inst.StartCoroutine(HideTitleAfter(delay, ctrl));
        }

        private IEnumerator HideTitleAfter(float delay, RGController ctrl = null) {
            yield return new WaitForSeconds(delay);
            
            var master = ctrl ?? RGGameSceneManager.Inst.controller;
            if (master == null) yield break;

            var buffObj = master.transform.Find("honor_title");
            if (buffObj != null) {
                buffObj.GetComponent<LegendHonorTitle>().FadeOut();
            }
        }
    }

    [Serializable]
    public class RankingEntry {
        [JsonIgnore] public Sprite Avatar { get; set; }
        [SerializeField] public string Name { get; set; }
        [SerializeField] public int KillCount { get; set; }
        [SerializeField] public bool IsPlayer { get; set; }
        [SerializeField] public emRankingBonusStatus BonusStatus { get; set; }

        [JsonIgnore] public List<RankingBonus> Bonuses = new();
    }

    /// <summary>
    /// 装备
    /// </summary>
    [Serializable]
    public class EquipmentDrop {
        public EquipmentRarity Rarity { get; set; } // 稀有度
        public int Value { get; set; } // 回收值
    }

    /// <summary>
    /// 装备稀有度
    /// </summary>
    public enum EquipmentRarity {
        Common = 0,
        Good = 1,
        Rare = 2,
    }

    /// <summary>
    /// 排行榜称号达成奖励状态
    /// </summary>
    public enum emRankingBonusStatus {
        Pending, // 未达成，无法领取
        Available, // 可领取
        Claimed, // 已领取
    }

    /// <summary>
    /// 排行榜的奖励item
    /// </summary>
    [Serializable]
    public class RankingBonus {
        public string Name { get; set; } // 奖励名称
        public Sprite Sprite { get; set; } // 奖励icon
        public int Count { get; set; } // 数量
    }

    [Serializable]
    public class RankingBuff {
        public string Desc { get; set; }
        public Sprite Icon { get; set; }
        public emRankingBonusStatus Status { get; set; }
    }

    // 辅助类用于序列化List
    [Serializable]
    public class RankingWrapper {
        public List<RankingEntry> List;
    }
}