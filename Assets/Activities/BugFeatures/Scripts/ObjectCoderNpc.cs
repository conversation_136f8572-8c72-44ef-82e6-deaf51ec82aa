using DG.Tweening;
using I2.Loc;
using RGScript.Data;
using RGScript.Util.MathTool;
using RGScript.WeaponAffix;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Util.TAUtil;

namespace Activities.BugFeatures.Scripts {
    /// <summary>
    /// 活动npc - coder，刷新漏洞芯片
    /// </summary>
    public class ObjectCoderNpc : ObjectActivityEnterNpc {
        public GameObject hitObj;
        public GameObject chipPrefab;
        private int _refreshTime = 0;
        private List<int> _refreshPrices;
        private List<int> _excludeAffixIds = new List<int>();

        protected override void Start() {
            base.Start();
            _refreshPrices = DataMgr.ActivityBugFeatureData.GetConstValue("RefreshPrice").Split(",").Select(int.Parse).ToList();
        }

        public override void OnTriggerEnter2D(Collider2D other) {
            if (other.gameObject.CompareTag("Body_P")) {
                _tab.gameObject.SetActive(false);
                other.GetComponent<RGController>().SetItemtf(transform);
                string talkText = ScriptLocalization.Get("activity/bugfeatures2/talk0");
                string dialog = RGItem.GetPaymentDialogText(talkText, GetRefreshPrice(), ItemLevel.GoldCoin);
                UICanvas.GetInstance().ShowObjectInfo(transform.position + new Vector3(0, labelHeight, 0), dialog);
            }
        }

        private int GetRefreshPrice() {
            if (_refreshTime < _refreshPrices.Count) {
                return _refreshPrices[_refreshTime];
            }

            return _refreshPrices[^1];
        }

        public override void ItemTrigger(RGController controller) {
            var frontWeapon = controller.hand.front_weapon;
            if (frontWeapon == null) {
                ShowTxt(ScriptLocalization.Get("activity/bugfeatures2/talk1", "兄嘚你没有漏洞芯片啊。"));
                return;
            }

            var wBadAffixes = frontWeapon.GetWeaponAffixList();
            if (wBadAffixes.Count == 0 || !((WeaponAffixBugFeatureBase)wBadAffixes[0]).IsBadAffix) {
                ShowTxt(ScriptLocalization.Get("activity/bugfeatures2/talk1", "兄嘚你没有漏洞芯片啊。"));
                return;
            }

            var price = GetRefreshPrice();
            if (!RGGameProcess.Inst.ConsumeCoin(price, emStatisticsType.BugFeature2RefreshBadChip)) {
                ShowTxt(ScriptLocalization.Get("activity/bugfeatures2/talk3", "兄嘚你钱钱不够啊。"));
            } else {
                _refreshTime++;
                var affix = wBadAffixes.First();
                frontWeapon.RemoveWeaponAffix(affix);

                _excludeAffixIds.Add(affix.Config.Id);
                var affixId = DataMgr.ActivityBugFeatureData.GetRandomBadAffixId(_excludeAffixIds);
                var newAffix = WeaponAffixFactory.Create(affixId);
                frontWeapon.AddWeaponAffix(newAffix);
                StartCoroutine(ShowChipAnim(affixId));
                UICanvas.GetInstance().UpdateGemText();
                frontWeapon.ShowWeaponAffix();
            }
        }

        private IEnumerator ShowChipAnim(int affixId) {
            var affixInfo = DataMgr.ActivityBugFeatureData.GetAffixInfo(affixId);
            var chipObj = Instantiate(chipPrefab, transform.position, Quaternion.identity);
            chipObj.transform.GetComponent<SpriteRenderer>().sprite = affixInfo.icon;
            var targetTr = RGGameSceneManager.Inst.controller.transform;
            var targetPos = targetTr.position + new Vector3(0, 1f);
            float jumpPower = 4.5f;
            float duration = 1f;
            float shrinkStartRatio = 0.5f;
            Sequence sequence = DOTween.Sequence();
            sequence.Append(chipObj.transform.DOJump(targetPos, jumpPower, 1, duration)
                .SetEase(Ease.OutQuad));
            sequence.Join(chipObj.transform.DORotate(new Vector3(0, 0, 720), duration, RotateMode.FastBeyond360));
            sequence.Insert(duration * shrinkStartRatio,
                chipObj.transform.DOScale(Vector3.zero, duration * (1 - shrinkStartRatio))
                    .SetEase(Ease.InQuad)
            );

            sequence.OnComplete(() => {
                Instantiate(hitObj, chipObj.transform.position, Quaternion.identity);
                UICanvas.Inst.weapon_item_icon.ShowActivityWeaponAffix(true, affixInfo.icon, affixId);
                Destroy(chipObj);
            }).SetUpdate(true);

            yield return null;
            ShowTxt(ScriptLocalization.Get("activity/bugfeatures2/talk4", $"又有动力修复漏洞了！"));
        }
    }
}