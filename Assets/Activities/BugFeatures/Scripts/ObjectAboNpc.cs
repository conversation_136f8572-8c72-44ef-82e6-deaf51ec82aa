using System;
using UIFramework;

namespace Activities.BugFeatures.Scripts {
    /// <summary>
    /// 活动npc - abo
    /// </summary>
    public class ObjectAboNpc : ObjectActivityEnterNpc {
        private void Awake() {
            activityCoinName = ActivityBugFeatureManager.MatName;
        }

        public override void ItemTrigger(RGController controller) {
            base.ItemTrigger(controller);
            if (!StatisticData.data.IsEventRecord(RGGameConst.OneOffEvent.ReadActivityBugFeaturesGuide.ToString())) {
                // 首次弹出介绍界面
                var guideView =
                    UIManager.Inst.OpenUIView<CommonGuideView>("Activities/BugFeatures/Prefabs/GuideView.prefab");
                guideView.AfterReadGuide = () => {
                    StatisticData.data.RecordEvent(RGGameConst.OneOffEvent.ReadActivityBugFeaturesGuide.ToString(),
                        true);
                    ShowTheView(controller);

                    // 首次对话后上报
                    TAUtil.Track("activity_bugfeatures_first_talk");
                };
            } else {
                ShowTheView(controller);
            }
        }

        private void ShowTheView(RGController controller) {
            controller.SetItemtf(null);
            if (UICanvas.GetInstance()) {
                UICanvas.GetInstance().HideTempUI();
                UICanvas.GetInstance().HideObjectInfo();
            }
            ActivityBugFeatureManager.ShowWindow();
        }
    }
}