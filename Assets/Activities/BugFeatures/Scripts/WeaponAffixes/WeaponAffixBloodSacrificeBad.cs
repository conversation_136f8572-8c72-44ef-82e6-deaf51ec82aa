using Activities.BugFeatures.Scripts;
using Tayx.Graphy.Utils.NumString;
using UnityEngine;

namespace RGScript.WeaponAffix {
    /// <summary>
    /// 血祭 漏洞芯片
    /// 每次使用武器都有概率会损失生命值
    /// </summary>
    public class WeaponAffixBloodSacrificeBad : WeaponAffixBugFeatureBase {
        private int _percent = 10;
        private const string VFX_PATH = "Activities/BugFeatures/Prefabs/effect_blood.prefab";
        private GameObject _bloodProto;

        protected override void OnInit() {
            base.OnInit();
            _percent = Config.Factor0.ToInt();
            _bloodProto = ResourcesUtil.Load<GameObject>(VFX_PATH);
        }

        protected override void OnGet() {
            base.OnGet();
        }

        protected override void OnRemove() {
            base.OnRemove();
        }

        protected override void DoEffect() {
            base.DoEffect();
        }

        private void AddVfx() {
            var vfx = PrefabPool.Inst.Take(_bloodProto, Weapon.transform);
            vfx.transform.localPosition = Vector3.zero;
            vfx.GetComponent<BloodSiphonController>().target = Weapon.transform;
            vfx.SetActive(true);
        }

        protected override void RemoveEffect() {
            base.RemoveEffect();
        }

        public override void OnAfterAttack() {
            base.OnAfterAttack();
            if (!isActive) return;
            if (Weapon.owner == null) return;
            if (Weapon.owner.attribute.hp <= 1) return;
            if (Random.Range(0, 100) >= _percent) return;

            Weapon.owner.attribute.RestoreHealth(-1);
            AddVfx();
        }
    }
}