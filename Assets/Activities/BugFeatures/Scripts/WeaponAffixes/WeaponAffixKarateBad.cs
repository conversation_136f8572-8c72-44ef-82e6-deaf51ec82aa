using Activities.BugFeatures.Scripts;
using DG.Tweening;
using System.Collections;
using TMPro;
using UnityEngine;
using ValueProperty;

namespace RGScript.WeaponAffix {
    /// <summary>
    /// 空手道 漏洞芯片
    /// 忘记了如何使用武器，只能用手刀攻击
    /// </summary>
    public class WeaponAffixKarateBad : WeaponAffixBugFeatureBase {
        // 连击系统变量
        private int _comboCount = 0;
        private float _lastHitTime = 0f;
        private const float COMBO_TIMEOUT = 5f; // 超时重置

        // UI 元素
        private static TextMeshProUGUI _comboText;
        private static RectTransform _comboPanel;
        private static Sequence _comboAnimation;
        private Coroutine _comboCheckCoroutine;

        protected override void OnInit() {
            base.OnInit();
            InitComboUI();
        }

        protected override void OnGet() {
            base.OnGet();
            if (Weapon.owner == null) return;
            if (Weapon.owner is not RGController rgCtrl) return;
            if (Weapon.owner != RGGameSceneManager.Inst.controller) return;

            rgCtrl.onBtnAttackClick += OnBtnAttackClick;
            if (_comboCheckCoroutine != null) {
                RGGameSceneManager.Inst.StopCoroutine(_comboCheckCoroutine);
            }

            _comboCheckCoroutine = RGGameSceneManager.Inst.StartCoroutine(ComboCheckRoutine());
            SimpleEventManager.AddEventListener<PlayerBulletHitEnemyEvent>(OnPlayerBulletHitEnemy);
        }

        protected override void OnRemove() {
            base.OnRemove();
            if (Weapon.owner == null) return;
            if (Weapon.owner is not RGController rgCtrl) return;
            if (Weapon.owner != RGGameSceneManager.Inst.controller) return;

            rgCtrl.onBtnAttackClick -= OnBtnAttackClick;
            SimpleEventManager.RemoveListener<PlayerBulletHitEnemyEvent>(OnPlayerBulletHitEnemy);
        }

        protected override void DoEffect() {
            base.DoEffect();
        }

        protected override void RemoveEffect() {
            base.RemoveEffect();
        }

        private bool OnBtnAttackClick(bool down) {
            if (!isActive) return true;
            if (!down) return true;

            Weapon.owner.hand.AtkCut();
            return false;
        }

        /// <summary>
        /// 初始化连击UI元素
        /// </summary>
        private void InitComboUI() {
            if (_comboText != null) return;

            _comboPanel = new GameObject("ComboPanel").AddComponent<RectTransform>();
            _comboPanel.SetParent(UICanvas.Inst.transform);
            _comboPanel.anchorMin = new Vector2(1, 1);
            _comboPanel.anchorMax = new Vector2(1, 1);
            _comboPanel.pivot = new Vector2(1, 1);
            _comboPanel.anchoredPosition = new Vector2(-115f, -56f);
            _comboPanel.sizeDelta = new Vector2(250, 100);

            GameObject textGO = new GameObject("ComboText");
            textGO.transform.SetParent(_comboPanel);
            textGO.layer = LayerMask.NameToLayer("UI");
            _comboText = textGO.AddComponent<TextMeshProUGUI>();
            _comboText.alignment = TextAlignmentOptions.Center;
            _comboText.fontSize = 40f;
            _comboText.color = Color.yellow;
            _comboText.text = "";

            RectTransform comboTextRectTransform = _comboText.rectTransform;
            comboTextRectTransform.anchorMin = new Vector2(0, 0);
            comboTextRectTransform.anchorMax = new Vector2(1, 1);
            comboTextRectTransform.pivot = new Vector2(0.5f, 0.5f);
            comboTextRectTransform.anchoredPosition = Vector2.zero;
            comboTextRectTransform.sizeDelta = Vector2.zero;

            comboTextRectTransform.offsetMin = new Vector2(-12.38f, -18.8f);
            comboTextRectTransform.offsetMax = Vector2.zero;
            comboTextRectTransform.localRotation = Quaternion.Euler(0, 0, 15f);
        }

        private void OnPlayerBulletHitEnemy(PlayerBulletHitEnemyEvent e) {
            if (!isActive) return;
            if (e.damageCarrier == null) return;
            if ((e.damageCarrier.damageType & emDamageType.HandCut) == 0) return;

            UpdateComboCount();
            if (e.enemy != null && e.enemy.dead) {
                var sprite = ActivityBugFeatureData.Atlas.GetSprite("ko");
                UICanvas.Inst.ShowObjectWithImage(e.enemy.transform, sprite, 2.5f, 1.5f);
            }
        }

        /// <summary>
        /// 更新连击计数
        /// </summary>
        private void UpdateComboCount() {
            if (Time.time - _lastHitTime > COMBO_TIMEOUT) {
                _comboCount = 0;
            }

            _comboCount++;
            _lastHitTime = Time.time;
            UpdateComboDisplay();
        }

        /// <summary>
        /// 更新连击显示并播放动画
        /// </summary>
        private void UpdateComboDisplay() {
            if (_comboText == null) return;

            _comboText.DOFade(1f, 0).SetUpdate(true);

            string comboText = "";
            string countText = _comboCount + " HITS";
            for (int i = 0; i < countText.Length; i++) {
                comboText += GetGradientCharacter(countText[i], i, countText.Length);
            }
            _comboText.text = comboText;

            if (_comboAnimation != null && _comboAnimation.IsPlaying()) {
                _comboAnimation.Kill();
            }

            _comboAnimation = DOTween.Sequence();
            _comboAnimation.Append(_comboText.transform.DOScale(1.5f, 0.2f).SetEase(Ease.OutBack).SetUpdate(true));
            _comboAnimation.Append(_comboText.transform.DOScale(1f, 0.3f).SetEase(Ease.OutBounce).SetUpdate(true));
            _comboAnimation.Play();
        }

        /// <summary>
        /// 获取具有垂直渐变颜色的字符
        /// </summary>
        private string GetGradientCharacter(char character, int index, int totalLength) {
            float ratio = (float)index / totalLength;
            Color color = Color.Lerp(Color.yellow, Color.red, ratio);

            return $"<color=#{ColorUtility.ToHtmlStringRGB(color)}>{character}</color>";
        }

        private IEnumerator ComboCheckRoutine() {
            var wait = new WaitForSeconds(0.2f);
            while (true) {
                yield return wait;
                if (_comboCount > 0 && Time.time - _lastHitTime > COMBO_TIMEOUT) {
                    ResetCombo();
                }
            }
        }

        /// <summary>
        /// 重置连击计数
        /// </summary>
        private void ResetCombo() {
            _comboCount = 0;
            if (_comboText != null) {
                _comboText.DOFade(0f, 0.5f).OnComplete(() => {
                    _comboText.text = "";
                }).SetUpdate(true);
            }
        }
    }
}