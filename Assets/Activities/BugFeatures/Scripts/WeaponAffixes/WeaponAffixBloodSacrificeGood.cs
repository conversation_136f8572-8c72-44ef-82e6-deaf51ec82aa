using Activities.BugFeatures.Scripts;
using System.Collections;
using Tayx.Graphy.Utils.NumString;
using UnityEngine;

namespace RGScript.WeaponAffix {
    /// <summary>
    /// 血祭 元气芯片
    /// 每次承受伤害，对角色实际造成的伤害量将转化为增加的武器攻击力，维持{0}秒，可叠加最多{1}层，可刷新时间
    /// </summary>
    public class WeaponAffixBloodSacrificeGood : WeaponAffixBugFeatureBase {
        private float _duration;
        private int _maxStack;
        private int _percent; // 没受到1点伤害，增加武器攻击力%
        private int _currStack; // 当前层数
        private Coroutine _coroutine;
        private int _totalGetDamage = 0;
        private const string VFX_PATH = "Activities/BugFeatures/Prefabs/effect_blood.prefab";
        private GameObject _bloodProto;
        
        protected override void OnInit() {
            base.OnInit();
            _duration = Config.Factor0;
            _maxStack = Config.Factor1.ToInt();
            _percent = Config.Factor2.ToInt();
            _bloodProto = ResourcesUtil.Load<GameObject>(VFX_PATH);
        }

        protected override void OnGet() {
            base.OnGet();
            if (Weapon.owner != RGGameSceneManager.Inst.controller) return;
            
            ((RGController)Weapon.owner).OnGetHurt += OnGetHurt;
        }

        protected override void OnRemove() {
            base.OnRemove();
            if (Weapon.owner != RGGameSceneManager.Inst.controller) return;
            
            ((RGController)Weapon.owner).OnGetHurt -= OnGetHurt;
        }

        protected override void DoEffect() {
            base.DoEffect();
        }

        protected override void RemoveEffect() {
            base.RemoveEffect();
            if (_coroutine != null && RGGameSceneManager.Inst != null) {
                RGGameSceneManager.Inst.StopCoroutine(_coroutine);
            }
        }

        public override void OnCreateBullet(GameObject bullet) {
            base.OnCreateBullet(bullet);
            if (!isActive) return;
            var carrier = bullet.GetComponent<DamageCarrier>();
            var bulletInfo = carrier.bulletInfo;
            var damageInfo = carrier.damageInfo;
            float finalDamage = damageInfo.damage;
            var totalPercent = _percent * _totalGetDamage * _currStack;
            if (totalPercent <= 0) return;
            
            finalDamage += finalDamage * totalPercent / 100f;
            damageInfo.SetDamage((int)finalDamage);
            carrier.UpdateInfo(bulletInfo, damageInfo);
        }

        private void OnGetHurt(HurtInfo hurtInfo) {
            if (!isActive) return;
            if (hurtInfo.Damage <= 0) return;
            if (_currStack >= _maxStack) return;

            _currStack++;
            _totalGetDamage += hurtInfo.Damage;
            if (LogUtil.IsShowLog) {
                LogUtil.Log($"currStack: {_currStack} totalGetDamage: {_totalGetDamage} damage: {hurtInfo.Damage}", Config.Class);
            }
            
            if (_coroutine != null) {
                RGGameSceneManager.Inst.StopCoroutine(_coroutine);
            }
            
            _coroutine = RGGameSceneManager.Inst.StartCoroutine(DelayClear());
            AddVfx();
        }
        
        private void AddVfx() {
            var vfx = PrefabPool.Inst.Take(_bloodProto, Weapon.transform);
            vfx.transform.localPosition = Vector3.zero;
            vfx.GetComponent<BloodSiphonController>().target = Weapon.transform;
            vfx.SetActive(true);
        }

        private IEnumerator DelayClear() {
            yield return new WaitForSeconds(_duration);
            
            // 清空层数
            _currStack = 0;
            _totalGetDamage = 0;
        }
    }
}