using RGScript.Util;
using UnityEngine;

namespace RGScript.WeaponAffix {
    /// <summary>
    /// PPT战神元气芯片词条
    /// 60帧以下，每5帧增加10%造成伤害。
    /// </summary>
    public class WeaponAffixPPTGood : WeaponAffixBugFeatureBase {
        private const int Limit = 60;
        private float _damageFactor;
        
        protected override void OnInit() {
            base.OnInit();
            _damageFactor = Config.Factor0;
        }

        protected override void OnGet() {
            base.OnGet();
        }

        protected override void OnRemove() {
            base.OnRemove();
            HelperUtil.Inst.ShowFPS(false);
        }
        
        protected override void DoEffect() {
            base.DoEffect();
            HelperUtil.Inst.ShowFPS(true);
        }

        protected override void RemoveEffect() {
            base.RemoveEffect();
        }

        public override void OnCreateBullet(GameObject bullet) {
            base.OnCreateBullet(bullet);
            if (!isActive) return;
            
            var fps = (int)HelperUtil.Inst.CurrentFPS;
            var overTime = (Limit - fps) / 5;
            if (overTime <= 0) return;
            
            var rgBullet = bullet.GetComponent<DamageCarrier>();
            var bulletInfo = rgBullet.bulletInfo;
            var damageInfo = rgBullet.damageInfo;
            var damage = bulletInfo.damage;
            var finalDmg = (int)(damage * (1 + _damageFactor * overTime / 100f));
            bulletInfo.damage = finalDmg;
            damageInfo.damage = finalDmg;
            rgBullet.UpdateInfo(bulletInfo, damageInfo);
        }
    }
}