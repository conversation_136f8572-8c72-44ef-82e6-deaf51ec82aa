using I2.Loc;
using RGScript.Util;
using Tayx.Graphy.Utils.NumString;
using UnityEngine;

namespace RGScript.WeaponAffix {
    /// <summary>
    /// 什么动静 漏洞芯片词条
    /// 相较于100%音量，每降低10%，攻速降低5%
    /// </summary>
    public class WeaponAffixWhatVolumeBad : WeaponAffixBugFeatureBase {
        private int _volumeChangeVal;
        private float _reduceApkSpeedFactor;
        
        protected override void OnInit() {
            base.OnInit();
            _volumeChangeVal = Config.Factor0.ToInt();
            _reduceApkSpeedFactor = -Config.Factor1 / 100f;
        }

        protected override void OnGet() {
            base.OnGet();
            SimpleEventManager.AddEventListener<VolumeChangeEvent>(OnVolumeChanged);
        }

        protected override void OnRemove() {
            base.OnRemove();
            SimpleEventManager.RemoveListener<VolumeChangeEvent>(OnVolumeChanged);
        }

        protected override void DoEffect() {
            base.DoEffect();
            DoEffectLogic();
            HelperUtil.Inst.ShowOtherText(true, GetVolume());
        }

        protected override void RemoveEffect() {
            base.RemoveEffect();
            DoRemoveEffect();
            HelperUtil.Inst.ShowOtherText(false);
        }
        
        private void OnVolumeChanged(VolumeChangeEvent e) {
            DoEffectLogic();
            HelperUtil.Inst.ShowOtherText(true, GetVolume());
        }
        
        private void DoEffectLogic() {
            if (!isActive) return;
            if (!Weapon.IsFrontWeapon) return;
            
            ChangeApkSpeed();
        }
        
        private void DoRemoveEffect() {
            if (Weapon.owner!= null) {
                Weapon.owner.Attribute.addAtkSpeed.RemoveValue(this, true);
                Weapon.owner.Attribute.OnAtkSpeedChanged();
            }
        }
        
        private void ChangeApkSpeed() {
            DoRemoveEffect();
            var downVolume = 100 - RGMusicManager.Inst.MainVolume * 100;
            if (downVolume <= 0) return;

            var reduceFactor = (int)downVolume / _volumeChangeVal;
            if (reduceFactor <= 0) return;
            
            var reduceSpeed = _reduceApkSpeedFactor * reduceFactor;
            Weapon.owner.Attribute.addAtkSpeed.AddAdditionValue(this, reduceSpeed);
            Weapon.owner.Attribute.OnAtkSpeedChanged();
        }
        
        private string GetVolume() {
            var text = ScriptLocalization.Get("ui/tip_volume");
            var volume = Mathf.RoundToInt(RGMusicManager.Inst.MainVolume * 100);
            return string.Format(text, volume);
        }
    }
}