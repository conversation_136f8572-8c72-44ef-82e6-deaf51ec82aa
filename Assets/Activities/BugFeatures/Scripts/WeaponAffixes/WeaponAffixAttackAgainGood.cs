using RGScript.Util.MathTool;
using System.Collections.Generic;
using UnityEngine;

namespace RGScript.WeaponAffix {
    /// <summary>
    /// 伤口撒盐 元气芯片
    /// 对同一个敌人连击时，攻击力按照5%叠加升高（基础攻击力*（1+x*5%）），攻击其他目标会打断重新计算
    ///（攻击力提升上限200%）
    /// </summary>
    public class WeaponAffixAttackAgainGood : WeaponAffixBugFeatureBase {
        private float _extraDamageFactor = 5f;
        private float _extraDamageFactorLimit = 200f;

        private int _lastTargetId; // 上一次攻击的敌人ID
        private int _comboCount; // 当前连击次数
        private float _maxMultiplier = 3f; // 200%上限（1+2=3）

        protected override void OnInit() {
            base.OnInit();
            _extraDamageFactor = Config.Factor0 / 100f;
            _extraDamageFactorLimit = Config.Factor1 / 100f;
            _lastTargetId = 0;
            _comboCount = 0;
        }

        protected override void OnGet() {
            base.OnGet();
            SimpleEventManager.AddEventListener<PlayerBulletPreHitEnemyEvent>(OnPlayerPreHitEnemy);
        }

        protected override void OnRemove() {
            base.OnRemove();
            SimpleEventManager.RemoveListener<PlayerBulletPreHitEnemyEvent>(OnPlayerPreHitEnemy);
        }

        protected override void DoEffect() {
            base.DoEffect();
        }

        protected override void RemoveEffect() {
            base.RemoveEffect();
        }

        private void OnPlayerPreHitEnemy(PlayerBulletPreHitEnemyEvent e) {
            if (!isActive) return;
            if (e.sourceWeapon != Weapon) return;

            var eHashCode = e.enemy.GetHashCode();
            if (_lastTargetId != eHashCode) {
                _comboCount = 0;
                _lastTargetId = eHashCode;
                return;
            }

            _comboCount++;
            float damageMultiplier = _comboCount * _extraDamageFactor;
            damageMultiplier = Mathf.Min(damageMultiplier, _extraDamageFactorLimit);

            e.ModifyDamage(new Cicada.AttributeModifier(damageMultiplier, Cicada.AttributeModifierType.PercentAdd, null));
        }
    }
}