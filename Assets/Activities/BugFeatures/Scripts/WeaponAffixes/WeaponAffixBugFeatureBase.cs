using RGScript.Data;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace RGScript.WeaponAffix {
    /// <summary>
    /// 满天飞活动芯片词条基类
    /// </summary>
    public class WeaponAffixBugFeatureBase : WeaponAffixBase {
        public readonly int CHIP_PROGRESS_TARGET = 100; // 漏洞芯片升级为元气芯片所需的进度
        public readonly string CHIP_PROGRESS = "chip_progress"; // 当前进度
        public readonly string CHIP_PROGRESS_INCREASE = "chip_progress_increase"; // 累加造成的伤害，未增加到chip_progress
        public bool IsBadAffix => DataMgr.ActivityBugFeatureData.BadAffixes.Contains(Id);
        public Color _badColor = new Color(0.56f, 0.56f, 0.56f);
        public Color _goodColor = Color.white;
        protected bool isActive = false;
        private GameObject _effectPrefab;
        private Coroutine _showItemInfoCoroutine;

        protected override void OnInit() {
            base.OnInit();
            if (IsBadAffix) {
                _effectPrefab = ResourcesUtil.Load<GameObject>("Activities/BugFeatures/Prefabs/effect_bad.prefab");
            } else {
                _effectPrefab = ResourcesUtil.Load<GameObject>("Activities/BugFeatures/Prefabs/effect_good.prefab");
            }
        }

        protected override void OnGet() {
            base.OnGet();
            AddVfx();

            if (Weapon.IsFrontWeapon) {
                DoEffect();
            }

            if (Weapon.owner != null && Weapon.owner == RGGameSceneManager.Inst.controller) {
                DataMgr.ActivityBugFeatureData.CacheChipForTrack(Id);
            }

            if (LogUtil.IsShowLog) {
                LogUtil.Log($"[{Weapon.GetItemName()}] OnGet Id:{Id}:{Config.Class}");
            }
        }

        protected override void OnRemove() {
            base.OnRemove();
            RemoveVfx();
            RemoveEffect();
            if (LogUtil.IsShowLog) {
                LogUtil.Log($"[{Weapon.GetItemName()}] OnRemove Id:{Id}:{Config.Class}");
            }
        }

        protected virtual void AddVfx() {
            if (Weapon.BodySprite == null) return;

            RemoveVfx();
            if (IsBadAffix) {
                ChangeWeaponBodyColors(_badColor);
            } else {
                ChangeWeaponBodyColors(_goodColor);
            }

            var vfx = GameObject.Instantiate(_effectPrefab, Weapon.transform);
            vfx.transform.SetAsLastSibling();
            vfx.transform.localPosition = Vector3.zero;
            vfx.gameObject.name = "ps_affix";

            Bounds bounds = Weapon.BodySprite.bounds;
            Vector3 localCenter = Weapon.BodySprite.transform.InverseTransformPoint(bounds.center);
            vfx.transform.localPosition = localCenter;

            if (!IsBadAffix) {
                var ps = vfx.GetComponent<ParticleSystem>();
                var shape = ps.shape;
                if (Weapon.BodySprite.sprite != null) {
                    shape.shapeType = ParticleSystemShapeType.SpriteRenderer;
                    shape.spriteRenderer = Weapon.BodySprite;
                }
            }
        }

        protected virtual void RemoveVfx() {
            ChangeWeaponBodyColors(_goodColor);
            
            foreach (Transform child in Weapon.transform) {
                if (child.name == "ps_affix") {
                    GameObject.Destroy(child.gameObject);
                }
            }
        }

        private void ChangeWeaponBodyColors(Color targetColor) {
            foreach (var item in Weapon.GetComponentsInChildren<SpriteRenderer>()) {
                if (item.gameObject.name == "w") {
                    item.color = targetColor;
                }
            }
        }

        protected virtual void DoEffect() {
            isActive = true;
        }

        protected virtual void RemoveEffect() {
            isActive = false;
        }

        public override void OnSwitchWeaponBack() {
            base.OnSwitchWeaponBack();
            RemoveEffect();

            if (Weapon.owner != RGGameSceneManager.Inst.controller) return;

            if (_showItemInfoCoroutine != null) {
                RGGameSceneManager.Inst.StopCoroutine(_showItemInfoCoroutine);
            }

            UICanvas.Inst.HideItemInfo();
        }

        /// <summary>
        /// 活动词条，手持武器才生效
        /// </summary>
        public override void OnSwitchWeaponFront() {
            base.OnSwitchWeaponFront();
            DoEffect();
            if (Weapon.owner == RGGameSceneManager.Inst.controller) {
                RGGameSceneManager.Inst.StartCoroutine(DelayShowAffix());
            }
        }

        private IEnumerator DelayShowAffix() {
            yield return null;
            Weapon.ShowWeaponAffix();

            if (_showItemInfoCoroutine != null) {
                RGGameSceneManager.Inst.StopCoroutine(_showItemInfoCoroutine);
            }

            _showItemInfoCoroutine = RGGameSceneManager.Inst.StartCoroutine(DelayHideItemInfo());
        }

        private IEnumerator DelayHideItemInfo() {
            yield return new WaitForSeconds(1.5f);
            UICanvas.Inst.HideItemInfo();
        }

        public override void OnWeaponDrop() {
            base.OnWeaponDrop();
            RemoveEffect();
        }
        
        public void ClearIncreaseValue() {
            if (CustomData.ContainsKey(CHIP_PROGRESS_INCREASE)) {
                CustomData[CHIP_PROGRESS_INCREASE] = 0;
            }
        }

        /// <summary>
        /// 词条升级数据
        /// </summary>
        /// <param name="key"></param>
        /// <param name="value"></param>
        public void AddIncreaseValue(int value) {
            if (!CustomData.TryAdd(CHIP_PROGRESS_INCREASE, value)) {
                CustomData[CHIP_PROGRESS_INCREASE] += value;
            }
        }

        public void AddCurrentValue(int value) {
            if (!CustomData.TryAdd(CHIP_PROGRESS, value)) {
                CustomData[CHIP_PROGRESS] += value;
            }
        }

        public int GetCurrentProgress() {
            var currVal = CustomData.GetValueOrDefault(CHIP_PROGRESS, 0);
            return Mathf.Min(currVal, CHIP_PROGRESS_TARGET);
        }

        public int GetIncreaseProgress() {
            return CustomData.GetValueOrDefault(CHIP_PROGRESS_INCREASE, 0);
        }

        public override void OnHit(RGEController target, HurtInfo hurtInfo) {
            base.OnHit(target, hurtInfo);
            if (!IsBadAffix) return;
            if (!Weapon.IsFrontWeapon) return;
            if (Weapon.owner == null) return;
            if (!((RGController)Weapon.owner).IsLocalPlayer()) return;

            // 累计伤害
            AddIncreaseValue(hurtInfo.Damage);
        }
    }
}