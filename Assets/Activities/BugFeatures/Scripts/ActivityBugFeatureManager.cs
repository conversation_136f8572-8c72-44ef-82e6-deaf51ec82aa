using cfg.Activity;
using I2.Loc;
using RGScript.Config.Manager;
using RGScript.Data;
using RGScript.Manager.Message;
using RGScript.Map;
using RGScript.Util.MathTool;
using RGScript.Weapon;
using RGScript.WeaponAffix;
using SoulKnight.Runtime.Item;
using System;
using System.Collections;
using System.Collections.Generic;
using UIFramework;
using UnityCommon.UI;
using UnityEngine;
using Random = UnityEngine.Random;

namespace Activities.BugFeatures.Scripts {
    /// <summary>
    /// 活动良性 + 恶心因子（活动所特有，不同于挑战因子）
    /// </summary>
    public enum emActivityFactor {
        None,
        GoodBegin, // 良性因子开始 //-------------------------------------------------------

        FloatingGunman, // 悬浮枪手
        PhantomMaster, // 分身大师
        WeaponMaster, // 武器操控大师
        PetEgg, // 宠物孵化师
        PinballShooter, // 弹珠射手
        StatueArtist, // 雕像艺术家
        TalentMaster, // 天赋大师
        SubmachineGun, // 爆弹猎手

        GoodEnd, // 良性因子结束 //-------------------------------------------------------

        BadBegin, // 恶性因子开始 //-------------------------------------------------------

        QuickMan, // 鬼畜疾行者
        RandomScale, // 伸缩怪客
        BossAngry, // 领主暴君
        FlashDevil, // 瞬移狂魔
        BulletHell, // 弹幕狂人
        EnemyElite, // 魔界精英
        WeaponDestroyer, // 武器破坏者
        ScaleMagic, // 缩放魔术师
        GhostShooter, // 幽灵枪手

        BadEnd, // 恶性因子结束 //-------------------------------------------------------

        MidBegin, // 中性搞梗因子，对玩家不可见

        RandomSkin, // 每一层用不同皮肤
        Sword999, // 一刀999
        Emoticon, // 表情包欢乐大典
        LazyCat, // 偷懒的胡椒
        MJ, // MJ机械舞
        MagicBgm, // 魔性BGM
        DanceWoman, // 舞蹈女郎
        DeadBodyMove, // 尸体漂移
        PillMod, // 药罐子

        MidEnd
    }

    // 特性结构体
    public class BugFactor {
        public Sprite sprite;
        public string name;
        public string desc;
        public bool available;
    }

    /// <summary>
    /// bug特性满天飞
    /// https://chillyroom.feishu.cn/wiki/SLErwlGrMiKOk3kuNAec747Kn7f
    /// </summary>
    // ReSharper disable once RedundantAttributeSuffix
    [RGActivity]
    public class ActivityBugFeatureManager {
        public static string TAG = "ActivityBugFeatureManager";
        public const string ABO_NPC_PATH = "Activities/BugFeatures/Prefabs/abo_npc.prefab";
        public const string ABO_REPAIR_PATH = "Activities/BugFeatures/Prefabs/abo_repair.prefab";
        public const string CUSTOM_REBOUND_PATH = "Activities/BugFeatures/Prefabs/Features/effect_rebound_custom.prefab";
        public const string PET_IDLE_PATH = "Activities/BugFeatures/Prefabs/Features/pet34_idle.prefab";
        public const string EGG_PATH = "Activities/BugFeatures/Prefabs/Features/egg.prefab";
        public static string FACTOR_INFO_PATH = "Activities/BugFeatures/Prefabs/bugfeature_factors.prefab";
        public static string GOODCHIP_INFO_PATH = "Activities/BugFeatures/Prefabs/window_goodchip_intro.prefab";
        public static string WEAPON_BAG_PATH = "Activities/BugFeatures/Prefabs/bugfeature_weaponbag.prefab";
        public static string MatName = "material_activity_coin_bug_feature2";
        public static string CODER_PATH = "Activities/BugFeatures/Prefabs/npc_coder.prefab";
        public static string GUIDE_VIEW = "Activities/BugFeatures/Prefabs/GuideView.prefab";

        private static string TRACK_PREFIX = "ActivityBugFeature_";
        private static string LUA_GOOD_KEY = "good_factor";
        private static string LUA_BAD_KEY = "bad_factor";
        private static string LUA_MID_KEY = "middle_factor";
        public static ActivityConfig Config => DataMgr.ActivityConfigData.GetActivityConfig(TAG);
        private static bool InDebugMode => LogUtil.ShowDebugLog || GameConfigManager.GetInstance().Config.IsOpenLua;
        public static int TalentCount = 34; // 天赋大师天赋数量
        public static bool Inited = false; // 避免重复初始化重复监听
        private static List<int> CreateShopLevels = new List<int>() { 2, 4,  6, 9, 12, 14, 17, 19 }; // 创建刷新漏洞芯片程序员npc关卡
        private const int MAX_COIN_ONE_GAME = 2120;
        private static int _gotCoin = 0;

        /// <summary>
        /// 活动引导配置
        /// </summary>
        private static List<TeachTargetInfo> _teach_target_infos = new List<TeachTargetInfo>() {
            new TeachTargetInfo() {
                canvas_name = "Canvas",
                canvas_path = "window_activity_entry/body/Content/detail_wrapper/checkin_detail/body/groups/intro/body/scroll_view/viewport",
                track_name = "finish_intro",
            },
            new TeachTargetInfo() {
                canvas_name = "Canvas",
                canvas_path = "window_activity_entry/body/Content/detail_wrapper/checkin_detail/body/groups/intro/body/goodRoot/thisWeek/subBg",
                track_name = "current_factor",
            },
            new TeachTargetInfo() {
                canvas_name = "Canvas",
                canvas_path = "window_activity_entry/body/Content/detail_wrapper/checkin_detail/body/groups/intro/body/open_or_close",
                track_name = "btn_open",
                OnClick = OnClickGuideBtn,
            }
        };

        /// <summary>
        /// 活动引导ui入口配置
        /// </summary>
        private static List<TeachTargetInfo> _entry_teach_target_infos = new List<TeachTargetInfo>() {
            new TeachTargetInfo() {
                canvas_name = "Canvas",
                canvas_path = "window_game_entry/BG/Slot0/Level/Root/ChildContent/Root/Button",
                track_name = "btn_begin_game",
                OnClick = OnClickGuideBeginGame,
            }
        };

        /// <summary>
        /// 非新手玩家活动引导配置
        /// </summary>
        private static List<TeachTargetInfo> _old_teach_target_infos = new List<TeachTargetInfo>() {
            new TeachTargetInfo() {
                canvas_name = "Canvas",
                canvas_path = "window_activity_entry/body/Content/detail_wrapper/checkin_detail/body/groups/intro/body/open_or_close",
                track_name = "btn_open_old",
                OnClick = ClickOpenActivityBtnGuide,
            }
        };

        /// <summary>
        /// 天赋大师可叠加升级的天赋
        /// </summary>
        public static Dictionary<emBuff, int> TalentBuffs = new Dictionary<emBuff, int>() {
            { emBuff.MasterReflection, 3 },
            { emBuff.MasterContinuous, 8 },
            { emBuff.MasterShotgun, 5 },
            { emBuff.MasterSwordRange, 5 },
            { emBuff.MasterSpeedAtk, 8 },
            { emBuff.MasterWeapon, 8 },
            { emBuff.MasterHold, 8 },
            { emBuff.MaxArmor, 8 },
            { emBuff.MaxHp, 8 },
            { emBuff.MaxEnergy, 8 },
            { emBuff.ExpertBoxPot, 8 },
            { emBuff.ExtraEmitBullet, 8 },
            { emBuff.HitThunderStormBuff, 8 },
            { emBuff.HurtBeCrazy, 8 },
            { emBuff.MasterAccurate, 8 },
            { emBuff.ExpertShop, 8 },
        };

        #region 加入活动统一UI入口界面所必须得属性，方法

        public static bool NeedPop() {
            return false;
        }

        public static bool ShouldShowRedPoint() {
            bool shouldShow = false;
            var currentGoodFactor = GetCurrentGoodFactor();
            var local = PlayerSaveData.GetString("bug_feature_factor", "None");
            if (currentGoodFactor.ToString() != local) {
                shouldShow = true;
                PlayerSaveData.SetString("bug_feature_factor", currentGoodFactor.ToString());
            }

            return shouldShow;
        }

        public static void RecordPopCount() {
            PlayerSaveData.Inst.today_open_activity_bug_count++;
        }

        /// <summary>
        /// item是否是活动奖励
        /// </summary>
        /// <param name="itemName"></param>
        /// <returns></returns>
        public static bool IsItemReward(string itemName) {
            return false;
        }
        
        public static void SetInited() {
            Inited = true;
        }

        public static void AfterLoadAbFinished() {
            SimpleEventManager.AddEventListener<SelectHeroEvent>(OnSelectHeroEvent);
            SimpleEventManager.AddEventListener<EnterModeEvent>(OnEnterMode);
            SimpleEventManager.AddEventListener<BeforeMultiGameStartEvent>(OnBeforeMultiGameStart);
            SimpleEventManager.AddEventListener<EnterGameEvent>(OnContinueGame);
            SimpleEventManager.AddEventListener<EnterSceneEvent>(OnEnterSceneEvent);
            SimpleEventManager.AddEventListener<FirstClickAtGameStatement>(OnAfterEnterStatement);
            SimpleEventManager.AddEventListener<BeforeSelfEnterHeroRoomEvent>(BeforeEnterHeroRoom);
            SimpleEventManager.AddEventListener<AfterFirstMallCloseEvent>(OnAfterFirstMallClose);
            if (_needShowGuide) {
                ShowGuide();
            }
        }

        #endregion
        
        // 是否有未领取的奖励
        public static bool HasRewardToClaim() {
            foreach (var itemConfig in DataMgr.ActivityBugFeatureData.RewardConfig.DataList) {
                if (DataMgr.ActivityBugFeatureData.GetRewardLeftCount(itemConfig) > 0) {
                    return true;
                }
            }

            return false;
        }

        private static void OnSelectHeroEvent(SelectHeroEvent e) {
            if (Inited && IsAvailable()) {
                InitNpc();
                RGGameSceneManager.Inst.StartCoroutine(CheckAndShowActivityGuide2());
                SimpleEventManager.Raise(new UpdateFactorBarEvent());
            }
        }

        private static BaseUIView _activityView;

        private static void ShowGuide() {
            if (!GameUtil.CompleteBugFeatureTutorial()) {
                _activityView = ShowWindow();
                DoGuide0();
            }
        }

        private static IEnumerator CheckAndShowActivityGuide2() {
            var hasNewPlayerFacter = GameUtil.HasNewPlayerFactor;
            var hasOpen = GameUtil.CompleteBugFeatureTutorial();
            var hasCoin = GetActivityCoinCount() > 0;
            if (hasCoin && !hasOpen) {
                // 有金币，但是没有打开过活动，及时更新下标记，避免货币兑换后再次弹出
                StatisticData.data.RecordEvent(RGGameConst.BUG_FEATURE_TOTURIAL, true);
            }

            if (!hasNewPlayerFacter && !hasOpen && !hasCoin && GameUtil.IsSingleGame()) {
                // UI的渲染可能会卡顿, 延迟1秒再弹出
                yield return new WaitForSeconds(1);
                _activityView = ShowWindow();
                DoGuide2();
            }
        }

        public static bool InActivityGuide() {
            return _needShowGuide;
        }

        private static void DoGuide0() {
            var uiTeaching = UIManager.Inst.OpenUIView<UITeachingActivity>("ui_teaching_activity");
            uiTeaching.SetUp(RGGameConst.BUG_FEATURE_TOTURIAL, _teach_target_infos);
        }

        private static void DoGuide1() {
            var uiTeaching = UIManager.Inst.OpenUIView<UITeachingActivity>("ui_teaching_activity");
            uiTeaching.SetUp(RGGameConst.BUG_FEATURE_TOTURIAL, _entry_teach_target_infos);
        }

        private static void DoGuide2() {
            var uiTeaching = UIManager.Inst.OpenUIView<UITeachingActivity>("ui_teaching_activity");
            uiTeaching.SetUp(RGGameConst.BUG_FEATURE_TOTURIAL, _old_teach_target_infos);
            RGGameSceneManager.Inst.StartCoroutine(uiTeaching.DelayClose(3));
            StatisticData.data.RecordEvent(RGGameConst.BUG_FEATURE_TOTURIAL, true);
        }

        public static void CheckAndShowActivityGuide() {
            if (IsAvailable() && InActivityGuide() && !GameUtil.CompleteBugFeatureTutorial()) {
                DoGuide1();
            }
        }

        public static void OnClickGuideBeginGame() {
            GameEntryData.EnterGame(emGameMode.Normal);
            _needShowGuide = false;
            StatisticData.data.RecordEvent(RGGameConst.BUG_FEATURE_TOTURIAL, true);
        }

        public static void OnClickGuideBtn() {
            var proto = ResourcesUtil.Load<GameObject>("RGPrefab/LevelObject/Item/target_point.prefab");
            var temp_obj = GameObject.Instantiate(proto, RGGameSceneManager.GetInstance().temp_objects_parent, true);
            temp_obj.transform.position = new Vector3(-3, 7, 0);
            temp_obj.GetComponent<TargetPoint>().SetDestroyWhenReach(false);
            ClickOpenActivityBtnGuide();
        }

        private static void ClickOpenActivityBtnGuide() {
            if (_activityView != null) {
                _activityView.OnClick_Close();
            }

            ActivityBugFeatureManager.EnableActivity(true);
            SimpleEventManager.Raise(new UpdateFactorBarEvent());

            // 开启的时候，上报埋点
            var ta_data = new System.Collections.Generic.Dictionary<string, object> {
                { "tab_title", ScriptLocalization.GetCN(Config.Title) },
                { "act_action", "open_activity" },
            };
            global::TAUtil.Track("activity_entry_tab_click", ta_data);
        }

        /// <summary>
        /// 新手教程阶段，abing之后，打开商城且关闭后，刷新下活动状态，弹出活动引导
        /// </summary>
        /// <param name="e"></param>
        private static bool _needShowGuide = false;

        private static void OnAfterFirstMallClose(AfterFirstMallCloseEvent e) {
            if (GameUtil.IsSingleGame()) {
                _needShowGuide = true;
                if (Inited) {
                    ShowGuide();
                } else {
                    ActivityManagerBase.ReDoActInit(TAG);
                }
            }
        }

        /// <summary>
        /// 每次进入大厅前刷新下活动状态
        /// </summary>
        /// <param name="e"></param>
        private static void BeforeEnterHeroRoom(BeforeSelfEnterHeroRoomEvent e) {
            // 正在活动引导过程，刷新大厅则取消强制引导
            if (InActivityGuide()) {
                StatisticData.data.RecordEvent(RGGameConst.BUG_FEATURE_TOTURIAL, true);
                _needShowGuide = false;
            }
        }

        public static void FuncHotFix() {
        }

        /// <summary>
        /// 活动开启条件
        /// </summary>
        /// <returns></returns>
        public static bool IsAvailable() {
            var inPeriod = IsInActivityPeriod();
            var isGardenUnlocked = RoomItemUnlockThroughBattle.GetUnlock(emRoomUnlockItem.garden);
            var clientReady = true;
            if (!GameUtil.InMultiGame() || NetControllerManager.Inst.isServer) {
                clientReady = isGardenUnlocked;
            }

            if (LogUtil.IsShowLog) {
                LogUtil.Log($"IsAvailable inPeriod:{inPeriod} isGardenUnlocked:{isGardenUnlocked} clientReady:{clientReady}", TAG);
            }

            return inPeriod && clientReady;
        }


        /// <summary>
        /// 倒计时
        /// </summary>
        /// <returns></returns>
        public static int GetEndDays() {
            var endDate = ActivityUtil.GetEndDate(TAG);
            TimeSpan timeRemaining = endDate - DateTime.Today;
            int daysRemaining = Mathf.CeilToInt((float)timeRemaining.TotalDays);
            if (InDebugMode) {
                LogUtil.Log($"endDate:{endDate} today:{DateTime.Today} remainning:{daysRemaining}", TAG);
            }

            return daysRemaining;
        }

        public static int GetActivityCoinCount() {
            return ItemData.data.GetMaterialCount(MatName);
        }

        public static void AddActivityCoin(int num = 1, bool showRewardWindow = false,
            RGController controller = null) {
            var itemConfig = ItemConfigLoader.GetItemConfig(MatName);
            var icon = itemConfig.Icon.GetSprite();
            if (num > 0) {
                PickableInfo info = new PickableInfo() {
                    count = num,
                    name = MatName,
                    itemType = emItemType.Material,
                };

                RGGameProcess.Inst.got_pickable.Add(info);
                if (showRewardWindow && controller) {
                    UICanvas.GetInstance().ShowPickableWithSprite(
                        controller.transform, info, icon, 2.25f, 4f);
                }
            } else {
                ItemData.data.ConsumeMaterial(MatName, Mathf.Abs(num), true, true);
            }
        }

        /// <summary>
        /// 活动有效期
        /// </summary>
        /// <returns></returns>
        public static bool IsInActivityPeriod() {
            var sTime = ActivityUtil.StartTime(TAG);
            var eTime = ActivityUtil.EndTime(TAG);
            var time = ActivityUtil.GetNetworkTime();
            if (string.IsNullOrEmpty(sTime) || string.IsNullOrEmpty(eTime) || time == null) {
                if (LogUtil.IsShowLog) {
                    LogUtil.Log(
                        "!!! net.time:" + time + " start:" + sTime + " end:" + eTime + " => false", TAG);
                }

                return false;
            }

            var startTime = TimeUtil.ChineseTimestampToLocalDateTime(sTime);
            var endTime = TimeUtil.ChineseTimestampToLocalDateTime(eTime);
            var inPeriod = time >= startTime && time < endTime;
            if (LogUtil.IsShowLog) {
                LogUtil.Log(
                    "net.time:" + time + " activity.start:" + startTime + " activity.end:" + endTime + " => " +
                    inPeriod, TAG);
            }

            return inPeriod;
        }

        private static void InitNpc() {
            try {
                var npc = GameObject.Instantiate(ResourcesUtil.Load<GameObject>(ABO_NPC_PATH),
                    RoomObjectManager.Inst.transform);
                npc.transform.position = RGGameConst.ActivityNpcSlot;
            } catch (Exception e) {
                Debug.LogError(e);
            }
        }

        public static BaseUIView ShowWindow() {
            return DataMgr.ActivityEntryData.ShowWindow(TAG);
        }
        
        public static void ShowGoodChipInfo(int affixId) {
            UIManager.Inst.OpenUIView<UIWindowGoodChipIntro>(GOODCHIP_INFO_PATH, new object[]{ affixId });
        }

        public static void ShowFactorInfo() {
            UIManager.Inst.OpenUIView<UIBugFeatureFactorInfoWindow>(FACTOR_INFO_PATH);
        }
        
        public static void ShowWeaponBag() {
            UIManager.Inst.OpenUIView<UIBugFeatureActivityWeaponBag>(WEAPON_BAG_PATH);
        }

        public static void EnableActivity(bool enable) {
            if (InActivityGuide() && !enable) {
                // 引导过程中，玩家关闭活动，则中断活动引导
                _needShowGuide = false;
                StatisticData.data.RecordEvent(RGGameConst.BUG_FEATURE_TOTURIAL, true);
                if (TargetPoint.Current != null) {
                    TargetPoint.Delete();
                }
            }
        }

        public static void EnableGoodFeature(bool enable) {
            BattleData.data.RemoveActivityFacter(emActivityFactor.GoodBegin, emActivityFactor.GoodEnd);
            if (enable) {
                var currGoodFactor = GetCurrentGoodFactor();
                BattleData.data.SetActivityFactor(currGoodFactor);
                if (LogUtil.IsShowLog) {
                    LogUtil.Log($"开启活动特性因子，良性因子:{currGoodFactor.ToString()}", TAG);
                }
            } else {
                if (LogUtil.IsShowLog) {
                    LogUtil.Log($"清除良性活动特性因子", TAG);
                }
            }
        }

        /// <summary>
        /// UI开启活动后调用，打入特性因子到Battledata
        /// </summary>
        public static void EnableRandomOtherFeature(bool enable) {
            BattleData.data.RemoveActivityFacter(emActivityFactor.BadBegin, emActivityFactor.BadEnd);
            BattleData.data.RemoveActivityFacter(emActivityFactor.MidBegin, emActivityFactor.MidEnd);
            if (enable) {
                var badFactor = GetRandomBadFactor();
                BattleData.data.SetActivityFactor(badFactor);

                var midFactor = GetRandomMidFactor();
                if (midFactor != emActivityFactor.None) {
                    BattleData.data.SetActivityFactor(midFactor);
                }

                if (LogUtil.IsShowLog) {
                    LogUtil.Log($"开启活动特性因子，恶性因子:{badFactor.ToString()} 中性搞梗因子：{midFactor.ToString()}", TAG);
                }
            } else {
                if (LogUtil.IsShowLog) {
                    LogUtil.Log($"清除恶性，中性活动特性因子", TAG);
                }
            }
        }

        /// <summary>
        /// 当前周期生效的良性因子
        /// </summary>
        /// <returns></returns>
        public static emActivityFactor GetCurrentGoodFactor() {
#if UNITY_EDITOR
            var goodFactor = UnityEditor.EditorPrefs.GetString("SelectGoodFactor", null);
            if (goodFactor != "None" && Enum.TryParse(goodFactor, out emActivityFactor gf)) {
                LogUtil.Log($"使用了调试工具设置的良性因子：" + goodFactor);
                return gf;
            }
#endif

            if (GameConfigManager.GetInstance().Config.IsDebug) {
                // 调试模式，可以读取lua设置的值
                var bfStr = PlayerSaveData.GetString(LUA_GOOD_KEY, null);
                if (bfStr != null && Enum.TryParse(bfStr, out emActivityFactor bf)) {
                    Debug.Log("使用了lua设置的良性因子：" + bfStr);
                    return bf;
                }
            }

            return GetGoodFactorByPeriod(0);
        }

        /// <summary>
        /// 下个周期生效的良性因子
        /// </summary>
        /// <returns></returns>
        public static emActivityFactor GetNextGoodFactor() {
            return GetGoodFactorByPeriod(1);
        }

        /// <summary>
        /// 获取下一个生效的良性因子日期
        /// </summary>
        /// <returns></returns>
        public static DateTime GetNextGoodFactorDate() {
            var sTime = ActivityUtil.StartTime(TAG);
            var nowTime = ActivityUtil.GetNetworkTime();
            var startTime = TimeUtil.ChineseTimestampToLocalDateTime(sTime);
            var nextGoodFactor = GetGoodFactorByPeriod(1);
            var period = GetPeriod();

            for (var f = emActivityFactor.GoodBegin + 1; f < emActivityFactor.GoodEnd;) {
                if (f == nextGoodFactor && startTime > nowTime) {
                    break;
                }

                f++;
                startTime = startTime.AddDays(period);
                if (f == emActivityFactor.GoodEnd) {
                    // 循环
                    f = emActivityFactor.GoodBegin + 1;
                }
            }

            return startTime;
        }

        /// <summary>
        /// 获取xx周期生效的良性因子，当前周期为 0，下个周期 period 传1
        /// </summary>
        /// <returns></returns>
        public static emActivityFactor GetGoodFactorByPeriod(int period = 0) {
            var sTime = ActivityUtil.StartTime(TAG);
            var nowTime = ActivityUtil.GetNetworkTime();
            if (string.IsNullOrEmpty(sTime) || nowTime == null) {
                return emActivityFactor.None;
            }

            var startTime = TimeUtil.ChineseTimestampToLocalDateTime(sTime);
            var subTime = nowTime - startTime;
            var subDays = subTime.Value.Days;
            var idx = subDays / GetPeriod();
            var goodTotal = emActivityFactor.GoodEnd - emActivityFactor.GoodBegin - 1;
            var fixIdx = (idx + period) % goodTotal;
            var theFactor = emActivityFactor.GoodBegin + fixIdx + 1;
            return theFactor;
        }

        public static int GetPeriod() {
#if UNITY_EDITOR
            var period = UnityEditor.EditorPrefs.GetInt("SelectFactorPeriod", 0);
            if (period > 0) {
                return period;
            }
#endif

            return Config.Extra.ToInt();
        }

        /// <summary>
        /// 随机恶性因子
        /// </summary>
        /// <returns></returns>
        public static emActivityFactor GetRandomBadFactor() {
#if UNITY_EDITOR
            var tmpFactor = UnityEditor.EditorPrefs.GetString("SelectBadFactor", null);
            if (tmpFactor != null && Enum.TryParse(tmpFactor, out emActivityFactor tf)) {
                LogUtil.Log($"使用了调试工具设置的恶性因子：" + tmpFactor);
                return tf;
            }
#endif

            if (GameConfigManager.GetInstance().Config.IsDebug) {
                // 调试模式，可以读取lua设置的值
                var bfStr = PlayerSaveData.GetString(LUA_BAD_KEY, null);
                if (bfStr != null && Enum.TryParse(bfStr, out emActivityFactor bf)) {
                    Debug.Log("使用了lua设置的恶心因子：" + bfStr);
                    return bf;
                }
            }

            var total = emActivityFactor.BadEnd - emActivityFactor.BadBegin - 1;
            var randomIdx = UnityEngine.Random.Range(0, total);
            var badFactor = emActivityFactor.BadBegin + randomIdx + 1;
            return badFactor;
        }

        /// <summary>
        /// 随机恶搞中性因子
        /// </summary>
        /// <returns></returns>
        public static emActivityFactor GetRandomMidFactor() {
#if UNITY_EDITOR
            var midFactor = UnityEditor.EditorPrefs.GetString("SelectMidFactor", null);
            if (midFactor != null && Enum.TryParse(midFactor, out emActivityFactor mf)) {
                LogUtil.Log($"使用了调试工具设置的中性因子：" + mf);
                return mf;
            }
#endif

            if (GameConfigManager.GetInstance().Config.IsDebug) {
                // 调试模式，可以读取lua设置的值
                var midStr = PlayerSaveData.GetString(LUA_MID_KEY, null);
                if (midStr != null && Enum.TryParse(midStr, out emActivityFactor bf)) {
                    Debug.Log("使用了lua设置的中性因子：" + midStr);
                    return bf;
                }
            }

            // 70%概率触发
            if (RGGameSceneManager.Inst.rg_random.Range(0, 100) > 70) {
                return emActivityFactor.None;
            }

            var total = emActivityFactor.MidEnd - emActivityFactor.MidBegin - 1;
            var randomIdx = UnityEngine.Random.Range(0, total);
            var factor = emActivityFactor.MidBegin + randomIdx + 1;
            if (factor == emActivityFactor.RandomSkin) {
                // 过滤掉随机皮肤
                return GetRandomMidFactor();
            }

            return factor;
        }

        /// <summary>
        /// 获取良性因子列表
        /// </summary>
        /// <returns></returns>
        public static List<BugFactor> GetGoodFactors() {
            var currGoodFactor = GetCurrentGoodFactor();
            List<BugFactor> features = new List<BugFactor>();
            for (var f = emActivityFactor.GoodBegin + 1; f < emActivityFactor.GoodEnd; f++) {
                var item = new BugFactor() {
                    name = GetBugFactorName(f),
                    desc = GetBugFactorDesc(f),
                    sprite = GetBugFactorSprite(f),
                    available = f == currGoodFactor,
                };
                features.Add(item);
            }

            return features;
        }

        /// <summary>
        /// 获取恶性因子列表
        /// </summary>
        /// <returns></returns>
        public static List<BugFactor> GetBadFactors() {
            List<BugFactor> features = new List<BugFactor>();
            for (var f = emActivityFactor.BadBegin + 1; f < emActivityFactor.BadEnd; f++) {
                var item = new BugFactor() {
                    name = GetBugFactorName(f),
                    desc = GetBugFactorDesc(f),
                    sprite = GetBugFactorSprite(f),
                    available = false,
                };
                features.Add(item);
            }

            return features;
        }

        public static string GetBugFactorName(emActivityFactor factor) {
            return ScriptLocalization.Get($"activity/bugfeatures/name_{factor.ToString()}", "#fallback因子名称");
        }

        public static string GetBugFactorDesc(emActivityFactor factor) {
            return ScriptLocalization.Get($"activity/bugfeatures/desc_{factor.ToString()}", "#fallback因子描述");
        }

        public static Sprite GetBugFactorSprite(emActivityFactor factor) {
            return SpriteMap.activityFactorSprite.GetSprite(factor.ToString());
        }

        /// <summary>
        /// 雕像艺术家每1.5层生成一个雕像房间
        /// </summary>
        /// <returns></returns>
        public static GameObject GenStatueRoom() {
            var statueRoomPath = "RGPrefab/Room/EventRoom/r_statue_artist.prefab";
            var specialRoom = ResourcesUtil.Load(statueRoomPath) as GameObject;
            return specialRoom;
        }

        public static bool ShouldGenStatueRoom() {
            if (!BattleData.data.CompareActivityFactor(emActivityFactor.StatueArtist)) {
                return false;
            }

            if (BattleData.data.marks == null) {
                return false;
            }

            var lastCheckLevelIdxKey = emActivityFactor.StatueArtist.ToString() + "_levelIdx";
            if (BattleData.data.marks.TryGetValue(lastCheckLevelIdxKey, out var checkLevelIdx) && checkLevelIdx == BattleData.data.levelIndex) {
                return false;
            }

            BattleData.data.SetMark(lastCheckLevelIdxKey, BattleData.data.levelIndex);

            var probabilityKey = emActivityFactor.StatueArtist.ToString() + "_probability";
            if (!BattleData.data.marks.TryGetValue(probabilityKey, out var probability)) {
                probability = 50; // 期望每两层生成1个雕像房间
            }

            var ret = false;
            var rgRandom = new RGRandom(RGGameInfo.Inst.MapRandomSeed);
            if (rgRandom.Range(0, 100) < probability) {
                if (probability == 50) {
                    probability = 0;
                } else {
                    probability = 50;
                }

                BattleData.data.SetMark(probabilityKey, probability);
                ret = true;
            } else {
                if (BattleData.data.levelIndex % 2 == 0) {
                    probability = 100;
                } else {
                    probability = 50;
                }
            }

            BattleData.data.SetMark(probabilityKey, probability);
            return ret;
        }
        
        /// <summary>
        /// 传送门房间放置npc商人
        /// </summary>
        /// <param name="e"></param>
        private static GenTransferGateEvent _gateEvent;
        private static GenTransferGateEvent _gateEventBoss;
        private static void OnGateCreated(GenTransferGateEvent e) {
            _gateEvent = null;
            _gateEventBoss = null;
            var parent = e.gate.parent;
            if (parent == null) return;
            
            var roomX = parent.GetComponentInParent<RGRoomX>();
            if (roomX.createType == MapManagerLevel.RoomCreateType.EndRoom) {
                if (roomX.hasBoss) {
                    // 或boss房等到进度完成再创建
                    _gateEventBoss = e;
                } else {
                    // 跳关情况下，需要等待角色创建后
                    _gateEvent = e;
                }
            }
        }

        /// <summary>
        /// 非boss传送门延迟一会创建，避免角色还没创建出来
        /// </summary>
        /// <param name="e"></param>
        /// <returns></returns>
        private static void CreateShop(GenTransferGateEvent e) {
            // var theLevel = BattleData.data.levelIndex + 1;
            // if (!CreateShopLevels.Contains(theLevel)) return;
            
            if (e.gate == null) return;
            var parent = e.gate.parent;
            if (parent.Find("npc_coder") != null) return;

            var roomX = parent.GetComponentInParent<RGRoomX>();
            var genPos = roomX.Grid.CenterPos;
            if (e.gate.transform.position.y <= roomX.Grid.CenterPos.y + 1f) {
                //传送门在中心点下方
                genPos.y = e.gate.transform.position.y + 4;
            } else {
                genPos.y = e.gate.transform.position.y - 3;
            }

            if (parent.GetComponent<ExtraditionNpc>() != null) {
                //过滤超界者升生成的传送门
                return;
            }

            if (parent.GetComponentInChildren<ExtraditionNpc>() != null) {
                // boss关卡扩展关卡传送门出现，忽略
                genPos.y = roomX.Grid.CenterPos.y - 4;
            }

            if (roomX.hasBoss) {
                genPos.x += 2f;
                GameUtil.DisplayFx("RGPrefab/Register/effect_show_up", genPos);
            }

            RGRoomX.HideRoomObstacleLevel(roomX);

            var shop = GameObject.Instantiate(ResourcesUtil.Load<GameObject>(CODER_PATH), parent);
            shop.name = "npc_coder";
            shop.transform.position = genPos;
        }
        
        public static void ShowGuideView() {
            ActivityUtil.ShowGuideView(GUIDE_VIEW, RGGameConst.OneOffEvent.ReadBugFeatureGuide.ToString());
        }

        #region 特性生效逻辑
        
        private static void OnBeforeMultiGameStart(BeforeMultiGameStartEvent e) {
            if (CanOpenFeature()) {
                OpenFeature(false);
            }
        }

        private static void OnEnterMode(EnterModeEvent e) {
            if (CanOpenFeature()) {
                OpenFeature(false);
            }
        }
        
        /// <summary>
        /// 继续游戏
        /// </summary>
        /// <param name="e"></param>
        private static void OnContinueGame(EnterGameEvent e) {
            if (CanOpenFeature()) {
                OpenFeature(true);
            }
        }
        
        private static bool CanOpenFeature() {
            var isNormalGame = BattleData.data.gameMode == emGameMode.Normal 
                               && BattleData.data.season == Season.None 
                               && !BattleData.data.CompareFactor(emBattleFactor.Loop);

            if (!isNormalGame || !IsActivityOn()) {
                BattleData.data.ClearActivity(TAG);
                BattleData.data.ClearActivityFactors();
                UnCastFeatures();
                return false;
            }

            return true;
        }
        
        public static bool IsActivityOn() {
            return BattleData.data.HasActivityEnabled(TAG, true);
        }

        public static void OpenFeature(bool continueGame = false) {
            _gotCoin = 0;
            _pets.Clear();
            
            if ((GameUtil.IsSingleGame() || NetControllerManager.Inst.isServer) && !continueGame) {
                // 从机已同步BattleData的设置
                EnableGoodFeature(true);
                EnableRandomOtherFeature(true);
            }
            CastFeatures();
        }


        private static void OnEnterSceneEvent(EnterSceneEvent e) {
            if (e.enterScene != emScene.MultiRoom &&
                e.enterScene != emScene.Game &&
                e.enterScene != emScene.Loading) {
                UnCastFeatures();
                _pets.Clear();
            }

            // 进入地牢就可以把开关关了，后面就依赖于battledata
            if (e.enterScene == emScene.Game) {
                if (!IsAvailable()) {
                    // 已过期则清除因子
                    UnCastFeatures();
                    _pets.Clear();
                    BattleData.data.ClearActivityFactors();
                    BattleData.data.ClearActivity(TAG);
                    SimpleEventManager.Raise(new UpdateFactorBarEvent());
                }
            }
        }

        private static void UnCastFeatures() {
            SimpleEventManager.RemoveListener<CreateBulletEvent>(OnBulletCreated);
            SimpleEventManager.RemoveListener<CharacterCreateEvent>(OnCharacterCreateEvent);
            SimpleEventManager.RemoveListener<EnemyCreateEvent>(OnEnemyCreateEvent);
            SimpleEventManager.RemoveListener<ExitDungeonRoomEvent>(OnLeaveDungeonRoom);
            SimpleEventManager.RemoveListener<EnterDungeonRoomEvent>(OnEnterDungeonRoom);
            SimpleEventManager.RemoveListener<CharacterRebornEvent>(OnPlayerResurrect);
            SimpleEventManager.RemoveListener<CreatePetCompleteEvent>(OnCreatePetCompleteEvent);
            SimpleEventManager.RemoveListener<PlayerBulletHitEnemyEvent>(OnPlayerBulletHitEnemy);
            SimpleEventManager.RemoveListener<WeaponGenEvent>(OnWeaponGen);
            SimpleEventManager.RemoveListener<ClearRoomEvent>(OnClearRoom);
            SimpleEventManager.RemoveListener<GenTransferGateEvent>(OnGateCreated);
            SimpleEventManager.RemoveListener<AfterCharacterSetupEvent>(OnPlayerSetUpped);
            SimpleEventManager.RemoveListener<PlayerSetWeaponFrontEvent>(OnPlayerSetWeaponFront);
            SimpleEventManager.RemoveListener<GetCommonMessageEvent>(OnGetCommonMessage);
            SimpleEventManager.RemoveListener<FinishUpgradeBadChipEvent>(OnFinishUpgradeBadChip);
        }

        private static void CastFeatures() {
            UnCastFeatures();
            SimpleEventManager.AddEventListener<EnterDungeonRoomEvent>(OnEnterDungeonRoom);
            SimpleEventManager.AddEventListener<ExitDungeonRoomEvent>(OnLeaveDungeonRoom);
            SimpleEventManager.AddEventListener<CharacterRebornEvent>(OnPlayerResurrect);
            SimpleEventManager.AddEventListener<CreatePetCompleteEvent>(OnCreatePetCompleteEvent);
            SimpleEventManager.AddEventListener<WeaponGenEvent>(OnWeaponGen);
            SimpleEventManager.AddEventListener<ClearRoomEvent>(OnClearRoom);
            SimpleEventManager.AddEventListener<GenTransferGateEvent>(OnGateCreated);
            SimpleEventManager.AddEventListener<AfterCharacterSetupEvent>(OnPlayerSetUpped);
            SimpleEventManager.AddEventListener<PlayerSetWeaponFrontEvent>(OnPlayerSetWeaponFront);
            SimpleEventManager.AddEventListener<GetCommonMessageEvent>(OnGetCommonMessage);
            SimpleEventManager.AddEventListener<FinishUpgradeBadChipEvent>(OnFinishUpgradeBadChip);

            if (BattleData.data.CompareActivityFactor(emActivityFactor.FloatingGunman) ||
                BattleData.data.CompareActivityFactor(emActivityFactor.PinballShooter) ||
                BattleData.data.CompareActivityFactor(emActivityFactor.WeaponDestroyer) ||
                BattleData.data.CompareActivityFactor(emActivityFactor.ScaleMagic) ||
                BattleData.data.CompareActivityFactor(emActivityFactor.GhostShooter) ||
                BattleData.data.CompareActivityFactor(emActivityFactor.BulletHell)) {
                SimpleEventManager.AddEventListener<CreateBulletEvent>(OnBulletCreated);
            }

            if (BattleData.data.CompareActivityFactor(emActivityFactor.PhantomMaster) ||
                BattleData.data.CompareActivityFactor(emActivityFactor.MJ) ||
                BattleData.data.CompareActivityFactor(emActivityFactor.RandomSkin)) {
                SimpleEventManager.AddEventListener<CharacterCreateEvent>(OnCharacterCreateEvent);
            }

            if (BattleData.data.CompareActivityFactor(emActivityFactor.FlashDevil) ||
                BattleData.data.CompareActivityFactor(emActivityFactor.RandomScale)) {
                SimpleEventManager.AddEventListener<EnemyCreateEvent>(OnEnemyCreateEvent);
            }

            if (BattleData.data.CompareActivityFactor(emActivityFactor.SubmachineGun)) {
                SimpleEventManager.AddEventListener<PlayerBulletHitEnemyEvent>(OnPlayerBulletHitEnemy);
            }
        }

        private static void OnFinishUpgradeBadChip(FinishUpgradeBadChipEvent e) {
            if (_gateEventBoss != null) {
                CreateShop(_gateEventBoss);
                _gateEventBoss = null;
            }
        }

        private static void OnGetCommonMessage(GetCommonMessageEvent e) {
            if (e.message.type == CommonMessageType.BadChipConvert2Good) {
                var netId = e.message.intArray[0];
                var master = NetControllerManager.Inst.GetNetCtrlByNetId((uint)netId);
                
                if (RGGameSceneManager.Inst.controller == master.controller) {
                    LogUtil.Log($"SyncUpgrade netId:{netId} is local player, ignore!!!");
                    return;
                }
                
                var wAffix = master.controller.hand.front_weapon.GetWeaponAffixList()[0];
                var goodAffixId = wAffix.Id + 1;
                var goodAffix = WeaponAffixFactory.Create(goodAffixId);
                master.controller.hand.front_weapon.RemoveWeaponAffix(wAffix);
                master.controller.hand.front_weapon.AddWeaponAffix(goodAffix);
            }
        }

        private static void OnPlayerSetWeaponFront(PlayerSetWeaponFrontEvent e) {
            if (e.frontWeapon.owner != RGGameSceneManager.Inst.controller) {
                return;
            }

            if (e.frontWeapon.transform.parent.gameObject.name != "h1") {
                // 解决骑士双持等技能有切换武器（副手武器没有芯片），导致芯片icon隐藏的问题
                return;
            }
            
            var wAffixes = e.frontWeapon.GetWeaponAffixList();
            if (wAffixes.Count == 0) {
                UICanvas.Inst.weapon_item_icon.ShowActivityWeaponAffix(false);
                return;
            } 
            
            var wAffix = wAffixes[0];
            var wAffixIcon = DataMgr.ActivityBugFeatureData.GetAffixSprite(wAffix.Id);
            if (wAffixIcon != null) {
                UICanvas.Inst.weapon_item_icon.ShowActivityWeaponAffix(true, wAffixIcon, wAffix.Id);
            } else {
                UICanvas.Inst.weapon_item_icon.ShowActivityWeaponAffix(false);
            }
        }

        private static void OnPlayerSetUpped(AfterCharacterSetupEvent e) {
            if (!e.controller.IsLocalPlayer()) {
                return;
            }

            if (_gateEvent != null) {
                CreateShop(_gateEvent);
                _gateEvent = null;
            }
        }

        private static void OnClearRoom(ClearRoomEvent e) {
            RGGameSceneManager.Inst.StartCoroutine(DelayShowUpgradeView(e.room));
        }

        private static IEnumerator DelayShowUpgradeView(RGRoomX room) {
            yield return new WaitForSeconds(1f);
            DataMgr.ActivityBugFeatureData.ShowWeaponUpgradeView(room);
        }

        private static void OnWeaponGen(WeaponGenEvent e) {
            DataMgr.ActivityBugFeatureData.ModifyWeaponOnGen(e.Weapon);
        }

        private static void OnCreatePetCompleteEvent(CreatePetCompleteEvent e) {
            if (BattleData.data.CompareActivityFactor(emActivityFactor.PetEgg)) {
                var myPet = RGGameSceneManager.Inst.partner;
                if (myPet && myPet.GetComponent<Pet36Controller>() != null) {
                    return;
                }

                InitPets();
                _genEggDic.Clear();
            }
        }

        private static void InitPets() {
            var eggObj = GameObject.Instantiate(ResourcesUtil.Load<GameObject>(EGG_PATH), RGGameSceneManager.Inst.temp_objects_parent);
            var eggCtrl = eggObj.GetComponent<EggController>();
            var footerSprite = eggCtrl.footerEffect;
            var eggColorDic = eggCtrl.eggColorDic;
            var eggBuffs = eggCtrl.eggBuffs;
            eggObj.gameObject.SetActive(false);
            GameObject.Destroy(eggObj);

            // 加入携带的宠物
            _pets.Clear();
            if (RGGameSceneManager.Inst.partner != null && RGGameSceneManager.Inst.partner.GetComponent<RGPetWithSkillController>() is { } rgPetWithSkillController) {
                _pets.Add(rgPetWithSkillController);
            }

            // 加入缓存的复制宠物
            int totalCount = 0;
            Dictionary<EggType, int> countDic = new Dictionary<EggType, int>();
            for (var eType = EggType.None + 1; eType < EggType.Count; eType++) {
                var eCount = BattleData.data.GetMark(eType.ToString());
                totalCount += eCount;
                if (eCount > 0) {
                    countDic.Add(eType, eCount);
                }

                for (var i = 0; i < eCount; i++) {
                    var clonePetCtrl = GenClonePet(eType, footerSprite, eggColorDic[eType], eggBuffs[(int)eType]);
                    _pets.Add(clonePetCtrl);
                    if (_pets.Count >= GetEggMaxCount()) {
                        break;
                    }

                    // 加入buff
                    foreach (var pet in _pets) {
                        if (pet != null && !pet.dead) {
                            GameObject.Instantiate(eggBuffs[(int)eType], pet.transform);
                        }
                    }
                }

                if (_pets.Count >= GetEggMaxCount()) {
                    break;
                }
            }
        }

        public static RGPetWithSkillController GenClonePet(EggType eType, Sprite footerSprite, Color color, GameObject buff) {
            var pet = RGGameSceneManager.Inst.partner;
            var genPoint = pet.transform.position + new Vector3(Random.Range(-2f, 2f), Random.Range(-1, 1f));
            PrefabPool.Inst.Take(PrefabManager.GetPrefab(PrefabName.effect_show_up), genPoint, Quaternion.identity);
            var clonePet = GameObject.Instantiate(pet, RGGameSceneManager.Inst.temp_objects_parent);
            clonePet.name = "pet_clone_" + _pets.Count;
            clonePet.transform.position = genPoint;
            var clonePetCtrl = clonePet.GetComponent<RGPetWithSkillController>();
            var shadowSR = clonePet.transform.Find("img/shadow").GetComponent<SpriteRenderer>();
            clonePetCtrl.Reborn();
            shadowSR.sprite = footerSprite;
            shadowSR.color = color;
            if (RGGameSceneManager.Inst.controller != null) {
                clonePetCtrl.SetMaster(RGGameSceneManager.Inst.controller.transform);
            }

            return clonePetCtrl;
        }

        private static void OnAfterEnterStatement(FirstClickAtGameStatement e) {
            // 移除上次游戏的监听事件
            UnCastFeatures();
            if (!IsActivityOn()) {
                return;
            }
            
            AddOutsideCoin();
        }
        
        /// <summary>
        /// 局外货币添加
        /// </summary>
        private static void AddOutsideCoin() {
            if (_gotCoin > 0) {
                Debug.LogError("duplicate addCoin!!! ignore..");
                return;
            }

            var level = BattleData.data.levelIndex;
            float addCoin = 0;
            for (var i = 1; i <= level; i++) {
                if (i <= 5) {
                    addCoin += 15;
                } else if (i <= 10) {
                    addCoin += 18;
                } else if (i <= 15) {
                    addCoin += 23;
                } else if (i <= 20) {
                    addCoin += 29;
                } else {
                    addCoin += 36;
                }
            }

            var playTime = StatisticData.data.GetToDayActivityPlayCount(MatName);
            if (addCoin > 0) {
                if (playTime < 3) {
                    addCoin *= 2;
                    StatisticData.data.AddToDayActivityPlayCount(MatName);
                    BattleData.data.SetMark(RGGameConst.TodayActivityDouble, 1);
                }
            }

            _gotCoin = Mathf.Min(MAX_COIN_ONE_GAME, Mathf.CeilToInt(addCoin));
            if (_gotCoin > 0) {
                PickableInfo info = new PickableInfo() {
                    count = _gotCoin,
                    name = MatName,
                    itemType = emItemType.Material,
                    multiple = playTime < 3 ? 2 : 1,
                };

                BattleData.data.AddPickables(new List<PickableInfo>() { info });
            }
        }

        // 瞬移狂魔
        private static void OnEnemyCreateEvent(EnemyCreateEvent e) {
            if (BattleData.data.CompareActivityFactor(emActivityFactor.FlashDevil)) {
                var featureObj = GenFeature(emActivityFactor.FlashDevil);
                if (featureObj != null) {
                    featureObj.transform.SetParent(e.enemy.transform);
                }
            }

            if (BattleData.data.CompareActivityFactor(emActivityFactor.RandomScale)) {
                var rgeCtrl = e.enemy.GetComponent<RGEController>();
                var addScale = Random.Range(0, 100) >= 50;
                float sizeRatio = 1;
                if (addScale) {
                    rgeCtrl.attribute.max_hp = (int)(rgeCtrl.attribute.max_hp * (1 + Random.Range(50, 100) * 0.01f));
                    rgeCtrl.attribute.hp = rgeCtrl.attribute.max_hp;
                    sizeRatio += Random.Range(50, 100) * 0.01f;
                } else {
                    sizeRatio -= Random.Range(20, 70) * 0.01f;
                    rgeCtrl.attribute.SpeedRate += Random.Range(0.5f, 1);
                }

                e.enemy.transform.localScale *= sizeRatio;
            }
        }

        private static void OnPlayerResurrect(CharacterRebornEvent e) {
            GenPhantomMasterBuff(e.controller.transform);
        }

        public static void GenPhantomMasterBuff(Transform parent) {
            if (BattleData.data.CompareActivityFactor(emActivityFactor.PhantomMaster)) {
                // 分身大师
                var featureObj = GenFeature(emActivityFactor.PhantomMaster);
                if (featureObj != null) {
                    featureObj.transform.SetParent(parent);
                }
            }
        }

        private static void OnCharacterCreateEvent(CharacterCreateEvent e) {
            GenPhantomMasterBuff(e.character.transform);

            if (BattleData.data.CompareActivityFactor(emActivityFactor.MJ)) {
                var localScale = Vector3.one;
                localScale.x = -1;
                var body = e.character.transform.Find("img/body");
                body.localScale = Vector3.Scale(body.localScale, localScale);
            }
        }

        private static void OnBulletCreated(CreateBulletEvent e) {
            if (e.bullet.gameObject.name.StartsWith("bullet_plane")) {
                return;
            }

            if (BattleData.data.CompareActivityFactor(emActivityFactor.FloatingGunman) && e.bullet.camp == 1) {
                // 良性：悬浮枪手
                DealFloatingGunman(e.bullet.transform);
            }

            if (BattleData.data.CompareActivityFactor(emActivityFactor.PinballShooter) && e.bullet.camp == 1) {
                // 弹珠射手
                DealPinballShooter(e.bullet);
            }

            if (BattleData.data.CompareActivityFactor(emActivityFactor.BulletHell) && e.bullet.camp == 0) {
                // 恶性：弹幕狂人
                DealBulletHell(e.bullet);
            }

            if (BattleData.data.CompareActivityFactor(emActivityFactor.ScaleMagic) && e.bullet.camp == 0) {
                // 恶性：缩放魔术师
                DealBulletScale(e.bullet);
            }

            if (BattleData.data.CompareActivityFactor(emActivityFactor.GhostShooter) && e.bullet.camp == 0) {
                // 恶性：幽灵枪手
                DealBulletIgnoreObstacles(e.bullet);
            }

            if (BattleData.data.CompareActivityFactor(emActivityFactor.WeaponDestroyer) && e.bullet.camp == 1) {
                DealWeaponDestroyer(e.bullet);
            }
        }

        private static void DealWeaponDestroyer(DamageCarrier damageCarrier) {
            var isBulletTypeOk = damageCarrier is RGBullet;
            if (!isBulletTypeOk) return;

            if (RGGameSceneManager.Inst.rg_random.Range(0, 100) < 50) {
                (damageCarrier as RGBullet).ChangeSpeed(-0.3f, 7);
            } else {
                var extraDeviation = 15;
                if ((damageCarrier as RGBullet).rigid2d.velocity.x < 0) {
                    damageCarrier.transform.eulerAngles = new Vector3(0, 0, 180 - damageCarrier.transform.eulerAngles.z + extraDeviation);
                } else {
                    damageCarrier.transform.eulerAngles = new Vector3(0, 0, damageCarrier.transform.eulerAngles.z + extraDeviation);
                }
            }
        }

        private static void DealFloatingGunman(Transform bulletTF) {
            var damageCarrier = bulletTF.GetComponent<DamageCarrier>();
            var isBulletTypeOk = damageCarrier is RGBullet;
            if (isBulletTypeOk) {
                var featureObj = GenFeature(emActivityFactor.FloatingGunman);
                if (featureObj != null) {
                    featureObj.transform.SetParent(bulletTF);
                }

                damageCarrier.damageInfo.damageType &= ~emDamageType.IgnoreBox;
                damageCarrier.damageInfo.damageType &= ~emDamageType.IgnoreWall;
            }
        }

        /// <summary>
        /// 忽略障碍物
        /// </summary>
        /// <param name="damageCarrier"></param>
        private static void DealBulletIgnoreObstacles(DamageCarrier damageCarrier) {
            if (damageCarrier is RGBullet) {
                damageCarrier.damageInfo.damageType |= emDamageType.IgnoreBox;
                damageCarrier.damageInfo.damageType |= emDamageType.IgnoreWall;
            }
        }

        private static void DealBulletScale(DamageCarrier damageCarrier) {
            if (damageCarrier is RGBullet) {
                var rnd = RGGameSceneManager.Inst.rg_random.Range(-35, 35);
                // 子弹缩小，速度增加
                // 子弹放大，速度减慢
                var sizeRate = 1 + rnd * 0.01f;
                var speedRate = -rnd * 0.01f;
                (damageCarrier as RGBullet).ChangeSpeed(speedRate, 10);
                if (damageCarrier.transform.Find("b") is { } b) {
                    b.localScale *= sizeRate;
                }
            }
        }

        private static void DealBulletHell(DamageCarrier bullet) {
            AddRebound(bullet, false, false);
        }

        private static int emitBulletTime = 1; // 发射次数
        private static int childBulletCount = 1; // 每次发射个数
        private static BulletInfo backWeaponBulletInfo;
        private static DamageInfo backWeaponDamageInfo;
        private static float lastLaserHitTime = 0;

        private static void OnPlayerBulletHitEnemy(PlayerBulletHitEnemyEvent e) {
            var src = e.sourceObj;
            if (e.damageCarrier != null && src != null && src.GetComponent<RGController>() != null && e.enemy != null) {
                DoBulletHitCallback(e.damageCarrier, e.enemy);
            }
        }

        // 母枪子弹击中回调
        private static void DoBulletHitCallback(DamageCarrier damageCarrier, RGEController enemyCtrl) {
            emWeaponType wType = emWeaponType.Default;
            RGWeapon backWeapon = null;
            if (damageCarrier == null) return;
            if (damageCarrier.hasSplitBullet) return;
            if (damageCarrier.fingerPrint is { isFromEffect: true }) return;
            if ((damageCarrier.damageType & emDamageType.HandCut) != 0) return;
            if (damageCarrier.bulletInfo.sourceWeapon == null) return;
            if (damageCarrier.bulletInfo.sourceWeapon.gameObject.GetComponentInParent<RGController>() == null) {
                // 非英雄角色
                return;
            }

            var sourceObject = damageCarrier.GetSourceObject();
            if (sourceObject != null && sourceObject.GetComponent<RGController>() is { } rgController) {
                wType = rgController.hand.front_weapon.weapon_type;
                backWeapon = rgController.GetBackWeapon();

                if (wType == emWeaponType.Laser) {
                    // 长按激光武器，限制1s只触发一次
                    if (lastLaserHitTime <= 0) {
                        lastLaserHitTime = Time.time;
                    }

                    if (Time.time - lastLaserHitTime < 1f) {
                        return;
                    }
                }
            }

            if (backWeapon == null) {
                return;
            }

            // 部分不发射子弹的武器，忽略
            if (backWeapon is GunPot ||
                backWeapon is GunRebornCross ||
                backWeapon is GunDeadNote ||
                backWeapon is GunDeploySummon ||
                backWeapon is GunLottery ||
                backWeapon is GunPetHope ||
                backWeapon is GunTrump ||
                backWeapon is GunBox ||
                backWeapon is WeaponBoss26 ||
                backWeapon is GunOnePunch ||
                backWeapon is GunChannel ||
                backWeapon is GunShield ||
                backWeapon is GunGourdWithFunnelSword ||
                backWeapon is GunMultiBullet02 ||
                backWeapon is MythicWeapon08 ||
                backWeapon is GunDrill ||
                backWeapon is GunStoneMobile ||
                backWeapon is GunBoom) {
                return;
            }

            if (backWeapon.bulletsInfo.Count == 0) {
                Debug.LogWarning("ignore bulletsInfo.count == 0 of " + backWeapon.GetItemName());
                return;
            }

            backWeaponBulletInfo = backWeapon.GetBulletInfo(0);
            backWeaponDamageInfo = backWeapon.GetDamageInfo(0);

            if (backWeaponBulletInfo.bulletProto == null) {
                Debug.LogWarning("ignore bulletProto is null of " + backWeaponBulletInfo.sourceWeapon.GetItemName());
                return;
            }

            _montherBulletPos = enemyCtrl.transform.position;
            switch (wType) {
                case emWeaponType.Rocket:
                case emWeaponType.ShotGun:
                case emWeaponType.Sword:
                    // 霰弹枪 + 近战 + 火箭
                    emitBulletTime = 1;
                    childBulletCount = 1;
                    break;
                case emWeaponType.Rifle:
                case emWeaponType.Gun:
                    // 步枪类
                    emitBulletTime = 2;
                    childBulletCount = 1;
                    break;
                case emWeaponType.Pistol:
                case emWeaponType.Throw:
                case emWeaponType.Spear:
                case emWeaponType.Bow:
                case emWeaponType.Axe:
                    // 轨道炮 + 手枪类 + 投掷 + 弓弩
                    emitBulletTime = 1;
                    childBulletCount = 1;
                    break;
                case emWeaponType.Laser:
                case emWeaponType.ShortLaser:
                case emWeaponType.Staff:
                case emWeaponType.Mythic:
                default:
                    // 法杖 + 神器 + 激光
                    emitBulletTime = 1;
                    childBulletCount = 2;
                    lastLaserHitTime = 0;
                    break;
            }

            damageCarrier.hasSplitBullet = true;
            EmitChildBullets(backWeapon, enemyCtrl.gameObject);
        }

        private static void EmitChildBullets(RGWeapon backWeapon, GameObject targetObj) {
            RGGameSceneManager.Inst.StartCoroutine(GenChildBullets(backWeapon, targetObj));

            // 一次性武器如大宝贝作为副武器，打一次后销毁掉
            if (backWeapon is GunNuclear) {
                (backWeapon.controller as RGController)?.DropWeaponOnBack(backWeapon);
                GameObject.Destroy(backWeapon.gameObject);
            }
        }

        private static GameObject _SubmachinegunSwordShellPrefab;

        private static Vector3 _montherBulletPos;

        // 按批次间隔生成子弹
        private static IEnumerator GenChildBullets(RGWeapon backWeapon, GameObject targetObj) {
            // 叠加关卡加成
            var bigLevel = BattleData.data.levelIndex / 5;
            var tmpChildBulletCount = childBulletCount + bigLevel;
            var tmpEmitBulletTime = emitBulletTime;
            if (bigLevel >= 3) {
                // 通过第三大关的时候，子弹数不加1
                tmpChildBulletCount--;
            }

            if (bigLevel >= 2) {
                tmpEmitBulletTime += 1;
            }


            float childBulletScale = 0;
            int addThroughCount = 0;

            // 根据背部副枪类型，调整部分数值
            switch (backWeapon.weapon_type) {
                case emWeaponType.Laser:
                case emWeaponType.ShortLaser:
                    // 激光
                    childBulletScale = 0.67f * tmpChildBulletCount;
                    tmpChildBulletCount = 1;
                    tmpEmitBulletTime = 1;
                    break;
                case emWeaponType.Sword:
                    // 近战
                    childBulletScale = 0.5f * tmpChildBulletCount;
                    tmpChildBulletCount = 1;
                    tmpEmitBulletTime = 1;
                    break;
                case emWeaponType.Throw:
                case emWeaponType.Spear:
                case emWeaponType.Bow:
                case emWeaponType.Pistol:
                case emWeaponType.Axe:
                    // 投掷
                    addThroughCount = 1;
                    break;
                case emWeaponType.Rocket:
                case emWeaponType.Rifle:
                case emWeaponType.Gun:
                case emWeaponType.ShotGun:
                    // 火箭 + 霰弹枪
                    // 步枪类
                    break;
                case emWeaponType.Staff:
                case emWeaponType.Mythic:
                default:
                    // 法杖 + 神器 + 杂项
                    childBulletScale = 0.15f * tmpChildBulletCount;
                    tmpChildBulletCount = 1;
                    tmpEmitBulletTime = 1;
                    break;
            }

            // 限制部分霰弹枪子弹数量
            if (backWeapon.name.Contains("weapon_054") ||
                backWeapon.name.Contains("weapon_140") ||
                backWeapon.name.Contains("weapon_126") ||
                backWeapon.name.Contains("weapon_006")
               ) {
                tmpEmitBulletTime = 1;
                tmpChildBulletCount = 1;
            }

            var throughCount = backWeaponDamageInfo.throughCount;
            var angle = 360 / tmpChildBulletCount;
            for (var i = 0; i < tmpEmitBulletTime; i++) {
                var initAngle = RGGameSceneManager.Inst.rg_random.Range(0, 360);
                // 每轮好一次蓝
                // 子枪耗蓝20%
                int totalConsume = 0;
                if (backWeapon is GunCoin) {
                    // 撒币枪 
                    var consumeCoin = ((GunCoin)backWeapon).DoConsume();
                    totalConsume = consumeCoin - Mathf.FloorToInt(consumeCoin * 0.8f);
                    if (totalConsume <= 0) {
                        totalConsume = 1;
                    }

                    if (RGGameProcess.Inst.coin_value < consumeCoin) {
                        // 背部武器能量消耗不足
                        yield break;
                    }
                } else if (backWeapon.realConsume > 0) {
                    var changeConsume = Mathf.FloorToInt(backWeapon.consume * 0.8f);
                    totalConsume = backWeapon.realConsume - changeConsume;
                    if (totalConsume <= 0) {
                        totalConsume = 1;
                    }

                    if (backWeapon.controller.attribute.energy < totalConsume) {
                        // 背部武器能量消耗不足
                        yield break;
                    }
                }

                backWeapon.consumeChange = totalConsume - backWeapon.realConsume;
                backWeapon.MakeConsume();
                backWeapon.consumeChange = 0;

                for (var j = 0; j < tmpChildBulletCount; j++) {
                    var fixedAngle = initAngle + j * angle + RGGameSceneManager.Inst.rg_random.Range(-15, 15);
                    var genPos = _montherBulletPos + Quaternion.Euler(0, 0, fixedAngle) * Vector3.up * 0.5f;
                    BulletInfo bInfo = new BulletInfo().SetUp(backWeaponBulletInfo.bulletProto, backWeaponBulletInfo.sourceObject, backWeaponBulletInfo.speed, genPos, fixedAngle, backWeaponBulletInfo.camp)
                        .SetBulletSize(backWeaponBulletInfo.size * (1 + childBulletScale))
                        .SetSourceWeapon(backWeaponBulletInfo.sourceWeapon);
                    DamageInfo dmgInfo = new DamageInfo()
                        .SetUp(backWeaponBulletInfo.bulletProto, backWeaponDamageInfo.damage, backWeaponDamageInfo.critic, backWeaponDamageInfo.repel, backWeaponDamageInfo.camp)
                        .SetThrough(throughCount + addThroughCount > 0)
                        .SetThroughCount(throughCount + addThroughCount);
                    dmgInfo = dmgInfo.SetDamageWithFactor(0.2f); // 伤害20%
                    bInfo.isFromEffect = true;
                    var cloneBullet = BulletFactory.TakeBullet(bInfo, dmgInfo);
                    cloneBullet.name = "bullet_clone_" + i + "_" + j;
                    if (cloneBullet.GetComponent<RGLaser>() is { } rgLaser) {
                        rgLaser.keepDuration = 1;
                        rgLaser.EndLaser();
                    }

                    if (cloneBullet.TryGetComponent<RGBullet>(out var bullet)) {
                        if (targetObj != null) {
                            bullet.bulletInfo.IgnoreTarget(targetObj);
                        }

                        if (!bullet.awake) {
                            bullet.SetAwakeTrue();
                        }
                    } else if (cloneBullet.TryGetComponent<RGSword>(out var sword)) {
                        if (_SubmachinegunSwordShellPrefab == null) {
                            _SubmachinegunSwordShellPrefab = new GameObject();
                            _SubmachinegunSwordShellPrefab.name = "submachinegun_sword_shell";
                            _SubmachinegunSwordShellPrefab.SetActive(false);
                            _SubmachinegunSwordShellPrefab.AddComponent<RGAutoDestory>().d_time = 2;
                        }

                        var shell = PrefabPool.Inst.Take(_SubmachinegunSwordShellPrefab);
                        shell.SetActive(true);
                        shell.GetComponent<RGAutoDestory>().OnTaken();
                        shell.transform.position = genPos;
                        sword.transform.SetParent(shell.transform);
                    }
                }

                yield return new WaitForSeconds(0.2f);
            }
        }

        private static void DealPinballShooter(DamageCarrier bullet) {
            var addReboundBullet = AddRebound(bullet, true, true);
            if (addReboundBullet != null) {
                var cancelEfx = GenCancelReboundEfx();
                if (cancelEfx != null) {
                    cancelEfx.transform.SetParent(addReboundBullet.Find("b"));
                }
            }
        }

        /// <summary>
        /// 给子弹添加无限反弹逻辑
        /// </summary>
        /// <param name="bullet"></param>
        /// <param name="customReboundEffect"></param>
        /// <param name="effectOnce"></param>
        /// <returns></returns>
        public static Transform AddRebound(DamageCarrier bullet, bool customReboundEffect, bool effectOnce) {
            var damageCarrier = bullet.gameObject.GetComponent<DamageCarrier>();
            var isBulletTypeOk = damageCarrier is RGBullet ||
                                 damageCarrier is RGLaser;
            if (!isBulletTypeOk) {
                return null;
            }

            var bTrigger = bullet.transform.Find("b");
            if (bTrigger == null || bTrigger.GetComponent<RGBulletTrigger>() == null) {
                return null;
            }

            if (damageCarrier.transform.GetComponentInChildren<ExplodeEffectTrigger>() != null) {
                // 过滤掉爆炸类子弹
                return null;
            }

            if (damageCarrier.transform.GetComponentInChildren<RGBulletTriggerDestroyClearRoom>() != null) {
                return null;
            }

            // 玩家的子弹，单机的时候无限反弹，联机的时候限制为4次
            var bulletSrc = bullet.bulletInfo.sourceObject;
            var isBossBullet = bulletSrc != null && bulletSrc.GetComponent<RGEBossController>() != null;
            int reboundCount = isBossBullet ? 1 : 15;
            if (bullet.camp == 1) {
                reboundCount = GameUtil.IsMultiGame() ? 4 : 9999;
            }

            var rgBullet = damageCarrier.gameObject.GetComponent<RGBullet>();
            if (rgBullet != null) {
                rgBullet.can_rebound = true;

                // 添加反弹
                ReboundEffectCustomTrigger rebound = bTrigger.GetComponentInChildren<ReboundEffectCustomTrigger>();
                if (rebound == null) {
                    var proto = ResourcesUtil.Load<GameObject>(CUSTOM_REBOUND_PATH);
                    var go = GameObject.Instantiate(proto, bTrigger, false);
                    rebound = go.GetComponent<ReboundEffectCustomTrigger>();
                    rebound.setAngle = false;
                    rebound.destoryInSpeed = false;
                }

                rebound.max_rebound_count = reboundCount;
                rebound.customReboundEffect = customReboundEffect;
                rebound.extraEffectOnce = effectOnce;

                // 添加销毁处理组件
                var noDestroyEfx = GenDonotDestroyEfx();
                if (noDestroyEfx != null) {
                    noDestroyEfx.transform.parent = rgBullet.transform.Find("b");
                    noDestroyEfx.GetComponent<RGBulletTriggerDestroyClearRoom>().OnTaken();
                }
            } else if (damageCarrier.gameObject.GetComponent<RGLaser>() is { } rgLaser) {
                rgLaser.isReflect = true;
            }

            bullet.bulletInfo.SetReflectCount(reboundCount);
            return damageCarrier.transform;
        }

        private static GameObject GenCancelReboundEfx() {
            try {
                var path = $"Activities/BugFeatures/Prefabs/Features/hit_enemy_cancel_rebound.prefab";
                var obj = GameObject.Instantiate(ResourcesUtil.Load<GameObject>(path));
                return obj;
            } catch (Exception e) {
                Debug.LogError(e);
            }

            return null;
        }

        private static GameObject GenDonotDestroyEfx() {
            try {
                var path = $"Activities/BugFeatures/Prefabs/Features/no_destroy.prefab";
                var obj = GameObject.Instantiate(ResourcesUtil.Load<GameObject>(path));
                return obj;
            } catch (Exception e) {
                Debug.LogError(e);
            }

            return null;
        }

        public static GameObject GenFeature(emActivityFactor factor) {
            try {
                var path = $"Activities/BugFeatures/Prefabs/Features/feature_{factor}.prefab";
                var obj = GameObject.Instantiate(ResourcesUtil.Load<GameObject>(path));
                return obj;
            } catch (Exception e) {
                Debug.LogError(e);
            }

            return null;
        }

        /// <summary>
        /// 生成偷懒胡椒
        /// </summary>
        public static void GenLazyCat(Vector3 pos, Transform parent) {
            try {
                var cat = GameObject.Instantiate(ResourcesUtil.Load<GameObject>(PET_IDLE_PATH), parent);
                cat.transform.position = pos;
            } catch (Exception e) {
                Debug.LogError(e);
            }
        }

        // 宠物蛋上限
        private const int Egg_Max_Count = 10;
        private static List<RGPetWithSkillController> _pets = new List<RGPetWithSkillController>();

        public static void AddClonePet(RGPetWithSkillController petCtrl, GameObject buff, EggType eType) {
            _pets.Add(petCtrl);
            foreach (var pet in _pets) {
                if (pet != null && !pet.dead) {
                    GameObject.Instantiate(buff, pet.transform);
                }
            }

            BattleData.data.AddMark(eType.ToString());
        }

        public static int GetEggMaxCount() {
            var initCount = 5;
            var currLevel = BattleData.data.levelIndex;
            var maxCount = initCount + currLevel;
            var total = Mathf.Min(maxCount, 15);
            var myPet = RGGameSceneManager.Inst.partner;
            if (myPet && myPet.GetComponent<Pet36Controller>() != null) {
                total = Mathf.Min(maxCount, 7);
            }

            return total;
        }

        // 控制每个房间掉落不超过2个egg
        private static Dictionary<string, int> _genEggDic = new Dictionary<string, int>();

        public static void MaybeGenPetEgg(RGController rgController, Transform enemyTF) {
            // 没有拿武器的，忽略
            if (rgController.hand.front_weapon == null) return;

            // 20%概率掉落
            var rate = 20;
            var myPet = RGGameSceneManager.Inst.partner;
            if (myPet && myPet.GetComponent<Pet36Controller>() != null) {
                // 限制九折宠物的概率，减少被刷
                // 直接不允许掉落
                return;
            }

            if (rgController.rg_random.Range(0, 100) > rate) {
                return;
            }

            // 每个房间上限2个以内
            var room = MapManagerLevel.Instance.GetClosestRoomToPlayer();
            var dicKey = $"level-{BattleData.data.levelIndex}-room-{room.name}";
            if (_genEggDic.TryGetValue(dicKey, out int eggCount) && eggCount >= 2) {
                if (LogUtil.IsShowLog) {
                    LogUtil.Log($"{room.name} 此房间宠物蛋已达上限，忽略", TAG);
                }

                return;
            }

            // 过滤掉已经死亡的得引用
            if (_pets.Count > 0) {
                for (var i = _pets.Count - 1; i >= 0; i--) {
                    var pet = _pets[i];
                    if (pet == null || (pet.dead && pet.gameObject.name.StartsWith("pet_clone"))) {
                        _pets.RemoveAt(i);
                    }
                }

                if (_pets.Count >= GetEggMaxCount()) {
                    if (LogUtil.IsShowLog) {
                        LogUtil.Log($"宠物蛋已达上限，忽略", TAG);
                    }

                    // 已达上限
                    return;
                }
            }

            if (_genEggDic.ContainsKey(dicKey)) {
                _genEggDic[dicKey]++;
            } else {
                _genEggDic.TryAdd(dicKey, 1);
            }

            GenPetEgg(rgController, enemyTF);
        }

        private static void GenPetEgg(RGController rgController, Transform enemyTF) {
            var eggType = EggType.None;
            var w = rgController.hand.front_weapon;
            switch (w.weapon_type) {
                case emWeaponType.Laser:
                case emWeaponType.ShortLaser:
                case emWeaponType.Throw:
                case emWeaponType.Spear:
                    // 激光 + 投掷
                    eggType = EggType.CdEgg;
                    break;
                case emWeaponType.Rocket:
                case emWeaponType.Sword:
                case emWeaponType.Axe:
                    // 火箭 + 近战
                    eggType = EggType.FireEgg;
                    break;
                case emWeaponType.Rifle:
                case emWeaponType.Gun:
                    // 步枪类
                    eggType = EggType.AgilityEgg;
                    break;
                case emWeaponType.Bow:
                case emWeaponType.ShotGun:
                    // 弓箭 + 霰弹枪
                    eggType = EggType.SharpEgg;
                    break;
                case emWeaponType.Staff:
                case emWeaponType.Mythic:
                    // 法杖 + 神器
                    eggType = EggType.MagicEgg;
                    break;
                case emWeaponType.Pistol:
                default:
                    // 手枪类 + 杂项
                    eggType = EggType.BigEgg;
                    break;
            }

            var animPath = $"Activities/BugFeatures/Animation/{eggType}.controller";
            var egg = GameObject.Instantiate(ResourcesUtil.Load<GameObject>(EGG_PATH), RGGameSceneManager.Inst.temp_objects_parent);
            egg.GetComponent<Animator>().runtimeAnimatorController = ResourcesUtil.Load(animPath) as RuntimeAnimatorController;
            egg.transform.position = enemyTF.position;
            egg.GetComponent<EggController>().SetUp(eggType);

            if (LogUtil.IsShowLog) {
                LogUtil.Log($"生成一个宠物蛋:{eggType}", TAG);
            }
        }

        /// <summary>
        /// 给个上限，控制浮游武器不消失的子弹上限
        /// </summary>
        private static Queue<RGBulletTriggerDestroyClearRoom> _floatingMasterBullets = new Queue<RGBulletTriggerDestroyClearRoom>();

        public static void AddFloatingBullet(RGBulletTriggerDestroyClearRoom effect) {
            _floatingMasterBullets.Enqueue(effect);
            if (_floatingMasterBullets.Count > 100) {
                var popEffect = _floatingMasterBullets.Dequeue();
                if (popEffect != null) {
                    popEffect.DoDestroy();
                }
            }
        }

        /// <summary>
        /// 冗余清理悬浮没有销毁的子弹
        /// </summary>
        /// <param name="e"></param>
        private static void OnEnterDungeonRoom(EnterDungeonRoomEvent e) {
            while (_floatingMasterBullets.Count > 0) {
                var popEffect = _floatingMasterBullets.Dequeue();
                if (popEffect != null) {
                    popEffect.DoDestroy();
                }
            }
        }

        private static void OnLeaveDungeonRoom(ExitDungeonRoomEvent e) {
            _floatingMasterBullets.Clear();
        }

        private static int _talentCount2 = 5; // lv2 5%概率
        private static int _talentCount3 = 2; // lv3 2%概率

        public static int GetCurrentTalentTime() {
            var total = 100;
            var levelIndex = BattleData.data.levelIndex;
            var passBigLevelCount = levelIndex / 5;
            var tmpTalentCount2 = _talentCount2 + passBigLevelCount * 2;
            var tmpTalentCount3 = _talentCount3 + passBigLevelCount * 1;
            if (levelIndex % 5 == 0) {
                // 打完boss的下一关
                tmpTalentCount2 = 80;
                tmpTalentCount3 = 20;
            }

            var rnd = Random.Range(0, total);
            if (rnd < tmpTalentCount2) return 2;
            if (rnd < tmpTalentCount2 + tmpTalentCount3) return 3;
            return 1;
        }

        public static List<int> GetUpgradeBuffIndexes() {
            List<int> intList = new List<int>();
            foreach (var pair in ActivityBugFeatureManager.TalentBuffs) {
                intList.Add((int)pair.Key);
            }

            return intList;
        }

        public static int GetBugFeatureExtraCount(emBuff buff) {
            int extraCount = 0;
            if (!BattleData.data.CompareActivityFactor(emActivityFactor.TalentMaster)) return extraCount;
            if (!TalentBuffs.ContainsKey(buff)) return extraCount;

            var buffTime = BattleData.data.GetNormalAndAdditionBuffTimes(buff);
            extraCount = buffTime - 1;
            return extraCount;
        }

        public static AudioClip GetMagicBGM(string name = "normal") {
            return ResourcesUtil.Load<AudioClip>($"Activities/BugFeatures/bug_{name}.mp3");
        }

        #endregion
        
        public static List<string> TrackGetAllGotChips() {
            List<string> result = new List<string>();
            return BattleData.data.activityExtras.GetValueOrDefault(TAG, result);
        }
    }
}