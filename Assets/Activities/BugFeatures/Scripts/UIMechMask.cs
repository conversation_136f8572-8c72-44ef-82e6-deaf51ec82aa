using DG.Tweening;
using UnityEngine;

namespace Activities.BugFeatures.Scripts {
    /// <summary>
    /// 电影主角遮罩
    /// </summary>
    public class UIMechMask : MonoBehaviour {
        private RectTransform _topRT;
        private RectTransform _bottomRT;
        private float _maskHeight;
        private float _duration = 0.5f;
        
        private void Awake() {
            _topRT = transform.Find("top").GetComponent<RectTransform>();
            _bottomRT = transform.Find("bottom").GetComponent<RectTransform>();
        }

        public void Show(float viewScale) {
            var rect = GetComponent<RectTransform>().rect;
            _maskHeight = rect.height * (1 - viewScale) / 2f;
            _topRT.sizeDelta = new Vector2(Screen.width, _maskHeight);
            _bottomRT.sizeDelta = new Vector2(Screen.width, _maskHeight);
            _topRT.anchoredPosition = new Vector2(0, _maskHeight / 2f);
            _bottomRT.anchoredPosition = new Vector2(0, -_maskHeight / 2f);
            
            _topRT.DOAnchorPosY(-_maskHeight / 2f, _duration).SetEase(Ease.InQuart);
            _bottomRT.DOAnchorPosY(_maskHeight / 2f, _duration).SetEase(Ease.InQuart);
        }
        
        public void Hide() {
            _topRT.DOAnchorPosY(_maskHeight / 2f, _duration / 2f).SetEase(Ease.InQuart);
            _bottomRT.DOAnchorPosY(-_maskHeight / 2f, _duration / 2f).SetEase(Ease.InQuart);
        }
    }
}