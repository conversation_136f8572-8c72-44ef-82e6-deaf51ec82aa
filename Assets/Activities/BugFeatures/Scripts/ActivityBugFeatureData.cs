using cfg.Activity;
using cfg.ActivityBugFeatureAffix;
using cfg.ActivityBugFeatureConst;
using cfg.BugActivityRewards;
using cfg.game;
using I2.Loc;
using RGScript.Data;
using RGScript.Manager.Message;
using RGScript.Util.MathTool;
using RGScript.WeaponAffix;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using UIFramework;
using UnityCommon.UI;
using UnityEngine.U2D;
using UnityEngine;
using Random = UnityEngine.Random;

namespace Activities.BugFeatures.Scripts {
    /// <summary>
    /// 2025 7月特性活动管理数据
    /// </summary>
    public class ActivityBugFeatureData : BaseData {
        public ActivityConfig Config => DataMgr.ActivityConfigData.GetActivityConfig(ActivityBugFeatureManager.TAG);
        public TbBugActivityRewards RewardConfig => DataMgr.ConfigData.Tables.TbBugActivityRewards;
        public TbActivityBugFeatureAffix AffixConfig => DataMgr.ConfigData.Tables.TbActivityBugFeatureAffix;
        public TbActivityBugFeatureConst ConstConfig => DataMgr.ConfigData.Tables.TbActivityBugFeatureConst;

        public static string WEAPON_UPGRADE_PATH = "Activities/BugFeatures/Prefabs/window_chip_progress.prefab";
        private List<int> _badAffixes = new List<int>();
        private List<int> _goodAffixes = new List<int>();
        private List<int> _availableBadAffixes = new List<int>(); // 当前生效的漏洞芯片
        public List<int> BadAffixes => _badAffixes;
        public List<int> AvailableBadAffixes => _availableBadAffixes;

        private static SpriteAtlas _atlas;

        public static SpriteAtlas Atlas {
            get {
                if (_atlas == null) {
                    _atlas = ResourcesUtil.Load<SpriteAtlas>("Activities/BugFeatures/Textures/atlas.spriteatlasv2");
                }

                return _atlas;
            }
        }

        public ActivityBugFeatureData() {
            InitData();
        }

        private void InitData() {
            var sTime = ActivityUtil.StartTime(ActivityBugFeatureManager.TAG);
            var networkTime = ActivityUtil.GetNetworkTime();
            var startTime = TimeUtil.ChineseTimestampToLocalDateTime(sTime);
            foreach (var affixConfig in AffixConfig.DataList) {
                if (affixConfig.Type == 0) {
                    _badAffixes.Add(affixConfig.Id);
                    var period = affixConfig.Period;
                    var periodEnd = startTime.AddDays(period * 7);
                    if (periodEnd <= networkTime) {
                        _availableBadAffixes.Add(affixConfig.Id);
                    }
                } else {
                    _goodAffixes.Add(affixConfig.Id);
                }
            }
        }

        /// <summary>
        /// 下周加入的漏洞芯片
        /// </summary>
        /// <returns></returns>
        public List<int> GetNextWeekAffixes() {
            if (_availableBadAffixes.Count == 0) {
                InitData();
            }

            var lastAvailableAffixId = _availableBadAffixes[^1];
            var lastAffixConfig = AffixConfig.DataList.FirstOrDefault(x => x.Id == lastAvailableAffixId);
            return AffixConfig.DataList
                .Where(x =>
                    x.Id > lastAvailableAffixId &&
                    x.Type == 0 &&
                    x.Period == lastAffixConfig.Period + 1)
                .Select(x => x.Id) // 增加ID提取
                .ToList();
        }

        /// <summary>
        /// 获取芯片词条配置
        /// </summary>
        /// <param name="affixId"></param>
        /// <returns></returns>
        public WeaponAffixConfig GetAffixConfig(int affixId) {
            return DataMgr.ConfigData.Tables.TbWeaponAffix.DataList.FirstOrDefault(x => x.Id == affixId);
        }

        public (Sprite icon, string name, string desc) GetAffixInfo(int affixId) {
            var affixConfig = GetAffixConfig(affixId);
            var affixIcon = DataMgr.ActivityBugFeatureData.GetAffixSprite(affixId);
            var affixName = ScriptLocalization.Get(affixConfig.NameKey);
            var affixDesc = ScriptLocalization.Get(affixConfig.DescKey);

            string[] dataParams = new[] {
                $"{affixConfig.Factor0}",
                $"{affixConfig.Factor1}",
                $"{affixConfig.Factor2}",
                $"{affixConfig.Factor3}",
                $"{affixConfig.Factor4}",
            };
            affixDesc = string.Format(affixDesc, dataParams);

            return (affixIcon, affixName, affixDesc);
        }

        /// <summary>
        /// 芯片图标
        /// </summary>
        /// <param name="affixId"></param>
        /// <returns></returns>
        public Sprite GetAffixSprite(int affixId) {
            return Atlas.GetSprite(affixId.ToString());
        }

        /// <summary>
        /// 重置一些本层数据
        /// </summary>
        public void ResetDataLevel() {
        }

        public int GetRewardLeftCount(BugActivityRewardsConfig reward) {
            if (ActivityUtil.CheckSkinUnlock(reward.Name)) return 0;
            if (ActivityUtil.CheckBlueprintUnlock(reward.Name)) return 0;
            if (ActivityUtil.CheckPetUnlock(reward.Name)) return 0;

            var rewardCount = 0;
            foreach (var config in RewardConfig.DataList) {
                if (config.Id == reward.Id) {
                    rewardCount = config.Number;
                    break;
                }
            }

            //检查皮肤碎片
            var redeemCount = ActivityUtil.GetRedeemRecords(ActivityBugFeatureManager.TAG, reward.Id);
            if (ActivityUtil.CheckSkinFragment(reward.Name, out int hasCount)) {
                redeemCount = hasCount;
            }

            var leftTime = rewardCount - redeemCount;
            if (leftTime < 0) {
                leftTime = 0;
            }

            return leftTime;
        }

        /// <summary>
        /// 查询配表数值
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public string GetConstValue(string key) {
            foreach (var item in ConstConfig.DataList) {
                if (item.Name == key) {
                    return item.Value;
                }
            }

            return "";
        }

        public int GetRandomBadAffixId(List<int> excludeIds = null) {
            var filterResults = _badAffixes.Where(x => excludeIds == null || !excludeIds.Contains(x));
            if (filterResults.Count == 0) {
                // 如果已经过滤了一遍，则过滤最近一个，其他重新随机
                filterResults = _badAffixes.Where(id => id != excludeIds[^1]);
            }

            return filterResults.GetRandomObject(RGGameSceneManager.Inst.rg_random);
        }

        /// <summary>
        /// 地牢的武器有50%概率增加一个漏洞芯片(词条)
        /// </summary>
        /// <param name="weapon"></param>
        public void ModifyWeaponOnGen(RGWeapon weapon) {
            RGGameSceneManager.Inst.StartCoroutine(NextFrameAddChip(weapon));
        }

        /// <summary>
        /// 部分武器不能添加芯片
        /// </summary>
        private List<string> _filterWeapons = new() {
            "weapon_244",
            "weapon_095",
            "weapon_332",
            "weapon_273",
            "weapon_242",
        };

        /// <summary>
        /// 延迟一帧，等待武器初始化
        /// </summary>
        /// <param name="weapon"></param>
        /// <returns></returns>
        private IEnumerator NextFrameAddChip(RGWeapon weapon) {
            yield return null;
            if (weapon == null) yield break;
            if (weapon is GunPot) yield break;
            if (weapon.owner != null) yield break;
            if (_filterWeapons.Contains(weapon.name)) yield break;
            if (weapon.GetWeaponAffixList().Count > 0) yield break;
            if (weapon.gameObject.GetComponentInParent<RGMountController>() != null) yield break;
            if (RGGameSceneManager.Inst.rg_random.Range(0, 100) > 75) yield break;
            
            AddBadChip(weapon);
        }

        /// <summary>
        /// 给武器添加漏洞芯片词条
        /// </summary>
        /// <param name="weapon"></param>
        private void AddBadChip(RGWeapon weapon) {
            var affixId = GetRandomBadAffixId();
            var affix = WeaponAffixFactory.Create(affixId);
            weapon.AddWeaponAffix(affix);

            if (LogUtil.IsShowLog) {
                LogUtil.Log($"AddBadChip: {affixId} to {weapon.name}, now total:{weapon.GetWeaponAffixList().Count}", ActivityBugFeatureManager.TAG);
            }
        }

        /// <summary>
        /// 击杀进度值
        /// </summary>
        /// <returns></returns>
        public (int hitDamage, int progress) GetHitEnemyVal() {
            var info = GetConstValue("HitEnemy").Split(",").Select(int.Parse).ToList();
            return (info[0], info[1]);
        }

        public void ShowWeaponUpgradeView(RGRoomX roomX) {
            RGGameSceneManager.Inst.StartCoroutine(ShowAbo(roomX));
        }

        private GameObject _abo = null;

        private Vector2 GetRandomPos(RGRoomX roomX) {
            var ctrlPos = RGGameSceneManager.Inst.controller.transform.position;
            var emptyPositions = roomX.GetRoomEmptyPosition(Vector2Int.one);

            Vector2 genPos = emptyPositions.Where(emptyPosition => {
                var dist = Vector2.Distance(ctrlPos, emptyPosition);
                return dist is < 4f and > 1.5f;
            }).ToList().GetRandomObject(RGGameSceneManager.Inst.rg_random);
            return genPos;
        }

        private IEnumerator ShowAbo(RGRoomX roomX) {
            var updateWeapons = GetNeedRepairWeapons();
            if (updateWeapons.Count == 0) {
                SimpleEventManager.Raise(FinishUpgradeBadChipEvent.UseCache());
                yield break;
            }

            var genPos = GetRandomPos(roomX);
            PrefabPool.Inst.Take(PrefabManager.GetPrefab(PrefabName.effect_show_up), genPos, Quaternion.identity);
            var aboProto = ResourcesUtil.Load<GameObject>(ActivityBugFeatureManager.ABO_REPAIR_PATH);
            _abo = GameObject.Instantiate(aboProto, genPos, Quaternion.identity);

            yield return new WaitForSecondsRealtime(1f);

            UIManager.Inst.OpenUIView<WindowChipProgress>(WEAPON_UPGRADE_PATH, new object[] { updateWeapons });
        }

        private List<RGWeapon> GetNeedRepairWeapons() {
            List<RGWeapon> updateWeapons = new List<RGWeapon>();
            var allMyWeapons = RGGameSceneManager.Inst.controller.hand.GetAllWeapons();
            (var hitDmg, var progress) = DataMgr.ActivityBugFeatureData.GetHitEnemyVal();
            foreach (var weapon in allMyWeapons) {
                var wAffixes = weapon.GetWeaponAffixList();
                if (wAffixes.Count == 0) continue;

                var wAffix = wAffixes[0];
                if (wAffix is not WeaponAffixBugFeatureBase bugFeatureAffix) {
                    continue;
                }

                if (!bugFeatureAffix.IsBadAffix) {
                    continue;
                }

                var currVal = bugFeatureAffix.GetCurrentProgress();
                var addVal = bugFeatureAffix.GetIncreaseProgress();
                if (addVal >= hitDmg && currVal < bugFeatureAffix.CHIP_PROGRESS_TARGET) {
                    updateWeapons.Add(weapon);
                }
            }

            return updateWeapons;
        }

        /// <summary>
        /// 漏洞芯片转换为元气芯片
        /// </summary>
        /// <param name="weapon"></param>
        public void ConvertBad2GoodChip(RGWeapon weapon) {
            var wAffix = weapon.GetWeaponAffixList()[0];
            var goodAffixId = wAffix.Id + 1;
            var goodAffix = WeaponAffixFactory.Create(goodAffixId);
            weapon.RemoveWeaponAffix(wAffix);
            weapon.AddWeaponAffix(goodAffix);
            
            var wAffixIcon = DataMgr.ActivityBugFeatureData.GetAffixSprite(goodAffix.Id);
            if (wAffixIcon != null) {
                UICanvas.Inst.weapon_item_icon.ShowActivityWeaponAffix(true, wAffixIcon, goodAffix.Id);
            }
            
            SimpleEventManager.Raise(UpgradeBadChipEvent.UseCache(goodAffix));
            
            if (!GameUtil.IsSingleGame()) {
                var netId = NetControllerManager.GetNetId(RGGameSceneManager.Inst.controller);
                MessageManager.SendCommonMessage(new CommonMessage() {
                    type = CommonMessageType.BadChipConvert2Good,
                    intArray = new[] { (int)netId, goodAffixId },
                });
            }
        }

        public void CacheChipForTrack(int chipId) {
            var affixInfo = GetAffixInfo(chipId);
            var key = ActivityBugFeatureManager.TAG;
            var chipName = affixInfo.name;
            if (chipId % 2 == 0) {
                chipName += "[元气]";
            } else {
                chipName += "[漏洞]";
            }
            if (BattleData.data.activityExtras.ContainsKey(key)) {
                BattleData.data.activityExtras[key].Add(chipName);
            } else {
                BattleData.data.activityExtras.Add(key, new List<string> { chipName });
            }
        }
    }
}