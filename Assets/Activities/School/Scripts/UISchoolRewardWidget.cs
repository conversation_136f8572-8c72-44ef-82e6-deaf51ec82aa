using cfg.ActivitySchoolReward;
using DG.Tweening;
using I2.Loc;
using RGScript.Data;
using RGScript.Data.GameItemData;
using RGScript.UI.Widget;
using System.Collections.Generic;
using UI.Base;
using UIFramework;
using UnityEngine;
using UnityEngine.UI;

namespace Activities.School.Scripts {
    /// <summary>
    /// 元气学院活动奖励兑换界面
    /// </summary>
    public class UISchoolRewardWidget : BaseUIWidget {
        private GameObject _itemPrefab;
        private Transform _content;
        private Text _titleText;
        private Text _coinText;

        private static List<ActivitySchoolRewardConfig> Configs =>
            DataMgr.ConfigData.Tables.TbActivitySchoolRewardConfig.DataList;

        protected override void OnInit() {
            base.OnInit();
            var body = transform.Find("body");
            _coinText = transform.Find("header/currency/num").GetComponent<Text>();
            var viewport = body.Find("viewport");
            _content = viewport.Find("content");
            _itemPrefab = _content.Find("itemProto").gameObject;
            InitRewards();
            RefreshCoin();
        }

        protected override void OnStartUp(params object[] args) {
            base.OnStartUp(args);
            RefreshCoin();
        }

        private void RefreshCoin() {
            _coinText.text = ActivitySchoolManager.GetActivityCoinCount().ToString();
        }

        private void InitRewards() {
            foreach (var reward in Configs) {
                var rewardKey = reward.Name;
                var rewardCost = reward.Cost;
                var rewardNumber = reward.Number;
                var rewardLeft = GetRewardLeftCount(reward);

                var item = Instantiate(_itemPrefab, _content);
                item.name = rewardKey;
                if (rewardKey.StartsWith("p_")) {
                    item.transform.Find("item/preview/background").gameObject.SetActive(false);
                    item.transform.Find("item/preview/icon").GetComponent<RectTransform>().sizeDelta =
                        new Vector2(200, 200);
                    item.transform.Find("item/preview/icon").GetComponent<Image>().sprite =
                        SpriteUtility.GetSprite(rewardKey);
                } else {
                    item.GetComponentInChildren<Item>().SetItem(rewardKey);
                }

                item.transform.Find("fee/num").GetComponent<Text>().text = rewardCost.ToString();
                //如果是蓝图或者是皮肤，则判断是否已拥有，已拥有则不显示。
                if (rewardKey.StartsWith("blueprint_weapon") ) {
                    rewardLeft = DataUtil.HasBluePrint(rewardKey) ? 0 : rewardLeft;
                }
                if (rewardKey.StartsWith("c_") && rewardKey.Contains("_s_")) {
                    string[] array = rewardKey.Split('_');
                    int hero = int.Parse(array[1]);
                    int skinIndex = int.Parse(array[3]);
                    rewardLeft = DataUtil.GetSkinUnlock((emHero)hero, skinIndex) ?  0 : rewardLeft;
                }
                
                item.transform.Find("left/text").GetComponent<Text>().text =
                    $"{ScriptLocalization.Get("mailbox/remainder", "#剩余")}:{rewardLeft}/{rewardNumber}";
                item.transform.Find("shadow").gameObject.SetActive(rewardLeft <= 0);
                AddClickEvent(item, reward, rewardCost);
            }

            _itemPrefab.SetActive(false);
        }

        private void AddClickEvent(GameObject item, ActivitySchoolRewardConfig reward, int cost) {
            item.GetComponent<Button>().onClick.AddListener(() => {
                if (GetRewardLeftCount(reward) <= 0) {
                    ShowLeftLimitEffect(item);
                    return;
                }

                if (ActivitySchoolManager.GetActivityCoinCount() < cost) {
                    ShowLimitCoinEffect();
                    return;
                }

                var info = UIManager.Inst.OpenUIView<UISchoolActivityRewardInfo>(
                    "Activities/School/Prefabs/school_reward_info.prefab");
                info.SetRewardInfo(reward, cost);
                info.ConfirmReward = null;
                info.ConfirmReward += OnConfirmReward;
            });
        }

        private void OnConfirmReward(bool rewardResult) {
            if (!rewardResult) {
                ShowLimitCoinEffect();
            }

            RefreshUI();
        }

        private void RefreshUI() {
            foreach (var reward in Configs) {
                var rewardKey = reward.Name;
                var rewardNumber = reward.Number;
                var rewardLeft = GetRewardLeftCount(reward);
                var rewardItem = _content.Find(rewardKey);
                rewardItem.transform.Find("left/text").GetComponent<Text>().text =
                    $"{ScriptLocalization.Get("mailbox/remainder", "#剩余")}:{rewardLeft}/{rewardNumber}";
                rewardItem.transform.Find("shadow").gameObject.SetActive(rewardLeft <= 0);
                _coinText.text = ActivitySchoolManager.GetActivityCoinCount().ToString();
            }
        }

        private int GetRewardLeftCount(ActivitySchoolRewardConfig reward) {
            var redeemCount = ActivityUtil.GetSchoolRedeemRecords(reward);
            var rewardCount = 0;
            foreach (var config in Configs) {
                if (config.Id == reward.Id) {
                    rewardCount = config.Number;
                    break;
                }
            }

            var leftTime = rewardCount - redeemCount;
            if (leftTime < 0) {
                leftTime = 0;
            }

            return leftTime;
        }

        private void ShowLeftLimitEffect(GameObject item) {
            var text = item.transform.Find("left/text").GetComponent<Text>();
            text.DOColor(Color.red, 0.5f).SetUpdate(true).OnComplete(() => {
                text.DOColor(Color.white, 0.5f).SetUpdate(true);
            });
        }

        private void ShowLimitCoinEffect() {
            _coinText.DOColor(Color.red, 0.5f).SetUpdate(true).OnComplete(() => {
                _coinText.DOColor(Color.white, 0.5f).SetUpdate(true);
            }).SetLoops(2);
        }
    }
}