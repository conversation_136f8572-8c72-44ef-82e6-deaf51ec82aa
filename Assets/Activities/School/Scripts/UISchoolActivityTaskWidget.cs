using I2.Loc;
using RGScript.Data;
using System.Collections;
using System.Collections.Generic;
using UI.Base;
using UnityEngine;
using UnityEngine.UI;

namespace Activities.School.Scripts {
    /// <summary>
    /// 任务列表界面
    /// </summary>
    public class UISchoolActivityTaskWidget : BaseUIWidget {
        private Text _dayTaskText;
        private Text _fixedTaskText;
        private GameObject _itemPrefab;
        private Transform _dayTasksTR;
        private Transform _fixedTasksTR;
        private Transform _content;

        protected override void OnInit() {
            base.OnInit();
            _content = transform.Find("body/viewport/content");
            _dayTasksTR = _content.Find("dayTasks");
            _fixedTasksTR = _content.Find("fixedTasks");
            _dayTaskText = _dayTasksTR.Find("title").GetComponent<Text>();
            _fixedTaskText = _fixedTasksTR.Find("title").GetComponent<Text>();
            _itemPrefab = _content.Find("itemProto").gameObject;
            _itemPrefab.SetActive(false);
        }

        protected override void OnStartUp(params object[] args) {
            base.OnStartUp(args);
            RefreshContent();
            StartCoroutine(FixedContentHeight());
        }
        
        private IEnumerator FixedContentHeight() {
            yield return null;
            AdjustScrollContentHeight();
        }

        void AdjustScrollContentHeight() {
            var sizeDelta = _content.GetComponent<RectTransform>().sizeDelta;
            var height = _dayTasksTR.GetComponent<RectTransform>().sizeDelta.y + _fixedTasksTR.GetComponent<RectTransform>().sizeDelta.y;
            _content.GetComponent<RectTransform>().sizeDelta = new Vector2(sizeDelta.x, height);
        }

        private void RefreshContent() {
            RefreshDayTasks();
            RefreshFixedTasks();
        }

        private void RefreshDayTasks() {
            var tasks = DataMgr.ActivitySchoolData.GetDay3Tasks();
            var totalCount = tasks.Count;
            var completeCount = 0;
            for (var i = 0; i < tasks.Count; i++) {
                var key = "dayTask_" + i;
                var item = _dayTasksTR.Find(key);
                if (item == null) {
                    item = Instantiate(_itemPrefab, _dayTasksTR).transform;
                    item.gameObject.name = key;
                }
                
                var taskStatus = tasks[i].GetTaskStatus();
                if (taskStatus == SchoolTaskStatus.Finish || taskStatus == SchoolTaskStatus.Taked) {
                    completeCount++;
                }
                RefreshTaskItem(item.transform, tasks[i]);
            }
            _dayTaskText.text = string.Format(ScriptLocalization.Get("activity/school/title_daily_task", "#每日任务{0}/{1}"), completeCount, totalCount);
        }
        
        private void RefreshFixedTasks() {
            var tasks = DataMgr.ActivitySchoolData.GetFixedTasks();
            var totalCount = tasks.Count;
            var completeCount = 0;
            for (var i = 0; i < tasks.Count; i++) {
                var key = "fixedTask_" + i;
                var item = _fixedTasksTR.Find(key);
                if (item == null) {
                    item = Instantiate(_itemPrefab, _fixedTasksTR).transform;
                    item.gameObject.name = key;
                }

                var taskStatus = tasks[i].GetTaskStatus();
                if (taskStatus == SchoolTaskStatus.Finish || taskStatus == SchoolTaskStatus.Taked) {
                    completeCount++;
                }
                RefreshTaskItem(item.transform, tasks[i]);
            }
            _fixedTaskText.text = string.Format(ScriptLocalization.Get("activity/school/title_total_task", "#总任务{0}/{1}"), completeCount, totalCount);
        }

        private void RefreshTaskItem(Transform item, SchoolTask task) {
            var taskStatus = task.GetTaskStatus();
            item.Find("desc").GetComponent<Text>().text = task.GetTaskName();
            item.Find("bonus/num").GetComponent<Text>().text = task.bonusCoin.ToString();
            var btnUnFinish = item.Find("btnUnFinish").GetComponent<Button>();
            var btnFinish = item.Find("btnFinish").GetComponent<Button>();
            var btnTaked = item.Find("btnTaked").GetComponent<Button>();
            
            btnUnFinish.gameObject.SetActive(false);
            btnFinish.gameObject.SetActive(false);
            btnTaked.gameObject.SetActive(false);
            
            if (taskStatus == SchoolTaskStatus.UnFinish) {
                btnUnFinish.onClick.RemoveAllListeners();
                btnUnFinish.gameObject.SetActive(true);
            } else if (taskStatus == SchoolTaskStatus.Finish) {
                btnFinish.onClick.RemoveAllListeners();
                btnFinish.onClick.AddListener(() => {
                    TakeBonus(task);
                });
                btnFinish.gameObject.SetActive(true);
            } else if (taskStatus == SchoolTaskStatus.Taked) {
                btnTaked.onClick.RemoveAllListeners();
                btnTaked.gameObject.SetActive(true);
            }
            
            item.Find("mask").gameObject.SetActive(taskStatus == SchoolTaskStatus.Taked);
            item.gameObject.SetActive(true);
            
            if (LogUtil.IsShowLog) {
                LogUtil.Log($"task:{task} status:{taskStatus}", ActivitySchoolManager.TAG);
            }
        }

        private void TakeBonus(SchoolTask task) {
            DataMgr.ActivitySchoolData.TakeTaskBonus(task);
            RefreshContent();
            
            TAUtil.Track("school_task_change", new Dictionary<string, object>() {
                { "task_status", "已领取" },
                { "task_name", task.GetTaskName(true) },
            });
        }
    }
}
