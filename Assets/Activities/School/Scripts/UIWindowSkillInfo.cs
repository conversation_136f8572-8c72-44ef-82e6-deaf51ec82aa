using UIFramework;
using UnityCommon.UI;
using UnityEngine;
using UnityEngine.UI;

namespace Activities.School.Scripts {
    public class UIWindowSkillInfo : BaseUIView {
        public Image image;
        public Text title;
        public Text desc;
        private Button _closeBtn;

        public override void InitView() {
            image = transform.Find("body/content/icon").GetComponent<Image>();
            title = transform.Find("body/title").GetComponent<Text>();
            desc = transform.Find("body/content/desc").GetComponent<Text>();
            _closeBtn = transform.Find("body/close_btn").GetComponent<Button>();
            _closeBtn.onClick.RemoveAllListeners();
            _closeBtn.onClick.AddListener(OnClick_Close);
            pauseWhenShow = true;
            base.InitView();
        }

        public override void HideView(params object[] objects) {
            base.HideView(objects);
            Destroy(gameObject);
        }

        public static UIWindowSkillInfo ShowDialog(Sprite sprite, string title, string desc) {
            var dialog = UIManager.Inst.OpenUIView<UIWindowSkillInfo>("window_skill_info");
            var dialogTf = dialog.transform;
            dialog.image.sprite = sprite;
            dialog.title.text = title;
            dialog.desc.text = desc;
            dialogTf.localPosition = Vector3.zero;
            dialogTf.localScale = Vector3.one;
            return dialog;
        }
    }
}