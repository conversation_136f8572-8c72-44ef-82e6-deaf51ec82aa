using Activities.School.Scripts;
using BattleSystem.Skill;
using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

namespace BattleSystem.SkillSystem {
    public class SkillScienceDiv : SkillEditable {
        private const string NameCount = "count";
        private const string NameSpeed = "speed";
        private const string NameWait = "wait";
        
        private readonly GameObject _bulletProto;

        public SkillScienceDiv(AIController aiController, SkillData data) : base(aiController, data) {
            _bulletProto = ResourcesUtil.Load<GameObject>(SkillConfig.Prefabs[0]);
        }

        public override bool CanUseSkill() {
            return ActivitySchoolManager.IsEqualOrGreaterDifficulty(SchoolDifficultyType.High) && base.CanUseSkill();
        }

        public override bool DoSkill(bool isEnforce = false) {
            if (!base.DoSkill(isEnforce)) {
                return false;
            }

            AIController.StartCoroutine(CreateBullet());
            return true;
        }

        private IEnumerator CreateBullet() {
            for (int i = 0; i < GetParameter<int>(NameCount); i++) {
                var weapon = AIController.Hand.GetRGEWeapon();
                float r2 = weapon.Rg_random.Range(-weapon.deviation, weapon.deviation);
                var bulletInfo = new BulletInfo {
                    bulletProto = _bulletProto,
                    createPosition = weapon.gun_point.position,
                    directionAngle = weapon.facing < 0 ? 180 - weapon.transform.eulerAngles.z + r2 : weapon.transform.eulerAngles.z + r2,
                    speed = GetParameter<int>(NameSpeed),
                    camp = weapon.camp,
                    sourceObject = AIController.BodyTransform.gameObject
                };

                var damageInfo = new DamageInfo {
                    camp = weapon.camp,
                    damage = 0,
                }.SetThrough(false).SetRepel(0);

                var tempObj = BulletFactory.TakeBullet(bulletInfo, damageInfo);
                RGMusicManager.GetInstance().PlayEffect(weapon.audio_clip);

                var rgBullet = tempObj.GetComponent<RGBullet>();
                if (weapon.camp != 0 && rgBullet) {
                    rgBullet.hitOn = true;
                }

                yield return new WaitForSeconds(GetParameter<float>(NameWait));
            }

            yield return null;
            ExpectedEnd();
        }
    }
}