using cfg.ActivitySchoolAnswer;
using cfg.ActivitySchoolBonus;
using cfg.ActivitySchoolQuestion;
using I2.Loc;
using RGScript.Battle;
using RGScript.Data;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using UnityEngine;

namespace Activities.School.Scripts {
    /// <summary>
    /// 4种正面buff
    /// </summary>
    public enum SchoolBuff {
        SchoolScience,
        SchoolWord,
        SchoolSport,
        SchoolArt,
        Count,
    }
    
    /// <summary>
    /// 题目类型
    /// </summary>
    public enum SchoolQuestionType {
        Common = 0,  // 通用
        Science = 1, // 理科
        Word = 2,    // 文科
        Sport = 3,   // 体育
        Art = 4,     // 艺术
        Count = 5,
        None
    }
    
    /// <summary>
    /// 难度类型
    /// </summary>
    public enum SchoolDifficultyType {
        Common = 0,  // 通用
        Primary = 1, // 小学
        Middle = 2,  // 初中
        High = 3,    // 高中
        Final = 4    // 高考
    }
    
    /// <summary>
    /// 奖励类型
    /// </summary>
    public enum SchoolBonusType {
        Other = -1,     // 其他
        Material = 0,   // 材料
        Potion = 1,     // 药水
        Plant = 2,      // 种子
        BuffBall = 3,   // 天赋球
        Drink = 4       // 饮料
    }
    
    public enum SchoolTaskType {
        EveryDay = 0,   // 每日刷新的任务
        Total = 1,      // 总任务
    }
    
    public enum SchoolTaskDifficulty {
        Simple = 0,     // 简单
        Hard = 1,       // 困难
    }
    
    public enum SchoolTaskStatus {
        UnFinish = 0,   // 未完成
        Finish = 1,     // 已完成，未领取
        Taked = 2,      // 已领取
    }

    public class SchoolTask {
        public int id;
        private SchoolTaskStatus status;
        public SchoolTaskType type;
        public SchoolTaskDifficulty difficulty;
        public string localKey;
        public string localFallback;
        public int bonusCoin;
        public int completeCount;
        public string extra;

        public string GetTaskName(bool forceCN = false) {
            var loc = forceCN 
                ? ScriptLocalization.GetCN(localKey) 
                : ScriptLocalization.Get(localKey, localFallback);
            
            var count = DataMgr.ActivitySchoolData.GetTaskCompleteCount(this);
            if (!string.IsNullOrEmpty(extra)) {
                var extraLoc = extra; 
                if (!int.TryParse(extraLoc, out int extraInt)) {
                    extraLoc = forceCN 
                        ? ScriptLocalization.GetCN(extra) 
                        : ScriptLocalization.Get(extra);
                }
                loc = loc.Replace("{0}", extraLoc);
                if (loc.Contains("{2}")) {
                    loc = loc.Replace("{1}", count.ToString());
                    loc = loc.Replace("{2}", completeCount.ToString());
                } else {
                    loc = loc.Replace("{1}", completeCount.ToString());
                }
            } else {
                if (loc.Contains("{1}")) {
                    loc = loc.Replace("{0}", count.ToString());
                    loc = loc.Replace("{1}", completeCount.ToString());
                } else {
                    loc = loc.Replace("{0}", completeCount.ToString());
                }
            }

            return loc;
        }

        public SchoolTaskStatus GetTaskStatus() {
            SchoolTaskStatus tmpStatus = SchoolTaskStatus.UnFinish;
            var hasTaked = DataMgr.ActivitySchoolData.HasTaskTaked(this);
            if (hasTaked) {
                tmpStatus = SchoolTaskStatus.Taked;
            } else {
                var count = DataMgr.ActivitySchoolData.GetTaskCompleteCount(this);
                if (count >= completeCount) {
                    tmpStatus = SchoolTaskStatus.Finish;
                }
            }

            return tmpStatus;
        }
        
        public override string ToString() {
            return $"id:{id} " +
                   $"type:{type} " +
                   $"difficulty:{difficulty} " +
                   $"localKey:{localKey} " +
                   $"localFallback:{localFallback} " +
                   $"bonusCoin:{bonusCoin} " +
                   $"completeCount:{completeCount} " +
                   $"extra:{extra}";
        }
    }
    
    /// <summary>
    /// 元气学院活动题库，答案，奖励配置管理数据
    /// </summary>
    public class ActivitySchoolData : BaseData {
        public TbActivitySchoolQuestionConfig QuestionConfig => DataMgr.ConfigData.Tables.TbActivitySchoolQuestionConfig;
        public TbActivitySchoolAnswerConfig AnswerConfig => DataMgr.ConfigData.Tables.TbActivitySchoolAnswerConfig;
        public TbActivitySchoolBonusConfig BonusConfig => DataMgr.ConfigData.Tables.TbActivitySchoolBonusConfig;
        private List<SchoolTask> _daySimpleTasks = new List<SchoolTask>();
        private List<SchoolTask> _dayHardTasks = new List<SchoolTask>();
        private List<SchoolTask> _fixedTasks = new List<SchoolTask>();
        
        // 技能升级经验值配置
        private Dictionary<SchoolDifficultyType, List<int>> _progressDic = new Dictionary<SchoolDifficultyType, List<int>>() {
            { SchoolDifficultyType.Primary, new List<int>() { 100, 100, 100, 100, 100, 100 } },
            { SchoolDifficultyType.Middle, new List<int>() { 300, 300, 300 } },
            { SchoolDifficultyType.High, new List<int>() { 500, 500, 500 } },
            { SchoolDifficultyType.Final, new List<int>() { 10000 } },
        };

        private Dictionary<string, SchoolBuff> _taskExtraDic = new Dictionary<string, SchoolBuff>() {
            { "activity/school/buff_science", SchoolBuff.SchoolScience },
            { "activity/school/buff_literature", SchoolBuff.SchoolWord },
            { "activity/school/buff_sports", SchoolBuff.SchoolSport },
            { "activity/school/buff_arts", SchoolBuff.SchoolArt },
        };
        private Dictionary<string, SchoolQuestionType> _taskExtraDic2 = new Dictionary<string, SchoolQuestionType>() {
            { "activity/school/subject_science", SchoolQuestionType.Science },
            { "activity/school/subject_literature", SchoolQuestionType.Word },
            { "activity/school/subject_sports", SchoolQuestionType.Sport },
            { "activity/school/subject_arts", SchoolQuestionType.Art },
        };

        /// <summary>
        /// 由活动管理器初始化后调用
        /// </summary>
        public void InitTask() {
            if (_daySimpleTasks.Count > 0) return;
            
            var TaskConfig = DataMgr.ConfigData.Tables.TbActivitySchoolTaskConfig;
            foreach (var item in TaskConfig.DataList) {
                var task = new SchoolTask {
                    id = item.Id,
                    type = (SchoolTaskType)item.Type,
                    difficulty = (SchoolTaskDifficulty)item.Difficulty,
                    localKey = item.TaskKey,
                    localFallback = item.TaskDesc,
                    bonusCoin = item.Bonus,
                    completeCount = item.Count,
                    extra = item.Extra
                };
                if (task.type == SchoolTaskType.EveryDay) {
                    if (task.difficulty == SchoolTaskDifficulty.Simple) {
                        _daySimpleTasks.Add(task);
                    } else {
                        _dayHardTasks.Add(task); 
                    }
                } else {
                    _fixedTasks.Add(task);
                }
            }
        }
        
        public List<int> GetProgressList(SchoolDifficultyType difficultyType) {
            return _progressDic[difficultyType];
        }

        /// <summary>
        /// 进度值映射到技能等级（对应神殿skillLevel）
        /// </summary>
        /// <returns></returns>
        public int GetProgress2SkillLevel() {
            var (difficultyType, idx) = GetProgress();
            return Difficulty2SkillLevel(difficultyType);
        }

        public int Difficulty2SkillLevel(SchoolDifficultyType difficultyType) {
            int skillLevel = 0;
            switch (difficultyType) {
                case SchoolDifficultyType.Primary:
                    skillLevel = 0;
                    break;
                case SchoolDifficultyType.Middle:
                    skillLevel = 2;
                    break;
                case SchoolDifficultyType.High:
                    skillLevel = 4;
                    break;
                case SchoolDifficultyType.Final:
                    skillLevel = 7;
                    break;
            }

            return skillLevel;
        }
        
        private  string TaskDayKey(SchoolTask task) {
            var dayKey = ActivityUtil.GetDayKey(ActivitySchoolManager.TAG);
            return dayKey + "_task_" + task.id;
        }

        private string TaskFixedKey(SchoolTask task) {
            return ActivitySchoolManager.TAG + ActivitySchoolManager.ReturnKey + "_task_" + task.id;
        }
        
        public string GetTaskKey(SchoolTask task) {
            return task.type == SchoolTaskType.EveryDay ? TaskDayKey(task) : TaskFixedKey(task);
        }

        public void TakeTaskBonus(SchoolTask task) {
            var key = GetTaskKey(task);
            if (StatisticData.data.Activity202508SchoolTakeTaskKeys.Contains(key)) {
                Debug.LogError($"has taked already:{task}");
                return;
            }

            ActivitySchoolManager.AddExp(task.bonusCoin);
            var rewardable = ActivityUtil.GetRewardable(ActivitySchoolManager.MatName, task.bonusCoin);
            ActivityUtil.AddReward(rewardable, GetItemSource.ActivitySchool);
            StatisticData.data.Activity202508SchoolTakeTaskKeys.Add(key);
            ItemData.Save();
            StatisticData.Save();
        }
        
        public bool HasTaskTaked(SchoolTask task) {
            var key = GetTaskKey(task);
            return StatisticData.data.Activity202508SchoolTakeTaskKeys.Contains(key);
        }
        
        public int GetTaskCompleteCount(SchoolTask task) {
            int count = 0;
            //7.25复刻
            string key = ActivitySchoolManager.ReturnKey;
            var today = ActivityUtil.GetNetToday();
            switch (task.id) {
                case 1:
                case 2:
                case 3:
                case 4:
                    key += $"{today}_kill_{task.extra}";
                    count = StatisticData.data.GetEventCount(key);
                    if (LogUtil.IsShowLog) {
                        LogUtil.Log($"task:{task} key:{key} count:{count}", ActivitySchoolManager.TAG);
                    }

                    break;
                case 5:
                case 6:
                case 7:
                case 8:
                    if (_taskExtraDic.TryGetValue(task.extra, out SchoolBuff tmpBuff)) {
                        key += $"{today}_buff_{tmpBuff}";
                        count = StatisticData.data.GetEventCount(key);
                        if (LogUtil.IsShowLog) {
                            LogUtil.Log($"task:{task} key:{key} count:{count}", ActivitySchoolManager.TAG);
                        }
                    } else {
                        Debug.LogError($"配置错误，extra缺少buff配置:{task}");
                    }

                    break;
                case 9:
                    for (var q = SchoolQuestionType.Science; q <= SchoolQuestionType.Art; q++) {
                        key += $"{today}_onegame_correct_{q}";
                        count += StatisticData.data.GetEventCount(key);
                        if (LogUtil.IsShowLog) {
                            LogUtil.Log($"task:{task} key:{key} count:{count}", ActivitySchoolManager.TAG);
                        }
                    }
                    break;
                case 10:
                case 11:
                case 12:
                case 13:
                    if (_taskExtraDic2.TryGetValue(task.extra, out SchoolQuestionType tmpQuestionType)) {
                        key += $"{today}_onegame_correct_{tmpQuestionType}";
                        count = StatisticData.data.GetEventCount(key);
                        if (LogUtil.IsShowLog) {
                            LogUtil.Log($"task:{task} key:{key} count:{count}", ActivitySchoolManager.TAG);
                        }
                    } else {
                        Debug.LogError($"配置错误，extra缺少buff配置:{task}");
                    }

                    break;
                case 14:
                    for (var q = SchoolQuestionType.Science; q <= SchoolQuestionType.Art; q++) {
                        key += $"{today}_correct_{q}";
                        count += StatisticData.data.GetEventCount(key);
                        if (LogUtil.IsShowLog) {
                            LogUtil.Log($"task:{task} key:{key} count:{count}", ActivitySchoolManager.TAG);
                        }
                    }
                    break;
                case 15:
                case 16:
                case 17:
                    var numStr = task.extra.Split("_")[1];
                    if (int.TryParse(numStr, out int num)) {
                        var difficultyType = (SchoolDifficultyType)(num + 1);
                        for (var d = difficultyType; d <= SchoolDifficultyType.Final; d++) {
                            key += $"{today}_school_game_{d}";
                            count += StatisticData.data.GetEventCount(key);
                            if (LogUtil.IsShowLog) {
                                LogUtil.Log($"task:{task} key:{key} count:{count}", ActivitySchoolManager.TAG);
                            }
                        }
                    } else {
                        Debug.LogError($"配置错误，extra缺少进度配置:{task}");
                    }
                    break;
                case 18:
                case 19:
                case 20:
                    var numStr2 = task.extra.Split("_")[1];
                    if (int.TryParse(numStr2, out int num2)) {
                        var difficultyType = (SchoolDifficultyType)(num2 + 1);
                        key += $"{today}_kill_boss_{difficultyType}";
                        count = StatisticData.data.GetEventCount(key);
                        if (LogUtil.IsShowLog) {
                            LogUtil.Log($"task:{task} key:{key} count:{count}", ActivitySchoolManager.TAG);
                        }
                    } else {
                        Debug.LogError($"配置错误，extra缺少进度配置:{task}");
                    }

                    break;
                case 21:
                    for (var theD = SchoolDifficultyType.Primary; theD <= SchoolDifficultyType.Final; theD++) {
                        key += $"{today}_school_game_{theD}_3";
                        count += StatisticData.data.GetEventCount(key);
                        if (LogUtil.IsShowLog) {
                            LogUtil.Log($"task:{task} key:{key} count:{count}", ActivitySchoolManager.TAG);
                        }
                    }
                    break;
                case 22:
                    for (var theD = SchoolDifficultyType.Primary; theD <= SchoolDifficultyType.Final; theD++) {
                        key += $"{today}_school_game_{theD}_4";
                        count += StatisticData.data.GetEventCount(key);
                        if (LogUtil.IsShowLog) {
                            LogUtil.Log($"task:{task} key:{key} count:{count}", ActivitySchoolManager.TAG);
                        }
                    }

                    break;
                case 23:
                    for (var theD = SchoolDifficultyType.Primary; theD <= SchoolDifficultyType.Final; theD++) {
                        key += $"{today}_school_game_{theD}_5";
                        count += StatisticData.data.GetEventCount(key);
                        if (LogUtil.IsShowLog) {
                            LogUtil.Log($"task:{task} key:{key} count:{count}", ActivitySchoolManager.TAG);
                        }
                    }

                    break;
                case 24:
                    for (var q = SchoolQuestionType.Science; q <= SchoolQuestionType.Art; q++) {
                        key += $"wrong_{q}";
                        count += StatisticData.data.GetEventCount(key);
                        if (LogUtil.IsShowLog) {
                            LogUtil.Log($"task:{task} key:{key} count:{count}", ActivitySchoolManager.TAG);
                        }
                    }
                    break;
                case 25:
                    for (var q = SchoolQuestionType.Science; q <= SchoolQuestionType.Art; q++) {
                        key += $"correct_{q}";
                        count += StatisticData.data.GetEventCount(key);
                        if (LogUtil.IsShowLog) {
                            LogUtil.Log($"task:{task} key:{key} count:{count}", ActivitySchoolManager.TAG);
                        }
                    }
                    break;
                case 26:
                case 27:
                case 28:
                case 29:
                    key += $"kill_{task.extra}";
                    count = StatisticData.data.GetEventCount(key);
                    if (LogUtil.IsShowLog) {
                        LogUtil.Log($"task:{task} key:{key} count:{count}", ActivitySchoolManager.TAG);
                    }

                    break;
                case 30:
                    key += "answer_correct_all";
                    count = StatisticData.data.GetEventCount(key);
                    if (LogUtil.IsShowLog) {
                        LogUtil.Log($"task:{task} key:{key} count:{count}", ActivitySchoolManager.TAG);
                    }

                    break;
                case 31:
                    int maxCount = 0;
                    for (var buff = SchoolBuff.SchoolScience; buff <= SchoolBuff.SchoolArt; buff++) {
                        key += $"buff_{buff}";
                        var tmpCount = StatisticData.data.GetEventCount(key);
                        if (LogUtil.IsShowLog) {
                            LogUtil.Log($"task:{task} key:{key} count:{tmpCount}", ActivitySchoolManager.TAG);
                        }

                        if (tmpCount > maxCount) {
                            maxCount = tmpCount;
                        }
                    }

                    count = maxCount;
                    break;
                case 32:
                    int minCount = 0;
                    for (var buff = SchoolBuff.SchoolScience; buff <= SchoolBuff.SchoolArt; buff++) {
                        key += $"buff_{buff}";
                        var tmpCount = StatisticData.data.GetEventCount(key);
                        if (LogUtil.IsShowLog) {
                            LogUtil.Log($"task:{task} key:{key} count:{tmpCount}", ActivitySchoolManager.TAG);
                        }

                        if (minCount == 0) {
                            minCount = tmpCount;
                        }

                        minCount = Mathf.Min(minCount, tmpCount);
                    }

                    count = minCount;
                    break;
                case 33:
                case 34:
                case 35:
                    var numStr3 = task.extra.Split("_")[1];
                    if (int.TryParse(numStr3, out int num3)) {
                        var (tmpD, idx) = DataMgr.ActivitySchoolData.GetProgress();
                        if (LogUtil.IsShowLog) {
                            LogUtil.Log($"task:{task} difficulty:{tmpD} idx:{idx}", ActivitySchoolManager.TAG);
                        }

                        int d2Int = (int)tmpD;
                        int targetD2Int = num3 + 1;
                        if (d2Int >= targetD2Int) {
                            count = 1;
                        }
                    } else {
                        Debug.LogError($"配置错误，extra缺少进度配置:{task}");
                    }

                    break;
                case 36:
                    key += "school_game_pass_Final";
                    count = StatisticData.data.GetEventCount(key);
                    if (LogUtil.IsShowLog) {
                        LogUtil.Log($"task:{task} key:{key} count:{count}", ActivitySchoolManager.TAG);
                    }

                    break;
            }

            return count;
        }
        
        /// <summary>
        /// 根据经验值增长的进度，不随难度选择而改变，影响角色技能升级
        /// </summary>
        /// <returns></returns>
        public (SchoolDifficultyType, int) GetProgress() {
            var total0 = _progressDic[SchoolDifficultyType.Primary].Sum();
            var total1 = _progressDic[SchoolDifficultyType.Middle].Sum();
            var total2 = _progressDic[SchoolDifficultyType.High].Sum();
            var total3 = _progressDic[SchoolDifficultyType.Final].Sum();
            var exp = ActivitySchoolManager.GetExp();
            var itemExtraExp = exp;
            
            SchoolDifficultyType difficultyType = SchoolDifficultyType.Primary;
            if (exp < total0) {
                difficultyType = SchoolDifficultyType.Primary;
            } else if (exp < total0 + total1) {
                difficultyType = SchoolDifficultyType.Middle;
                itemExtraExp -= total0;
            } else if (exp < total0 + total1 + total2) {
                difficultyType = SchoolDifficultyType.High;
                itemExtraExp -= (total0 + total1);
            } else {
                difficultyType = SchoolDifficultyType.Final;
                itemExtraExp -= (total0 + total1 + total2);
            }

            int idx = 0;
            int tmpTotal = 0;
            var expList = _progressDic[difficultyType];
            for (var i = 0; i < expList.Count; i++) {
                tmpTotal += expList[i];
                if (itemExtraExp < tmpTotal) {
                    idx = i;
                    break;
                }
            }

            if (LogUtil.IsShowLog) {
                LogUtil.Log($"difficultyType:{difficultyType} idx:{idx} exp:{exp}", ActivitySchoolManager.TAG);
            }

            return (difficultyType, idx);
        }
        

        public List<SchoolTask> GetFixedTasks() {
            // 按完成状态排序
            var oldTasks = new List<SchoolTask>(_fixedTasks);
            oldTasks = oldTasks.OrderBy(task => task.GetTaskStatus() == SchoolTaskStatus.Finish ? 0 : 
                task.GetTaskStatus() == SchoolTaskStatus.UnFinish ? 1 : 2).ToList();
            return oldTasks;
        }
                
        // 2个简单任务 + 1个困难任务
        // 并按完成状态排序
        public List<SchoolTask> GetDay3Tasks() {
            List<SchoolTask> tasks = new List<SchoolTask>();
            var cacheTasks = PlayerSaveData.Inst.TodayActivitySchoolTasks;
            if (cacheTasks.Count == 0) {
                var hardIdx = Random.Range(0, _dayHardTasks.Count);
                var hardTask = _dayHardTasks[hardIdx];
                tasks.Add(hardTask);

                while (tasks.Count < 3) {
                    var idx = Random.Range(0, _daySimpleTasks.Count);
                    var task = _daySimpleTasks[idx];
                    if (!tasks.Contains(task)) {
                        tasks.Add(task);
                    }
                }
                
                var ids = tasks.Select(item => item.id).ToList();
                PlayerSaveData.Inst.TodayActivitySchoolTasks = ids;
            } else {
                foreach (var cacheId in cacheTasks) {
                    var task = _daySimpleTasks.FindLast(item => item.id == cacheId);
                    if (task == null) {
                        task = _dayHardTasks.FindLast(item => item.id == cacheId);
                    }
                    tasks.Add(task);
                }
            }

            // 按完成状态排序
            tasks = tasks.OrderBy(task => task.GetTaskStatus() == SchoolTaskStatus.Finish ? 0 : 
                task.GetTaskStatus() == SchoolTaskStatus.UnFinish ? 1 : 2).ToList();
            return tasks;
        }

        /// <summary>
        /// 获取随机题目
        /// </summary>
        /// <param name="difficultyType"></param>
        /// <returns></returns>
        public ActivitySchoolQuestionConfig GetRandomQuestion(SchoolQuestionType questionType, SchoolDifficultyType difficultyType) {
            var dataList = QuestionConfig.DataList.FindAll(x => (x.Type == (int)SchoolQuestionType.Common || x.Type == (int)questionType)&& 
                                                                (x.Difficulty == (int)SchoolDifficultyType.Common || x.Difficulty == (int)difficultyType));
            return dataList.GetRandomObject(RGGameSceneManager.Inst.rg_random);
        }
        
        /// <summary>
        /// 返回题目答案
        /// </summary>
        /// <param name="questionConfig"></param>
        /// <returns></returns>
        public Dictionary<int, bool> GetQuestionAnswers(ActivitySchoolQuestionConfig questionConfig) {
            Dictionary<int, bool> dataDic = new Dictionary<int, bool>();
            var option0Arr = questionConfig.Option0.Split("#");
            var option1Arr = questionConfig.Option1.Split("#");
            var option2Arr = questionConfig.Option2.Split("#");
            var option3Arr = questionConfig.Option3.Split("#");
            var anwer0 = AnswerConfig.DataList.FindLast(
                x => x.Id == int.Parse(option0Arr[0], CultureInfo.InvariantCulture));
            var anwer1 = AnswerConfig.DataList.FindLast(
                x => x.Id == int.Parse(option1Arr[0], CultureInfo.InvariantCulture));
            var anwer2 = AnswerConfig.DataList.FindLast(
                x => x.Id == int.Parse(option2Arr[0], CultureInfo.InvariantCulture));
            var anwer3 = AnswerConfig.DataList.FindLast(
                x => x.Id == int.Parse(option3Arr[0], CultureInfo.InvariantCulture));
            dataDic.Add(anwer0.Id, option0Arr[1] == "1");
            dataDic.Add(anwer1.Id, option1Arr[1] == "1");
            dataDic.Add(anwer2.Id, option2Arr[1] == "1");
            dataDic.Add(anwer3.Id, option3Arr[1] == "1");
            return dataDic;
        }

        public ActivitySchoolAnswerConfig GetAnswer(int id) {
            var answer = AnswerConfig.DataList.FindLast(x => x.Id == id);
            return answer;
        }

        /// <summary>
        /// 题目奖励配置
        /// 返回1. you_choice，2具体某一个可发放的奖励
        /// </summary>
        /// <param name="questionConfig"></param>
        /// <returns></returns>
        public ActivitySchoolBonusConfig GetQuestionBonusConfig(ActivitySchoolQuestionConfig questionConfig) {
            ActivitySchoolBonusConfig bonusConfig = null;
            var bonusId = questionConfig.BonusId;
            Debug.Log("bonusId: " + bonusId + " questionConfig:" + questionConfig);
            if (!string.IsNullOrEmpty(bonusId)) {
                // 有指定奖励
                bonusConfig = BonusConfig.DataList.FindLast(x => x.Id == int.Parse(bonusId));
                // 进一步判断是否是随机奖励
                if (bonusConfig != null) {
                    if (bonusConfig.BonusKey == "you_choice") {
                        // 奖励是选项本身
                    } else if (bonusConfig.BonusKey.StartsWith("random_")) {
                        // 随机奖励
                        var bonusType = bonusConfig.BonusKey.Replace("random_", "");
                        var allTypeBonus = BonusConfig.DataList.FindAll(x => x.Type == int.Parse(bonusType));
                        bonusConfig = GetRandomBonusByWeight(allTypeBonus);
                    }
                }
            } 
            if (bonusConfig == null) {
                // 没有指定奖励，则从奖励配置中随机
                bonusConfig = GetRandomBonusByWeight(BonusConfig.DataList);
            }

            return bonusConfig;
        }
        
        public ActivitySchoolBonusConfig GetRandomBonusByWeight(List<ActivitySchoolBonusConfig> allTypeBonus) {
            int totalWeight = 0;
            foreach (var bonus in allTypeBonus) {
                totalWeight += bonus.Weight;
            }

            int randomNumber = RGGameSceneManager.Inst.rg_random.Range(0, totalWeight);
            foreach (var bonus in allTypeBonus) {
                randomNumber -= bonus.Weight;
                if (randomNumber < 0) {
                    return bonus;
                }
            }

            return null; // In case allTypeBonus is empty
        }
        
        public void ShowBuffOnUI(SchoolBuff buff) {
            switch (buff) {
                case SchoolBuff.SchoolScience:
                    AddBuff<Buff_SchoolScience>(buff, RGGameSceneManager.Inst.controller);
                    break;
                case SchoolBuff.SchoolWord:
                    AddBuff<Buff_SchoolWord>(buff, RGGameSceneManager.Inst.controller);
                    break;
                case SchoolBuff.SchoolSport:
                    AddBuff<Buff_SchoolSport>(buff, RGGameSceneManager.Inst.controller);
                    break;
                case SchoolBuff.SchoolArt:
                    AddBuff<Buff_SchoolArt>(buff, RGGameSceneManager.Inst.controller);
                    break;
            }
            
        }

        void AddBuff<T>(SchoolBuff buff, RGController controller) where T : MonoBehaviour {
            var existBuff = controller.gameObject.GetComponentInChildren<T>(true);
            if (existBuff != null) {
                controller.buffMgr.RemoveUtilBuff((int)CommonBuffTypeID.CommonReserve1 + (int)buff);
            }
            
            var skill1StateDisplayInfo = new BuffDisplayBasicInfo {
                innerIcon = SpriteMap.activitySprite.GetSprite(buff.ToString()),
                defaultDesc = GetBuffDesc(buff)
            };
            skill1StateDisplayInfo.SetCounterGetter(new FuncObj(this, nameof(GetBuffCount), buff));
            var uBuff = UtilBuff.Create((int)CommonBuffTypeID.CommonReserve1 + (int)buff, skill1StateDisplayInfo);
            uBuff.hideTimeDisplay = true;
            uBuff.gameObject.AddComponent<T>();
            var tmpBuff = RGBuff.AddBuff(uBuff, controller.gameObject, controller.transform);
            tmpBuff.RefreshTime(int.MaxValue, true);
        }
        
        public int GetBuffCount(SchoolBuff buff) {
            var count = BattleData.data.GetMark(buff.ToString());
            return count;
        }

        public string GetQuestionEnemyName(SchoolQuestionType questionType) {
            string enemyName = "";
            switch (questionType) {
                case SchoolQuestionType.Science:
                    enemyName = "e_science";
                    break;
                case SchoolQuestionType.Word:
                    enemyName = "e_literature";
                    break;
                case SchoolQuestionType.Sport:
                    enemyName = "e_sports";
                    break;
                case SchoolQuestionType.Art:
                    enemyName = "e_arts";
                    break;
            }

            return enemyName;
        }
        
        public string GetBuffDesc(SchoolBuff buff) {
            string desc = "activity/school/";
            string key = "activity/school/";
            switch (buff) {
                case SchoolBuff.SchoolScience:
                    desc += "buff_science_intro";
                    key += "buff_science";
                    break;
                case SchoolBuff.SchoolWord:
                    desc += "buff_literature_intro";
                    key += "buff_literature";
                    break;
                case SchoolBuff.SchoolSport:
                    desc += "buff_sports_intro";
                    key += "buff_sports";
                    break;
                case SchoolBuff.SchoolArt:
                    desc += "buff_arts_intro";
                    key += "buff_arts";
                    break;
            }

            return string.Format(ScriptLocalization.Get(desc), ScriptLocalization.Get(key));
        }
        
        public string GetQuestionBuffKey(SchoolQuestionType questionType) {
            string key = "";
            switch (questionType) {
                case SchoolQuestionType.Science:
                    key = "buff_science";
                    break;
                case SchoolQuestionType.Word:
                    key = "buff_literature";
                    break;
                case SchoolQuestionType.Sport:
                    key = "buff_sports";
                    break;
                case SchoolQuestionType.Art:
                    key = "buff_arts";
                    break;
            }

            return key;
        }

        public IEnumerator GotBuff(SchoolQuestionType questionType, int buffCount = 1) {
            yield return new WaitForSeconds(1.5f);

            string descKey = DataMgr.ActivitySchoolData.GetQuestionBuffKey(questionType);
            var talk = ScriptLocalization.Get($"activity/school/{descKey}", "#理学领悟") + "Lv+" + buffCount;
            ActivitySchoolManager.AddBuffInGame(questionType, buffCount);
            UICanvas.Inst.ShowTextTalk(RGGameSceneManager.Inst.controller.transform, talk, 2.5f, 1.5f);

            yield return new WaitForSeconds(1.5f);
            
            // 不管对错，都会获得金币
            int coinCount = 18;
            ActivitySchoolManager.AddActivityCoin(coinCount);
            talk = ScriptLocalization.Get("material_activity_coin_school", "#学霸金币") + "+" + coinCount;
            UICanvas.Inst.ShowTextTalk(RGGameSceneManager.Inst.controller.transform, talk, 2.5f, 1.5f);

            yield return new WaitForSeconds(0.25f);
        }
    }
}
