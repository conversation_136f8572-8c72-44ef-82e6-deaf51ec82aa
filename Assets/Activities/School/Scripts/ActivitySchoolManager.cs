using cfg.Activity;
using Cicada;
using EnemyGenerator;
using EnemySystem;
using MapSystem;
using RGScript.Config.Manager;
using RGScript.Data;
using RGScript.Map;
using RGScript.Util.NewConfigs;
using System;
using System.Collections.Generic;
using System.Linq;
using UIFramework;
using UnityCommon.UI;
using UnityEngine;
using Random = UnityEngine.Random;

namespace Activities.School.Scripts {
    /// <summary>
    /// 元气学院
    /// https://chillyroom.feishu.cn/wiki/Zl2owBIIuibWaKkCtxJcIDUOnv6
    /// </summary>
    // ReSharper disable once RedundantAttributeSuffix
    [RGActivity]
    public class ActivitySchoolManager {
        public static string TAG = "ActivitySchoolManager";
        public static string ReturnKey = "202508";
        public const string NPC_PATH = "Activities/School/Prefabs/npc_guide.prefab";
        public static string MatName = "material_activity_coin_school_1";
        public static string GUIDE_VIEW = "Activities/School/Prefabs/GuideView.prefab";
        public static string BATTLE_INFO = "Activities/School/Prefabs/window_battle_info.prefab";

        private static string TRACK_PREFIX = "ActivitySchool_";
        public static bool Inited = false; // 避免重复初始化重复监听
        public static ActivityConfig Config => DataMgr.ActivityConfigData.GetActivityConfig(TAG);
        private static bool InDebugMode => LogUtil.ShowDebugLog || GameConfigManager.GetInstance().Config.IsOpenLua;
        private static SchoolQuestionType _questionType = SchoolQuestionType.Science; // 理科，文科，体育，艺术

        public static string ExpKey => TAG + "_EXP" + ReturnKey;
        private const int MAX_COIN_ONE_GAME = 1600;
        private static int _gotCoin = 0;
        private static Dictionary<string, SchoolQuestionType> _roomQuestionDic = new Dictionary<string, SchoolQuestionType>();
        private static string _npcRoomName;
        private static List<string> _lastRoomNames = new List<string>();

        #region 加入活动统一UI入口界面所必须得属性，方法

        public static int Priority = 2;
        public static string TitleKey = "activity/school/title";
        public static string TitleFallback = "元气学院";
        public static string WidgetPath = "Activities/School/Prefabs/widget_activity.prefab";

        public static bool NeedPop() {
            return false;
        }

        public static bool ShouldShowRedPoint() {
            var tabIdx = PlayerSaveData.GetInt("school_tab_index", 0);
            return tabIdx > 0;
        }

        public static void RecordPopCount() {
        }

        /// <summary>
        /// item是否是活动奖励
        /// </summary>
        /// <param name="itemName"></param>
        /// <returns></returns>
        public static bool IsItemReward(string itemName) {
            return false;
        }

        #endregion

        public static void Initialize() {
#if UNITY_EDITOR
            // 使用本地配置
            if (UnityEditor.EditorPrefs.GetBool("DontFetchConfig")) {
                DoAfterConfigReady(null);
                SimpleEventManager.AddEventListener<BeforeSelfEnterHeroRoomEvent>(BeforeEnterHeroRoom);
                return;
            }
#endif
            if (NetTime.GotNetworkTime) {
                DoAfterConfigReady(null);
            } else {
                NetTime.GetNetTimeCallback += OnNetTimeReady;
            }

            SimpleEventManager.AddEventListener<BeforeSelfEnterHeroRoomEvent>(BeforeEnterHeroRoom);
        }

        private static void OnNetTimeReady(bool canUseNetTime) {
            if (canUseNetTime) {
                DoAfterConfigReady(null);
            }
        }
        /// <summary>
        /// 初始化后框架标记一下，避免重复执行
        /// </summary>
        public static void SetInited() {
            Inited = true;
        }

        public static void DoAfterConfigReady(NetConfigIsReadyEvent e) {
            
        }

        public static void AfterLoadAbFinished() {
            if (Inited) return;
            if (!IsAvailable()) return;
            var theType = typeof(ActivitySchoolManager);
            SimpleEventManager.Raise(ActivityReady.UseCache(theType));
            SimpleEventManager.AddEventListener<SelectHeroEvent>(OnSelectHeroEvent);
            SimpleEventManager.AddEventListener<EnterModeEvent>(OnEnterGame);
            SimpleEventManager.AddEventListener<EnterGameEvent>(OnContinueGame);
            SimpleEventManager.AddEventListener<AfterCreatePlayerEvent>(OnCreatedPlayer);
            SimpleEventManager.AddEventListener<EnterGameStatement>(OnEnterGameStatement);
            FuncHotFix();
            DataMgr.ActivitySchoolData.InitTask();
        }

        
        private static void OnSelectHeroEvent(SelectHeroEvent e) {
            if (Inited && IsAvailable()) {
                InitNpc();
                InitDifficulty();
            }
        }

        /// <summary>
        /// 每次进入大厅前刷新下活动状态
        /// </summary>
        /// <param name="e"></param>
        private static void BeforeEnterHeroRoom(BeforeSelfEnterHeroRoomEvent e) {
            DoAfterConfigReady(null);
        }

        public static void FuncHotFix(params object[] objects) {
        }

        /// <summary>
        /// 活动开启条件
        /// </summary>
        /// <returns></returns>
        public static bool IsAvailable() {
            var inPeriod = IsInActivityPeriod();
            //var abingUnlocked = StatisticData.data.IsEventRecord("abing_unlock_intro");
            var isGardenUnlocked = RoomItemUnlockThroughBattle.GetUnlock(emRoomUnlockItem.garden);
            var isSingle = GameUtil.IsSingleGame();
            if (LogUtil.IsShowLog) {
                LogUtil.Log($"IsAvailable isSingle:{isSingle} inPeriod:{inPeriod} isGardenUnlocked:{isGardenUnlocked}", TAG);
            }

            return inPeriod && isGardenUnlocked && isSingle;
        }

        public static string StartTime() {
            return ActivityUtil.StartTime(TAG);
        }

        public static string EndTime() {
            return ActivityUtil.EndTime(TAG);
        }

        public static DateTime GetEndDate() {
            return ActivityUtil.GetEndDate(TAG);
        }

        public static int GetActivityCoinCount() {
            return ItemData.data.GetMaterialCount(MatName);
        }

        /// <summary>
        /// 战斗中获取的货币添加逻辑
        /// </summary>
        /// <param name="num"></param>
        /// <param name="showRewardWindow"></param>
        /// <param name="controller"></param>
        public static void AddActivityCoin(int num = 1, bool showRewardWindow = false,
            RGController controller = null) {
            ActivityUtil.AddActivityCoin(MatName, num, showRewardWindow, controller);
        }
        
        /// <summary>
        /// 经验值，与货币1:1
        /// </summary>
        /// <returns></returns>
        public static int GetExp() {
            var obtainCount = StatisticData.data.GetObtainTime(ExpKey);
            return obtainCount;
        }

        public static void AddExp(int count) {
            if (count > 0) {
                StatisticData.data.AddObtainTimes(ExpKey, count, true);
            }
        }

        private static void OnEnterGameStatement(EnterGameStatement e) {
            if (!IsAvailable() || !IsActivityOn()) {
                return;
            }

            AddCoin();
            StatData();
        }

        private static void StatData() {
            var difficulty = GetDifficulty();
            var today = ActivityUtil.GetNetToday();
            var reachBigLevel = MapManager.GetSceneIndex(
                BattleData.data.levelIndex,
                BattleData.data.IsBossRushMode,
            BattleData.data.CompareFactor(emBattleFactor.Loop),
                true, BattleData.data);
            StatisticData.data.AddEventCount(ReturnKey + $"{today}_school_game_{difficulty}", 1, false);
            StatisticData.data.AddEventCount(ReturnKey + $"{today}_school_game_{difficulty}_{reachBigLevel}", 1, false);
            if (BattleData.data.IsLocalLevelIndexPass) {
                StatisticData.data.AddEventCount(ReturnKey + $"school_game_pass_{difficulty}", 1, false);
            }

            var totalQuestion = Mathf.Max(reachBigLevel, 3);
            
            var correctCount = GetAnswerCorrectTotal();
            if (correctCount >= totalQuestion) {
                StatisticData.data.AddEventCount(ReturnKey + "answer_correct_all", 1, false);
            }

            foreach (var correctPair in BattleData.data.answerCorrectOneGameDic) {
                var q = correctPair.Key;
                int count = correctPair.Value;
                StatisticData.data.AddEventCount(ReturnKey +$"correct_{q}", count, false);
                StatisticData.data.AddEventCount(ReturnKey +$"{today}_correct_{q}", count, false);

                // 单局内历史记录取答对次数多的
                var key = ReturnKey + $"{today}_onegame_correct_{q}";
                int oldCount = StatisticData.data.GetEventCount(key);
                if (oldCount > 0) {
                    if (oldCount < count) {
                        StatisticData.data.SetEventCount(key, count, false);
                    }
                } else {
                    StatisticData.data.AddEventCount(key, count, false);
                }
            }
            foreach (var wrongPair in BattleData.data.answerWrongOneGameDic) {
                StatisticData.data.AddEventCount(ReturnKey +$"wrong_{wrongPair.Key}", wrongPair.Value, false);
            }

            if (BattleData.data.killBossCountOneGame > 0) {
                StatisticData.data.AddEventCount(ReturnKey +$"{today}_kill_boss_{difficulty}", BattleData.data.killBossCountOneGame, false);
            }
            
            // 记录单局内的buffs叠加情况，记录最大值
            for (var buff = SchoolBuff.SchoolScience; buff <= SchoolBuff.SchoolArt; buff++) {
                // total task
                var totalTaskKey = $"buff_{buff}";
                var oldMax = StatisticData.data.GetEventCount(totalTaskKey);
                var count = BattleData.data.GetMark(buff.ToString());
                if (count > oldMax) {
                    StatisticData.data.SetEventCount(totalTaskKey, count, false);
                }
                
                // today task
                var todayTaskKey = $"{today}_buff_{buff}";
                var oldTodayMax = StatisticData.data.GetEventCount(todayTaskKey);
                if (count > oldTodayMax) {
                    StatisticData.data.SetEventCount(todayTaskKey, count, false);
                }
            }
            
            TrackTasks();
        }

        private static void TrackTasks() {
            foreach (var task in _beginTasksProgress.Keys) {
                if (task.GetTaskStatus() == SchoolTaskStatus.Finish) {
                    // 任务完成
                    TAUtil.Track("school_task_change", new Dictionary<string, object>() {
                        { "task_status", "已完成" },
                        { "task_name", task.GetTaskName(true) },
                    });
                }
                int oldCount = _beginTasksProgress[task];
                int newCount = DataMgr.ActivitySchoolData.GetTaskCompleteCount(task);
                if (newCount > oldCount) {
                    // 有进度变化
                    TAUtil.Track("school_task_progress", new Dictionary<string, object>() {
                        { "task_name", task.GetTaskName(true) },
                        { "progress", $"{newCount}/{task.completeCount}" },
                        { "difficulty", (int)GetDifficulty() },
                    });

                    if (task.GetTaskStatus() == SchoolTaskStatus.Finish) {
                        // 新完成的任务，红点提醒
                        PlayerSaveData.SetInt("school_tab_index", 1);
                    }
                }
            }
        }
        
        private static Dictionary<SchoolTask, int> _beginTasksProgress = new Dictionary<SchoolTask, int>();
        /// <summary>
        /// 开始游戏前检查任务状态，以便前后对比上报埋点
        /// </summary>
        private static Dictionary<SchoolTask, int> CheckTasks() {
            Dictionary<SchoolTask, int> tmpDic = new Dictionary<SchoolTask, int>();
            var tasks = DataMgr.ActivitySchoolData.GetDay3Tasks();
            var fixedTasks = DataMgr.ActivitySchoolData.GetFixedTasks();
            tasks.AddRange(fixedTasks);
            
            for (var i = 0; i < tasks.Count; i++) {
                var task = tasks[i];
                var taskStatus = task.GetTaskStatus();
                if (taskStatus == SchoolTaskStatus.UnFinish) {
                    var count = DataMgr.ActivitySchoolData.GetTaskCompleteCount(task);
                    tmpDic.Add(task, count);
                }
            }

            return tmpDic;
        }

        private static void AddCoin() {
            if (_gotCoin > 0) {
                Debug.LogError("duplicate addCoin!!! ignore..");
                return;
            }

            if (_gotCoin >= MAX_COIN_ONE_GAME) {
                Debug.LogError("reach max of " + MAX_COIN_ONE_GAME);
                return;
            }

            var level = BattleData.data.levelIndex;
            var addCoin = 0;
            for (var i = 1; i <= level; i++) {
                if (i <= 5) {
                    addCoin += 8;
                } else if (i <= 10) {
                    addCoin += 10;
                } else if (i <= 15) {
                    addCoin += 13;
                } else if (i <= 20) {
                    addCoin += 18;
                } else {
                    addCoin += 25;
                }
            }
            
            // 加上答题 + 击杀精英怪获得
            var extraCount = BattleData.data.gotPickables.Sum(pickable => pickable.name == MatName ? pickable.count : 0);
            addCoin += extraCount;

            // 难度加成
            var difficulty = GetDifficulty();
            switch (difficulty) {
                case SchoolDifficultyType.Primary:
                    break;
                case SchoolDifficultyType.Middle:
                    addCoin = (int)(addCoin * 1.1f);
                    break;
                case SchoolDifficultyType.High:
                    addCoin = (int)(addCoin * 1.2f);
                    break;
                case SchoolDifficultyType.Final:
                    addCoin = (int)(addCoin * 1.5f);
                    break;
            }

            addCoin = Mathf.Min(MAX_COIN_ONE_GAME, addCoin);
            _gotCoin = addCoin;

            // 这里需要减去extraCount部分，其已加入pickables结构中
            var newCount = Mathf.Max(addCoin - extraCount, 0);
            var playTime = StatisticData.data.GetToDayActivityPlayCount(MatName);
            if (newCount > 0) {
                if (playTime < 3) {
                    newCount *= 2;
                    StatisticData.data.AddToDayActivityPlayCount(MatName);
                    BattleData.data.SetMark(RGGameConst.TodayActivityDouble, 1);
                }
            }
            if (newCount > 0) {
                PickableInfo info = new PickableInfo() {
                    count = newCount,
                    name = MatName,
                    itemType = emItemType.Material,
                };
                BattleData.data.AddPickables(new List<PickableInfo>() { info });
            }
            
            // 1:1转换为经验值，加上答题等部分的值
            AddExp(addCoin);
        }

        /// <summary>
        /// 活动有效期
        /// </summary>
        /// <returns></returns>
        public static bool IsInActivityPeriod() {
            var sTime = StartTime();
            var eTime = EndTime();
            var time = ActivityUtil.GetNetworkTime();
            if (string.IsNullOrEmpty(sTime) || string.IsNullOrEmpty(eTime) || time == null) {
                if (LogUtil.IsShowLog) {
                    LogUtil.Log(
                        "!!! net.time:" + time + " start:" + sTime + " end:" + eTime + " => false", TAG);
                }

                return false;
            }

            var startTime = TimeUtil.ChineseTimestampToLocalDateTime(sTime);
            var endTime = TimeUtil.ChineseTimestampToLocalDateTime(eTime);
            var inPeriod = time >= startTime && time < endTime;
            if (LogUtil.IsShowLog) {
                LogUtil.Log(
                    "net.time:" + time + " activity.start:" + startTime + " activity.end:" + endTime + " => " +
                    inPeriod, TAG);
            }

            return inPeriod;
        }

        private static void InitNpc() {
            try {
                var npc = GameObject.Instantiate(ResourcesUtil.Load<GameObject>(NPC_PATH),
                    RoomObjectManager.Inst.transform);
                npc.transform.position = RGGameConst.ActivityNpcSlot;
            } catch (Exception e) {
                Debug.LogError(e);
            }
        }

        public static BaseUIView ShowWindow() {
            return DataMgr.ActivityEntryData.ShowWindow(TAG);
        }

        public static void ShowInfo() {
            UIManager.Inst.OpenUIView<UISchoolBattleInfoWindow>(BATTLE_INFO);
        }

        public static void EnableActivity(bool enable, TrackOpenFrom from) {
            BattleData.data.ClearActivity(TAG);
            if (enable) {
                BattleData.data.EnableActivity(TAG);
                ActivityUtil.TrackOpenActivity(TAG, from);
            }
        }

        public static bool IsActivityOn() {
            return BattleData.data.HasActivityEnabled(TAG);
        }

        public static void SetDifficulty(int level) {
            BattleData.data.difficultyType = (SchoolDifficultyType)level;
            PlayerSaveData.Inst.LastSelectSchoolDifficulty = level;
        }

        public static SchoolDifficultyType GetDifficulty() {
            return BattleData.data.difficultyType;
        }
        
        public static bool IsEqualOrGreaterDifficulty(SchoolDifficultyType schoolDifficultyType) {
            return (int)BattleData.data.difficultyType >= (int)schoolDifficultyType;
        }
        
        /// <summary>
        /// 非普攻单人关卡模式进入游戏时清除活动因子
        /// </summary>
        /// <param name="e"></param>
        private static void OnEnterGame(EnterModeEvent e) {
            if (e.game_mode == emGameMode.Normal) {
                OpenFeature();
            }
        }
        
        /// <summary>
        /// 继续游戏
        /// </summary>
        /// <param name="e"></param>
        private static void OnContinueGame(EnterGameEvent e) {
            OpenFeature();
        }

        public static void OpenFeature() {
            if (!IsActivityOn()) {
                RemoveListeners();
                return;
            }

            var isNormalGameSingle = BattleData.data.gameMode == emGameMode.Normal &&
                                     !BattleData.data.CompareFactor(emBattleFactor.Loop) &&
                                     GameUtil.IsSingleGame();
            if (!isNormalGameSingle) {
                BattleData.data.ClearActivity(TAG);
                RemoveListeners();
                if (LogUtil.IsShowLog) {
                    LogUtil.Log("非关卡模式，清除活动因子", TAG);
                }

                return;
            }
            
            _gotCoin = 0;
            _npcRoomName = "";
            _lastRoomNames.Clear();
            _roomQuestionDic.Clear();
            InitRandomQuestionType();
            AddInitBuffs();
            AddEventListeners();
            _beginTasksProgress = new Dictionary<SchoolTask, int>(CheckTasks());
        }

        /// <summary>
        /// 未手动设置之前，根据经验值设置难度
        /// </summary>
        private static void InitDifficulty() {
            var cacheDifficulty = PlayerSaveData.Inst.LastSelectSchoolDifficulty;
            if (cacheDifficulty > 0) {
                BattleData.data.difficultyType = (SchoolDifficultyType)cacheDifficulty;
                return;
            }
            
            var (difficultyType, _) = DataMgr.ActivitySchoolData.GetProgress();
            BattleData.data.difficultyType = difficultyType;
        }

        private static void InitRandomQuestionType() {
            var idx = Random.Range(1, (int)SchoolQuestionType.Count);
            _questionType = (SchoolQuestionType)idx;
            
#if UNITY_EDITOR
            var qType = UnityEditor.EditorPrefs.GetString("Fix_QuestionType", null);
            if (qType != null && Enum.TryParse(qType, out SchoolQuestionType qT)) {
                LogUtil.Log($"使用了调试工具设置的Npc类型：" + qT);
                _questionType = qT;
            }
            LogUtil.Log($"random questionType:{_questionType}", TAG);
#endif
        }
        
        public static SchoolQuestionType GetQuestionType() {
            return _questionType;
        }

        private static void AddEventListeners() {
            RemoveListeners();
            SimpleEventManager.AddEventListener<BossDeadEvent>(OnBossDead);
            SimpleEventManager.AddEventListener<CreatePartEnemyEvent>(OnCreatePartEnemy);
            SimpleEventManager.AddEventListener<EnterTransferGateEvent>(OnEnterTransferGate);
            SimpleEventManager.AddEventListener<CharacterRebornEvent>(OnReborn);
            SimpleEventManager.AddEventListener<CreateEnemyEvent>(OnCreatedEnemy);
            SimpleEventManager.AddEventListener<BossCreateEvent>(OnBossCreate);
        }

        /// <summary>
        /// 进入下一大关的1-0，随机作业类型
        /// </summary>
        /// <param name="e"></param>
        private static void OnEnterTransferGate(EnterTransferGateEvent e) {
            if ((BattleData.data.levelIndex + 1) % 5 == 0 && !BattleData.data.IsPassGame) {
                InitRandomQuestionType();
            }
        }

        private static void RemoveListeners() {
            SimpleEventManager.RemoveListener<BossDeadEvent>(OnBossDead);
            SimpleEventManager.RemoveListener<CreatePartEnemyEvent>(OnCreatePartEnemy);
            SimpleEventManager.RemoveListener<EnterTransferGateEvent>(OnEnterTransferGate);
            SimpleEventManager.RemoveListener<CharacterRebornEvent>(OnReborn);
            SimpleEventManager.RemoveListener<CreateEnemyEvent>(OnCreatedEnemy);
            SimpleEventManager.RemoveListener<BossCreateEvent>(OnBossCreate);
        }

        private static List<string> _enemyIds = new List<string>() {
            "e_science",
            "e_literature",
            "e_sports",
            "e_arts",
        };
        
        public static bool IsCustomEnemy(string enemyName) {
            foreach (var enemyId in _enemyIds) {
                if (enemyName.Contains(enemyId)) {
                    return true;
                }
            }

            return false;
        }

        private static float _hpAddByDifficulty = 0.4f;
        private static float _atkSpeedAddByDifficulty = 0.25f;
        private static float _bossBuffRate = 0.5f;
        /// <summary>
        /// 这里处理非活动敌人属性调整
        /// </summary>
        /// <param name="e"></param>
        private static void OnCreatedEnemy(CreateEnemyEvent e) {
            if (IsCustomEnemy(e.enemy.name)) {
                return;
            }
            
            var enemyCtrl = e.enemy.GetComponent<RGEController>();
            // EDIT ZC 修正boss血量 BOSS只能吃到_bossBuffRate倍的增益
            float buffRate = 1;
            buffRate = enemyCtrl.isBoss ? _bossBuffRate : 1;
            
            var attribute = enemyCtrl.attribute;
            var difficulty = GetDifficulty();
            if (difficulty == SchoolDifficultyType.Final) {
                difficulty = SchoolDifficultyType.High;
            }
            
            var speedRate = Mathf.Max((int)difficulty - 1, 0) * _atkSpeedAddByDifficulty * buffRate + 1;
            enemyCtrl.shoot_cd /= speedRate;
            enemyCtrl.scout_rate /= speedRate;
            
            var factor = Mathf.Max((int)difficulty - 1, 0) * _hpAddByDifficulty * buffRate + 1;
            enemyCtrl.Attribute.MaxHpValue.AddMultiplicationValue("buff_difficulty", factor);
            enemyCtrl.Attribute.hp = enemyCtrl.Attribute.MaxHpValue;
            enemyCtrl.Attribute.HpChanged();
            if (enemyCtrl is RGEBossController { } rgeBossController && rgeBossController.boss_info) {
                rgeBossController.role_attribute.max_hp = enemyCtrl.Attribute.MaxHpValue;
                rgeBossController.boss_info.UpdateBossMaxHp();
            }
        }

        /// <summary>
        /// 4个大关额外敌人生成次数
        /// </summary>
        private static List<int> _levelExtraEnemyTimes = new List<int>() {
            1, 2, 3, 3
        };

        public static bool NeedGenExtraEnemy() {
            var currentBigLevel = BattleData.data.levelIndex / 5;
            var currIdx = currentBigLevel;
            var levelIdx = BattleData.data.levelIndex % 5;
            var leftLevelCount = 5 - levelIdx;
            currIdx = Mathf.Max(currIdx, 0);
            
            if (BattleData.data.schoolExtraEnemyTimes.Count == 0) {
                BattleData.data.schoolExtraEnemyTimes = new List<int>(_levelExtraEnemyTimes);
            }
            
            var leftTime = BattleData.data.schoolExtraEnemyTimes[currIdx];
            var rate = (leftTime * 1f / leftLevelCount) * 100;
            var ok = RGGameSceneManager.Inst.rg_random.Range(1, 100) <= rate;

            if (LogUtil.IsShowLog) {
                LogUtil.Log($"currentBigLevel:{currentBigLevel} " +
                            $"currIdx:{currIdx} " +
                            $"leftTime:{leftTime} " +
                            $"leftLevelCount:{leftLevelCount} " +
                            $"rate:{rate} " +
                            $"addEnemy:{ok} " +
                            $"levelIndex:{BattleData.data.levelIndex}", TAG);
            }

            if (ok) {
                BattleData.data.schoolExtraEnemyTimes[currIdx] -= 1;
            }

            return ok;
        }
        
        private static void OnBossCreate(BossCreateEvent e) {
            var theRoom = e.bossInstance.transform.parent.parent.GetComponent<RGRoomX>();
            var key = GetRoomKey(theRoom.gameObject.name);
            if (_lastRoomNames.Contains(key)) {
                Debug.LogError($"此房间已生成过精英怪了，忽略:{theRoom.gameObject.name} cache:{string.Join(",", _lastRoomNames)}");
                return;
            }

            _lastRoomNames.Add(key);
            var difficulty = GetDifficulty();
            if (difficulty == SchoolDifficultyType.Final) {
                // 最高难度Boss房间，随机一个精英怪
                var questionType = (SchoolQuestionType)theRoom.rg_random.Range(1, 5);
                string enemyName = DataMgr.ActivitySchoolData.GetQuestionEnemyName(questionType);
                AddEnemy(enemyName, theRoom, false, false);
            }
        }

        private static void OnCreatePartEnemy(CreatePartEnemyEvent e) {
            if (e.theRoom == null) return;

            var roomName = e.theRoom.gameObject.name;
            var roomQuestion = GetRoomQuestion(roomName);
            if (roomQuestion == SchoolQuestionType.None) {
                return;
            }
            
            var key = GetRoomKey(roomName);
            if (_lastRoomNames.Contains(key)) {
                return;
            }

            _lastRoomNames.Add(key);
            string enemyName = DataMgr.ActivitySchoolData.GetQuestionEnemyName(roomQuestion);

            int count = 1;
            var difficulty = GetDifficulty();
            if (difficulty == SchoolDifficultyType.Final) {
                count = 2;
            }

            bool dropBonus = true;
            for (var i = 0; i < count; i++) {
                AddEnemy(enemyName, e.theRoom, false, dropBonus);
                dropBonus = false;
            }
        }

        public static RGEController AddEnemy(string enemyName, RGRoomX theRoom, bool fromNpc = false, bool dropBonus = true) {
            var proto = ResourcesUtil.Load<GameObject>($"Activities/School/Prefabs/Enemy/{enemyName}.prefab");
            var enemyCtrl = EnemyFactory.CreateEnemy(proto, 0, theRoom.transform.Find("enemy_group"));
            // EDIT ZC 活动精英怪，优先放置在房间中央。若中央有障碍物，则随机放在空位上
            Vector3 finalPos = Vector3.zero;
            Vector2Int roomGridCenter = theRoom.Grid.WorldPos2GridPos(theRoom.transform.position);
            if (!theRoom.Grid.CanSetObjectAt(enemyCtrl, roomGridCenter,true) || !dropBonus) {// boss房或复制体 dropBonus=false
                var placementInfo = new RoomObjectPlacementInfo(proto);
                theRoom.Grid.GetPlacementInfo(placementInfo, theRoom.rg_random,true);
                finalPos = placementInfo.CanBePlaced ? placementInfo.LocalPos : Vector3.zero;; // 如果没找到合适空位,还是放在原点
            } else {
                finalPos = Vector3.zero;
            }
            enemyCtrl.transform.localPosition = finalPos;
            
            enemyCtrl.enemyName = proto.name;
            enemyCtrl.the_maker = theRoom.gameObject.GetComponent<EnemyMaker>();
            enemyCtrl.freeInPart = true;
            if (fromNpc) {
                enemyCtrl.start_awake = true;
                enemyCtrl.transform.GetComponent<EnemySchoolBrain>().npcEnemy = true;
            }
            
            enemyCtrl.transform.GetComponent<EnemySchoolBrain>().dropBonus = dropBonus;
            // EDIT ZC 热更修正精英怪的血量
            if (enemyCtrl.transform.TryGetComponent(out EnemySchoolBrain brain)) {
                brain.levelUp = 0.8f; // 血量随关卡上升
                brain.difficultyUp = 0.4f; // 血量随难度上升
                brain.speedUp = 0.25f; // 攻击频率上升
            }
            enemyCtrl.SetAsExtraEnemyOfMaker();

            return enemyCtrl;
        }
        
        private static void OnBossDead(BossDeadEvent e) {
            BattleData.data.killBossCountOneGame++;
        }

        /// <summary>
        /// 初始buff
        /// </summary>
        /// <param name="e"></param>
        private static void OnCreatedPlayer(AfterCreatePlayerEvent e) {
            if (!IsActivityOn()) return;
            var isNormalGameSingle = BattleData.data.gameMode == emGameMode.Normal &&
                                     !BattleData.data.CompareFactor(emBattleFactor.Loop) &&
                                     GameUtil.IsSingleGame();
            if (!isNormalGameSingle) {
                return;
            }

            ShowBuffs();
            InitSkillUpgrade();
        }
        
        private static void OnReborn(CharacterRebornEvent e) {
            if (!IsActivityOn()) return;
            
            ShowBuffs();
        }

        // 导师强化 + 技能升级
        private static void InitSkillUpgrade() {
            var controller = RGGameSceneManager.Inst.controller;
            if (controller.SkillExtraUpdate != 1) {
                BattleData.data.LearnBuffAddition(true, emBuff.Tutor, true);
            }
            
            var skillLevel = DataMgr.ActivitySchoolData.GetProgress2SkillLevel();
            if (skillLevel > 0) {
                controller.attribute.SkillLevel.AddModifier(skillLevel, AttributeModifierType.Flat, TAG);
            }
        }

        private static void ShowBuffs() {
            var count0 = BattleData.data.GetMark(SchoolBuff.SchoolScience.ToString());
            var count1 = BattleData.data.GetMark(SchoolBuff.SchoolWord.ToString());
            var count2 = BattleData.data.GetMark(SchoolBuff.SchoolSport.ToString());
            var count3 = BattleData.data.GetMark(SchoolBuff.SchoolArt.ToString());
            if (count0 > 0) {
                DataMgr.ActivitySchoolData.ShowBuffOnUI(SchoolBuff.SchoolScience);
            }

            if (count1 > 0) {
                DataMgr.ActivitySchoolData.ShowBuffOnUI(SchoolBuff.SchoolWord);
            }

            if (count2 > 0) {
                DataMgr.ActivitySchoolData.ShowBuffOnUI(SchoolBuff.SchoolSport);
            }

            if (count3 > 0) {
                DataMgr.ActivitySchoolData.ShowBuffOnUI(SchoolBuff.SchoolArt);
            }
        }

        /// <summary>
        /// 进入地牢初始buff
        /// </summary>
        private static void AddInitBuffs() {
            var count0 = BattleData.data.GetMark(SchoolBuff.SchoolScience.ToString());
            var count1 = BattleData.data.GetMark(SchoolBuff.SchoolWord.ToString());
            var count2 = BattleData.data.GetMark(SchoolBuff.SchoolSport.ToString());
            var count3 = BattleData.data.GetMark(SchoolBuff.SchoolArt.ToString());
            
            // 继续游戏可能已经存档有加过了
            if (count0 + count1 + count2 + count3 > 0) {
                return;
            }
            
            int count = 1;
            switch (BattleData.data.difficultyType) {
                case SchoolDifficultyType.Primary:
                    count = 2;
                    break;
                case SchoolDifficultyType.Middle:
                    count = 4;
                    break;
                case SchoolDifficultyType.High:
                case SchoolDifficultyType.Final:
                    count = 6;
                    break;
            }
            
            Dictionary<SchoolBuff, int> tmpDic = new Dictionary<SchoolBuff, int>();
            for (var i = 0; i < count; i++) {
                SchoolBuff randomBuff = (SchoolBuff)Random.Range(0, (int)SchoolBuff.Count);
                if (!tmpDic.TryAdd(randomBuff, 1)) {
                    tmpDic[randomBuff]++;
                }
            }

            foreach (var buff in tmpDic.Keys) {
                AddBuff(buff, tmpDic[buff]);
            }
        }

        public static void AddBuffInGame(SchoolQuestionType questionType, int count) {
            SchoolBuff addBuff = SchoolBuff.SchoolScience;
            switch (questionType) {
                case SchoolQuestionType.Science:
                    addBuff = SchoolBuff.SchoolScience;
                    break;
                case SchoolQuestionType.Word:
                    addBuff = SchoolBuff.SchoolWord;
                    break;
                case SchoolQuestionType.Sport:
                    addBuff = SchoolBuff.SchoolSport;
                    break;
                case SchoolQuestionType.Art:
                    addBuff = SchoolBuff.SchoolArt;
                    break;
            }
            
            AddBuff(addBuff, count);
            DataMgr.ActivitySchoolData.ShowBuffOnUI(addBuff);
            SimpleEventManager.Raise(SchoolBuffAddEvent.UseCache(addBuff, count));
        }

        public static void AddBuff(SchoolBuff buff, int count) {
            BattleData.data.AddMark(buff.ToString(), count);

            if (LogUtil.IsShowLog) {
                LogUtil.Log($"AddBuff {buff} count:{count}", TAG);
            }
        }

        public static void ShowGuideView() {
            var guideView = UIManager.Inst.OpenUIView<CommonGuideView>(GUIDE_VIEW);
            guideView.AfterReadGuide = () => {
                if (!StatisticData.data.IsEventRecord(RGGameConst.OneOffEvent.ReadActivitySchoolGuide.ToString())) {
                    StatisticData.data.RecordEvent(RGGameConst.OneOffEvent.ReadActivitySchoolGuide.ToString(), true);
                    // 首次对话后上报
                    TAUtil.Track("activity_school_first_talk");
                }
            };
        }

        public static void AddAnswerRecord(SchoolQuestionType questionType, bool correct) {
            if (correct) {
                if (!BattleData.data.answerCorrectOneGameDic.TryAdd(questionType, 1)) {
                    BattleData.data.answerCorrectOneGameDic[questionType]++;
                }
            } else {
                if (!BattleData.data.answerWrongOneGameDic.TryAdd(questionType, 1)) {
                    BattleData.data.answerWrongOneGameDic[questionType]++;
                }
            }
        }
        public static int GetAnswerCorrectTotal() {
            return BattleData.data.answerCorrectOneGameDic.Sum(pair => pair.Value);
        }

        private static string GetRoomKey(string roomName) {
            return $"{roomName}_{BattleData.data.levelIndex}";
        }
        
        public static void AddRoomQuestion(string roomName, SchoolQuestionType questionType) {
            var key = GetRoomKey(roomName);
            if (!_roomQuestionDic.TryAdd(key, questionType)) {
                _roomQuestionDic[key] = questionType;
            }
        }

        public static SchoolQuestionType GetRoomQuestion(string roomName) {
            var key = GetRoomKey(roomName);
            if (_roomQuestionDic.ContainsKey(key)) {
                return _roomQuestionDic[key];
            }

            return SchoolQuestionType.None;
        }

        public static void SetNpcRoom(string roomName) {
            var key = GetRoomKey(roomName);
            _npcRoomName = key;
        }

        public static bool IsNpcRoom(string roomName) {
            return IsActivityOn() && _npcRoomName == GetRoomKey(roomName);
        }
    }
}