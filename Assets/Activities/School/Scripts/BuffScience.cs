using System.Collections.Generic;
using UnityEngine;

namespace Activities.School.Scripts {
    public class BuffScience : RGBuffSetMaterial {
        public Sprite[] numbers;
        public Sprite[] equas;
        public GameObject buff;

        private List<RGEController> _diedRGEController;
        private GameObject _sourceObject;
        private float _eleBuffTime;

        public void SetNumber(float eleBuffTime, int number, List<RGEController> diedRGEController) {
            transform.Find("icon").GetComponent<SpriteRenderer>().sprite = numbers[number];
            _diedRGEController = diedRGEController;
            _eleBuffTime = eleBuffTime;
        }

        public void SetEqua(float eleBuffTime, int number1, int equa, int number2, List<RGEController> diedRGEController) {
            transform.Find("icon").gameObject.SetActive(false);
            var equaObj = transform.Find("equa").gameObject;
            equaObj.SetActive(true);
            equaObj.transform.Find("1").GetComponent<SpriteRenderer>().sprite = numbers[number1];
            equaObj.transform.Find("2").GetComponent<SpriteRenderer>().sprite = equas[equa];
            equaObj.transform.Find("3").GetComponent<SpriteRenderer>().sprite = numbers[number2];
            _diedRGEController = diedRGEController;
            _eleBuffTime = eleBuffTime;
        }

        protected override void LateUpdate() {
            if (transform.parent == null) {
                return;
            }

            base.LateUpdate();
            transform.localRotation = transform.parent.localRotation;
        }

        public override void BuffEnd() {
            if (transform.parent != null && transform.parent.TryGetComponent<RGEController>(out var eController) &&
                eController.dead) {
                foreach (RGEController rgeController in _diedRGEController) {
                    if (rgeController == null || rgeController.dead) continue;

                    BuffEffectTrigger.EnemyAddBuff(_sourceObject, sourceWeapon, rgeController.gameObject, buff, -1, _eleBuffTime);
                    var bt = rgeController.transform.Find(gameObject.name);
                    if (bt != null) {
                        bt.GetComponent<BuffScience>().BuffEnd();
                    }
                }
            }
            
            base.BuffEnd();
        }
    }
}