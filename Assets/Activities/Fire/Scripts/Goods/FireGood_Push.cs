using RGScript.Data;
using System.Collections;
using UnityEngine;

namespace Activities.Fire.Scripts.Goods {
    /// <summary>
    /// 五个桃核豆奶
    /// 火力全开时，碰撞敌人会造成震荡波（自己不会受到震荡波的伤害）。
    /// </summary>
    public class FireGood_Push : FireGood {
        public float coolDown = 1;
        public GameObject _bulletProto;
        private bool _ready = false;
        private Coroutine _checkCollisionCoroutine;
        RaycastHit2D[] hit_list = new RaycastHit2D[1];
        private float _lastBoomTime;

        protected override void OnBuffStart() {
            base.OnBuffStart();
            _ready = true;
        }

        protected override void OnFireActive(RGController ctrl) {
            base.OnFireActive(ctrl);
            if (_ready) {
                BattleData.data.LearnBuffTemp(emBuff.ShieldSting, true);
                if (_checkCollisionCoroutine != null) {
                    StopCoroutine(_checkCollisionCoroutine);
                }

                _checkCollisionCoroutine = StartCoroutine(nameof(CheckCollisionCoroutine));
            }
        }

        protected override void OnFireUnActive(RGController ctrl) {
            base.OnFireUnActive(ctrl);
            if (_ready) {
                BattleData.data.ForgetBuffTemp(emBuff.ShieldSting);
                StopBuffCoroutine();
            }
        }
        
        protected override void OnDestroy() {
            StopBuffCoroutine();
            base.OnDestroy();
        }
        
        private void StopBuffCoroutine() {
            if (_checkCollisionCoroutine != null) {
                StopCoroutine(_checkCollisionCoroutine);
                _checkCollisionCoroutine = null;
            }
        }
        
        private IEnumerator CheckCollisionCoroutine() {
            var wait = new WaitForSeconds(0.5f);
            while (true) {
                CheckCollision();
                yield return wait;
            }
        }

        private void CheckCollision() {
            if (Time.time - _lastBoomTime < coolDown) {
                return;
            }
            
            int hitCount = Physics2D.CircleCastNonAlloc(controller.transform.position, 1.8f, Vector2.zero, hit_list, 0f,
                1 << LayerMask.NameToLayer("Body_E"));
            if (hitCount > 0) {
                GenBoom();
            }
        }

        void GenBoom() {
            var count = DataMgr.ActivityFireData.GetBuyGoodTime(good_name);
            var bulletSize = 1 + (count - 1) * 0.1f;
            var damage = 0;
            switch (count) {
                case 1:
                case 2:
                    damage = 3;
                    break;
                case 3:
                case 4:
                    damage = 4;
                    break;
                case 5:
                    damage = 5;
                    break;
            }
            var explode = BulletFactory.TakeBullet(
                new BulletInfo().SetUp(_bulletProto, controller.gameObject, 0, transform.position, 0, 1).SetBulletSize(bulletSize),
                new DamageInfo().SetUp(_bulletProto, damage, 0, damage, 1),
                true, RGGameSceneManager.Inst.temp_objects_parent);

            _lastBoomTime = Time.time;
        }
    }
}