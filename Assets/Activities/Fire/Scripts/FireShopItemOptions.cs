using I2.Loc;
using RGScript.Data;
using System;
using UnityEngine;

namespace Activities.Fire.Scripts {
    public abstract class FireShopItemOptions {
        public GameObject gameObject;

        public abstract string GetLabel();
        public abstract bool CanDo();
        public abstract void Do();

        public virtual void OnCreate() { }

        public virtual void OnDestroy() {
            GameObject.Destroy(gameObject);
        }

        public void PlaceAtPosition(Vector3 worldPos) {
            gameObject.transform.position = worldPos - Vector3.up * 0.7f;
        }

        public static FireShopItemOptions Create<T>(string prefabPath) where T : FireShopItemOptions, new() {
            var ret = new T {
                gameObject = GameObject.Instantiate(ResourcesUtil.Load<GameObject>(prefabPath)),
            };
            if (ret.gameObject.GetComponent<ObjectMachanics>() is var objMach && objMach != null) {
                objMach.getLabelCallback = ret.GetLabel;
                objMach.canTrigger = ret.CanDo;
                objMach.triggerCallback = ret.Do;
            }

            ret.OnCreate();
            return ret;
        }
    }

    public class FireShopItemRefresh : FireShopItemOptions {
        public int priceBase = 2; // 刷新一次的费用
        public const string prefabPath = "Activities/Fire/Prefabs/item_refresher.prefab";
        Action onRefresh;

        int price {
            get {
                if (DataMgr.ActivityFireData.lastCreatedRewardItems.Count == 0) {
                    return 0;
                }

                if (DataMgr.ActivityFireData.freeFreshItemTimeLeft > 0) {
                    return 0;
                }

                var t = DataMgr.ActivityFireData.RefreshShopTimesThisLevel - DataMgr.ActivityFireData.freeFreshItemTimeLeft;
                if (t < 0) {
                    return 0;
                }

                return priceBase * (int)Math.Pow(2, t);
            }
        }

        public override string GetLabel() {
            if (CanDo()) {
                var priceText = "";
                if (price > 0) {
                    priceText = RGItem.GetCurrencyString(string.Format(ItemLevel.FireCoin.ToRichText(), price), ItemLevel.FireCoin);
                }
                return string.Format(ScriptLocalization.Get("shopItemRefresh", "刷新商品 {0}"), priceText);
            }
            
            return ScriptLocalization.Get("ui/event_noMoney", "#食材不足");
        }

        public override bool CanDo() {
            return DataMgr.ActivityFireData.FireCoin >= price;
        }

        public override void Do() {
            if (CanDo()) {
                DataMgr.ActivityFireData.FireCoin -= price;
                RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Button);
                if (price == 0) {
                    DataMgr.ActivityFireData.freeFreshItemTimeLeft -= 1;
                } else {
                    DataMgr.ActivityFireData.RefreshShopTimesThisLevel += 1;
                    
                }
                onRefresh();
            }
        }

        public static FireShopItemRefresh Create(Action onRefresh) {
            var ret = Create<FireShopItemRefresh>(prefabPath) as FireShopItemRefresh;
            ret.onRefresh = onRefresh;
            return ret;
        }
    }

    public class FireCharRefresh : FireShopItemOptions {
        public const string PrefabPath = "Activities/Fire/Prefabs/char_refresher.prefab";
        private int _price = 100;
        Action _onRefresh;

        int Price {
            get {
                //   刷新按键（需场景交互）首次消耗100蓝币，每次刷新价格=上一个价格x2
                return _price * (int)Math.Pow(2, DataMgr.ActivityFireData.RefreshCharTime);
            }
        }

        public override string GetLabel() {
            if (CanDo()) {
                return string.Format(ScriptLocalization.Get("fire/char_refresh", "消耗{0}{1}刷新角色"),
                    string.Format(ItemLevel.Gem.ToRichText(), $"{Price}"),
                    ScriptLocalization.Get("A_Gem"));
            }

            if (RefreshOverTime()) {
                return ScriptLocalization.Get("item/use_max");
            }
            
            return ScriptLocalization.Get("I_no_gem", "宝石不足");
        }

        private bool RefreshOverTime() {
            return DataMgr.ActivityFireData.RefreshCharTime >= 10;
        }

        public override bool CanDo() {
            return RGSaveManager.Inst.GetGem() >= Price && !RefreshOverTime();
        }

        public override void Do() {
            if (CanDo()) {
		        RGSaveManager.Inst.ConsumeGem(Price, true, true);
                DataMgr.ActivityFireData.RefreshCharTime += 1;
                RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Button);
                _onRefresh();
            }
        }

        public static FireCharRefresh Create(Action onRefresh) {
            var ret = Create<FireCharRefresh>(PrefabPath) as FireCharRefresh;
            ret!._onRefresh = onRefresh;
            return ret;
        }
    }
}