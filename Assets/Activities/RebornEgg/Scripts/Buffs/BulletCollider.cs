using System;
using System.Collections.Generic;
using UnityEngine;

namespace Activities.RebornEgg.Scripts.Buffs {
    /// <summary>
    /// 碰撞伤害
    /// </summary>
    public class BulletCollider : MonoBehaviour {
        private int damage = 10;
        private bool can_hit = true;
        private ActivityDeadBodyController _ower;
        private float _originColliderRadius = 1.2f;
        private CircleCollider2D _collider2D;

        public void SetUp(int dmg) {
            _collider2D = transform.GetComponentInChildren<CircleCollider2D>();
            _originColliderRadius = _collider2D.radius;
            damage = dmg;
            can_hit = true;
            _ower = transform.parent.GetComponent<ActivityDeadBodyController>();
            ChangeColliderSize();
        }

        public void Refresh(int newDamage) {
            damage = newDamage;
            ChangeColliderSize();
        }

        private void ChangeColliderSize() {
            _collider2D.radius = _originColliderRadius * _ower.transform.localScale.x;
        }

        private void OnTriggerEnter2D(Collider2D other) {
            if (!can_hit) return;
            if (_ower == null) return;
            if (_ower.enemy.dead || _ower.enemy.dizzy) return;

            HitUnit(other);
        }

        private void HitUnit(Collider2D target) {
            if (!target.gameObject.tag.Equals("Body_E")) return;

            var finalDmg = Mathf.CeilToInt(damage * (1 + _ower.follower.level * 0.1f));
            var hurtInfo = new HurtInfo {
                Critic = false,
                Damage = finalDmg,
                DamageType = DamageType.Melee,
                ElementalType = ElementalType.None,
                Source = gameObject,
                SourceWeapon = null,
                FingerPrint = DamageCarrier.DefaultFingerPrint,
                Tags = new HashSet<string> { HurtInfoTags.Collision }
            };

            //尝试对怪物造成碰撞
            target.gameObject.GetComponent<RGEController>().GetHurt(hurtInfo);
            target.gameObject.GetComponent<RGEController>().GetForce(_ower.enemy.move_direction, 10);
            can_hit = false;
            Invoke("TurnCanHit", 1.5f);
        }

        protected void TurnCanHit() {
            can_hit = true;
        }
    }
}