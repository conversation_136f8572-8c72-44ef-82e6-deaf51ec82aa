using Activities.Fire.Scripts;
using Activities.School.Scripts;
using I2.Loc;
using RGScript.Data;
using RGScript.Data.GameItemData;
using RGScript.Util.QuickCode;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UI.Base;
using UIFramework;
using UnityEngine;
using UnityEngine.UI;

namespace Activities.RebornEgg.Scripts {
    /// <summary>
    /// 活动主界面，相关介绍
    /// </summary>
    public class UIActivityInfoWidget : BaseUIWidget {
        private static string INVENTORY_VIEW = "Activities/RebornEgg/Prefabs/window_inventory.prefab";
        private static string BUFF_VIEW = "Activities/RebornEgg/Prefabs/window_buff_info.prefab";
        private Button _openOrCloseActivityBtn;
        private bool _activeOpen;

        public Sprite btnOpen;
        public Sprite btnClose;
        private Text _doubleText;

        private Button _introButton;
        private SchoolDifficultyType _progressDifficultyType;
        private SchoolDifficultyType _selectDifficultyType;
        private int _progressIdx;
        private float _scrollDuration = 0.5f;
      
        
        private Transform _inventoryBtnRoot;
        private Button _buffInfoBtn;
        private Transform _buffListRoot;
        private GridLayoutGroup _buffLayoutGroup;
        public Sprite[] buffSprites;
        protected override void OnInit() {
            base.OnInit();
            var body = transform.Find("body");
            _openOrCloseActivityBtn = body.Find("open_or_close").GetComponent<Button>();
            _openOrCloseActivityBtn.onClick.AddListener(OpenOrCloseActivity);

            _introButton = body.Find("BtnIntro").GetComponent<Button>();
            _introButton.onClick.AddListener(OnClickIntro);

            _inventoryBtnRoot = body.Find("bottomRoot/btn_inventory/layout");
            for (int i = 0; i < _inventoryBtnRoot.childCount; i++) {
                int index = i;
                _inventoryBtnRoot.GetChild(i).GetComponent<Button>().onClick.AddListener(()=> {
                    OnClickInventory(index);
                });
            }
            _buffInfoBtn = body.Find("bottomRoot/btn_buff").GetComponent<Button>();
            _buffInfoBtn.onClick.AddListener(OnClickBuff);
            _buffListRoot = _buffInfoBtn.transform.Find("buff_list");
            _buffLayoutGroup = _buffListRoot.GetComponent<GridLayoutGroup>();
            // 从机不让点击按钮
            if (!GameUtil.IsSingleGame() && !NetControllerManager.Inst.isServer) {
                _openOrCloseActivityBtn.onClick.RemoveAllListeners();
                _openOrCloseActivityBtn.interactable = false;
            }
            _doubleText = body.Find("double").GetComponent<Text>();
        }

        private void OnClickInventory(int i) {
           var inventory = UIManager.Inst.OpenUIView<UIWindowInventory>(INVENTORY_VIEW,new object[] {i});
           inventory.OnClose += RefreshInventory;
           inventory.OnClose += RefreshBuffList;
        }

        private void OnClickBuff() {
            UIManager.Inst.OpenUIView<UIWindowBuffInfo>(BUFF_VIEW);
        }

        
        private void OnClickIntro() {
            ActivityRebornEggManager.ShowGuideView();
        }

        protected override void OnStartUp(params object[] args) {
            base.OnStartUp(args);
            RefreshDouble();
            RefreshOpenBtn();
            RefreshBuffList();
            RefreshInventory();
        }

        private void OpenOrCloseActivity() {
            _activeOpen = ActivityUtil.OpenOrCloseActivity(typeof(ActivityRebornEggManager));
            RefreshOpenBtn();
        }

        private void RefreshOpenBtn() {
            _activeOpen = BattleData.data.HasActivityEnabled(ActivityRebornEggManager.TAG);
            _openOrCloseActivityBtn.GetComponent<Image>().sprite = _activeOpen ? btnClose : btnOpen;
            _openOrCloseActivityBtn.transform.Find("text").GetComponent<Text>().text = _activeOpen
                ? ScriptLocalization.Get("fusionActivity/close", "#关闭活动")
                : ScriptLocalization.Get("fusionActivity/open", "#开启活动");
            _openOrCloseActivityBtn.transform.Find("text").GetComponent<Text>().resizeTextMaxSize = 30;
        }

        private void RefreshInventory() {
            var followers = DataMgr.ActivityRebornEggData.GetFollowers();
            for (int i = 0; i < _inventoryBtnRoot.childCount; i++) {
                if (followers.Count <= i) {
                    ShowEmpty(i);
                    return;
                }

                if (followers[i] != null) {
                    var info = _inventoryBtnRoot.GetChild(i).transform.Find("info");
                    info.gameObject.SetActive(true);
                    _inventoryBtnRoot.GetChild(i).transform.Find("empty").gameObject.SetActive(false);
                    info.Find("icon").GetComponent<Image>().sprite =
                        DataMgr.HandbookEnemyData.GetEnemySprite(followers[i].enemyId);
                    info.Find("lv").GetComponent<Text>().text =$"Lv.{(followers[i].level + 1).ToString()}";
                    info.Find("name").GetComponent<Text>().text = DataMgr.ActivityRebornEggData.GetEnemyName(followers[i].enemyId);
                } else {
                    ShowEmpty(i);
                }
            }
        }
        private void ShowEmpty(int i) {
            _inventoryBtnRoot.GetChild(i).transform.Find("empty").gameObject.SetActive(true);
            _inventoryBtnRoot.GetChild(i).transform.Find("info").gameObject.SetActive(false);
        }
        private void RefreshBuffList() {
            var buffList = DataMgr.ActivityRebornEggData.GetAllSortedBuffs();
            int showBuffCount = 0;
            for (int i = 0; i < _buffListRoot.childCount; i++) {
                var buff = _buffListRoot.GetChild(i);
                if (buffList[i].Item2 > 0) {
                    buff.gameObject.SetActive(true);
                    buff.transform.Find("icon").GetComponent<Image>().sprite = 
                        DataMgr.ActivityRebornEggData.GetBuffSprite(buffList[i].Item1);
                    buff.GetComponent<Image>().sprite = buffSprites[buffList[i].Item2];
                    showBuffCount++;
                } else {
                    buff.gameObject.SetActive(false);
                }
            }

            FixBuffScale(showBuffCount);
        }

        private void FixBuffScale(int count) {
            switch (count) {
                case < 3:
                    _buffLayoutGroup.cellSize = new Vector2(60, 60);
                    break;
                case < 7:
                    _buffLayoutGroup.cellSize = new Vector2(45, 45);
                    break;
                default:
                    _buffLayoutGroup.cellSize = new Vector2(35, 35);
                    return;
            }
        }
        
        private void RefreshDouble() {
            int total = 3;
            var playTime = StatisticData.data.GetToDayActivityPlayCount(ActivityRebornEggManager.TAG);
            var leftTime = Mathf.Max(0, total - playTime);
            _doubleText.text = string.Format(ScriptLocalization.Get("activity/today_activity_play_time", "#今日剩余活动货币翻倍次数：{0}/{1}"), leftTime, total);
        }
    }
}