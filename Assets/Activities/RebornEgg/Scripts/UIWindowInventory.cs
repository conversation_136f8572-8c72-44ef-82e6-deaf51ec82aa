using RGScript.Data;
using System;
using System.Collections.Generic;
using UnityCommon.UI;
using UnityEngine;
using UnityEngine.UI;

namespace Activities.RebornEgg.Scripts {
    public class UIWindowInventory : BaseUIView
    {
        private Button _closeBtn;
        private Button _saveBtn;
        public Button[] btnMonsters;
        private Text _countText;
        private int _selectedIndex = -1;
        private UIFollowerInfoView _followerInfoView;
        private Transform _selectedImg;
        private int _inventoryIndex;
        public Action OnClose;

        public Transform[] SelectedIndexImgs;
        public override void InitView() {
            base.InitView();
            pauseWhenShow = true;
            handleEsc = true;
            FindRefs();
        }
        
        private void FindRefs() {
            _closeBtn = transform.Find("body/close").GetComponent<Button>();
            _closeBtn.onClick.AddListener(OnClickClose);
            btnMonsters = transform.Find("body/right_root/grid").GetComponentsInChildren<Button>();
            for (int i = 0; i < btnMonsters.Length; i++) {
                int index = i;
                btnMonsters[i].onClick.AddListener(() => {
                    OnSelect(index);
                });
            }
            _countText = transform.Find("body/count").GetComponent<Text>();
            _followerInfoView = transform.Find("body/ui_follower_card").GetComponent<UIFollowerInfoView>();
            _selectedImg = transform.Find("body/right_root/select_img");
            _followerInfoView.FindRefs();
            _followerInfoView.OnRemoveFollowers += OnRemoveFollower;
            _saveBtn = transform.Find("body/right_root/btn_save").GetComponent<Button>();
            _saveBtn.onClick.AddListener(OnClickSaveBtn);
        }
        
        public override void ShowView(params object[] args) {
            base.ShowView(args);
            _inventoryIndex = (int)args[0];
            RefreshView();
        }

        private void RefreshView() {
            List<RebornEggFollower> allFollowers = DataMgr.ActivityRebornEggData.GetInventoryFollowers();
            _countText.text = $"{allFollowers.Count}/30";
            var followers = DataMgr.ActivityRebornEggData.GetFollowers();
            for (int i = 0; i < allFollowers.Count; i++) {
                var icon = btnMonsters[i].transform.Find("icon").GetComponent<Image>();
                icon.gameObject.SetActive(true);
                icon.sprite = DataMgr.HandbookEnemyData.GetEnemySprite(allFollowers[i].enemyId);
                var lvText = btnMonsters[i].transform.Find("lv").GetComponent<Text>();
                lvText.gameObject.SetActive(true);
                btnMonsters[i].transform.Find("bg_1").gameObject.SetActive(true);
                lvText.gameObject.SetActive(true);
                lvText.text = $"Lv.{(allFollowers[i].level + 1).ToString()}";
                //新号有可能没有数据
                if(followers.Count > _inventoryIndex && followers[_inventoryIndex] != null && allFollowers[i].id == followers[_inventoryIndex].id) {
                    OnSelect(i);
                }

                for (int j = 0; j < followers.Count; j++) {
                    if (followers.Count <= j)
                        break;
                    if (followers[j] == null) {
                        SelectedIndexImgs[j].gameObject.SetActive(false);
                    } else if (allFollowers[i].id == followers[j].id) {
                        SelectedIndexImgs[j].parent = btnMonsters[i].transform;
                        SelectedIndexImgs[j].localPosition = Vector3.zero;
                        SelectedIndexImgs[j].gameObject.SetActive(true);
                    }
                }
            }
        }

        private void OnSelect(int index) {
            var data = DataMgr.ActivityRebornEggData.GetInventoryFollowers();
            if(data.Count <= index) {
                return;
            }
            _selectedImg.gameObject.SetActive(true);
            _selectedImg.parent = btnMonsters[index].transform;
            _selectedImg.localPosition = Vector3.zero;
            ChooseFollower(index);
        }
        
        private void ChooseFollower(int index) {
            if (_selectedIndex == index) {
                btnMonsters[index].transform.Find("select_img").gameObject.SetActive(false);
                _selectedIndex = -1;
                _followerInfoView.ShowEmptyData();
            } else {
                btnMonsters[index].transform.Find("select_img").gameObject.SetActive(true);
                _selectedIndex = index;
                List<RebornEggFollower> followers = DataMgr.ActivityRebornEggData.GetInventoryFollowers();
                _followerInfoView.RefreshView(followers[index]);
            } 
        }

        private void OnRemoveFollower() {
            _selectedIndex = -1; 
            _selectedImg.gameObject.SetActive(false);
            _followerInfoView.ShowEmptyData();
            List<RebornEggFollower> allFollowers = DataMgr.ActivityRebornEggData.GetInventoryFollowers();
            if (allFollowers.Count < btnMonsters.Length) {
                var icon = btnMonsters[allFollowers.Count].transform.Find("icon").GetComponent<Image>();
                icon.gameObject.SetActive(false);
                btnMonsters[allFollowers.Count].transform.Find("bg_1").gameObject.SetActive(false);
                btnMonsters[allFollowers.Count].transform.Find("lv").gameObject.SetActive(false);
            }
            RefreshView();
        }
        
        private void SaveFollowers() {
            List<RebornEggFollower> allFollowers = DataMgr.ActivityRebornEggData.GetInventoryFollowers();
            List<string> followersId = GetFollowerId();
            if (_selectedIndex == -1) {
                followersId[_inventoryIndex] = "";
            } else {
                if (allFollowers[_selectedIndex].id == followersId[_inventoryIndex]) {
                    return;
                }

                for (int i = 0; i < followersId.Count; i++) {
                    if (allFollowers[_selectedIndex].id == followersId[i]) {
                        followersId[i] = "";
                        break;
                    }
                }
                followersId[_inventoryIndex] = allFollowers[_selectedIndex].id;
            }
            DataMgr.ActivityRebornEggData.Deploy(followersId);
        }

        private  List<string> GetFollowerId() {
            var folloers= DataMgr.ActivityRebornEggData.GetFollowers();
            List<string> followersId = new List<string>(){"","",""};
            for (int i = 0; i < folloers.Count; i++) {
                if (folloers[i] != null) {
                    followersId[i] = folloers[i].id;
                }
            }
            return followersId;
        }
        
        public void OnClickClose() {
            RGMusicManager.GetInstance().PlayEffect(RGMusicManager.emEffect.Button);
            OnClose?.Invoke();
            OnClick_Close();
        }

        public void OnClickSaveBtn() {
            SaveFollowers();
            OnClickClose();
        }
    }

}

