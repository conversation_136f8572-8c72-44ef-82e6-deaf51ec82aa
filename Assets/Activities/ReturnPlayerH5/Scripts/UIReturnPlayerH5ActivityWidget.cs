using Activities.ActivityEntry.Scripts;
using Coffee.UIExtensions;
using System;
using System.Collections;
using UnityEngine;
using UnityEngine.UI;

namespace Activities.ReturnPlayerH5.Scripts {
    /// <summary>
    /// 活动主界面
    /// </summary>
    public class UIReturnPlayerH5ActivityWidget : UIActivityEntryDetailWidgetBase {
        private bool _activeOpen;
        private ToggleGroup _toggleGroup;
        private Transform _tabPrefab;
        private Transform _groups;
        private Text _leftText;
        private UIParticle _particle;

        private Coroutine _coroutine;
        private UIReturnPlayerH5MainWidget _mainWidget;

        protected override void OnInit() {
            base.OnInit();
            var body = transform.Find("body");

            _leftText = body.Find("footer/left_time").GetComponent<Text>();
            _mainWidget = body.Find("groups/task_list").GetComponent<UIReturnPlayerH5MainWidget>();
            _particle = transform.Find("UIParticle").GetComponent<UIParticle>();
            _particle.gameObject.SetActive(false);
            SimpleEventManager.AddEventListener<ReturnPlayerH5TaskCompleteEvent>(OnReturnPlayerH5TaskCompleteEvent);
            SimpleEventManager.AddEventListener<ReturnPlayerH5RefreshStar>(OnReturnPlayerH5RefreshStar);
        }


        protected override void OnBeforeDestroy() {
            base.OnBeforeDestroy();
            SimpleEventManager.RemoveEventListener<ReturnPlayerH5TaskCompleteEvent>(OnReturnPlayerH5TaskCompleteEvent);
            SimpleEventManager.RemoveEventListener<ReturnPlayerH5RefreshStar>(OnReturnPlayerH5RefreshStar);
        }


        protected override void OnStartUp(params object[] args) {
            base.OnStartUp(args);

            if (_coroutine != null) {
                StopCoroutine(_coroutine);
            }

            _coroutine = StartCoroutine(CountingDown());
            _mainWidget.StartUp();
        }

        protected override void OnClear() {
            base.OnClear();
            if (_coroutine != null) {
                StopCoroutine(_coroutine);
                _coroutine = null;
            }
            _mainWidget.Clear();
        }

        private void OnReturnPlayerH5RefreshStar(ReturnPlayerH5RefreshStar e) {
            RefreshParticle(e.isOn);
        }

        public void RefreshParticle(bool isOn) {
            _particle.gameObject.SetActive(isOn);
        }

        private void OnReturnPlayerH5TaskCompleteEvent(ReturnPlayerH5TaskCompleteEvent e) {
        }

        private DateTime GetEndDate() {
            DateTime activityEndDate = ActivityUtil.GetEndDate(ActivityReturnPlayerH5Manager.TAG);
            // DateTime endDateTime = DataMgr.ReturnPlayerH5Data.GetStatusEndTime();
            // DateTime earlierDate = (activityEndDate < endDateTime) ? activityEndDate : endDateTime;
            return activityEndDate;
        }

        private IEnumerator CountingDown() {
            DateTime endDate = GetEndDate();
            var wait = new WaitForSecondsRealtime(0.1f);
            while (true) {
                var timeSpan = endDate - DateTime.Now;
                if (timeSpan.TotalSeconds > 0) {
                    _leftText.text = string.Format(I2.Loc.ScriptLocalization.Get("ui/shop_limit_count_down"),
                        timeSpan.Days,
                        timeSpan.Hours, timeSpan.Minutes, timeSpan.Seconds);
                } else {
                    _leftText.text = string.Format(I2.Loc.ScriptLocalization.Get("ui/shop_limit_count_down"), 0, 0, 0,
                        0);
                    BattleData.data.ClearActivity(ActivityReturnPlayerH5Manager.TAG);
                    yield break;
                }

                yield return wait;
            }
        }
    }
}