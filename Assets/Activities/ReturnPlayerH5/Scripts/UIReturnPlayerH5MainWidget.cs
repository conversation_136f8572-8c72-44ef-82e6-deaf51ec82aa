using RGScript.Data;
using TaskSystem;
using UI.Base;
using UnityEngine;
using UnityEngine.UI;

namespace Activities.ReturnPlayerH5.Scripts {
    public class UIReturnPlayerH5MainWidget : BaseUIWidget {
        private Button _openBtn;
        private Button _factorBtn;
        private UIReturnPlayerH5ActivityWidget _activityWidget;

        /// <summary>
        /// 当前UI选择的是第几天
        /// </summary>
        protected override void OnInit() {
            base.OnInit();

            _activityWidget = transform.parent.parent.parent.GetComponent<UIReturnPlayerH5ActivityWidget>();
            _openBtn = transform.Find("bg/blue/banner/btnOpen").GetComponent<Button>();
            _factorBtn = transform.Find("bg/blue/bottomLeft/btnFactor").GetComponent<Button>();
            _openBtn.onClick.AddListener(OnClickOpen);
            _factorBtn.onClick.AddListener(OnClickFactor);
            SimpleEventManager.AddEventListener<ReturnPlayerH5GetTaskListEvent>(OnReturnPlayerH5GetTaskListEvent);
            SimpleEventManager.AddEventListener<RefreshQuestionnaireEvent>(OnRefreshQuestionnaireEvent);

            Init();
        }

        protected override void OnStartUp(params object[] args) {
            base.OnStartUp(args);

            RefreshUI();
            TrackOpen();
        }

        private void OnClickFactor() {
            // DataMgr.ReturnPlayerH5Data.EnableFactor();
            // RefreshUI();
        }


        protected override void OnAfterDestroy() {
            base.OnAfterDestroy();
            SimpleEventManager.RemoveEventListener<ReturnPlayerH5GetTaskListEvent>(OnReturnPlayerH5GetTaskListEvent);
            SimpleEventManager.RemoveEventListener<RefreshQuestionnaireEvent>(OnRefreshQuestionnaireEvent);
        }

        private void OnReturnPlayerH5GetTaskListEvent(ReturnPlayerH5GetTaskListEvent e) {
            RefreshUI();
        }

        private void OnRefreshQuestionnaireEvent(RefreshQuestionnaireEvent e) {
            RefreshUI();
        }


        private void Init() {
            TaskManager.Instance.CheckReturnPlayerH5Tasks();

            Transform bannerTf = transform.Find("bg/blue/banner");
            bannerTf.Find("GameObject").GetComponent<RectTransform>().sizeDelta = new Vector2(0, 65f);
            var textGo = new GameObject("Text");
            textGo.transform.SetParent(bannerTf, false);
            var text = textGo.AddComponent<Text>();
            text.text = I2.Loc.ScriptLocalization.Get("return_player_h5/email_tip", "#若因延迟未收到奖励邮件，请重新进入客厅后再次查看~");
            text.rectTransform.localPosition = new Vector3(-73f, -243.4f, 0);
            text.rectTransform.localScale = new Vector3(0.5f, 0.5f, 1f);
            text.rectTransform.sizeDelta = new Vector2(1326, 60);
            text.fontSize = 42;
            text.font = CommonAssets.Assets.zpix;
            text.resizeTextForBestFit = true;
            text.resizeTextMinSize = 20;
            text.resizeTextMaxSize = 42;
            text.alignment = TextAnchor.MiddleLeft;
            text.color = Color.white;
            textGo.AddComponent<Shadow>().effectDistance = new Vector2(2f, -2f);
        }


        public void RefreshUI() {
            var hasEnableFactor = DataMgr.ReturnPlayerH5Data.HasEnableFactor();
            var canEnableFactor = DataMgr.ReturnPlayerH5Data.CanEnableFactor() && !hasEnableFactor;
            Debug.Log(
                $"returnplayerh5 server {DataMgr.ReturnPlayerH5Data.CanEnableFactorByServer()} client {DataMgr.ReturnPlayerH5Data.CanEnableFactorByClient()} hasEnableFactor {hasEnableFactor}");
            _factorBtn.GetComponent<Image>().color = hasEnableFactor ? Color.white : new Color(0.3f, 0.3f, 0.3f, 1);
            _activityWidget.RefreshParticle(hasEnableFactor);

            if (canEnableFactor) {
                DataMgr.ReturnPlayerH5Data.EnableFactor();
            }

            var combinedInfo = DataMgr.ReturnPlayerH5Data.SelfTeamId;
            bool canReceiveScore = false;
            if (!string.IsNullOrEmpty(combinedInfo) && combinedInfo.Contains('|')) {
                var parts = combinedInfo.Split('|');
                canReceiveScore = parts[2] == "true";
            }

            if (canReceiveScore) {
                var redpointProto = ResourcesUtil.Load<GameObject>("RGPrefab/Other/scene_object/redPoint_ui.prefab");
                var redPoint = Instantiate(redpointProto, _openBtn.transform).transform as RectTransform;
                if (redPoint != null) {
                    redPoint.anchorMax = Vector2.one;
                    redPoint.anchorMin = Vector2.one;
                    redPoint.anchoredPosition = new Vector2(-3.8f, 0f);
                    redPoint.gameObject.SetActive(true);
                }
            }
        }


        private void TrackOpen() {
            // TAUtil.Track("mission_return_open_activity_task", new Dictionary<string, object>() {
            //     { "mission_return_ui_source", _desc.autoOpen ? "auto" : "manual" },
            //     { "mission_return_return_times", DataMgr.ReturnPlayerData.GetReturnTimes() },
            // });
        }

        private void RefreshTask(int index) {
            ReloadList();
        }

        private void ReloadList() {
        }

        private void OnClickOpen() {
            DataMgr.ReturnPlayerH5Data.CallListTask();
        }


        protected override void OnClear() {
            base.OnClear();
        }
    }
}