using UI.Base;
using UnityEngine;
using UnityEngine.UI;

namespace Activities.FusionWeapon.Scripts
{
    public class UIHandItemCell : BaseUIWidget {
        public WeaponItemBase itemBase;
        public RGWeapon weapon;
        public float sizeFactor;
        private Image _select;
        public Sprite defaultSprite;
        protected override void OnStartUp(params object[] args) {
            base.OnStartUp(args);
            itemBase = args[0] as WeaponItemBase;
            weapon = args[1] as RGWeapon;
            _select = transform.Find("select").GetComponent<Image>();
            ActivityFusionManager.UIFusionItemsBag.itemHandCells.Add(this);
            if (itemBase == null) {
                transform.GetComponent<Image>().sprite = defaultSprite;
                return;
            }
            
            var spriteItem = WeaponItemFactory.GetWeaponItemSprite(itemBase.gameObject.name);
            transform.GetComponent<Image>().sprite = spriteItem;
            transform.GetComponent<RectTransform>().localScale = new Vector3(1, 1, 0) * sizeFactor;
        }
        
        public void HideSelect() {
            if (_select) {
                _select.enabled = false;
            }
        }
        
        public void ShowSelect() {
            if (_select) {
                _select.enabled = true;
            }
        }
    }
}
