using RGScript.UI;
using System.Collections.Generic;
using UI.Base;
using UnityEngine.UI;

namespace Activities.FusionWeapon.Scripts
{
    public class UIFusionItemsLineWidget : BaseUIWidget
    {
        private UIFusionItemsLineData _data;
        private HorizontalLayoutGroup _horizontalLayoutGroup;
        private readonly UILazyLoadWidgetList<UIItemCell> _itemCell = new();
        
        protected override void OnInit() {
            base.OnInit();
            _horizontalLayoutGroup = transform.Find("item_list").GetComponent<HorizontalLayoutGroup>();
            _itemCell.SetRootTf(_horizontalLayoutGroup.transform);
            _itemCell.SetItemPrefabPath("Activities/FusionWeapon/Prefabs/widget_item_cell.prefab");
        }
        
        protected override void OnStartUp(params object[] args) {
            base.OnStartUp(args);
            _data = (UIFusionItemsLineData)args[0];
            RefreshUI();
            
        }
        
        private void RefreshUI() {
            UpdateList(_data.WeaponItems);
        }
        
        private void UpdateList(List<string> items) {
            _itemCell.UpdateList(items.Count, (index, widget) => {
                var item = items[index];
                widget.Clear();
                widget.StartUp(item);
            });
        }
        
        protected override void OnClear() {
            base.OnClear();
            _data = null;
            _itemCell?.ForeachItems((_, item) => {
                if (item != null) {
                    item.Clear();
                }
            });
        }

    }
}
