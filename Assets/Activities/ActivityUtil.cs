using Activities.Fire.Scripts;
using cfg.ActivityDreamReward;
using cfg.ActivityChristmas2024Reward;
using cfg.ActivityFireReward;
using cfg.ActivitySchoolReward;
using cfg.BugActivityRewards;
using ChillyRoom.Services.Core.Libs;
using I2.Loc;
using PaPa;
using RGScript.Config.Manager;
using RGScript.Data;
using RGScript.Data.GameItemData;
using RGScript.Data.Mall;
using RGScript.Util;
using RGScript.Util.LifeCycle;
using SoulKnight.Runtime.Item;
using SoulKnight.Runtime.Skill;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using UIFramework;
using UnityEngine;
using Util.Model;
using Util.TAUtil;

namespace Activities {
    /// <summary>
    /// 活动统一可以兑换的奖励
    /// </summary>
    [Serializable]
    public class ActivityRedeemReward {
        public int Id { get; private set; }
        public string Name { get; private set; }
        public int Number { get; private set; }
        public int Cost { get; private set; }
    }

    public struct CheckInReward {
        public string key;
        public int count;
        public int replace2Gem;
    }

    /// <summary>
    /// 签到开启状态
    /// </summary>
    public enum CheckInOpenStatus {
        None = -1, // 未曾开启过
        Opening = 0, // 开启中
        Closed = 1, // 签到完成，已关闭
    }

    /// <summary>
    /// 签到奖励状态
    /// </summary>
    public enum CheckStatus {
        Disabled, // 未解锁
        Available, // 已解锁，可领取
        Received, // 已领取
    }

    public enum TrackAction {
        auto_open_view, // 自动打开
        manual_open_view, // 手动打开
        claim_reward // 领取奖励
    }

    /// <summary>
    /// 活动开启来源
    /// </summary>
    public enum TrackOpenFrom {
        ActivityIcon, // 活动图标
        Entry, // 地牢入口
        DefaultOpen, //默认开启
    }

    public class ActivityUtil {
        private const string TAG = "ActivityUtil";
        private static string packageRegex = @"\(([^)]*)\)"; // 礼包里面item匹配
        private static Regex skinRegex = new Regex(@"c_(\d+)_s_(\d+)"); // 皮肤
        private static Regex skillRegex = new Regex(@"skill_(\d+)_(\d+)"); // 角色技能
        private const string LOCAL_CACHE_KEY = "bonus_unconsume_goods";

        /// <summary>
        /// 未领取奖励需要实时检测技能，皮肤是否已解锁
        /// checkInReward.key可能为optional_skin(c_2_s_15|c_3_s_19|c_1_s_16)，表示按顺序获得一个皮肤，都已解锁则转换为蓝币
        /// </summary>
        /// <param name="checkInReward"></param>
        /// <returns></returns>
        public static CheckInReward ReplaceUnlockedReward(CheckInReward checkInReward, bool hasClaimed,
            Dictionary<string, CheckInReward> cacheDic) {
            if (hasClaimed) {
                // 已领取过的也查找是否是被替换过的奖励
                if (cacheDic.ContainsKey(checkInReward.key)) {
                    return cacheDic[checkInReward.key];
                }

                return checkInReward;
            }

            // 解析自动识别皮肤
            if (checkInReward.key.StartsWith("optional_skin(")) {
                string[] skins = GetPackageItems(checkInReward.key);
                for (var i = 0; i < skins.Length; i++) {
                    var sIdxes = GetHeroSkinIndexes(skins[i]);
                    var hIndex = sIdxes.Item1;
                    var sIndex = sIdxes.Item2;
                    if (hIndex >= 0 && sIndex >= 0) {
                        if (!DataUtil.GetSkinUnlock((emHero)hIndex, sIndex)) {
                            // 按顺序存在未解锁的皮肤，则使用
                            var skinReward = new CheckInReward() {
                                key = skins[i],
                                count = checkInReward.count
                            };

                            // 皮肤这里可能会变动，需要实时更新缓存
                            if (cacheDic.ContainsKey(checkInReward.key)) {
                                cacheDic.Remove(checkInReward.key);
                            }

                            CheckAddReplaceReward(checkInReward.key, skinReward, cacheDic);
                            return skinReward;
                        }
                    }
                }

                // 所有皮肤都已解锁，则取第一个皮肤转换其蓝币
                if (LogUtil.IsShowLog) {
                    LogUtil.Log(
                        $"{checkInReward.key} all skins has unlocked! convert to gems:{checkInReward.replace2Gem}",
                        TAG);
                }

                var gemReward = new CheckInReward() {
                    key = ItemData.GemName,
                    count = checkInReward.replace2Gem
                };
                CheckAddReplaceReward(checkInReward.key, gemReward, cacheDic);
                return gemReward;
            }

            // 解析自动识别武器皮肤
            if (checkInReward.key.StartsWith("optional_weapon_skin(")) {
                string[] skins = GetPackageItems(checkInReward.key);
                for (var i = 0; i < skins.Length; i++) {
                    var sIdxes = GetWeaponSkinIndexes(skins[i]);
                    var hIndex = sIdxes.Item1;
                    var sIndex = sIdxes.Item2;
                    if (!string.IsNullOrEmpty(hIndex) && sIndex >= 0) {
                        if (!DataMgr.WeaponEvolutionModule.HasUnlockWeaponSkin(hIndex, sIndex)) {
                            // 按顺序存在未解锁的皮肤，则使用
                            var skinReward = new CheckInReward() {
                                key = skins[i],
                                count = checkInReward.count
                            };

                            // 皮肤这里可能会变动，需要实时更新缓存
                            if (cacheDic.ContainsKey(checkInReward.key)) {
                                cacheDic.Remove(checkInReward.key);
                            }

                            CheckAddReplaceReward(checkInReward.key, skinReward, cacheDic);
                            return skinReward;
                        }
                    }
                }

                // 所有皮肤都已解锁，则取第一个皮肤转换其蓝币
                if (LogUtil.IsShowLog) {
                    LogUtil.Log(
                        $"{checkInReward.key} all skins has unlocked! convert to gems:{checkInReward.replace2Gem}",
                        TAG);
                }

                var gemReward = new CheckInReward() {
                    key = ItemData.GemName,
                    count = checkInReward.replace2Gem
                };
                CheckAddReplaceReward(checkInReward.key, gemReward, cacheDic);
                return gemReward;
            }

            if (IsWeaponSkin(checkInReward.key)) {
                // 武器皮肤
                if (DataMgr.WeaponEvolutionModule.HasUnlockWeaponSkin(checkInReward.key)) {
                    var gemReward = new CheckInReward() {
                        key = ItemData.GemName,
                        count = checkInReward.replace2Gem
                    };
                    CheckAddReplaceReward(checkInReward.key, gemReward, cacheDic);
                    return gemReward;
                }
            }

            // 单独判断皮肤和角色，角色要写成 c_34_s_0 的形势
            var skinIndexes = GetHeroSkinIndexes(checkInReward.key);
            var heroIndex = skinIndexes.Item1;
            var skinIndex = skinIndexes.Item2;
            if (heroIndex >= 0 && skinIndex >= 0) {
                var unlocked = skinIndex == 0
                    ? DataUtil.GetHeroUnlock((emHero)heroIndex)
                    : DataUtil.GetSkinUnlock((emHero)heroIndex, skinIndex);
                if (unlocked) {
                    // 首先判断是否是角色，因为角色默认皮肤是默认解锁的
                    // 已解锁的蓝币皮肤转换为对应蓝币
                    if (LogUtil.IsShowLog) {
                        LogUtil.Log(
                            $"{checkInReward.key} skin has unlocked! convert to gems:{checkInReward.replace2Gem}", TAG);
                    }

                    var newReward = new CheckInReward() {
                        key = ItemData.GemName,
                        count = checkInReward.replace2Gem
                    };
                    CheckAddReplaceReward(checkInReward.key, newReward, cacheDic);
                    return newReward;
                }
            }

            // 单独判断技能
            var skillIndexes = GetHeroSkillIndexes(checkInReward.key);
            heroIndex = skillIndexes.Item1;
            var skillIndex = skillIndexes.Item2;
            if (heroIndex >= 0 && skillIndex >= 0) {
                // 已解锁的蓝币技能转换为对应蓝币
                if (DataUtil.NewGetSkillUnlock((emHero)heroIndex, skillIndex)) {
                    var skillInfo = SkillConfigLoader.GetSkillInfoList(heroIndex)[skillIndex];
                    var toGems = skillInfo.price;
                    if (LogUtil.IsShowLog) {
                        LogUtil.Log($"{checkInReward.key} skill has unlocked! convert to gems:{toGems}", TAG);
                    }

                    var newReward = new CheckInReward() {
                        key = ItemData.GemName,
                        count = toGems
                    };
                    CheckAddReplaceReward(checkInReward.key, newReward, cacheDic);
                    return newReward;
                }
            }

            return checkInReward;
        }

        /// <summary>
        /// 返回礼包items
        /// </summary>
        /// <param name="itemName"></param>
        /// <returns></returns>
        public static string[] GetPackageItems(string itemName) {
            Match match = Regex.Match(itemName, packageRegex);
            var contents = match.Success ? match.Groups[1].Value : "";
            return contents.Split('|');
        }

        /// <summary>
        /// 返回hero,skin下标
        /// </summary>
        /// <param name="itemName"></param>
        /// <returns></returns>
        public static (int, int) GetHeroSkinIndexes(string itemName) {
            Match match = skinRegex.Match(itemName);
            return match.Success ? (int.Parse(match.Groups[1].Value), int.Parse(match.Groups[2].Value)) : (-1, -1);
        }

        /// <summary>
        /// 返回weaponName,skin下标
        /// </summary>
        /// <param name="itemName"></param>
        /// <returns></returns>
        public static (string, int) GetWeaponSkinIndexes(string itemName) {
            bool success = ItemUtility.GetWeaponSkinInfo(itemName, out var config);
            if (!success) {
                return (null, -1);
            }

            return (config.WeaponID, config.SkinIndex);
        }

        /// <summary>
        /// 返回hero,skill下标
        /// </summary>
        /// <param name="itemName"></param>
        /// <returns></returns>
        public static (int, int) GetHeroSkillIndexes(string itemName) {
            Match match = skillRegex.Match(itemName);
            return match.Success ? (int.Parse(match.Groups[1].Value), int.Parse(match.Groups[2].Value)) : (-1, -1);
        }

        /// <summary>
        /// 存一份替换为蓝币的奖励记录
        /// </summary>
        /// <param name="replaceKey"></param>
        /// <param name="toReward"></param>
        private static void CheckAddReplaceReward(string replaceKey, CheckInReward toReward, Dictionary<string, CheckInReward> cacheDic) {
            cacheDic.TryAdd(replaceKey, toReward);
        }

        public static IRewardable Convert2Rewardable(CheckInReward checkInReward) {
            return GetRewardable(checkInReward.key, checkInReward.count);
        }

        public static IRewardable GetRedeemRewardable(ActivityRedeemReward redeemReward) {
            return GetRewardable(redeemReward.Name, 1);
        }

        /// <summary>
        /// 活动奖励转换为可领取奖励
        /// </summary>
        /// <param name="itemName"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        public static IRewardable GetRewardable(string itemName, int count) {
            if (ItemConfigLoader.TryGetItemConfig(itemName, out ItemConfig config) && config != null) {
                emItemType itemType = config.ItemType;
                if (itemType != emItemType.None) {
                    // 区分特定武器兑换券
                    if (GameUtil.IsSpecialWeaponToken(itemName)) {
                        return new WeaponTokenRewardInfo() { tokenKey = itemName, count = count };
                    }

                    return new PickableInfo(itemType, itemName, count);
                }
            }

            var skinIndexes = GetHeroSkinIndexes(itemName);
            var heroIndex = skinIndexes.Item1;
            var skinIndex = skinIndexes.Item2;
            if (heroIndex >= 0 && skinIndex >= 0) {
                if (skinIndex == 0) {
                    // 角色
                    return new HeroRewardInfo() { hero = (emHero)heroIndex };
                }

                // 皮肤
                return new SkinRewardInfo() { hero = (emHero)heroIndex, skinIndex = skinIndex };
            }

            var skillIndexes = GetHeroSkillIndexes(itemName);
            heroIndex = skillIndexes.Item1;
            var skillIndex = skillIndexes.Item2;
            if (heroIndex >= 0 && skillIndex >= 0) {
                // 技能
                return new HeroSkillRewardInfo() { hero = (emHero)heroIndex, skillIndex = skillIndex };
            }

            if (Enum.TryParse(itemName, out emBattleFactor factor)) {
                // 挑战因子兑换券
                var tokenName = ItemTokenTicket.GetTokenFactorNameByFactor(factor);
                return new PickableInfo(emItemType.TokenTicket, tokenName, count);
            }

            if (itemName.StartsWith("p_")) {
                var thePet = ItemUtility.GetEmPetFromId(itemName);
                return new PetRewardInfo() { pet = thePet };
            }

            if (IsWeaponSkin(itemName)) {
                // 武器皮肤
                return new WeaponSkinRewardInfo() { id = itemName };
            }

            if (IsMythicWeapon(itemName)) {
                // 神话武器
                return new MythicWeaponRewardInfo() {
                    key = itemName,
                    count = count,
                };
            }

            return null;
        }

        public static bool IsMythicWeapon(string name) {
            return name.StartsWith("weapon_mythic_");
        }

        public static bool IsWeaponSkin(string name) {
            return Regex.IsMatch(name, @"^weapon_\d{3}_s_\d+$");
        }

        public static List<IRewardable> GetPackageIRewardables(CheckInReward checkInReward) {
            List<IRewardable> rewardables = new List<IRewardable>();
            // 天赋种子包 | 挑战因子包
            string[] items = GetPackageItems(checkInReward.key);
            foreach (var item in items) {
                CheckInReward itemReward = new CheckInReward() {
                    key = item,
                    count = 1
                };
                var itemRewardable = Convert2Rewardable(itemReward);
                if (itemRewardable != null) {
                    rewardables.Add(itemRewardable);
                }
            }

            return rewardables;
        }

        public static string GetDayKey(string prefix) {
            var netToday = GetNetToday();
            if (string.IsNullOrEmpty(netToday)) {
                return null;
            }

            return $"{prefix}_{netToday}";
        }

        public static string GetNetToday() {
            if (!NetTime.GotNetworkTime) {
                if (LogUtil.IsShowLog) {
                    Debug.Log("network time not ready!");
                }

                return null;
            }

            DateTime localDateTime = NetTime.Time.ToLocalTime();

            // 调试工具天数
            if (GameConfigManager.GetInstance().Config.IsDebug && PlayerSaveData.Inst.last_play_time_with_network_time > 0) {
                int thisDay = NetTime.Time.Subtract2000_01_01Day();
                int lastPlayDay = PlayerSaveData.Inst.last_play_time_with_network_time;
                localDateTime = localDateTime.AddDays(thisDay - lastPlayDay);
            }

            return localDateTime.ToString("yyyy-MM-dd");
        }

        public static DateTime? GetNetworkTime() {
            DateTime time = DateTime.Now;
            bool fetchConfig = true;

#if UNITY_EDITOR
            DateTime? editorTime = Date.EditorTime;
            if (editorTime.HasValue) {
                Debug.Log($"使用的编辑器时间: {editorTime.Value}");
                return editorTime;
            }

            // 使用本地配置
            fetchConfig = !UnityEditor.EditorPrefs.GetBool("DontFetchConfig");
#endif
            if (fetchConfig) {
                if (!NetTimeUtil.CurrentTime.Item1) {
                    return null;
                }

                time = MallUtility.CurrentTime;
            }

            return time;
        }

        /// <summary>
        /// 签到活动统一上报的领取奖励结构
        /// </summary>
        /// <param name="checkRewards"></param>
        /// <param name="dayIdx"></param>
        public static List<object> GetCheckInDayBonusForTrack(List<IRewardable> checkRewards, int dayIdx) {
            var bonusList = new List<object>();
            foreach (var rewardable in checkRewards) {
                var id = "";
                var count = rewardable.GetCount();
                if (count <= 0) {
                    // 皮肤，技能
                    count = 1;
                }

                var itemType = "";
                if (rewardable is PickableInfo pickableInfo) {
                    id = pickableInfo.name;
                    itemType = pickableInfo.itemType.ToString();
                } else {
                    id = rewardable.GetName();
                    if (rewardable is SkinRewardInfo skinRewardInfo) {
                        itemType = "Skin";
                        id = $"c_{(int)skinRewardInfo.hero}_s_{skinRewardInfo.skinIndex}";
                    } else if (rewardable is HeroSkillRewardInfo heroSkillRewardInfo) {
                        itemType = "Skill";
                        id = $"skill_{(int)heroSkillRewardInfo.hero}_{heroSkillRewardInfo.skillIndex}";
                    } else if (rewardable is WeaponTokenRewardInfo weaponTokenRewardInfo) {
                        itemType = "WeaponToken";
                        id = weaponTokenRewardInfo.tokenKey;
                    } else if (rewardable is HeroRewardInfo heroRewardInfo) {
                        itemType = "Hero";
                        id = $"c_{(int)heroRewardInfo.hero}";
                    }
                }

                var dic = new Dictionary<string, object> {
                    { "type", itemType },
                    { "id", id },
                    { "count", count },
                    { "day", dayIdx + 1 },
                };
                bonusList.Add(dic);
            }

            return bonusList;
        }

        /// <summary>
        /// 签到活动统一埋点
        /// </summary>
        /// <param name="trackAction"></param>
        /// <param name="totalDay"></param>
        /// <param name="checkRewards"></param>
        /// <param name="dayIdx"></param>
        public static void TrackCheckIn(TrackAction trackAction, int totalDay, string actName, List<IRewardable> checkRewards, int dayIdx = 0) {
            var properties = new Dictionary<string, object> {
                { "checkin_total_day", totalDay },
                { "checkin_action", trackAction.ToString() },
                { "activity_name", actName },
            };
            if (trackAction == TrackAction.claim_reward) {
                // 查找此次将领取的所有奖励，一并上报
                var bonusList = new List<object>();
                var dayTrackData = ActivityUtil.GetCheckInDayBonusForTrack(checkRewards, dayIdx);
                bonusList.AddRange(dayTrackData);
                properties.Add("checkin_bonus", bonusList);
            }

            TAUtil.Track("activity_checkin_seven_day_login", properties);
        }

        /// <summary>
        /// 从两处地方校对存储结果，避免被刷
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public static int GetBugFeatureRedeemRecords(BugActivityRewardsConfig reward) {
            // 7月版本的配置(id <= 18)，还是用key来取，新版本加上ID前缀，因为有小鱼干同名
            var key = reward.Id <= 18 ? reward.Name : $"{reward.Id}_{reward.Name}";

            if (!StatisticData.data.BugFeatureRedeemRecords.ContainsKey(key)) {
                StatisticData.data.BugFeatureRedeemRecords.Add(key, 0);
                StatisticData.Save();
            }

            if (!ItemData.data.BugFeatureRedeemRecords.ContainsKey(key)) {
                ItemData.data.BugFeatureRedeemRecords.Add(key, 0);
                ItemData.Save();
            }

            var rCount0 = StatisticData.data.BugFeatureRedeemRecords[key];
            var rCount1 = ItemData.data.BugFeatureRedeemRecords[key];
            if (rCount0 != rCount1) {
                var exp = new Exception($"s.count:{rCount0} i.count:{rCount1}");
                BuglyUtil.ReportException($"BugFeature_Redeem_Data_Exp_{LifeCycleManager.Instance.GetChillyUid}", exp);
            }

            return Mathf.Max(rCount0, rCount1);
        }

        public static void AddBugFeatureRedeemRecordAndSave(BugActivityRewardsConfig reward) {
            // 7月版本的配置(id <= 18)，还是用key来取，新版本加上ID前缀，因为有小鱼干同名
            var key = reward.Id <= 18 ? reward.Name : $"{reward.Id}_{reward.Name}";

            if (StatisticData.data.BugFeatureRedeemRecords.TryGetValue(key, out int rCount0)) {
                StatisticData.data.BugFeatureRedeemRecords[key] = rCount0 + 1;
            } else {
                StatisticData.data.BugFeatureRedeemRecords.Add(key, 1);
            }

            StatisticData.Save();

            if (ItemData.data.BugFeatureRedeemRecords.TryGetValue(key, out int rCount1)) {
                ItemData.data.BugFeatureRedeemRecords[key] = rCount1 + 1;
            } else {
                ItemData.data.BugFeatureRedeemRecords.Add(key, 1);
            }

            ItemData.Save();
        }

        /// <summary>
        /// 缓存活动等获取的付费非消耗品，避免下载存档等方式白嫖
        /// </summary>
        /// <param name="goodKey"></param>
        private static void AddUnConsumeGoodLocal(string goodKey) {
            var goodKeys = PlayerSaveData.GetStringList(LOCAL_CACHE_KEY, "");
            if (!goodKeys.Contains(goodKey)) {
                goodKeys.Add(goodKey);
                PlayerSaveData.SetList(LOCAL_CACHE_KEY, goodKeys);
            }
        }

        public static List<string> GetBonusUnConsumeGoods() {
            return PlayerSaveData.GetStringList(LOCAL_CACHE_KEY, "");
        }

        public static void CacheUnConsumeGoods(List<IRewardable> infos) {
            foreach (var info in infos) {
                string propId = null;
                if (info is SkinRewardInfo) {
                    SkinRewardInfo skinRewardInfo = (SkinRewardInfo)info;
                    var prefix = SkuConfigManager.ConvertHero2Sku(skinRewardInfo.hero);
                    propId = prefix + "_skin_" + skinRewardInfo.skinIndex;
                } else if (info is HeroSkillRewardInfo) {
                    HeroSkillRewardInfo heroSkillRewardInfo = (HeroSkillRewardInfo)info;
                    var prefix = SkuConfigManager.ConvertHero2Sku(heroSkillRewardInfo.hero);
                    propId = $"skill_{prefix}_{heroSkillRewardInfo.skillIndex}";
                } else if (info is HeroRewardInfo) {
                    HeroRewardInfo heroRewardInfo = (HeroRewardInfo)info;
                    propId = SkuConfigManager.ConvertHero2Sku(heroRewardInfo.hero);
                }

                if (propId != null) {
                    propId = propId.ToLower();
                    var isProduct = SkuConfigManager.IsProduct(propId);
                    if (!isProduct) {
                        continue;
                    }

                    var sku = SkuConfigManager.GetProduct(propId);
                    if (sku != null && sku.productType == SkuConfigManager.ProductType.UnConsumeable) {
                        // 非消耗品
                        AddUnConsumeGoodLocal(propId);
                        if (LogUtil.IsShowLog) {
                            LogUtil.Log($"AddUnConsumeGoodLocal new good:{propId}", TAG);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 添加或消耗对应活动的货币
        /// </summary>
        /// <param name="matName"></param>
        /// <param name="num"></param>
        /// <param name="showRewardWindow"></param>
        /// <param name="controller"></param>
        public static void AddActivityCoin(string matName, int num = 1, bool showRewardWindow = false,
            RGController controller = null) {
            if (num > 0) {
                PickableInfo info = new() {
                    count = num,
                    name = matName,
                    itemType = emItemType.Material,
                };

                // 地牢内逻辑，待结算存储到itemData
                RGGameProcess.Inst.got_pickable.Add(info);
                if (showRewardWindow && controller) {
                    var itemConfig = ItemConfigLoader.GetItemConfig(matName);
                    var icon = itemConfig.Icon.GetSprite();
                    UICanvas.GetInstance().ShowPickableWithSprite(
                        controller.transform, info, icon, 2.25f, 4f);
                }
            } else {
                ActivityUtil.ConsumeMaterial(matName, num);
            }
        }

        public static int GetSchoolRedeemRecords(ActivitySchoolRewardConfig reward) {
            var key = reward.Id;
            if (StatisticData.data.SchoolRedeemRecords202508.TryAdd(key, 0)) {
                StatisticData.Save();
            }

            if (ItemData.data.SchoolRedeemRecords202508.TryAdd(key, 0)) {
                ItemData.Save();
            }

            var rCount0 = StatisticData.data.SchoolRedeemRecords202508[key];
            var rCount1 = ItemData.data.SchoolRedeemRecords202508[key];
            if (rCount0 != rCount1) {
                var exp = new Exception($"s.count:{rCount0} i.count:{rCount1}");
                BuglyUtil.ReportException($"School_Redeem_Data_Exp_{LifeCycleManager.Instance.GetChillyUid}", exp);
            }

            return Mathf.Max(rCount0, rCount1);
        }

        public static void AddSchoolRedeemRecordAndSave(ActivitySchoolRewardConfig reward) {
            var key = reward.Id;
            if (StatisticData.data.SchoolRedeemRecords202508.TryGetValue(key, out int rCount0)) {
                StatisticData.data.SchoolRedeemRecords202508[key] = rCount0 + 1;
            } else {
                StatisticData.data.SchoolRedeemRecords202508.Add(key, 1);
            }

            StatisticData.Save();

            if (ItemData.data.SchoolRedeemRecords202508.TryGetValue(key, out int rCount1)) {
                ItemData.data.SchoolRedeemRecords202508[key] = rCount1 + 1;
            } else {
                ItemData.data.SchoolRedeemRecords202508.Add(key, 1);
            }

            ItemData.Save();
        }

        public static int GetDreamRedeemRecords(ActivityDreamRewardConfig reward) {
            var key = reward.Id;
            if (StatisticData.data.DreamRedeemRecords.TryAdd(key, 0)) {
                StatisticData.Save();
            }

            if (ItemData.data.DreamRedeemRecords.TryAdd(key, 0)) {
                ItemData.Save();
            }

            var rCount0 = StatisticData.data.DreamRedeemRecords[key];
            var rCount1 = ItemData.data.DreamRedeemRecords[key];
            if (rCount0 != rCount1) {
                var exp = new Exception($"s.count:{rCount0} i.count:{rCount1}");
                BuglyUtil.ReportException($"School_Redeem_Data_Exp_{LifeCycleManager.Instance.GetChillyUid}", exp);
            }

            return Mathf.Max(rCount0, rCount1);
        }

        public static void AddDreamRedeemRecordAndSave(ActivityDreamRewardConfig reward, int count) {
            var key = reward.Id;
            if (StatisticData.data.DreamRedeemRecords.TryGetValue(key, out int rCount0)) {
                StatisticData.data.DreamRedeemRecords[key] = rCount0 + count;
            } else {
                StatisticData.data.DreamRedeemRecords.Add(key, count);
            }

            StatisticData.Save();

            if (ItemData.data.DreamRedeemRecords.TryGetValue(key, out int rCount1)) {
                ItemData.data.DreamRedeemRecords[key] = rCount1 + count;
            } else {
                ItemData.data.DreamRedeemRecords.Add(key, count);
            }

            ItemData.Save();
        }


        /// <summary>
        /// 宝石获取数量埋点
        /// </summary>
        /// <param name="checkRewards"></param>
        /// <param name="type"></param>
        public static void TrackGem(List<IRewardable> checkRewards, emObtainGemType type) {
            int rewardGemCount = 0;
            foreach (var reward in checkRewards) {
                if (!(reward is PickableInfo)) {
                    continue;
                }

                var pickInfo = (PickableInfo)reward;
                if (pickInfo.name == ItemData.GemName) {
                    rewardGemCount += pickInfo.count;
                }
            }

            ConsumeStatistics.TrackObtainType(rewardGemCount, type);
        }

        public static void TrackRedeemReward(string titleKey, string itemName, string coinItemName, int coinCount) {
            SimpleEventManager.Raise(ActivityRedeemRewardEvent.UseCache());
            TAUtil.Track("get_activity_reward", new Dictionary<string, object>() {
                { "activity_name", ScriptLocalization.GetCN(titleKey) },
                { "activity_reward_name", itemName },
                { "activity_coin_name", coinItemName },
                { "activity_coin_count", coinCount }
            });
        }

        /// <summary>
        /// 有多波次的可以传wave
        /// </summary>
        /// <param name="activityName"></param>
        /// <param name="from"></param>
        /// <param name="wave"></param>
        public static void TrackOpenActivity(string activityName, TrackOpenFrom from) {
            var wave = ActivityEntryData.GetActivityCurrentWave(activityName);
            TAUtil.Track("open_activity", new Dictionary<string, object>() {
                { "activity_name", activityName },
                { "from", from.ToString() },
                { "activity_wave", wave },
            });
        }

        public static int GetChristmas2024RedeemRecords(ActivityChristmas2024RewardConfig reward) {
            var key = reward.Id;
            if (StatisticData.data.Christmas2024RedeemRecords.TryAdd(key, 0)) {
                StatisticData.Save();
            }

            if (ItemData.data.Christmas2024RedeemRecords.TryAdd(key, 0)) {
                ItemData.Save();
            }

            var rCount0 = StatisticData.data.Christmas2024RedeemRecords[key];
            var rCount1 = ItemData.data.Christmas2024RedeemRecords[key];
            if (rCount0 != rCount1) {
                var exp = new Exception($"s.count:{rCount0} i.count:{rCount1}");
                BuglyUtil.ReportException($"Christmas2024_Redeem_Data_Exp_{LifeCycleManager.Instance.GetChillyUid}", exp);
            }

            return Mathf.Max(rCount0, rCount1);
        }

        public static void AddChristmas2024RedeemRecordAndSave(ActivityChristmas2024RewardConfig reward, int count) {
            var key = reward.Id;
            AddDicValue(StatisticData.data.Christmas2024RedeemRecords, key, count);
            StatisticData.Save();
            AddDicValue(ItemData.data.SchoolRedeemRecords202508, key, count);
            ItemData.Save();
        }

        private static void AddDicValue(Dictionary<int, int> itemRecordDic, int key, int addCount) {
            if (itemRecordDic.TryGetValue(key, out int rCount1)) {
                itemRecordDic[key] = rCount1 + addCount;
            } else {
                itemRecordDic.Add(key, addCount);
            }
        }

        /// <summary>
        /// 活动兑换奖励统一调用
        /// </summary>
        /// <param name="info"></param>
        /// <param name="source"></param>
        /// <param name="save"></param>
        public static void AddReward(IRewardable info, string source, bool save = true) {
            if (LogUtil.IsShowLog) {
                LogUtil.Log($"AddReward reward:{info.GetName()} count:{info.GetCount()}", TAG);
            }

            if (info is SkinRewardInfo skinRewardInfo) {
                skinRewardInfo.GetReward(save);
            } else if (info is PetRewardInfo petRewardInfo) {
                petRewardInfo.GetReward(save);
            } else if (info is HeroSkillRewardInfo heroSkillRewardInfo) {
                heroSkillRewardInfo.GetReward(save);
            } else if (info is HeroLevelRewardInfo heroLevelRewardInfo) {
                heroLevelRewardInfo.GetReward(save);
            } else if (info is HeroRewardInfo heroRewardInfo) {
                heroRewardInfo.GetReward(save);
            } else if (info is WeaponRewardInfo weaponRewardInfo) {
                weaponRewardInfo.GetReward(save);
            } else if (info is MultiRoomRewardInfo multiRoomRewardInfo) {
                multiRoomRewardInfo.GetReward(save);
            } else if (info is HeroRoomRewardInfo heroRoomRewardInfo) {
                heroRoomRewardInfo.GetReward(save);
            } else if (info is WeaponTokenRewardInfo weaponTokenRewardInfo) {
                weaponTokenRewardInfo.GetReward(save);
            } else if (info is WeaponSkinRewardInfo weaponSkinRewardInfo) {
                weaponSkinRewardInfo.GetReward(save);
            } else if (info is MythicWeaponRewardInfo mythicWeaponRewardInfo) {
                mythicWeaponRewardInfo.GetReward(save);
            } else {
                var pickableInfo = (PickableInfo)info;
                ItemData.data.AddPickable(pickableInfo, source, save, true);
            }
        }

        public static void ConsumeMaterial(string matName, int num) {
            ItemData.data.ConsumeMaterial(matName, Mathf.Abs(num), true, true, true);
        }

        /// <summary>
        /// 打开活动
        /// </summary>
        /// <param name="actName"></param>
        /// <param name="enable"></param>
        /// <param name="from"></param>
        public static void EnableActivity(string actName, bool enable, TrackOpenFrom from) {
            BattleData.data.ClearActivity(actName);
            if (enable) {
                BattleData.data.EnableActivity(actName);
                TrackOpenActivity(actName, from);
            }
        }

        public static int GetFireRedeemRecords(ActivityFireRewardConfig reward) {
            var key = reward.Id;
            if (StatisticData.data.FireRedeemRecords.TryAdd(key, 0)) {
                StatisticData.Save();
            }

            if (ItemData.data.FireRedeemRecords.TryAdd(key, 0)) {
                ItemData.Save();
            }

            var rCount0 = StatisticData.data.FireRedeemRecords[key];
            var rCount1 = ItemData.data.FireRedeemRecords[key];
            if (rCount0 != rCount1) {
                var exp = new Exception($"s.count:{rCount0} i.count:{rCount1}");
                BuglyUtil.ReportException($"Fire_Redeem_Data_Exp_{LifeCycleManager.Instance.GetChillyUid}", exp);
            }

            return Mathf.Max(rCount0, rCount1);
        }

        public static void AddFireRedeemRecordAndSave(ActivityFireRewardConfig reward, int count) {
            var key = reward.Id;
            if (StatisticData.data.FireRedeemRecords.TryGetValue(key, out int rCount0)) {
                StatisticData.data.FireRedeemRecords[key] = rCount0 + count;
            } else {
                StatisticData.data.FireRedeemRecords.Add(key, count);
            }

            StatisticData.Save();

            if (ItemData.data.FireRedeemRecords.TryGetValue(key, out int rCount1)) {
                ItemData.data.FireRedeemRecords[key] = rCount1 + count;
            } else {
                ItemData.data.FireRedeemRecords.Add(key, count);
            }

            ItemData.Save();
        }

        #region 2025 3月版本及之后活动通用

        /// <summary>
        /// 已经兑换过活动奖励
        /// </summary>
        /// <param name="actName"></param>
        /// <returns></returns>
        public static bool HasRedeemActSomething(string actName) {
            return StatisticData.data.ActivityRedeemRecords.Any(item => item.Key == actName) ||
                   ItemData.data.ActivityRedeemRecords.Any(item => item.Key == actName);
        }

        /// <summary>
        /// 活动统一查询已兑换数据
        /// </summary>
        /// <param name="actName"></param>
        /// <param name="itemId"></param>
        /// <returns></returns>
        public static int GetRedeemRecords(string actName, int itemId) {
            var rCount0 = 0;
            var rCount1 = 0;
            if (StatisticData.data.ActivityRedeemRecords.ContainsKey(actName)) {
                if (StatisticData.data.ActivityRedeemRecords[actName].TryGetValue(itemId, out int theCount)) {
                    rCount0 = theCount;
                }
            }

            if (ItemData.data.ActivityRedeemRecords.ContainsKey(actName)) {
                if (ItemData.data.ActivityRedeemRecords[actName].TryGetValue(itemId, out int theCount)) {
                    rCount1 = theCount;
                }
            }

            if (rCount0 != rCount1) {
                var exp = new Exception($"s.count:{rCount0} i.count:{rCount1}");
                BuglyUtil.ReportException($"Act_Redeem_Data_Exp_{LifeCycleManager.Instance.GetChillyUid}", exp);
            }

            return Mathf.Max(rCount0, rCount1);
        }

        /// <summary>
        /// 统一存储活动兑换的奖励数据
        /// </summary>
        /// <param name="actName"></param>
        /// <param name="itemId"></param>
        /// <param name="count"></param>
        public static void AddRedeemRecordAndSave(string actName, int itemId, int count) {
            if (StatisticData.data.ActivityRedeemRecords.TryGetValue(actName, out var actItem)) {
                if (actItem.TryGetValue(itemId, out var oldCount)) {
                    actItem[itemId] = oldCount + count;
                } else {
                    actItem.Add(itemId, count);
                }
            } else {
                var newItem = new Dictionary<int, int> { { itemId, count } };
                StatisticData.data.ActivityRedeemRecords.Add(actName, newItem);
            }

            StatisticData.Save();

            if (ItemData.data.ActivityRedeemRecords.TryGetValue(actName, out var actItem2)) {
                if (actItem2.TryGetValue(itemId, out var oldCount)) {
                    actItem2[itemId] = oldCount + count;
                } else {
                    actItem2.Add(itemId, count);
                }
            } else {
                var newItem = new Dictionary<int, int> { { itemId, count } };
                ItemData.data.ActivityRedeemRecords.Add(actName, newItem);
            }

            ItemData.Save();
        }

        #endregion

        /// <summary>
        /// 生成花园烟花
        /// </summary>
        public static void GenFireWork() {
            var proto = ResourcesUtil.Load<GameObject>("Activities/Fire/Prefabs/firework.prefab");
            GameObject.Instantiate(proto, RoomObjectManager.Inst.transform.Find("garden_slot/garden/function"));
        }

        public static bool OpenOrCloseActivity(Type actType) {
            if (actType == null) {
                Debug.LogError($"OpenOrCloseActivity actType == null，跳过");
                return false;
            }
            
            var activeOpen = false;
            var funcAvailable = actType.GetMethod("IsAvailable", BindingFlags.Public | BindingFlags.Static);
            if (funcAvailable != null && (bool)funcAvailable.Invoke(null, null)) {
                activeOpen = BattleData.data.HasActivityEnabled(actType.Name);
                activeOpen = !activeOpen;
                
                // 主动开启的活动，当前只支持同时开启一个
                // 这里把别的手动开启的活动关闭
                foreach (var cellData in DataMgr.ActivityEntryData.GetAvailableActivities()) {
                    if (cellData.activityType == actType) continue;
                    if (string.IsNullOrEmpty(cellData.btnPath)) continue;
                    
                    var hasOpened = BattleData.data.HasActivityEnabled(cellData.activityType.Name);
                    if (hasOpened) {
                        BattleData.data.ClearActivity(cellData.activityType.Name);
                    }
                }
                
                EnableActivity(actType.Name, activeOpen, TrackOpenFrom.ActivityIcon);

                if (activeOpen) {
                    // 开启的时候，上报埋点
                    var taData = new Dictionary<string, object> {
                        { "tab_title", ScriptLocalization.GetCN(DataMgr.ActivityFireData.Config.Title) },
                        { "act_action", "open_activity" },
                    };
                    TAUtil.Track("activity_entry_tab_click", taData);
                }
            } else {
                EnableActivity(ActivityFireManager.TAG, false, TrackOpenFrom.ActivityIcon);
            }

            SimpleEventManager.Raise(new UpdateFactorBarEvent());
            return activeOpen;
        }

        public static bool InDebugMode => LogUtil.ShowDebugLog || GameConfigManager.GetInstance().Config.IsOpenLua;

        /// <summary>
        /// 活动开始时间
        /// </summary>
        /// <param name="actName"></param>
        /// <returns></returns>
        public static string StartTime(string actName) {
            if (InDebugMode) {
                var beginTs = PlayerSaveData.GetStringLocal(actName + "_begin_date", null);
                if (!string.IsNullOrEmpty(beginTs)) {
                    return beginTs;
                }
            }

            var Config = DataMgr.ActivityConfigData?.GetActivityConfig(actName);
            return Config?.TimeStart;
        }

        /// <summary>
        /// 第二波开始时间
        /// </summary>
        /// <param name="actName"></param>
        /// <returns></returns>
        public static string MidTime(string actName) {
            if (InDebugMode) {
                var ts = PlayerSaveData.GetStringLocal(actName + "_begin_date_mid", null);
                if (!string.IsNullOrEmpty(ts)) {
                    return ts;
                }
            }

            var Config = DataMgr.ActivityConfigData?.GetActivityConfig(actName);
            return Config?.Extra;
        }

        /// <summary>
        /// 活动结束时间
        /// </summary>
        /// <param name="actName"></param>
        /// <returns></returns>
        public static string EndTime(string actName) {
            if (InDebugMode) {
                var endTs = PlayerSaveData.GetStringLocal(actName + "_end_date", null);
                if (!string.IsNullOrEmpty(endTs)) {
                    return endTs;
                }
            }

            var Config = DataMgr.ActivityConfigData?.GetActivityConfig(actName);
            return Config?.TimeEnd;
        }

        /// <summary>
        /// 获取活动结束时间，并转换为本地时间
        /// </summary>
        /// <param name="actName"></param>
        /// <returns></returns>
        public static DateTime GetEndDate(string actName) {
            return TimeUtil.ChineseTimestampToLocalDateTime(EndTime(actName));
        }

        /// <summary>
        /// 活动有效期
        /// </summary>
        /// <returns></returns>
        public static bool IsInActivityPeriod(string actName) {
            var sTime = ActivityUtil.StartTime(actName);
            var eTime = ActivityUtil.EndTime(actName);
            var time = ActivityUtil.GetNetworkTime();
            if (string.IsNullOrEmpty(sTime) || string.IsNullOrEmpty(eTime) || time == null) {
                if (LogUtil.IsShowLog) {
                    LogUtil.Log(
                        "!!! net.time:" + time + " start:" + sTime + " end:" + eTime + " => false", actName);
                }

                return false;
            }

            var startTime = TimeUtil.ChineseTimestampToLocalDateTime(sTime);
            var endTime = TimeUtil.ChineseTimestampToLocalDateTime(eTime);
            var inPeriod = time >= startTime && time < endTime;
            if (LogUtil.IsShowLog) {
                LogUtil.Log(
                    "net.time:" + time + " activity.start:" + startTime + " activity.end:" + endTime + " => " +
                    inPeriod, actName);
            }

            return inPeriod;
        }
        
        public static bool CheckPetUnlock(string rewardName) {
            if (!rewardName.StartsWith("p_")) return false;
           
            var thePet = ItemUtility.GetEmPetFromId(rewardName);
            return DataUtil.GetPetUnlock(thePet);
        }
        
        public static bool CheckBlueprintUnlock(string rewardName) {
               if (!rewardName.StartsWith("blueprint_")) return false;
               
               var status = ItemData.data.GetBluePrintStatus(rewardName);
               return status != emBluePrintStatus.None;
        }

        public static bool CheckSkinUnlock(string rewardName) {
            if (!rewardName.StartsWith("c_") ||
                !ItemUtility.GetHeroSkinInfo(rewardName, out var heroId, out var skinId)) {
                return false;
            }

            return DataUtil.GetSkinUnlock(heroId, skinId);
        }

        public static bool CheckSkinFragment(string id, out int hasCount) {
            if (!id.StartsWith("material_skin_fragment")) {
                hasCount = 0;
                return false;
            }

            if (ItemUtility.GetHeroSkinFragmentInfo(id, out emHero hero, out int skinId)) {
                if (DataUtil.GetSkinUnlock(hero, skinId)) {
                    hasCount = 8;
                    return true;
                }

                hasCount = DataUtil.GetMaterialCount(id);
                return true;
            }

            hasCount = 0;
            return false;
        }

        /// <summary>
        /// 点击活动标签埋点
        /// </summary>
        /// <param name="actTitle"></param>
        /// <param name="tabIndex"></param>
        public static void TrackActivityClickTab(string actTitle, int tabIndex) {
            var ta_data = new Dictionary<string, object> {
                { "tab_title", GetActTitle(actTitle, null, true) },
                { "menu_index", tabIndex },
                { "act_action", "tap_menu" },
            };
            global::TAUtil.Track("activity_entry_tab_click", ta_data);
        }

        /// <summary>
        /// 活动介绍弹窗
        /// </summary>
        /// <param name="viewPath"></param>
        /// <param name="mark"></param>
        public static void ShowGuideView(string viewPath, string mark) {
            var guideView = UIManager.Inst.OpenUIView<CommonGuideView>(viewPath);
            guideView.AfterReadGuide = () => {
                if (!StatisticData.data.IsEventRecord(mark)) {
                    StatisticData.data.RecordEvent(mark, true);
                }
            };
        }

        /// <summary>
        /// 活动货币
        /// </summary>
        /// <param name="actMatName"></param>
        /// <returns></returns>
        public static int GetActivityCoinCount(string actMatName) {
            return ItemData.data.GetMaterialCount(actMatName);
        }

        /// <summary>
        /// 统一获取活动标题本地化
        /// </summary>
        /// <param name="titleKey"></param>
        /// <param name="fallback"></param>
        /// <param name="forceCN"></param>
        /// <returns></returns>
        public static string GetActTitle(string titleKey, string fallback = "", bool forceCN = false) {
            var loc = forceCN
                ? ScriptLocalization.GetCN(titleKey)
                : ScriptLocalization.Get(titleKey, fallback);
            if (titleKey == "activity/yearly_checkin_title") {
                loc = loc.Replace("{0}", DateTime.Today.Year.ToString());
            }

            return loc;
        }
    }
}