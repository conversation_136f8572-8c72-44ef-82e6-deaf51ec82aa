using UnityEngine;

public class ObjectShaker {
    private Vector2 XRange { get; }
    private Vector2 YRange { get; }
    private float Duration { get; }
    private Transform TargetTransform { get; }
    private TimeCounter DurationCounter { get; }
    private TimeCounter PeriodCounter { get; }
    public bool IsShaking { get; set; }
    private int ShakeCount { get; }
    private int CurrentShakeCount { get; set; }
    private Vector3 ShakeFromPosition { get; set; }
    private Vector3 ShakeTargetPosition { get; set; }
    private Vector3 ShakeInitPosition { get; set; }
    public ObjectShaker(Vector2 xRange, Vector2 yRange, float duration, int shakeCount, Transform targetTransform) {
        XRange = xRange;
        YRange = yRange;
        Duration = duration;
        ShakeCount = shakeCount;
        DurationCounter = new TimeCounter();
        PeriodCounter = new TimeCounter();
        IsShaking = false;
        TargetTransform = targetTransform;
    }

    public void Start() {
        if (IsShaking) {
            Stop();
        }
        IsShaking = true;
        PeriodCounter.ResetTime(Duration / ShakeCount);
        CurrentShakeCount = 0;
        DurationCounter.ResetTime(Duration);
        ShakeInitPosition = TargetTransform.localPosition;
        ShakeTargetPosition = ShakeInitPosition;
        ShakeFromPosition = ShakeInitPosition;
        SetShakeTarget();
    }

    public void Update() {
        if (!IsShaking) {
            return;
        }

        if (DurationCounter.isTimeOut) {
            Stop();
        }

        if (PeriodCounter.isTimeOut) {
            CurrentShakeCount += 1;
            SetShakeTarget();
            PeriodCounter.ResetTime(Duration / ShakeCount);
        } else {
            UpdatePosition();
        }
    }

    private void UpdatePosition() {
        TargetTransform.localPosition =
            Vector3.Lerp(ShakeFromPosition, ShakeTargetPosition, PeriodCounter.CountProgress);
    }

    private void SetShakeTarget() {
        ShakeFromPosition = ShakeTargetPosition;
        TargetTransform.localPosition = ShakeTargetPosition;
        if (CurrentShakeCount < ShakeCount - 1) {
            var positionOffset = new Vector3(Random.Range(XRange.x, XRange.y), Random.Range(YRange.x, YRange.y));
            ShakeTargetPosition = ShakeInitPosition + positionOffset;
        } else {
            ShakeTargetPosition = ShakeInitPosition;
        }
    }

    public void Stop() {
        IsShaking = false;
        TargetTransform.localPosition = ShakeInitPosition;
    }
}
