using RGScript.Character.Player.SkinSkillEffect;
using Sirenix.OdinInspector;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;

namespace Utils.GameUtil {
    /// <summary>
    /// Sprite淡入淡出效果组件
    /// </summary>
    public class SpriteAlphaFadeEffect : MonoBehaviour, ISkillEffect {
        public float fadeTime = 1.0f;

        public float minAlpha = 0;

        public float maxAlpha = 1f;

        public EFadeAnim fadeType = EFadeAnim.FadeIn;

        public EScaleAnim scaleType = EScaleAnim.None;

        public List<SpriteRenderer> spriteRenderers = new List<SpriteRenderer>();

        private bool _isDestroyed;

        private List<Vector3> _startScaleList = new List<Vector3>();

        void OnEnable() {
            _startScaleList.Clear();
            if (scaleType is EScaleAnim.ScaleThis) {
                _startScaleList.Add(transform.localScale);
            } else if (scaleType is EScaleAnim.ScaleEach) {
                foreach (var sr in spriteRenderers) {
                    _startScaleList.Add(sr.transform.localScale);
                }
            }
            if (fadeType is EFadeAnim.FadeIn or EFadeAnim.FadeInOut) {
                StartCoroutine(Fade(true));
            }
        }

        /// <summary>
        /// 执行淡入淡出效果的协程
        /// </summary>
        private IEnumerator Fade(bool forward) {
            float elapsed = 0f;
            while (elapsed < fadeTime) {
                elapsed += Time.deltaTime;
                float t = elapsed / fadeTime;
                float trueTime = forward ? t : 1 - t;

                for (int i = 0; i < spriteRenderers.Count; i++) {
                    SpriteRenderer sr = spriteRenderers[i];
                    if (!sr) {
                        continue;
                    }

                    float alpha = Mathf.Lerp(minAlpha, maxAlpha, trueTime);
                    sr.color = sr.color.Alpha(alpha);

                    if (scaleType != EScaleAnim.ScaleEach) {
                        continue;
                    }

                    if (i < _startScaleList.Count) {
                        sr.transform.localScale = Vector3.Lerp(Vector3.zero, _startScaleList[i], trueTime);
                    }
                }

                if (scaleType == EScaleAnim.ScaleThis) {
                    if (_startScaleList.Count > 0) {
                        transform.localScale = Vector3.Lerp(Vector3.zero, _startScaleList[0], trueTime);
                    } else {
                        Debug.LogError($"-- _startScaleList {_startScaleList.Count}");
                    }

                }

                yield return null;
            }
        }

        public void Init(RGBaseController controller) { }

        public void EndSkill() {
            if (_isDestroyed) {
                return;
            }

            if (fadeType is EFadeAnim.FadeOut or EFadeAnim.FadeInOut) {
                StartCoroutine(Fade(false));
            }

            Destroy(gameObject, fadeTime);
        }

        private void OnDestroy() {
            _isDestroyed = true;
        }

#if UNITY_EDITOR
        [Button]
        void FindSpriteRenderers() {
            spriteRenderers.AddRange(GetComponentsInChildren<SpriteRenderer>());
        }
#endif
    }

    public enum EFadeAnim {
        FadeIn,
        FadeOut,
        FadeInOut,
    }

    public enum EScaleAnim {
        None,
        ScaleThis, //整体缩小
        ScaleEach //各自缩小
    }
}