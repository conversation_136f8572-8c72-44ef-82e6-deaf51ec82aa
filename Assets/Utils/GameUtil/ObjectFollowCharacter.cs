using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 跟随
/// </summary>
public class ObjectFollowCharacter : MonoBehaviour {
    public float height = 0.8f;
    public float distance = 0.6f;
    public float accelerate = 0.02f;
    public float maxSpeed = 10;

    private float _minSpeed = 3f;
    private float _curSpeed = 0.1f;

    private RGBaseController _targetController;

    public void InitiateAndSetTarget(RGBaseController controller) {
        var obj = GameObject.Instantiate(gameObject);
        obj.SetActive(true);
        obj.GetComponent<ObjectFollowCharacter>().SetTarget(controller);
    }

    public void SetTarget(RGBaseController controller) {
        _targetController = controller;
        var forwardDir = new Vector3(_targetController.facing, 0, 0);
        transform.position = _targetController.transform.position - (forwardDir * distance) + new Vector3(0, height, 0);
    }
    
    // Update is called once per frame
    void Update()
    {
        if (_targetController) {
            var forwardDir = new Vector3(_targetController.facing, 0, 0);
            var destination = _targetController.transform.position -
                           (forwardDir * distance) + new Vector3(0, height, 0);

            var diff = destination - transform.position;
            if (diff.magnitude > Mathf.Epsilon) {
                if (_curSpeed < _minSpeed) {
                    _curSpeed = _minSpeed;
                }

                _curSpeed *= (1+accelerate);
                if (_curSpeed > maxSpeed)
                    _curSpeed = maxSpeed;
                var delta = Mathf.Clamp(_curSpeed * Time.deltaTime, 0, diff.magnitude);
                transform.position += delta * diff.normalized;
                if (Vector3.Dot(diff.normalized, transform.right) < 0) {
                    transform.right *= -1;
                }
            } else {
                _curSpeed /= (1+accelerate);
            }
        }
    }
}
