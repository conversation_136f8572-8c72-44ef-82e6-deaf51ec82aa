using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class RigidbodyDampen : MonoBehaviour
{
    public float velocityLimit;
    public float dampen;
    Rigidbody2D _rigidbody2D;
    public float interval = 0.1f;
    float _elapsedTime;

    void Start()
    {
        _rigidbody2D = GetComponent<Rigidbody2D>();
    }

    // Update is called once per frame
    void FixedUpdate()
    {
        if((_elapsedTime += Time.fixedDeltaTime) >= interval && _rigidbody2D != null){
            _elapsedTime = 0;
            GameUtil.Dampen(_rigidbody2D, velocityLimit, dampen);
        }
    }
}
