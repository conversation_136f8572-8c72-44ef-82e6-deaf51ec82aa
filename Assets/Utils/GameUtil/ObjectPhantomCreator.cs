using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ObjectPhantomCreator : Singleton<ObjectPhantomCreator>
{
    class PhantomInfo{
        public Transform transform;
        public PhantomCreator phantomCreator;
        public System.Func<bool> isValid;
        
        public void Start(Color color, float fadeSpeed = 2f) {
            phantomCreator = transform.gameObject.AddComponent<PhantomCreator>();
            phantomCreator.mode = PhantomCreator.emTargetMode.SpriteRender;
            phantomCreator.sprite_color = color;
            phantomCreator.interval = 0.05f;
            phantomCreator.endAlpha = 0.05f;
            phantomCreator.fadeSpeed = fadeSpeed;
            phantomCreator.enabled = true;
            phantomCreator.syncScaleTf = transform;
        }

        public void End(){
            GameObject.Destroy(phantomCreator);
        }
    }

    List<PhantomInfo> _list = new List<PhantomInfo>();
    public void AddPhantom(Transform target, Color color, System.Func<bool> isValid){
        var newInfo = new PhantomInfo(){
            transform = target,
            isValid = isValid,
        };
        newInfo.Start(color);
        _list.Add(newInfo);
    }

    float _checkCooldown = 0;
    void Update(){
        if((_checkCooldown -= Time.deltaTime) <= 0){
            _checkCooldown = 0.2f;
            DoClean();
        }
    }

    public void DoClean(){
        for(var i = _list.Count - 1; i >= 0; --i){
            if(_list[i].isValid?.Invoke() == false){
                _list[i].End();
                _list.RemoveAt(i);
            }
        }
    }
}
