using System;
using UnityEngine;

namespace AudioPlayer {
    public class ColdDownAudioPlayer : RandomBehaviour, IAudioPlayer {
        public AudioClip[] audioClips;
        public float duration;
        public bool useUnscaledTime;
        private static float _startTime = Single.NaN;
        private float CurrentTime => useUnscaledTime ? Time.unscaledTime : Time.time;

        private void Awake() {
            // SetUpRGRandom();
        }

        public void PlayAudio() {
            if (!float.IsNaN(_startTime) && CurrentTime - _startTime < duration) {
                return;
            }

            _startTime = CurrentTime;
            var currentClip = audioClips.GetRandomObject(rg_random);
            RGMusicManager.Inst.PlayEffect(currentClip);
        }
    }
}