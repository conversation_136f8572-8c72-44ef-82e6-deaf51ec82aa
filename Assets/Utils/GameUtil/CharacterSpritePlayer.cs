
using UnityEngine.UI;
using UnityEngine;
using System.Collections.Generic;
using System.Linq;
public class CharacterSpritePlayer : SpritePlayer{

    private static readonly int MaterialSpriteRectID = Shader.PropertyToID("_SpriteRect");
    CharacterSpriteModel _characterSpriteModel;

    public CharacterSpritePlayer(CharacterSpriteModel characterSpriteModel, float frameRate, Image imageSource) : base(frameRate, imageSource) {
        SetCharacterSprite(characterSpriteModel);
        imageSource.material = new Material(imageSource.material);
    }

    public CharacterSpritePlayer(CharacterSpriteModel characterSpriteModel, float frameRate, SpriteRenderer spriteRendererSource) : base(frameRate, spriteRendererSource) {
        SetCharacterSprite(characterSpriteModel);
    }

    private static List<Sprite> SpriteDataList2SpriteList(IEnumerable<CharacterSpritesSpriteData> spriteDataList) {
        return spriteDataList.Select(spriteData => spriteData.GetData()).Where(sprite => sprite).ToList();
    }
    
    private void SetCharacterSprite(CharacterSpriteModel animateModel) {
        OnFrameChange += OnCharacterSpriteChange;
        AddAnimation("idle", SpriteDataList2SpriteList(animateModel.idleSprites));
        AddAnimation("run", SpriteDataList2SpriteList(animateModel.runSprites));
        AddAnimation("dead", SpriteDataList2SpriteList(animateModel.deadSprites));
        PlayAnimation("idle");
    }

    private void OnCharacterSpriteChange(string stateName, Sprite sprite) {
        if (sprite == null) {
            return;
        }

        var spriteRect = sprite.rect;
        var packingRect = sprite.textureRect;
        if (ImageSource == null) {
            return;
        }
        if (ImageSource.material == null) {
            return;
        }
        var material = ImageSource.material;
        material.SetVector(
            MaterialSpriteRectID,
            new Vector4(packingRect.x, packingRect.y, spriteRect.width, spriteRect.height)
        );
        var imageTransform = ImageSource.transform as RectTransform;
        if (imageTransform == null) {
            return;
        }
        imageTransform.sizeDelta = new Vector2(spriteRect.width / 8 * 22, spriteRect.height / 8 * 22);
        if (stateName == "dead") {
            ImageSource.color = new Color(.47f, .47f, .47f);
        }
    }
}