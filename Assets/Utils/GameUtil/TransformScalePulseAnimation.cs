using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;

public class TransformScalePulseAnimation : MonoBehaviour {
    public Transform targetTransform;
    public float expandTime;
    public float shrinkTime;
    public Vector3 maxScale;
    public void Animation() {
        var initScale = targetTransform.localScale;
        var sequence = DOTween.Sequence();
        sequence.Append(targetTransform.DOScale(maxScale, expandTime));
        sequence.Append(targetTransform.DOScale(initScale, shrinkTime));
        sequence.Play();
    }
}
