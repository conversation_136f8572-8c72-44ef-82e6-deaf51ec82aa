using Abo;
using Newtonsoft.Json;
using RGScript.Manager.SDK;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEngine;

public class NewtonJsonUtil {
    public static string ToJson(object data, bool isPretty = false) {
        return JsonConvert.SerializeObject(data, isPretty ? Formatting.Indented : Formatting.None,
            settings: global::GameUtil.JsonSerializerSettings);
    }

    public static T LoadJson<T>(string filePath) {
        T obj = default(T);
        string json = Encoding.UTF8.GetString(SaveUtil.LoadData(filePath));
        if (!string.IsNullOrEmpty(json)) {
            obj = ParseJson<T>(json);
        } else {
            Debug.LogFormat("File content is empty:{0}", filePath);
        }

        return obj;
    }

    public static T ParseJson<T>(string json) {
        return ParseJson<T>(json, "");
    }

    public static T ParseJson<T>(string json, string buglyName = "") {
        if (string.IsNullOrEmpty(json)) {
            return default(T);
        }
        
        try {
            var fixJson = json.Replace("\\0", "").Replace("\\a", "");
            return JsonConvert.DeserializeObject<T>(fixJson, settings: global::GameUtil.JsonSerializerSettings);
        } catch (Exception exception) {
            if (string.IsNullOrEmpty(buglyName)) {
                buglyName = "ParseJson";
            }
            Debug.LogError($"{buglyName}:{exception}");
            return default(T);
        }
    }

    public static T LoadRijndaelData<T>(string filePath, string buglyName = "") {
        long dataLength = 0;
        T obj = default(T);
        try {
            var strData = SaveUtil.LoadRijndaelData(filePath);
            if (!string.IsNullOrEmpty(strData)) {
                dataLength = strData.Length;
                obj = ParseJson<T>(strData);
            } else {
                Debug.LogFormat("File content is empty:{0}", filePath);
            }
        } catch (Exception e) {
            obj = default(T);
            if (e is not System.IO.FileNotFoundException && e is not System.IO.DirectoryNotFoundException) {
                Debug.LogError(e);
                if (!string.IsNullOrEmpty(buglyName)) {
                    BuglyUtil.ReportException(buglyName, e);
                }

                // 上报数据解密异常
                string expName = e.GetType().Name;
                TAUtil.Track("load_crypt_data_fail", new Dictionary<string, object>() {
                    { "crypt_path", filePath },
                    { "crypt_length", dataLength },
                    { "exp_name", GameUtil.CutString(expName.ToString(), 1000) }
                });
                SaveBrokenData(filePath);
            } else {
                Debug.Log(e);
            }
        }

        return obj;
    }

    public static T LoadJsonWithCrypt<T>(string filePath, string secret, string buglyName = "") {
        long dataLength = 0;
        T obj = default(T);
        try {
            string json = FileUtil.ReadFile(filePath);
            if (!string.IsNullOrEmpty(json)) {
                dataLength = json.Length;
                string clearText = CryptUtil.DecryptDES(json, secret);
                if (!string.IsNullOrEmpty(clearText)) {
                    obj = ParseJson<T>(clearText);
                } else {
                    Debug.LogFormat("File content is empty:{0}", filePath);
                }
            } else {
                Debug.LogFormat("File content is empty:{0}", filePath);
            }
        } catch (Exception e) {
            obj = default(T);
            if (e is not System.IO.FileNotFoundException && e is not System.IO.DirectoryNotFoundException) {
                Debug.LogError(e);
                if (!string.IsNullOrEmpty(buglyName)) {
                    BuglyUtil.ReportException(buglyName, e);
                }

                // 上报数据解密异常
                string expName = e.GetType().Name;
                TAUtil.Track("load_crypt_data_fail", new Dictionary<string, object>() {
                    { "crypt_path", filePath },
                    { "crypt_length", dataLength },
                    { "exp_name", expName }
                });
                SaveBrokenData(filePath);
            } else {
                Debug.Log(e);
            }
        }

        return obj;
    }

    static void SaveBrokenData(string filePath) {
        try {
            var directory = Path.Combine(Path.GetDirectoryName(filePath), "BrokenData");
            if (!Directory.Exists(directory)) {
                Directory.CreateDirectory(directory);
            }

            var bakPath = Path.Combine(directory, $"{new FileInfo(filePath).Name}{FileUtil.BrokenFileSuffix}");
            Abo.FileUtil.CopyStringFile(filePath, bakPath);
        } catch (Exception ex) {
            Debug.LogError(ex);
        }
    }
}