using System.Reflection;
using System;
using System.Collections.Generic;
using UnityEngine;

public class SimpleJSONUtil
{
    static public T CreateObjectFromJSON<T>(string text, bool throwExceptionOnInvlalidField) {
        var ret = default(T);
        try{
            var jsonNode = I2.Loc.SimpleJSON.JSONNode.Parse(text) as I2.Loc.SimpleJSON.JSONClass;
            ret = CreateObjectFromJSONNode<T>(jsonNode, throwExceptionOnInvlalidField);
        }
        catch{}
        return ret;
    }

    static public T CreateObjectFromJSONNode<T>(I2.Loc.SimpleJSON.JSONNode node, bool throwExceptionOnInvlalidField) {
        var type = typeof(T);
        return (T)CreateObjectFromJSONNode(type, node, throwExceptionOnInvlalidField);
    }

    static object CreateObjectFromJSONNode(Type objectType, I2.Loc.SimpleJSON.JSONNode node, bool throwExceptionOnInvlalidField){
        object obj = null;
        try{
            if(objectType == typeof(DynamicDataObject)){
                var commonDataObj = new DynamicDataObject();
                commonDataObj.rawValue = node.Value.ToString();
                obj = commonDataObj;
            }
            else if(objectType == typeof(Dictionary<string, DynamicDataObject>)){
                var dict = new Dictionary<string, DynamicDataObject>();
                foreach(var key_value in (node as I2.Loc.SimpleJSON.JSONClass).TopLevelObjects){
                    dict[key_value.Key] = (DynamicDataObject)CreateObjectFromJSONNode(typeof(DynamicDataObject), key_value.Value, throwExceptionOnInvlalidField);
                }
                obj = dict;
            }
            else if(objectType.IsValueType && !objectType.IsEnum && !objectType.IsPrimitive){ // struct
                Assembly assembly = Assembly.GetExecutingAssembly();
                obj = assembly.CreateInstance(objectType.FullName);
                object tempObj = obj;
                foreach(var field_value in (node as I2.Loc.SimpleJSON.JSONClass).TopLevelObjects){
                    var field = objectType.GetField(field_value.Key);
                    if(field != null){
                        var fieldType = field.FieldType;
                        field.SetValue(tempObj, CreateObjectFromJSONNode(fieldType, field_value.Value, throwExceptionOnInvlalidField));
                    }
                    else if(throwExceptionOnInvlalidField){
                        throw new System.Exception($"CreateObjectFromJSONNode Invalid field {field_value.Key} of type {objectType.ToString()}");
                    }
                }
                obj = tempObj;
            }
            else if(!objectType.IsClass){
                var value = node.Value.ToString();
                if(objectType == typeof(float)){
                    obj = float.Parse(value.Trim(), System.Globalization.CultureInfo.InvariantCulture);
                }
                else if(objectType == typeof(int)){
                    obj = int.Parse(value.Trim(), System.Globalization.CultureInfo.InvariantCulture);
                }
                else if(objectType == typeof(bool)){
                    obj = System.Boolean.Parse(value.Trim());
                }
            }
            else if(objectType == typeof(string)){
                obj = node.Value.ToString();
            }
            else if(objectType.IsEnum){
                obj = System.Enum.Parse(objectType, node.Value.ToString());
            }
            else if(objectType.IsArray){
                var elementType = objectType.GetElementType();
                var jsonNodeArray = node.AsArray;
                var array = Array.CreateInstance(elementType, jsonNodeArray.Count);
                for(var i = 0; i < array.Length; ++i){
                    array.SetValue(CreateObjectFromJSONNode(elementType, jsonNodeArray[i], throwExceptionOnInvlalidField), i);
                }
                obj = array;
            }
            else{
                Assembly assembly = Assembly.GetExecutingAssembly();
                obj = assembly.CreateInstance(objectType.FullName);
                foreach(var field_value in (node as I2.Loc.SimpleJSON.JSONClass).TopLevelObjects){
                    var field = objectType.GetField(field_value.Key);
                    if(field != null){
                        var fieldType = field.FieldType;
                        field.SetValue(obj, CreateObjectFromJSONNode(fieldType, field_value.Value, throwExceptionOnInvlalidField));
                    }
                    else if(throwExceptionOnInvlalidField){
                        throw new System.Exception($"CreateObjectFromJSONNode Invalid field {field_value.Key} of type {objectType.ToString()}");
                    }
                }
            }
        }
        catch (Exception e){
            Debug.LogError(e.Message + "\n" + e.StackTrace);
        }
        return obj;
    }

    public struct DynamicDataObject{
        public DynamicDataObject(string s = ""){
            rawValue = s;
            _updated = false;
            _intValue = 0;
            _floatValue = 0;
            _boolValue = false;
        }

        public string rawValue;
        bool _updated;
        int _intValue;
        float _floatValue;
        bool _boolValue;

        void _Refresh(){
            var s = rawValue?.Trim();
            try{
                _intValue = int.Parse(s, System.Globalization.CultureInfo.InvariantCulture);
            }catch{}
            try{
                _floatValue = float.Parse(s, System.Globalization.CultureInfo.InvariantCulture);
            }catch{}
            try{
                _boolValue = System.Boolean.Parse(s);
            }catch{}
        }
        
        public int IntValue{
            get{
                if(!_updated){
                    _Refresh();
                    _updated = true;
                }
                return _intValue;
            }
        }

        public float Value{
            get{
                if(!_updated){
                    _Refresh();
                    _updated = true;
                }
                return _floatValue;
            }
        }

        public bool BoolValue{
            get{
                if(!_updated){
                    _Refresh();
                    _updated = true;
                }
                return _boolValue;
            }
        }

        public string StringValue{
            get{
                return rawValue;
            }
        }
    }
}
