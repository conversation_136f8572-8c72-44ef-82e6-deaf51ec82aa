using ChillyRoom.Services.Accounts.Utils;
using ChillyRoom.Services.Core.Errors;
using I2.Loc;
using System;
using System.Collections.Generic;
using System.Net;

namespace Utils.CommonUtils {
    public static class ErrorUtil {
        public static void HandleApiException(ApiException<ErrorResponse> exception) {
            switch (exception.StatusCode) {
                case (int)HttpStatusCode.Unauthorized:
                    HandleApiException_Unauthorized(exception);
                    break;
                default:
                    HandleApiException_Common(exception);
                    break;
            }
        }

        private static void HandleApiException_Unauthorized(ApiException<ErrorResponse> exception) {
            LogUtil.LogError($"[ErrorUtil] HandleApiException_Unauthorized : {exception.Result.Error}");
            switch (exception.Result.Error) {
                case (int)GenericErrorCode.Unauthenticated:
                    break;
                case (int)GenericErrorCode.PlayerHasBeenBanned:
                    ShowPlayerBannedDetails(exception.Result.Errordetails, true);
                    break;
            }
        }

        private static void HandleApiException_Common(ApiException<ErrorResponse> exception) {
            // switch (exception.Result.Error) {
            //     // 成功
            //     case (int)CommonErrorCodes.SUCCESS:
            //         break;
            //     // 输入异常，超出范围等
            //     case (int)CommonErrorCodes.INVALID_INPUT:
            //         break;
            //     // 找不到资源
            //     case (int)CommonErrorCodes.NOT_FOUND:
            //         break;
            //     // 资源已存在
            //     case (int)CommonErrorCodes.ALREADY_EXISTS:
            //         break;
            //     // 临时问题，可以重试
            //     case (int)CommonErrorCodes.TEMPORARY_UNAVAILABLE:
            //         break;
            //     // 服务关闭
            //     case (int)CommonErrorCodes.CLOSED:
            //         break;
            //     // 网络不通
            //     case (int)CommonErrorCodes.NETWORK_UNREACHABLE:
            //         break;
            //     // 网络请求超时
            //     case (int)CommonErrorCodes.NETWORK_TIMEOUT:
            //         break;
            //     // 根据网络安全策略，当前请求被拒绝
            //     case (int)CommonErrorCodes.NETWORK_UNTRUSTED:
            //         break;
            //     // 服务限流，Details里将有等待时间
            //     case (int)CommonErrorCodes.RATE_LIMITED:
            //         break;
            //     // 服务过载，功能关闭
            //     case (int)CommonErrorCodes.OVERLOAD:
            //         break;
            //     // 服务器内部错误
            //     case (int)CommonErrorCodes.INTERNAL_ERROR:
            //         break;
            //     default:
            //         // 出现了以上之外的其他错误码
            //         throw new ArgumentOutOfRangeException();
            // }

            LogUtil.LogError($"[ErrorUtil] HandleApiException_Common : {exception.Result.Error}");
            var resultStr = $"{ScriptLocalization.Get("multi_remote/client_unknowerror")}_{exception.Result.Error}";
            CommonUiUtil.ShowMsg(UICanvasRoot.Inst.transform, resultStr, 3.5f);
        }

        public static void ShowPlayerBannedDetails(IDictionary<string, string> details, bool accountBanned) {
            string title;
            string content;
            if (accountBanned) {
                title = ScriptLocalization.Get("player_banned/account_title", "#账号已被冻结");
                content = ScriptLocalization.Get("player_banned/account_desc_0", "#检测到该账号有异常行为");
            } else {
                title = ScriptLocalization.Get("player_banned/multi_game_title", "#联机功能已被禁用");
                content = ScriptLocalization.Get("player_banned/multi_game_desc", "#检测到该账号有异常行为，联机功能被禁用。");
            }

            if (details.TryGetValue("localizedDetail", out string localizedDetail) && !string.IsNullOrEmpty(localizedDetail)) {
                content = localizedDetail;
            } else if (details.TryGetValue("reason", out string reason) && !string.IsNullOrEmpty(reason)) {
                content = reason;
                if (details.TryGetValue("timeLeft", out string timeLeft) && !string.IsNullOrEmpty(timeLeft)) {
                    if (long.TryParse(timeLeft, out var time)) {
                        var timeLeftSpan = TimeSpan.FromSeconds(time);
                        var timeStr0 = $"\n{ScriptLocalization.Get("player_banned/multi_game_time_left", "#解禁剩余时间：{0}")}";
                        var timeStr1 = $"{timeLeftSpan.Days}{ScriptLocalization.Get("mailbox/day")} {timeLeftSpan.Hours}{ScriptLocalization.Get("mailbox/hour")} {timeLeftSpan.Minutes}{ScriptLocalization.Get("sb/setting_minute")}";
                        timeStr0 = timeStr0.Replace("{0}", timeStr1);
                        content += timeStr0;
                    }
                }
            }
            
            if (details.TryGetValue("uid", out var uid)) {
                content += $" uid:{Int64.Parse(uid)}";
            }

            UiUtil.ShowDialogWindow(UICanvasRoot.Inst.transform, title,
                content, (ScriptLocalization.Get("anti/btn_know"), null), ("", null));
        }
    }
}