
using UnityEngine;

public class Sprite<PERSON>endererUtil {
    public static Vector3 AlignToSpriteCenter(SpriteRenderer spriteRenderer) {
        // 检查 spriteRenderer 和 sprite 是否为空
        if (spriteRenderer == null || spriteRenderer.sprite == null) {
            return Vector3.zero;
        }

        // 获取 sprite 的相关属性
        Sprite sprite = spriteRenderer.sprite;
        Rect rect = sprite.rect;
        Vector2 pivot = sprite.pivot;
        float pixelsPerUnit = sprite.pixelsPerUnit;

        // 计算精灵的中心相对于 pivot 的偏移
        Vector2 centerOffset = new Vector2(
            (rect.width * 0.5f - pivot.x) / pixelsPerUnit,
            (rect.height * 0.5f - pivot.y) / pixelsPerUnit
        );

        return new Vector3(-centerOffset.x, -centerOffset.y, 0);
    }

    public static float GetSpriteRadius(Sprite sprite) {
        float pixelsPerUnit = sprite.pixelsPerUnit;
        var x = sprite.rect.width / pixelsPerUnit;
        var y = sprite.rect.height / pixelsPerUnit;
        return Mathf.Sqrt(x * x + y * y) / 2;
    }
}
