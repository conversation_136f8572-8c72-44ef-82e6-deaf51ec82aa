using SK_Editor;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using I2.Loc;
using RGScript.Other.GameEmail;
using Sirenix.OdinInspector;
using Sirenix.OdinInspector.Editor;
using Sirenix.Utilities.Editor;
using UnityEditor;
using UnityEngine;

public class AnnouncementGenerateWindow : OdinEditorWindow {
    [MenuItem("Window/生成公告")]
    public static void ShowChangeFontWindow() {
        var window = EditorWindow.GetWindow<AnnouncementGenerateWindow>("公告生成器");
    }

    [BoxGroup("读取")] public TextAsset announcementFile;

    [<PERSON>Group("读取"), Button("读取公告")]
    public void ReadAnnouncement() {
        announcementInfos = new List<AnnouncementInfoOrigin>();
        var config = Abo.JsonUtil.ParseJson<AnnouncementInfoConfig>(announcementFile.text);
        foreach (var info in config.announcementInfos) {
            var origin = new AnnouncementInfoOrigin();
            origin.announcementList = info.announcementList;
            origin.start_time_stamp = long.Parse(info.start_time_stamp);
            origin.stop_time_stamp = long.Parse(info.stop_time_stamp);
            announcementInfos.Add(origin);
        }

        foreach (var image in config.images) {
            if (!string.IsNullOrEmpty(image.Key) && image.Value.data != null &&
                image.Value.data.Length > 0) {
                Sprite sp = Sprite.Create(image.Value.data.ToTexture(image.Value.width, image.Value.height)
                    , new Rect(0, 0, image.Value.width, image.Value.height), Vector2.zero, 16,0,SpriteMeshType.FullRect);
                textureDic.Add(image.Key, sp.texture);
            }
        }

        version = config.version;
        linkDic = config.links;
    }

    [BoxGroup("生成")] public int version;
    [BoxGroup("生成")] public List<AnnouncementInfoOrigin> announcementInfos = new List<AnnouncementInfoOrigin>();

    [BoxGroup("生成")] [ShowInInspector]
    public Dictionary<string, Texture> textureDic = new Dictionary<string, Texture>();

    [BoxGroup("生成")] [ShowInInspector] public Dictionary<string, string> linkDic = new Dictionary<string, string>();

    [BoxGroup("生成"), FolderPath(AbsolutePath = true)]
    public string generatePath;

    [BoxGroup("生成"), Button("生成公告")]
    public void GenerateAnnouncement() {
        DealTextureReadable();

        var config = new AnnouncementInfoConfig();
        
        config.version = version;
        
        foreach (var info in announcementInfos) {
            var announcementInfo = new AnnouncementInfo();
            announcementInfo.start_time_stamp = info.start_time_stamp.ToString();
            announcementInfo.stop_time_stamp = info.stop_time_stamp.ToString();
            announcementInfo.announcementList = info.announcementList;
            config.announcementInfos.Add(announcementInfo);
        }

        config.images = textureDic.Where(_ => !string.IsNullOrEmpty(_.Key) && _.Value != null)
            .ToDictionary(_ => _.Key, _ => (_.Value as Texture2D).GetPngTuple());

        config.links = linkDic;
        
        WUDI_DEBUG.WriteToFile("AnnouncementInfoConfig", "json", generatePath, Abo.JsonUtil.ToJson(config));
    }

    /// <summary>
    /// 编辑器模式下Texture的可读
    /// </summary>
    /// <param name="texture"></param>
    /// <returns></returns>
    private void DealTextureReadable() {
        foreach (var texture2d in textureDic.Values) {
            var assetPath = AssetDatabase.GetAssetPath(texture2d);
            if (!string.IsNullOrEmpty(assetPath)) {
                UnityEditor.TextureImporter ti =
                    (UnityEditor.TextureImporter)UnityEditor.TextureImporter.GetAtPath(
                        UnityEditor.AssetDatabase.GetAssetPath(texture2d));
                if (!ti.isReadable) {
                    //图片Read/Write Enable的开关                                                                                                                                           
                    ti.isReadable = true;
                    UnityEditor.AssetDatabase.ImportAsset(UnityEditor.AssetDatabase.GetAssetPath(texture2d));
                }
            }
        }
    }
}

[Serializable]
public class AnnouncementInfoOrigin {
    [VerticalGroup("Group 1")] [LabelText("开始时间戳")]
    public long start_time_stamp;

    [VerticalGroup("Group 1")]
    [LabelText(" ")]
    [ShowInInspector]
    public string start =>
        TimeUtil.TimeStampToDateTime(start_time_stamp.ToString()).ToString(CultureInfo.CurrentCulture);

    [VerticalGroup("Group 2")] [LabelText("结束时间戳")]
    public long stop_time_stamp;

    [VerticalGroup("Group 2")]
    [LabelText(" ")]
    [ShowInInspector]
    public string stop => TimeUtil.TimeStampToDateTime(stop_time_stamp.ToString()).ToString(CultureInfo.CurrentCulture);

    /// <summary>
    /// key : 语言代码
    /// value : 公告本地化内容
    /// </summary>
    [ListDrawerSettings(OnBeginListElementGUI = "BeginDrawAnnouncementListElement",
        OnEndListElementGUI = "EndDrawAnnouncementListElement")]
    public AnnouncementItem[] announcementList = new AnnouncementItem[LocalizationManager.GetAllLanguages().Count];


    private void BeginDrawAnnouncementListElement(int index) {
        SirenixEditorGUI.BeginBox(LocalizationManager.GetAllLanguages()[index]);
    }

    private void EndDrawAnnouncementListElement(int index) {
        SirenixEditorGUI.EndBox();
    }
}