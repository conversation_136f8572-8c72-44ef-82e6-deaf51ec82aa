using RGScript.UI.ChooseHero;
using RGTest.Editor.OdinWindow;
using Sirenix.OdinInspector;
using SoulKnight.Runtime.Skill;
using System;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;
using Util.Model;
using Random = UnityEngine.Random;

// ReSharper disable Odin.OdinUnknownGroupingPath

namespace Utils.DebugUtil.Editor.DebugWindow.TestCommonDebug {
    public partial class TestCommonDebugWindow {
        private RGSaveManager saveManager;
        private ValueDropdownList<int> _skinValueDropdownList = new();

        [OnInspectorInit]
        public void InitDataWindow() {
            saveManager = TestUtil.ReturnSingletonGroupSaveManager();
        }

        [ShowIfGroup("DataWindow")]
        [PropertyOrder(1)]
        [BoxGroup("DataWindow/存档面板", centerLabel: true)]
        [HorizontalGroup("DataWindow/存档面板/G1")]
        [Button("初始化存档", ButtonSizes.Large)]
        public void InitData() {
            string directoryPath = Application.persistentDataPath;

            var files = Directory.GetFiles(directoryPath);
            var directories = Directory.GetDirectories(directoryPath);

            foreach (var file in files) {
                File.Delete(file);
            }

            foreach (var dir in directories) {
                Directory.Delete(dir, true);
            }

            PlayerPrefs.DeleteAll();
        }

        [HorizontalGroup("DataWindow/存档面板/G1")]
        [Button("满级存档", ButtonSizes.Large)]
        public void GetEverything() {
            var groupObject = TestUtil.ReturnSingletonGroup();

            PlayerSaveData.SetInt("has_tutorial", 1);
            PlayerPrefs.Save();
            StatisticData.data.RecordEvent(RGGameConst.NEED_NEW_PLAYER_TUTORIAL, false);
            StatisticData.data.RecordEvent(RGGameConst.NEW_PLAYER_TUTORIAL_GEM_REWARD, false);
            StatisticData.data.SetEventCount(RGGameConst.NEW_PLAYER_FACTOR_COUNT, 0, true);

            PlayerSaveData.SetInt("first_in_room", 7);
            StatisticData.data.RecordEvent(RGGameConst.ADV_TOTURIAL, true);
            StatisticData.data.RecordEvent(RGGameConst.FIRST_FORGE_WEAPON, true);
            StatisticData.data.RecordEvent(RGGameConst.FIRST_PLANT, true);
            StatisticData.data.RecordEvent(RGGameConst.FIRST_TASK, true);
            StatisticData.data.RecordEvent(RGGameConst.FIRST_USE_HERO_LIST, true);
            StatisticData.data.RecordEvent(RGGameConst.FIRST_USE_TICKET, true);
            StatisticData.data.RecordEvent(RGGameConst.FIRST_CHANGE_SKIN, true);
            StatisticData.data.RecordEvent(RGGameConst.FIRST_SEASON, true);
            StatisticData.data.RecordEvent(RGGameConst.FIRST_USE_HOME, true);
            StatisticData.data.RecordEvent(RGGameConst.NEED_GUIDE_SEASON_OR_DEFENCE, true);
            StatisticData.Save();

            for (int i = 0; i < emHero.Count.ToInt(); i++) {
                DataUtil.SetHeroUnlock((emHero)i, true);
                PlayerPrefs.SetInt($"c{i}_level", 7);
            }

            for (int i = 0; i < emHero.Count.ToInt(); i++) {
                for (int j = 1; j < saveManager.char_list[i].skin_list.Length; j++) {
                    DataUtil.SetSkinUnlock((emHero)i, j, true);
                }
            }

            for (var i = 0; i < saveManager.pet_list.Length; i++) {
                RGSaveManager.Inst.PetUnLock(i);
            }

            UnlockAllRoomItem();

            //解锁联机房皮肤
            for (var i = 0; i < ScriptableConfigUtil.Load<MultiRoomSkinConfig>().data.Count; i++) {
                DataUtil.SetMultiRoomSkinUnlock(i);
            }

            //所有武器获取数量+8
            foreach (var weaponInfo in WeaponInfo.info.name2Weapon.Where(weaponInfo =>
                         weaponInfo.Value.type != emWeaponType.Token)) {
                StatisticData.data.AddObtainTimes(weaponInfo.Key, 8, false);
            }

            //试炼之地瓦尔基里解锁
            StatisticData.data.SetEventCount(RGGameConst.STATIC_DATA_BOSSRUSH, 3, true);

            StatisticData.Save();

            DestroyImmediate(groupObject);
            PlayerPrefs.Save();
        }

        [BoxGroup("DataWindow/存档面板/Skin", false)]
        [HorizontalGroup("DataWindow/存档面板/Skin/1", 0.4f)]
        [OnValueChanged(nameof(InitSkinListByHero))]
        [LabelText("英雄")]
        [LabelWidth(100)]
        public emHero hero;
        
        public emHero GetHero{
            get {
                if (hero != emHero.None) {
                    return hero;
                }

                GetRoleData(out emHero curHero, out int curSkinIndex);
                skinCount = curSkinIndex;
                return curHero;
            }
        }



        [HorizontalGroup("DataWindow/存档面板/Skin/1", 0.3f)]
        [ValueDropdown(nameof(_skinValueDropdownList))]
        [HideLabel]
        public int skinCount;
        
        [HorizontalGroup("DataWindow/存档面板/Skin/1", 0.3f)]
        [OnValueChanged(nameof(LockRandomCharacter))]
        [LabelText("多重人格锁定")] [LabelWidth(100)]
        public bool isLock;
        
        public void LockRandomCharacter() {
            if (isLock) {
                SimpleEventManager.AddEventListener<UpdateBattleDataEvent>(ChangeRandomCharacterIndex);
            } else {
                SimpleEventManager.RemoveEventListener<UpdateBattleDataEvent>(ChangeRandomCharacterIndex);
            }
        }

        private void ChangeRandomCharacterIndex(UpdateBattleDataEvent e) {
            if (BattleData.data.CompareFactor(emBattleFactor.RandomCharactor)) {
                BattleData.data.playerIndex = hero.ToInt();
                BattleData.data.skinIndex = skinCount;
                var skillCount = SkillConfigLoader.GetHeroSkillCount(hero.ToInt());
                BattleData.data.skillIndex = Random.Range(0, skillCount);
                BattleData.data.heroLevel = RGSaveManager.Inst.char_list[BattleData.data.playerIndex].level;
            } else {
                Debug.LogError("当前没有多重人格因子，工具无法生效");
            }
        }

        [HorizontalGroup("DataWindow/存档面板/Skin/2")]
        [Button("解锁皮肤", ButtonSizes.Medium)]
        public void UnlockSkin() {
            DataUtil.SetSkinUnlock((emHero)(int)GetHero, skinCount, true);
            PlayerSaveData.Save();
        }

        [HorizontalGroup("DataWindow/存档面板/Skin/2")]
        [Button("锁定皮肤", ButtonSizes.Medium)]
        public void LockSkin() {
            emHero tempHero = GetHero;
            DataUtil.SetSkinUnlock(tempHero, skinCount, false);
            RGSaveManager.Inst.char_list[(int)tempHero].skin_list[skinCount] =
                RGSaveManager.Inst.char_list[(int)tempHero].origin_skin_list[skinCount];

            if (ItemData.data.GetBluePrintStatus($"blueprint_skin_{tempHero.ToString()}_{skinCount}".ToLower()) ==
                emBluePrintStatus.Researched) {
                ItemData.data.blueprints[$"blueprint_skin_{tempHero.ToString()}_{skinCount}".ToLower()] =
                    emBluePrintStatus.None;
            }

            PlayerSaveData.Save();
        }

        [HorizontalGroup("DataWindow/存档面板/Skin/2")]
        [Button("解锁全部皮肤", ButtonSizes.Medium)]
        public void UnlockAllSkin() {
            if (!Application.isPlaying) {
                this.ShowNotification(new GUIContent("需要先运行游戏"));
                return;
            }
            for (int i = 0; i < emHero.Count.ToInt(); i++) {
                for (int j = 1; j < saveManager.char_list[i].skin_list.Length; j++) {
                    RGSaveManager.Inst.char_list[i].skin_list[j] = 1;
                    PlayerSaveData.SetInt("c" + i + "_skin" + j, 1);
                }
            }

            SimpleEventManager.Raise(new SkinUnlockEvent {
                hero = hero,
                skinIndex = 1,
                isUnlock = true
            });
            SimpleEventManager.Raise(new HonoraryTitleProgressUpdate() {
                key = 6,
            });
            PlayerPrefs.Save();
            this.ShowNotification(new GUIContent("解锁成功"));
        }

        [HorizontalGroup("DataWindow/存档面板/Skin/2")]
        [Button("锁定全部皮肤", ButtonSizes.Medium)]
        public void LockAllSkin() {
            if (!Application.isPlaying) {
                this.ShowNotification(new GUIContent("需要先运行游戏"));
                return;
            }
            for (int i = 0; i < emHero.Count.ToInt(); i++) {
                RGSaveManager.Inst.char_list[i].skin_list = RGSaveManager.Inst.char_list[i].origin_skin_list;
                for (int j = 1; j < saveManager.char_list[i].skin_list.Length; j++) {
                    int skinValue = RGSaveManager.Inst.char_list[i].skin_list[j];
                    RGSaveManager.Inst.char_list[i].skin_list[j] = skinValue;
                    PlayerSaveData.SetInt("c" + i + "_skin" + j, skinValue);
                    RGSaveManager.Inst.char_list[i].skin_list[j] =
                        RGSaveManager.Inst.char_list[i].origin_skin_list[j];
                }
            }

            SimpleEventManager.Raise(new SkinUnlockEvent {
                hero = hero,
                skinIndex = 1,
                isUnlock = false
            });
            SimpleEventManager.Raise(new HonoraryTitleProgressUpdate() {
                key = 6,
            });
            PlayerPrefs.Save();
        }

        [HorizontalGroup("DataWindow/存档面板/Skin/3")]
        [Button("解锁英雄", ButtonSizes.Medium)]
        public void UnlockHero() {
            DataUtil.SetHeroUnlock(GetHero, true);
        }

        [HorizontalGroup("DataWindow/存档面板/Skin/3")]
        [Button("锁定英雄", ButtonSizes.Medium)]
        public void LockHero() {
            RGSaveManager.Inst.char_list[(int)GetHero].unlock = false;
            DataUtil.SetHeroLevel(hero, 0);
            PlayerSaveData.SetString("c" + (int)hero + "_unlock", false.ToString());
        }

        [HorizontalGroup("DataWindow/存档面板/Skin/3")]
        [Button("解锁全部英雄", ButtonSizes.Medium)]
        public void UnlockAllHero() {
            for (int i = 0; i < emHero.Count.ToInt(); i++) {
                DataUtil.SetHeroUnlock((emHero)i, true);
            }
        }

        [HorizontalGroup("DataWindow/存档面板/Skin/3")]
        [Button("锁定全部英雄", ButtonSizes.Medium)]
        public void LockAllHero() {
            for (int i = 0; i < emHero.Count.ToInt(); i++) {
                RGSaveManager.Inst.char_list[i].unlock = false;
                PlayerSaveData.SetString("c" + i + "_unlock", false.ToString());
            }
        }

        public void InitSkinListByHero() {
            _skinValueDropdownList = new ValueDropdownList<int>();
            if (hero == emHero.None) {
                return;
            } 
            for (int i = 0; i < saveManager.char_list[(int)hero].skin_list.Length; i++) {
                _skinValueDropdownList.Add(DataUtil.GetSkinName(hero, i, true), i);
            }

            skinCount = 0;
        }
        
       private void GetRoleData(out emHero emHero, out int index) {
           emHero = emHero.None;
           index = 0;

           var player = RGGameSceneManager.Inst.controller;
           if (player) {
               emHero = player.GetHeroType();
               index = player.GetSkinIndex();
           } else {
               ChooseHeroModel model = GameObject.FindObjectOfType<ChooseHeroModel>();
               if (model != null) {
                   var field = ReflectionHelper.GetFieldNamedData(model.GetType(), "data");
                   object obj = field.GetValue(model);
                   ChooseHeroData data = (ChooseHeroData)obj;
                   emHero = (emHero)data.id;
                   index = data.skinIndex;
               }
           }
        }

        [HorizontalGroup("DataWindow/存档面板/G2", 0.5f)] [LabelText("房屋家具")]
        public emRoomUnlockItem roomUnlockItem;

        [HorizontalGroup("DataWindow/存档面板/G2", 0.25f)]
        [Button("单个解锁", ButtonSizes.Medium)]
        public void UnlockRoomItem() {
            RoomItemUnlockThroughBattle.TrySetUnlock(roomUnlockItem);
        }

        [HorizontalGroup("DataWindow/存档面板/G2", 0.25f)]
        [Button("全部解锁", ButtonSizes.Medium)]
        public void UnlockAllRoomItem() {
            foreach (emRoomUnlockItem item in Enum.GetValues(typeof(emRoomUnlockItem))) {
                RoomItemUnlockThroughBattle.TrySetUnlock(item);
            }
        }

        [HorizontalGroup("DataWindow/存档面板/G3", 0.5f)] [LabelText("游玩次数")]
        public int statementCount;

        [HorizontalGroup("DataWindow/存档面板/G3", 0.5f)]
        [Button("设置数据", ButtonSizes.Medium)]
        public void SetStatementData() {
            StatisticData.data.AddEventCount(RGGameConst.ROOM_UNLOCK_STATEMENTS_COUNT, statementCount, true);
        }

        [HorizontalGroup("DataWindow/存档面板/G4", 0.5f)] [LabelText("神话武器解锁")]
        public int mythicWeaponUnlockCount = 5;

        [Button("解锁神话武器", ButtonStyle.FoldoutButton)]
        [HorizontalGroup("DataWindow/存档面板/G4", 0.5f)]
        public void UnlockAllMythicWeapon() {
            for (int i = 0; i < mythicWeaponUnlockCount; i++) {
                foreach (var m in ItemData.data.AllMythicWeapons()) {
                    ItemData.data.UnlockMythicWeapon(m.id, false);
                }
            }

            ItemData.Save();
        }

        [HorizontalGroup("DataWindow/存档面板/G5", 0.3f)]
        [LabelText("编辑器时间")]
        [LabelWidth(100)]
        [OnValueChanged(nameof(ChangeEditorTime))]
        public bool useEditorTime;

        [HorizontalGroup("DataWindow/存档面板/G5", 0.7f)] [HideLabel] [OnValueChanged(nameof(ChangeEditorTime))]
        public Date eventTime;

        private void ChangeEditorTime() {
            EditorPrefs.SetBool(Date.IsUseEditorTimeKey, useEditorTime);
            EditorPrefs.SetString(Date.EditorTimeKey, eventTime.TimeStamp.ToString());
        }
    }
}