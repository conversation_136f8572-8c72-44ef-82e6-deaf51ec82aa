using RGTest.Editor.OdinWindow;
using Sirenix.OdinInspector;
using Sirenix.OdinInspector.Editor;
using SoulKnight.Editor.Common.Data;
using System;
using UnityEditor;
using UnityEngine;

// ReSharper disable InconsistentNaming

namespace Utils.DebugUtil.Editor.DebugWindow.TestCommonDebug {
    public partial class TestCommonDebugWindow : OdinEditorWindow {
        private ValueDropdownList<string> _weaponDropDown => TestUtil.ReturnWeaponValuePathDropDownList();
        private ValueDropdownList<string> _weaponEvolutionValueDropDownList => TestUtil.ReturnWeaponEvolutionValueDropDownList();
        private ValueDropdownList<string> _weaponSkinValueDropDownList => TestUtil.ReturnWeaponSkinValueDropDownList();
        private ValueDropdownList<string> _weaponEvolutionAndSkinValueDropDownList => TestUtil.ReturnWeaponEvolutionSkinValueDropDownList();
        private ValueDropdownList<string> _categorizedAssetDropDown => TestUtil.ReturnCategorizedAssetDropdownList();
        private ValueDropdownList<GameObject> _chestDropDown => TestUtil.ReturnChestValueDropDownList();
        private ValueDropdownList<GameObject> _specialRoomPrefabList => TestUtil.ReturnSpecialRoomValueDropDownList();
        private ValueDropdownList<string> PickableItemDropDownList =>
            ChooseItemDataUtil.GetValueDropdownList(emItemType.None);
        private ValueDropdownList<GameObject> drinkDropDownList => TestUtil.ReturnDrinkValueDropDownList();
        private ValueDropdownList<emBattleFactor> battleFactorDropDown =>
            TestUtil.ReturnBattleFactorsValueDropDownList();
        private ValueDropdownList<emBuff> _buffDropDownList = new ();
        private ValueDropdownList<string> _enemyDropDown => TestUtil.ReturnEnemyPathValueDropDownList();
        private ValueDropdownList<string> _npcDropDown => TestUtil.ReturnNpcPathValueDropDownList();
        // ReSharper disable once CollectionNeverQueried.Local
        private ValueDropdownList<string> _combinedDropDown;

        [OnInspectorInit]
        public void InitWindow() {
            InitEnemyAndNpcDropDownList();
            InitPlayerAttribute();
            AddOrRemoveListenerByConfig();
        }
        
        protected override void OnEnable() {
            base.OnEnable();
            _buffDropDownList = TestUtil.ReturnBuffValueDropDownList();
        }

        protected override void OnDestroy() {
            base.OnDestroy();
            _skillTimer?.Cancel();
        }

        private void AddOrRemoveListenerByConfig() {
            if (EditorPrefs.GetBool("InstantiateTransferGate", true)) {
                SimpleEventManager.AddEventListener<AfterCreatePlayerEvent>(CreateGateEvent);
            } else {
                SimpleEventManager.RemoveEventListener<AfterCreatePlayerEvent>(CreateGateEvent);
            }

            if (EditorPrefs.GetBool(nameof(RebornCountEndless), false)) {
                SimpleEventManager.AddEventListener<AfterCreatePlayerEvent>(BindingResetRebornCountToRebornAction);
            } else {
                SimpleEventManager.RemoveEventListener<AfterCreatePlayerEvent>(BindingResetRebornCountToRebornAction);
            }
        }

        [Flags]
        public enum TestSetting {
            [LabelText("角色属性面板")] A = 1 << 0,
            [LabelText("物品创建面板")] B = 1 << 1,
            [LabelText("存档面板")] C = 1 << 2,
            [LabelText("敌人和NPC创建面板")] D = 1 << 3,
            [LabelText("跳关面板")] E = 1 << 4,
            [LabelText("天赋面板")] G = 1 << 5,
            [LabelText("因子面板")] H = 1 << 6,
            [LabelText("基础设置面板")] I = 1 << 7
        }

        [EnumToggleButtons] [LabelText("选择需要显示的面板")] [PropertySpace(SpaceAfter = 20)] [PropertyOrder(-1)]
        public TestSetting testSetting;

        public bool ItemCreate => testSetting.HasFlag(TestSetting.B);
        public bool EnemyCreate => testSetting.HasFlag(TestSetting.D);
        public bool DataWindow => testSetting.HasFlag(TestSetting.C);
        public bool HeroSettingWindow => testSetting.HasFlag(TestSetting.A);
        public bool JumpLevel => testSetting.HasFlag(TestSetting.E);
        public bool FactorWindow => testSetting.HasFlag(TestSetting.H);
        public bool BuffWindow => testSetting.HasFlag(TestSetting.G);
        public bool EditorSettingWindow => testSetting.HasFlag(TestSetting.I);

        private static bool CheckIsEnterAndPickHero() {
            return !Application.isPlaying || !RGGameSceneManager.Inst || !RGGameSceneManager.Inst.controller;
        }
    }
}