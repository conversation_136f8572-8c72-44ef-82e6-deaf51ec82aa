using RGScript.Data;
using RGScript.Map;
using RGScript.Net.StateSync.Core;
using RGScript.Net.StateSync.Objects;
using RGScript.Weapon;
using RGScript.WeaponEvolution;
using Sirenix.OdinInspector;
using SoulKnight.Runtime.Item;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using UnityEngine.Serialization;

// ReSharper disable Odin.OdinUnknownGroupingPath

namespace Utils.DebugUtil.Editor.DebugWindow.TestCommonDebug {
    public partial class TestCommonDebugWindow {
        private void CreateObject(GameObject itemGameObject, bool isWeapon = false) {
            if (!Application.isPlaying) {
                Debug.LogError("存档相关调试工具: 只能在游戏运行时使用");
                return;
            }

            if (itemGameObject == null) {
                return;
            }

            var controller = RGGameSceneManager.Inst.controller;
            if (controller == null) {
                Debug.LogError("存档相关调试工具: 无法找到玩家");
                return;
            }

            if (global::GameUtil.IsSingleGame() || !isWeapon) {
                var newItem = Instantiate(itemGameObject,
                    controller.transform.position + Vector3.down * .5f,
                    Quaternion.identity,
                    RGGameSceneManager.Inst.temp_objects_parent);
                newItem.name = itemGameObject.name;
            } else {
                itemGameObject = WeaponFactory.CreateWeaponDisableRegistration(itemGameObject);
                itemGameObject.transform.position = controller.transform.position + Vector3.down * .5f;
                if (itemGameObject.TryGetComponent<StateSyncRgWeapon>(out var stateSyncRgWeapon)) {
                    stateSyncRgWeapon.ClientSyncObjectToOtherPlayers(controller);
                }
            }
        }

        [FormerlySerializedAs("weaponGameObject")]
        [ShowIfGroup("ItemCreate")]
        [BoxGroup("ItemCreate/物品创建面板/Weapon", false)]
        [HorizontalGroup("ItemCreate/物品创建面板/Weapon/G1", 0.5f)]
        [LabelWidth(100)]
        [ShowInInspector, ValueDropdown(nameof(_weaponDropDown)), LabelText("武器")]
        public string weapon;

        [Button("创建武器", ButtonStyle.FoldoutButton)]
        [HorizontalGroup("ItemCreate/物品创建面板/Weapon/G1")]
        public void CreateWeapon() {
            var obj = WeaponFactory.GetWeaponPrefab(weapon);
            CreateObject(obj, true);
        }

        [Button("清空蓝图碎片", ButtonStyle.FoldoutButton)]
        [HorizontalGroup("ItemCreate/物品创建面板/Weapon/G1")]
        public void ClearEvolvedWeaponBlueprintAndFragment() {
            foreach (var config in DataMgr.ConfigData.Tables.TbWeaponEvolution.DataList) {
                var weaponName = config.WeaponName;
                var blueprintName = WeaponEvolutionModule.WeaponBlueprintPrefix + weaponName;
                var fragmentName = WeaponEvolutionModule.WeaponBlueprintFragmentPrefix + weaponName;
                var count = ItemData.data.GetMaterialCount(fragmentName);
                if (count > 0) {
                    ItemData.data.ConsumeMaterial(fragmentName, count);
                }

                if (ItemBluePrint.HasBlueprint(blueprintName)) {
                    ItemData.data.blueprints[blueprintName] = emBluePrintStatus.None;
                }

                ItemData.Save();
            }
        }

        [Button("武器升级", ButtonStyle.FoldoutButton)]
        [HorizontalGroup("ItemCreate/物品创建面板/Weapon/G2")]
        public void WeaponUpgrade() {
            foreach (var item in _weaponEvolutionValueDropDownList) {
                if (item.Value == weapon) {
                    WeaponEvolutionData.data.SetWeaponEvolution(item.Value, true);
                    ItemData.data.ResearchBlueprint($"blueprint_evolution_{item.Value}");
                }
            }
        }

        [Button("取消升级", ButtonStyle.FoldoutButton)]
        [HorizontalGroup("ItemCreate/物品创建面板/Weapon/G2")]
        public void CancelWeaponUpgrade() {
            foreach (var item in _weaponEvolutionValueDropDownList) {
                if (item.Value == weapon) {
                    WeaponEvolutionData.data.SetWeaponEvolution(item.Value, false);
                    if (ItemData.data.blueprints.ContainsKey($"blueprint_evolution_{item.Value}")) {
                        ItemData.data.blueprints[$"blueprint_evolution_{item.Value}"] = emBluePrintStatus.None;
                    }
                }
            }
        }

        [Button("解锁皮肤", ButtonStyle.FoldoutButton)]
        [HorizontalGroup("ItemCreate/物品创建面板/Weapon/G2")]
        public void UnlockWeaponSkin() {
            foreach (var item in _weaponSkinValueDropDownList) {
                if (item.Value == weapon) {
                    WeaponEvolutionData.data.SetWeaponSkin(weapon, 1);
                }
            }
        }

        [Button("原皮肤", ButtonStyle.FoldoutButton)]
        [HorizontalGroup("ItemCreate/物品创建面板/Weapon/G2")]
        public void LocockWeaponSkin() {
            foreach (var item in _weaponSkinValueDropDownList) {
                if (item.Value == weapon) {
                    WeaponEvolutionData.data.SetWeaponSkin(weapon, 0);
                }
            }
        }

        [BoxGroup("ItemCreate/物品创建面板")]
        [HorizontalGroup("ItemCreate/物品创建面板/G2", 0.7f)]
        [LabelWidth(100)]
        [ShowInInspector, ValueDropdown(nameof(_categorizedAssetDropDown)), LabelText("配件/机甲/鱼竿")]
        public string categorizedAssetPath;

        [Button("创建", ButtonStyle.FoldoutButton)]
        [HorizontalGroup("ItemCreate/物品创建面板/G2")]
        public void CreateCategorizedAsset() {
            var obj = AssetDatabase.LoadAssetAtPath<GameObject>(categorizedAssetPath);
            CreateObject(obj);
        }

        [BoxGroup("ItemCreate/物品创建面板")]
        [HorizontalGroup("ItemCreate/物品创建面板/G6", 0.4f)]
        [LabelWidth(100)]
        [ShowInInspector, ValueDropdown(nameof(_chestDropDown)), LabelText("宝箱")]
        public GameObject chestGameObject;

        [BoxGroup("ItemCreate/物品创建面板")]
        [HorizontalGroup("ItemCreate/物品创建面板/G6", 0.3f)]
        [LabelWidth(100)]
        [ShowInInspector, ValueDropdown(nameof(_weaponDropDown))]
        [HideLabel]
        public string targetWeapon;

        [HorizontalGroup("ItemCreate/物品创建面板/G6/Right")]
        [HideLabel]
        public int chestOpenNumber = 1;

        [HorizontalGroup("ItemCreate/物品创建面板/G6/Right")]
        [Button("开启宝箱", ButtonSizes.Medium)]
        public void OpenChest() {
            var chest = Instantiate(chestGameObject,
                RGGameSceneManager.Inst.controller.transform.position + Vector3.down * .5f,
                Quaternion.identity,
                RGGameSceneManager.Inst.temp_objects_parent).GetComponent<RGChest>();

            if (targetWeapon == null) {
                Debug.LogWarning("未选择目标武器");
                return;
            }

            var isFind = false;
            // 添加一个字典来记录每种武器的出现次数
            Dictionary<string, int> weaponCount = new();
            int totalAttempts = 0;
            var finalLevel = MapManager.Instance.ChestLevel + chest.level;

            Timer.Register(0.1f, false, false, () => {
                for (int i = 0; i < chestOpenNumber; i++) {
                    totalAttempts++;
                    var weaponName = WeaponDropInfo.Instance.GetWeaponWithDropLevel(
                        finalLevel,
                        chest.randomGenerator,
                        null,
                        true
                    );

                    // 记录武器出现次数
                    if (!weaponCount.TryAdd(weaponName, 1)) {
                        weaponCount[weaponName]++;
                    }

                    if (weaponName == targetWeapon) {
                        Debug.Log("找到目标武器");
                        var rgWeapon = WeaponFactory.CreateWeapon(weaponName, emWeaponSource.LevelChest);
                        rgWeapon.name = weaponName;
                        rgWeapon.transform.SetParent(chest.transform, false);
                        isFind = true;
                        break;
                    }
                }

                if (!isFind) {
                    Debug.Log($"未找到目标武器 '{targetWeapon}'");
                    Debug.Log($"当前最终等级 (finalLevel): {finalLevel}");
                    Debug.Log($"总尝试次数: {totalAttempts}");
                    Debug.Log("武器出现统计:");

                    foreach (var kvp in weaponCount) {
                        float probability = (float)kvp.Value / totalAttempts * 100;
                        Debug.Log($"- {kvp.Key}: {kvp.Value} 次 ({probability:F2}%)");
                    }
                }
            });
        }

        [HorizontalGroup("ItemCreate/物品创建面板/G7")]
        [ShowInInspector, LabelText("可拾取物品")]
        [LabelWidth(100)]
        [ValueDropdown(nameof(PickableItemDropDownList))]
        public string itemNameWithChinese;

        [HorizontalGroup("ItemCreate/物品创建面板/G7/Right")]
        [HideLabel]
        public int pickableItemCount = 1;

        [HorizontalGroup("ItemCreate/物品创建面板/G7/Right"), Button("获取物品", ButtonSizes.Medium)]
        public void GetPickableItem() {
            if (!Application.isPlaying) {
                Debug.LogError("存档相关调试工具: 只能在游戏运行时使用");
                return;
            }

            var controller = RGGameSceneManager.Inst.controller;
            if (controller == null) {
                Debug.LogError("存档相关调试工具: 无法找到玩家");
                return;
            }

            var itemName = SoulKnight.Editor.Common.Utils.RemoveChineseName(itemNameWithChinese);
            var itemConfig = ItemConfigLoader.GetItemConfig(itemName);

            PickableInfo info = new() {
                count = pickableItemCount,
                name = itemName,
                itemType = itemConfig.ItemType,
            };

            ItemData.data.AddPickable(info, GetItemSource.Debug, false);
        }

        [HorizontalGroup("ItemCreate/物品创建面板/G7/Right"), Button("移除物品", ButtonSizes.Medium)]
        public void RemovePickableItem() {
            var itemName = SoulKnight.Editor.Common.Utils.RemoveChineseName(itemNameWithChinese);
            ItemData.data.materials.Remove(itemName);
        }

        [HorizontalGroup("ItemCreate/物品创建面板/G8")]
        [ShowInInspector, LabelText("饮料")]
        [LabelWidth(100)]
        [ValueDropdown(nameof(drinkDropDownList))]
        public GameObject drink;

        [HorizontalGroup("ItemCreate/物品创建面板/G8/Right")]
        [HideLabel]
        public int drinkItemCount = 1;

        [HorizontalGroup("ItemCreate/物品创建面板/G8/Right"), Button("生成", ButtonSizes.Medium)]
        public void InstantiateDrink() {
            if (!RGGameSceneManager.GetInstance() || !RGGameSceneManager.GetInstance().controller) {
                return;
            }

            if (drink == null) return;
            var controller = RGGameSceneManager.GetInstance().controller;
            if (global::GameUtil.IsSingleGame()) {
                for (int i = 0; i < drinkItemCount; i++) {
                    var drinkGo = Instantiate(drink, controller.transform.position + Vector3.down,
                        Quaternion.identity);
                    drinkGo.name = drink.name;
                }
            } else {
                MessageManager.SendSpawnGameObject(drink, controller.transform.position + Vector3.down);
            }
        }

        [HorizontalGroup("ItemCreate/物品创建面板/G9")]
        [ShowInInspector, LabelText("因子兑换卷")]
        [LabelWidth(100)]
        [ValueDropdown(nameof(battleFactorDropDown), FlattenTreeView = true)]
        private emBattleFactor _factor;

        [HorizontalGroup("ItemCreate/物品创建面板/G9/Right")]
        [HideLabel]
        public int factorItemCount = 1;

        [HorizontalGroup("ItemCreate/物品创建面板/G9/Right"), Button("获取", ButtonSizes.Medium)]
        public void GetFactorTokenTicket() {
            var tokenName = ItemTokenTicket.GetTokenFactorNameByFactor(_factor);
            var pickable = new PickableInfo(emItemType.TokenTicket, tokenName, factorItemCount);
            ItemData.data.AddPickable(pickable, GetItemSource.Debug, false);
        }

        [HorizontalGroup("ItemCreate/物品创建面板/G10", 0.7f)]
        [LabelWidth(100)]
        [ValueDropdown(nameof(_buffDropDownList)), LabelText("天赋球")]
        public emBuff buff;

        [HorizontalGroup("ItemCreate/物品创建面板/G10")]
        [Button("创建天赋球", ButtonStyle.FoldoutButton)]
        public void CreateBuffBall() {
            var controller = RGGameSceneManager.GetInstance().controller;
            var pos = controller.transform.position;
            var prefab =
                AssetDatabase.LoadAssetAtPath<GameObject>("Assets/RGPrefab/LevelObject/DrinkAndBook/ball_buff.prefab");

            if (global::GameUtil.IsSingleGame()) {
                var obj = Instantiate(prefab, pos, Quaternion.identity);
                obj.transform.position = RGGameSceneManager.Inst.controller.transform.position;

                var itemBuffBook = obj.GetComponent<ItemBuffBook>();
                itemBuffBook.setupOnStart = false;
                itemBuffBook.buff = buff;
                itemBuffBook.RefreshBuffIcon();
            } else {
                MessageManager.SendSpawnGameObject(prefab,
                    RGGameSceneManager.Inst.controller.transform.position);
            }
        }

        [HorizontalGroup("ItemCreate/物品创建面板/G11", 0.5f)]
        [LabelWidth(100)]
        [LabelText("球数量")]
        public int ballNumber = 1;

        [HorizontalGroup("ItemCreate/物品创建面板/G11", 0.25f)]
        [Button("创建生命球", ButtonStyle.FoldoutButton)]
        public void CreateHealthBall() {
            for (int i = 0; i < ballNumber; i++) {
                var obj = Instantiate(
                    ResourcesUtil.Load<GameObject>("RGPrefab/Register/health.prefab"));

                obj.transform.position =
                    RGGameSceneManager.Inst.controller.transform.position + GetPositionOnCircle2D();
            }
        }

        [HorizontalGroup("ItemCreate/物品创建面板/G11", 0.25f)]
        [Button("创建法力球", ButtonStyle.FoldoutButton)]
        public void CreateEnergyBall() {
            for (int i = 0; i < ballNumber; i++) {
                var obj = Instantiate(
                    ResourcesUtil.Load<GameObject>("RGPrefab/Register/energy.prefab"));

                obj.transform.position =
                    RGGameSceneManager.Inst.controller.transform.position + GetPositionOnCircle2D();
            }
        }

        private Vector3 GetPositionOnCircle2D(float radius = 5) {
            float angle = Random.Range(0, Mathf.PI * 2); // 随机角度
            return new Vector3(Mathf.Cos(angle) * radius, Mathf.Sin(angle) * radius, 0); // 固定在2D平面上的圆周上
        }
    }
}