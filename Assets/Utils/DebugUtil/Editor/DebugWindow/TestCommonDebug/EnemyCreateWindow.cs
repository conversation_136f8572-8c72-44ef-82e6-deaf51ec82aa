using Activities.RebornEgg.Scripts;
using I2.Loc;
using ModeDefence;
using RGTest.Editor.OdinWindow;
using Sirenix.OdinInspector;
using SoulKnight.Runtime.Config2Code.Config;
using System.IO;
using UnityEditor;
using UnityEngine;
// ReSharper disable Odin.OdinUnknownGroupingPath

namespace Utils.DebugUtil.Editor.DebugWindow.TestCommonDebug {
    public partial class TestCommonDebugWindow {
        private void InitEnemyAndNpcDropDownList() {
            _combinedDropDown = new ValueDropdownList<string>();

            foreach (var item in _enemyDropDown) {
                _combinedDropDown.Add(item);
            }

            foreach (var item in _npcDropDown) {
                _combinedDropDown.Add(item);
            }
        }

        [ShowIfGroup("EnemyCreate")]
        [BoxGroup("EnemyCreate/敌人NPC创建面板", centerLabel: true)]
        [HorizontalGroup("EnemyCreate/敌人NPC创建面板/G1", 100, LabelWidth = 80)]
        [HideLabel, ReadOnly, PreviewField(100, ObjectFieldAlignment.Left)]
        public GameObject enemyIcon;

        [PropertySpace(5)]
        [VerticalGroup("EnemyCreate/敌人NPC创建面板/G1/Right")]
        [HorizontalGroup("EnemyCreate/敌人NPC创建面板/G1/Right/0", 0.8f)]
        [ShowInInspector, ValueDropdown(nameof(_combinedDropDown), ExpandAllMenuItems = false)]
        [OnValueChanged(nameof(ChangeIcon))]
        [LabelText("敌人/NPC")] [LabelWidth(100)]
        public string enemyOrNpcPath;

        [HorizontalGroup("EnemyCreate/敌人NPC创建面板/G1/Right/0", 0.2f)] [HideLabel]
        [PropertySpace(5)]
        public int number = 1;

        [VerticalGroup("EnemyCreate/敌人NPC创建面板/G1/Right")] [ShowInInspector] [LabelText("基因")]
        [LabelWidth(100)]
        public DefenceEnemyGene.EnemyGene enemyGene;

        [PropertySpace]
        [VerticalGroup("EnemyCreate/敌人NPC创建面板/G1/Right")]
        [HorizontalGroup("EnemyCreate/敌人NPC创建面板/G1/Right/1", MarginLeft = 100)]
        [Button("创建怪物", ButtonSizes.Medium)]
        public void CreateEnemy() {
            if (!Application.isPlaying || string.IsNullOrEmpty(enemyOrNpcPath)) {
                return;
            }

            var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(enemyOrNpcPath);
            for (int i = 0; i < number; i++) {
                if (prefab.TryGetComponent(out RGEController _)) {
                    var enemy = TestUtil.CreateEnemy(prefab,
                        RGGameSceneManager.Inst.controller.transform.position);
                    if (enemyGene != DefenceEnemyGene.EnemyGene.none && enemyGene != DefenceEnemyGene.EnemyGene.count) {
                        DefenceEnemyGene.AddGene(enemyGene, enemy.GetComponent<RGEController>());
                    }
                } else {
                    var npc = Instantiate(prefab,
                        RGGameSceneManager.Inst.controller.transform.position + Vector3.up,
                        Quaternion.identity);
                    npc.name = prefab.name;
                }
            }
        }
        
        [PropertySpace]
        [HorizontalGroup("EnemyCreate/敌人NPC创建面板/G1/Right/1")]
        [Button("创建怪物蛋", ButtonSizes.Medium)]
        public void CreateEnemyEgg() {
            var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(enemyOrNpcPath);
            ActivityRebornEggManager.GenTargetEgg(prefab.name);
        }

        [PropertySpace]
        [HorizontalGroup("EnemyCreate/敌人NPC创建面板/G1/Right/1")]
        [Button("导出敌人数据", ButtonSizes.Medium)]
        public void ExportEnemyData() {
            string filePath = Path.Combine(Path.GetDirectoryName(Application.dataPath) ?? string.Empty,
                "Temp/EnemyData.csv");

            using (StreamWriter writer = new(filePath)) {
                writer.WriteLine("monster,id,Reward_rate,goldCoin,silverCoin,copperCoin,energy");

                foreach (var dropdownItem in _enemyDropDown) {
                    var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(dropdownItem.Value);
                    var rgeController = prefab.GetComponent<RGEController>();
                    var data =
                        $"{ScriptLocalization.GetCN(rgeController.enemy_id)},{rgeController.enemy_id}," +
                        $"{rgeController.reward_rate}";

                    for (int i = 0; i < 4; i++) {
                        if (i < rgeController.reward_value.Length) {
                            data += $",{rgeController.reward_value[i]}";
                        } else {
                            data += ",无";
                        }
                    }

                    writer.WriteLine(data);
                }
            }

            Application.OpenURL($"file://{filePath}");
        }
        
        [HorizontalGroup("EnemyCreate/敌人NPC创建面板/G1/Right/2", MarginLeft = 100)]
        [Button("必现武器收藏家", ButtonSizes.Medium)]
        public void ShowWeaponCollector() {
            ItemData.data.evolutionWeaponBossRatesIndex = 10;
            ItemData.Save();
        }

        [HorizontalGroup("EnemyCreate/敌人NPC创建面板/G1/Right/2")]
        [Button("重置武器收藏家概率", ButtonSizes.Medium)]
        public void ResetWeaponCollector() {
            ItemData.data.evolutionWeaponBossRatesIndex = 0;
            ItemData.Save();
        }
        
        [HorizontalGroup("EnemyCreate/敌人NPC创建面板/G1/Right/2")]
        [Button("怪物击杀添加100", ButtonSizes.Medium)]
        public void GetAllEnemyKillData() {
            foreach ((string enemyName, EnemyTable.EnemyData _) in EnemyTable.Data) {
                StatisticData.data.AddObtainTimes(enemyName, 100, false);
                StatisticData.data.AddEnemyKilledCount(emGameMode.Normal, enemyName, 100);
            }
            
            StatisticData.Save();
        }
        
        private void ChangeIcon() {
            var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(enemyOrNpcPath);
            enemyIcon = prefab;
        }
    }
}