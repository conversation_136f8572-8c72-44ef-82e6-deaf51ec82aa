<?xml version="1.0" encoding="utf-8"?>
<!-- This file was automatically generated by the Google Play Games plugin for Unity
     Do not edit. -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.google.example.games.mainlibproj"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk android:minSdkVersion="8" android:targetSdkVersion="16" />

    <application>

     <!-- Required for Nearby Connections API -->
        <meta-data android:name="com.google.android.gms.nearby.connection.SERVICE_ID"
            android:value="" />

        <!-- the space in these forces it to be interpreted as a string vs. int -->
        <meta-data android:name="com.google.android.gms.games.APP_ID"
            android:value="\ 98467207184" />
        <meta-data android:name="com.google.android.gms.games.unityVersion"
            android:value="\ 0.9.35" />
        <!-- <meta-data android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" /> -->

        <activity android:name="com.google.games.bridge.NativeBridgeActivity"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
    </application>
</manifest>

