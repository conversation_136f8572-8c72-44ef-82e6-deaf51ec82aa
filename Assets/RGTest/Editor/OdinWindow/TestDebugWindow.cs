using Sirenix.OdinInspector.Editor;
using Sirenix.Utilities;
using Sirenix.Utilities.Editor;
using UnityEditor;

namespace RGTest.Editor.OdinWindow {
    public class TestDebugWindow : OdinMenuEditorWindow {
        [MenuItem("SoulKnight/Test/TestOdinWindow %#t")]
        private static void OpenWindow() {
            var window = GetWindow<TestDebugWindow>() ;
            window.position = GUIHelper.GetEditorWindowRect().AlignCenter(800, 500);
            window.OnClose += () => {
                foreach (var t in window.MenuTree.MenuItems.Where(t => t.Value is DebugMenuItemBase))
                {
                    ((DebugMenuItemBase)t.Value).OnClose();
                }
            };
        }

        protected override OdinMenuTree BuildMenuTree() {
            var tree = new OdinMenuTree(true) {
                {"攻击自动测试相关", new AutoTesting()},
                {"骑士之路相关", new TaskTool()},
                {"活动任务相关", new ActivityTaskTool()},
                {"赏金系统相关", new RewardTool()},
                {"统计数据相关", new StatisticDataTool()},
                {"地图生成相关", new MapTestTool()},
                {"其它工具", new OtherTool()},
                {"自动工具", new AutoTestConfigTool()},
                {"盒子工具", new BoxTool()},
                {"地牢定位工具", new AisleTool()},
                {"深度拷贝工具", new DeepCopyTool()}
            };
            return tree;
        }
    }
}
