#if UNITY_EDITOR && !UNITY_SWITCH
using System.Collections;
using System.Collections.Generic;
using NUnit.Framework;
using UnityEditor;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.TestTools;

namespace PaPa.DataTest {
    public class IntegratedDataTester : GenericTest {
        public override string[] related_data_path_list {
            get {
                return new string[] { TestPathConf.data_path, TestPathConf.backup_path };
            }
        }

        public override string[] assetbundle_list {
            get {
                return new string[] { };
            }
        }

        public override string[] destroyed_gameobject_list {
            get {
                return new string[] { "singleton_group", "(singleton) AssetBundleLoader", "MockSecretKeyList" };
            }
        }

        /// <summary>
        /// 清除PlayerRefs中的信息后再加载测试场景，SDKManager的Awake中调用RGSaveManager的Init()函数
        /// Init()函数调用InitSave()函数初始化PlayerPrefs
        /// 检测PlayerRefs中的英雄解锁、英雄等级、英雄皮肤、宠物解锁与RGSaveManager中的一致性
        /// </summary>        
        [UnityTest, Description("加载场景后初始化存档的整合测试")]
        public IEnumerator InitTest() {
            Scene testScene = SceneManager.GetActiveScene();
            // 加载测试场景
            yield return TestUtils.LoadSceneAddictive(TestConf.data_related_scene);

            // 检测PlayerPrefs
            var char_list = RGSaveManager.Inst.char_list;
            var pet_list = RGSaveManager.Inst.pet_list;
            TestUtils.RGSaveManagerSave();
            // 测试PlayerPrefs
            PlayerPrefsValidation(char_list, pet_list);

            // 清除游戏场景
            SceneManager.SetActiveScene(testScene);
            yield return SceneManager.UnloadSceneAsync(TestConf.data_related_scene);
        }

        /// <summary>
        // 加载DataTest场景后修改PlayerPref中的角色、宠物相关信息,存档
        // unload场景，清除PlayerPref中角色、宠物相关信息
        // 重新加载DataTest场景，检测PlayerPref中角色、宠物相关信息
        /// </summary>
        [UnityTest, Description("保存读取存档的整合测试")]
        public IEnumerator SaveLoadDataTest() {
            Scene testScene = SceneManager.GetActiveScene();
            // 加载测试场景
            yield return TestUtils.LoadSceneAddictive(TestConf.data_related_scene);
            List<int> hero_skin_length_list = new List<int>();
            foreach (var character in RGSaveManager.Inst.char_list) {
                hero_skin_length_list.Add(character.skin_list.Length);
            }
            // 随机生成解锁信息
            UnlockInfo unlock_info = TestUtils.GetUnlockInfo(hero_skin_length_list, RGSaveManager.Inst.pet_list.Length);
            UnlockRGSaveManagerData(unlock_info);

            // 深拷贝角色、宠物信息作为验证
            var validate_char_list = TestUtils.DeepCopy(RGSaveManager.Inst.char_list);
            var validate_pet_list = TestUtils.DeepCopy(RGSaveManager.Inst.pet_list);
            // 保存到PlayerPrefs
            TestUtils.RGSaveManagerSave();
            RGSaveManager.Inst.SaveGameData();

            // unload场景
            SceneManager.SetActiveScene(testScene);
            TestUtils.DestroyGameObject("singleton_group", "(singleton) AssetBundleLoader");
            yield return SceneManager.UnloadSceneAsync(TestConf.data_related_scene);

            // 重新加载场景
            yield return TestUtils.LoadSceneAddictive(TestConf.data_related_scene);

            // 测试RGSaveManager实例中的角色信息
            RGDataValidation(validate_char_list, validate_pet_list);

            // 清除游戏场景
            SceneManager.SetActiveScene(testScene);
            yield return SceneManager.UnloadSceneAsync(TestConf.data_related_scene);
        }

        /// <summary>
        /// PlayerPrefs中的角色、宠物信息与传入列表验证
        /// </summary>
        private void PlayerPrefsValidation(RGCharacterInfo[] char_list, RGPetInfo[] pet_list) {
            for (int i = 0; i < char_list.Length; i++) {
                string error = " PlayerPrefs error at No." + i;
                Assert.AreEqual(char_list[i].level, PlayerSaveData.GetInt("c" + i + "_level", 0), "character level" + error);
                Assert.AreEqual(char_list[i].unlock, bool.Parse(PlayerSaveData.GetString("c" + i + "_unlock", "false")), "character lock" + error);
                for (int j = 0; j < char_list[i].skin_list.Length; j++) {
                    Assert.AreEqual(TestUtils.RegularSkin(char_list[i].skin_list[j]), TestUtils.RegularSkin(PlayerSaveData.GetInt("c" + i + "_skin" + j, -1)), "character skin" + error + " skin " + j);
                }
            }

            for (int i = 0; i < pet_list.Length; i++) {
                Assert.AreEqual(pet_list[i].unlock, bool.Parse(PlayerSaveData.GetString("p" + i + "_unlock", "false")), "PlayerPrefs pet unlock error at " + i);
            }
        }

        /// <summary>
        /// RGSaveManager中的角色、宠物信息与传入列表验证
        /// </summary>
        private void RGDataValidation(RGCharacterInfo[] char_list, RGPetInfo[] pet_list) {
            for (int i = 0; i < char_list.Length; i++) {
                string error = " RGData error at No." + i;
                Assert.AreEqual(char_list[i].level, RGSaveManager.Inst.char_list[i].level, "character level" + error);
                Assert.AreEqual(char_list[i].unlock, RGSaveManager.Inst.char_list[i].unlock, "character lock" + error);
                for (int j = 0; j < char_list[i].skin_list.Length; j++) {
                    Assert.AreEqual(TestUtils.RegularSkin(char_list[i].skin_list[j]), TestUtils.RegularSkin(RGSaveManager.Inst.char_list[i].skin_list[j]), "character skin" + error + " skin " + j);
                }
            }

            for (int i = 0; i < pet_list.Length; i++) {
                Assert.AreEqual(pet_list[i].unlock, RGSaveManager.Inst.pet_list[i].unlock, "RGData pet unlock error at " + i);
            }
        }

        /// <summary>
        /// 解锁RGSaveManager中的信息
        /// </summary>
        private void UnlockRGSaveManagerData(UnlockInfo unlock_info) {
            foreach (var hero_info in unlock_info.unlock_hero_list) {
                RGSaveManager.Inst.char_list[hero_info.hero_index].unlock = true;
                RGSaveManager.Inst.char_list[hero_info.hero_index].level = hero_info.level;
                foreach (var unlock_skin in hero_info.hero_skin) {
                    RGSaveManager.Inst.char_list[hero_info.hero_index].skin_list[unlock_skin] = 1;
                }
            }
            foreach (var pet_index in unlock_info.unlock_pet_index_list) {
                RGSaveManager.Inst.pet_list[pet_index].unlock = true;
            }
        }
    }
}
#endif