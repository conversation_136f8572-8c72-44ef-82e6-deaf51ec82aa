using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEditor.Animations;
using UnityEngine;
using Object = UnityEngine.Object;

# if UNITY_EDITOR
public class HeroAnimGenerator {
    emHero hero;
    protected int skinIndex;
    bool overrideOld;
    protected string heroLower => hero.ToString().ToLower();
    protected HeroAnimGenConfig.AnimConfig config;
    internal Sprite deadTapSprite;
    protected static readonly float frameTime = 1f / 16;
    protected static readonly float pixelOffset = 1f / 16;

    protected string directory => $"Assets/RGAnim/hero/{heroLower}";
    string texturePath => $"Assets/RGTexture/character/hero/{heroLower}/{heroLower}_{skinIndex}.png";

    string controllerOldFileName => $"Assets/RGAnim/player_anim/char_{hero.ToInt()}_skin_{skinIndex}.controller";
    string controllerFileName => $"Assets/RGAnim/player_anim/new/char_{hero.ToInt()}_skin_{skinIndex}.controller";
    string idleFileName => $"{directory}/skin_{skinIndex}_idle.anim";
    string runFileName => $"{directory}/skin_{skinIndex}_run.anim";
    string deadFileName => $"{directory}/skin_{skinIndex}_dead.anim";

    public void GenerateAnimation(emHero hero, int skinIndex, HeroAnimGenConfig.AnimConfig config, bool overrideOld) {
        this.hero = hero;
        this.config = config;
        this.skinIndex = skinIndex;
        this.overrideOld = overrideOld;
        if (TexturePathValid(texturePath) && NeedGenerate(overrideOld)) {
            DoSlideTexture();
            EditorApplication.delayCall += DoGenerateAnimation;
        }
    }

    protected virtual void DoSlideTexture() {
        if (!Directory.Exists(directory)) {
            Directory.CreateDirectory(directory);
        }

        //预处理图片
        Texture2D texture = AssetDatabase.LoadAssetAtPath<Texture2D>(texturePath);
        SlideTexture(texture);
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }

    protected virtual void DoGenerateAnimation() {
        //读取Sprite信息
        // try {
        //     Object[] objects = AssetDatabase.LoadAllAssetsAtPath(texturePath);
        //     Sprite[] sprites = new Sprite[objects.Length - 1];
        //     for (int i = 0; i < sprites.Length; i++) {
        //         sprites[i] = (Sprite)objects[i + 1];
        //     }
        //
        //     //生成动画
        //     GenerateAnimation(sprites);
        //     //生成过后刷新icon，用于标识。
        //     config.newIcon = sprites[0];
        //     
        //     AssetDatabase.SaveAssets();
        // } catch (System.Exception e) {
        //     var texture = AssetDatabase.LoadAssetAtPath<Texture>(texturePath);
        //     Debug.LogError($"Generate error:{hero}_{skinIndex}\n{e}", texture);
        // }

        Object[] objects = AssetDatabase.LoadAllAssetsAtPath(texturePath);
        Sprite[] sprites = new Sprite[objects.Length - 1];
        for (int i = 0; i < sprites.Length; i++) {
            sprites[i] = (Sprite)objects[i + 1];
        }

        //生成动画
        GenerateAnimation(sprites);
        //生成过后刷新icon，用于标识。
        if (config.newIcons == null) {
            config.newIcons = new Dictionary<int, Sprite>();
        }

        if (!config.newIcons.ContainsKey(skinIndex)) {
            config.newIcons.Add(skinIndex, sprites[0]);
        } else {
            config.newIcons[skinIndex] = sprites[0];
        }

        AssetDatabase.SaveAssets();

        AssetDatabase.Refresh();
    }

    protected Sprite[] GetSlicedSprites(string path) {
        if (!File.Exists(path)) {
            Debug.LogError($"[{GetType().Name}] 找不到对应路径的文件：{path}");
            return Array.Empty<Sprite>();
        }
        var objs = AssetDatabase.LoadAllAssetRepresentationsAtPath(path);
        List<Sprite> sprites = new List<Sprite>();
        foreach (var obj in objs) {
            sprites.Add((Sprite)obj);
        }

        return sprites.ToArray();
    }

    /// <summary>
    /// 先尝试从新路径获取动画，如果获取不到，就在旧路径获取，并Copy至新路径
    /// </summary>
    /// <param name="oldPath"></param>
    /// <param name="newPath"></param>
    /// <returns></returns>
    protected AnimationClip GetMotion(string oldPath, string newPath) {
        var motion = AssetDatabase.LoadAssetAtPath<AnimationClip>(newPath);
        if (motion == null) {
            AssetDatabase.CopyAsset(oldPath, newPath);
        }

        return AssetDatabase.LoadAssetAtPath<AnimationClip>(newPath);
    }

    protected AnimatorController animator;

    protected virtual void GenerateAnimation(Sprite[] sprites) {
        CreateAnimator(sprites);

        GenerateCommonBaseLayer();

        GenerateHitLayer();
    }

    protected void CreateAnimator(Sprite[] sprites) {
        //生成动画
        var idleClip = GenAnimIdle(sprites);
        var runClip = GenAnimRun(sprites);
        var deadClip = GenAnimDead(sprites);

        if (config.needOverride) {
            animator = AnimatorController.CreateAnimatorControllerAtPath(controllerOldFileName);
            AssetImporter ctrlImporter = AssetImporter.GetAtPath(controllerOldFileName);
            ctrlImporter.assetBundleName = "common";
        } else {
            animator = AnimatorController.CreateAnimatorControllerAtPath(controllerFileName);
            AssetImporter ctrlImporter = AssetImporter.GetAtPath(controllerFileName);
        }
        
        animator.AddParameter("run", AnimatorControllerParameterType.Bool);
        animator.AddParameter("hit", AnimatorControllerParameterType.Trigger);
        animator.AddParameter("dead", AnimatorControllerParameterType.Trigger);
        animator.AddParameter("reborn", AnimatorControllerParameterType.Trigger);

        AssetDatabase.CreateAsset(idleClip, idleFileName);
        AssetDatabase.CreateAsset(runClip, runFileName);
        if (deadClip != null) {
            AssetDatabase.CreateAsset(deadClip, deadFileName);
        }

        AssetDatabase.SaveAssets();
    }

    /// <summary>
    /// 一般通用的普通层，像狼人这种比较特殊的结构除外。
    /// </summary>
    protected void GenerateCommonBaseLayer() {
        var stateMachine = animator.layers[0].stateMachine;

        AnimatorState idleState = stateMachine.AddState("ide", new Vector3(300, 100 + 15));
        idleState.motion = AssetDatabase.LoadAssetAtPath<AnimationClip>(idleFileName);
        AnimatorState runState = stateMachine.AddState("run", new Vector3(300, 200 + 15));
        runState.motion = AssetDatabase.LoadAssetAtPath<AnimationClip>(runFileName);
        AnimatorState deadState = stateMachine.AddState("dead", new Vector3(300, 0 + 15));
        deadState.motion = AssetDatabase.LoadAssetAtPath<AnimationClip>(deadFileName);
        stateMachine.exitPosition = new Vector3(50, 200 + 15);

        var deadTransition = stateMachine.AddAnyStateTransition(deadState);
        deadTransition.AddCondition(AnimatorConditionMode.If, 1, "dead");
        deadTransition.hasExitTime = false;
        deadTransition.duration = 0;
        var rebornTransition = deadState.AddTransition(idleState);
        rebornTransition.AddCondition(AnimatorConditionMode.If, 1, "reborn");
        rebornTransition.hasExitTime = false;
        rebornTransition.duration = 0;
        var idle2RunTransition = idleState.AddTransition(runState);
        idle2RunTransition.AddCondition(AnimatorConditionMode.If, 1, "run");
        idle2RunTransition.hasExitTime = false;
        idle2RunTransition.duration = 0;
        var run2IdleTransition = runState.AddTransition(idleState);
        run2IdleTransition.AddCondition(AnimatorConditionMode.IfNot, 1, "run");
        run2IdleTransition.hasExitTime = false;
        run2IdleTransition.duration = 0;
    }

    protected void GenerateHitLayer() {
        //HitLayer
        AddLayerWithDefaultWeight("HitLayer", 1f);

        var hitLayerStateMachine = animator.layers.ToList().Find(l => l.name == "HitLayer").stateMachine;
        AnimatorState hitState = hitLayerStateMachine.AddState("char_hit", new Vector3(300, 100 + 15));
        AnimatorState emptyState = hitLayerStateMachine.AddState("New State", new Vector3(300, 200 + 15));
        hitLayerStateMachine.defaultState = emptyState;
        hitState.motion = AssetDatabase.LoadAssetAtPath<AnimationClip>("Assets/RGAnim/enemy/char_hit.anim");

        var anyStateToHitState = hitLayerStateMachine.AddAnyStateTransition(hitState);
        anyStateToHitState.AddCondition(AnimatorConditionMode.If, 1, "hit");
        anyStateToHitState.exitTime = 0.9f;
        anyStateToHitState.hasExitTime = false;
        anyStateToHitState.hasFixedDuration = true;
        anyStateToHitState.duration = 0.1f;
        anyStateToHitState.offset = 0;
        anyStateToHitState.interruptionSource = TransitionInterruptionSource.None;
        anyStateToHitState.canTransitionToSelf = true;

        var hitStateToEmptyState = hitState.AddTransition(emptyState);
        hitStateToEmptyState.exitTime = 0.9f;
        hitStateToEmptyState.hasExitTime = true;
        hitStateToEmptyState.hasFixedDuration = true;
        hitStateToEmptyState.duration = 0.1f;
        hitStateToEmptyState.offset = 0;
        hitStateToEmptyState.interruptionSource = TransitionInterruptionSource.None;
        hitStateToEmptyState.orderedInterruption = true;
    }

    protected void AddLayerWithDefaultWeight(string name, float defaultWeight) {
        var layer = new AnimatorControllerLayer {
            name = name,
            defaultWeight = defaultWeight,
            stateMachine = new AnimatorStateMachine(),
        };

        layer.stateMachine.name = layer.name;
        layer.stateMachine.hideFlags = HideFlags.HideInHierarchy;
        if (AssetDatabase.GetAssetPath(animator) != "")
            AssetDatabase.AddObjectToAsset((UnityEngine.Object)layer.stateMachine,
                AssetDatabase.GetAssetPath(animator));

        animator.AddLayer(layer);
    }

    void SlideTexture(Texture2D texture) {
        //设置基本属性
        var importer = AssetImporter.GetAtPath(texturePath) as TextureImporter;
        importer.textureType = TextureImporterType.Sprite;
        importer.spriteImportMode = SpriteImportMode.Multiple;
        importer.mipmapEnabled = false;
        importer.filterMode = FilterMode.Point;
        importer.textureCompression = TextureImporterCompression.Uncompressed;
        importer.spritePixelsPerUnit = 16;

        var textureSettings = new TextureImporterSettings();
        importer.ReadTextureSettings(textureSettings);
        textureSettings.spriteMeshType = SpriteMeshType.Tight;
        textureSettings.spriteExtrude = 1;
        importer.spritePackingTag = "hero";

        importer.SetTextureSettings(textureSettings);
        //切图
        var slideSize = config.size;
        Rect[] rects =
            UnityEditorInternal.InternalSpriteUtility.GenerateGridSpriteRectangles(texture, Vector2.zero, slideSize,
                Vector2.zero);
        List<SpriteMetaData> spriteMetas = new List<SpriteMetaData>();
        for (int i = 0; i < rects.Length; i++) {
            SpriteMetaData metaData = new SpriteMetaData();
            metaData.rect = rects[i];
            metaData.name = texture.name + "_" + i;
            metaData.alignment = (int)SpriteAlignment.Custom;
            metaData.pivot = config.pivot;
            spriteMetas.Add(metaData);
        }

        importer.spritesheet = spriteMetas.ToArray();
        AssetDatabase.ImportAsset(texturePath);
    }

    /// <summary>
    /// 生成站立动画
    /// </summary>
    protected virtual AnimationClip GenAnimIdle(Sprite[] sprites) {
        AnimationClip clip = new AnimationClip() {frameRate = 16};
        ObjectReferenceKeyframe[] keys = new ObjectReferenceKeyframe[8];
        for (int i = 0; i < 8; i++) {
            keys[i] = new ObjectReferenceKeyframe() {time = frameTime * i, value = sprites[i]};
        }

        EditorCurveBinding binding = new EditorCurveBinding() {
            path = "img/body",
            propertyName = "m_Sprite",
            type = typeof(SpriteRenderer)
        };
        AnimationUtility.SetObjectReferenceCurve(clip, binding, keys);
        var clipSetting = AnimationUtility.GetAnimationClipSettings(clip);
        clipSetting.loopTime = true;
        AnimationUtility.SetAnimationClipSettings(clip, clipSetting);
        AssetDatabase.ImportAsset(texturePath);
        return clip;
    }

    /// <summary>
    /// 生成奔跑动画
    /// </summary>
    protected virtual AnimationClip GenAnimRun(Sprite[] sprites) {
        AnimationClip clip = new AnimationClip() {frameRate = 16};
        ObjectReferenceKeyframe[] keys = new ObjectReferenceKeyframe[8];
        int spriteOffset = 8;
        for (int i = 0; i < 8; i++) {
            keys[i] = new ObjectReferenceKeyframe() {time = frameTime * i, value = sprites[i + spriteOffset]};
        }

        EditorCurveBinding binding = new EditorCurveBinding() {
            path = "img/body",
            propertyName = "m_Sprite",
            type = typeof(SpriteRenderer)
        };
        AnimationUtility.SetObjectReferenceCurve(clip, binding, keys);

        //增加奔跑位移
        float[] offsets = new float[] {config.runAnimOffset.x, config.runAnimOffset.y};
        string[] propertyNames = new string[] {"m_LocalPosition.x", "m_LocalPosition.y"};
        for (int i = 0; i < 2; i++) {
            AnimationCurve curve = new AnimationCurve();
            List<int> upFrame = new List<int> {0, 6};
            List<int> downFrame = new List<int> {1, 5};
            for (int j = 0; j < 8; j++) {
                if (upFrame.Contains(j)) {
                    curve.AddKey(frameTime * j, offsets[i]);
                }

                if (downFrame.Contains(j)) {
                    curve.AddKey(frameTime * j, 0);
                }
            }

            binding = new EditorCurveBinding() {
                path = "img",
                propertyName = propertyNames[i],
                type = typeof(Transform)
            };
            AnimationUtility.SetEditorCurve(clip, binding, curve);
        }

        var clipSetting = AnimationUtility.GetAnimationClipSettings(clip);
        clipSetting.loopTime = true;
        AnimationUtility.SetAnimationClipSettings(clip, clipSetting);
        AssetDatabase.ImportAsset(texturePath);
        return clip;
    }

    /// <summary>
    /// 生成死亡动画
    /// </summary>
    protected virtual AnimationClip GenAnimDead(Sprite[] sprites) {
        if (sprites.Length < 17) {
            Debug.Log($"图集：{hero}_{skinIndex} 为非常规图集，请自行处理死亡动画！");
            return null;
        }

        AnimationClip clip = new AnimationClip() {frameRate = 16, wrapMode = WrapMode.Default};
        //Img
        AnimationCurve imgPositionCurve = new AnimationCurve();
        int[] frames = new int[] {0, 4, 10, 20, 32};
        float[] imgPositions = new float[] {0, 0.6f, -0.2f, -0.2f};
        for (int i = 0; i < imgPositions.Length; i++) {
            imgPositionCurve.AddKey(frameTime * frames[i], imgPositions[i]);
        }

        EditorCurveBinding binding = new EditorCurveBinding()
            {path = "img", propertyName = "m_LocalPosition.y", type = typeof(Transform)};
        AnimationUtility.SetEditorCurve(clip, binding, imgPositionCurve);
        //Body
        ObjectReferenceKeyframe[] bodyKeys = new ObjectReferenceKeyframe[] {
            new ObjectReferenceKeyframe() {time = frameTime * 0, value = sprites[16]}
        };
        binding = new EditorCurveBinding()
            {path = "img/body", propertyName = "m_Sprite", type = typeof(SpriteRenderer)};
        AnimationUtility.SetObjectReferenceCurve(clip, binding, bodyKeys);

        AnimationCurve bodyColorCurve = new AnimationCurve();
        float[] bodyColors = new float[] {1, 0.73529f, 0.47059f};
        string[] colorPropertyNames = new string[] {"m_Color.r", "m_Color.g", "m_Color.b"};
        foreach (var colorPropertyName in colorPropertyNames) {
            for (int i = 0; i < bodyColors.Length; i++) {
                bodyColorCurve.AddKey(frameTime * frames[i], bodyColors[i]);
            }

            binding = new EditorCurveBinding()
                {path = "img/body", propertyName = colorPropertyName, type = typeof(SpriteRenderer)};
            AnimationUtility.SetEditorCurve(clip, binding, bodyColorCurve);
        }

        //Dead Tap
        ObjectReferenceKeyframe[] deadTapSpriteKeys = new ObjectReferenceKeyframe[] {
            new ObjectReferenceKeyframe() {time = frameTime * 0, value = deadTapSprite}
        };
        binding = new EditorCurveBinding()
            {path = "dead_tap", propertyName = "m_Sprite", type = typeof(SpriteRenderer)};
        AnimationUtility.SetObjectReferenceCurve(clip, binding, deadTapSpriteKeys);

        AnimationCurve deadTapEnable = new AnimationCurve();
        deadTapEnable.AddKey(frameTime * 10, 0);
        deadTapEnable.AddKey(frameTime * 32, 1);
        binding = new EditorCurveBinding()
            {path = "dead_tap", propertyName = "m_Enabled", type = typeof(SpriteRenderer)};
        AnimationUtility.SetEditorCurve(clip, binding, deadTapEnable);

        string[] posittionNames = new string[] {"m_LocalPosition.x", "m_LocalPosition.y"};
        foreach (var propertyName in posittionNames) {
            AnimationCurve deadTapPosition = new AnimationCurve() {
                keys = new Keyframe[] {new Keyframe(0, 0.8f)}
            };
            binding = new EditorCurveBinding()
                {path = "dead_tap", propertyName = propertyName, type = typeof(Transform)};
            AnimationUtility.SetEditorCurve(clip, binding, deadTapPosition);
        }

        //h1
        var h1Curve = new AnimationCurve();
        h1Curve.AddKey(0, 0);
        binding = new EditorCurveBinding() {
            path = "img/h1",
            propertyName = "m_IsActive",
            type = typeof(GameObject)
        };
        AnimationUtility.SetEditorCurve(clip, binding, h1Curve);

        //Shadow
        AnimationCurve shadowCurve = new AnimationCurve();
        int[] shadowFrames = new int[] {0, 4, 10, 32};
        float[] shadowScale = new float[] {1, 0.5f, 1, 1};
        for (int i = 0; i < shadowFrames.Length; i++) {
            shadowCurve.AddKey(frameTime * shadowFrames[i], shadowScale[i]);
        }

        binding = new EditorCurveBinding() {path = "shadow", propertyName = "m_LocalScale.x", type = typeof(Transform)};
        AnimationUtility.SetEditorCurve(clip, binding, shadowCurve);

        return clip;
    }

    bool TexturePathValid(string path) {
        if (string.IsNullOrEmpty(path)) {
            Debug.LogError($"[HeroAnimGenerator] path is null or empty: {path}");
            return false;
        }

        if (!File.Exists(path)) {
            Debug.LogError($"[HeroAnimGenerator] file not exist: {path}");
            return false;
        }

        if (!path.EndsWith(".png")) {
            Debug.LogError($"[HeroAnimGenerator] file is not png: {path}");
            return false;
        }

        return true;
    }

    bool NeedGenerate(bool overrideOld) {
        return overrideOld || !File.Exists(controllerFileName);
    }
#endif
}