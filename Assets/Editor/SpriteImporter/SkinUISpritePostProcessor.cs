using System.Collections.Generic;
using UnityEditor;

namespace SpriteImporter {
    public class SkinUISpritePostProcessor : SpritePostProcessorGeneric {
        public override void OnInit() {
            includeSet = new HashSet<string> {
                "Assets/RGTexture/ui/skin/"
            };
        }

        public override void OnPreprocessTexture(AssetPostProcessContext context) {
            var textureImporter = context.AssetImporter as TextureImporter;
            SpriteImportMode mode = SpriteImportMode.Single;
            if (null != textureImporter) {
                mode = textureImporter.spriteImportMode;
            }

            ProcessGeneralTexture(context, mode);
        }

        protected override void SetSpritePixelsPerUnit(TextureImporter textureImporter) {
            if (textureImporter.spritePixelsPerUnit == 16) {
                textureImporter.spritePixelsPerUnit = GetSpritePixelsPerUnit();
            }
        }

        public override int GetSpritePixelsPerUnit() {
            return 32;
        }
    }
}