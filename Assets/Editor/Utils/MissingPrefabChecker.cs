using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

public class MissingPrefabChecker {
    [MenuItem("SoulKnight/Assets Tool/Check Missing Prefabs in All Prefabs")]
    public static void CheckMissingPrefabsInAllPrefabs() {
        // 获取所有 Prefab 的 GUID
        string[] prefabGuids = AssetDatabase.FindAssets("t:Prefab");
        int totalMissingCount = 0;

        foreach (string guid in prefabGuids) {
            // 通过 GUID 获取 Prefab 的路径
            string prefabPath = AssetDatabase.GUIDToAssetPath(guid);
            // 加载 Prefab
            GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);

            if (prefab == null) {
                continue;
            }

            // 检查该 Prefab 中丢失的 Prefab
            List<Transform> missingPrefabs = new List<Transform>();
            CheckForMissingPrefabs(prefab.transform, ref missingPrefabs);

            if (missingPrefabs.Count <= 0) {
                continue;
            }

            totalMissingCount += missingPrefabs.Count;
            Debug.LogError($"在 Prefab {prefabPath} 中发现 {missingPrefabs.Count} 个丢失的 Prefab：");
            foreach (Transform missingPrefab in missingPrefabs) {
                Debug.LogError($"丢失的 Prefab 路径：{Path.Combine(prefabPath, GetTransformPath(missingPrefab))}");
            }
        }

        if (totalMissingCount > 0) {
            Debug.LogError($"总共发现 {totalMissingCount} 个丢失的 Prefab。");
        } else {
            Debug.Log("在所有 Prefab 中未发现丢失的 Prefab。");
        }
    }

    private static void CheckForMissingPrefabs(Transform parent, ref List<Transform> missingPrefabs) {
        for (int i = 0; i < parent.childCount; i++) {
            Transform child = parent.GetChild(i);
            if (child.gameObject.name.Contains("Missing Prefab")) {
                missingPrefabs.Add(child);
            } else {
                // 递归检查子物体
                CheckForMissingPrefabs(child, ref missingPrefabs);
            }
        }
    }

    private static string GetTransformPath(Transform transform) {
        string path = transform.name;
        while (transform.parent != null) {
            transform = transform.parent;
            path = transform.name + "/" + path;
        }

        return path;
    }
}