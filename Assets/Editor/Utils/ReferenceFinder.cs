using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;

// ReSharper disable once CheckNamespace
public class ReferenceFinder : EditorWindow {
    private Vector2 _scrollPosition = Vector2.zero;
    private readonly List<Object> _references = new();
    private List<string> _paths;
    private bool _showPath;

    // Used to queue a call to FindObjectReferences() to avoid doing it mid-layout
    private Object _findReferencesAfterLayout;

    [MenuItem("Assets/Find References", false, 39)]
    private static void FindObjectReferences() {
        var window = GetWindow<ReferenceFinder>(true, "Find References", true);
        window.FindMultiObjectReferences(Selection.objects);
    }
    
    public static void FindReferences(string guid) {
        var window = GetWindow<ReferenceFinder>(true, "Find References", true);
        window.FindGuidReferences(guid);
    }

    private void OnGUI() {
        GUILayout.Space(5);
        _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition);

        GUILayout.BeginHorizontal();
        GUILayout.Label("Found: " + _references.Count);
        if (GUILayout.Button("Clear", EditorStyles.miniButton)) {
            _references.Clear();
        }

        GUILayout.EndHorizontal();

        GUILayout.Space(5);

        for (var i = _references.Count - 1; i >= 0; --i) {
            LayoutItem(_references[i]);
        }

        EditorGUILayout.EndScrollView();
        
        if (GUILayout.Button("Sort By Path")){
            SortByPath();
        }

        if (_findReferencesAfterLayout == null) {
            return;
        }

        FindObjectReferences(_findReferencesAfterLayout);

        _findReferencesAfterLayout = null;
    }
    
    private void SortByPath() {
        _showPath = !_showPath;
        SortListByAssetPath(_references);
    }

    private static void SortListByAssetPath(List<Object> references) {
        references.Sort((objA, objB) => {
            string pathA = AssetDatabase.GetAssetPath(objA);
            string pathB = AssetDatabase.GetAssetPath(objB);

            return string.IsNullOrEmpty(pathA) switch {
                false when !string.IsNullOrEmpty(pathB) => String.Compare(pathA, pathB, StringComparison.Ordinal),
                false => -1,
                _ => !string.IsNullOrEmpty(pathB) ? 1 : 0
            };
        });
    }

    private void LayoutItem(Object obj) {
        var style = EditorStyles.miniButtonLeft;
        style.alignment = TextAnchor.MiddleLeft;

        if (obj == null) {
            return;
        }

        GUILayout.BeginHorizontal();

        string showName = obj.name;

        if (_showPath) {
            showName = $"{obj.name}\t{AssetDatabase.GetAssetPath(obj)}";
        }

        if (GUILayout.Button(showName, style)) {
            Selection.activeObject = obj;
            EditorGUIUtility.PingObject(obj);
        }

        // Use "right arrow" unicode character 
        if (GUILayout.Button("\u25B6", EditorStyles.miniButtonRight, GUILayout.MaxWidth(20))) {
            _findReferencesAfterLayout = obj;
        }

        GUILayout.EndHorizontal();
    }
    
    private void FindObjectReferences(Object toFind) {
        _references.Clear();
        
        EditorUtility.DisplayProgressBar("Searching", "Generating file paths", 0.0f);

        ReferenceUtil.FindObjectReferencesInFiles(
            toFind,
            ReferenceUtil.TargetExtensionName,
            o => {
                _references.Add(o);
            });

        EditorUtility.ClearProgressBar();
    }
    
    private void FindMultiObjectReferences(Object[] objects) {
        _references.Clear();
        EditorUtility.DisplayProgressBar("Searching", "Generating file paths", 0.0f);

        HashSet<Object> refHashSet = new();
        int len = objects.Length;
        for (int i = 0; i < len; i++) {
            ReferenceUtil.FindObjectReferencesInFiles(
                objects[i],
                ReferenceUtil.TargetExtensionName,
                o => {
                    refHashSet.Add(o);
                });
        }
        _references.AddRange(refHashSet);

        EditorUtility.ClearProgressBar();
    }
    
    private void FindGuidReferences(string guid) {
        _references.Clear();

        EditorUtility.DisplayProgressBar("Searching", "Generating file paths", 0.0f);

        ReferenceUtil.FindGuidReferences(
            guid,
            ReferenceUtil.TargetExtensionName,
            o => {
                _references.Add(o);
            });

        EditorUtility.ClearProgressBar();
    }

    /// Finds references to passed objects and puts them in m_references
    private void FindObjectReferencesBuiltIn(Object toFind) {
        EditorUtility.DisplayProgressBar("Searching", "Generating file paths", 0.0f);
        //
        // Get all prefabs in the project
        //
        if (_paths == null) {
            _paths = new List<string>();
            GetFilePaths("Assets", ".prefab", ref _paths);
        }

        float progressBarPos = 0;
        var numPaths = _paths.Count;
        var hundredthIteration =
            Mathf.Max(1, numPaths / 100); // So we only update progress bar 100 times, not for every item

        var tmpArray = new Object[1];
        _references.Clear();

        //
        // Loop through all files, and add any that have the selected object in it's list of dependencies
        //
        for (var i = 0; i < numPaths; ++i) {
            tmpArray[0] = AssetDatabase.LoadMainAssetAtPath(_paths[i]);
            if (tmpArray.Length > 0 && tmpArray[0] != toFind) {
                var dependencies = EditorUtility.CollectDependencies(tmpArray);
                if (Array.Exists(dependencies, item => item == toFind)) {
                    // Don't add if another of the dependencies is already in there
                    _references.Add(tmpArray[0] as GameObject);
                }
            }

            if (i % hundredthIteration != 0) {
                continue;
            }

            progressBarPos += 0.01f;
            EditorUtility.DisplayProgressBar("Searching", "Searching dependencies", progressBarPos);
        }

        EditorUtility.DisplayProgressBar("Searching", "Removing redundant references", 1);

        //
        // Go through the references, get dependencies of each and remove any that have another dependency
        // on the match list. We only want direct dependencies.
        //
        for (var i = _references.Count - 1; i >= 0; i--) {
            tmpArray[0] = _references[i];
            var dependencies = EditorUtility.CollectDependencies(tmpArray);

            var shouldRemove = false;

            for (var j = 0; j < dependencies.Length && shouldRemove == false; ++j) {
                var dependency = dependencies[j];
                shouldRemove =
                    _references.Find(item => item == dependency && item != tmpArray[0]) != null;
            }

            if (shouldRemove)
                _references.RemoveAt(i);
        }

        EditorUtility.ClearProgressBar();
    }

    /// Recursively find all file paths with particular extension in a directory
    private static void GetFilePaths(string startingDirectory, string extension, ref List<string> paths) {
        try {
            // Add any file paths with the correct extension
            var files = Directory.GetFiles(startingDirectory);
            paths.AddRange(files.Where(file => file.EndsWith(extension)));

            // Recurse for all directories
            var directories = Directory.GetDirectories(startingDirectory);
            foreach (var t in directories) {
                GetFilePaths(t, extension, ref paths);
            }
        } catch (Exception e) {
            Debug.LogError(e.Message);
        }
    }
}