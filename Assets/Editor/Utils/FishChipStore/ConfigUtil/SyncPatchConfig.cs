using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Abo;
using MiniExcelLibs;
using NewDynamicConfig;
using RGScript.Data;
using Sirenix.OdinInspector;
using System.Text;
using UnityEditor;
using UnityEngine;

namespace FishChipStore.Util.ConfigUtil {
    public class SyncPatchConfig {
        public string SubTitle {
            get {
                return string.Join(", ", ChanelNames);
            }
        }

        private Dictionary<string, ConfigModel> ConfigModels { get; }
        private EditorWindow ParentWindow { get; }
        public string[] ConfigPaths { get; }
        private string ParentPath { get; }
        public string[] ChanelNames { get; }
        private List<FishChipConfigTable> _fishChipConfigTables;
        private bool HasLoadConfig => _storeConfig != null;
        private FishChipStoreConfig _storeConfig;
        private string _prevStoreConfigJson;
        public const string ConfigFileName = "FishChipStoreConfig.json";
        private string _lubanConfigPath;

        public SyncPatchConfig(string[] paths, string parentPath, Dictionary<string, ConfigModel> configModels,
            EditorWindow parentWindow) {
            ConfigPaths = paths;
            ConfigModels = configModels;
            ParentWindow = parentWindow;
            ParentPath = parentPath;
            var chanelNames = from p in paths
                let c = p.Remove(0, parentPath.Length + 1)
                select c;
            ChanelNames = chanelNames.ToArray();
        }

        public void SetLubanConfigPath(string path) {
            _lubanConfigPath = path;
        }
        private static void ExportStoreTimeConfig2LubanFormat(List<StoreConfig> storeConfigs) {
            var storeConfigTable = DataMgr.ConfigData.Tables.TbFishAndSeasonStoreConfig;
            StringBuilder sb = new StringBuilder();
            sb.Append($"##var,ConfigID,StoreTypeID,GoodID,StartTime,EndTime,CustomContents,GoodValue,SpecialMatchChannel\r\n");
            foreach (var config in storeConfigs) {
                foreach (var item in config.storeItems) {
                    var itemId = item.id;
                    if (item.id.StartsWith(Const.ItemSpecificMaterialIdPrefix)) {
                        itemId = Const.ItemSpecificMaterialIdPrefix;
                    }
        
                    if (item.id.StartsWith(Const.ItemSpecificBlueprintPrefix)) {
                        itemId = Const.ItemSpecificBlueprintPrefix;
                    }
        
                    var storeConfig = storeConfigTable.DataList.FirstOrDefault(_ => _.Good == itemId && _.StoreTypeID == 100);
                    if (storeConfig != null) {
                        sb.Append(
                            $",{storeConfig.ConfigID},{storeConfig.StoreTypeID},{item.index},{config.startTimeStamp},{config.expireTimestamp},{string.Join(";", item.customContents)},{item.price},,\r\n");
                    } else {
                        Debug.LogError($"没有找到对应的商店配置，id:{item.id}");
                    }
                }
            }
        
            File.Delete($"{Application.dataPath}/RGPrefab/UI/FishChipStore/鱼干商店货物TimeLuban.csv");
            File.WriteAllText($"{Application.dataPath}/RGPrefab/UI/FishChipStore/鱼干商店货物TimeLuban.csv", sb.ToString(),
                new UTF8Encoding(true));
        }
        [ShowInInspector, LabelText("配置优先级"), PropertyOrder(-1), ShowIf("HasLoadConfig")]
        public int compareValue {
            get {
                if (_storeConfig == null) {
                    return -1;
                }
                return (int)_storeConfig.CompareValue;
            }
            set {
                if (_storeConfig == null) {
                    return;
                }
                if (value < 0) {
                    value = 0;
                }
                _storeConfig.CompareValue = (uint)value;
            }
        }

        [ButtonGroup("r1", Order = -2), Button("读取配置(默认读第一个)", ButtonSizes.Medium)]
        public void LoadConfig() {
            var p = from d in Directory.GetDirectories(ConfigPaths[0])
                where d.EndsWith("_Branch")
                from f in Directory.GetFiles(d)
                where f.EndsWith(ConfigFileName)
                select f;
            var path = p.FirstOrDefault();
            if (string.IsNullOrEmpty(path)) {
                ParentWindow.ShowNotification(new GUIContent("无法读取到配置"));
                return;
            }

            Debug.Log($"load config from path {path}");
            try {
                var config = JsonUtil.LoadJson<FishChipStoreConfig>(path);
                _storeConfig = config;
                FlushConfigTables();
                ParentWindow.ShowNotification(new GUIContent("配置读取完成"));
                currentIndex = _fishChipConfigTables.Count - 1;
            } catch (Exception e) {
                ParentWindow.ShowNotification(new GUIContent("读取配置错误"));
                Debug.LogError(e);
            }
        }

        private void FlushConfigTables() {
            _fishChipConfigTables = new List<FishChipConfigTable>();
            currentIndex = 0;
            for (var i = 0; i < _storeConfig.configs.Count; i++) {
                var encryptedText = _storeConfig.configs[i];
                var configText = CryptUtil.DecryptDES(encryptedText, Const.StoreConfigEncryptKey);
                var storeConfig = JsonUtil.ParseJson<StoreConfig>(configText);
                var table = new FishChipConfigTable();
                table.FlushConfigModels(ConfigModels);
                table.UpdateFromStoreConfig(storeConfig);
                _fishChipConfigTables.Add(table);
            }
        }

        [ButtonGroup("r4", Order = 3), Button("写入配置", ButtonSizes.Medium), ShowIf("HasLoadConfig")]
        public void SaveConfig() {
            var errorMessages = new List<string>();
            foreach (var fishChipConfigTable in _fishChipConfigTables) {
                if (fishChipConfigTable.IsInvalidConfig(out var messages)) {
                    errorMessages.Add($"{fishChipConfigTable.ToShortString()} 配置错误：{string.Join(", ", messages)}");
                }
            }

            // 有错误无法导入
            if (errorMessages.Count > 0) {
                var errorText = string.Join("\n", errorMessages);
                ParentWindow.ShowNotification(new GUIContent(errorText));
                return;
            }

            var confirmResult = EditorUtility.DisplayDialog("是否写入配置", "是否写入配置？", "是", "否");
            if (!confirmResult) {
                return;
            }

            try {
                _prevStoreConfigJson = JsonUtil.ToJson(_storeConfig, true);
                _storeConfig.configs.Clear();
                foreach (var fishChipConfigTable in _fishChipConfigTables) {
                    var jsonText = JsonUtil.ToJson(fishChipConfigTable.ToStoreConfig(), true);
                    var encryptText = CryptUtil.EncryptDES(jsonText, Const.StoreConfigEncryptKey);
                    _storeConfig.configs.Add(encryptText);
                }

                var configText = JsonUtil.ToJson(_storeConfig, true);
                for (var i = 0; i < ConfigPaths.Length; i++) {
                    var currentPath = $"{ConfigPaths[i]}/{ChanelNames[i]}_Branch/{ConfigFileName}";
                    File.WriteAllText(currentPath, configText);
                }
                
                // 写入luban配置
                var storeConfigs = _storeConfig.GetStoreConfig();
                foreach (var config in storeConfigs) {
                    config.InitItemIndex();
                }
                ExportStoreTimeConfig2LubanFormat(storeConfigs);

                ParentWindow.ShowNotification(new GUIContent("写入配置成功"));
                importText = "";
                exportText = "";
            } catch (Exception e) {
                ParentWindow.ShowNotification(new GUIContent("写入配置错误"));
                Debug.LogError(e);
            }
        }

        private bool HasBackupConfig => _prevStoreConfigJson != null;

        [Button("还原配置", ButtonSizes.Medium), ShowIf("HasBackupConfig"), PropertyOrder(4)]
        public void RestoreConfig() {
            if (_prevStoreConfigJson == null) {
                return;
            }

            var isConfirm = EditorUtility.DisplayDialog("是否还原为先前配置？", "是否还原为先前配置？", "是", "否");
            if (!isConfirm) {
                return;
            }

            try {
                for (var i = 0; i < ConfigPaths.Length; i++) {
                    var currentPath = $"{ConfigPaths[i]}/{ChanelNames[i]}_Branch/{ConfigFileName}";
                    File.WriteAllText(currentPath, _prevStoreConfigJson);
                }

                LoadConfig();
                ParentWindow.ShowNotification(new GUIContent("还原配置成功"));
                _prevStoreConfigJson = null;
                importText = "";
                exportText = "";
            } catch (Exception e) {
                Debug.LogError(e);
                ParentWindow.ShowNotification(new GUIContent("还原配置错误"));
            }
        }

        [HorizontalGroup("r2", Order = 1), LabelText("配置"), ValueDropdown("ConfigTableDropDropdownList"),
         LabelWidth(50), ShowIf("HasLoadConfig")]
        public int currentIndex = -1;

        private ValueDropdownList<int> ConfigTableDropDropdownList {
            get {
                var list = new ValueDropdownList<int>() {
                    {"无", -1}
                };
                if (!HasLoadConfig) {
                    return list;
                }

                for (var i = 0; i < _fishChipConfigTables.Count; i++) {
                    list.Add(_fishChipConfigTables[i].ToShortString(), i);
                }

                return list;
            }
        }

        [HorizontalGroup("r2", Width = 100), ButtonGroup("r2/b"), Button("添加"), ShowIf("HasLoadConfig")]
        public void AddConfig() {
            _fishChipConfigTables.Sort((table1, table2) => table1.expireTime.TimeStamp.CompareTo(table2.expireTime.TimeStamp));
            var table = new FishChipConfigTable();
            table.FlushConfigModels(ConfigModels);
            _fishChipConfigTables.Add(table);
            currentIndex = _fishChipConfigTables.Count - 1;
            if (_fishChipConfigTables.Count > 2) {
                var prevTable = _fishChipConfigTables[_fishChipConfigTables.Count - 2];
                table.startTime.FromTimeStamp(prevTable.expireTime.TimeStamp);
                var expireTimeStamp = table.startTime.TimeStamp + 60 * 60 * 24 * 7;
                table.expireTime.FromTimeStamp(expireTimeStamp);
            }
        }

        [HorizontalGroup("r2", Width = 100), ButtonGroup("r2/b"), Button("删除"), ShowIf("HasLoadConfig")]
        public void RemoveConfig() {
            if (currentIndex == -1) {
                return;
            }

            var result = EditorUtility.DisplayDialog(
                "是否删除配置?",
                $"是否删除配置 id：{_fishChipConfigTables[currentIndex].configId}({_fishChipConfigTables[currentIndex].startTime}~{_fishChipConfigTables[currentIndex].expireTime})?",
                "是", "否");
            if (result) {
                _fishChipConfigTables.RemoveAt(currentIndex);
                currentIndex = -1;
            }
        }

        [BoxGroup("商店配置"), Button("控制台输出预览"), ShowIf("IsSelectConfig")]
        public void PreviewInConsole() {
            ParentWindow.ShowNotification(new GUIContent("已在控制台输出"));
            var storeConfig = configTable.ToStoreConfig();
            var jsonText = JsonUtil.ToJson(storeConfig);
            var encryptText = CryptUtil.EncryptDES(jsonText, Const.StoreConfigEncryptKey);
            Debug.Log($"[小鱼干商店配置]json文本:\n{jsonText}");
            Debug.Log($"[小鱼干商店配置]加密文本:\n{encryptText}");
        }

        [BoxGroup("商店配置", Order = 2), ShowInInspector, HideLabel, HideReferenceObjectPicker,
         ShowIf("IsSelectConfig"), PropertyOrder(2)]
        public FishChipConfigTable configTable {
            get {
                if (!IsSelectConfig) {
                    return null;
                }

                return _fishChipConfigTables[currentIndex];
            }
            set {
                if (!IsSelectConfig) {
                    return;
                }

                _fishChipConfigTables[currentIndex] = value;
            }
        }

        [HorizontalGroup("商店配置/import", Title = "导入配置", Order = 3), HideLabel, ShowIf("IsSelectConfig"), PropertyOrder(0)]
        public string importText;

        [HorizontalGroup("商店配置/import"), Button("导入加密字符串"), ShowIf("IsSelectConfig")]
        public void ImportEncryptText() {
            if (string.IsNullOrEmpty(importText)) {
                ParentWindow.ShowNotification(new GUIContent("字符串为空"));
                return;
            }

            try {
                var jsonText = CryptUtil.DecryptDES(importText, Const.StoreConfigEncryptKey);
                var config = JsonUtil.ParseJson<StoreConfig>(jsonText);
                configTable.UpdateFromStoreConfig(config);
                importText = "";
                exportText = "";
            } catch (Exception e) {
                Debug.LogError(e);
                ParentWindow.ShowNotification(new GUIContent("发生错误"));
            }
        }
        
        [HorizontalGroup("商店配置/export", Title = "导出配置", Order = 3), HideLabel, ShowIf("IsSelectConfig"), PropertyOrder(1)]
        public string exportText;

        [HorizontalGroup("商店配置/export"), Button("导出加密字符串"), ShowIf("IsSelectConfig"), PropertyOrder(0)]
        public void exportEncryptText() {
            if (configTable.IsInvalidConfig(out var errorMessages)) {
                ParentWindow.ShowNotification(new GUIContent(string.Join(", ", errorMessages)));
                return;
            }

            try {
                var config = configTable.ToStoreConfig();
                var jsonText = JsonUtil.ToJson(config, true);
                var encryptText = CryptUtil.EncryptDES(jsonText, Const.StoreConfigEncryptKey);
                exportText = encryptText;
            } catch (Exception e) {
                Debug.LogError(e);
                ParentWindow.ShowNotification(new GUIContent("发生错误"));
            }
        }

        private bool IsSelectConfig => HasLoadConfig && currentIndex != -1;

        public List<FishChipConfigTable> FishChipConfigTables {
            set { _fishChipConfigTables = value; }
            get { return _fishChipConfigTables; }
        }

        public void Validate() {
            if (_fishChipConfigTables == null) {
                return;
            }
            foreach (var table in _fishChipConfigTables) {
                table.Validate();
            }
        }
    }
}