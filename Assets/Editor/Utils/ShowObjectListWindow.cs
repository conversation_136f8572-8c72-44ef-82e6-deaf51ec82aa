using DG.DemiEditor;

using Sirenix.OdinInspector;
using Sirenix.OdinInspector.Editor;
using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using Object = UnityEngine.Object;

public class ShowObjectListWindow : OdinEditorWindow {

    public List<Object> objects;


    public static ShowObjectListWindow GetWin() {
        var win = GetWindow<ShowObjectListWindow>("ShowObjectListWin");
        win.Show();
        return win;
    }

    protected override IEnumerable<object> GetTargets() {
        yield return this;
    }

    [Button("Set To AB Name By Type")]
    private void AutoABNameByType() {
        foreach (var obj in objects) {
            string path = AssetDatabase.GetAssetPath(obj);
            var assetImporter = AssetImporter.GetAtPath(path);

            if (!assetImporter.assetBundleName.IsNullOrEmpty()) {
                continue;
            }

            bool isChange = false;

            if (path.EndsWith(".png") && path.StartsWith("Assets/RGTexture")) {
                assetImporter.assetBundleName = "sprite_atlas";
                isChange = true;            
            }

            if (path.EndsWith(".mp3")) {
                assetImporter.assetBundleName = "sound_effect";
                isChange = true;
            }

            if (path.EndsWith(".anim") || path.EndsWith(".controller")) {
                assetImporter.assetBundleName = "common";
                isChange = true;
            }

            if (isChange) {
                LogUtil.Log($"--- change {path}");
                EditorUtility.SetDirty(obj);
            }

        }
    }

    [Button("SelectAll")]
    private void SelectAll() {
        Selection.objects = objects.ToArray();
    }
    
    public void SortByType() {
        SortAssetObjectByType(objects);
    }

    public static void SortAssetObjectByType(List<Object> objects) {
        objects.Sort((a, b)=> {
                string pathA = AssetDatabase.GetAssetPath(a);
                string pathB = AssetDatabase.GetAssetPath(b);
                return String.Compare( Path.GetExtension(pathA), Path.GetExtension(pathB), StringComparison.Ordinal);
            }
        );
    }
}
