using ChillyRoom.IM;
using ChillyRoom.IM.DataModel;
using ChillyRoom.IM.V1;
using I2.Loc;
using RGScript.Data;
using UIFramework;
using UnityCommon.UI;
using UnityEngine;
using UnityEngine.UI;

namespace IM.Scripts {
    /// <summary>
    /// 好友个人信息面板
    /// </summary>
    public class UIWindowProfileFriend : BaseUIView {
        private Button _btnClose;
        private Button _btnBlacklist;
        private Button _btnRemove;
        private ContactModel _contactModel;
        private Transform _content;
        private Image _avatar;
        private float _smallHeight = 223f;
        private RectTransform _body;

        public override void InitView() {
            handleEsc = true;
            base.InitView();
            _body = transform.Find("body").GetComponent<RectTransform>();
            _content = transform.Find("body/content");
            _avatar = _content.Find("avatar/icon").GetComponent<Image>();
            _btnClose = transform.Find("body/header/btn_close").GetComponent<Button>();
            _btnBlacklist = transform.Find("body/btn_blacklist").GetComponent<Button>();
            _btnRemove = transform.Find("body/btn_remove").GetComponent<Button>();
            _btnClose.onClick.AddListener(OnClick_Close);
            _btnBlacklist.onClick.AddListener(OnClickBlacklist);
            _btnRemove.onClick.AddListener(OnClickRemove);
        }

        private void OnClickBlacklist() {
            var title = ScriptLocalization.Get("I_Warn");
            var content = ScriptLocalization.Get("im/friend_blacklist_confirm");
            UIWindowDialog.ShowDialog(UIManager.FindCanvasRoot(), title, content,
                () => {
                    DataMgr.IMData.BlockUser(_contactModel.UserId);
                    OnClick_Close();
                }, null, 0, true, useAb: true, showMask: true);
        }

        private void OnClickRemove() {
            var title = ScriptLocalization.Get("I_Warn");
            var content = ScriptLocalization.Get("im/friend_remove_confirm");
            UIWindowDialog.ShowDialog(UIManager.FindCanvasRoot(), title, content,
                () => {
                    DataMgr.IMData.RemoveFriend(_contactModel.UserId);
                    OnClick_Close();
                }, null, 0, true, useAb: true, showMask: true);
        }

        public override void ShowView(params object[] args) {
            base.ShowView(args);
            if (args == null || args.Length == 0) {
                Debug.LogError("参数不能为空");
                return;
            }

            _contactModel = (ContactModel)args[0];
            var uidStr = "UID";
            var nicknameStr = ScriptLocalization.Get("im/nickname", "#昵称");
            _content.Find("detail/nickname").GetComponent<Text>().text = nicknameStr + ":" + _contactModel.NickName;
            _content.Find("detail/uid").GetComponent<Text>().text = uidStr + ":" + _contactModel.UserId;
            FriendUtils.LoadProfileIcon(_contactModel.AvatarUrl, _avatar, 90f);
            
            // 不是好友关系，则隐藏两个操作按钮
            if (_contactModel.RelationType != UserRelationType.Friend) {
                _btnBlacklist.gameObject.SetActive(false);
                _btnRemove.gameObject.SetActive(false);
                _body.sizeDelta = new Vector2(_body.sizeDelta.x, _smallHeight);
            }
        }
    }
}