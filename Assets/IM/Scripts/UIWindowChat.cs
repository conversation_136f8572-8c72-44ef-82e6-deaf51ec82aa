using ChillyRoom.IM;
using ChillyRoom.IM.DataModel;
using ChillyRoom.IM.V1;
using Extensions.Unity.ImageLoader;
using I2.Loc;
using RGScript.Data;
using System;
using UIFramework;
using UnityCommon.UI;
using UnityEngine;
using UnityEngine.UI;
using UniRx;

namespace IM.Scripts {
    /// <summary>
    /// 聊天窗口
    /// </summary>
    public class UIWindowChat : BaseUIView {
        public Action OnBack;
        private const string SENTENCE_WINDOW_PATH = "IM/Prefabs/window_sentences.prefab";
        private const string EMO_WINDOW_PATH = "IM/Prefabs/window_emos.prefab";
        private Transform _content;
        private UIWidgetChatDialog _uiWidgetChatDialog;
        private UIWindowSentences _uiWindowSentences;
        private UIWindowEmos _uiWindowEmos;
        private Image _friendAvatar;
        private Text _friendNickname;
        private GameObject _friendStatusOn;
        private GameObject _friendStatusOff;
        private GameObject _friendStatusBusy;
        private Button _btnBack;
        private Button _btnClose;
        private Button _btnSentences;
        private Button _btnEmos;
        private Button _btnMultiInvite;
        private Button _btnMultiInviteOff;
        private ContactModel _friendModel;
        // private ContactModel _myProfile;

        public override void InitView() {
            handleEsc = true;
            base.InitView();
            // _myProfile = DataMgr.IMData.GetMyProfile();
            FindRefs();
        }
        
        private void FindRefs() {
            _content = transform.Find("body/content");
            _uiWidgetChatDialog = _content.Find("scroller").GetComponent<UIWidgetChatDialog>();
            RegisterUIWidget(_uiWidgetChatDialog);

            _friendAvatar = transform.Find("body/header/btn_avatar/icon").GetComponent<Image>();
            _friendNickname = transform.Find("body/header/nickname/text").GetComponent<Text>();
            _friendStatusOn = transform.Find("body/header/btn_avatar/status_online").gameObject;
            _friendStatusOff = transform.Find("body/header/btn_avatar/status_offline").gameObject;
            _friendStatusBusy = transform.Find("body/header/btn_avatar/status_busy").gameObject;
            
            _btnMultiInvite = transform.Find("body/console/btn_invite_on").GetComponent<Button>();
            _btnMultiInviteOff = transform.Find("body/console/btn_invite_off").GetComponent<Button>();
            _btnBack = transform.Find("body/header/btn_back").GetComponent<Button>();
            _btnSentences = transform.Find("body/console/btn_group/tab_txt").GetComponent<Button>();
            _btnEmos = transform.Find("body/console/btn_group/tab_emo").GetComponent<Button>();
            _btnClose = transform.Find("body/header/btn_close").GetComponent<Button>();
            
            _btnClose.onClick.AddListener(OnClickCloseFamily);
            _btnBack.onClick.AddListener(OnClickBackBtn);
            _btnSentences.onClick.AddListener(OnClickSentences);
            _btnEmos.onClick.AddListener(OnClickEmo);
            _btnMultiInvite
                .OnClickAsObservable()
                .ThrottleFirst(TimeSpan.FromMilliseconds(3000))
                .Subscribe(_ => OnClickInvite())
                .AddTo(this);
            _btnMultiInviteOff
                .OnClickAsObservable()
                .ThrottleFirst(TimeSpan.FromMilliseconds(3000))
                .Subscribe(_ => OnClickInviteOff())
                .AddTo(this);
        }

        public override void ShowView(params object[] args) {
            base.ShowView(args);
            
            if (args == null || args.Length == 0) {
                Debug.LogError("[IMChat] args should not be empty");
                return;
            }

            _friendModel = (ContactModel)args[0];
            RefreshFriendInfo();
            RefreshChatWidget();
        }

        public void OnClickBackBtn() {
            OnClick_Close();
            OnBack?.Invoke();
        }
        
        private void OnClickInviteOff() {
            UICanvasRoot.Inst.ShowMessage(
                ScriptLocalization.Get("im/tip/not_in_multi_room", "#处于远程联机房间中才可发起邀请"),
                2f);
        }
        
        private void OnClickInvite() {
            _uiWidgetChatDialog.SendChatMessage("", ChatMessageType.TeamInvite);
        }
        
        private void OnClickSentences() {
            _uiWindowSentences = UIManager.Inst.OpenUIView<UIWindowSentences>(SENTENCE_WINDOW_PATH);
            _uiWindowSentences.OnClick = OnSelectSentence;
            if (_uiWindowEmos != null) {
                _uiWindowEmos.OnClick_Close();
            }
        }
        
        private void OnClickEmo() {
            _uiWindowEmos = UIManager.Inst.OpenUIView<UIWindowEmos>(EMO_WINDOW_PATH);
            _uiWindowEmos.OnClick = OnSelectEmo;
            if (_uiWindowSentences != null) {
                _uiWindowSentences.OnClick_Close();
            }
        }

        /// <summary>
        /// 不直接上屏，根据消息回调上屏
        /// 据前传说，延迟比较低，他们也是这么干的
        /// </summary>
        /// <param name="text"></param>
        private void OnSelectSentence(string text) {
            _uiWidgetChatDialog.SendChatMessage(text, ChatMessageType.Text);
        }

        private void OnSelectEmo(int groupIdx, int emojiIdx) {
            _uiWidgetChatDialog.SendChatEmoji(groupIdx, emojiIdx);
        }
        
        public void SendInviteCode(string inviteCode) {
            _uiWidgetChatDialog.SendChatInviteCode(inviteCode);
        }

        private void RefreshStatus() {
            var isBusy = _friendModel.MasterStatus == ContactMasterStatus.Busy;
            var online = _friendModel.MasterStatus == ContactMasterStatus.Online;
            var offline = _friendModel.MasterStatus == ContactMasterStatus.Offline;
            var showInviteBtn = FriendUtils.InMultiRoom();
            _btnMultiInvite.gameObject.SetActive(showInviteBtn);
            _btnMultiInviteOff.gameObject.SetActive(!showInviteBtn);
            
            _friendNickname.text = _friendModel.NickName;
            _friendStatusOn.SetActive(online);
            _friendStatusOff.SetActive(offline);
            _friendStatusBusy.SetActive(isBusy);
        }

        private void RefreshFriendInfo() {
            RefreshStatus();
            _friendAvatar.sprite = null;
            FriendUtils.LoadProfileIcon(_friendModel.AvatarUrl, _friendAvatar, 55f);
        }

        private void RefreshChatWidget() {
            _uiWidgetChatDialog.StartUp(_friendModel);
        }
    }
}
