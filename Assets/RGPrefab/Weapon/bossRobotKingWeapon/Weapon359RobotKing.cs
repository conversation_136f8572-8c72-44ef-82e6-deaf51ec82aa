using MycroftToolkit.QuickCode;
using System.Collections.Generic;
using UnityEngine;


public class Weapon359RobotKing : RGWeapon, WeaponInterface,  IWeaponSpecial,  IWeaponCustomText, ChargeWeaponInterface {
    public string damage { get => "3~16"; }
    public new string consume { get => "0~10"; }
    public new string critical { get=>"0~20"; }
    public new string deviation { get => base.clampedRuntimeDeviation.ToString(); }
    
    public bool IsSpecial => true;
    public int SpecialMode => -1;
    public override bool HandCutWhenTargetNear { get => false; }
    protected override emWeaponType default_type => emWeaponType.Axe;
    
    public List<AudioClip> audioClips;

    private int _currentSkillID;
    private readonly int[] _skillConsumes = { 0, 3, 10, 5};
    private readonly bool[] _isSkillNeedLockAtTarget = { true, false, false, true};
    private bool _isDoingSkill;
    
    public GameObject holdGo;
    private GameObject _holdGo;
    public AudioClip holdClip;
    public float targetHoldTime;
    private float _atkHoldTime;
    private bool _isHoldMax;
    private bool _isHolding;

    private readonly List<string> _guardUnitPathList = new List<string> {
        "Level/2/G/Enemy/e_robot_knight_1/e_robot_knight_1.prefab",
        "Level/2/G/Enemy/e_robot_knight_2/e_robot_knight_2.prefab",
        "Level/2/G/Enemy/e_robot_knight_3/e_robot_knight_3.prefab",
        "Level/2/G/Enemy/e_robot_knight_4/e_robot_knight_4.prefab"
    };
    public readonly List<GameObject> GuardUnitList = new List<GameObject>();

    private static readonly int SkillIndex = Animator.StringToHash("skillIndex");
    private static readonly int AtkT = Animator.StringToHash("atk_t");

    public bool testCallRobotKing;
    public bool testCallRobotQueen;

    protected override void Start() {
        base.Start();
        _effectShowUpPrefab = PrefabManager.GetPrefab(PrefabName.effect_show_up);
        _effectShowOutPrefab = PrefabManager.GetPrefab(PrefabName.effect_show_out);

        
        StartCoroutine(AssetBundleLoader.Inst.LoadBundle("level/2/g", () => {
            foreach (var path in _guardUnitPathList) {
                var prefab = ResourcesUtil.Load<GameObject>(path);
                if (prefab == null) {
                    Debug.LogError($"Cant load guard unit{path}!");
                    continue;
                }
                GuardUnitList.Add(prefab);
            }
        }));
    }
    
    private void Update() {
        if (!controller) {
            return;
        }

        if (_isHolding && !_isDoingSkill) {
            UpdateHold();
        }

        if (_isDoingSkill && _currentSkillID == 3) {
            controller.facing = _currentFacing;
        }
    }

    #region 按键处理

    public override void SetAttack(bool isDown, bool manual = true) {
        if (!controller) {
            return;
        }

        if (activate) {
            if (isDown) {
                AttackKeyDown(manual);
            } else {
                AttackKeyUp(manual);
            }
        } else {
            ActivateFalseTips();
        }
    }

    public override void AttackKeyDown(bool manual) {
        if (_isDoingSkill) {
            return;
        }

        atk_b = true;

        if (controller.attribute.energy < _skillConsumes[1]) {
            base.AttackKeyDown(manual);
            DoSkill(0);
            return;
        }
        
        if (!manual) {
            base.AttackKeyDown(manual);
            DoSkill(1);
            return;
        }
        
        _isHolding = true;
    }

    public override void AttackKeyUp(bool manual) {
        if (!atk_b)return;
        if (!_isDoingSkill) {
            AttackStart();
            DoSkill(_isHoldMax ? 3 : 1);
        }

        ResetHold();
        base.AttackKeyUp(manual);
    }
    
    public void WeaponSpecial(bool isDown) {
        if (!isDown || _isDoingSkill) {
            return;
        }
        AttackStart();
        DoSkill(2);
    }
    
    private void UpdateHold() {
        if (controller.attribute.energy >= _skillConsumes[2]) {
            if (_atkHoldTime < targetHoldTime) {
                _atkHoldTime += Time.deltaTime * _speedFactor * holdSpeed;
                if (_holdGo || !(_atkHoldTime > .2f * targetHoldTime)) {
                    return;
                }

                _holdGo = PrefabPool.Inst.Take(holdGo);
                _holdGo.transform.SetParent(controller.transform, false);
                _holdGo.GetComponent<ReloadClip>()
                    .PlayReloadAnim((targetHoldTime - _atkHoldTime) / holdSpeed / _speedFactor);
                RGMusicManager.GetInstance().PlayEffect(holdClip);
                return;
            }

            _isHoldMax = true;
            anim.SetBool(Hold, true);
            
            if (_autoAttack) {
                SetAttack(false, false);
            }
            return;
        }

        AttackStart();
        DoSkill(1);
    }

    private void ResetHold() {
        _atkHoldTime = 0;
        _isHoldMax = false;
        _isHolding = false;
        anim.SetBool(Hold, false);
        if (_holdGo) {
            Destroy(_holdGo);
        }
    }
    #endregion

    public override void StartUseWeapon() {
        base.StartUseWeapon();
        _isHolding = false;
        handCutWhenOutOfEnergy = false;
        _isDoingSkill = false;
    }
    
    public override void StopWeapon() {
        base.consume = _skillConsumes[1];
        ResetHold();
        need_lock = true;

        if (_skill3BulletCtrl != null) {
            PrefabPool.Inst.Store(_skill3BulletCtrl.gameObject);
            _skill3BulletCtrl = null;
        }

        if (_theGuardCtrl == null) {
            _hasGuard = false;
        }
        _isDoingSkill = false;
        base.StopWeapon();
    }

    protected override void OnDestroy() {
        base.OnDestroy();
        if (_creatGuardTimer != null) {
            _creatGuardTimer.Cancel();
            _creatGuardTimer = null;
        }

        if (_theGuardCtrl != null) {
            _theGuardCtrl.OnEnemyDestroy -= OnGuardDestroy;
        }
    }

    private void DoSkill(int skillIndex) {
        if (_isDoingSkill) {
            return;
        }
        if (controller.attribute.energy < _skillConsumes[skillIndex]) {
            skillIndex = 0;
        }

        switch (skillIndex) {
            case 0:
                ActivateFalseTips();
                break;
            case 2://不能携带宠物,佣兵的因子不召唤
                if (_hasGuard || _theGuardCtrl != null || BattleData.data.CompareFactor(emBattleFactor.AllAlone)) {
                    if (BattleData.data.CompareFactor(emBattleFactor.AllAlone)) {
                        UICanvas.GetInstance().ShowTextTalk(controller.transform, I2.Loc.ScriptLocalization.Get("I_allalone"), 2.5f, 1f);
                    }
                    base.consume = 0;
                    ActivateFalseTips();
                    atk_b = false;
                    return;
                }
                _hasGuard = true;
                break;
            case 3:
                _currentFacing = controller.facing;
                break;
        }
        
        _currentSkillID = skillIndex;
        need_lock = _isSkillNeedLockAtTarget[_currentSkillID];
        ResetHold();
        anim.SetTrigger(AtkT);
        anim.SetInteger(SkillIndex, skillIndex);
        _isDoingSkill = true;
    }

    #region 动画事件回调
    public void Attack() => Anima_OnSkillEffectiveStart();

    protected void Anima_OnSkillEffectiveStart() {
        Invoke($"AtkMode{_currentSkillID}_Start",0);
        base.consume = _skillConsumes[_currentSkillID];
        MakeConsume();
    }
    
    protected void Anima_OnSkillEffectiveEnd() =>
        Invoke($"AtkMode{_currentSkillID}_End",0);
    
    protected void Anima_OnSkillEnd() {
        AfterAttackEvent();
        base.consume = _skillConsumes[1];
        _isDoingSkill = false;
        need_lock = true;
        atk_b = false;
    }

    protected void Anima_OnSkillSoundEffectPlay() {
        switch (_currentSkillID) {
            case 0:
                SoundEffectPlay(0);
                return;
            case 1:
                SoundEffectPlay(0);
                return;
            case 2:
                SoundEffectPlay(1);
                return;
            case 3:
                SoundEffectPlay(2);
                return;
        }
    }
    #endregion

    #region 武器技能相关
    void AtkMode0_Start() {// 抡
        GetBulletInstance<DamageCarrier>(0, gun_point.position);
    }
    
    void AtkMode1_Start() {// 重锤
        GetComponent<HarmmerLightning>().AfterBulletCreate();
        GetBulletInstance<DamageCarrier>(0, gun_point.position);
    }

    private bool _hasGuard;
    private RGEController _theGuardCtrl;
    private GameObject _effectShowUpPrefab;
    private GameObject _effectShowOutPrefab;
    private Timer _creatGuardTimer;
    private void AtkMode2_Start() {// 机械护驾
        if (testCallRobotKing || testCallRobotQueen || rg_random.GetBool(0.3f)) {
            AtkMode4_Start();
            return;
        }
        
        var targetPos = transform.position;
        var targetGuard = GuardUnitList.GetRandomObject(rg_random);
        
        // 若有宠物增强因子就召唤精英怪
        bool isExGuard = GameUtil.GetBattleData(controller).HasBuff(emBuff.ExpertPet);
        if (isExGuard) {
            targetGuard = targetGuard.GetComponent<RGEController>().exEnemy;
        }
        
        var parent = RGGameSceneManager.GetInstance().temp_objects_parent;

        ShowEffect(_effectShowUpPrefab, targetPos);
        var master = controller;
        _creatGuardTimer = Timer.Register(0.5f, false, false, () => {
            GameObject newEnemy = Instantiate(targetGuard, targetPos, Quaternion.identity, parent);
            _theGuardCtrl = newEnemy.GetComponent<RGEController>();
            _theGuardCtrl.start_awake = true;
            _theGuardCtrl.temp_enemy = true;
            _theGuardCtrl.destory_on_dead = true;
            _theGuardCtrl.dead_destory_delay_time = 2f;
            _theGuardCtrl.ai_level = 1;
            DeadBodyController.CreateController(_theGuardCtrl, master.transform, 1);
            _theGuardCtrl.DestroySelf(10f);
            _theGuardCtrl.OnEnemyDestroy += OnGuardDestroy;
            master = null;
        });
    }

    private void OnGuardDestroy() {
        ShowEffect(_effectShowOutPrefab, _theGuardCtrl.transform.position);
        _theGuardCtrl.OnEnemyDestroy -= OnGuardDestroy;
        _theGuardCtrl = null;
        _hasGuard = false;
    }

    private RGSword _skill3BulletCtrl;
    private int _currentFacing;
    void AtkMode3_Start() {// 权杖转转转
        var theBulletCtrl = GetBulletInstance<RGSword>(2, gun_point.position, false);
        var targetTransform = theBulletCtrl.transform;
        targetTransform.parent = gun_point;
        targetTransform.localPosition = Vector3.zero;
        theBulletCtrl.manualDestroy = true;
        theBulletCtrl.OnTaken();
        _skill3BulletCtrl = theBulletCtrl;
    }

    void AtkMode3_End() {
        if (_skill3BulletCtrl == null) {
            return;
        }

        _skill3BulletCtrl.manualDestroy = false;
        _skill3BulletCtrl.DestroySelf(0);
    }

    public GameObject robotKingCallObj;
    public GameObject robotQueenCallObj;
    public GameObject robotQueenWeaponDrop;
    private static readonly int Hold = Animator.StringToHash("isHold");

    void AtkMode4_Start() {// 英灵召唤
        _hasGuard = false;
        GameObject callTarget;
        var parent = RGGameSceneManager.GetInstance().temp_objects_parent;
        var targetPos = transform.position;
        
        ShowEffect(_effectShowUpPrefab, targetPos, 3f);

        Timer.Register(0.5f, false, false, () => {
            if (this == null) {
                return;
            }
            if (testCallRobotQueen || rg_random.GetBool(0.3f)) {
                callTarget = robotQueenCallObj;
                var callObjCtrl = Instantiate(callTarget, transform.position, Quaternion.identity, parent)
                    .GetComponent<Weapon359CallBoss>();
                callObjCtrl.Init(gameObject, controller.camp, 10, 0);
                callObjCtrl.OnShowOut += ()=> {
                    ShowEffect(_effectShowOutPrefab, targetPos, 3f);
                    var drop = ShowEffect(robotQueenWeaponDrop, targetPos);
                    drop.name = drop.name.Replace("(Clone)", "");
                };
            } else {
                callTarget = robotKingCallObj;
                var callObjCtrl = Instantiate(callTarget, transform.position, Quaternion.identity, parent)
                    .GetComponent<Weapon359CallBoss>();
                float targetAngle;
                if (targetObj != null) {
                    Vector3 t = targetObj.position - callObjCtrl.firePos.position;
                    targetAngle = Vector3.SignedAngle(Vector3.right, t, Vector3.forward);
                } else {
                    targetAngle = fixedAngle;
                }

                callObjCtrl.Init(gameObject, controller.camp, 4, targetAngle);
                callObjCtrl.OnShowOut += ()=> {
                    ShowEffect(_effectShowOutPrefab, targetPos, 3f);
                };
            }

            Anima_OnSkillEnd();
        });
    }

    private GameObject ShowEffect(GameObject effectPrefab, Vector3 pos, float scale = 1f) {
        var parent = RGGameSceneManager.GetInstance().temp_objects_parent;
        var go = Instantiate(effectPrefab, pos, Quaternion.identity, parent);
        go.transform.localScale *= scale;
        return go;
    }
    #endregion

    private void SoundEffectPlay(int index) {
        if (index < 0 || index >= audioClips.Count) {
            return;
        }
        RGMusicManager musicManager = RGMusicManager.GetInstance();
        musicManager.SetEffectMixer(5);
        musicManager.PlayEffect(audioClips[index]);
    }

    private T GetBulletInstance<T>(int index, Vector3 pos,bool invokeTaken = true , float angle = 0, float speed = -1) {
        BulletInfo theBulletInfo = GetBulletInfo(index);
        theBulletInfo.camp = 1; // 1为玩家阵营
        theBulletInfo.createPosition = pos;
        // 角度调整
        if (angle == -1) {
            angle = fixedAngle;
            if (targetObj != null) {
                Vector3 t = targetObj.position - pos;
                angle = Vector3.SignedAngle(Vector3.right, t, Vector3.forward);
            }
        }
        theBulletInfo.directionAngle = angle;
            
        if (speed != -1) {
            theBulletInfo.speed = speed;
        }

        GameObject targetBullet =BulletFactory.TakeBullet(theBulletInfo, GetDamageInfo(index), invokeTaken);
        targetBullet.transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;
        T targetBulletCtrl = targetBullet.GetComponent<T>();
        if (targetBulletCtrl.HasMethod("SetSourceObject")) {
            targetBulletCtrl.InvokeMethod("SetSourceObject",gameObject);
        }

        BulletCreateEvent(targetBullet);
        AfterBulletCreateEvent();
        AfterAttackEvent();

        return targetBulletCtrl;
    }

    public float GetMaxChargeTime() {
        return targetHoldTime;
    }

	public WeaponChargeState GetWeaponChargeState() {
        var chargeAmount = GetChargeAmount();
        if (chargeAmount <= 0) {
            return WeaponChargeState.Normal;
        } else if (chargeAmount >= 1) {
            return WeaponChargeState.FullCharged;
        }
        return WeaponChargeState.Charging;
    }

	public float GetChargeAmount() {
        return _atkHoldTime / targetHoldTime;
    }

    public void SetChargeAmount(float amount) {
        if (_holdGo != null) {
            _holdGo.GetComponent<ReloadClip>().SetReloadProgress(amount, false);
        }
        _atkHoldTime = amount * GetMaxChargeTime();
    }
}
