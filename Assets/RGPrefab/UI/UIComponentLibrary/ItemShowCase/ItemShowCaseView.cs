using RGScript.UI.Widget;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Common {
    public class ItemShowCaseView : MonoBehaviour {
        public void SetItem(string item, int num) {
            if (num < 0) {
                num = 0;
            }

            var itemWidget = transform.Find("item").GetComponent<Item>();
            itemWidget.SetItem(item);
            transform.Find("item_num").GetComponent<Text>().text = num.ToString();
        }

        public void SetItemColor(Color color) {
            var itemWidget = transform.Find("item").GetComponent<Item>();
            itemWidget.SetColor(color);
        }
    }
}