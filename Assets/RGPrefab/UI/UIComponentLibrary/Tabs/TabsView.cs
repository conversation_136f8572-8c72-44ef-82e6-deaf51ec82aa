using System;
using System.Collections.Generic;
using System.Linq;
using UnityCommon.UI;
using UnityEngine;
using UnityEngine.UI;

namespace RGPrefab.UI.UIComponentLibrary.Tabs {
    public class TabsView : BaseUIView {
        [Serializable]
        public struct Content {
            public string tabName;
            public Button tabBtn;
            public Sprite selectSprite;
            public Sprite unSelectSprite;
            public GameObject content;
        }

        public bool needChangeTextColor;
        public Color selectColor;
        public Color unselectColor;
        public string defaultTabName;
        protected Content CurrentContent;
        public List<Content> contentList;

        public override void InitView() {
            base.InitView();
            handleEsc = true;
            pauseWhenShow = true;
        }

        protected virtual void Start() {
            if (contentList.Count == 0) {
                return;
            }

            foreach (var btn in contentList.Select(content => content.tabBtn)) {
                btn.onClick.AddListener(() => OnTabBtnClick(btn.gameObject));
            }

            SelectTab(defaultTabName);
        }

        public virtual void OnTabBtnClick(GameObject targetBtn) {
            if (targetBtn == null || targetBtn.Equals(CurrentContent.tabBtn.gameObject)) {
                return;
            }
            var targetContent = GetContent(targetBtn.GetComponent<Button>());
            SelectTab(targetContent);
        }

        public void SelectTab(string tabName) {
            var targetContent = GetContent(tabName);
            SelectTab(targetContent);
        }

        public void SelectTab(Content targetContent) {
            foreach (var content in contentList) {
                bool isTarget = content.tabName == targetContent.tabName;
                content.content.SetActive(isTarget);
                if (isTarget) {
                    content.tabBtn.GetComponent<RectTransform>().SetAsLastSibling();
                } else {
                    content.tabBtn.GetComponent<RectTransform>().SetAsFirstSibling();
                }
                
                Color targetColor = isTarget ? selectColor : unselectColor;
                Sprite targetSprite = isTarget ? content.selectSprite : content.unSelectSprite;
                if (targetSprite != null) {
                    content.tabBtn.GetComponent<Image>().sprite = targetSprite;
                }
                
                if (needChangeTextColor) {
                    content.tabBtn.transform.Find("Text").GetComponent<Text>().color = targetColor;
                }
            }

            CurrentContent = targetContent;
        }

        public Content GetContent(string contentName) {
            return contentList.Find((x) => x.tabName == contentName);
        }

        public Content GetContent(Button button) {
            return contentList.Find((x) => x.tabBtn.gameObject.Equals(button.gameObject));
        }
    }
}