using UnityEngine;
using System.Collections;

/// <summary>
/// 训练假人
/// </summary>
public class EnemyDummy : RGEController {
    public static int dummyIndex;

    protected override void Awake() {
        base.Awake();
        anim = transform.GetComponent<Animator>();
        role_attribute = gameObject.GetComponent<RoleAttribute>();
        shadow_lock = transform.Find("shadow_lock").GetComponent<SpriteRenderer>();
        name = "dummy_" + dummyIndex++;
    }

    public override void SetUpAttribution() {
        base.SetUpAttribution();
        StopCoroutine("SendingPosition");
        
    }

    protected override void FixedUpdate() {
        InnerFixedUpdate();
    }

    public override void Dead(GameObject source_object, bool sync) {
        if (!dead) {
            transform.GetComponent<BoxCollider2D>().enabled = false;
            //transform.Find("collider").gameObject.SetActive(false);
            dead = true;
            Buff<PERSON><PERSON>roy();
            RGMusicManager.GetInstance().PlayEffect(clip_dead);
            gameObject.SetActive(false);
            Destroy(gameObject, 2f);
        }
    }
    //眩晕
    public override void Dizzy(float duration, bool isFreeze) { }


}
