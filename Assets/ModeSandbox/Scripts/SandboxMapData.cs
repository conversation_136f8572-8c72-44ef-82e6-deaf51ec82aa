using FullSerializer;
using System;
using System.Collections;
using System.Collections.Generic;
using Abo;
using RGScript.Data;
using RGScript.Manager.SDK;
using RGScript.Map;
using RGScript.Other.NewSDK;
using RGScript.Util.LifeCycle;
using System.IO;
using UnityEngine;
using Utils.CommonUtils;


/// <summary>
/// 沙盒配置数据
/// </summary>
[System.Serializable]
public class SandboxConfigData : IData {
    public bool firstEnter = true;
    public List<Color> recentColors = new List<Color>();//最近使用的颜色
    public int mapIndex;//当前地图索引

    private static SandboxConfigData _data;
    public static SandboxConfigData data {
        get {
            if (_data == null) {
                Load();
            }
            return _data;
        }
    }
    #region 保存到本地
    static string _43OldPath =>Abo.FileUtil.InternalPersistentPath + "/sandbox_config.data";
    private static string Path => NewSDKManager.IsLogined() ? Abo.FileUtil.InternalPersistentPath + $"/sandbox_config_{PrefixUID.uid}.data" : _43OldPath;
    public static void FixData(string uid) {
        FileUtil.CopyStringFile(_43OldPath, $"{Abo.FileUtil.InternalPersistentPath}/sandbox_config_{uid}_.data");
        SetData(null);
    }
        
    /// <summary>
    /// 账号相关目录下不存在数据文件才拷贝，避免错误覆盖
    /// </summary>
    public static void FixDataIfNotExist() {
        if (!ChannelConfig.IsNeedFixDataIfNotExist) {
            return;
        }
        var oldExits = Abo.FileUtil.Exists(_43OldPath);
        var exits = Abo.FileUtil.Exists(Path);
        if (!exits && oldExits) {
            if(LogUtil.IsShowLog){LogUtil.Log($"FixDataIfNotExist {_43OldPath} => {Path}", NewSDKManager.Move_Tag);}
            Abo.FileUtil.CopyStringFile(_43OldPath, Path);
        }
    }

    public static void Save() {
        try {
            // 明文保存
            SaveUtil.SaveData(data, Path);
        } catch (System.Exception e) {
            Debug.LogError(e);
            BuglyUtil.ReportException("SandboxConfigSave", e);
        }
    }
    
    // 获取账户的数据位置
    public static string GetDataPathByAccountID(string accountID) {
        return Abo.FileUtil.InternalPersistentPath + $"/sandbox_config_{accountID}_.data";
    }
        
    // 获取账户数据
    public static IData GetAccountData(string dataPath) {
        if (Abo.FileUtil.Exists(dataPath)) {
            // 明文读取
            return SaveUtil.LoadData<SandboxConfigData>(dataPath);
        }

        return null;
    }
    
    // 设置数据
    public static void SetData(IData iData) {
        if (iData == null) {
            _data = null;
            return;
        }
        
        if (iData is SandboxConfigData sandboxConfigData) {
            _data = sandboxConfigData;
        }
    }
    
    public static void Load() {
        var accountData = GetAccountData(Path);
        if (null != accountData && accountData is SandboxConfigData sandboxConfigData) {
            SetData(sandboxConfigData);
        }
        
        if (_data == null) {
            SetData(new SandboxConfigData());
            Save();
        }
    }
    #endregion

    public static void Reload() {
        SetData(null);
        Load();
    }

    public static void ClearData(string prefix) {
        File.Delete(Abo.FileUtil.InternalPersistentPath + $"/sandbox_config_{prefix}.data");
    }
}


[System.Serializable]
public class SandboxMaps : IData {
    public List<SandboxMapData> maps = new List<SandboxMapData>();
    public SandboxMapData GetMapData(int mapIndex) {
        while (maps.Count <= mapIndex) {
            maps.Add(new SandboxMapData());
        }
        return maps[mapIndex];
    }

    private static SandboxMaps _data;
    public static SandboxMaps data {
        get {
            if (_data == null) {
                Load();
            }
            return _data;
        }
        set {
            SetData(value);
        }
    }
    public SandboxMapData currentMap {
        get {
            return GetMapData(SandboxConfigData.data.mapIndex);
        }
    }

    [fsIgnore]
    private SandboxMapData _editingMap;
    [fsIgnore]
    public SandboxMapData editingMap {
        get {
            if (_editingMap == null) {
                ReloadEditingMap();
            }
            return _editingMap;
        }
    }

    public void ReloadEditingMap() {
        _editingMap = currentMap.Clone();
    }
    public void SetEditingMap(SandboxMapData data) {
        _editingMap = data;
    }

    #region 保存到本地
    static string _43OldPath => Abo.FileUtil.InternalPersistentPath + "/sandbox_maps.data";
    private static string Path => NewSDKManager.IsLogined() ? Abo.FileUtil.InternalPersistentPath + $"/sandbox_maps_{PrefixUID.uid}.data" : _43OldPath;
    public static void FixData(string uid) {
        FileUtil.CopyStringFile(_43OldPath, $"{Abo.FileUtil.InternalPersistentPath}/sandbox_maps_{uid}_.data");
        SetData(null);
    }
        
    /// <summary>
    /// 账号相关目录下不存在数据文件才拷贝，避免错误覆盖
    /// </summary>
    public static void FixDataIfNotExist() {
        if (!ChannelConfig.IsNeedFixDataIfNotExist) {
            return;
        }
        var oldExits = Abo.FileUtil.Exists(_43OldPath);
        var exits = Abo.FileUtil.Exists(Path);
        if (!exits && oldExits) {
            if(LogUtil.IsShowLog){LogUtil.Log($"FixDataIfNotExist {_43OldPath} => {Path}", NewSDKManager.Move_Tag);}
            Abo.FileUtil.CopyStringFile(_43OldPath, Path);
        }
    }
    public static void Save() {
        Save(false);
    }
    public static void Save(bool updateMap) {
        try {
            if (updateMap) {
                data.UpdateCurrentMap();
            }
            
            // 明文保存
            SaveUtil.SaveData(data, Path);
            if (updateMap) {
                var canvasInstance = UICanvas.GetInstance();
                if (canvasInstance != null) {
                    canvasInstance.ShowTempMessage("sb/save_done", 1f);
                }
            }
        } catch (System.Exception e) {
            Debug.LogError(e);
            BuglyUtil.ReportException("SandboxMapSave", e);
        }
    }
    
    // 获取账户的数据位置
    public static string GetDataPathByAccountID(string accountID) {
        return Abo.FileUtil.InternalPersistentPath + $"/sandbox_maps_{accountID}_.data";
    }
    
    // 获取账户数据
    public static IData GetAccountData(string dataPath) {
        if (Abo.FileUtil.Exists(dataPath)) {
            // 明文读取
            return SaveUtil.LoadData<SandboxMaps>(dataPath);;
        }

        return null;
    }
    
    // 设置数据
    public static void SetData(IData iData) {
        if (iData == null) {
            _data = null;
            return;
        }
        
        if (iData is SandboxMaps sandboxMaps) {
            _data = sandboxMaps;
        }
    }
    
    public static void Load() {
        var accountData = GetAccountData(Path);
        if (null != accountData && accountData is SandboxMaps sandboxConfigData) {
            SetData(sandboxConfigData);
        }
        
        if (_data == null) {
            SetData(new SandboxMaps());
            Save(false);
        }
    }
    public static void ClearData(string prefix) {
        File.Delete(Abo.FileUtil.InternalPersistentPath + $"/sandbox_maps_{prefix}.data");
    }
    #endregion
    public void UpdateCurrentMap() {
        if (currentMap != null) {
            currentMap.UpdateMap();
        }
    }

    public static void Reload() {
        SetData(null);
        Load();
    }
}
/// <summary>
/// 沙盒地图数据
/// </summary>
[System.Serializable]
public class SandboxMapData {
    static int CurrentVersion = 1;

    public int version;//序列化版本
    public int tileFloor;//地面瓷砖的Index
    public Vector2 startPosition = new Vector2(102, 102);
    public float cameraSize = 7.5f;
    public List<SandboxObjectData> objectDatas = new List<SandboxObjectData>();//物件
    [fsIgnore]
    public bool hasChanged;

    public SandboxMapData() {
        tileFloor = 0;
        version = CurrentVersion;
    }

    public SandboxMapData Clone() {
        var newMap = new SandboxMapData();
        newMap.tileFloor = this.tileFloor;
        newMap.cameraSize = this.cameraSize;
        newMap.startPosition = this.startPosition;
        newMap.hasChanged = false;
        foreach (var objData in this.objectDatas) {
            newMap.objectDatas.Add(objData.Clone());
        }
        return newMap;
    }
    public void Reset() {
        version = CurrentVersion;//序列化版本
        tileFloor = 0;//地面瓷砖的Index
        cameraSize = 7.5f;
        objectDatas = new List<SandboxObjectData>();//物件数据. 
    }

    public void UpdateMap() {
        version = CurrentVersion;
        cameraSize = Camera.main.orthographicSize;
        if (MapManager.Instance is MapManagerSandbox) {
            tileFloor = MapManagerSandbox.Instance.floorTileId;
        }
        if (MapManagerSandbox.Instance.StartPoint) {
            startPosition = MapManagerSandbox.Instance.StartPoint.transform.position;
            Debug.Log("Update Start Poisition: "+startPosition);
        }
        objectDatas = new List<SandboxObjectData>();
        var root = RGGameSceneManager.GetInstance().temp_objects_parent;
        foreach (var obj in root.GetComponentsInChildren<SandboxObject>()) {
            var objDatas = obj.GetSerializedData();
            if (objDatas != null) {
                objectDatas.Add(objDatas);
            }
        }
    }

}

/// <summary>
/// 沙盒箱子数据
/// </summary>
public class SandboxObjectDataUtil {
    #region 序列化版本1
    ///// <summary>
    ///// 第一版的版本数据存储: 共计3字节.
    ///// 第1个字节: 8bit拿物品ID. 支持的物品ID为[0,255]
    ///// 第2个字节: 8bit拿X轴坐标
    ///// 第3个字节: 8bit拿X轴坐标
    ///// </summary>
    //public static byte[] GetObjectDatas_3(int objectId, Vector2Int position) {
    //    byte[] codes = new byte[3];
    //    if (PositionInRange(position)) {
    //        codes[0] = (byte)objectId;
    //        codes[1] = (byte)position.x;
    //        codes[2] = (byte)position.y;
    //    }
    //    return codes;
    //}
    //public static void SetObjectDatas_3(byte[] codes) {
    //    if (codes.Length == 3) {
    //        int objProto = codes[0];
    //        position.x = codes[1];
    //        position.y = codes[2];
    //        int direction = codes[0];
    //    }
    //}
    //public static byte[] GetObjectDatas_4_Dir(int objectId, Vector2Int position, int direction) {
    //    byte[] codes = new byte[4];
    //    if (PositionInRange(position)) {
    //        codes[0] = (byte)objectId;
    //        codes[1] = (byte)position.x;
    //        codes[2] = (byte)position.y;
    //        codes[3] = (byte)direction;
    //    }
    //    return codes;
    //}
    //public static void SetObjectDatas_4_Dir(byte[] codes) {
    //}
    //public static byte[] GetObjectDatas_6_Color(int objectId, Vector2Int position, Color color) {
    //    byte[] codes = new byte[6];
    //    if (PositionInRange(position)) {
    //        codes[0] = (byte)objectId;
    //        codes[1] = (byte)position.x;
    //        codes[2] = (byte)position.y;
    //        Color32 color32 = color;
    //        codes[3] = color32.r;
    //        codes[4] = color32.g;
    //        codes[5] = color32.b;
    //    }
    //    return codes;
    //}
    //public static void SetObjectDatas_6(byte[] codes) {
    //}
    #endregion




    public static bool PositionInRange(Vector2Int position) {
        return position.x >= 0 && position.x <= 102;
    }
    public static byte[] IntToBitConverter(int num) {
        byte[] bytes = BitConverter.GetBytes(num);
        return bytes;
    }
    public static int IntToBitConverter(byte[] bytes) {
        int temp = BitConverter.ToInt32(bytes, 0);
        return temp;
    }
}

[System.Serializable]
public class SandboxObjectData {
    public int id;
    public Dictionary<string, string> datas = new Dictionary<string, string>();
    public SandboxObjectData Clone() {
        SandboxObjectData newData = new SandboxObjectData();
        newData.id = this.id;
        foreach (var pair in datas) {
            newData.datas[pair.Key] = pair.Value;
        }
        return newData;
    }
}

