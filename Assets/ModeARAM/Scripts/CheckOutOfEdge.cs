using DG.Tweening;
using RGScript.Map;
using System;
using UnityEngine;
using Random = UnityEngine.Random;

public class CheckOutOfEdge : MonoBehaviour {
    private void OnCollisionEnter2D(Collision2D col) {
        if (!BattleData.data.IsARAM) {
            return;
        }

        if (col.transform && col.transform.GetComponent<IUnit>() is { } u) {
            u.Object().transform.position = MapManager.Instance.CenterPoint.Vec3();
        }
    }

    private void OnTriggerEnter2D(Collider2D col) {
        if (col.transform && col.transform.GetComponent<ItemPickable>() is { } p) {
            p.transform.DOMove(
                MapManager.Instance.CenterPoint.Vec3() + Quaternion.Euler(0, 0, Random.Range(0, 360f)) * Vector3.right *
                Random.Range(3f, 5f), 1);
        }
    }
}