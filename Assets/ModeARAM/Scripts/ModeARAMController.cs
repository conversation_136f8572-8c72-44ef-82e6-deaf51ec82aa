using cfg.Aram;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Object = UnityEngine.Object;
using ModeDefence;
using ModeSeason.ARAM;

namespace RGScript.Mode.ModeMapLevelController {
    public sealed class ModeARAMController : BaseModeController {

        RGRandom rng;
        Queue<GameObject> existsEnemyBodies = new();

        public ModeARAMController(int seed) : base(seed) {
            OnInit();
            rng = new RGRandom(seed);
        }

        protected override void OnInit() {
            base.OnInit();
            SimpleEventManager.AddEventListener<AfterEnemyStart>(AfterEnemyCreate);
            SimpleEventManager.AddEventListener<CreateEnemyEvent>(OnCreateEnemy);
        }

        public override void OnDestroy() {
            base.OnDestroy();
            SimpleEventManager.RemoveListener<AfterEnemyStart>(AfterEnemyCreate);
            SimpleEventManager.RemoveListener<CreateEnemyEvent>(OnCreateEnemy);
            existsEnemyBodies.Clear();
        }

        private void OnCreateEnemy(CreateEnemyEvent evData) {
            if (evData.enemy is RGEBossController boss) {
                if (BattleData.data.levelIndex < 15) {
                    boss.PlayFightBGM = false;
                } else {
                    boss.onShowBossInfo = () => {
                        var intro = ResourcesUtil.Load<AudioClip>("RGSound/bgm/boss_aram_intro.mp3");
                        var loop = ResourcesUtil.Load<AudioClip>("RGSound/bgm/boss_aram_bgm.mp3");
                        RGMusicManager.Inst.PlayBGMWithIntro(intro, loop, 0, RGMusicManager.emBGM.Boss);
                    };
                }
            }
        }

        private void AfterEnemyCreate(AfterEnemyStart e) {
            if (e == null || e.enemy == null) {
                return;
            }

            var enemy = e.enemy;
            if (ARAMConfig.TryGetEnemyProperty(enemy, out var properties)) {
                enemy.attributeProxy.max_hp = ((int)(properties.Hp * MapManagerARAM.Map.LevelConfig.EHpFactor));
                if (enemy is LegendaryPlant && BattleData.data.HasBuff(emBuff.ToughPlant)) {
                    var param = RGScript.Battle.BuffStackCfgToughPlant.GetParam(2f, 1f);
                    enemy.attributeProxy.max_hp = Mathf.FloorToInt(enemy.attributeProxy.max_hp * (1 + param.hpFactor));
                }
                enemy.attributeProxy.hp = enemy.attributeProxy.max_hp;
                foreach (var weapon in enemy.GetComponentsInChildren<RGEWeapon>()) {
                    weapon.atk = Mathf.FloorToInt(properties.Damage * MapManagerARAM.Map.LevelConfig.EAtkFactor);
                }

                enemy.attribute.speed = properties.Speed;
                enemy.shoot_cd = properties.AttackCooldown;
                enemy.MoveType = properties.MoveType;

                enemy.scout_rate = properties.ScoutRate;
                enemy.attributeProxy.skillDamageFactor.SetOriginalValue(properties.DamagFactor);
            } else {
                Debug.LogError($"Enemy config not found:{enemy.enemy_id}", enemy);
            }

            if (BattleData.data.levelIndex >= 15 && enemy.enemy_id.Contains("werewolf")) {
                ARAMModeData.Data.hasWerewolfBoss = true;
            }

            enemy.lowPriorityForPetTarget = true;
            enemy.findTargetRange = 999;
            enemy.ignoreObstacleWhenTargeting = true;

            if (enemy is RGEBossController boss) {
                if (boss.boss_info != null) {
                    boss.boss_info.HideHpBar();
                }

                if (BattleData.data.CompareFactor(emBattleFactor.DoubleBoss)) {
                    boss.role_attribute.max_hp = (int)(boss.role_attribute.max_hp * 0.75f);
                    boss.role_attribute.hp = boss.role_attribute.max_hp;
                    boss.shoot_cd *= 1.33f;
                }

                if (boss is BossBloodyWerewolf werewolf && BattleData.data.levelIndex == ARAMConfig.LevelCount() - 1) {
                    werewolf.canGrowBig = true;
                }
            }

            enemy.reward_rate = 0;
            enemy.can_award = false;
            enemy.ai_level = 0;

            if (ARAMModeData.Data.IsYaoQiModeEnabled() && enemy is not LegendaryPlant) {
                var yaoQiEnemyEffectCfg = ARAMYaoQiEffects.GetYapQiEnemyEffectCfg(ARAMModeData.Data.yaoQiLevel);
                if (yaoQiEnemyEffectCfg != null && rng.Range(0, 1f) < yaoQiEnemyEffectCfg.EnhanceProbability / 100f) {
                    EnhanceEnemyInYaoqiMode(enemy, rng);
                }
            }
        }

        public static void EnhanceEnemyInYaoqiMode(RGEController enemy, RGRandom rng) {
            if (enemy == null || enemy.dead || enemy is RGEBossController || enemy.transform.Find("yaoqi") != null) {
                return;
            }

            var yaoQiEnemyEffectCfg = ARAMYaoQiEffects.GetYapQiEnemyEffectCfg(Mathf.Max(1, ARAMModeData.Data.yaoQiLevel));
            enemy.attributeProxy.ChangeAttribute(emRoleAttribute.MoveSpeed, yaoQiEnemyEffectCfg.Speed / 100f);
            enemy.attributeProxy.atk_damage_factor += yaoQiEnemyEffectCfg.Damage / 100f;
            if (yaoQiEnemyEffectCfg.RandomGene > 0 && enemy.enemy_id != "e_aram_swampTentacle") {
                var geneCtrl = enemy.gameObject.AddComponent<DefenceEnemyGene>();
                geneCtrl.gene = (DefenceEnemyGene.EnemyGene)rng.Range(1, (int)DefenceEnemyGene.EnemyGene.count);
            }
            var fxObj = GameObject.Instantiate(ResourcesUtil.Load<GameObject>("ModeARAM/Prefabs/aram2_enemy_yaoqi.prefab"), enemy.transform);
            fxObj.transform.localPosition = Vector3.up * 0.6f;
            fxObj.transform.localScale = Vector3.one;
            fxObj.name = "yaoqi";
        }

        public override void OnEnemyDead(RGEController enemy) {
            try {
                if (enemy.HpBar != null) {
                    Object.Destroy(enemy.HpBar.gameObject);
                }

                if (enemy.isBoss) {
                    // Boss 死亡时尝试修正位置，避免掉落物无法拾取
                    var room = RGRoomX.FindNearestRoom(enemy.transform.position);
                    var pos = room.FindNearestPositionInRoom(enemy.transform.position);
                    enemy.transform.position = MapManagerARAM.Map.GetValidPosition(pos);
                }

                if (enemy.isBoss && enemy.DeadHurtInfo.Source != null &&
                    enemy.DeadHurtInfo.Source.GetComponent<RGController>() is var rgController && rgController != null) {
                    if (BattleData.data.levelIndex >= 15) {
                        BattleData.data.aramMode.hasBeatFinalBoss = true;
                    }
                    BattleData.data.aramMode.beatNormalBossIds.Add(enemy.enemy_id);

                    if (enemy.enemy_id.Contains("werewolf") && (enemy.DeadHurtInfo.ElementalType | ElementalType.Fire) > 0) {
                        var statValues = ARAMModeData.Data.GetStatValues();
                        statValues["KillWerewolfWithFire"] = statValues.GetValueOrDefault("KillWerewolfWithFire", 0) + 1;
                    }
                }

                if (ARAMModeData.Data.IsYaoQiModeEnabled()) {
                    var yaoqiFxObj = enemy.transform.Find("yaoqi");
                    if (yaoqiFxObj != null) {
                        GameObject.Destroy(yaoqiFxObj.gameObject);
                    }
                }

                if (!enemy.UnnaturalDead && !enemy.GetComponent<DeadBodyController>()) {
                    if (enemy.temp_enemy || enemy.HasTag(emEnemyTag.Copy)) {
                        if (rgRandom.Range(0, 100) >= ARAMConfig.Config.splitOrTempEnemyDropRate) {
                            return;
                        }
                    }

                    if (enemy is LegendaryPlant) {
                        var statValues = ARAMModeData.Data.GetStatValues();
                        statValues["DestroyPlant"] = statValues.GetValueOrDefault("DestroyPlant", 0) + 1;
                    }

                    if (ARAMConfig.TryGetEnemyProperty(enemy, out var properties)) {
                        var dropItems = CreateRandomReward(properties, rgRandom);
                        if (dropItems != null) {
                            foreach (var dropItem in dropItems.ItemList) {
                                switch (dropItem.Item) {
                                    case "AramCoin": {
                                            var coinCount = 0;
                                            if (rgRandom.Range(0, 100) < ARAMConfig.Config.enemyDropRate) {
                                                coinCount = dropItem.ItemQuantity;
                                            }

                                            if (enemy is LegendaryPlant && BattleData.data.HasBuff(emBuff.ToughPlant)) {
                                                var param = RGScript.Battle.BuffStackCfgToughPlant.GetParam(2f, 1f);
                                                coinCount = Mathf.FloorToInt(coinCount * (1 + param.coinFactor));
                                            }

                                            ARAMConfig.DropCoin(coinCount, enemy.transform.position, rgRandom);
                                            break;
                                        }
                                    default:
                                        if (!string.IsNullOrWhiteSpace(dropItem.Item)) {
                                            var list = new List<GameObject>();
                                            for (var i = 0; i < dropItem.ItemQuantity; ++i) {
                                                var item = ResourcesUtil.CreateItem(dropItem.Item, null);
                                                item.transform.position = enemy.transform.position;
                                                list.Add(item);
                                            }

                                            GameUtil.DropScatterThings(enemy.transform.position, null,
                                                new Vector2(2, 2), list.ToArray());
                                        }

                                        break;
                                }
                            }
                        } else {
                            // 没有配置则默认掉落豆子
                            var coinCount = 0;
                            if (rgRandom.Range(0, 100) < ARAMConfig.Config.enemyDropRate) {
                                coinCount = rgRandom.Range(ARAMConfig.Config.defaultDropCoinMin,
                                    ARAMConfig.Config.defaultDropCoinMax + 1);
                            }

                            ARAMConfig.DropCoin(coinCount, enemy.transform.position, rgRandom);
                        }
                    }

                    if (enemy is not RGEBossController) {
                        existsEnemyBodies.Enqueue(enemy.gameObject);
                        if (existsEnemyBodies.Count > 24) {
                            var obj = existsEnemyBodies.Dequeue();
                            if (obj != null) {
                                GameObject.Destroy(obj);
                            }
                        }
                    }
                }
            }
            catch (System.Exception e) {
                Debug.LogError(e.Message);
                Debug.LogError(e.StackTrace);
            }
        }

        DropItemList CreateRandomReward(AramEnemyProperties property, RGRandom rng) {
            return property.DropItem.GetRandomWeightObject(property.DropItem.Select(_ => _.ItemWeight), rng);
        }

        public override void OnNextLevelServer() {
            base.OnNextLevelServer();
        }
    }
}
