using System;
using UnityEngine;

namespace NarrativeDialog.Scripts {
    public class SpeakerDecorate : MonoBehaviour {
        protected object ExtraData;
        public Transform imagePosTf;
        public void SetExtraData(object extraData) {
            this.ExtraData = extraData;
        }

        protected virtual void OnInit() {
            
        }

        private void Start() {
            OnInit();
        }
    }
}