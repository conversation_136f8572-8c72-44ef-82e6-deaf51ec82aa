using I2.Loc;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class TroopUpgrade : RGStateItem {

    protected override void OnItemTriggerState(RGController controller, int state) {
        base.OnItemTriggerState(controller, state);
    }

    protected override void ShowStateTab(RGController controller, int state) {
        var talkText = ScriptLocalization.Get(talkStates[state], "提升领导力");
        var priceText = "<color=#00ff00ff>" + GetItemValue() + "</color>";
        var dialog = LanguageUtil.PriceRTL ? priceText + " " + talkText : talkText + " " + priceText;
        ShowTap(dialog, false);
    }

    protected override void OnItemTriggerSuccess(RGController controller, string extraInfo = "") {
        base.OnItemTriggerSuccess(controller, extraInfo);
        BattleData.data.troopMode.flagLevel++;
    }

    protected override void OnItemTriggerFail(RGController controller) {
        base.OnItemTriggerFail(controller);
        ShowTalk(controller.transform, I2.Loc.ScriptLocalization.Get("token/lack_petcoin", "猫币不足"));
        base.OnItemTriggerSuccess(controller);
        triggerState = 0;
    }
    int flagLevel => BattleData.data.troopMode.flagLevel;
    public override int GetItemValue() {
        return 2 + flagLevel * 2;
    }

    public override string GetItemName() {
        return $"{base.GetItemName()} {flagLevel}";
    }


}
