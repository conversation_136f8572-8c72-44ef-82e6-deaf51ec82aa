using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 宠物角色的手
/// </summary>
public class TroopHand : RGHand {
    public override RGWeapon[] SetUpWeapon(List<BattleData.WeaponInBattle> weapons) {
        var ret = base.SetUpWeapon(weapons);
        foreach (var w in GetAllWeapons()) {
            w.<PERSON>();
        }

        if (front_weapon) {
            front_weapon.ShowWeapon();
        }

        return ret;
    }

    public override void SetWeaponBack(RGWeapon w, bool temporarily, bool asFirstSibling) {
        base.SetWeaponBack(w, temporarily, asFirstSibling);
        if (w) {
            w.HideWeapon();
        }
    }

    public override void PickUpItem(Transform new_weapon_tf, bool fusion = false, bool showText = true,
        bool force = false) {
        base.PickUpItem(new_weapon_tf, fusion, showText, force);
        if (new_weapon_tf && new_weapon_tf.GetComponent<RGWeapon>()) {
            new_weapon_tf.GetComponent<RGWeapon>().ShowWeapon();
        }
    }

    public override GameObject AtkCut() {
        if (Time.timeScale != 0 && controller) {
            float swordScale = 1f;
            var proto = ResourcesUtil.Load<GameObject>("RGPrefab/Bullet/SwordBullet/sword_bit.prefab");
            var sword = BulletFactory.TakeBullet(
                new BulletInfo().SetUp(proto, controller.gameObject, 0, transform.position, 0, controller.camp)
                    .SetBulletSize(swordScale),
                new DamageInfo().SetUp(proto, controller.attribute.atk, controller.attribute.critical, 2,
                    controller.camp)
            );
            sword.transform.localScale = new Vector3(controller.facing, 1, 1);
            RGMusicManager.GetInstance().PlayEffect(3);
            controller.RoleAttackEvent(true);
            return sword;
        }

        return null;
    }


    public override bool NeedLock() {
        return false;
    }
}