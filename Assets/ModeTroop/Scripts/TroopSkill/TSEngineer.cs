using RGScript.Character;
using SoulKnight.Runtime.Skill;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class TSEngineer : RGTroopSkill {
    public override void RoleSkill() {
        base.RoleSkill();
        if (isStrength) {
            CreateLaserBattery();
            ShowMasterCDAnim();
        } else {
            CreateBattery();
        }

        RGMusicManager.GetInstance().PlayEffect(skillInfo.audioClip);
        RoleSkillEnd();
    }

    void CreateBattery() {
        GameObject temp_obj = Instantiate(skillInfo.SkillObject) as GameObject;
        RGBatteryController batteryObject = temp_obj.GetComponent<RGBatteryController>();
        batteryObject.SetMaster(transform);
        temp_obj.transform.position = transform.position;
        temp_obj.SetActive(true);
        batteryObject.intensify = true;
        batteryObject.bulletCount = batteryObject.intensify ? 1 : 0;
        batteryObject.role_attribute.hp += 10;
        batteryObject.role_attribute.MaxHpValue.AddAdditionValue(RoleAttributeValueEnum.MaxHpUp, 10);

        foreach (var skinChanger in batteryObject.GetComponentsInChildren<DerivativeSkinChanger>(true)) {
            Destroy(skinChanger);
        }
        
    }

    void CreateLaserBattery() {
        ShowMasterCDAnim();
        RGMusicManager.Inst.PlayEffect(skillInfo.skinSkillCfg.data.GetSkinAudioClip(skinIndex));
        var skinData = skillInfo.skinSkillCfg.data.GetSkinData<NormalSkinData>(skinIndex);
        var prefab = skillInfo.SkillObject;
        if (skinData != null) {
            prefab = skinData.objectData.GetData();
        }
        var batteryObject = Instantiate(
            prefab,
            transform.position,
            Quaternion.identity,
            RGGameSceneManager.Inst.temp_objects_parent
        ).GetComponent<RGInterceptBatteryController>();
        batteryObject.Intensify = true;
        // batteryObject.SetInterceptInterval(0.2f);
        batteryObject.SetDamage(8);
        batteryObject.role_attribute.hp += 15;
        batteryObject.role_attribute.MaxHpValue.AddAdditionValue(RoleAttributeValueEnum.MaxHpUp, 15);
        // batteryObject.AddInterceptCount(1);
        
        foreach (var skinChanger in batteryObject.GetComponentsInChildren<DerivativeSkinChanger>(true)) {
            Destroy(skinChanger);
        }
        
    }

    private Animator cdAnim;

    void ShowMasterCDAnim() {
        Invoke("EndShowInSkill", skillInfo.FinalCooldown);
        GetComponent<Animator>().SetBool("in_skill", true);
        if (!cdAnim) {
            GameObject effect = Instantiate(
                ResourcesUtil.Load("RGPrefab/Effect/cd_clock.prefab")) as GameObject;
            Transform head_effect_tf = transform.Find("img/head_effect");
            effect.transform.SetParent(head_effect_tf);
            //处理变大变小因子
            effect.transform.localPosition = Vector3.up * 1.6f * (transform.Find("img/body").localScale.y - 1f);
            effect.transform.localScale = Vector3.one;
            cdAnim = effect.GetComponent<Animator>();
        }
        cdAnim.speed = Mathf.Max(0.01f, 1f / skillInfo.FinalCooldown);
        cdAnim.gameObject.SetActive(true);
        cdAnim.Play("loading", 0, 0);
    }

    public void EndShowInSkill() {
        GetComponent<Animator>().SetBool("in_skill", false);
    }
}