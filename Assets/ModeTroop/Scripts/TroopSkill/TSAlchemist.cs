using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class TSAlchemist : RGTroopSkill {
    private int bulletIndex;

    public override void RoleSkill() {
        base.RoleSkill();
        skillCasting = true;
        StartCoroutine(ThrowingBullet());
    }

    IEnumerator ThrowingBullet() {
        for (int i = 0; i < (isStrength ? 2 : 1); i++) {
            int multiCount = 1;
            var bulletGo = skillInfo.SkillObjectData[bulletIndex].GetData();
            ThrowBullet(bulletGo, multiCount);
            bulletIndex = (bulletIndex + 1) % skillInfo.SkillObjectData.Length;
            RGMusicManager.GetInstance().PlayEffect(skillInfo.audioClip);
            yield return new WaitForSeconds(0.6f);
        }
        RoleSkillEnd();
    }

    private void ThrowBullet(GameObject bullet, int multiCount) {
        int speed = 18;
        int damage = 2;
        for (int i = -multiCount; i <= multiCount; i++) {
            float angle = controller.facing > 0
                ? controller.FixedAngle + i * 20
                : 180 - controller.FixedAngle + i * 20;
            var bulletInfo = new BulletInfo().SetUp(bullet, gameObject, speed, controller.hand.transform.position,
                angle, controller.camp);
            var damageInfo = new DamageInfo().SetUp(bullet, damage, 0, 0, controller.camp);
            var tmp_bottle = BulletFactory.TakeBullet(bulletInfo, damageInfo);
            // var explodeEffectTrigger = tmp_bottle.GetComponentInChildren<ExplodeEffectTrigger>();
            TrailRenderer trail = tmp_bottle.GetComponent<TrailRenderer>();
            if (trail) { trail.Clear(); }
        }
    }
}