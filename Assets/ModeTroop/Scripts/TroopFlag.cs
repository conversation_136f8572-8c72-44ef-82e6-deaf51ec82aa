using I2.Loc;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class TroopFlag : RGItem {
    private static readonly int maxFlagLevel = 4;

    protected override void Start() {
        base.Start();
        RefreshSprites();
    }

    protected override void OnTriggerEnter2D(Collider2D other) {
        base.OnTriggerEnter2D(other);
        if (other.gameObject.CompareTag("Body_P") && other.GetComponent<RGController>().IsLocalPlayer()) {
            int limit = BattleData.data.troopMode.MaxMercenariesCount;
            UICanvas.Inst.ShowItemInfo(string.Format(ScriptLocalization.Get("troop/flag_desc"), limit));
        }
    }

    protected override void OnTriggerExit2D(Collider2D other) {
        base.OnTriggerExit2D(other);
        if (other.gameObject.CompareTag("Body_P") && other.GetComponent<RGController>().IsLocalPlayer()) {
            if (UICanvas.Inst != null) {
                UICanvas.Inst.HideItemInfo();
            }
        }
    }

    protected override void OnItemTriggerSuccess(RGController controller, string extraInfo = "") {
        base.OnItemTriggerSuccess(controller, extraInfo);
        if (controller.IsLocalPlayer()) {
            BattleData.data.tokenCoin -= GetItemValue();
            BattleData.data.troopMode.flagLevel++;
            UICanvas.Inst.UpdateTokenCoin();
            RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Buy);
        }
        var showEffect = Instantiate<GameObject>(PrefabManager.GetPrefab(PrefabName.effect_show_up), transform.position + Vector3.down * .8f,
            Quaternion.identity, RGGameSceneManager.Inst.temp_objects_parent);
        showEffect.transform.localScale = Vector3.one * 1.5f;
        showEffect.GetComponent<Animator>().speed = 1.33f;
        transform.GetComponentInParent<TroopStartRoom>().CompleteMercenaries();
        RefreshSprites();
    }

    protected override void OnItemTriggerFail(RGController controller) {
        base.OnItemTriggerFail(controller);
        if (triggerState == 0 && BattleData.data.troopMode.flagLevel < maxFlagLevel) {
            var talkText = ScriptLocalization.Get("troop/flag_talk", "升级旗帜");
            var priceText = "<color=#00ff00ff>" + GetItemValue() + "</color>";
            var dialog = LanguageUtil.PriceRTL ? priceText + " " + talkText : talkText + " " + priceText;
            ShowTap(dialog, false);
            triggerState++;
        } else {
            if (BattleData.data.troopMode.flagLevel >= maxFlagLevel) {
                ShowTalk(controller.transform, I2.Loc.ScriptLocalization.Get("troop/max_level", "已到最高级"));
            } else if (BattleData.data.tokenCoin < GetItemValue()) {
                ShowTalk(controller.transform, I2.Loc.ScriptLocalization.Get("token/lack_petcoin", "猫币不足"));
            }
            base.OnItemTriggerSuccess(controller);
            triggerState = 0;
            if (controller.IsLocalPlayer()) {
                UICanvas.Inst.HideItemInfo();
            }
        }
    }


    protected override bool Triggerable(RGController controller) {
        if (triggerState < 1) {
            return false;
        } else if (BattleData.data.tokenCoin < GetItemValue()) {
            return false;
        } else if (BattleData.data.troopMode.flagLevel >= maxFlagLevel) {
            return false;
        }
        return true;
    }

    private void RefreshSprites() {
        foreach (var upgrader in GetComponentsInChildren<UpgradeSprites>()) {
            upgrader.SetSprite(Season.Troop, BattleData.data.troopMode.flagLevel);
        }
    }

    public override int GetItemValue() {
        return 1 + BattleData.data.troopMode.flagLevel * 2;
    }

}
