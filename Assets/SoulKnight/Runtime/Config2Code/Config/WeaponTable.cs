using SoulKnight.Runtime.Common.Data;
using SoulKnight.Runtime.Weapon;
using System.Collections.Generic;

// ReSharper disable UnassignedField.Global
// ReSharper disable CollectionNeverUpdated.Global
// ReSharper disable MemberCanBePrivate.Global
// ReSharper disable once MemberHidesStaticFromOuterClass
namespace SoulKnight.Runtime.Config2Code.Config {
    public static class WeaponTable {
        public class WeaponData {
            public string Key;
            public string Path;
            public string OriginalWeapon;
            public bool DisableUnlockByPick;
            public bool ShowNum;
            public int Price;
            public string DropAdd;
            public List<ChooseItemWithNumData> Materials;
            public List<FusionConfig> FusionList;
            public List<FusionRangeConfig> FusionRangeList;
            public List<TalentConfig> TalentList;
        }

        private static readonly CsvLoader<WeaponData> CsvLoader = new();
        public static bool Load() {
            return CsvLoader.Load("SoulKnight/Runtime/Config2Code/EncryptedCsv/weapons.csv");
        }

        public static IReadOnlyDictionary<string, WeaponData> Data => CsvLoader.GetData();

        public static WeaponData GetWeaponData(string key) {
            return Data.ContainsKey(key) ? Data[key] : null;
        }
    }
}
