using System.Collections.Generic;

// ReSharper disable UnassignedField.Global
// ReSharper disable CollectionNeverUpdated.Global
// ReSharper disable MemberCanBePrivate.Global
namespace SoulKnight.Runtime.Config2Code.Config {
    public static class AchievementTable {
        public class AchievementData {
            public string Key;
            public string DataStr;
        }

        private static readonly CsvLoader<AchievementData> CsvLoader = new();
        public static bool Load() {
            return CsvLoader.Load("SoulKnight/Runtime/Config2Code/EncryptedCsv/achievements.csv");
        }

        public static IReadOnlyDictionary<string, AchievementData> Data => CsvLoader.GetData();

        public static AchievementData GetAchievementData(string key) {
            return Data.ContainsKey(key) ? Data[key] : null;
        }
    }
}
