using System.Collections.Generic;

// ReSharper disable Unassigned<PERSON>ield.Global
// ReSharper disable CollectionNeverUpdated.Global
// ReSharper disable MemberCanBePrivate.Global
// ReSharper disable once MemberHidesStaticFromOuterClass
namespace SoulKnight.Runtime.Config2Code.Config {
    public static class RandomObjectsTable {
        public class RandomObjectsData {
            public string Key;
            public string Data;
        }

        private static readonly CsvLoader<RandomObjectsData> CsvLoader = new();
        public static bool Load() {
            return CsvLoader.Load("SoulKnight/Runtime/Config2Code/EncryptedCsv/random_objects.csv");
        }

        public static IReadOnlyDictionary<string, RandomObjectsData> Data => CsvLoader.GetData();

        public static RandomObjectsData GetRandomObjectData(string key) {
            return Data.ContainsKey(key) ? Data[key] : null;
        }
    }
}
