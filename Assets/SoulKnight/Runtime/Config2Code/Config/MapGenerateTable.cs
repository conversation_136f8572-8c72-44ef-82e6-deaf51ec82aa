using System.Collections.Generic;

// ReSharper disable UnassignedField.Global
// ReSharper disable CollectionNeverUpdated.Global
// ReSharper disable MemberCanBePrivate.Global
namespace SoulKnight.Runtime.Config2Code.Config {
    public static class MapGenerateTable {
        public class MapGenerateData {
            public string LevelName;
            public int MiniStep;
            public string RoomSizeRange;
            public string CorridorRange;
            public string WallSize;
            public string DoorSize;
            public string DoorPrefabPaths;
            public string OtherPrefabPaths;
            public string RoomSubBuilderClassNameList;
            public string CorridorSubBuilderClassNameList;
            public string RoomBasePrefabPath;
            public string CorridorBasePrefabPath;
            public string SpriteAtlasPaths;
        }

        private static readonly CsvLoader<MapGenerateData> CsvLoader = new();
        public static bool Load() {
            return CsvLoader.Load("SoulKnight/Runtime/Config2Code/EncryptedCsv/level_generate_config.csv");
        }

        public static IReadOnlyDictionary<string, MapGenerateData> Data => CsvLoader.GetData();

        public static MapGenerateData GetMapGenerateData(string key) {
            return Data.ContainsKey(key) ? Data[key] : null;
        }
    }
}
