using UnityEngine;

namespace SoulKnight.Runtime.Tool.FrameBatch {
    public class FrameBatchSplitBulletData {
        public RGWeapon Weapon;
        public GameObject Bullet;
        public BulletSplitConfig SplitConfig;
    }
    
    public class FrameBatchSplitBullet : FrameBatchProcessor<FrameBatchSplitBulletData> {
        public FrameBatchSplitBullet(float maxMs<PERSON>er<PERSON>rame, FrameBatchEnum frameBatchEnum) :
            base(maxMsPer<PERSON>rame, frameBatchEnum) {
        }
        
        protected override void ProcessObject(FrameBatchSplitBulletData data) {
            if (!data.Weapon || !data.Bullet) {
                return;
            }
            
            DefaultWeaponBulletSpliter.CreateSplitBullet(data.Weapon, data.Bullet, data.SplitConfig);
        }
    }
}