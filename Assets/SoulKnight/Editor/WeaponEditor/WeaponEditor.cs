using Newtonsoft.Json;
using RGScript.Util;
using Sirenix.OdinInspector.Editor;
using Sirenix.Utilities;
using Sirenix.Utilities.Editor;
using SoulKnight.Editor.Common;
using SoulKnight.Editor.Common.Cache;
using SoulKnight.Editor.Common.Data;
using SoulKnight.Editor.Common.Odin;
using SoulKnight.Editor.Config2Code;
using SoulKnight.Runtime.Weapon;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;

namespace SoulKnight.Editor.WeaponEditor {
    public class WeaponEditor : PreviewOdinMenuEditorWindow<WeaponConfig> {
        protected override string SaveFilePath => "SoulKnight/Editor/Config2Code/Config/weapons.csv";
        
        [MenuItem("SoulKnight/Editors/Weapon Editor", false, 13)]
        private static void Open() {
            var window = GetWindow<WeaponEditor>();
            window.position = GUIHelper.GetEditorWindowRect().AlignCenter(800, 600);
        }

        private void OnEditorReloadConfig(EditorReloadConfig message) {
            if (message.Config is not WeaponConfig config) {
                return;
            }

            var weaponName = config.Weapon.name;
            var menuItem = MenuTree.FindMenuItem(weaponName);
            if (menuItem == null) {
                return;
            }

            LoadCsvData(weaponName, config);
        }
        
        private void RegisterEvents() {
            SimpleEventManager.AddEventListener<EditorReloadConfig>(OnEditorReloadConfig);
        }
        
        private void UnregisterEvents() {
            SimpleEventManager.RemoveListener<EditorReloadConfig>(OnEditorReloadConfig);
        }

        protected override void OnDestroy() {
            base.OnDestroy();
            UnregisterEvents();
        }

        private static bool FilterFusion(OdinMenuItem menuItem) {
            if (menuItem.Value is not WeaponConfig weaponConfig) {
                return false;
            }

            return weaponConfig.FusionList.Count > 0;
        }

        private static emBuff ParseToEmBuff(string text) {
            if (!Enum.TryParse<emBuff>(text, out var ret)) {
                ret = emBuff.None;
            }

            return ret;
        }
        
        private static bool FilterTalent(OdinMenuItem menuItem, emBuff buff) {
            return menuItem.Value is WeaponConfig weaponConfig &&
                   weaponConfig.TalentList.Any(talentConfig => talentConfig.talentName == buff.ToString());
        }
        
        protected override void OnBuild(OdinMenuTree tree) {
            RegisterEvents();
            tree.Config.SearchFunction = item => {
                var searchTerm = tree.Config.SearchTerm;
                if (searchTerm == "fusion") {
                    return FilterFusion(item);
                }

                var emBuff = ParseToEmBuff(searchTerm);
                if (emBuff != emBuff.None) {
                    return FilterTalent(item, emBuff);
                }
                
                if (item.Name.Contains(searchTerm)) {
                    return true;
                }

                return item.Value is WeaponConfig weaponConfig && weaponConfig.ChineseName.Contains(searchTerm);
            };
        }
        
        private readonly List<string> _allWeaponDirs = new() {
            "Assets/RGPrefab/Weapon",
            "Assets/Skin/Character/Paladin",
            "Assets/Skin/Character/Alchemist",
            "Assets/Skin/Character/Officer",
            "Assets/Skin/Character/Joker",
            "Assets/ModeSandbox/Prefabs/Weapons"
        };
        
        private readonly List<string> _exclusionDirs = new() {
            // ReSharper disable once StringLiteralTypo
            "Assets/RGPrefab/Weapon/purebossrush_init_weapon"
        };

        private void LoadCsvData(string weaponName, WeaponConfig weaponConfig) {
            if (!CsvData.TryGetValue(weaponName, out var config)) {
                return;
            }

            weaponConfig.DisableUnlockByPick = bool.Parse(config["DisableUnlockByPick"]);
            weaponConfig.ShowNum = bool.Parse(config["ShowNum"]);
            weaponConfig.Price = int.Parse(config["Price"]);
            weaponConfig.DropAdd = config["DropAdd"];
            weaponConfig.Materials =
                JsonConvert.DeserializeObject<List<ChooseMaterialWithNumData>>(config["Materials"]);
            weaponConfig.FusionList = JsonConvert.DeserializeObject<List<FusionConfig>>(config["FusionList"]);
            weaponConfig.FusionRangeList =
                JsonConvert.DeserializeObject<List<FusionRangeConfig>>(config["FusionRangeList"]);
            weaponConfig.TalentList =
                JsonConvert.DeserializeObject<List<TalentConfig>>(config["TalentList"]);
            foreach (var talentConfig in weaponConfig.TalentList) {
                var keyValueList = TalentConfig.GetTalentKeyValueList(talentConfig.talentChineseName);
                var keyValueListTemp = talentConfig.keyValueList;
                talentConfig.keyValueList = keyValueList;
                foreach (var keyValue in keyValueList) {
                    foreach (var keyValueTemp in keyValueListTemp.Where(
                                 keyValueTemp => keyValue.key == keyValueTemp.key)) {
                        keyValue.value = keyValueTemp.value;
                    }
                }
            }
        }

        private static bool HasOriginalWeapon(RGWeapon rgWeapon) {
            if (!rgWeapon) {
                return false;
            }

            if (!rgWeapon.weapon_tags.Contains(emWeaponTag.InitWeapon)) {
                return false;
            }

            var weaponName = rgWeapon.name;
            // ReSharper disable once ConvertIfStatementToReturnStatement
            if (!weaponName.Contains("xx")) {
                return false;
            }

            return true;
        }

        private static string GetOriginalWeaponName(RGWeapon rgWeapon) {
            if (!HasOriginalWeapon(rgWeapon)) {
                return string.Empty;
            }

            var weaponName = rgWeapon.name;
            return weaponName[..(weaponName.IndexOf("xx", StringComparison.Ordinal) + 1)];
        }

        private static void LoadWithOriginalWeapon(OdinMenuTree tree) {
            foreach (var menuItem in tree.EnumerateTree()) {
                var weaponConfig = (WeaponConfig)menuItem.Value;
                if (!weaponConfig.Weapon) {
                    continue;
                }

                var originalWeaponName = GetOriginalWeaponName(weaponConfig.Weapon.GetComponent<RGWeapon>());
                originalWeaponName = string.IsNullOrEmpty(originalWeaponName)
                    ? GetWeaponSkillOriginName(weaponConfig.Weapon.name)
                    : originalWeaponName;
                if (string.IsNullOrEmpty(originalWeaponName)) {
                    continue;
                }

                var originalWeaponConfig = (WeaponConfig)tree.FindMenuItem(originalWeaponName)?.Value;
                if (originalWeaponConfig == null) {
                    continue;
                }

                weaponConfig.DisableUnlockByPick = originalWeaponConfig.DisableUnlockByPick;
                weaponConfig.ShowNum = originalWeaponConfig.ShowNum;
                weaponConfig.Price = originalWeaponConfig.Price;
                weaponConfig.DropAdd = originalWeaponConfig.DropAdd;
                weaponConfig.OriginalWeapon = originalWeaponConfig.Weapon;
                weaponConfig.Materials = originalWeaponConfig.Materials;
                
                var fusionList = new List<FusionConfig>();
                foreach (var originalConfig in originalWeaponConfig.FusionList) {
                    var fusionConfig = new FusionConfig();
                    fusionList.Add(fusionConfig);
                    
                    // ReSharper disable once ForeachCanBePartlyConvertedToQueryUsingAnotherGetEnumerator
                    foreach (var chooseWeaponData in originalConfig.sourceWeapons) {
                        var key = chooseWeaponData.key;
                        if (key == originalWeaponName) {
                            key = weaponConfig.Weapon.name;
                        }
                        fusionConfig.sourceWeapons.Add(new ChooseWeaponData(key));
                    }

                    fusionConfig.targetWeapon = originalConfig.targetWeapon;
                }

                weaponConfig.FusionList = fusionList;
                weaponConfig.TalentList = originalWeaponConfig.TalentList;
            }
        }

        private static string GetWeaponSkillOriginName(string weaponName) {
            var regex = new Regex(@"^(weapon_skill_[a-zA-Z0-9_]+)_s\d+$");
            var match = regex.Match(weaponName);
            return match.Success ? match.Groups[1].Value : string.Empty;
        }

        protected override void OnLoad(OdinMenuTree tree) {
            var i = 1;
            foreach (var dir in _allWeaponDirs) {
                var files = Directory.GetFiles(dir, "*.prefab", SearchOption.AllDirectories);
                foreach (var file in files) {
                    var directoryName = Path.GetDirectoryName(file)?.Replace('\\', '/');
                    if (_exclusionDirs.Contains(directoryName)) {
                        continue;
                    }

                    var gameObject = AssetDatabase.LoadAssetAtPath<GameObject>(file);
                    var rgWeapon = gameObject.GetComponent<RGWeapon>();
                    if (!rgWeapon) {
                        var fishRod = gameObject.GetComponent<FishRod>();
                        if (!fishRod) {
                            continue;
                        }
                    }

                    var weaponName = gameObject.name;
                    if (tree.FindMenuItem(weaponName) != null) {
                        Debug.LogError($"Repeat weapon name {weaponName}");
                        continue;
                    }

                    var weaponSkillOriginName = GetWeaponSkillOriginName(weaponName);
                    var chineseName = NameUtil.GetWeaponNameNoLog(
                        string.IsNullOrEmpty(weaponSkillOriginName) ? weaponName : weaponSkillOriginName);
                    var weaponConfig = new WeaponConfig {
                        Weapon = gameObject,
                        ChineseName = chineseName,
                        Path = file.ToPathWithoutAssets()
                    };
                    
                    if (!HasOriginalWeapon(rgWeapon) || string.IsNullOrEmpty(weaponSkillOriginName)) {
                        // Not init weapon and weapon skill
                        LoadCsvData(weaponName, weaponConfig);
                    }
                    
                    weaponConfig.LoadPreview(i++);
                    tree.AddObjectAtPath(weaponName, weaponConfig);
                }
            }

            LoadWithOriginalWeapon(tree);
        }
        
        private readonly Dictionary<string, string> _filedNameAndType = new() {
            { "Key", "string" },
            { "Path", "string" },
            { "OriginalWeapon", "string" },
            { "DisableUnlockByPick", "bool" },
            { "ShowNum", "bool" },
            { "Price", "int" },
            { "DropAdd", "string" },
            { "Materials", "List<ChooseItemWithNumData>" },
            { "FusionList", "List<FusionConfig>" },
            { "FusionRangeList", "List<FusionRangeConfig>" },
            { "TalentList", "List<TalentConfig>" }
        };

        protected override void OnSave(StreamWriter streamWriter) {
            Common.Utils.WriteFiledAndType(streamWriter, _filedNameAndType);
            foreach (var menuItem in MenuTree.EnumerateTree()) {
                var weaponConfig = (WeaponConfig)menuItem.Value;
                var weaponName = weaponConfig.Weapon.name;
                var path = weaponConfig.Path;
                var originalWeapon = string.Empty;
                if (weaponConfig.OriginalWeapon) {
                    originalWeapon = weaponConfig.OriginalWeapon.name;
                }
                var disableUnlockByPick = weaponConfig.DisableUnlockByPick;
                var showNum = weaponConfig.ShowNum;
                var price = weaponConfig.Price;
                var dropAdd = weaponConfig.DropAdd;
                streamWriter.Write(
                    $"{weaponName},{path},{originalWeapon},{disableUnlockByPick},{showNum},{price},{dropAdd},");
                
                var materials = Common.Utils.SerializeObjectToCsvJson(weaponConfig.Materials);
                if (weaponConfig.OriginalWeapon) {
                    materials = "[]";
                }
                streamWriter.Write($"{materials},");
                
                var fusionList = Common.Utils.SerializeObjectToCsvJson(weaponConfig.FusionList);
                if (weaponConfig.OriginalWeapon) {
                    fusionList = "[]";
                }
                streamWriter.Write($"{fusionList},");
                
                var fusionRangeList = Common.Utils.SerializeObjectToCsvJson(weaponConfig.FusionRangeList);
                streamWriter.Write($"{fusionRangeList},");
                
                var talentList = Common.Utils.SerializeObjectToCsvJson(weaponConfig.TalentList);
                if (weaponConfig.OriginalWeapon) {
                    talentList = "[]";
                }
                streamWriter.Write($"{talentList}\n");
            }
        }

        protected override void OnAfterSave() {
            WeaponCsvCache.Load();
            if (Application.isPlaying) {
                WeaponConfigLoader.Load();
            }
        }
    }
}