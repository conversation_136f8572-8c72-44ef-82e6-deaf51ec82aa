using ExcelDataReader;
using I2.Loc;
using RGScript.UI.ChooseHero;
using Sirenix.OdinInspector;
using Sirenix.OdinInspector.Editor;
using Sirenix.Utilities;
using Sirenix.Utilities.Editor;
using SoulKnight.Editor.Common;
using SoulKnight.Editor.Common.Data;
using SoulKnight.Editor.Common.Odin;
using SoulKnight.Editor.Config2Code;
using SoulKnight.Editor.SkillEditor;
using SoulKnight.Editor.Tools;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using UnityEditor;
using UnityEditor.Animations;
using UnityEditor.Compilation;
using UnityEngine;
using Object = UnityEngine.Object;

namespace SoulKnight.Editor.SkinEditor {
    public class SkinAutoGenerator : OdinEditorWindow {
        public const string Line1 = "line1";
        public const string Line3 = "line3";
        public const string Line4 = "line4";

        [HorizontalGroup(Line1,0.85f)]
        [OnValueChanged(nameof(OnSelectHero))] 
        [LabelText("角色")]
        public emHero hero;
        
        [PropertySpace(2)]
        [Button("读取当前角色")]
        [HorizontalGroup(Line1)]
        void GetCurRole() {
            if (Application.isPlaying) {
                GetRoleData(out emHero emHero, out int index);
                if (emHero == emHero.None) {
                    return;
                }
                hero = emHero;
                skinIndex = index;
            }
            _singletonGroup = PrefabUtility.LoadPrefabContents(SingletonGroupPath);
            _saveManager = _singletonGroup.GetComponentInChildren<RGSaveManager>();
            unlockWay = _saveManager.char_list[hero.ToInt()].skin_list[skinIndex];
            LogUtil.Log($"--- c_{(int)hero}_s_{skinIndex} {RGCharacterInfo.GetSkinUnlockMethod(unlockWay)} \n ");

            SkinSubtitleConfig skinSubtitleConfig =
                AssetDatabase.LoadAssetAtPath<SkinSubtitleConfig>("Assets/Resources/SkinSubtitleConfig.asset");
            if (skinSubtitleConfig.SkinSubtitleDic.ContainsKey(hero) &&
                skinSubtitleConfig.SkinSubtitleDic[hero] != null &&
                skinSubtitleConfig.SkinSubtitleDic[hero].ContainsKey(skinIndex)) {
                subTitle = skinSubtitleConfig.SkinSubtitleDic[hero][skinIndex];
            } else {
                subTitle = "";
            }            
        }
        

        [LabelText("皮肤ID")] public int skinIndex = 99;

        [HorizontalGroup(Line3,0.85f)]
        [LabelText("解锁方式")] 
        public int unlockWay = 2333;

        [HorizontalGroup(Line4,0.85f)]
        [ValueDropdown(nameof(SubTitleDropDown), FlattenTreeView = true)]
        [LabelText("子标题")] 
        public string subTitle;

        public ValueDropdownList<string> SubTitleDropDown() {
            var nameDropDown = new ValueDropdownList<string>();
            var skinSubtitleConfig = ScriptableConfigUtil.Load<SkinSubtitleConfig>();
            var subTitles = skinSubtitleConfig.GetAllSubtitles();
            subTitles.ForEach(key => {
                var shownName = $"{ScriptLocalization.GetCN(key)}({key})";
                nameDropDown.Add(shownName, key);
            });
            return nameDropDown;
        }
        
        [PropertySpace(2)]
        [Button("设置unlock")]
        [HorizontalGroup(Line3)]
        void SetUnlock() {
            var tempHero = hero;
            var tempSkinIndex = skinIndex;
            if (Application.isPlaying) {
                GetRoleData(out emHero emHero, out int index);
                hero = emHero;
                skinIndex = index;
            }
            if (hero == emHero.None) {
                hero = tempHero;
                skinIndex = tempSkinIndex;
            }
            
            _singletonGroup = PrefabUtility.LoadPrefabContents(SingletonGroupPath);
            _saveManager = _singletonGroup.GetComponentInChildren<RGSaveManager>();
            var listSkin = _saveManager.char_list[hero.ToInt()].skin_list;

            if (listSkin.Length > skinIndex) {
                listSkin[skinIndex] = unlockWay;
            } else {
                LogUtil.Log($"-- len error ret");
                return;
            }
            PrefabUtility.SaveAsPrefabAsset(_singletonGroup, SingletonGroupPath);
            LogUtil.Log($"-- save unlock {RGCharacterInfo.GetSkinUnlockMethod(unlockWay)}");
        }

        [PropertySpace(2)]
        [Button("设置子标题")]
        [HorizontalGroup(Line4)]
        void SetSubTitle() {
            var tempHero = hero;
            var tempSkinIndex = skinIndex;
            if (Application.isPlaying) {
                GetRoleData(out emHero emHero, out int index);
                hero = emHero;
                skinIndex = index;
            }
            if (hero == emHero.None) {
                hero = tempHero;
                skinIndex = tempSkinIndex;
            }
            SkinSubtitleConfig skinSubtitleConfig = AssetDatabase.LoadAssetAtPath<SkinSubtitleConfig>("Assets/Resources/SkinSubtitleConfig.asset");
            if (!skinSubtitleConfig.SkinSubtitleDic.ContainsKey(hero)) {
                skinSubtitleConfig.SkinSubtitleDic.Add(hero, new Dictionary<int, string>());
            }
            skinSubtitleConfig.SkinSubtitleDic[hero][skinIndex] = subTitle;

            EditorUtility.SetDirty(skinSubtitleConfig);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }

        [LabelText("基础动画总帧数")] public int animFrameCount = 17;
        [LabelText("参考皮肤")] public int copyFromSkinIndex;

        [LabelText("基础动画资源裁切列数"), HorizontalGroup("xy")]
        public int columnCount = 8;

        [LabelText("基础动画资源裁切行数"), HorizontalGroup("xy")]
        public int rowCount = 3;

        [LabelText("idle帧数"), HorizontalGroup("idleRun")]
        public int idleFrameNumber = 8;

        [LabelText("run帧数"), HorizontalGroup("idleRun")]
        public int runFrameNumber = 8;

        [LabelText("皮肤基础动画资源"), HorizontalGroup("pathTexture")]
        public string texturePath;

        [LabelText("皮肤ui资源"), HorizontalGroup("pathUI")]
        public string uiPath;

        [LabelText("自动命名prefab"), LabelWidth(100)]
        public bool autoRenameNewSkinPrefab = true;

        [LabelText("已导入纹理"), LabelWidth(100),HorizontalGroup("gen")] public Object targetTexture;
        [LabelText("已生成武器"), LabelWidth(100),HorizontalGroup("gen")] public Object genWeapon;

        private GameObject _singletonGroup;
        private RGSaveManager _saveManager;
        private string _skinFoldPath;
        private string _heroName;
        private string _textureName;
        private bool _breakGenerate;
        private const string SingletonGroupPath = "Assets/RGPrefab/Other/singleton/singleton_group.prefab";

        [MenuItem("SoulKnight/Skin Tool/Skin Auto Generator &g")]
        public static void Open() {
            if (HasOpenInstances<SkinAutoGenerator>()) {
                FocusWindowIfItsOpen<SkinAutoGenerator>();
                return;
            }
            var window = GetWindow<SkinAutoGenerator>();
            window.position = GUIHelper.GetEditorWindowRect().AlignCenter(400, 400);
        }

        [Button("设置动画资源路径"), HorizontalGroup("pathTexture")]
        private void SetTexturePath() {
            texturePath = EditorUtility.OpenFilePanel("导入动画资源", texturePath, "");
        }

        [Button("设置ui资源路径"), HorizontalGroup("pathUI")]
        private void SetUIPath() {
            uiPath = EditorUtility.OpenFilePanel("导入动画资源", uiPath, "");
        }

        [BoxGroup("自动生成相关"), Button("生成皮肤")]
        private void GenerateSkinConfigs() {
            LoadSaveManager();
            GenerateNewSkinFolder();
            UpdateSaveManagerData();
            UpdateNewSkinConfig();
            ScanAssetBundleAndCharacterSprite();
        }
        

        void GetRoleData(out emHero emHero, out int index) {
            emHero = emHero.None;
            index = 0;

            var player = RGGameSceneManager.Inst.controller;
            if (player) {
                emHero = player.GetHeroType();
                index = player.GetSkinIndex();
            } else {
                ChooseHeroModel model = GameObject.FindObjectOfType<ChooseHeroModel>();
                if (model != null) {
                    var field = ReflectionHelper.GetFieldNamedData(model.GetType(), "data");
                    object obj = field.GetValue(model);
                    ChooseHeroData data = (ChooseHeroData)obj;
                    emHero = (emHero)data.id;
                    index = data.skinIndex;
                }
            }
        }
        
        //保存解锁方式
        private void UpdateSaveManagerData() {
            if (_breakGenerate) {
                return;
            }

            var listSkin = _saveManager.char_list[hero.ToInt()].skin_list;
            var tempListSkin = new int[listSkin.Length + 1];
            for (int i = 0; i < listSkin.Length; i++) {
                tempListSkin[i] = listSkin[i];
            }

            tempListSkin[listSkin.Length] = unlockWay;
            _saveManager.char_list[hero.ToInt()].skin_list = tempListSkin;
            
            PrefabUtility.SaveAsPrefabAsset(_singletonGroup, SingletonGroupPath);
        }

        
        private void LoadSaveManager() {
            _singletonGroup =
                PrefabUtility.LoadPrefabContents(SingletonGroupPath);
            _saveManager = _singletonGroup.GetComponentInChildren<RGSaveManager>();
            _breakGenerate = false;
            _heroName = hero.ToString()[0].ToString().ToLower() + hero.ToString().Substring(1);
        }

        private void OnSelectHero() {
            try {
                _singletonGroup =
                    PrefabUtility.LoadPrefabContents(SingletonGroupPath);
                _saveManager = _singletonGroup.GetComponentInChildren<RGSaveManager>();
                skinIndex = _saveManager.char_list[hero.ToInt()].skin_list.Length;
            } catch (Exception e) {
                LogUtil.LogError($"--- {e}");
            }
        }

        private void GenerateNewSkinFolder(bool autoGenerateAnim = true) {
            if (!_saveManager) {
                Debug.LogError("生成皮肤相关配置错误：没有找到saveManager");
                _breakGenerate = true;
                return;
            }

            if (skinIndex < _saveManager.char_list[hero.ToInt()].skin_list.Length) {
                Debug.LogError($"skinIndex:{skinIndex}不是最新的皮肤,已打断自动生成，调整skinIndex后重新点击生成");
                _breakGenerate = true;
                return;
            }

            if (skinIndex > _saveManager.char_list[hero.ToInt()].skin_list.Length) {
                skinIndex = _saveManager.char_list[hero.ToInt()].skin_list.Length;
                Debug.LogError($"设置的skinIndex不是正确的下一个新皮肤序号，已自动修正成{skinIndex}以继续自动生成\n（不打断自动生成）");
            }

            _skinFoldPath = $"Assets/Skin/Character/{hero.ToString()}/Skin_{skinIndex}";
            if (AssetDatabase.IsValidFolder(_skinFoldPath)) {
                Debug.LogError($"新皮肤资源文件夹{_skinFoldPath}已存在\n（不打断自动生成）");
            } else {
                if (autoGenerateAnim) {
                    AssetDatabase.CreateFolder($"Assets/Skin/Character/{hero.ToString()}", $"Skin_{skinIndex}");
                } else {
                    AssetDatabase.CopyAsset($"Assets/Skin/Character/{hero.ToString()}/Skin_{copyFromSkinIndex}",
                        _skinFoldPath);
                }

                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
            }

            ImportTexture(autoGenerateAnim);
            ImportUI();
        }

        private void ImportTexture(bool autoSlice = true) {
            if (_heroName.IsNullOrWhitespace()) {
                _heroName = hero.ToString()[0].ToString().ToLower() + hero.ToString().Substring(1);
            }

            if (texturePath.IsNullOrWhitespace()) {
                Debug.LogError("导入资源失败，没有设置texture路径");
                _breakGenerate = true;
                return;
            }

            if (_skinFoldPath == "") {
                _skinFoldPath = $"Assets/Skin/Character/{hero.ToString()}/Skin_{skinIndex}";
            }

            if (!AssetDatabase.IsValidFolder(_skinFoldPath)) {
                Debug.LogError($"导入皮肤动画资源失败，没有找到{_skinFoldPath}文件夹");
                _breakGenerate = true;
                return;
            }

            _textureName =
                $"{_skinFoldPath}/{_heroName}_{skinIndex}.png";
            File.Copy(texturePath, _textureName, true);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            if (!File.Exists(_textureName)) {
                Debug.LogError($"导入资源失败，没有找到{_textureName}");
                _breakGenerate = true;
                return;
            }

            if (!autoSlice) {
                return;
            }

            SliceTexture(_textureName);
            GenerateNewSkinAnim();
        }
        
        private void UpdateNewSkinConfig() {
            if (_breakGenerate) {
                return;
            }

            var newSkinConfig =
                AssetDatabase.LoadAssetAtPath<NewSkinConfig>("Assets/RGPrefab/Other/scene_object/NewSkinConfig.asset");
            newSkinConfig.AddNewSkin(hero, skinIndex);
            EditorUtility.SetDirty(newSkinConfig);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }

        private void GenerateNewSkinAnim() {
            if (_skinFoldPath == "") {
                _skinFoldPath = $"Assets/Skin/Character/{hero.ToString()}/Skin_{skinIndex}";
            }

            if (copyFromSkinIndex >= skinIndex || copyFromSkinIndex < 0) {
                Debug.LogError($"参考皮肤序号{copyFromSkinIndex}不存在，默认使用0号皮肤作为参考\n（不打断自动生成流程）");
                copyFromSkinIndex = 0;
            }

            AssetDatabase.CopyAsset($"Assets/Skin/Character/{hero.ToString()}/Skin_{copyFromSkinIndex}/skin.controller",
                $"{_skinFoldPath}/skin.controller");
            AssetDatabase.CopyAsset(
                $"Assets/Skin/Character/{hero.ToString()}/Skin_{copyFromSkinIndex}/skin_{copyFromSkinIndex}_dead.anim",
                $"{_skinFoldPath}/skin_{skinIndex}_dead.anim");
            AssetDatabase.CopyAsset(
                $"Assets/Skin/Character/{hero.ToString()}/Skin_{copyFromSkinIndex}/skin_{copyFromSkinIndex}_run.anim",
                $"{_skinFoldPath}/skin_{skinIndex}_run.anim");
            AssetDatabase.CopyAsset(
                $"Assets/Skin/Character/{hero.ToString()}/Skin_{copyFromSkinIndex}/skin_{copyFromSkinIndex}_idle.anim",
                $"{_skinFoldPath}/skin_{skinIndex}_idle.anim");

            var newController = AssetDatabase.LoadAssetAtPath<AnimatorController>($"{_skinFoldPath}/skin.controller");
            var newIdle = AssetDatabase.LoadAssetAtPath<AnimationClip>($"{_skinFoldPath}/skin_{skinIndex}_idle.anim");
            var newRun = AssetDatabase.LoadAssetAtPath<AnimationClip>($"{_skinFoldPath}/skin_{skinIndex}_run.anim");
            var newDead = AssetDatabase.LoadAssetAtPath<AnimationClip>($"{_skinFoldPath}/skin_{skinIndex}_dead.anim");
            if (!newController || !newIdle || !newRun || !newDead) {
                Debug.LogError($"自动生成皮肤动画机失败，未能找到{_skinFoldPath}下的动画或动画机");
                _breakGenerate = true;
                return;
            }

            var childStates = newController.layers[0].stateMachine.states;
            foreach (var state in childStates) {
                state.state.motion = state.state.name switch {
                    "ide" => newIdle,
                    "run" => newRun,
                    "dead" => newDead,
                    _ => state.state.motion
                };
            }

            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            UpdateAnimationClipRef(newIdle, idleFrameNumber, 0);
            UpdateAnimationClipRef(newRun, runFrameNumber, idleFrameNumber);
            UpdateAnimationClipRef(newDead, 1, runFrameNumber + idleFrameNumber);
        }

        private void UpdateAnimationClipRef(AnimationClip clip, int frameCount, int startIndex) {
            var sprites = AssetDatabase.LoadAllAssetsAtPath(_textureName);
            var bindings = AnimationUtility.GetObjectReferenceCurveBindings(clip);
            foreach (var binding in bindings) {
                var keyFrames = AnimationUtility.GetObjectReferenceCurve(clip, binding);
                if (frameCount != keyFrames.Length) {
                    Debug.LogError($"替换动画帧{clip.name}资源时发现{clip.name}的帧数和设置的帧数不一致，" +
                                   $"自动生成结束后请手动观察以下新皮肤的动画表现是否正常\n（不打断自动生成）");
                }

                var newFrames = new ObjectReferenceKeyframe[frameCount];
                for (int j = startIndex; j < startIndex + frameCount; j++) {
                    if ((j - startIndex) < (keyFrames.Length)) {
                        newFrames[j - startIndex] = keyFrames[j - startIndex];
                    }

                    if (newFrames[0].value && newFrames[0].value.name.Contains(_heroName)) {
                        if (j >= sprites.Length - 1) {
                            Debug.LogError($"替换动画帧{clip.name}资源失败，" +
                                           $"\n请检查{_heroName}_{skinIndex}裁切行列数和帧数设置是否正确");
                            _breakGenerate = true;
                            return;
                        }

                        newFrames[j - startIndex].value = sprites[j + 1];
                    }
                }

                AnimationUtility.SetObjectReferenceCurve(clip, binding, newFrames);
            }

            EditorUtility.SetDirty(clip);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }

        private void SliceTexture(string sourceTexturePath) {
            targetTexture = AssetDatabase.LoadAssetAtPath<Object>(sourceTexturePath);
            AssetDatabase.ImportAsset(sourceTexturePath, ImportAssetOptions.ForceUpdate);
            TextureImporter textureImporter = (TextureImporter)AssetImporter.GetAtPath(sourceTexturePath);
            if (!textureImporter) {
                Debug.LogError($"自动裁剪失败，没有找到{sourceTexturePath}");
                return;
            }

            SpliteTextureHelper.Split(sourceTexturePath, columnCount, rowCount, new Vector2(0.5f, 0),
                SpriteAlignment.Custom);
        }


        private void ScanAssetBundleAndCharacterSprite() {
            if (_breakGenerate) {
                return;
            }

            SkinTool.ScanSkinAssetBundleAndExportData();
            CharacterSpritesDrawer.UpdateCharacterSprite();
            Debug.Log($"自动生成皮肤成功{_heroName}_{skinIndex}");
        }


        [BoxGroup("重导入相关"), Button("重新导入皮肤动画资源")]
        private void ReimportTexture() {
            _heroName = hero.ToString()[0].ToString().ToLower() + hero.ToString().Substring(1);

            if (texturePath.IsNullOrWhitespace()) {
                Debug.LogError("导入资源失败，没有设置texture路径");
                _breakGenerate = true;
                return;
            }

            if (_skinFoldPath.IsNullOrWhitespace()) {
                _skinFoldPath = $"Assets/Skin/Character/{hero.ToString()}/Skin_{skinIndex}";
            }

            if (!AssetDatabase.IsValidFolder(_skinFoldPath)) {
                Debug.LogError($"导入皮肤动画资源失败，没有找到{_skinFoldPath}文件夹");
                _breakGenerate = true;
                return;
            }

            _textureName =
                $"{_skinFoldPath}/{_heroName}_{skinIndex}.png";
            File.Copy(texturePath, _textureName, true);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            if (!File.Exists(_textureName)) {
                Debug.LogError($"导入资源失败，没有找到{_textureName}");
            }

            UpdateAssetBundle();
        }

        [BoxGroup("重导入相关"), Button("重新导入UI资源")]
        private void OnImportUIBtn() {
            if (Application.isPlaying) {
                GetRoleData(out emHero getHero, out int index);
                if (getHero != emHero.None && getHero != hero || index != skinIndex) {
                    this.ShowNotification(new GUIContent("当前界面角色皮肤与编辑器选项不同,请先点击'读取当前角色'按钮"));
                    return;
                }

            }
            ImportUI();
        }
        private void ImportUI() {
            _heroName = hero.ToString()[0].ToString().ToLower() + hero.ToString().Substring(1);
            var uiName =
                $"Assets/RGTexture/ui/skin/{_heroName}_{skinIndex}_ui.png";
            var skinUI =
                AssetDatabase.LoadAssetAtPath<Sprite>(uiName);
            Debug.Log($"-- 导入UI {uiName}");

            if (uiPath.IsNullOrWhitespace()) {
                uiPath = "Assets/RGTexture/ui/skin/miner_0_ui.png";
                Debug.Log("没有设置UI路径,默认使用miner_0_ui.png代替\n");
            }
            
            File.Copy(uiPath, uiName, true);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            SavaUIToSaveManager();
        }

        void SavaUIToSaveManager() {
            Debug.Log($"-- SavaUIToSaveManager");
            var skinUI =
                AssetDatabase.LoadAssetAtPath<Sprite>($"Assets/RGTexture/ui/skin/{_heroName}_{skinIndex}_ui.png");
            if (!skinUI) {
                Debug.LogError($"-- no skinUI {_heroName}_{skinIndex}_ui.png");
                return;
            }

            if (!_saveManager) {
                LoadSaveManager();
            }
            
            var listUI = _saveManager.hero_skin_list[hero.ToInt()].sprite_list;
            var tempListUI = new List<Sprite>(listUI);
            if (tempListUI.Count <= skinIndex ) {
                tempListUI.Add(skinUI);
            } else {
                tempListUI[skinIndex] = skinUI;
            }
            _saveManager.hero_skin_list[hero.ToInt()].sprite_list = tempListUI.ToArray();
            PrefabUtility.SaveAsPrefabAsset(_singletonGroup, SingletonGroupPath);
        }
        

        [BoxGroup("重导入相关"), Button("重新刷新assetBundle和characterSprite")]
        private void UpdateAssetBundle() {
            SkinTool.ScanSkinAssetBundleAndExportData();
            CharacterSpritesDrawer.UpdateCharacterSprite();
        }

        [BoxGroup("自动生成相关"), Button("生成初始武器")]
        private void GenerateNewSkinWeapon() {
            var skinWeaponConfig =
                AssetDatabase.LoadAssetAtPath<UIHeroSkinWeaponIndex>("Assets/Resources/HeroSkinWeaponIndex.asset");
            var newIndex = 0;
            if (skinWeaponConfig.skinWeaponIndex.ContainsKey(hero)) {
                var weaponDic = skinWeaponConfig.skinWeaponIndex[hero];
                foreach (var weapon in weaponDic) {
                    if (weapon.Value >= newIndex) {
                        newIndex = weapon.Value + 1;
                    }
                }
            }

            string oldWeaponName = GetOldWeaponName(hero);
            string newWeaponName = GetNewWeaponName(hero, newIndex);

            string oldWeaponPath = GetWeaponPath(oldWeaponName, hero);
            string newWeaponPath = GetWeaponPath(newWeaponName, hero);

            //复制武器
            AssetDatabase.CopyAsset(oldWeaponPath, newWeaponPath);
            //写入皮肤武器序号
            try {
                skinWeaponConfig.AddSkinWeaponIndex(hero, skinIndex, newIndex);
                EditorUtility.SetDirty(skinWeaponConfig);
            } catch (Exception e) {
                LogUtil.LogError($"skinWeaponConfig 写入失败 {e}");
            }

            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            genWeapon = AssetDatabase.LoadAssetAtPath<GameObject>(newWeaponPath);
            CheckInitWeaponCanForgeable();
        }
        
        [BoxGroup("自动生成相关"), Button("打开WeaponEditor")]
        void WeaponSave() {
            var window = GetWindow<WeaponEditor.WeaponEditor>();
            window.position = GUIHelper.GetEditorWindowRect().AlignCenter(800, 600);
        }
        
        private static string GetOldWeaponName(emHero hero) {
            string heroName = hero.ToString().ToLower();
            return hero.Equals(emHero.Knight) ? "weapon_000x" : $"weapon_init_{heroName.ToLower()}x";
        }

        private static string GetNewWeaponName(emHero hero, int newIndex) {
            if (hero.Equals(emHero.Knight)) {
                return $"weapon_000xx{newIndex}";
            } else {
                string heroName = hero.ToString().ToLower();
                return newIndex > 1
                    ? $"weapon_init_{heroName.ToLower()}xx{newIndex}"
                    : $"weapon_init_{heroName.ToLower()}xx";
            }
        }

        private static string GetWeaponPath(string weaponName, emHero hero) {
            string oldWeaponPath = hero.Equals(emHero.Knight)
                ? $"Assets/RGPrefab/Weapon/{weaponName}.prefab"
                : $"Assets/RGPrefab/Weapon/init_weapon/{weaponName}.prefab";
            return oldWeaponPath;
        }

        public static string DataTableToCsv(DataTable dataTable) {
            StringBuilder sb = new StringBuilder();
            int columnCount = dataTable.Columns.Count;

            // 添加行数据
            foreach (DataRow row in dataTable.Rows) {
                for (int i = 0; i < columnCount; i++) {
                    // 检查是否有需要用双引号括起来的字符
                    string value = row[i].ToString();
                    if (value.Contains(",") || value.Contains("\"") || value.Contains("\n")) {
                        // 如果有，用双引号括起来，并转义双引号
                        value = value.Replace("\"", "\"\"");
                        sb.Append("\"" + value + "\"");
                    } else {
                        // 如果没有，直接添加
                        sb.Append(value);
                    }

                    if (i < columnCount - 1) {
                        sb.Append(",");
                    }
                }

                sb.AppendLine();
            }

            return sb.ToString();
        }

        //忽略列表
        private HashSet<(emHero, int)> _skipDic = new HashSet<(emHero, int)>() {
            { (emHero.Airbender, 0) }
        };
        
        [BoxGroup("自动生成相关"), Button("复制SkillEditor的prefab配置")]
        void CopyEditorConfig() {
            var editor = EditorWindow.GetWindow<SoulKnight.Editor.SkillEditor.SkillEditor>();
            editor.Show();

            for (int i = 0; i < 3; i++) {
                string heroPath = $"c{hero.ToInt()}/skill {i + 1}";
                if (hero.ToInt() < 10) {
                    heroPath = $"c0{hero.ToInt()}/skill {i + 1}";
                }
                if (_skipDic.Contains((hero, i))) {
                    LogUtil.Log($"-- skip {hero} skill{i}");
                    continue;
                }

                var item = editor.MenuTree.FindMenuItemFullPath(heroPath);
                LogUtil.Log($"-- find heroPath {heroPath} {item == null}");
                if (item == null) {
                    LogUtil.Log($"-- null {hero} skill{i}");
                    continue;
                }


                if (item.Value is SkillConfig<GameObjectListSkinData> data) {
                    CopyWithListSkinData(data, heroPath, i);
                } else if (item.Value is SkillConfig<NormalSkinData> normal) {
                    CopyWithNormalSkinData(normal, heroPath, i);
                } else {
                    CopyWithOtherSkinData(item.Value, heroPath, i);
                }
                
            }
            
            editor.Save();
        }

        
        private bool CopyWithListSkinData(SkillConfig<GameObjectListSkinData> data, string heroPath, int i){
            var skinData = data.skillSkinData;
            if (skinData.skinIndexDataList.Count <= skinIndex) {
                Debug.LogError($"-- out array {skinData.skinIndexDataList.Count} <= {skinIndex}");
                return true;
            }
                
            //跳过非0的皮肤索引
            if (IsNewSkinDataIndex(skinData.skinIndexDataList[skinIndex])) {
                Debug.Log($"-- skip {heroPath}, skinDataIndex != 0");
                return false;
            }

            //获取皮肤文件夹路径 并创建文件夹
            string skinDir = CheckSkinDirExists(i);


            var skin0 = skinData.skinDataList[0].gameObjectList;
            List<string> newPaths = new List<string>();
            for (int j = 0; j < skin0.Count; j++) {
                //复制预制体到skinDir下,并保持文件名不变
                GameObject skin0GameObject = skin0[j].gameObject;
                string newPath = CopyPrefabToNewSkinDir(skin0GameObject, skinDir);
                newPaths.Add(newPath);
            }
                
            List<ObjectData> newGameObjectList = new List<ObjectData>();
            foreach (var t in newPaths){ ;
                newGameObjectList.Add(new ObjectData(AssetDatabase.LoadAssetAtPath<GameObject>(t)));
            }
                
            //复制原皮肤配置
            var newSkinData = new GameObjectListSkinData() { gameObjectList = newGameObjectList };
            skinData.skinDataList.Add(newSkinData);
            skinData.skinIndexDataList[skinIndex].skinDataIndex = skinData.skinDataList.Count - 1;
            Debug.Log($"-- add {heroPath}");
            return false;
        }

        private bool CopyWithOtherSkinData(object itemValue, string heroPath, int i) {
            Debug.Log($"-- skill{i} item.Value type => {itemValue.GetType()}");
            Type type = itemValue.GetType();

            //SkillConfig<T> skinDataList
            //T.SkillSkinData<T2>
            BindingFlags all = ~BindingFlags.Default;
            var skillSkinDataFieldInfo = type.GetField("skillSkinData", all);

            object skillSkinData = skillSkinDataFieldInfo.GetValue(itemValue);
            Type skinDataType = skillSkinData.GetType();


            var skinDataListField = skinDataType.GetField("skinDataList", all);
            var skinDataList = (IList)skinDataListField.GetValue(skillSkinData);

            //跳过非0的皮肤索引
            var skinIndexDataListField = skinDataType.GetField("skinIndexDataList", all);
            List<SkinIndexData> skinIndexDataList = (List<SkinIndexData>)skinIndexDataListField.GetValue(skillSkinData);
            if (skinIndexDataList.Count <= skinIndex) {
                Debug.LogError($"-- out array {skinIndexDataList.Count} <= {skinIndex}");
                return false;
            }
            
            if (IsNewSkinDataIndex(skinIndexDataList[skinIndex])) {
                Debug.Log($"-- skip {heroPath}, skinDataIndex != 0");
                return false;
            }

            //复制skin0
            //处理ObjectData 和 List<ObjectData>
            string skinDir = CheckSkinDirExists(i);
            object copyObject = Activator.CreateInstance(skinDataList[0].GetType());
            foreach (var field in skinDataList[0].GetType().GetFields(BindingFlags.Public | BindingFlags.Instance)) {
                LogUtil.Log($"--  field copy {field.Name}");

                if (field.FieldType == typeof(ObjectData)) {
                    ObjectData objectData = field.GetValue(skinDataList[0]) as ObjectData;
                    ObjectData newObjectData = CopyObjectData(objectData, skinDir);
                    field.SetValue(copyObject, newObjectData);
                } else if (field.FieldType == typeof(List<ObjectData>)) {
                    List<ObjectData> objectDataList = field.GetValue(skinDataList[0]) as List<ObjectData>;
                    List<ObjectData> newObjectDataList = new List<ObjectData>();
                    foreach (var objectData in objectDataList) {
                        ObjectData newObjectData = CopyObjectData(objectData, skinDir);
                        newObjectDataList.Add(newObjectData);
                    }
                    field.SetValue(copyObject, newObjectDataList);
                } else if(field.FieldType == typeof(List<ElfSkinDataSkill3.FairyModel>)) {
                    var list = field.GetValue(skinDataList[0]) as List<ElfSkinDataSkill3.FairyModel>;
                    var newList = new List<ElfSkinDataSkill3.FairyModel>();
                    foreach (var objectData in list) {
                        ElfSkinDataSkill3.FairyModel newObjectData = new ElfSkinDataSkill3.FairyModel();
                        newObjectData.fairy = CopyObjectData(objectData.fairy, skinDir);
                        newObjectData.bullet = CopyObjectData(objectData.bullet, skinDir);
                        newList.Add(newObjectData);
                    }
                    field.SetValue(copyObject, newList);
                }
                else {
                    field.SetValue(copyObject, field.GetValue(skinDataList[0]));
                }
            }

            skinDataList.Add(copyObject);
            skinIndexDataList[skinIndex].skinDataIndex = skinDataList.Count - 1;
            Debug.Log($"-- set value succeed {heroPath}");
            return true;
        }

        private ObjectData CopyObjectData(ObjectData objectData, string skinDir) {
            if (objectData.gameObject == null) {
                return new ObjectData();
            }
            
            string newPath = CopyPrefabToNewSkinDir(objectData.gameObject, skinDir);
            var newObjectData = new ObjectData(AssetDatabase.LoadAssetAtPath<GameObject>(newPath));
            return newObjectData;
        }


        private void CopyWithNormalSkinData(SkillConfig<NormalSkinData> normal, string heroPath, int i){
            var norskinData = normal.skillSkinData;
            if (norskinData.skinIndexDataList.Count <= skinIndex) {
                Debug.LogError($"-- out array {norskinData.skinIndexDataList.Count} <= {skinIndex}");
                return;
            }
            
            //跳过非0的皮肤索引
            if (IsNewSkinDataIndex(norskinData.skinIndexDataList[skinIndex])) {
                Debug.Log($"-- skip {heroPath}, skinDataIndex != 0");
                return;
            } 
            //获取皮肤文件夹路径 并创建文件夹
            string skinDir = CheckSkinDirExists(i);
            GameObject skin0GameObject = norskinData.skinDataList[0].objectData.gameObject;
            //复制预制体
            string newPath = CopyPrefabToNewSkinDir(skin0GameObject, skinDir);
            var newObjectData = new ObjectData(AssetDatabase.LoadAssetAtPath<GameObject>(newPath));
            norskinData.skinDataList.Add(new NormalSkinData() { objectData = newObjectData });
            norskinData.skinIndexDataList[skinIndex].skinDataIndex = norskinData.skinDataList.Count - 1;
        }

        private bool IsNewSkinDataIndex(SkinIndexData skinIndexData) {
            return skinIndexData.skinDataIndex != 0;
        }

        private string CopyPrefabToNewSkinDir(GameObject skin0GameObject, string skinDir){
            var path = AssetDatabase.GetAssetPath(skin0GameObject);
            string prefabName = GetSkinExtendName(skin0GameObject.name);
            string newPath = $"{skinDir}/{prefabName}.prefab";

            if (File.Exists(newPath)) {
                Debug.Log($"-- tip: prefab exist {newPath}");
                return newPath;
            } else {
                AssetDatabase.CopyAsset(path, newPath);
            }

            var abName = GetAbNameByPath(newPath);
            AssetImporter.GetAtPath(newPath).SetAssetBundleNameAndVariant(abName, "");
            
            return newPath;
        }

        private string GetAbNameByPath(string file) {
            var path = file.ToPathWithoutAssets();
            path = path[..path.LastIndexOf('/')];
            path = path.ToLower();
            var abName = String.Join("/",path.Split('/').Take(4));
            return abName;
        }
        
        private string CheckSkinDirExists(int i){
            string skinDir = $"Assets/Skin/Character/{hero.ToString()}/Skin_{skinIndex}/skill{i}";
            if (!Directory.Exists(skinDir)) {
                Directory.CreateDirectory(skinDir);
                LogUtil.Log($"-- create Directory {skinDir}");
            }

            return skinDir;
        }


        //获取皮肤命名格式
        private string GetSkinExtendName(string prefabName) {
            if (!autoRenameNewSkinPrefab) {
                return prefabName;
            }

            string skinNameHead = hero.ToString().ToLower() + "_0";
            if (prefabName.StartsWith(skinNameHead)) {
                return prefabName.Replace(skinNameHead, $"{hero.ToString().ToLower()}_{skinIndex}");
            }

            if (prefabName.Contains("_s0")) {
                return prefabName.Replace("_s0", $"_s{skinIndex}");
            }

            //直接加后缀
            return $"{prefabName}_s{skinIndex}";
        }
        
        

#if UNITY_EDITOR
        [MenuItem("SoulKnight/Skin Tool/Weapons/Check Weapons Bullet Info Asset Bundle")]
        private static void CheckWeaponBulletInfoAssetBundle() {
            var correctName = new List<string>() {
                "common", "levelobjects", "levelcommon",
            };
            
            string[] guids = AssetDatabase.FindAssets("weapon t:prefab", new[] { "Assets/RGPrefab/Weapon" });
            List<GameObject> weapons = new();
            foreach (var guid in guids) {
                var path = AssetDatabase.GUIDToAssetPath(guid);
                var weapon = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                weapons.Add(weapon);
            }
            
            foreach (var weapon in weapons) {
                var rgWeapon = weapon.GetComponent<RGWeapon>();
                if (!rgWeapon) {
                    continue;
                }

                var bulletInfos = rgWeapon.bulletsInfo;
                for (int index = 0; index < bulletInfos.Count; index++) {
                    BulletInfoInspector bulletInfo = bulletInfos[index];
                    var bulletProto = bulletInfo.bulletProto;
                    if (!bulletProto) {
                        Debug.LogWarning($"{weapon.name} bulletsInfo[{index}] bullet proto is null!");
                    } else {
                        var path = AssetDatabase.GetAssetPath(bulletProto);
                        var assetBundleName = AssetDatabase.GetImplicitAssetBundleName(path);
                        if (assetBundleName.IsNullOrWhitespace()) {
                            continue;
                        }

                        if (correctName.Contains(assetBundleName)) {
                            continue;
                        }

                        Debug.LogError(
                            $"{weapon.name} bulletsInfo[{index}] bullet {path} assetBundle is {assetBundleName} !");
                    }
                    
                    Debug.Log($"CheckWeaponBulletInfoAssetBundle END");
                }
            }
        }
        
        [MenuItem("SoulKnight/Skin Tool/Weapons/CheckInitWeaponCanForgeable")]
        private static void CheckInitWeaponCanForgeable() {
            string[] guids = AssetDatabase.FindAssets("weapon t:prefab", new[] { "Assets/RGPrefab/Weapon/init_weapon" });
            List<GameObject> weapons = new();
            foreach (var guid in guids) {
                var path = AssetDatabase.GUIDToAssetPath(guid);
                var weapon = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                weapons.Add(weapon);
            }
            
            foreach (var weapon in weapons) {
                var rgWeapon = weapon.GetComponent<RGWeapon>();
                if (!rgWeapon) {
                    continue;
                }

                if (rgWeapon.item_level != 1) {
                    continue;
                }
                
                if(rgWeapon.HasTag(emWeaponTag.NotReforge))
                {
                    if (!rgWeapon.name.Contains("airbender")) {
                        rgWeapon.weapon_tags.Remove(emWeaponTag.NotReforge);
                    }
                }
                
                if (!rgWeapon.weapon_tags.Contains(emWeaponTag.CantExchangeByToken)) {
                    rgWeapon.weapon_tags.Add(emWeaponTag.CantExchangeByToken);
                }
                
                EditorUtility.SetDirty(weapon);
            }
            
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }


        [MenuItem("SoulKnight/Skin Tool/Weapons/Check Bullets Asset Bundle")]
        private static void CheckBulletAssetBundle() {
            var correctName = new List<string>() {
                "common", "levelobjects", "levelcommon",
            };
            
            string[] guids = AssetDatabase.FindAssets("t:prefab", new[] { "Assets/RGPrefab/Bullet" });
            List<GameObject> weapons = new();
            foreach (var guid in guids) {
                var path = AssetDatabase.GUIDToAssetPath(guid);
                var assetBundleName = AssetDatabase.GetImplicitAssetBundleName(path);
                if (assetBundleName.IsNullOrWhitespace()) {
                    continue;
                }

                if (correctName.Contains(assetBundleName)) {
                    continue;
                }

                Debug.LogError($"bullet {path} assetBundle is {assetBundleName} !");
            }
            
            Debug.Log($"CheckBulletAssetBundle END");
        }
#endif
    }
}