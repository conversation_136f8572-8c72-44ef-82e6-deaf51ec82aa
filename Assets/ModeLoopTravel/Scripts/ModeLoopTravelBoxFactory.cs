using RGScript.Config;
using RGScript.Other.LotteryUtil;
using System.Collections.Generic;

namespace ModeLoopTravel.Scripts {
    public class LoopTravelFactorMachineItem : ILotteryMachineItem{
        public float Weight { get; set; }
        public int MaxCount { get; set; }
        public bool Ban { get; set; }
        public string Name { get; set; }
        
        public ModeLoopTravelFactorConfig.ModeLoopTravelFactorData FactorData;
    }
    public class ModeLoopTravelBoxFactory {
        public class BattleFactorMachine : BaseLotteryMachine {
            public BattleFactorMachine() {
                var challengeInfos = ChallengeInfo.info.challenges;
                var normalFactorList = new List<emBattleFactor>();
                var goodFactorList = new List<emBattleFactor>();
                foreach (var info in challengeInfos) {
                    if (info.weight == 0) {
                        continue;
                    }

                    if (!DataUtil.IsNormalBattleFactor(info.battleFactor)) {
                        continue;
                    }

                    if (info.difficulty == 0) {
                        normalFactorList.Add(info.battleFactor);
                    }

                    if (info.difficulty < 0) {
                        goodFactorList.Add(info.battleFactor);
                    }
                }


                int normalFactorCount = normalFactorList.Count;
                int goodFactorCount = goodFactorList.Count;

                float normalRate = ModeLoopTravelConfig.Config.FACTOR_NORMAL_WEIGHT / normalFactorCount;
                float goodRate = ModeLoopTravelConfig.Config.FACTOR_GOOD_WEIGHT / goodFactorCount;

                foreach (var factor in normalFactorList) {
                    AddItem(
                        new LotteryMachineItemWithItemType(normalRate,  $"token_factor_{factor}_3", 1, emItemType.TokenTicket, true));
                }

                foreach (var factor in goodFactorList) {
                    AddItem(
                        new LotteryMachineItemWithItemType(goodRate, $"token_factor_{factor}_5", 1, emItemType.TokenTicket, true));
                }
            }

            public sealed override void AddItem(ILotteryMachineItem item) {
                base.AddItem(item);
            }
        }


        public class SeedMachine : BaseLotteryMachine {
            public SeedMachine() {
                //添加所有的种子
                var level0Seeds = PickableDistribution.datas[0].seedDistribute;
                var level1Seeds = PickableDistribution.datas[1].seedDistribute;
                var level2Seeds = PickableDistribution.datas[2].seedDistribute;

                float lv0Weight = ModeLoopTravelConfig.Config.SEED_LV0_WEIGHT / level0Seeds.Count;
                float lv1Weight = ModeLoopTravelConfig.Config.SEED_LV1_WEIGHT / level1Seeds.Count;
                float lv2Weight = ModeLoopTravelConfig.Config.SEED_LV2_WEIGHT / level2Seeds.Count;


                foreach (var seed in level0Seeds) {
                    AddItem(new LotteryMachineItemWithItemType(lv0Weight, seed.item, 1, emItemType.Seed));
                }

                foreach (var seed in level1Seeds) {
                    AddItem(new LotteryMachineItemWithItemType(lv1Weight, seed.item, 1, emItemType.Seed));
                }

                foreach (var seed in level2Seeds) {
                    AddItem(new LotteryMachineItemWithItemType(lv2Weight, seed.item, 1, emItemType.Seed));
                }
            }
        }

        public readonly LotteryMachineGroup LotteryMachineGroup;

        public ModeLoopTravelBoxFactory(RGRandom rgRandom) {
            LotteryMachineGroup = new LotteryMachineGroup(rgRandom);
            var gemMachine = new BaseLotteryMachine {
                Weight = ModeLoopTravelConfig.Config.GEM_WEIGHT,
            };
            LotteryMachineGroup.AddMachine(gemMachine);
            gemMachine.AddItem(new LotteryMachineItemWithItemType(ModeLoopTravelConfig.Config.GEM_100_WEIGHT,
                ItemData.GemName, 100, emItemType.Material));

            gemMachine.AddItem(new LotteryMachineItemWithItemType(ModeLoopTravelConfig.Config.GEM_200_WEIGHT,
                ItemData.GemName, 200, emItemType.Material));


            gemMachine.AddItem(new LotteryMachineItemWithItemType(ModeLoopTravelConfig.Config.GEM_500_WEIGHT,
                ItemData.GemName, 500, emItemType.Material));


            gemMachine.AddItem(new LotteryMachineItemWithItemType(ModeLoopTravelConfig.Config.GEM_1000_WEIGHT,
                ItemData.GemName, 1000, emItemType.Material, true));


            //material
            var matMachine = new MaterialBoxLotteryMachine {
                Weight = ModeLoopTravelConfig.Config.MATERIAL_WEIGHT,
            };
            LotteryMachineGroup.AddMachine(matMachine);


            //seed
            var seedMachine = new SeedMachine {
                Weight = ModeLoopTravelConfig.Config.SEED_WEIGHT,
                MaxCount = ModeLoopTravelConfig.Config.SEED_MAX_COUNT,
                ProbabilityAddWithBan = gemMachine
            };
            LotteryMachineGroup.AddMachine(seedMachine);


            //weapon item
            var equipmentTokenMachine = new BaseLotteryMachine {
                Weight = ModeLoopTravelConfig.Config.EQUIPMENT_WEIGHT,
                MaxCount = ModeLoopTravelConfig.Config.EQUIPMENT_MAX_COUNT,
                ProbabilityAddWithBan = gemMachine
            };
            LotteryMachineGroup.AddMachine(equipmentTokenMachine);
            equipmentTokenMachine.AddItem(
                new LotteryMachineItemWithItemType(10, ItemData.TokenEquipment, 1, emItemType.TokenTicket, true));

            //battle factor
            var factorMachine = new BattleFactorMachine {
                Weight = ModeLoopTravelConfig.Config.FACTOR_WEIGHT,
                MaxCount = ModeLoopTravelConfig.Config.FACTOR_MAX_COUNT,
                ProbabilityAddWithBan = gemMachine
            };
            LotteryMachineGroup.AddMachine(factorMachine);


            //hero ticket
            var heroTokenMachine = new BaseLotteryMachine {
                Weight = ModeLoopTravelConfig.Config.HERO_WEIGHT,
                MaxCount = ModeLoopTravelConfig.Config.HERO_MAX_COUNT,
                ProbabilityAddWithBan = gemMachine
            };
            LotteryMachineGroup.AddMachine(heroTokenMachine);
            heroTokenMachine.AddItem(
                new LotteryMachineItemWithItemType(10, ItemData.TokenHero, 1, emItemType.TokenTicket, true));
        }
    }
}