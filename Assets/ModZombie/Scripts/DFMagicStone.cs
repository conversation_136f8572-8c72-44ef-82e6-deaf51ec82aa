using Com.LuisPedroFonseca.ProCamera2D;
using DG.Tweening;
using I2.Loc;
using ModeDefence;
using Sirenix.OdinInspector;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using static ModeDefence.DefenceMapManager;

/// <summary>
/// 防守模式的魔法石
/// </summary>
public class DFMagicStone : DefenceNPC {
    public int healthRegen;
    public bool fireNovaEnable;
    public bool hasFireNova;
    public int fireNovaCd = 300;
    public int fireNovaSize = 100;
    public int fireDamage = 2000;
    public int fireNovaCount = 3;
    public GameObject novaPrefab;
    public AudioClip activateClip;

    public int hp {
        get => ModeDefenceData.Data.magic_stone_hp;
        set => ModeDefenceData.Data.magic_stone_hp = value;
    }

    Transform hpBar;
    public GameObject bar;
    float hpBarWidth;
    bool awake = false;

    private void Awake() {
        hpBar = transform.Find("hp_bar/bar/hp");
        hpBarWidth = hpBar.GetComponent<RectTransform>().sizeDelta.x;
        UpdateHpBar();
        Invoke(nameof(DelayAwake), 5f);
    }

    protected override void Start() {
        base.Start();
        if (hp <= 0) {
            GetComponent<Collider2D>().enabled = false;
            MagicStoneBroken();
        }

        Invoke("HealthRegen", 5);
        Invoke(nameof(TurnHasFireNova), fireNovaCd);
    }

    void DelayAwake() {
        awake = true;
    }

    public void RestoreHealth(int value) {
        hp += value;
        if (value >= 0) {
            UICanvas.GetInstance().ShowTextHp(transform.position, value, 2, gameObject.name);
            if (hp > DefenceModeConfig.Config.MAGIC_STONE_MAX_HP)
                hp = DefenceModeConfig.Config.MAGIC_STONE_MAX_HP;
            Instantiate(PrefabManager.GetPrefab(PrefabName.effect_health), transform, false);
        } else {
            if (hp <= 0) {
                hp = 0;
            }
        }

        UIDefence.Inst.ShowMessage(ScriptLocalization.Get("defence/magic_stone_restore_health"), 3, Color.green, false,
            false);
        UIVignette.Create(Color.cyan);
        UpdateHpBar();
    }
    
    private void HealthRegen() {
        if (hp > 0 && healthRegen > 0 && hp < DefenceModeConfig.Config.MAGIC_STONE_MAX_HP) {
            RestoreHealth(healthRegen);
        }
        
        CancelInvoke("HealthRegen");
        Invoke("HealthRegen", 5);
    }

    private void TurnHasFireNova() {
        hasFireNova = true;
    }

    [Button(ButtonSizes.Medium)]
    private void CreateFireNova() {
        void CreateNova() {
            BulletFactory.TakeBullet(new BulletInfo {
                bulletProto = novaPrefab,
                sourceObject = gameObject,
                createPosition = transform.position,
                size = fireNovaSize,
                camp = 1,
            }, new DamageInfo {
                damageType = emDamageType.Fire,
                damage = fireDamage,
                repel = 10,
                camp = 1
            }, parent: transform);
        }

        var sequence = DOTween.Sequence();
        for (int i = 0; i < fireNovaCount; i++) {
            sequence.AppendCallback(CreateNova);
            sequence.AppendInterval(0.5f);
        }
        
        hasFireNova = false;
        CancelInvoke(nameof(TurnHasFireNova));
        Invoke(nameof(TurnHasFireNova), fireNovaCd);
        UIDefence.Inst.OpenMinimapCamera();
    }

    protected override void OnTriggerEnter2D(Collider2D other) {
        if (ModeDefenceData.Data.state != DefenceModeState.IN_BATTLE) return;
        if (FindObjectOfType<AlienCarrier>()) return;

        if (other.gameObject.CompareTag("Body_E")) {
            if (other.GetComponent<RGEController>().HasTag(emEnemyTag.NonCollisionMagicStone)) return;
            if (!other.GetComponent<RGEController>().dead) {
                if (fireNovaEnable && hasFireNova) {
                    CreateFireNova();
                } else {
                    other.GetComponent<RGEController>().Dead(gameObject, true);
                    hp -= other.GetComponent<RGEController>().isBoss ? 5 : 1;
                    GameUtil.CameraShake(2);
                    UIDefence.Inst.ShowMessage(ScriptLocalization.Get("defence/magic_stone_under_attack"), 3, Color.red,
                        true, false);
                    UIVignette.Create(Color.red);
                    UpdateHpBar();
                    CancelInvoke("HealthRegen");
                    if (hp <= 0) {
                        ModeDefenceData.Data.SaveData();
                        GetComponent<Collider2D>().enabled = false;
                        MagicStoneBroken();
                    } else {
                        Invoke("HealthRegen", 5);
                    }
                }
            }
        } else {
            if (DefenceMap.enemy_manager.CanStartWave() && awake) {
                base.OnTriggerEnter2D(other);
            }
        }
    }

    protected override bool Triggerable(RGController controller) {
        return triggerState >= 1 && DefenceMap.enemy_manager.CanStartWave();
    }

    protected override void OnItemTriggerSuccess(RGController controller, string extraInfo = "") {
        base.OnItemTriggerSuccess(controller, extraInfo);
        DefenceMap.enemy_manager.StartNextWave();
    }

    protected override void OnItemTriggerFail(RGController controller) {
        base.OnItemTriggerFail(controller);
        triggerState++;
        ShowTap("defence/magic_stone_talk", true);
    }

    void UpdateHpBar() {
        float percentage = Mathf.Clamp01(hp / (float)DefenceModeConfig.Config.MAGIC_STONE_MAX_HP);
        hpBar.GetComponent<RectTransform>().sizeDelta =
            new Vector2(hpBarWidth * percentage, hpBar.GetComponent<RectTransform>().sizeDelta.y);
    }

    public void HideHpBar() {
        bar.gameObject.SetActive(false);
    }

    bool isBroken;
    
    public void MagicStoneBroken() {
        if (!isBroken) {
            foreach (var sr in transform.Find("magic_stone").GetComponentsInChildren<SpriteRenderer>()) {
                if (sr.sortingLayerName == "WallFont") sr.sortingLayerName = "Effect";
            }

            isBroken = true;
            ModeDefenceData.Data.GameOver();
            ProCamera2D.Instance.RemoveAllCameraTargets();
            ProCamera2D.Instance.AddCameraTarget(transform, 1, 1, 0, new Vector2(0, 0.6f));
            StartCoroutine(GameFailAnim());
            var winRebornTransform = UICanvas.GetInstance().transform.Find("window_reborn");
            if (winRebornTransform != null) {
                var windowReborn = winRebornTransform.GetComponent<UIWindowReborn>();
                if (windowReborn != null) {
                    windowReborn.HideWindow();
                }
            }
        }
    }

    IEnumerator GameFailAnim() {
        yield return new WaitForSeconds(0.25f);
        for (int i = 1; i <= 4; i++) {
            yield return new WaitForSeconds(0.25f);
            transform.Find("magic_stone/line" + i).GetComponent<Animator>().SetBool("play", true);
            RGMusicManager.Inst.PlayEffect(activateClip);
        }

        yield return new WaitForSeconds(1f);
        transform.Find("magic_stone").GetComponent<Animator>().SetTrigger("explode");
        yield return new WaitForSeconds(2.5f);
        UIDefence.Inst.TryShowPlotEnd();
    }
}