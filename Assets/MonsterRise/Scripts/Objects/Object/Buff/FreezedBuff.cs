using System.Collections;
using System.Collections.Generic;

namespace MonsterRise {
    public class FreezedBuff : FBuff {
        public override BuffTag Tags => BuffTag.Debuff | BuffTag.Control;
        public FreezedBuff (IBuffSource source) : base (source, BuffName.Freezed) { }

        protected override void OnStart() {
            carrier.stats.AddAttackSpeedFactor(-config["slow"], false);
        }
        
        protected override void OnEnd() {
            carrier.stats.AddAttackSpeedFactor(config["slow"], true);
        }
    }
}