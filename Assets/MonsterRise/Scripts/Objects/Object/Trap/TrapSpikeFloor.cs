
using FixedNum;

namespace MonsterRise
{
    public class TrapSpikeFloor : FComponent, IHitSource, ITrap {
        public FObject hitSourceObject => owner;

        public FixedNumber damage = FixedNumber.oneTenth * 10;
        public FixedVector2 size;

        public UnitStats stats => null;
        public FixedNum.TimeCounter timeCounter => _timeCounter;
        private FixedNum.TimeCounter _timeCounter;

        ObjectTrigger _trigger = new ObjectTrigger();

        public void SetParam(params string[] paramList){
            damage = new FixedNumber(paramList[0]);
            _timeCounter.interval = new FixedNumber(paramList[1]);
            size = new FixedVector2(1, 1);
        }

        protected override void OnUpdate(FixedNumber deltaTime){
            if(_timeCounter.Update(deltaTime)){
                _trigger.BoxFloorCheck(_game, transform.position, 0, size, CollisionDef.GetMask(ObjectCategory.Character));
                _trigger.ProcessEvent(OnEvent);
                _trigger.Clear();
            }
        }

        void OnEvent(FObject obj, bool enter) {
            var hitTarget = obj.GetComponent<IHitTarget>();
            if (GameplayUtil.IsValid(hitTarget)) {
                Hit(owner, FixedVector2.zero, hitTarget);
            }
        }

        public bool CheckHitable(IHitTarget target){
            return true;
        }

        public bool CheckCrit(out FixedNumber critDamageFactor){
            critDamageFactor = FixedNumber.zero;
            return false;
        }

        public FixedNumber damageMultiply;
        public FixedNumber DamageMultiply { get => damageMultiply; set => damageMultiply = value; }

        public bool Hit(FObject source, FixedVector2 dir, IHitTarget target){
            var info = target.TakeDamage(DamageInfo.Make( 
                DamageSubtype.Environment | DamageSubtype.AOE,
                target is HitUnit hitUnit ? damage * hitUnit.stats.MaxHealth : 1,
                this,
                source.id,
                target
            ));
            return info.InflictedIndeed;
        }
    }
}
