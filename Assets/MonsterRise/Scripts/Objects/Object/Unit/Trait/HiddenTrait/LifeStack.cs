using FixedNum;

namespace MonsterRise {
    public class LifeStackTrait : Trait, TraitIdAssignment {
        public static int TraitId;
        public static void AssignTraitId(int id) {
            TraitId = id + 1;
        }
        public override int id => TraitId;

        public int remain;

        protected override void OnStart() {
            remain = configParams["stack"].Round();
            game.eventHub.AddListener<UnitPreDieEvent>(OnUnitPreDieEvent);
        }

        void OnUnitPreDieEvent(UnitPreDieEvent e) {
            if (e.unit.id == Self.id && --remain > 0) {
                e.unit.stats.dead = false;
                e.unit.stats.ChangeHp(e.unit.stats.MaxHealth);
            }
        }
    }
}