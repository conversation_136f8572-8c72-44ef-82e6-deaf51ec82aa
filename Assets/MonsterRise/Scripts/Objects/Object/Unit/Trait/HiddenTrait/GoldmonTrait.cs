using FixedNum;

namespace MonsterRise {
    public class GoldmonTrait : Trait, TraitIdAssignment {
        public static int TraitId;
        public static void AssignTraitId(int id) {
            TraitId = id + 1;
        }
        public override int id => TraitId;

        FixedNumber hpFactor;
        int deadGold;

        public FixedNumber lastHpPercent;
        public FixedNumber lostHpPercent;
        public bool deadFlag;

        protected override void OnEnable(){
            lastHpPercent = (carrier as FUnit).stats.HpPercent;
            lostHpPercent = FixedNumber.zero;
            deadFlag = (carrier as FUnit).stats.dead;
            hpFactor = configParams["hpFactor"];
            deadGold = configParams["deadGold"].Round();

            game.eventHub.AddListener<UnitHitEvent>(OnUnitHit);
        }

        void OnUnitHit(UnitHitEvent evData) {
            if (evData.unit.id != Self.id) {
                return;
            }

            lostHpPercent += FixedNumber.Max(FixedNumber.zero, lastHpPercent - (carrier as FUnit).stats.HpPercent);
            lastHpPercent = (carrier as FUnit).stats.HpPercent;
            while (hpFactor > FixedNumber.zero && lostHpPercent >= hpFactor) {
            SendEventTakeEffect();
                MonsterModeData.Inst.pveData.AddCustomReward(MonsterRiseUtil.MakeMoneyItem(1));
                lostHpPercent -= hpFactor;
            }

            if (!deadFlag && (carrier as FUnit).stats.dead) {
                deadFlag = true;
                SendEventTakeEffect();
                MonsterModeData.Inst.pveData.AddCustomReward(MonsterRiseUtil.MakeMoneyItem(deadGold));
            }
        }
    }
}