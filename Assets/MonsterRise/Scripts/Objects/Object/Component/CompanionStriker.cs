using FixedNum;

namespace MonsterRise {
    public class CompanionStriker : FComponent, IHitSource {
        public FObject hitSourceObject => owner;
        public int targetId;
        public FixedNumber speed;
        public FixedNumber lifeTime;
        public FixedNumber elapsedTime;
        public FixedNumber damageFactor;
        public int attackedTimes;
        public int masterId;
        public ProjectileInfo projectileInfo;

        FComponentQuery moverQuery = FComponentQuery.Create();
        Mover mover => moverQuery.Get<Mover>(game, owner.entity);

        public System.Action onAttack;


        protected override void OnUpdate(FixedNumber deltaTime) {
            var target = owner.game.GetObject(targetId) as FUnit;
            var master = owner.game.GetObject(masterId) as FUnit;

            if (master == null || !master.IsValid()) {
                owner.Destroy();
                return;
            }

            if (target == null || !target.IsValid()) {
                targetId = masterId;
                target = master;
            }

            if (target != null) {
                var followPos = target.transform.position - target.transform.angle.Vec2().Rotate(15) * FixedNumber.oneTenth * 18;
                var displacement = followPos - transform.position;
                if (displacement.magnitude > FixedNumber.oneTenth * 5) {
                    mover.MovePosition(followPos , speed);
                }

                if (attackedTimes < target.stats.attackedTimes) {
                    attackedTimes = target.stats.attackedTimes;
                    var dir = target.transform.angle.Vec2();
                    if (target.attacker.IsTargetValid) {
                        dir = (target.attacker.Target.transform.position - transform.position).normalized;
                    }
                    Attack(dir);
                }
            }
            elapsedTime += deltaTime;
            if (elapsedTime >= lifeTime) {
                owner.Destroy();
            }
        }

        void Attack(FixedVector2 dir) {
            onAttack?.Invoke();
            var master = owner.game.GetObject(masterId) as FUnit;
            master.attacker.EmitProjectile(transform.position, transform.position, dir, projectileInfo, this, true, FixedNumber.zero, null);
        }

        public bool CheckHitable(IHitTarget hitTarget) {
            var master = owner.game.GetObject(masterId) as FUnit;
            return GameplayUtil.IsValid(hitTarget) && UnitCampUtil.IsHostile(hitTarget.camp, master.stats.camp);
        }

        public bool CheckCrit(out FixedNumber critDamageFactor) {
            critDamageFactor = FixedNumber.zero;
            return false;
        }

        public FixedNumber damageMultiply;
        public FixedNumber DamageMultiply { get => damageMultiply; set => damageMultiply = value; }

        public bool Hit(FObject source, FixedVector2 dir, IHitTarget hitTarget) {
            var master = owner.game.GetObject(masterId) as FUnit;
            var damageInfo = DamageInfo.Make(DamageSubtype.Ability, new CombatActionInfo {
                type = CombatActionType.Ranged
            }, master.stats.AttackDamage * damageFactor, this, source.id, hitTarget);
            damageInfo.damageSubtype |= DamageSubtype.Ranged;
            var result = hitTarget.TakeDamage(damageInfo);
            return result.InflictedIndeed;
        }
    }
}