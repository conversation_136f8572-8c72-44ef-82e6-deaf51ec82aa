using FixedNum;
using System;

namespace MonsterRise
{
    public class TrapBuilder : IObjectBuilder {

        public IConfigLoaderProxy ConfigLoaderProxy { get; set; }

        public FObject Create(IGameInterface game, string name) {
            if (ConfigLoaderProxy == null) {
                throw new Exception("TrapBuilder.configLoaderProxy is null");
            }

            // load config
            var TMPConfigData = ConfigLoaderProxy.Get<cfg.MonsterRise.CfgTrap>(name);
            if (TMPConfigData == null) {
                throw new Exception($"TrapBuilder name {name} not found");
            }

            
            var trapType = Type.GetType($"MonsterRise.Trap{TMPConfigData.Name}");
            if(trapType == null){
                throw new Exception($"TrapBuilder Trap{TMPConfigData.Name} not implemented");
            }
            var trap = new Trap(game, FArchetype.Get(typeof(FTransform), typeof(ObjectSkin), trapType));
            trap.category = (int)ObjectCategory.Trap;
            var trapInterface = trap.GetComponent<ITrap>();
            trapInterface.SetParam(TMPConfigData.Param0, TMPConfigData.Param1, TMPConfigData.Param2, TMPConfigData.Param3);
            trap.GetComponent<ObjectSkin>().name = name;
            return trap;
        }

        public void Destroy(IGameInterface game, FObject obj) { }
    }
}
