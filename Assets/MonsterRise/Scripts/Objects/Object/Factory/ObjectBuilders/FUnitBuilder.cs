using cfg.MonsterRise;
using FixedNum;
using System;
using System.Collections.Generic;

namespace MonsterRise {
    public class FUnitBuilder : IObjectBuilder {

        public IConfigLoaderProxy ConfigLoaderProxy { get; set; }

        public void Destroy(IGameInterface game, FObject obj) { }

        public FObject Create(IGameInterface game, string name) {
            if (ConfigLoaderProxy == null) {
                throw new Exception("UnitBuilder.configLoaderProxy is null");
            }

            // load config
            var unitData = ConfigLoaderProxy.Get<CfgUnit>(name);
            if (unitData == null) {
                throw new Exception($"Unit name {name} not found");
            }

            var types = new List<Type> {
                typeof(FTransform),
                typeof(FieldOccupation),
                typeof(UnitStats),
                typeof(HitUnit),
                typeof(UnitMover),
                typeof(AbilityExecutor),
                typeof(Attacker),
                typeof(BuffManager),
                typeof(ThreatRecorder),
                typeof(UnitTraits),
                typeof(FCollider),
                typeof(ObjectSkin),
                typeof(FsmExecutor),
                typeof(ObjectCombatActionExe),
                typeof(PlayerControlHandle),
                typeof(ObjectCustomData)
            };

            var archetype = FArchetype.Get(types.ToArray());
            var unit = new FUnit(game, archetype);
            var category = ObjectCategory.Character;
            unit.category = (int)category;

            var stats = unit.GetComponent<UnitStats>();
            stats.unitName = name;
            stats.bodyRadius = new FixedNumber(unitData.BodyRadius);
            stats.height = FixedNumber.oneTenth * 17;
            System.Enum.TryParse<UnitClass>(unitData.UnitClass, out var unitClass);
            stats.unitClass = unitClass;
            System.Enum.TryParse<RaceType>(unitData.Race, out stats.race);

            stats.health = new ResourceValue(new FixedNumber(unitData.Health));
            stats.moveSpeed.ModifyValue(new FixedNumber(unitData.MoveSpeed) * game.battleFieldUnitSize);

            var abilityCfg = ConfigLoaderProxy.Get<cfg.MonsterRise.CfgAbility>(unitData.Ability);
            if (abilityCfg != null) {
                // temporary process skill cool down
                stats.mana = new ResourceValue(1);
                var cooldown = new FixedNumber(abilityCfg.Cooldown);
                if (cooldown > FixedNumber.zero) {
                    stats.manaRegen = FixedNumber.one / new FixedNumber(abilityCfg.Cooldown);
                }
                if (abilityCfg.InitPrepared > 0) {
                    stats.mana.SetValue(stats.mana.MaxValue);
                    stats.manaRegen = 0;
                }
                else {
                    stats.mana.SetValue(0);
                }
            }

            var cfgAttackActionInfo = ConfigLoaderProxy.Get<CfgCombatAction>(unitData.AtkType);
            stats.attackAction = new CombatActionInfo { 
                ActionId = Attacker.ActionId,
                type = System.Enum.Parse<CombatActionType>(cfgAttackActionInfo.Type),
                param0 = new FixedNumber(cfgAttackActionInfo.Value1),
                param1 = new FixedNumber(cfgAttackActionInfo.Value2),
                param2 = new FixedNumber(cfgAttackActionInfo.Value3),
            };
            stats.attackStrategy = (FindTargetStrategy)unitData.AtkStrategy;

            var unitTraits = unit.GetComponent<UnitTraits>();
            unitTraits.AddActionTrait(cfgAttackActionInfo);


            var cfgProjectile = ConfigLoaderProxy.Get<cfg.MonsterRise.CfgProjectile>(unitData.ProjectileName);

            stats.projectileInfos = new StatsValueContainer<ProjectileInfo>(ProjectileInfo.CreateFromConfig(cfgProjectile));
            stats.hitSourceStack = new StatsValueContainer<IHitSource>(unit.GetComponent<Attacker>());


            stats.configPreAttackTimeLength = new FixedNumber(unitData.PreAtkActionTime);
            stats.configPostAttackTimeLength = new FixedNumber(unitData.PostAtkActionTime);
            stats.configAttackTimeLength = new FixedNumber(unitData.AtkActionTime);
            stats.configAttackAnimLength = new FixedNumber(unitData.AnimActionTime);

            var totalAttackActionInterval = stats.configAttackTimeLength * stats.attackAction.attackTimes + 
                new FixedNumber(unitData.AtkActionWait) + stats.configPreAttackTimeLength + stats.configPostAttackTimeLength;
            stats.configAttackInterval = totalAttackActionInterval;

            stats.configAttackEventProgress = new FixedNumber(unitData.AtkActionEvent);

            stats.attackRange = new FixedNumber(unitData.AtkRange).Round();
            stats.attackDamage = new FixedNumber(unitData.AtkDamage);
            stats.critRate = new FixedNumber(unitData.CritRate);
            stats.critDamage = new FixedNumber(unitData.CritDamage);

            var attacker = unit.GetComponent<Attacker>();
            attacker.muzzle = new FixedVector2(1, 0) * new FixedNumber(unitData.BodyRadius);
            unit.GetComponent<BuffManager>();


            var bodyRadius = new FixedNumber(unitData.BodyRadius);
            unit.GetComponent<FCollider>().SetInfo(Box2dCollider.Shape.Circle, new Box2dCollider.Info {
                radius = bodyRadius,
                yOffset = FixedMath.Max(0, bodyRadius)
            });
            unit.GetComponent<ObjectSkin>().name = name;
            var context = new ObjectActionContext(game, unit);
            var fsmExe = unit.GetComponent<FsmExecutor>();
            fsmExe.context = context;
            var aiFSM = ActionFSMCreator.Create(unitData.Ai, context);
            fsmExe.Set(aiFSM);

            return unit;
        }

        public static void SetUpAbillties(IGameInterface game, FUnit unit, string[] skills, string[] traits) {
            foreach(var skill in skills) {
                CreateAbilities(game, unit, skill, 0);
            }
            if(traits == null) {
                return;
            }

            var unitTraits = unit.GetComponent<UnitTraits>();
            foreach (var str in traits) {
                var traitKey = str.Trim();
                unitTraits.TryAddTrait(traitKey);
            }
        }

        public static void SetUpAbillties(IGameInterface game, FUnit unit, string skill, int skillLevel, string[] traits) {
            CreateAbilities(game, unit, skill, skillLevel);

            if(traits == null) {
                return;
            }

            var unitTraits = unit.GetComponent<UnitTraits>();
            foreach (var str in traits) {
                var traitKey = str.Trim();
                unitTraits.TryAddTrait(traitKey);
            }
        }

        public static void CreateAbilities(IGameInterface game, FUnit unit, string skill, int skillLevel) {
            if (skill == null) {
                return;
            }
            var abilityExecutor = unit.GetComponent<AbilityExecutor>();
            var abilityName = skill.Trim();
            if(string.IsNullOrWhiteSpace(abilityName)) {
                return;
            }
            var AbilityData = game.configLoader.Get<CfgAbility>(abilityName);
            if (AbilityData != null) {
                var abilityType = Type.GetType($"MonsterRise.{abilityName}Ability");
                if (abilityType == null) {
                    FDebug.LogError($"Ability {abilityName} not found");
                    return;
                }
                var ability = (UnitAbility)Activator.CreateInstance(abilityType);
                if (ability != null) {
                    ability.game = game;
                    ability.level = skillLevel;
                    ability.owner = unit;
                    ability.duration = new FixedNumber(AbilityData.Duration);
                    ability.config = AbilityData;
                    ability.configParams = new Dictionary<string, FixedNumber>();
                    if (!string.IsNullOrEmpty(AbilityData.Attribute1)) {
                        ability.configParams.Add(AbilityData.Attribute1, new FixedNumber(AbilityData.Value1));
                    }

                    if (!string.IsNullOrEmpty(AbilityData.Attribute2)) {
                        ability.configParams.Add(AbilityData.Attribute2, new FixedNumber(AbilityData.Value2));
                    }

                    if (!string.IsNullOrEmpty(AbilityData.Attribute3)) {
                        ability.configParams.Add(AbilityData.Attribute3, new FixedNumber(AbilityData.Value3));
                    }

                    if (!string.IsNullOrEmpty(AbilityData.Attribute4)) {
                        ability.configParams.Add(AbilityData.Attribute4, new FixedNumber(AbilityData.Value4));
                    }

                    if (!string.IsNullOrEmpty(AbilityData.Attribute5)) {
                        ability.configParams.Add(AbilityData.Attribute5, new FixedNumber(AbilityData.Value5));
                    }

                    if (!string.IsNullOrEmpty(AbilityData.Attribute6)) {
                        ability.configParams.Add(AbilityData.Attribute6, new FixedNumber(AbilityData.Value6));
                    }

                    abilityExecutor.ability = ability;
                    ability.Init();
                }
                else {
                    FDebug.LogError($"ability {abilityName} config not found");
                }  
            }
        }
    }
}