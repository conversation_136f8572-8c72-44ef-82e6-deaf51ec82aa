using FixedNum;
using System.Collections.Generic;
using System.Linq;

namespace MonsterRise
{
    public class ObjectActionUtil {
        public static FixedVector2 AdjustMoveDirByBattleField(ObjectActionContext context, FixedVector2 moveDir){
            var currentPos = context.owner.transform.position;
            var battleFieldRect = context.battleFieldRect;
            if (!battleFieldRect.Intersect(new FixedCircle(currentPos, context.stats.bodyRadius))) {
                var disp = battleFieldRect.FindNearestPos(currentPos) - currentPos;
                if (disp != FixedVector2.zero) {
                    moveDir = disp.normalized;
                }
                return moveDir;
            }

            var predictDistance = FixedNumber.Clamp(context.stats.MoveSpeed * FixedNumber.oneTenth * 5, FixedNumber.oneTenth * 5, FixedNumber.oneTenth * 15);
            var line = new FixedLine(currentPos, currentPos + moveDir * predictDistance);
            if (!battleFieldRect.Intersect(new FixedCircle(line.end, FixedNumber.zero))) {
                battleFieldRect.FindNearestPointOnEdge(line.end, out var nearestPoint, out var normal);
                var angle = FixedVector2.SignedAngle(moveDir, normal);
                var angleSign = 1;
                if (angle == 180) {
                    angleSign = context.owner.id % 2 == 0 ? -1 : 1;
                }
                else {
                    angleSign = angle > 0 ? -1 : 1;
                }
                moveDir = FixedVector2.Rotate(normal, angleSign * 90);
            }
            return moveDir;
        }

        public static FixedVector2 CalculateFleeDirection(ObjectActionContext context) {
            var args = new ObjectHasThreat.ThreatArguments();
            args.context = context;
            args.threatMask = ObjectHasThreat.ThreatType.Character;
            args.isObjectThreat = (threatType, obj) => { 
                if (!(obj.GetComponent<UnitStats>() is {} unitStats)) {
                    return false;
                }

                if (unitStats.dead || UnitCampUtil.IsAlliance(context.stats.camp, unitStats.camp)) {
                    return false;
                }

                if(FixedVector2.Distance(unitStats.transform.position, context.owner.transform.position) > FixedNumber.Min(5, FixedNumber.percent * 85 * context.stats.attackRange) &&
                    unitStats.attackRange >= context.stats.attackRange &&
                     unitStats.attackRange > ObjectActionContext.StdMeleeAttackRange + FixedNumber.oneTenth * 10) {
                    return false;
                }

                if (!GameplayUtil.CanAttackReachTarget(unitStats, context.stats)) {
                    return false;
                }

                return true;
            };
            if (ObjectHasThreat.GetDirection(args, out var moveDir)) {
                return moveDir;
            }
            return FixedVector2.zero;
        }

        static FComponentsQuery projectileQuery = new FComponentsQuery(typeof(ProjectileStats));
        public static FixedVector2 CalculateDodgeDirection(ObjectActionContext context, FixedNumber deltaTime, FixedVector2 lastDodgeDir, FixedNumber dodgeCounterFactor, ref FixedNum.TimeCounter attensionUpdateCounter){
            if (attensionUpdateCounter.Update(deltaTime)) {
                attensionUpdateCounter.Reset();
                attensionUpdateCounter.interval = context.game.randGenerator.Range(FixedNumber.oneTenth * 5, FixedNumber.oneTenth * 13) * dodgeCounterFactor; // reaction time
            }
            else {
                return lastDodgeDir;
            }

            var projectiles = new List<FObject>();
            projectileQuery.DoQuery(context.game, (_, _)=>{
                var projectileStats = projectileQuery.GetData<ProjectileStats>(0);
                var projectile = projectileStats.owner as FProjectile;
                if (projectile.CheckHitable(context.hitTarget)) {
                    projectiles.Add(projectile);
                }
            });


            if (projectiles.Count > 0) {
                var predictionTime = context.game.randGenerator.Range(FixedNumber.oneTenth * 8, FixedNumber.oneTenth * 12); // 0.8 ~ 1.2 sec
                var moveDirs = new List<FixedVector2>();
                foreach (var projectile in projectiles) {
                    var stats = projectile.GetComponent<ProjectileStats>();
                    var length = stats.MoveSpeed * predictionTime;
                    var width = stats.BodyRadius * 2;
                    var obb = new FixedOBB {
                        center = projectile.transform.position + projectile.transform.angle.Vec2() * length / 2,
                        size = new FixedVector2(length, width),
                        rotation = projectile.transform.angle
                    };

                    if (obb.Overlaps(context.owner.transform.position, context.stats.bodyRadius)) {
                        var p = FixedVector2.ClosePointOnLine(projectile.transform.position, obb.center, context.owner.transform.position);
                        var disp = (p - context.owner.transform.position);
                        if (disp.Length() < FixedNumber.epsilon) {
                            moveDirs.Add(FixedVector2.Rotate((obb.center - projectile.transform.position).normalized, (context.game.randGenerator.Range(0, 2) < 1 ? -1 : 1) * 90));
                        }
                        else {
                            moveDirs.Add(disp.normalized);
                        }
                    }
                }

                if (moveDirs.Count > 0) {
                    var sum = FixedVector2.zero;
                    for (var i = 0; i < moveDirs.Count; ++i) {
                        sum += moveDirs[i];
                    }
                    lastDodgeDir = sum.normalized;
                    if (lastDodgeDir == FixedVector2.zero) {
                        lastDodgeDir = moveDirs[0];
                    }
                }
                else {
                    lastDodgeDir = FixedVector2.zero;
                }
            }
            return lastDodgeDir;
        }

        public static FixedVector2 CalculateDirectionToAvoidCorner(ObjectActionContext context, FixedNumber checkRange) {
            var battleFieldRect = context.battleFieldRect;
            var mask = battleFieldRect.IntersectOnEdge(new FixedCircle(context.owner.transform.position, checkRange));
            FixedVector2 dir = FixedVector2.zero;
            int detected = 0;
            for (var i = 0; i < 4; ++i) {
                if ((mask & (1 << i)) > 0) {
                    switch(i) {
                        case 0:
                            dir += FixedVector2.up;
                            break;
                        case 1:
                            dir += FixedVector2.left;
                            break;
                        case 2:
                            dir += FixedVector2.down;
                            break;
                        case 3:
                            dir += FixedVector2.right;
                            break;
                    }
                    ++detected;
                }
            }

            FixedVector2 result = FixedVector2.zero;

            if (detected > 1) {
                var checkMoveDirCount = System.Math.Min(context.lastMoveDirections.count, 5);

                var dir1 = dir;
                dir1.x = 0;
                dir1.Normalize();
                var v1 = FixedNumber.zero;
                for (var i = 0; i < checkMoveDirCount; ++i) {
                    v1 += FixedVector2.Dot(context.lastMoveDirections.Peek(i), dir1);
                }

                var dir2 = dir;
                dir2.y = 0;
                dir2.Normalize();
                var v2 = FixedNumber.zero;
                for (var i = 0; i < checkMoveDirCount; ++i) {
                    v2 += FixedVector2.Dot(context.lastMoveDirections.Peek(i), dir2);
                }

                if (v1 > v2) {
                    result = dir1;
                }
                else {
                    result = dir2;
                }
            }

            return result;
        }
    }
}
