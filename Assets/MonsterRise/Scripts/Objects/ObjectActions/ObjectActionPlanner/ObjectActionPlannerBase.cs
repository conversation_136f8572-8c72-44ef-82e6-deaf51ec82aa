using System.Collections.Generic;
using FixedNum.GOAP;
using FixedNum;

namespace MonsterRise
{
    public enum Goal { 
        DefeatEnemy = 1,
        Flee = 2,
        Dodge = 3,
        SneakToEnemy = 4,
        Protect = 5
    }

    public class ObjectActionBase : IGoapActionNode {
        public ObjectActionContext context;
        public System.Action onEnter;
        public System.Action onExit;
        public void OnEnter(){
            onEnter?.Invoke();
            Enter();
        }
        public bool OnUpdate(FixedNumber deltaTime) => Update(deltaTime);
        public void OnExit(){
            onExit?.Invoke();
            Exit();
        }

        protected virtual void Enter(){}
        protected virtual bool Update(FixedNumber deltaTime) => true;
        protected virtual void Exit(){}
    }

    public abstract class ObjectActionPlannerBase : IGoapPlanner
    {
        public System.Action<int, int> onGoalChanged;
        protected ObjectActionContext context;
        public int currentGoal { get; private set; }

        public ObjectActionPlannerBase SetContext(ObjectActionContext context) {
            this.context = context;
            Init();
            return this;
        }

        protected virtual void Init() {}

        public virtual void OnStartUp() { }


        public abstract int DetermineGoal(GoapPlan currentPlan, int prevGoal);

        public GoapPlan DeterminePlan(int prevGoal, int goal){
            var plan = OnDeterminePlan(prevGoal, goal);
            if (plan != null) {
                foreach(var action in plan.Actions) {
                    if (action is ObjectActionBase objectAction) {
                        objectAction.context = context;
                    }
                }
            }
            return plan;
        }

        protected abstract GoapPlan OnDeterminePlan(int prevGoal, int goal);

        public void OnGoalChanged(int goal) {
            onGoalChanged?.Invoke(currentGoal, goal);
            currentGoal = goal;
        }

        public void Update(FixedNumber deltaTime) {
            OnUpdate(deltaTime);
        }

        protected virtual void OnUpdate(FixedNumber deltaTime){}

        public static int AddGoal(ref int goalBitsMask, Goal goal) {
            goalBitsMask |= (1 << (int)goal);
            return goalBitsMask;
        }

        public static bool HasGoal(int goalBitsMask, Goal goal) {
            return (goalBitsMask & (1 << (int)goal)) > 0;
        }
    }
}
