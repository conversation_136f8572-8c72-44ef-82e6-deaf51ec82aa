using FixedNum;
using Sirenix.OdinInspector;
using System.Collections.Generic;
using UnityEngine;
using System.Linq;
using CommandUtil;

namespace MonsterRise {
    public class BattleCampStatistic {
        public Dictionary<MonsterData, int> monsterKillDict = new();
        public Dictionary<MonsterData, FixedNumber> monsterDamageDict = new();
        public Dictionary<MonsterData, FixedNumber> monsterHealingDict = new();
        public Dictionary<MonsterData, FixedNumber> monsterTakenDamageDict = new();
        public Dictionary<MonsterData, FixedNumber> monsterGetHealingDict = new();
    }
    public class BattleStatistic { 
        public BattleCampStatistic[] campStats;
        public float battleTime;
        public int winCamp;
        public bool draw;
    }
    
    public interface GPEventTypeIdAssignment { }
    public interface SkillIdAssignment { }
    public interface TraitIdAssignment { }
    public struct BattleStartEvent { }


    public class MonsterRiseGame : MonoBehaviour, IMonsterRiseGame {

        public float ObjectScale => 1;
#if UNITY_EDITOR
        public float timeScale = 1;
#endif

        public bool isBackground => _isBackground;

        [System.NonSerialized] public Dictionary<int, FixedNumber> monsterInflictDamage = new Dictionary<int, FixedNumber>();
        [System.NonSerialized] public Dictionary<int, FixedNumber> monsterTakeDamage = new Dictionary<int, FixedNumber>();
        [System.NonSerialized] public Dictionary<int, FixedNumber> monsterHealing = new Dictionary<int, FixedNumber>();
        [System.NonSerialized] public Dictionary<int, FixedNumber> monsterGetHealing = new Dictionary<int, FixedNumber>();
        [System.NonSerialized] public Dictionary<int, int> monsterKill = new Dictionary<int, int>();
        [System.NonSerialized] public List<Dictionary<int, MonsterRise.MonsterData>> monsterDataOfCampDict = new List<Dictionary<int, MonsterRise.MonsterData>>();
        [System.NonSerialized] public Dictionary<int, MonsterRise.MonsterData> monsterDataDict = new Dictionary<int, MonsterRise.MonsterData>();
        [System.NonSerialized] public Dictionary<MonsterRise.MonsterData, int> monsterDataRevertDict = new Dictionary<MonsterRise.MonsterData, int>();
        [System.NonSerialized] public Dictionary<MonsterData, int> pickedUpExpDict = new Dictionary<MonsterData, int>();

        class PendingChargeJoinFight {
            public FUnit monster;
            public int reservedUnitId;
            public IChargeTraitImplementation chargeTraitImpl;
        }
        Dictionary<int, PendingChargeJoinFight> pendingChargeJoinFightDict = new Dictionary<int, PendingChargeJoinFight>();

        public FixedNumber totalBattleTime => gameInst != null ? gameInst.timeSinceStart : 0;



        [System.NonSerialized] public int killMonster = 0;
        [System.NonSerialized] public int maxOneshotDamage;
        [System.NonSerialized] public FixedNumber totalTakeDamage;

        public event System.Action<FixedNumber> onGameStep;
        public System.Func<FixedNumber, bool> gameOverChecker;

        public event System.Action<DamageInfo,FixedNumber> onMonsterTakeDamage;


        float _elapsedTime;
        public static FixedNumber step = new FixedNumber("0.03");
        public static float stepFloat = (float)step;
        float _floatStep;
        // Start is called before the first frame update

        public IConfigLoaderProxy configLoader;

        Dictionary<int, ISkinnedObject> objectSkins = new Dictionary<int, ISkinnedObject>();
        public GameObject GetSkinnedObject(int id) {
            if (objectSkins.TryGetValue(id, out var skin) && skin != null) {
                return skin.gameObject;
            }
            return null;
        }

        public ISkinnedObject GetSkinnedObjectInterface(int id) {
            if (objectSkins.TryGetValue(id, out var skin) && skin != null) {
                return skin;
            }
            return null;
        }

        public GameInstance gameInst { get; set; } = new GameInstance();

        UnityEngine.Transform unitRoot;
        UnityEngine.Transform projectileRoot;
        UnityEngine.Transform trapRoot;
        GlobalThreatRecorder globalThreatRecorder;
        bool _isBackground;

        public void Init(bool hasTerrainBorder, IConfigLoaderProxy configLoader, uint randomSeed, bool isBackground = false){
            _floatStep = (float)step;
            _isBackground = isBackground;
            
            gameInst = new GameInstance();
            gameInst.gamestep = step;
            
            #if UNITY_EDITOR
            gameInst.DebugGlobalDamageFactor = FixedNumber.one;
            timeScale = 1;
            #endif

            gameInst.gravity = new FixedNumber(40);
            gameInst.battleFieldSize = new FixedVector2("22", "10");
            gameInst.battleFieldUnitSize = new FixedNumber("2");
            gameInst.hasTerrainBorder = hasTerrainBorder;
            gameInst.random.SetSeed(randomSeed);
            FixedNum.FDebug.log = UnityEngine.Debug.Log;
            FixedNum.FDebug.logError = UnityEngine.Debug.LogError;

            if (!isBackground) {
                gameInst.AddSystem(new MonsterRiseOccupationSystem());
                gameInst.AddSystem(new MonsterRiseCollisionSystem());
                gameInst.AddSystem(new MonsterRisePickupSystem());
                gameInst.AddSystem(new MonsterRiseBuffSystem());
            }
            

            globalThreatRecorder = gameInst.GetGlobalData<GlobalThreatRecorder>();
            gameInst.GetGlobalData<ActionContextGlobalData>().UpdateBeforeFrame(FixedNumber.zero); // get the data initialized

            gameInst.eventHub.AddListener<UnitExitBattleFieldEvent>(OnUnitExitBattleField);

            // global threat recorder
            // config
            FObjectFactory.configLoader = this.configLoader = configLoader;
            gameInst.configLoader = configLoader;

            gameInst.OnCreateObject += OnCreateObject;
            gameInst.OnDestoryObject += OnDestroyObject;
            if (!_isBackground) {
                FPhysicsUtil.collisionDetection = gameInst.GetSystem<MonsterRiseCollisionSystem>();
                gameInst.eventHub.AddListener<PostProjectileHit>(OnProjectileHit);
                gameInst.eventHub.AddListener<UnitChargeJoinFightEvent>(OnUnitChargeJoinFight);
                gameInst.eventHub.AddListener<UnitGetPickupEvent>(OnUnitPickup);
                gameInst.eventHub.AddListener<TraitChangedEvent>(OnTraitChanged);
            }

            unitRoot = new GameObject("units").transform;
            projectileRoot = new GameObject("projectiles").transform;
            trapRoot = new GameObject("traps").transform;
        }

        public void Pause() {
            gameInst.Pause();
        }

        public void Resume() {
            gameInst.Resume();
        }

        void OnContactBegin(Box2dCollider colliderA, Box2dCollider colliderB) {
        }

        void OnContactEnd(Box2dCollider colliderA, Box2dCollider colliderB) {
        }

        void OnDisable() {
            // do cleanup before destroy
            gameInst.Destroy();
            gameInst.OnCreateObject -= OnCreateObject;
            gameInst.OnDestoryObject -= OnDestroyObject;

            try {
                foreach (var kv in objectSkins) {
                    if (kv.Value != null) {
                        Destroy(kv.Value.gameObject);
                    }
                }
                objectSkins.Clear();
            }
            catch{}
            Box2dFixedUtil.DestroyWorld();
        }


        public event System.Action<FUnit> OnCreateUnitEvent;
        void OnCreateObject(FixedNum.FObject obj) {
            FUnit unit = obj as FUnit;
            if (unit != null) {
                var camp = obj.GetComponent<UnitStats>().camp;
                while (monsterDataOfCampDict.Count <= camp) {
                    monsterDataOfCampDict.Add(new Dictionary<int, MonsterRise.MonsterData>());
                }
                var monsterData = obj.GetComponent<ObjectCustomData>().data0 as MonsterData;
                if (monsterData != null) {
                    monsterDataOfCampDict[camp][unit.id] = monsterData;
                    var oldUnitId = monsterDataRevertDict.GetValueOrDefault(monsterData, 0);
                    if (oldUnitId > 0) {
                        monsterDataDict.Remove(oldUnitId);
                    }
                    monsterDataDict[unit.id] = monsterData;
                    monsterDataRevertDict[monsterData] = unit.id;
                }

                if (obj.GetComponent<HitUnit>() is {} hitUnit) {
                    hitUnit.AfterTakeDamage += globalThreatRecorder.AfterUnitTakeDamage;
                    hitUnit.AfterTakeDamage += OnMonsterTakeDamage;
                    hitUnit.OnGetHealing += globalThreatRecorder.OnUnitGetHealing;
                    hitUnit.OnGetHealing += OnMonsterGetHealing;
                    hitUnit.OnDie += globalThreatRecorder.OnUnitDie;
                }
                if (obj.GetComponent<BuffManager>() is {} buffManager) {
                    buffManager.OnAddBuff += globalThreatRecorder.OnInflictBuff;
                }
            }

            if (!_isBackground) {
                TryCreateObjectSkin(obj);
            }

            if (unit != null) {
                OnCreateUnitEvent?.Invoke(unit);
            }
        }

        void OnMonsterTakeDamage(MonsterRise.DamageInfo damageInfo, FixedNumber damage) {
            if (monsterTakeDamage.TryGetValue(damageInfo.target.owner.id, out var takenDamage)) {
                takenDamage += damage;
                monsterTakeDamage[damageInfo.target.owner.id] = takenDamage;
            } else if (damageInfo.source != null) {
                monsterTakeDamage[damageInfo.target.owner.id] = damage;
            }

            if (damageInfo.target.owner is FUnit tmpUnit && tmpUnit.stats.camp == 1) {
                totalTakeDamage += damage;
            }

            if (damageInfo.source != null) {
                var damageSourceObjectId = damageInfo.source.hitSourceObject.id;
                var sourceUnit = damageInfo.source.hitSourceObject as FUnit;
                if (sourceUnit != null && sourceUnit.stats.masterId > 0) {
                    damageSourceObjectId = sourceUnit.stats.masterId;
                    sourceUnit = gameInst.GetObject(damageSourceObjectId) as FUnit;
                }

                monsterInflictDamage[damageSourceObjectId] = monsterInflictDamage.GetValueOrDefault(damageSourceObjectId, 0) + damage;

                if (sourceUnit != null) {
                    if (sourceUnit.stats.camp == 1) {
                        maxOneshotDamage = Mathf.Max(maxOneshotDamage, (int)damageInfo.damage);
                    }

                    if (damageInfo.target.owner is FUnit targetUnit && targetUnit.stats.health.Value <= 0) {
                        if (sourceUnit.stats.camp == 1) {
                            killMonster++;
                        }

                        monsterKill[sourceUnit.id] = monsterKill.GetValueOrDefault(sourceUnit.id, 0) + 1;
                    }
                }
            }

            if (damageInfo.target.owner is FUnit u) {
                UpdateMonsterDataHp(u.id, u.stats);
            }

            SendMonsterGetHurtEvent(damageInfo, damage);
        }

        void SendMonsterGetHurtEvent(DamageInfo damageInfo, FixedNumber damage) {
            try{
                onMonsterTakeDamage?.Invoke(damageInfo, damage);
            } catch { }
        }

        void OnMonsterGetHealing(HealingInfo healingInfo, FixedNumber heal) {
            if (monsterGetHealing.TryGetValue(healingInfo.target.owner.id, out var takenHealing)) {
                takenHealing += heal;
                monsterGetHealing[healingInfo.target.owner.id] = takenHealing;
            }
            else if (healingInfo.source != null) {
                monsterGetHealing[healingInfo.target.owner.id] = heal;
            }

            if (healingInfo.source != null && monsterHealing.TryGetValue(healingInfo.source.hitSourceObject.id, out var inflictedHealing)) {
                inflictedHealing += heal;
                monsterHealing[healingInfo.source.hitSourceObject.id] = inflictedHealing;
            }
            else if (healingInfo.source != null) {
                monsterHealing[healingInfo.source.hitSourceObject.id] = heal;
            }

            if (healingInfo.target.owner is FUnit u) {
                UpdateMonsterDataHp(u.id, u.stats);
            }
        }
        

        void OnDestroyObject(FixedNum.FObject obj) {
            if (obj is FUnit) {
                globalThreatRecorder.OnUnitDie(obj);
            }
            if (obj.GetComponent<HitUnit>() is {} hitUnit) {
                hitUnit.AfterTakeDamage -= globalThreatRecorder.AfterUnitTakeDamage;
                hitUnit.AfterTakeDamage -= OnMonsterTakeDamage;
                hitUnit.OnGetHealing -= globalThreatRecorder.OnUnitGetHealing;
                hitUnit.OnGetHealing -= OnMonsterGetHealing;
                hitUnit.OnDie -= globalThreatRecorder.OnUnitDie;
            }

            if (obj.GetComponent<BuffManager>() is {} buffManager) {
                buffManager.OnAddBuff -= globalThreatRecorder.OnInflictBuff;
            }

            if (objectSkins.TryGetValue(obj.id, out var skinObj)) {
                objectSkins.Remove(obj.id);
                skinObj.onDeleteComplete = ()=> Destroy(skinObj.gameObject);
                skinObj.Delete();
            }

            if (obj is FUnit) {
                monsterDataDict.Remove(obj.id);
                var customData = obj.GetComponent<ObjectCustomData>();
                if (customData != null && customData.data0 is MonsterData monsterData) {
                    monsterDataRevertDict.Remove(monsterData);
                }
            }
        }

        void OnUnitExitBattleField(UnitExitBattleFieldEvent evData) {
            var monsterData = evData.unit.GetComponent<ObjectCustomData>().data0 as MonsterData;
            var camp = evData.unit.stats.camp;
            CreateReservedUnit(monsterData, camp);
        }

        void OnProjectileHit(PostProjectileHit evData) {
            try{
                if (objectSkins.TryGetValue(evData.projectile.id, out var skin)) {
                    var skinnedBullet = skin as SkinnedBullet;
                    if (skinnedBullet != null) {
                        skinnedBullet.OnHit();
                    }
                }
            }
            catch{}
        }

    
        void OnUnitChargeJoinFight(UnitChargeJoinFightEvent evData) {
            pendingChargeJoinFightDict[evData.unit.id] = new PendingChargeJoinFight {
                monster = evData.unit,
                reservedUnitId = evData.reservedUnitId,
                chargeTraitImpl = evData.traitImpl
            };
        }

        void OnUnitPickup(UnitGetPickupEvent evData) {
            var unit = evData.unit;
            var monsterData = GetMonsterData(unit.id);
            if (monsterData != null) {
                if (evData.pickup is ExpBall expBall) {
                    pickedUpExpDict[monsterData] = pickedUpExpDict.GetValueOrDefault(monsterData) + expBall.exp;
                }
            }
        }

        void OnTraitChanged(TraitChangedEvent evData) {
            var skinCharacter = objectSkins.GetValueOrDefault(evData.unit.id) as SkinnedCharacter;
            if (skinCharacter != null) {
                skinCharacter.OnTraitChanged(evData.add, evData.traitId);
            }
        }


        FComponentsQuery abilityQuery = new FComponentsQuery(typeof(AbilityExecutor), typeof(UnitTraits));
        public void GameInit() {
            // create physics world
            if (!_isBackground) {
                Box2dFixedUtil.CreateWorld(new FixedNumber("0"), new FixedNumber("0"));
                var contactListener = new Box2dContactListener {
                    onBegin = OnContactBegin,
                    onEnd = OnContactEnd
                };
                Box2dFixedUtil.SetContactListener(contactListener);
            }
            gameInst.Init();
            
        }

        public void GameStart() {
            SetupInitObjectActionWait();
            gameInst.Start();
            gameInst.eventHub.Raise(new BattleStartEvent());
        }

        void SetupInitObjectActionWait() {
            abilityQuery.DoQuery(gameInst, (_, _) => {
                var abilityExe = abilityQuery.GetData<AbilityExecutor>(0);
                var unitTraits = abilityQuery.GetData<UnitTraits>(1);
                if (abilityExe.ability != null && abilityExe.IsBattleCry && new FixedNumber(abilityExe.ability.config.Duration) > 0) {
                    abilityQuery.Halt();
                    gameInst.GetGlobalData<ActionContextGlobalData>().initWaitFrames = (FixedNumber.half / gameInst.gamestep).Round();
                }

                for (var i = 0; i < unitTraits.Traits.Count; ++i) {
                    if (unitTraits.Traits[i].config.Tag.Contains("battleStart")) {
                        abilityQuery.Halt();
                        gameInst.GetGlobalData<ActionContextGlobalData>().initWaitFrames = (FixedNumber.half / gameInst.gamestep).Round();
                        break;
                    }
                }
            });
        }
        
        public FUnit CreateUnit(MonsterRise.MonsterData monsterData, int camp, FixedVector2 pos, int dir, System.Func<string, bool> ignoreAbility = null) {
            var skill = monsterData.skill;
            if (_isBackground) {
                monsterData.skill = null;
            }
            var unit = MonsterRiseUtil.CreateMonster(gameInst, monsterData, camp, pos, dir, ignoreAbility);
            monsterData.skill = skill;
            return unit;
        }
        

        public void CreateTrap(string trapName, FixedVector2 pos){
            try {
                var obj = FObjectFactory.CreateObject<Trap>(gameInst, trapName);
                obj.transform.position = pos;
            }
            catch{
                UnityEngine.Debug.Log("Trap not found: " + trapName);
            }
        }

        public void CreateReservedUnit(MonsterData monsterData, int camp) {
            var componentTypes = new System.Type[] { typeof(ReservedUnit), typeof(ChargePoints), typeof(ChargeTraitPreparation) };
            var archetype = FArchetype.Get(componentTypes);
            var obj = new FObject(gameInst, archetype);
            obj.category = (int)ObjectCategory.Reserved;
            var reservedUnit = obj.GetComponent<ReservedUnit>();
            reservedUnit.camp = camp;
            reservedUnit.monsterData = monsterData;
            var chargeTrait = obj.GetComponent<ChargeTraitPreparation>();
            if (monsterData.HasChargeTrait()) {
                chargeTrait.traitId = monsterData.GetChargeTrait();
            }
            gameInst.RegisterObject(obj);
            monsterDataDict[obj.id] = monsterData;
            monsterDataRevertDict[monsterData] = obj.id;
        }

        public void AddTrait(int unitId, string traitId, bool dontModifyData) {
            var cfg = configLoader.Get<cfg.MonsterRise.CfgTrait>(traitId);
            if (cfg != null) {
                var monsterData = GetMonsterData(unitId);
                var add = true;
                if (!dontModifyData) {
                    add = !monsterData.HasTrait(cfg.Trait);
                    if (add) {
                        monsterData.traits.Add(new TraitData { key = cfg.Id, innate = false });
                    }
                }
                if (add) {
                    var unit = gameInst.GetObject(unitId) as FUnit;
                    if (unit != null) {
                        unit.GetComponent<UnitTraits>().TryAddTrait(cfg.Id);
                    }
                }
            }
        }

        public void RemoveTrait(int unitId, string traitId) {
            var cfg = configLoader.Get<cfg.MonsterRise.CfgTrait>(traitId);
            if (cfg != null) {
                var monsterData = GetMonsterData(unitId);
                var unit = gameInst.GetObject(unitId) as FUnit;
                if (unit != null) {
                    unit.GetComponent<UnitTraits>().RemoveTrait(cfg.Id, false);
                }
            }
        }

        void UpdateMonsterDataHp(int objId, UnitStats unitStats) {
            if (monsterDataDict.TryGetValue(objId, out var monsterData)) {
                monsterData.hpPercent = unitStats.HpPercent;
            }
        }

        public MonsterRise.MonsterData GetMonsterData(int objId) {
            if (monsterDataDict.TryGetValue(objId, out var monsterData)) {
                return monsterData;
            }

            var unit = gameInst.GetObject(objId) as FUnit;
            if (unit != null) {
                var cfg = configLoader.Get<cfg.MonsterRise.CfgUnit>(unit.stats.unitName);
                return MonsterRise.MonsterData.CreateFromCfg(cfg);
            }

            return null;
        }

        public int GetUnitId(MonsterRise.MonsterData monsterData) {
            if (monsterDataRevertDict.TryGetValue(monsterData, out var unitId)) {
                return unitId;
            }
            return -1;
        }

        public FUnit GetUnit(MonsterRise.MonsterData monsterData) {
            var unitId = -1;
            foreach (var kv in monsterDataDict) {
                if (kv.Value == monsterData) {
                    unitId = kv.Key;
                    break;
                }
            }

            if (unitId >= 0) {
                return gameInst.GetObject(unitId) as FUnit;
            }

            return null;
        }


        class MockHitter : IHitSource {
            public MockHitter(IGameInterface game) {
                obj = new FObject(game, FArchetype.Get());
            }

            FObject obj;
            public FObject owner => obj;
            public FObject hitSourceObject => owner;
            
            public bool CheckHitable(IHitTarget target) { return false; }

            public bool CheckCrit(out FixedNumber critDamageFactor) {
                critDamageFactor = 1;
                return false;
            }

            public FixedNumber damageMultiply;
            public FixedNumber DamageMultiply { get => damageMultiply; set => damageMultiply = value; }
            public bool Hit(FObject source, FixedVector2 dir, IHitTarget target) {
                return true;
            }
        }

        [Button]
        void CreateBullet(string bulletName, int camp) {
            var obj = FObjectFactory.CreateObject<FProjectile>(gameInst, bulletName);
            obj.InitAtPos(FixedVector2.zero, FixedVector2.zero, new MockHitter(gameInst));
        }


        [Button]
        void DebugAddBuff(int objectId, string buffId){
            var obj = gameInst.GetObject(objectId);
            if(obj != null && obj.GetComponent<BuffManager>() is { } buffManager){
                buffManager.AddBuff(null, buffId);
            }
        }

        [Button]
        void DebugFindFireBarrel(Vector2 pos){
            var mask = CollisionDef.GetMask(ObjectCategory.Character, ObjectCategory.Trap);
            var objects = FixedNum.FPhysicsUtil.CircleCastObject(new FixedVector2(pos.x.ToString(), pos.y.ToString()), 3, mask, o => {
                if (!(o.GetComponent<IHitTarget>() is { } hitTarget) || !hitTarget.IsValid()) {
                    return false;
                }

                UnityEngine.Debug.Log(o.id);
                return true;
            });
        }

        [Button]
        public void DebugAngle(Vector2 dir) {
            var fv = new FixedVector2(dir.x.ToString(), dir.y.ToString());
            var angle = fv.SignedAngle();
            UnityEngine.Debug.Log((float)(new FixedNumber { raw = -4294967297 }));
            UnityEngine.Debug.Log((float)(new FixedNumber { raw = -4294967298 }).Acos());
            var a = new FixedNumber(-1);
            UnityEngine.Debug.Log((float)a.Acos());
            UnityEngine.Debug.Log((float)angle);
        }

        [Button]
        public void TestUI() {
            var prefab = ResourcesUtil.Load<GameObject>("MonsterRise/Prefab/UI/window_monster_rise_event_loot.prefab");
            var loots = new List<System.Tuple<string, string, int>> { 
                System.Tuple.Create("monster", "Monster1", 33),
                System.Tuple.Create("", "food", 10),
                System.Tuple.Create("", "shell", 4),
            };
            UIFramework.UIManager.Inst.OpenUIView<UIMonsterRiseEventLoot>(prefab, new object[] { configLoader, loots });
        }


#if  UNITY_EDITOR
        GamePlayEventLoop testGamePlayEventLoop;
        [Button]
        public void TestPushGamePlayEvent(string evKey) {
            if (testGamePlayEventLoop == null) {
                testGamePlayEventLoop = new GamePlayEventLoop();
                testGamePlayEventLoop.Start(GameObject.FindObjectOfType<SceneMonsterRise>().gameMode, 
                    new FixedNum.FixedRandom((uint)UnityEngine.Random.Range(0, int.MaxValue)));
                SceneMonsterRise.Inst.gameMode.eventHub.AddListener<MonsterRiseExecuteEventStart>(TestOnGamePlayEventStart);
                SceneMonsterRise.Inst.gameMode.eventHub.AddListener<MonsterRiseExecuteEventEnd>(TestOnGamePlayEventEnd);
            }
            testGamePlayEventLoop.PushEvent(evKey);
        }

        void TestOnGamePlayEventStart(MonsterRiseExecuteEventStart evData) {
            UnityEngine.Debug.Log(evData.eventContext.eventKey + "  start");
        }

        void TestOnGamePlayEventEnd(MonsterRiseExecuteEventEnd evData) {
            UnityEngine.Debug.Log(evData.eventContext.eventKey + "  end");
        }

        [Button]
        public void TestUpdateGamePlayEventLoop() {
            if (testGamePlayEventLoop != null) {
                testGamePlayEventLoop.Update();
            }
        }

        [Button]
        public void TestGamePlayEventLoopSelectMonsters(int id) {
            if (testGamePlayEventLoop != null) {
                if (testGamePlayEventLoop.GetLastSelectedMonsters() == null) {
                    testGamePlayEventLoop.SetSelectedMonsters(new List<int>{ id });
                }
                else {
                    testGamePlayEventLoop.GetLastSelectedMonsters().Add(id);
                }
            }
        }

        [Button]
        public void TestGamePlayEventLoopSelectItem(int idx) {
            SceneMonsterRise.Inst.gameMode.eventHub.Raise(new MonsterRiseEventSelection {
                selectionIndex = idx
            });
        }

        [Button]
        public void ResetGamePlayEventLoop() {
            if (testGamePlayEventLoop != null) {
                testGamePlayEventLoop.Stop();
            }
            testGamePlayEventLoop = null;
            SceneMonsterRise.Inst.gameMode.eventHub.RemoveListener<MonsterRiseExecuteEventStart>(TestOnGamePlayEventStart);
            SceneMonsterRise.Inst.gameMode.eventHub.RemoveListener<MonsterRiseExecuteEventEnd>(TestOnGamePlayEventEnd);
        }

        
#endif

        public GameObject debugAudioPos;
        public AudioClip debugAudioClip;
        [Button]
        void DebugAudio(){
            AudioUtil.PlaySound(debugAudioClip);
        }

        // Update is called once per frame
        void Update() {
            UpdateGame(Time.deltaTime);
        }

        FComponentsQuery colliderQuery = new FComponentsQuery(typeof(FCollider));


        void UpdateGame(float dt) {
            if (_isBackground) {
                return;
            }
            #if UNITY_EDITOR
            dt *= timeScale;
            #endif
            _elapsedTime += dt;
            while (_elapsedTime >= _floatStep) {
                _elapsedTime -= (float)step;
                GameStep(false);
            }
        }

        public void GameStep(bool background) {
            gameInst.UpdateFrame(step);
            if (!background) {
                onGameStep?.Invoke(step);
                colliderQuery.DoQuery(gameInst, (_, _) =>{
                    var collider = colliderQuery.GetData<FCollider>(0);
                    if (collider.owner.isAwakeAndActive) {
                        collider.UpdateColliderTransform(step);
                    }
                });
                Box2dFixedUtil.Step(step);
                if (gameOverChecker != null && !gameOverChecker(step)) {
                    gameOverChecker = null;
                    gameInst.gameOver = true;
                }

                foreach(var skinObj in objectSkins.Values) {
                    skinObj.GameStep();
                }
            }
        }



        void TryCreateObjectSkin(FObject obj) {
            var objSkin = obj.GetComponent<ObjectSkin>();
            GameObject skinnedObj = null;
            if (objSkin != null) {
                if (obj.category == (int)ObjectCategory.Character) {
                    skinnedObj = CreateSkinnedCharacter(obj, objSkin);
                    if (pendingChargeJoinFightDict.TryGetValue(obj.id, out var pending)) {
                        var traitImpl = pending.chargeTraitImpl;
                        if (traitImpl != null) {
                            skinnedObj.GetComponent<SkinnedCharacter>().CreateChargeTraitActor(traitImpl, pending.reservedUnitId);
                        }
                        pendingChargeJoinFightDict.Remove(obj.id);
                    }
                } 
                else if (obj.category == (int)ObjectCategory.Projectile) {
                    skinnedObj = new GameObject();
                    skinnedObj.name = ((ObjectCategory)obj.category) + "_" + (obj as FProjectile).stats.name + "_" + obj.id;
                    var skinnedBullet = skinnedObj.AddComponent<SkinnedBullet>();
                    skinnedBullet.objectSkin = objSkin;
                    skinnedObj.transform.SetParent(projectileRoot);
                    skinnedBullet.monsterRiseGame = this;
                    skinnedBullet.Init();
                }
                else if(obj.category == (int)ObjectCategory.Trap){
                    skinnedObj = new GameObject();
                    skinnedObj.name = ((ObjectCategory)obj.category) + "_" + obj.id;
                    skinnedObj.AddComponent<SkinnedTrap>().objectSkin = objSkin;
                    skinnedObj.transform.SetParent(trapRoot);
                }
                else if (obj.category == (int)ObjectCategory.Minion) {
                    if (obj is FUnit) {
                        skinnedObj = CreateSkinnedCharacter(obj, objSkin);
                    }
                    else {
                        skinnedObj = CreateOtherMinion(obj, objSkin);
                    }
                }
                else if (obj.category == (int)ObjectCategory.Powerup) {
                    skinnedObj = CreatePowerup(obj, objSkin);
                }

                if (skinnedObj != null) {
                    skinnedObj.transform.position = obj.transform.position.Vec2();
                    var skinnedObjInterface = skinnedObj.GetComponent<ISkinnedObject>();
                    objectSkins.Add(obj.id, skinnedObjInterface);
                    skinnedObjInterface.monsterRiseGame = this;
                }
            }
        }

        GameObject CreateSkinnedCharacter(FObject obj, ObjectSkin objSkin) { 
            var skinnedObj = new GameObject();
            skinnedObj.name = ((ObjectCategory)obj.category) + "_" + (obj as FUnit).stats.unitName + "_" + obj.id;
            var skinnedChar = skinnedObj.AddComponent<SkinnedCharacter>();
            skinnedChar.objectSkin = objSkin;
            skinnedChar.monsterRiseGame = this;
            skinnedChar.SetUp();
            skinnedObj.transform.SetParent(unitRoot);
            return skinnedObj;
        }

        GameObject CreateOtherMinion(FObject obj, ObjectSkin objSkin) {
            var skinnedObj = new GameObject();
            skinnedObj.name = ((ObjectCategory)obj.category) + "_" + obj.id;
            var companionStriker = skinnedObj.AddComponent<SkinnedCompanionStriker>();
            companionStriker.objSkin = objSkin;
            companionStriker.monsterRiseGame = this;
            skinnedObj.transform.SetParent(unitRoot);
            return skinnedObj;
        }

        GameObject CreatePowerup(FObject obj, ObjectSkin objSkin) {
            var skinnedObj = new GameObject();
            skinnedObj.name = ((ObjectCategory)obj.category) + "_" + obj.id;
            var skin = skinnedObj.AddComponent<SkinnedPowerup>();
            skin.skin = objSkin;
            skin.monsterRiseGame = this;
            skinnedObj.transform.SetParent(unitRoot);
            return skinnedObj;
        }

        FComponentsQuery unitStatsQuery = new FComponentsQuery(typeof(UnitStats));

        public bool IsAliveObjectOfCampExists(int camp){
            bool ret = false;
            unitStatsQuery.DoQuery(gameInst, (_, _)=>{
                if (gameInst.IsSpawningObject) {
                    ret = true;
                }
                if (ret) {
                    return;
                }
                var stats = unitStatsQuery.GetData<UnitStats>(0);
                if (stats.camp == camp && !stats.dead) {
                    ret = true;
                    return;
                }
            });
            return ret;
        }

        public void ForeachAliveUnit(int camp, System.Action<UnitStats> action) {
            unitStatsQuery.DoQuery(gameInst, (_, _)=>{
                var stats = unitStatsQuery.GetData<UnitStats>(0);
                if (stats.camp == camp && !stats.dead) {
                    action(stats);
                }
            });
        }

        public bool IsObjectDead(int objectId) {
            var stats = gameInst.GetObject(objectId).GetComponent<UnitStats>();
            return stats.dead;
        }

        public bool IsCampOfObjectTheOneAlive(int objectId) {
            var stats = gameInst.GetObject(objectId).GetComponent<UnitStats>();
            var camp = stats.camp;
            var count1 = 0;
            var count2 = 0;
            unitStatsQuery.DoQuery(gameInst, (_, _)=>{
                var stats = unitStatsQuery.GetData<UnitStats>(0);
                if (!stats.dead) {
                    if (stats.camp == camp) {
                        count1++;
                    } else {
                        count2++;
                    }
                }
            });
            return count1 > 0 && count2 == 0;
        }

        public void StopAllMonsterAI() {
            var query = new FComponentsQuery(typeof(FsmExecutor));
            query.DoQuery(gameInst, (_, _) => {
                var fsmExe = query.GetData<FsmExecutor>(0);
                fsmExe.pause = true;
            });
        }


        public BattleStatistic ExportBattleStatistic() {
            var ret = new BattleStatistic() {
                battleTime = (float)totalBattleTime,
                campStats = new BattleCampStatistic[2] {
                    new BattleCampStatistic(),
                    new BattleCampStatistic()
                }
            };

            for (var camp = 1; camp <= 2; ++camp) {
                if (monsterDataOfCampDict.Count <= camp) {
                    continue;
                }
                var monsterDatas = monsterDataOfCampDict[camp];
                var campBattleStat = ret.campStats[camp - 1];
                foreach(var kv in monsterDatas) {
                    var monsterData = kv.Value;
                    var unitId = kv.Key;
                    if (monsterKill.TryGetValue(unitId, out var kill)) {
                        campBattleStat.monsterKillDict[monsterData] = kill;
                    }

                    if (monsterInflictDamage.TryGetValue(unitId, out var damage)) {
                        campBattleStat.monsterDamageDict[monsterData] = damage;
                    }

                    if (monsterHealing.TryGetValue(unitId, out var healing)) {
                        campBattleStat.monsterHealingDict[monsterData] = healing;
                    }

                    if (monsterTakeDamage.TryGetValue(unitId, out var takenDamage)) {
                        campBattleStat.monsterTakenDamageDict[monsterData] = takenDamage;
                    }

                    if (monsterGetHealing.TryGetValue(unitId, out var getHealing)) {
                        campBattleStat.monsterGetHealingDict[monsterData] = getHealing;
                    }
                }
            }
            
            return ret;
        }

        #if UNITY_EDITOR
        FComponentsQuery debugUnitStatsHitQuery = new FComponentsQuery(typeof(UnitStats), typeof(IHitTarget));
        public void DebugTryToKillCamp(int camp) {
            debugUnitStatsHitQuery.DoQuery(gameInst, (_, _)=>{
                var stats = debugUnitStatsHitQuery.GetData<UnitStats>(0);
                var hitTarget = debugUnitStatsHitQuery.GetData<IHitTarget>(1);
                if (stats.camp == camp && !stats.dead) {
                    var dmgInfo = new DamageInfo { damage = 9999999 };
                    dmgInfo.target = hitTarget;
                    hitTarget.TakeDamage(dmgInfo);
                }
            });
        }

        [Sirenix.OdinInspector.Button]
        public void TestPathFinder(Vector2 start, Vector2 end) {
            var sys = new MonsterRiseOccupationSystem();
            sys.gameInterface = gameInst;
            sys.Start();
            var fStart = new FixedVector2(start.x.ToString(), start.y.ToString());
            var fEnd = new FixedVector2(end.x.ToString(), end.y.ToString());
            sys.ConvertPositionToGridIndex(fStart, out var startX, out var startY);
            sys.ConvertPositionToGridIndex(fEnd, out var endX, out var endY);
            if (sys.FindPath(sys.MakeGrid(startX, startY), sys.MakeGrid(endX, endY), 1)) {
                var path = sys.GetPath(sys.MakeGrid(startX, startY));
                for (var i = 0; i < path.Count; ++i) {
                    var x = path[i].x;
                    var y = path[i].y;
                    Debug.Log(x + " " + y);
                }
            }
        }

        [Sirenix.OdinInspector.Button]
        public void TestCreateExpBall(Vector2 pos, int exp) {
            MonsterRiseUtil.CreateExpBall(gameInst, new FixedVector2(pos.x.ToString(), pos.y.ToString()), exp);
        }

        #endif
    }
}