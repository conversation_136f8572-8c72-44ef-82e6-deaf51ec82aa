using FixedNum;

namespace MonsterRise {
    public static class UnitCampUtil {
        public enum TargetType {
            Hostile,
            Alliance,
            Any,
        }

        public static bool IsHostile(int campA, int campB) {
            return campA != campB;
        }

        public static bool IsAlliance(int campA, int campB) {
            return !IsHostile(campA, campB);
        }

        public static bool CompareCamp(TargetType targetType, int campA, int campB) {
            switch (targetType) {
                case TargetType.Hostile:
                    return IsHostile(campA, campB);
                case TargetType.Alliance:
                    return IsAlliance(campA, campB);
            }
            return false;
        }

        static OptimizedList<FUnit> searchUnitResult = new(true);
        public static OptimizedList<FUnit> FindUnitsInRange(int camp, FixedVector2 position, int range, TargetType targetType, bool includeInvisibleTarget) {
            searchUnitResult.Clear();
            var list = FPhysicsUtil.collisionDetection.FindUnitsInRange(position, range);
            for (var i = 0; i < list.Count; ++i) {
                var obj = list[i];
                if (!(obj is FUnit unit)) {
                    continue;
                }
                if (!unit.IsValid()) {
                    continue;
                }
                if (!unit.collider.enabled || unit.stats.invincible) {
                    continue;
                }

                if (!includeInvisibleTarget && unit.stats.invisible) {
                    continue;
                }

                if (targetType != TargetType.Any) {
                    if (targetType == TargetType.Hostile && !IsHostile(unit.stats.camp, camp)) {
                        continue;
                    }
                    if (targetType == TargetType.Alliance && !IsAlliance(unit.stats.camp, camp)) {
                        continue;
                    }
                }
                searchUnitResult.Add(unit);
            }
            return searchUnitResult;
        }

        public static OptimizedList<FUnit> FindNearbyUnits(int camp, FixedVector2 position, TargetType targetType, bool includeInvisibleTarget) {
            return InnerFindNearbyUnits(camp, position, targetType, includeInvisibleTarget, false, false);
        }

        public static OptimizedList<FUnit> FindNearbyUnitsIgnoreColliderState(int camp, FixedVector2 position, TargetType targetType, bool includeInvisibleTarget) {
            return InnerFindNearbyUnits(camp, position, targetType, includeInvisibleTarget, true, false);
        }

        static OptimizedList<FUnit> InnerFindNearbyUnits(int camp, FixedVector2 position, TargetType targetType, bool includeInvisibleTarget, bool ignoreColliderState, bool ignoreInvincibleState) {
            searchUnitResult.Clear();
            var list = FPhysicsUtil.collisionDetection.FindUnitsNearby(position);
            for (var i = 0; i < list.Count; ++i) {
                var obj = list[i];
                if (!(obj is FUnit unit)) {
                    continue;
                }
                if (!unit.IsValid()) {
                    continue;
                }

                if (!ignoreInvincibleState && unit.stats.invincible) {
                    continue;
                }

                if (!ignoreColliderState && !unit.collider.enabled) {
                    continue;
                }

                if (!includeInvisibleTarget && unit.stats.invisible) {
                    continue;
                }
                if (targetType == TargetType.Hostile && !IsHostile(unit.stats.camp, camp)) {
                    continue;
                }
                if (targetType == TargetType.Alliance && !IsAlliance(unit.stats.camp, camp)) {
                    continue;
                }
                searchUnitResult.Add(unit);
            }
            return searchUnitResult;
        }

        public static FUnit FindUnitAtGrid(int camp, FixedVector2 position, TargetType targetType, bool includeInvisibleTarget) {
            searchUnitResult.Clear();
            var list = FPhysicsUtil.collisionDetection.FindUnitsInRange(position, 0);
            for (var i = 0; i < list.Count; ++i) {
                var obj = list[i];
                if (!(obj is FUnit unit)) {
                    continue;
                }
                if (!unit.IsValid()) {
                    continue;
                }
                if (!unit.collider.enabled || unit.stats.invincible) {
                    continue;
                }
                if (!includeInvisibleTarget && unit.stats.invisible) {
                    continue;
                }
                if (targetType == TargetType.Hostile && !IsHostile(unit.stats.camp, camp)) {
                    continue;
                }
                if (targetType == TargetType.Alliance && !IsAlliance(unit.stats.camp, camp)) {
                    continue;
                }
                searchUnitResult.Add(unit);
                break;
            }

            if (searchUnitResult.Count > 0) {
                return searchUnitResult[0];
            }
            return null;
        }

        public static OptimizedList<FUnit> FindUnitsAtGrid(int camp, FixedVector2 position, TargetType targetType, bool includeInvisibleTarget) {
            searchUnitResult.Clear();
            var list = FPhysicsUtil.collisionDetection.FindUnitsInRange(position, 0);
            for (var i = 0; i < list.Count; ++i) {
                var obj = list[i];
                if (!(obj is FUnit unit)) {
                    continue;
                }
                if (!unit.IsValid()) {
                    continue;
                }
                if (!unit.collider.enabled || unit.stats.invincible) {
                    continue;
                }
                if (!includeInvisibleTarget && unit.stats.invisible) {
                    continue;
                }
                if (targetType == TargetType.Hostile && !IsHostile(unit.stats.camp, camp)) {
                    continue;
                }
                if (targetType == TargetType.Alliance && !IsAlliance(unit.stats.camp, camp)) {
                    continue;
                }
                searchUnitResult.Add(unit);
            }
            return searchUnitResult;
        }

        public static FUnit FindUnitAtPosition(int camp, FixedVector2 position, FixedNumber radius, TargetType targetType, bool includeInvisibleTarget) {
            var objs = FixedNum.FPhysicsUtil.CircleCastObject(position, radius, CollisionDef.GetMask(ObjectCategory.Character));
            for (var i = 0; i < objs.Count; ++i) {
                var obj = objs[i] as FUnit;
                if (obj != null && obj.IsValid() && CompareCamp(targetType, camp, obj.stats.camp)) {
                    return obj;
                }
            }
            return null;
        }

        public static bool IsUnitIsolated(FUnit unit) {
            var list = FPhysicsUtil.collisionDetection.FindUnitsNearby(unit.transform.position);
            for (var i = 0; i < list.Count; ++i) {
                var obj = list[i];
                if (!(obj is FUnit otherUnit)) {
                    continue;
                }
                if (!otherUnit.IsValid()) {
                    continue;
                }
                if (!unit.collider.enabled) {
                    continue;
                }
                if (otherUnit == unit) {
                    continue;
                }
                if (!IsAlliance(unit.stats.camp, otherUnit.stats.camp)) {
                    continue;
                }
                return false;
            }
            return true;
        }
    }
}