
using UnityEngine;
using UnityEngine.UI;
using cfg.MonsterRise;
using System.Collections.Generic;
using DG.Tweening;
using RGScript.Data;
using CommandUtil;
using System.Linq;

namespace MonsterRise {
    public class WindowMonsterChest {

        static List<string> FreeEmblems = new List<string> { 
            "Relay"
        };

        FixedNum.IConfigLoaderProxy configLoader;
        Transform transform;
        GameObject unlockOne;
        GameObject unlockTen;
        GameObject btnUnlock;



        GameObject costChest;
        GameObject costChestTen;
        GameObject coin;


        class RewardItem {
            public cfg.MonsterRise.CfgCollect item;
            public int refund;
        }
        private List<RewardItem> rewardList;
        private bool _showMonsterUpdate;
        private int _costOfChest = 500;
        int refundGem = 0;

        int timesLimit;

        UiUtil.UIEventMutex mutex = new UiUtil.UIEventMutex();

        public void Show(GameObject obj, FixedNum.IConfigLoaderProxy loader, System.Action onClose) {
            configLoader = loader;
            transform = obj.transform;
            UiUtil.AddClickEventToRectTrans(transform.Find("back") as RectTransform, () => {
                RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Button);
                onClose();
                GameObject.Destroy(obj);
                if (tmpUICanvasObj != null) {
                    GameObject.Destroy(tmpUICanvasObj);
                    tmpUICanvasObj = null;
                }
            });

            _costOfChest = MonsterRiseUtil.GetConstInt("OpenMonsterChestPrice");
            coin = transform.Find("coin").gameObject;
            costChest = transform.Find("content/Viewport/button_unlock_once/content_price/coin").gameObject;
            transform.Find("content/Viewport/text_one").GetComponent<Text>().text = 
                string.Format(I2.Loc.ScriptLocalization.Get("monsrise/open_monster_chest", "开启{0}次"), 1);
            transform.Find("content/Viewport/button_unlock_once/content_num/num").GetComponent<Text>().text = "1";
            costChest.GetComponent<Text>().text = $"{_costOfChest}";
            costChestTen = transform.Find("content/Viewport/button_unlock_ten/content_price/coin").gameObject;
            transform.Find("content/Viewport/text_ten").GetComponent<Text>().text = 
                string.Format(I2.Loc.ScriptLocalization.Get("monsrise/open_monster_chest", "开启{0}次"), 10);
                transform.Find("content/Viewport/button_unlock_ten/content_num/num").GetComponent<Text>().text = "10";
            costChestTen.GetComponent<Text>().text = $"{_costOfChest * 10}";

            unlockOne = transform.Find("content/Viewport/button_unlock_once").gameObject;
            unlockOne.SetActive(true);
            unlockTen = transform.Find("content/Viewport/button_unlock_ten").gameObject;
            unlockOne.SetActive(true);
            UpdateCoinText();

            UiUtil.AddClickEventToRectTrans(unlockOne.transform as RectTransform, ClickUnlockOnce);
            UiUtil.AddClickEventToRectTrans(unlockTen.transform as RectTransform, ClickUnlockTen);

            timesLimit = MonsterRiseUtil.GetConstInt("OpenMonsterChestTimesLimit");
            transform.Find("probabilityStatement").gameObject.SetActive(LanguageUtil.IsChinaMainland());
            var probabilityStatementText = transform.Find("probabilityStatement/Viewport/Content/text").GetComponent<Text>();
            probabilityStatementText.text = string.Format(probabilityStatementText.text, timesLimit);

            UpdateTimesLimit();

            UIPointerEventHandle.SetClickCallback(transform.Find("coin"), () => {
                OpenGemShop(transform.Find("coin"), false);
            });
        }

        void ClickUnlockOnce() {
            if (LanguageUtil.IsChinaMainland()) {
                DisplayConfirmDialog(1);
            } 
            else {
                UnlockMonsterChest(unlockOne.transform, 1);
            }
        }

        void ClickUnlockTen() {
            if (LanguageUtil.IsChinaMainland()) {
                DisplayConfirmDialog(10);
            } 
            else {
                UnlockMonsterChest(unlockTen.transform, 10);
            }
        }

        int _confirmOpenChestTimes;
        void DisplayConfirmDialog(int times) {
            if (TimesRemain() < times) {
                UiUtil.GraphicBlink(transform.Find("content/Viewport/timesLimit").GetComponent<Text>(), Color.white, Color.red, ref textBlinkSeq);
                return;
            }
            var dialogObj = transform.Find("confirmDialog").gameObject;
            dialogObj.SetActive(true);
            transform.Find("confirmDialog/panel/desc/text").GetComponent<Text>().text = 
                string.Format(ConfirmDialogTextFmg, _costOfChest * times, times, timesLimit - MonsterRiseData.data.monsterChestOpenTimesDaily);

            _confirmOpenChestTimes = times;
            transform.Find("confirmDialog/panel/btnOK").GetComponent<Button>().onClick.RemoveAllListeners();            
            transform.Find("confirmDialog/panel/btnOK").GetComponent<Button>().onClick.AddListener(() => {
                dialogObj.SetActive(false);
                UnlockMonsterChest(unlockTen.transform, _confirmOpenChestTimes);
            });

            transform.Find("confirmDialog/panel/btnCancel").GetComponent<Button>().onClick.RemoveAllListeners();
            transform.Find("confirmDialog/panel/btnCancel").GetComponent<Button>().onClick.AddListener(() => dialogObj.SetActive(false));
        }

        const string ConfirmDialogTextFmg = "确认使用 <quad name=0/>{0} 兑换 <quad name=1/>x{1} 进行{1}次开箱？\n今日剩余次数:{2}";

        GameObject tmpUICanvasObj = null;

        void OpenGemShop(Transform requester, bool auto) {
            if (UICanvas.Inst == null) {
                var prefab = ResourcesUtil.Load<GameObject>("RGPrefab/Other/scene_object/Canvas.prefab");
                prefab.SetActive(false);
                tmpUICanvasObj = GameObject.Instantiate(prefab);
                prefab.SetActive(true);
                tmpUICanvasObj.name = "Canvas";
                tmpUICanvasObj.SetActive(true);
            }
            if (!mutex.Lock(requester)) {
                return;
            }
            UICanvas.ShowUIWindowShop(null, null, auto ? Util.TAUtil.ConsumeStatistics.BuyWay.MonsterChestAuto : Util.TAUtil.ConsumeStatistics.BuyWay.MonsterChestManual);
            var cmd = Command.WaitUntil(() => {
                return !UIFramework.UIManager.Inst.HasUIViewOpened<UIWindowShop>();
            }).SetFinCallback(() => {
                UpdateCoinText();
                mutex.Unlock(requester);
            });
            RGGameProcess.StartCommand(cmd);
        }

        int GetMonsterShopSeed() {
            if (MonsterRiseData.data.monsterShopUniqueSeed != 0) {
                return (MonsterRiseData.data.monsterShopUniqueSeed % int.MaxValue +
                        MonsterRiseData.data.monsterChestOpenTimesTotal % int.MaxValue) % int.MaxValue;
            }

            MonsterRiseData.data.monsterShopUniqueSeed = PlayerSaveData.Inst.unique_random_seed;
            MonsterRiseData.Save();

            return (MonsterRiseData.data.monsterShopUniqueSeed % int.MaxValue +
                    MonsterRiseData.data.monsterChestOpenTimesTotal % int.MaxValue) % int.MaxValue;
        }

        CommandUtil.ICommand textBlinkSeq;
        void UnlockMonsterChest(Transform requester, int times) {
            RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Button);
            if (RGSaveManager.Inst.GetGem() < _costOfChest * times) {
                OpenGemShop(requester, true);
                return;
            }

            if (TimesRemain() < times) {
                UiUtil.GraphicBlink(transform.Find("content/Viewport/timesLimit").GetComponent<Text>(), Color.white, Color.red, ref textBlinkSeq);
                return;
            }

            if (!mutex.Lock(requester)) {
                return;
            }
            OpenChest(times, ()=> {
                mutex.Unlock(requester);
            });
        }

        void OpenChest(int times, System.Action onEnd) {
            rewardList = new List<RewardItem>();

            var config = RGScript.Data.DataMgr.ConfigData.Tables.TbMRCollect.DataMap;
            for (int i = 0; i < times; i++) {
                if (RGSaveManager.Inst.GetGem() < _costOfChest) {
                    break;
                }
                var rewards = GetRewards(config);
                rewardList.AddRange(rewards);
                foreach (var reward in rewards) {
                    if (reward.refund == 0) {
                        if (reward.item.Type == 0) {
                            RGSaveManager.Inst.ConsumeGem(_costOfChest);
                        }
                        else {
                            RGSaveManager.Inst.ConsumeGem(_costOfChest / 2);
                        }
                    }
                    else {
                        refundGem += reward.refund;
                    }
                }
                MonsterRiseData.Save();
            }
            UpdateCoinText();
            DisplayRewards(onEnd);
        }

        CfgCollect GetNextFreeEmblem() {
            for (var i = 0; i < FreeEmblems.Count; ++i) {
                if (MonsterRiseData.data.GetEmblemCount(FreeEmblems[i]) == 0) {
                    foreach(var item in DataMgr.ConfigData.Tables.TbMRCollect.DataList) {
                        if ((string.IsNullOrEmpty(item.Set) || string.IsNullOrWhiteSpace(item.Set)) && item.Type == 1 && item.ItemName == FreeEmblems[i]) {
                            return item;
                        }
                    }
                }
            }
            return null;
        }

        List<RewardItem> GetRewards(Dictionary<int, CfgCollect> config) {
            var monsterList = new List<CfgCollect>();
            var traitList = new List<CfgCollect>();
            foreach (var kv in config) {
                if (kv.Value.Type == 0) {
                    var monsterCfg = DataMgr.ConfigData.Tables.TbMRUnit.Get($"{kv.Value.ItemName}");
                    if (monsterCfg != null) {
                        monsterList.Add(kv.Value);
                    }
                } 
                else {
                    var limit = kv.Value.Limit > 0 ? kv.Value.Limit : 8;
                    for (var i = 0; i < limit; ++i) {
                        traitList.Add(kv.Value);
                    }
                }
            }

            var random = new System.Random(GetMonsterShopSeed());

            FixedNum.RandomUtil.Shuffle(traitList, new FixedNum.FixedRandom((ulong)random.Next()));

            var monsterProbability = 50;
            if (MonsterRiseData.data.CollectedMonsters == null || MonsterRiseData.data.CollectedMonsters.Count == 0) {
                monsterProbability = 100;
            }
            var rewardType = random.Next(0, 100) < monsterProbability ? 0 : 1;

            var trackItems = new List<System.Tuple<string, string>>();

            var rewards = new List<RewardItem>();

            if (rewardType == 0) {
                var rewardMonster = monsterList[random.Next(0, monsterList.Count)];
                var rewardName = rewardMonster.ItemName;
                var refund = 0;
                var monsterCfg = DataMgr.ConfigData.Tables.TbMRUnit.Get($"{rewardName}");
                if (MonsterData.MaxLevel(monsterCfg) <= MonsterRiseData.data.GetMonsterLevel(rewardName)){
                    refund = _costOfChest;
                }
                else {
                    MonsterRiseData.data.AddPlayerMonster(rewardName);
                }
                rewards.Add(new RewardItem{ item = rewardMonster, refund = refund });
                trackItems.Add(System.Tuple.Create<string, string>("monster", rewardName));
            } else {
                var randomIndex = random.Next(0, traitList.Count);
                var rewardTrait1 = traitList[randomIndex];
                var rewardTrait2 = traitList[(randomIndex + 1) % traitList.Count];

                var freeEmblem = GetNextFreeEmblem();
                if (freeEmblem != null) {
                    rewardTrait1 = freeEmblem;
                }

                var rewardName1 = rewardTrait1.ItemName;
                var rewardName2 = rewardTrait2.ItemName;

                var refund1 = 0;
                var emblemCountLimit1 = rewardTrait1.Limit > 0 ? rewardTrait1.Limit : 8;
                var emblemCountLimit2 = rewardTrait2.Limit > 0 ? rewardTrait2.Limit : 8;
                if (emblemCountLimit1 <= MonsterRiseData.data.GetEmblemCount(rewardName1)) {
                    refund1 = _costOfChest / 2;
                }
                else {
                    MonsterRiseData.data.AddPlayerEmblem(rewardName1);
                }

                var refund2 = 0;
                if (emblemCountLimit2 <= MonsterRiseData.data.GetEmblemCount(rewardName2)) {
                    refund2 = _costOfChest / 2;
                }
                else {
                    MonsterRiseData.data.AddPlayerEmblem(rewardName2);
                }

                rewards.Add(new RewardItem {item = rewardTrait1, refund = refund1});
                rewards.Add(new RewardItem {item = rewardTrait2, refund = refund2});

                trackItems.Add(System.Tuple.Create<string, string>("emblem", rewardName1));
                trackItems.Add(System.Tuple.Create<string, string>("emblem", rewardName2));
            }

            MonsterRiseData.data.monsterChestOpenTimesTotal++;
            MonsterRiseData.data.monsterChestOpenTimesDaily++;

            Util.TAUtil.CommonStatistics.TrackMonsterRiseGatcha(trackItems);

            return rewards;
        }

        

        CommandList rewardAnimationsCmd = new CommandList(false);
        void DisplayRewards(System.Action onEnd) {
            UpdateTimesLimit();

            var displayRewards = transform.Find("displayRewards");
            displayRewards.gameObject.SetActive(true);
            var closed = displayRewards.Find("closed");
            closed.gameObject.SetActive(true);
            var opened = displayRewards.Find("opened");
            var button = displayRewards.Find("button");
            button.gameObject.SetActive(false);
            var createdFx = displayRewards.Find("createdFx");
            var dark = displayRewards.Find("dark").GetComponent<Image>();;
            var trailFxPrefab = displayRewards.Find("trailFxPrefab").gameObject;
            var shiningFxPrefab = displayRewards.Find("shiningFxPrefab").gameObject;
            var gemFxPrefab = displayRewards.Find("gemFx").gameObject;
            var gemHalfFxPrefab = displayRewards.Find("gemHalfFx").gameObject;
            var descBg = displayRewards.Find("contentDesc") as RectTransform;
            var emblemDesc = displayRewards.Find("contentDesc/emblemInfo") as RectTransform;

            var itemPositionList = new List<Vector3>();
            var rewardGameObjects = new List<GameObject>();

            UiUtil.AddClickEventToRectTrans(descBg, () => { 
                descBg.gameObject.SetActive(false);
                emblemDesc.gameObject.SetActive(false);
            });

            UiUtil.SetAlpha(dark, 0);

            rewardAnimationsCmd.Clear();
            var seq = rewardAnimationsCmd;
            bool chestOpenFlag = false;
            seq.Add(CommandDuration.Delay(0.5f));
            opened.GetComponent<ObjectActions>().onEmit = (signal) => { 
                if (signal == "open") {
                    chestOpenFlag = true;
                }
            };
            seq.Add(() => { 
                opened.gameObject.SetActive(true);
            });
            seq.Add(Command.WaitUntil(() => {
                return chestOpenFlag;
            }));
            seq.Add(() => {
                closed.gameObject.SetActive(false);
            });

            var itemPopupCmds = new CommandList(true);
            itemPopupCmds.Add(CommandDuration.Progress(1, (dt, pg) => {
                UiUtil.SetAlpha(dark, pg * 0.85f);
                return true;
            }));
            
            var cols = 8;
            var rows = (int)System.Math.Ceiling(rewardList.Count * 1f / cols);

            var itemWidth = 130;
            var rowHeight = 120;

            var itemPopupDelay = 0f;
            for (var i = 0; i < rewardList.Count; ++i) {
                var itemType = rewardList[i].item.Type;
                var itemKey = rewardList[i].item.ItemName;

                GameObject rewardGameObj = null;

                if (itemType == 0) {
                    var monsterCfg = DataMgr.ConfigData.Tables.TbMRUnit.Get($"{itemKey}");
                    var monsterObj = MonsterRiseResUtil.CreateMonsterUIObj(monsterCfg, createdFx.transform, 1.5f);
                    monsterObj.GetComponent<MonsterUIItem>().SetLoadedCallback((uiObj, monsterData) => { 
                        UiUtil.AddClickEventToRectTrans(monsterObj.transform.Find("root/sprToImg/body") as RectTransform, () => { 
                            var monsterData = MonsterData.CreateFromCfg(monsterCfg);
                            descBg.gameObject.SetActive(true);
                            UIMonsterInfo.ShowMonsterInfoInMainUI(monsterData, false, transform.GetComponentInParent<Canvas>(), () => {
                                UIMonsterInfo.HideMonsterInfo();
                            });
                        });
                    });
                    monsterObj.SetActive(false);
                    rewardGameObjects.Add(monsterObj);
                    rewardGameObj = monsterObj;
                } else {
                    var emblemObj = MonsterRiseResUtil.CreateEmblemIconObj(itemKey, createdFx.transform);
                    UiUtil.AddClickEventToRectTrans(emblemObj.transform as RectTransform, () => { 
                        descBg.gameObject.SetActive(true);
                        emblemDesc.gameObject.SetActive(true);

                        var nameTextComponent = emblemDesc.Find("bg/name").GetComponent<Text>();
                        var descTextComponent = emblemDesc.Find("bg/text").GetComponent<Text>();
                        nameTextComponent.text = MonsterRiseResUtil.GetEmblemLocalizedName(itemKey);
                        descTextComponent.text = MonsterRiseResUtil.GetEmblemDesc(itemKey, null);

                        var iconContainer = emblemDesc.Find("bg/item/Image");
                        GameUtil.RemoveAllChildren(iconContainer);
                        var icon = MonsterRiseResUtil.CreateEmblemIconObj(itemKey, iconContainer);
                        icon.transform.localPosition = Vector3.zero;
                        UiUtil.RectTransformFitIn(icon.GetComponent<RectTransform>(), icon.transform.parent.GetComponent<RectTransform>(), true);
                    });
                    emblemObj.SetActive(false);
                    rewardGameObjects.Add(emblemObj);
                    rewardGameObj = emblemObj;
                }

                var itemPopupAnim = new CommandList(false);
                var trailFxObj = GameObject.Instantiate(trailFxPrefab, createdFx);

                var row = i / cols;
                var col = i % cols;
                var countInRow = System.Math.Min(cols, rewardList.Count - row * cols);

                var x = -countInRow * itemWidth / 2 + (col + 0.5f) * itemWidth;
                var y = -rows * rowHeight / 2 + (row + 0.5f) * rowHeight;
                y += -40;
                var endPos = new Vector3(x, y, 0);
                var velocity = (new Vector3(UnityEngine.Random.Range(-0.25f, 0.25f), 1, 0)).normalized * 2000;
                var angleSpeed = 50f;
                var angleSpeedIncrement = 5f;
                trailFxObj.transform.localPosition = new Vector3(UnityEngine.Random.Range(-30, 30), UnityEngine.Random.Range(-10, 10), 0);
                float stopDelay = 0.3f;
                itemPopupAnim.Add(CommandDuration.Delay(itemPopupDelay));
                itemPopupAnim.Add(new CommandPersist {
                    onStart = ()=>{
                        trailFxObj.SetActive(true);
                    },
                    onUpdate = (dt) => {
                        var disp = endPos - trailFxObj.transform.localPosition;
                        if (stopDelay <= 0 && disp.magnitude < velocity.magnitude * dt) {
                            trailFxObj.transform.localPosition = endPos;
                            return false;
                        }
                        stopDelay -= dt;
                        trailFxObj.transform.localPosition += velocity * dt;
                        if (Vector3.Dot(disp, velocity) == -1) {
                            velocity = Quaternion.Euler(0, 0, (UnityEngine.Random.Range(0, 1f) < 0.5f ? 1 : -1) * angleSpeed * dt) * velocity;
                        }
                        else {
                            var signedAngle = Vector3.SignedAngle(velocity, disp, Vector3.forward);
                            var rotateAngle = Mathf.Min(angleSpeed * dt, Mathf.Abs(signedAngle)) * Mathf.Sign(signedAngle);
                            velocity = Quaternion.Euler(0, 0, rotateAngle) * velocity;
                        }
                        angleSpeed += angleSpeedIncrement * dt;
                        angleSpeedIncrement += 2400 * dt;
                        velocity = velocity.normalized * System.Math.Min(velocity.magnitude * 1.01f, 3000);
                        return true;
                    },
                    onEnd = ()=>{
                        var shiningFx = GameObject.Instantiate(shiningFxPrefab, createdFx);
                        shiningFx.transform.localPosition = trailFxObj.transform.localPosition;
                        shiningFx.SetActive(true);
                        rewardGameObj.transform.localPosition = trailFxObj.transform.localPosition;
                        rewardGameObj.SetActive(true);
                        SimpleEventManager.Raise(new ClosePurchaseUIEvent {});
                    }
                });
                itemPopupCmds.Add(itemPopupAnim);
                itemPopupDelay = UnityEngine.Random.Range(0.05f, 0.25f + rewardList.Count * 0.02f);
            }
            seq.Add(itemPopupCmds);
            seq.Add(CommandDuration.Delay(1));
            seq.Add(() => {
                button.gameObject.SetActive(true);
            });
            CommandListExecution.Get(transform.gameObject).Add(seq);

            UiUtil.AddClickEventToRectTrans(button as RectTransform, () => {
                RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Button);
                button.gameObject.SetActive(false);
                var objectToRefund = rewardList.Aggregate(0, (acc, reward) => {
                    return acc + reward.refund > 0 ? 1 : 0;
                });

                if (objectToRefund == 0) {
                    displayRewards.gameObject.SetActive(false);
                    GameUtil.RemoveAllChildren(createdFx);
                    onEnd();
                }
                else {
                    var gemFxCmds = new CommandList(true);
                    
                    for (var i = 0; i < rewardList.Count; ++i) {
                        if (rewardList[i].refund == 0) {
                            continue;
                        }
                        var gemAnim = new CommandList(false);
                        var obj = rewardGameObjects[i];
                        var refund = rewardList[i].refund;
                        gemAnim.Add(CommandDuration.Delay(UnityEngine.Random.Range(0.1f, 0.25f)));
                        gemAnim.Add(() => { 
                            var gemFxObj = GameObject.Instantiate(refund == _costOfChest ? gemFxPrefab : gemHalfFxPrefab, obj.transform.parent);
                            gemFxObj.transform.localPosition = obj.transform.localPosition;
                            gemFxObj.SetActive(true);
                            obj.SetActive(false);
                        });
                        gemFxCmds.Add(gemAnim);
                    }
                    
                    seq.Add(gemFxCmds);
                    seq.Add(()=>{
                        refundGem = 0;
                        UpdateCoinText();
                    });
                    seq.Add(CommandDuration.Delay(1));
                    seq.Add(() => {
                        GameUtil.RemoveAllChildren(createdFx);
                        displayRewards.gameObject.SetActive(false);
                    });
                    seq.Add(onEnd);
                    CommandListExecution.Get(transform.gameObject).Add(seq);
                }
            });
        }

        void UpdateCoinText() {
            UpdateCoinText(RGSaveManager.Inst.GetGem() - refundGem);
        }

        void UpdateCoinText(int value) {
            var textCom = transform.Find("coin/coin").GetComponent<Text>();
            textCom.text = $"{value}";
            UiUtil.RefreshSizeAfterContentChanged(textCom.gameObject);
        }

        int TimesRemain() {
            return System.Math.Max(0, timesLimit - MonsterRiseData.data.monsterChestOpenTimesDaily);
        }

        void UpdateTimesLimit() {
            var textCom = transform.Find("content/Viewport/timesLimit").GetComponent<Text>();
            textCom.text = I2.Loc.ScriptLocalization.Get("item/egg_machine_left", "今日剩余次数:") + TimesRemain();
        }
    }
}