using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityCommon.UI;
using UnityEngine.UI;
using DG.Tweening;
using cfg.MonsterRise;
using CommandUtil;
using FixedNum;
using System.Linq;
namespace MonsterRise {
    public class UIMonsterRiseShop : BaseUIView {

        Transform commodityContainer;
        GameObject commodityPrefab;
        SceneMonsterRise scene;
        FixedNum.IConfigLoaderProxy configLoader => scene.gameMode.configLoader;
        RectTransform buttonClose;
        RectTransform buttonSteal;
        RectTransform buttonRefresh;
        Transform stealMiniGame;
        float stealMiniGameSuccessAreaInitWidth;

        ShopFunction shopFunction;

        public override void ShowView(params object[] args) {
            base.ShowView(args);

            shopFunction = new ShopFunction { 
                eventLoop = MonsterModeData.Inst.pveData.eventLoop,
                configLoader = SceneMonsterRise.Inst.gameMode.configLoader
            };

            stealMiniGameSuccessAreaInitWidth = UnityEngine.Random.Range(0.07f, 0.1f);

            commodityContainer = transform.Find("bg/commodities/Viewport/Content");
            commodityPrefab = transform.Find("bg/commodities/Viewport/shopItemPrefab").gameObject;

            scene = GameObject.FindObjectOfType<SceneMonsterRise>();

            stealMiniGame = transform.Find("stealing_mini_game");

            buttonSteal = transform.Find("bg/buttonSteal") as RectTransform;
            buttonRefresh = transform.Find("bg/button_refresh") as RectTransform;
            buttonRefresh.transform.Find("group/text").GetComponent<Text>().text = shopFunction.RefreshPrice().ToString();
            UiUtil.RefreshSizeAfterContentChanged(buttonRefresh.gameObject);
            UiUtil.AddClickEventToRectTrans(buttonRefresh, Refresh);

            buttonRefresh.gameObject.SetActive(shopFunction.merchantConfig.Refresh > 0);

            DisplayCommodities();

            buttonClose = transform.Find("bg/button_close") as RectTransform;
            UiUtil.AddClickEventToRectTrans(buttonClose, () => {
                RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Button);
                UIFramework.UIManager.Inst.CloseUIView(this);
            });
            buttonClose.gameObject.SetActive(MonsterModeData.Inst.monsters.Count > 0);

            var isblackmarket = shopFunction.isblackMarket;
            transform.Find("bg/img").gameObject.SetActive(!isblackmarket);
            transform.Find("bg/img_blackmarket").gameObject.SetActive(isblackmarket);
            if (isblackmarket) {
                // stealing enabled
                CommandListExecution.Execute(gameObject, new CommandPersist { 
                    onUpdate = (dt) => {
                        UpdateStealingState(dt);
                        return true;
                    }
                });
            }
        }

        void TempHideCommodityObj(GameObject obj) {
            for (var i = 0; i < obj.transform.childCount; ++i) {
                var child = obj.transform.GetChild(i);
                child.gameObject.SetActive(false);
            }
        }

        GameObject lastSelectedCommodityObj;
        void DisplayCommodities() {
            GameUtil.RemoveAllChildren(commodityContainer, (child)=>{
                child.transform.Find("button").GetComponent<UnityEngine.UI.Button>().onClick.RemoveAllListeners();
            });
            var commodities = shopFunction.commodities;
            for (var i = 0; i < commodities.Count; ++i) {
                var item = commodities[i];
                var obj = GameObject.Instantiate(commodityPrefab, commodityContainer);
                obj.SetActive(true);
                
                DisplayCommodityIcon(item, obj.transform.Find("container"));

                UiUtil.RefreshSizeAfterContentChanged(obj);
                UIPointerEventHandle.SetClickCallback(obj.transform, () => {
                    RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Button);
                    OnClickItem(item, obj);
                });

                obj.transform.Find("button/group/icon").GetComponent<SpriteListMono>().Set(item.priceType);
                obj.transform.Find("button/group/value").GetComponent<Text>().text = item.price.ToString();

                var buyButton = obj.transform.Find("button");
                // buy button
                UiUtil.AddClickEventToRectTrans(buyButton, () => {
                    if (transform.Find("fullblocker").gameObject.activeSelf) {
                        return;
                    }

                    if (!shopFunction.CanAfford(item.priceType, item.price)) {
                        UIMonsterRiseMainWindow.ResourceBlink(item.priceType);
                        AudioUtil.PlaySound("RGSound/effect/fx_failed.mp3");
                        return;
                    }

                    RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Button);

                    if (item.type == ItemType.Trait || item.type == ItemType.ConvealedTrait) {
                        // need a monster to get trait
                        var commodityIndex = obj.transform.GetSiblingIndex();
                        BuyTrait(SceneMonsterRise.Inst.gameMode, commodityIndex, item, obj, false);
                    }
                    else if (item.type == ItemType.Monster || item.type == ItemType.ConvealedMonster) {
                        var result = shopFunction.BuyMonster(SceneMonsterRise.Inst.gameMode, obj, obj.transform.GetSiblingIndex(), false);
                        if (result) {
                            buttonClose.gameObject.SetActive(true);
                            DelayDestroyCommodityObject(obj);
                        }
                        else {
                            DisplayHint(I2.Loc.ScriptLocalization.Get("monsrise/team_full", "不能雇佣更多怪兽了"));
                        }
                    }
                    else if (item.type == ItemType.Food) {
                        var commodityIndex = obj.transform.GetSiblingIndex();
                        BuyFood(SceneMonsterRise.Inst.gameMode, commodityIndex, obj);
                    }
                    else if (item.type == ItemType.HealthPotion) {
                        var commodityIndex = obj.transform.GetSiblingIndex();
                        SpecifyMonsterForCure(commodityIndex, item, obj, false);
                    }
                    else if (item.type == ItemType.BigHealthPotion) {
                        var commodityIndex = obj.transform.GetSiblingIndex();
                        var result = shopFunction.BuyBigHealthPotion(SceneMonsterRise.Inst.gameMode, commodityIndex, false);
                        if (result) {
                            GameObject.Destroy(obj);
                            ClearDesc();
                        }
                    }
                    else if(item.type == ItemType.Dismiss) {
                        var commodityIndex = obj.transform.GetSiblingIndex();
                        SpecifyMonsterForDismiss(SceneMonsterRise.Inst.gameMode, commodityIndex, item, obj);
                    }
                    else if (item.type == ItemType.Reborn) {
                        var commodityIndex = obj.transform.GetSiblingIndex();
                        var result = shopFunction.BuyReborn(SceneMonsterRise.Inst.gameMode, commodityIndex);
                        if (result) {
                            obj.SetActive(false);
                            obj.transform.SetAsLastSibling();
                            ClearDesc();
                        }
                    }
                    else if (item.type == ItemType.Insurance) {
                        var commodityIndex = obj.transform.GetSiblingIndex();
                        SpecifyMonsterForInsurance(commodityIndex, item, obj);
                    }
                });

                if(item.type == ItemType.Dismiss || item.type == ItemType.HealthPotion || item.type == ItemType.BigHealthPotion || 
                    item.type == ItemType.Reborn || item.type == ItemType.Food || item.type == ItemType.Insurance || 
                    shopFunction.merchantConfig.Lock == 0) {
                    buyButton.GetComponent<RectTransform>().SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal, buyButton.parent.GetComponent<RectTransform>().rect.width - 4);
                    obj.transform.Find("lock").gameObject.SetActive(false);
                }
                else {
                    var lockButton = obj.transform.Find("lock");
                    lockButton.transform.Find("icon").GetComponent<SpriteListMono>().Set(shopFunction.GetLock(item) ? 1 : 0);
                    UiUtil.AddClickEventToRectTrans(lockButton, ()=>{
                        AudioUtil.PlaySound("RGSound/effect/fx_double_click.mp3");
                        shopFunction.SetLock(item, !shopFunction.GetLock(item));
                        lockButton.transform.Find("icon").GetComponent<SpriteListMono>().Set(shopFunction.GetLock(item) ? 1 : 0);
                    });
                }

                if (item.type == ItemType.Reborn && MonsterModeData.Inst.monsters.Find((monster)=> MonsterModeData.Inst.IsMonsterDead(monster.id)) == null) {
                    obj.SetActive(false);
                    obj.transform.SetAsLastSibling();
                }
            }
        }

        void DelayDestroyCommodityObject(GameObject obj) {
            TempHideCommodityObj(obj);
            ClearDesc();
            transform.Find("fullblocker").gameObject.SetActive(true);
            CommandListExecution.Get(obj).Add(CommandDuration.Delay(0.5f).SetFinCallback(()=>{
                transform.Find("fullblocker").gameObject.SetActive(false);
                GameObject.Destroy(obj);
            }));
        }

        void DisplayCommodityIcon(ItemData item, Transform container) {
            GameUtil.RemoveAllChildren(container);
            if (item.type == ItemType.Monster || item.type == ItemType.ConvealedMonster) {
                CreateMonsterObject(item.type, item.Param0, container);
            } else if (item.type == ItemType.Trait || item.type == ItemType.ConvealedTrait) {
                CreateTraitObject(item.type, item.Param0, container);
            } else if (item.type == ItemType.Drink || item.type == ItemType.ConvealedDrink) {
                CreateDrinkObject(item.type, item.Param0, container);
            }
            else if(item.type == ItemType.Food) {
                CreateFoodObject(item.type, item.count, container);
            }
            else if(item.type == ItemType.TeamCapacity) {
                CreateCapacityObject(item.type, item.count, container);
            }
            else if (item.type == ItemType.Dismiss) {
                CreateDismissObject(item.type, container);
            }
            else if (item.type == ItemType.HealthPotion || item.type == ItemType.BigHealthPotion) {
                CreateHealthPotionObject(item.type, container);
            }
            else if (item.type == ItemType.Reborn) {
                CreateRebornObject(item.type, container);
            }else if (item.type == ItemType.Insurance) {
                CreateInsuranceObject(item.type, container);
            }
        }

        int stealState = 0;
        UiUtil.GraphicAlphaDict stealAlphaDict;
        void UpdateStealingState(float dt) {
            switch(stealState) {
                case 0:
                    CommandListExecution.Execute(gameObject, CommandDuration.Delay(UnityEngine.Random.Range(2f, 3f)).SetFinCallback(()=>{
                        stealState = 2;
                    }));
                    stealState = 1;
                    break;
                case 2:
                    if (commodityContainer.childCount > 0) {
                        var commodityIdx = UnityEngine.Random.Range(0, commodityContainer.childCount);
                        var obj = commodityContainer.GetChild(commodityIdx).gameObject;
                        var stealTrans = obj.transform.Find("steal");
                        buttonSteal.gameObject.SetActive(true);
                        buttonSteal.Find("text").GetComponent<Text>().text = I2.Loc.ScriptLocalization.Get("monsrise/ev_steal", "悄悄拿一个");
                        if (stealAlphaDict == null) {
                            stealAlphaDict = UiUtil.StoreAlpha(buttonSteal);
                        }
                        UiUtil.SetAlpha(stealAlphaDict, 0);
                        var seq = new CommandList(false);
                        seq.Add(CommandDuration.Progress(1, (pg) => {
                            UiUtil.SetAlpha(stealAlphaDict, pg);
                        }));
                        seq.Add(CommandDuration.Delay(UnityEngine.Random.Range(1, 1.5f)));
                        seq.Add(CommandDuration.Progress(1, (pg) => {
                            UiUtil.SetAlpha(stealAlphaDict, 1 - pg);
                        }).SetFinCallback(()=>{
                            buttonSteal.gameObject.SetActive(false);
                            UiUtil.SetAlpha(stealAlphaDict, 1);
                        }));

                        CommandListExecution.Get(buttonSteal.gameObject).Clear();
                        CommandListExecution.Execute(buttonSteal.gameObject, new CommandPersist{
                            onUpdate = (dt) => {
                                if (stealTrans == null) {
                                    buttonSteal.gameObject.SetActive(false);
                                    return false;
                                }
                                buttonSteal.position = stealTrans.position;
                                return true;
                            }
                        });
                        CommandListExecution.Execute(buttonSteal.gameObject, seq);
                        CommandListExecution.Execute(gameObject, CommandDuration.Delay(4).SetFinCallback(() => {
                            stealState = 0;
                        }));

                        stealState = 3;

                        UiUtil.AddClickEventToRectTrans(buttonSteal, () => {
                            RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Button);
                            var stealCommodityIdx = obj.transform.GetSiblingIndex();
                            StartStealingMiniGame(stealCommodityIdx);
                        });
                    }
                    break;
            }
        }

        void DisplayHint(string text) {
            UIMonsterCommonDisplayText.Display(text, 20, 0.25f, 1, null);
        }

        void BuyTrait(IMonsterRiseGameMode gameMode, int commodityIndex, ItemData item, GameObject obj, bool steal) {
            var result = shopFunction.BuyTrait(SceneMonsterRise.Inst.gameMode, commodityIndex, steal);
            if (result) {
                gameMode.eventHub.Raise(new MonsterRiseBuyEmblem { commodityObj = obj, emblem = item.Param0 });
                TempHideCommodityObj(obj);
                ClearDesc();
                transform.Find("fullblocker").gameObject.SetActive(true);
                (SceneMonsterRise.Inst.gameMode as MonsterRisePVE).battleUI.ShowBagButton(true);

                CommandListExecution.Get(obj).Add(CommandDuration.Delay(0.5f).SetFinCallback(()=>{
                    transform.Find("fullblocker").gameObject.SetActive(false);
                    GameObject.Destroy(obj);
                }));
            }
        }

        void BuyFood(IMonsterRiseGameMode gameMode, int commodityIndex, GameObject obj) {
            var battleUI = gameMode.battleUI;
            battleUI.mainWindow.pauseUpdate = true;
            var result = shopFunction.BuyCapacity(SceneMonsterRise.Inst.gameMode, commodityIndex, false);
            if (result) {
                TempHideCommodityObj(obj);
                ClearDesc();
                var foodIconObj = obj.transform.Find("container").GetChild(0).gameObject;
                var iconObj = GameObject.Instantiate(foodIconObj, foodIconObj.GetComponentInParent<UIMonsterRiseShop>().transform);
                iconObj.transform.position = foodIconObj.transform.position;
                transform.Find("fullblocker").gameObject.SetActive(true);
                var foodIconTransform = battleUI.mainWindow.GetFoodIconTransform();
                var cmd = MonsterRiseResUtil.CreateGetItemAnimation(iconObj, foodIconTransform, Vector2.zero, 0.3f, 0.35f, () => { 
                    transform.Find("fullblocker").gameObject.SetActive(false);
                    GameObject.Destroy(obj);
                    GameObject.Destroy(iconObj);
                    CommandListExecution.TemporaryActivate(foodIconTransform.transform.Find("efx"), 0.75f);
                    AudioUtil.PlaySound("RGSound/effect/fx_paper_slide_01.mp3");
                    battleUI.mainWindow.pauseUpdate = false;
                });
                CommandListExecution.Execute(iconObj, cmd);
            }
            else {
                battleUI.mainWindow.pauseUpdate = false;
            }
        }

        void SpecifyMonsterForCure(int commodityIndex, ItemData item, GameObject obj, bool steal) {
            var text = I2.Loc.ScriptLocalization.Get("monsrise/cure_select_monster", "选择要治疗的怪兽");
            CommonSelectMonster.Wait(text, null, MonsterModeData.Inst.GetAllMonsters(), 1, (selectedMonsters) => {
                var result = shopFunction.BuyHealthPotion(SceneMonsterRise.Inst.gameMode, commodityIndex, selectedMonsters[0], steal);
                if (result) {
                    ClearDesc();
                } else {
                    DisplayHint(I2.Loc.ScriptLocalization.Get("monsrise/cant_reborn", "不能对阵亡的怪兽使用"));
                }
            }, ()=>{ 
                // regret
            });
        }

        
        void SpecifyMonsterForDismiss(IMonsterRiseGameMode gameMode, int commodityIndex, ItemData item, GameObject obj) {
            var text = I2.Loc.ScriptLocalization.Get("monsrise/dismiss_select_monster", "选择要解散的怪兽");
            CommonSelectMonster.Wait(text, null, MonsterModeData.Inst.GetAllMonsters(), 1, (selectedMonsters) => {
                var monsterData = MonsterModeData.Inst.FindMonster(selectedMonsters[0]);
                var result = shopFunction.Dismiss(SceneMonsterRise.Inst.gameMode, commodityIndex, monsterData);
                if (result) {
                    ClearDesc();
                    gameMode.eventHub.Raise(new MonsterRiseMonstersChanged {
                        camp = MonsterModeData.Inst.camp,
                        removed = new List<MonsterRise.MonsterData> { monsterData }
                    });
                }
                else {
                    DisplayHint(I2.Loc.ScriptLocalization.Get("monsrise/cant_dismiss", "不能再解散怪兽了"));
                }
            }, ()=>{ 
                // regret, do nothing
            });
        }

        void SpecifyMonsterForInsurance(int commodityIndex, ItemData item, GameObject obj) {
            var text = I2.Loc.ScriptLocalization.Get("monsrise/insurance_select_monster", "选择要投保的怪兽");
            var pveData = MonsterModeData.Inst.pveData;
            CommonSelectMonster.Wait(text, null, MonsterModeData.Inst.GetAllMonsters().Where(m => !MonsterModeData.Inst.IsMonsterDead(m.id) && !pveData.IsInsuranced(m.id)).ToList(), 1, (selectedMonsters) => {
                var monsterData = MonsterModeData.Inst.FindMonster(selectedMonsters[0]);
                var result = shopFunction.BuyInsurance(SceneMonsterRise.Inst.gameMode, commodityIndex, monsterData);
                if (result) {
                    ClearDesc();
                }
            }, ()=>{ 
                // regret, do nothing
            });
        }

        GameObject CreateDrinkObject(ItemType type, string drinkName, Transform parent) {
            GameObject drinkItem = null;
            if (type == ItemType.Drink) {
                drinkItem = GameObject.Instantiate(MonsterRiseResUtil.GetItemUIObjPrefab(drinkName), parent);
            } 
            else {
                drinkItem = GameObject.Instantiate(MonsterRiseResUtil.GetItemUIObjPrefab($"convealed_drink"), parent);
            }
            drinkItem.transform.localPosition = Vector3.zero;
            return drinkItem;
        }

        GameObject CreateMonsterObject(ItemType type, string monsterName, Transform parent) {
            GameObject monsterItem = null;
            if (type == ItemType.Monster) {
                var configLoader = scene.gameMode.configLoader;
                monsterItem = GameObject.Instantiate(MonsterRiseResUtil.GetItemUIObjPrefab("monster"), parent);
                monsterItem.GetComponent<MonsterRise.MonsterUIItem>().SetPrefabByMonsterName(configLoader, monsterName);
            }
            else {
                monsterItem = GameObject.Instantiate(MonsterRiseResUtil.GetItemUIObjPrefab($"convealed_monster"), parent);
            }
            monsterItem.transform.localPosition = Vector3.zero;
            return monsterItem;
        }

        GameObject CreateTraitObject(ItemType type, string traitName, Transform parent) {
            GameObject traitItem = null;
            if (type == ItemType.Trait) {
                traitItem = MonsterRiseResUtil.CreateEmblemIconObj(traitName, parent);
            } 
            else {
                traitItem = MonsterRiseResUtil.CreateConvealedTraitIconObj(-1, parent);
            }
            traitItem.transform.localPosition = Vector3.zero;
            return traitItem;
        }

        GameObject CreateFoodObject(ItemType type, int count, Transform parent) {
            var obj = GameObject.Instantiate(MonsterRiseResUtil.GetItemUIObjPrefab("food"), parent);
            obj.transform.localPosition = Vector3.zero;
            return obj;
        }

        GameObject CreateCapacityObject(ItemType type, int count, Transform parent) {
            var obj = GameObject.Instantiate(MonsterRiseResUtil.GetItemUIObjPrefab("capacity"), parent);
            obj.transform.localPosition = Vector3.zero;
            return obj;
        }

        GameObject CreateDismissObject(ItemType type, Transform parent) {
            var obj = GameObject.Instantiate(MonsterRiseResUtil.GetItemUIObjPrefab("dismiss"), parent);
            obj.transform.localPosition = Vector3.zero;
            return obj;
        }

        GameObject CreateHealthPotionObject(ItemType type, Transform parent) {
            var obj = GameObject.Instantiate(MonsterRiseResUtil.GetItemUIObjPrefab(type == ItemType.HealthPotion ? "health_potion" : "big_health_potion"), parent);
            obj.transform.localPosition = Vector3.zero;
            return obj;
        }

        GameObject CreateRebornObject(ItemType type, Transform parent) {
            var obj = GameObject.Instantiate(MonsterRiseResUtil.GetItemUIObjPrefab("reborn"), parent);
            obj.transform.localPosition = Vector3.zero;
            return obj;
        }

        GameObject CreateInsuranceObject(ItemType type, Transform parent) {
            var obj = GameObject.Instantiate(MonsterRiseResUtil.GetItemUIObjPrefab("insurance"), parent);
            obj.transform.localPosition = Vector3.zero;
            return obj;
        }
        
        GameObject monsterInfo;

        void OnClickItem(ItemData itemData, GameObject item) {
            if (item == null) {
                return;
            }

            if (lastSelectedCommodityObj != null) {
                lastSelectedCommodityObj.transform.Find("selected").gameObject.SetActive(false);
            }
            lastSelectedCommodityObj = item;
            item.transform.Find("selected").gameObject.SetActive(true);
            
            var textComponent = transform.Find("bg/desc/text").GetComponent<Text>();
            var descText = "";
            if (itemData.type == ItemType.Monster) {
                var recruitLabel = I2.Loc.ScriptLocalization.Get("monsrise/ev_recruit_monster");
                descText = recruitLabel  + " " + MonsterRiseResUtil.GetMonsterLocalizedName(itemData.Param0);
                UIMonsterInfo.ShowMonsterInfo(MonsterRise.MonsterData.Create(configLoader, itemData.Param0), false);
                var pos = UiUtil.ConvertPosToTargetSpace(Vector2.zero, textComponent.transform as RectTransform, textComponent.GetComponentInParent<Canvas>().transform as RectTransform);
                UIMonsterInfo.SetPosition(pos);
            }
            else if(itemData.type == ItemType.Drink) {
                descText = MonsterRiseResUtil.GetDrinkLocalizedName(itemData.itemKey) + "\n" + MonsterRiseResUtil.GetDrinkDesc(itemData.itemKey);
            }
            else if(itemData.type == ItemType.Trait) {
                descText = MonsterRiseResUtil.GetEmblemLocalizedName(itemData.Param0) + "\n" + MonsterRiseResUtil.GetEmblemDesc(itemData.Param0, null);
            }
            else if(itemData.type == ItemType.ConvealedMonster) {
                descText = I2.Loc.ScriptLocalization.Get("monsrise/convealed_monster", "未知怪兽");
            }
            else if(itemData.type == ItemType.ConvealedDrink) {
                descText = I2.Loc.ScriptLocalization.Get("monsrise/convealed_drink", "未知饮料");
            }
            else if(itemData.type == ItemType.ConvealedTrait) {
                descText = I2.Loc.ScriptLocalization.Get("monsrise/convealed_trait", "未知特性");
            }
            else if(itemData.type == ItemType.Food) {
                descText = I2.Loc.ScriptLocalization.Get("monsrise/food", "食物") + " x " + itemData.count;
            }
            else if(itemData.type == ItemType.TeamCapacity) {
                descText = I2.Loc.ScriptLocalization.Get("monsrise/attr_capacity", "队伍规模") + " + " + itemData.count;
            }
            else if (itemData.type == ItemType.Dismiss) {
                descText = I2.Loc.ScriptLocalization.Get("monsrise/dismiss_monster", "解散怪兽");
            }
            else if (itemData.type == ItemType.HealthPotion) {
                descText = string.Format(I2.Loc.ScriptLocalization.Get("monsrise/item_health_potion_desc", "一个怪兽回复{0}%生命值"), MonsterRiseUtil.GetConstInt("HealthPotionCure"));
            }
            else if (itemData.type == ItemType.BigHealthPotion) {
                descText = string.Format(I2.Loc.ScriptLocalization.Get("monrise/item_big_health_potion_desc", "全部怪兽回复{0}%生命值"), MonsterRiseUtil.GetConstInt("BigHealthPotionCure"));
            }
            else if (itemData.type == ItemType.Reborn) {
                descText = string.Format(I2.Loc.ScriptLocalization.Get("monsrise/item_reborn_desc", "复活队伍中全部怪兽"));
            }
            else if (itemData.type == ItemType.Insurance) {
                descText = string.Format(I2.Loc.ScriptLocalization.Get("monsrise/item_insurance_desc", "阵亡保险，怪兽在战斗中阵亡可以获得{0}金币赔偿"), 
                    MonsterRiseUtil.GetConstInt("InsuranceCompensation"));
            }
            
            textComponent.text = descText;
            UiUtil.AvoidLineEndNumber(textComponent);
            UiUtil.RefreshSizeAfterContentChanged(textComponent.gameObject);
        }

        void ClearDesc() {
            lastSelectedCommodityObj = null;
            transform.Find("bg/desc/text").GetComponent<Text>().text = "";
        }

        void Refresh() {
            RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Button);
            if (shopFunction.Refresh(SceneMonsterRise.Inst.gameMode)) {
                buttonRefresh.transform.Find("group/text").GetComponent<Text>().text = shopFunction.RefreshPrice().ToString();
                UiUtil.RefreshSizeAfterContentChanged(buttonRefresh.gameObject);
                DisplayCommodities();
            }
            else {
                UIMonsterRiseMainWindow.ResourceBlink(0);
            }
        }


        void StartStealingMiniGame(int commodityIndex) {
            stealMiniGame.gameObject.SetActive(true);
            stealMiniGame.Find("bg/button_close").gameObject.SetActive(true);
            UiUtil.AddClickEventToRectTrans(stealMiniGame.Find("bg/button_close"), () => { 
                RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Button);
                stealMiniGame.gameObject.SetActive(false);
            });

            var itemData = shopFunction.commodities[commodityIndex];
            DisplayCommodityIcon(itemData, stealMiniGame.Find("bg/Image"));

            var bar = stealMiniGame.Find("bg/bar") as RectTransform;
            var successArea = stealMiniGame.Find("bg/bar/success") as RectTransform;
            var pointer = stealMiniGame.Find("bg/bar/pointer") as RectTransform;

            var successAreaWidth = bar.rect.width * stealMiniGameSuccessAreaInitWidth * Mathf.Pow(0.68f, shopFunction.StolenTimes());
            successArea.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal, successAreaWidth);
            var pointerSpeed = bar.rect.width / 0.65f * Mathf.Pow(1.4f, shopFunction.StolenTimes());

            successArea.localPosition = new Vector3(UnityEngine.Random.Range(-0.5f, 0.5f) * Mathf.Min(100, bar.rect.width - successAreaWidth - pointer.rect.width), 0, 0);
            if (successArea.localPosition.x >= 0) {
                pointer.transform.localPosition = new Vector3(-pointer.rect.width / 2, 0, 0);
            }
            else {
                pointer.transform.localPosition = new Vector3(pointer.rect.width / 2, 0, 0);
            }
            var pointerMoveDir = successArea.localPosition.x >= 0 ? 1 : -1;

            var pointerMovingCtrl = CommandListExecution.Get(pointer.gameObject);
            pointerMovingCtrl.Clear();
            pointerMovingCtrl.Add(new CommandPersist { 
                onUpdate = (dt) => {
                    pointer.localPosition += new Vector3(pointerSpeed * pointerMoveDir * dt, 0, 0);
                    if ((pointer.localPosition.x < -bar.rect.width / 2 && pointerMoveDir < 0) || (pointer.localPosition.x > bar.rect.width / 2 && pointerMoveDir > 0)) {
                        pointerMoveDir = -pointerMoveDir;
                    }
                    return true;
                }
            });

            pointer.GetComponent<Image>().color = Color.white;

            stealMiniGame.localScale = Vector3.zero;
            stealMiniGame.DOScale(Vector3.one, 0.25f).SetEase(Ease.OutBack);
        }

    }

    public class ShopFunction {
        public bool isblackMarket;
        public IGamePlayEventLoop eventLoop;
        public IConfigLoaderProxy configLoader;
        private int _stoleTimes;

        public cfg.MonsterRise.CfgMerchant merchantConfig =>
            MonsterRiseUtil.GetPveLevelConfigs(MonsterModeData.Inst.pveData.levelIndex, MonsterModeData.Inst.pveData.subLevelIndex, MonsterModeData.Inst.pveData.GetExplorationPhase());

        public List<ItemData> commodities => MonsterModeData.Inst.pveData.shopCommodities;

        public int RefreshPrice() {
            return MonsterModeData.Inst.pveData.RefreshShopCommoditiesPrice();
        }

        public bool Refresh(IMonsterRiseGameMode gameMode) {
            if (CanAfford(0, MonsterModeData.Inst.pveData.RefreshShopCommoditiesPrice())) {
                Consume(gameMode, 0, MonsterModeData.Inst.pveData.RefreshShopCommoditiesPrice(), "RefreshShop");
                ++MonsterModeData.Inst.pveData.shopRefreshTimes;
                MonsterModeDataFunc.InnerRefreshShopCommodities(MonsterModeData.Inst);
                return true;
            }
            return false;
        }

        public bool CanAfford(int priceType, int price) {
            return MonsterModeData.Inst.CanAfford(priceType, price);
        }

        void Consume(IMonsterRiseGameMode gameMode, int priceType, int price, string target) {
            MonsterModeData.Inst.Pay(gameMode, priceType, price, MonsRiseResModifyType.Shop, target);
        }

        public string PreviewMonster(int index, IRandomGenerator randGen = null, List<ItemData> specifiedCommodities = null) {
            if (randGen == null) {
                randGen = eventLoop.random.Clone();
            }
            if (specifiedCommodities == null) {
                specifiedCommodities = commodities;
            }
            var monsterName = "";
            var itemData = specifiedCommodities[index];
            if (itemData.type == ItemType.Monster) {
                monsterName = itemData.Param0;
            }
            return monsterName;
        }

        public bool BuyMonster(IMonsterRiseGameMode gameMode, GameObject commodityObj, int index, bool steal) {
            if (!MonsterModeData.Inst.CanRecruit()) {
                return false;
            }
            if (index >= 0 && index < commodities.Count) {
                var itemData = commodities[index];
                if (steal) {
                    var monsterKey = PreviewMonster(index, eventLoop.random);
                    commodities.RemoveAt(index);
                    var newMonsterData = MonsterRise.MonsterData.Create(configLoader, monsterKey);
                    MonsterModeData.Inst.Recruit(eventLoop.gameMode, commodityObj, newMonsterData);
                    MonsterRiseUtil.TrackItem(newMonsterData.monsterName, ItemType.Monster, 1);
                    MonsterModeData.Inst.pveData.UnlockCommodity(itemData);
                    return true;
                }
                else if (CanAfford(itemData.priceType, itemData.price)) {
                    var monsterKey = PreviewMonster(index, eventLoop.random);
                    commodities.RemoveAt(index);
                    Consume(gameMode, itemData.priceType, itemData.price, monsterKey);
                    var newMonsterData = MonsterRise.MonsterData.Create(configLoader, monsterKey);
                    MonsterModeData.Inst.Recruit(eventLoop.gameMode, commodityObj, newMonsterData);
                    MonsterRiseUtil.TrackItem(newMonsterData.monsterName, ItemType.Monster, 1);
                    MonsterModeData.Inst.pveData.UnlockCommodity(itemData);
                    return true;
                }
            }
            return false;
        }

        public bool BuyCapacity(IMonsterRiseGameMode gameMode, int index, bool steal) {
            if (index >= 0 && index < commodities.Count) {
                var itemData = commodities[index];
                if (CanAfford(itemData.priceType, itemData.price) && itemData.type == ItemType.Food) {
                    commodities.RemoveAt(index);
                    Consume(gameMode, itemData.priceType, itemData.price, ItemType.Food.ToString());
                    MonsterModeData.Inst.pveData.UnlockCommodity(itemData);
                    MonsterModeData.Inst.AddItem(gameMode, MonsterRise.ItemType.Food, itemData.count, MonsRiseResModifyType.Shop);
                    return true;
                }
            }
            return false;
        }


        public string PreviewDrink(int index, IRandomGenerator randGen = null, List<ItemData> specifiedCommodities = null) {
            if (randGen == null) {
                randGen = eventLoop.random.Clone();
            }
            if (specifiedCommodities == null) {
                specifiedCommodities = commodities;
            }
            var drinkName = "";
            var itemData = specifiedCommodities[index];
            if (itemData.type == ItemType.Drink) {
                drinkName = itemData.itemKey;
            }

            if (string.IsNullOrWhiteSpace(drinkName)) {
                return "";
            }
            return drinkName;
        }


        public bool BuyHealthPotion(IMonsterRiseGameMode gameMode, int index, int monsterId, bool steal) {
            if (!MonsterModeData.Inst.IsMonsterDead(monsterId)) {
                return false;
            }
            
            if (index >= 0 && index < commodities.Count) {
                var itemData = commodities[index];
                if (steal) {
                    MonsterModeData.Inst.HealMonster(monsterId, MonsterRiseUtil.GetConstInt("HealthPotionCure") * 0.01f);
                    MonsterRiseUtil.TrackItem("HealthPotion", ItemType.HealthPotion, 1);
                    return true;
                }
                else if (CanAfford(itemData.priceType, itemData.price)) {
                    Consume(gameMode, itemData.priceType, itemData.price, "HealthPotion");
                    MonsterModeData.Inst.HealMonster(monsterId, MonsterRiseUtil.GetConstInt("HealthPotionCure") * 0.01f);
                    MonsterRiseUtil.TrackItem("HealthPotion", ItemType.HealthPotion, 1);
                    return true;
                }
            }
            return false;
        }

        public bool BuyBigHealthPotion(IMonsterRiseGameMode gameMode, int index, bool steal) {
            if (index >= 0 && index < commodities.Count) {
                var itemData = commodities[index];
                if (CanAfford(itemData.priceType, itemData.price)) {
                    Consume(gameMode, itemData.priceType, itemData.price, "BigHealthPotion");
                    commodities.Remove(itemData);
                    commodities.Add(itemData);
                    var allMonsters = MonsterModeData.Inst.GetAllMonsters();
                    foreach(var monsterData in allMonsters) {
                        if (!MonsterModeData.Inst.IsMonsterDead(monsterData.id)) {
                            MonsterModeData.Inst.HealMonster(monsterData, MonsterRiseUtil.GetConstInt("BigHealthPotionCure") * 0.01f);
                        }
                    }
                    gameMode.eventHub.Raise(new MonsterRiseHealMonster());
                    MonsterRiseUtil.TrackItem("BigHealthPotion", ItemType.HealthPotion, 1);
                    return true;
                }
            }
            return false;
        }

        public bool BuyInsurance(IMonsterRiseGameMode gameMode, int index, MonsterData monster) {
            if (index >= 0 && index < commodities.Count) {
                var itemData = commodities[index];
                if (CanAfford(itemData.priceType, itemData.price)) {
                    MonsterModeData.Inst.pveData.insuranceMonsterIds.Add(monster.id);
                    Consume(gameMode, itemData.priceType, itemData.price, "Insurance");
                    MonsterRiseUtil.TrackItem("Insurance", ItemType.Insurance, 1);
                    gameMode.eventHub.Raise(new MonsterRiseMonstersChanged {camp = MonsterModeData.Inst.camp});
                    return true;
                }
            }
            return false;
        }

        public string PreviewTrait(int index, IRandomGenerator randGen = null, List<ItemData> specifiedCommodities = null) {
            if (randGen == null) {
                randGen = eventLoop.random.Clone();
            }
            if (specifiedCommodities == null) {
                specifiedCommodities = commodities;
            }
            var traitName = "";
            var itemData = specifiedCommodities[index];
            if (itemData.type == ItemType.Trait) {
                traitName = itemData.Param0;
            }

            if (string.IsNullOrWhiteSpace(traitName)) {
                return "";
            }
            return traitName;
        }

        public bool BuyTrait(IMonsterRiseGameMode gameMode, int index, bool steal) {
            if (index >= 0 && index < commodities.Count) {
                var itemData = commodities[index];
                if (steal) {
                    var traitName = PreviewTrait(index, eventLoop.random, commodities);
                    commodities.RemoveAt(index);
                    MonsterModeData.Inst.AddEmblem(traitName);
                    MonsterRiseUtil.TrackItem(traitName, ItemType.Trait, 1);
                    MonsterModeData.Inst.pveData.UnlockCommodity(itemData);
                    return true;
                }
                else if (CanAfford(itemData.priceType, itemData.price)) {
                    var traitName = PreviewTrait(index, eventLoop.random, commodities);
                    commodities.RemoveAt(index);
                    Consume(gameMode, itemData.priceType, itemData.price, traitName);
                    MonsterModeData.Inst.AddEmblem(traitName);
                    MonsterRiseUtil.TrackItem(traitName, ItemType.Trait, 1);
                    MonsterModeData.Inst.pveData.UnlockCommodity(itemData);
                    return true;
                }
            }
            return false;
        }

        public bool Dismiss(IMonsterRiseGameMode gameMode, int index, MonsterRise.MonsterData monster) {
            if (index >= 0 && index < commodities.Count) {
                var itemData = commodities[index];
                if (CanAfford(itemData.priceType, itemData.price) && MonsterModeData.Inst.monsters.Count > 1) {
                    Consume(gameMode, itemData.priceType, itemData.price, "Dismiss");
                    MonsterModeData.Inst.Dismiss(monster);
                    return true;
                }
            }
            return false;
        }
        
        public bool BuyReborn(IMonsterRiseGameMode gameMode, int index) {
            if (index >= 0 && index < commodities.Count) {
                var itemData = commodities[index];
                if (CanAfford(itemData.priceType, itemData.price)) {
                    commodities.Remove(itemData);
                    commodities.Add(itemData);
                    Consume(gameMode, itemData.priceType, itemData.price, "Reborn");
                    MonsterModeData.Inst.RebornMonsters();
                    gameMode.eventHub.Raise(new MonsterRiseMonstersChanged{camp = MonsterModeData.Inst.camp});
                    RGMusicManager.Inst.PlayEffect(ResourcesUtil.Load<AudioClip>("RGSound/effect/fx_ui_levelup_success2.mp3"));
                    return true;
                }
            }
            return false;
        }

        public void SetLock(ItemData item, bool lockState) {
            if(lockState) {
                MonsterModeData.Inst.pveData.LockCommodity(item);
            }
            else {
                MonsterModeData.Inst.pveData.UnlockCommodity(item);
            }
        }

        public bool GetLock(ItemData item) {
            return MonsterModeData.Inst.pveData.lockedItemData.IndexOf(item) >= 0;
        }

        public void OnSteal(int commodityIndex, bool result) {
            /*
            if (commodityIndex < 0 || commodityIndex >= commodities.Count) {
                return;
            }

            ++_stoleTimes;
            if (!result) {
                eventLoop.PushEvent(roomEvent.Param1);
                var itemData = commodities[commodityIndex];
                if (itemData.type == ItemType.ConvealedDrink){
                    itemData = MonsterRiseUtil.GenItemFromConvealedItem(eventLoop.gameMode.unlockContent, eventLoop.gameMode.configLoader, eventLoop.random, itemData);
                }
                else if (itemData.type == ItemType.ConvealedMonster) {
                    itemData = MonsterRiseUtil.GenItemFromConvealedItem(eventLoop.gameMode.unlockContent, eventLoop.gameMode.configLoader, eventLoop.random, itemData);
                }
                else if (itemData.type == ItemType.ConvealedTrait) {
                    itemData = MonsterRiseUtil.GenItemFromConvealedItem(eventLoop.gameMode.unlockContent, eventLoop.gameMode.configLoader, eventLoop.random, itemData);
                }
                itemData.price = 0;
                MonsterModeData.Inst.pveData.AddCustomReward(itemData);
                MonsterModeData.Inst.pveData.UnlockCommodity(itemData);
                isDone = true;
            }
            */
        }

        public int StolenTimes() {
            return _stoleTimes;
        }
    }
}