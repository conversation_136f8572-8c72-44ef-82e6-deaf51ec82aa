using RGScript.Data;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace MonsterRise {
    public class UIMonsterRiseEmblemBag {
        class EmblemObjectInfo {
            public GameObject itemObj;
            public EmblemData emblemData;
            public int displayedOwnerId;
        }


        static EmblemObjectInfo lastSelected;
        static int selectedMonsterId;
        static int selectedSlotIdx;
        static int selectedEmblemId;
        static Dictionary<string, List<EmblemObjectInfo>> itemToEmblemData = new ();
        static List<RectTransform> touchBlockers = new List<RectTransform>();

        static RectTransform bagPanel;
        static RectTransform descPanel;
        static RectTransform descPanel2;
        static RectTransform emblemListPanel;

        static System.Func<string, string> attributeGetter;
        static GameObject emblemObjectItemPrefab;
        static System.Action<int, bool> onRefresh;
        static RectTransform emblemObjectContainer;
        static RectTransform equipButton;
        static RectTransform emblemListScrollView;
        static UiUtil.UIEventMutex mutex = new UiUtil.UIEventMutex();

        static public void Show(RectTransform transform, int monsterId, int slotIdx, System.Func<string,string> attributeGetterFunc, System.Action<int, bool> onRefreshFunc, bool autoLocate) {
            mutex = new UiUtil.UIEventMutex();
            bagPanel = transform.Find("emblemPack") as RectTransform;
            emblemObjectContainer = bagPanel.transform.Find("emblems/Scroll View/Viewport/Content") as RectTransform;
            emblemListScrollView = bagPanel.transform.Find("emblems/Scroll View") as RectTransform;
            var refresh = !bagPanel.gameObject.activeSelf;
            bagPanel.gameObject.SetActive(true);

            attributeGetter = attributeGetterFunc;
            onRefresh = onRefreshFunc;

            emblemListPanel = bagPanel.transform.Find("emblems") as RectTransform;
            descPanel = bagPanel.transform.Find("emblems/desc") as RectTransform;
            descPanel2 = bagPanel.transform.Find("emblems/desc2") as RectTransform;
            equipButton = bagPanel.transform.Find("equip") as RectTransform;
            if (refresh) {
                descPanel.gameObject.SetActive(false);
                descPanel2.gameObject.SetActive(false);
                CancelEquipmentChangeConfirmation();
            }
            emblemObjectItemPrefab = bagPanel.transform.Find("emblems/Scroll View/Viewport/item").gameObject;

            var modeData = MonsterModeData.Inst;
            var pveData = modeData.pveData;

            selectedMonsterId = monsterId;
            selectedSlotIdx = slotIdx;

            if (refresh) {
                RefreshList(modeData, emblemObjectItemPrefab, emblemObjectContainer, attributeGetter);
            }


            var monsterData = modeData.FindMonster(selectedMonsterId);
            int initEmblemId = 0;
            if (monsterData.slots[selectedSlotIdx].id > 0) {
                initEmblemId = monsterData.slots[selectedSlotIdx].id;
                selectedEmblemId = initEmblemId;
            }

            if (initEmblemId > 0) {
                var emblemIdx = modeData.emblems.FindIndex((emblem) => { return emblem.id == initEmblemId; });
                if (emblemIdx >= 0) {

                    if (autoLocate) {
                        var gridCols = 1;
                        var row = emblemIdx / gridCols;
                        var totalRows = Mathf.CeilToInt(modeData.emblems.Count * 1f / gridCols);
                        UiUtil.InitScrollRectContentTo(emblemObjectContainer, false, row * 1f / totalRows);
                    }
                    
                    var emblemObjectList = itemToEmblemData[modeData.emblems[emblemIdx].key];
                    var emblemObjInfo = emblemObjectList.Find(eo => eo.displayedOwnerId == selectedMonsterId);

                    if (emblemObjInfo != lastSelected) {
                        if (lastSelected != null) {
                            lastSelected.itemObj.transform.Find("selected").gameObject.SetActive(false);
                        }
                        CancelEquipmentChangeConfirmation();
                        OnClickEmblem(emblemObjInfo, false);
                        UpdateOutsideTouchBlockers();

                        lastSelected = emblemObjInfo;
                        lastSelected.itemObj.transform.Find("selected").gameObject.SetActive(true);
                    }
                }
            }
            else {
                if (lastSelected != null) {
                    lastSelected.itemObj.transform.Find("selected").gameObject.SetActive(false);
                    lastSelected = null;
                }
                if (refresh) {
                    descPanel.gameObject.SetActive(false);
                    descPanel2.gameObject.SetActive(false);
                    UpdateOutsideTouchBlockers();
                }
                CancelEquipmentChangeConfirmation();
            }
        }

        static void ConfirmEuquipmentChange(EmblemObjectInfo info, bool equip) {
            mutex.Lock(equipButton.transform);
            ShowEquipButton(equip, info);
        }

        static void CancelEquipmentChangeConfirmation() {
            HideEquipButton();
            if (equipButton != null) {
                mutex.Unlock(equipButton.transform);
            }
        }

        static void RefreshList(MonsterModeData modeData, GameObject itemPrefab, Transform container, System.Func<string,string> attributeGetter) {
            itemToEmblemData.Clear();
            GameUtil.RemoveAllChildren(container);
            lastSelected = null;
            for (var i = 0; i < modeData.emblems.Count; ++i) {
                var emblemData = modeData.emblems[i];
                var emblemCfg = DataMgr.ConfigData.Tables.TbMRTrait.GetOrDefault(emblemData.key);
                if (emblemCfg == null) {
                    continue;
                }
                var key = emblemData.key;
                var equipedCount = 0;
                for (var equipedMonsterIdx = 0; equipedMonsterIdx < emblemData.equipedMonsterIds.Count; ++equipedMonsterIdx) {
                    var equipedMonsterId = emblemData.equipedMonsterIds[equipedMonsterIdx];
                    var equipedMonsterData = modeData.FindMonster(equipedMonsterId);
                    if (equipedMonsterData == null) {
                        continue;
                    }
                    ++equipedCount;
                    
                    var obj = CreateEmblemUIObject(modeData, itemPrefab, container, emblemData, equipedMonsterId, attributeGetter);
                    DisplayMonsterIcon(modeData, obj, equipedMonsterId);

                    var ownerMonsterData = modeData.FindMonster(equipedMonsterId);
                    var ownerMonsterIcon = MonsterRiseResUtil.CreateMonsterUIObj(ownerMonsterData.configData, obj.transform.Find("owner"), 1);
                    ownerMonsterIcon.transform.localScale = Vector3.one * 0.5f;
                    ownerMonsterIcon.transform.localPosition = new Vector3(0, 25, 0);
                }

                if (equipedCount < emblemData.count) {
                    var obj = CreateEmblemUIObject(modeData, itemPrefab, container, emblemData, 0, attributeGetter);
                    var count = emblemData.count - equipedCount;
                    obj.transform.Find("count").GetComponent<Text>().text = count > 1 ? count.ToString() : "";
                }
            }

            if (container.childCount > 8) {
                emblemListScrollView.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal, 164);
            }
            else {
                emblemListScrollView.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal, 80);
            }
        }

        static void UpdateListAfterEquipmentChange(MonsterModeData modeData, GameObject itemPrefab, Transform container, System.Func<string,string> attributeGetter, int emblemId) {
            var emblemData = modeData.FindEmblem(emblemId);
            var key = emblemData.key;
            var emblemObjects = itemToEmblemData[key];

            if (emblemData.equipedMonsterIds.Contains(selectedMonsterId)) {
                // equip
                if (lastSelected.displayedOwnerId == 0) {
                    if (emblemData.equipedMonsterIds.Count < emblemData.count) {
                        // add one object
                        var obj = CreateEmblemUIObject(modeData, itemPrefab, container, emblemData, selectedMonsterId, attributeGetter);
                        var tmp = emblemObjects[emblemObjects.Count - 1];
                        emblemObjects[emblemObjects.Count - 1] = emblemObjects[emblemObjects.Count - 2];
                        emblemObjects[emblemObjects.Count - 2] = tmp;
                        obj.transform.SetSiblingIndex(emblemObjects[^1].itemObj.transform.GetSiblingIndex());
                        DisplayMonsterIcon(modeData, obj, selectedMonsterId);
                        var count = emblemData.count - emblemData.equipedMonsterIds.Count;
                        emblemObjects[^1].itemObj.transform.Find("count").GetComponent<Text>().text = count > 1 ? count.ToString() : "";
                        lastSelected.itemObj.transform.Find("selected").gameObject.SetActive(false);
                        lastSelected = emblemObjects.Find(eo => eo.itemObj == obj);
                        lastSelected.itemObj.transform.Find("selected").gameObject.SetActive(true);
                    }
                    else {
                        // use last vacant object
                        DisplayMonsterIcon(modeData, lastSelected.itemObj, selectedMonsterId);
                        lastSelected.displayedOwnerId = selectedMonsterId;
                    }
                }
                else {
                    // replace
                    DisplayMonsterIcon(modeData, lastSelected.itemObj, selectedMonsterId);
                    lastSelected.displayedOwnerId = selectedMonsterId;
                }
            }
            else {
                // unequip
                if (emblemObjects[^1].displayedOwnerId != 0) {
                    // release one object as vacant
                    var emblemObj = emblemObjects.Find(eo => eo.displayedOwnerId == selectedMonsterId);
                    emblemObj.displayedOwnerId = 0;
                    GameUtil.RemoveAllChildren(emblemObj.itemObj.transform.Find("owner"));
                    emblemObj.itemObj.transform.Find("count").GetComponent<Text>().text = "";
                    if (emblemObjects.Count > 1 && emblemObjects.IndexOf(emblemObj) != emblemObjects.Count - 1) {
                        emblemObjects.Remove(emblemObj);
                        emblemObj.itemObj.transform.SetSiblingIndex(emblemObj.itemObj.transform.parent.childCount - 1);
                        emblemObj.itemObj.transform.SetSiblingIndex(emblemObjects[emblemObjects.Count - 1].itemObj.transform.GetSiblingIndex() + 1);
                        emblemObjects.Add(emblemObj);
                    }
                }
                else {
                    // remove one object, update count
                    var toRemove = emblemObjects.Find(em => em.displayedOwnerId == selectedMonsterId);
                    if (toRemove != null) {
                        GameObject.Destroy(toRemove.itemObj);
                        emblemObjects.Remove(toRemove);
                    }
                    for (var i = 0; i < emblemObjects.Count; ++i) {
                        if (emblemObjects[i].itemObj != null && emblemObjects[i].displayedOwnerId == 0) {
                            var count = emblemData.count - emblemData.equipedMonsterIds.Count;
                            emblemObjects[i].itemObj.transform.Find("count").GetComponent<Text>().text = count > 1 ? count.ToString() : "";
                            break;
                        }
                    }
                }
            }
        }

        static void DisplayMonsterIcon(MonsterModeData modeData, GameObject itemObj, int equipedMonsterId) {
            var ownerMonsterData = modeData.FindMonster(equipedMonsterId);
            GameUtil.RemoveAllChildren(itemObj.transform.Find("owner"));
            var ownerMonsterIcon = MonsterRiseResUtil.CreateMonsterUIObj(ownerMonsterData.configData, itemObj.transform.Find("owner"), 1);
            itemObj.transform.Find("count").GetComponent<Text>().text = "";
            ownerMonsterIcon.transform.localScale = Vector3.one * 0.5f;
            ownerMonsterIcon.transform.localPosition = new Vector3(0, 25, 0);
        }

        static GameObject CreateEmblemUIObject(MonsterModeData modeData, GameObject itemPrefab, Transform container, EmblemData emblemData, int equipedMonsterId, System.Func<string,string> attributeGetter) {
            var obj = GameObject.Instantiate(itemPrefab, container);
            obj.SetActive(true);
            if (!itemToEmblemData.ContainsKey(emblemData.key)) {
                itemToEmblemData[emblemData.key] = new List<EmblemObjectInfo>();
            }
            var info = new EmblemObjectInfo() {
                itemObj = obj,
                emblemData = emblemData,
                displayedOwnerId = equipedMonsterId,
            };
            itemToEmblemData[emblemData.key].Add(info);
            var icon = MonsterRiseResUtil.CreateEmblemIconObj(emblemData.key, obj.transform.Find("Image"));
            icon.transform.localPosition = Vector3.zero;
            UiUtil.RectTransformFitIn(icon.GetComponent<RectTransform>(), icon.transform.parent.GetComponent<RectTransform>(), true);
            obj.transform.Find("selected").gameObject.SetActive(false);

            void ClickEmblem() {
                RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Button);

                selectedEmblemId = emblemData.id;
                if (lastSelected != null) {
                    lastSelected.itemObj.transform.Find("selected").gameObject.SetActive(false);
                }
                obj.transform.Find("selected").gameObject.SetActive(true);

                OnClickEmblem(info, true);
                lastSelected = info;
                var monsterData = modeData.FindMonster(selectedMonsterId);
                var slot = monsterData.slots[selectedSlotIdx];
                UpdateOutsideTouchBlockers(); 

                if (lastSelected.displayedOwnerId == selectedMonsterId) {
                    var emblemSlotIdx = monsterData.FindEmblemSlotIdx(selectedEmblemId);
                    if (emblemSlotIdx != selectedSlotIdx) {
                        selectedSlotIdx = emblemSlotIdx;
                        onRefresh?.Invoke(selectedSlotIdx, false);
                    }
                }
            }

            UIPointerEventHandle.SetClickCallback(icon.transform, ClickEmblem, mutex);

            return obj;
        }

        static void ShowEquipButton(bool equip, EmblemObjectInfo emblemObjInfo) {
            equipButton.gameObject.SetActive(true);
            equipButton.gameObject.transform.position = emblemObjInfo.itemObj.transform.Find("equip").transform.position;
            if (equip) {
                equipButton.Find("equip").gameObject.SetActive(true);
                equipButton.Find("unequip").gameObject.SetActive(false);
            }
            else {
                equipButton.Find("equip").gameObject.SetActive(false);
                equipButton.Find("unequip").gameObject.SetActive(true);
            }

            UIPointerEventHandle.SetClickCallback(equipButton.transform, () => {
                if (equip) {
                    AudioUtil.PlaySound("RGSound/effect/fx_ting_01.mp3");
                    DisplayEmblemDesc(emblemObjInfo.emblemData, descPanel, selectedMonsterId);
                    descPanel.gameObject.SetActive(true);
                    descPanel2.gameObject.SetActive(false);
                    UpdateOutsideTouchBlockers();
                    CancelEquipmentChangeConfirmation();
                    EquipEmblem(emblemObjInfo);
                }
                else {
                    RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Button);
                    UpdateOutsideTouchBlockers();
                    CancelEquipmentChangeConfirmation();
                    DisplayEmblemDesc(emblemObjInfo.emblemData, descPanel,0);
                    descPanel.gameObject.SetActive(true);
                    descPanel2.gameObject.SetActive(false);
                    UnequipEmblem();
                }
            }, mutex);

            var updateMono = CommonUpdateMono.Get(equipButton.gameObject);
            updateMono.onUpdate = (dt) => {
                if (Input.GetMouseButton(0)) {
                    var mousePos = Input.mousePosition;
                    if (!UiUtil.IsMousePositionInRectTransform(mousePos, equipButton)) {
                        CancelEquipmentChangeConfirmation();
                    }
                }
            };
        }

        static void HideEquipButton() {
            if (equipButton != null) {
                equipButton.gameObject.SetActive(false);
            }
        }

        static void EquipEmblem(EmblemObjectInfo emblemObjInfo) {
            var modeData = MonsterModeData.Inst;
            RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Button);
            var monsterData = modeData.FindMonster(selectedMonsterId);
            if (monsterData.slots[selectedSlotIdx].id > 0) {
                var emblemInSlot = modeData.FindEmblem(monsterData.slots[selectedSlotIdx].id);
                modeData.MonsterUnequip(selectedMonsterId, emblemInSlot.id);
                if (SceneMonsterRise.Inst != null) {
                    SceneMonsterRise.Inst.gameMode.eventHub.Raise(new MonsterRiseChangeEquipment { monster = monsterData, equip = false, emblem = emblemInSlot.key });
                }
                UpdateListAfterEquipmentChange(modeData, emblemObjectItemPrefab, emblemObjectContainer, attributeGetter, emblemInSlot.id);
            }

            var oldMonsterId = emblemObjInfo.displayedOwnerId;
            modeData.MonsterEquipEmblem(selectedMonsterId, oldMonsterId, selectedSlotIdx, selectedEmblemId);
            var emblem = modeData.FindEmblem(selectedEmblemId);
            if (SceneMonsterRise.Inst != null) {
                SceneMonsterRise.Inst.gameMode.eventHub.Raise(new MonsterRiseChangeEquipment { monster = monsterData, oldMonsterId = oldMonsterId, equip = true, emblem = emblem.key });
            }
            
            UpdateListAfterEquipmentChange(modeData, emblemObjectItemPrefab, emblemObjectContainer, attributeGetter, emblemObjInfo.emblemData.id);
            onRefresh?.Invoke(selectedSlotIdx, true);
        }

        static void UnequipEmblem() {
            var modeData = MonsterModeData.Inst;
            RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Button);
            var emblemInSlot = modeData.FindEmblem(modeData.FindMonster(selectedMonsterId).slots[selectedSlotIdx].id);
            modeData.MonsterUnequip(selectedMonsterId, emblemInSlot.id);
            var monsterData = modeData.FindMonster(selectedMonsterId);
            if (SceneMonsterRise.Inst != null) {
                SceneMonsterRise.Inst.gameMode.eventHub.Raise(new MonsterRiseChangeEquipment { monster = monsterData, equip = false, emblem = emblemInSlot.key });
            }

            var list = itemToEmblemData[lastSelected.emblemData.key];
            UpdateListAfterEquipmentChange(modeData, emblemObjectItemPrefab, emblemObjectContainer, attributeGetter, emblemInSlot.id);
            if (lastSelected != null) {
                lastSelected.itemObj.transform.Find("selected").gameObject.SetActive(false);
            }
            lastSelected = list.Find(eo => eo.displayedOwnerId == 0);
            lastSelected.itemObj.transform.Find("selected").gameObject.SetActive(true);

            onRefresh?.Invoke(selectedSlotIdx, false);
        }

        static void OnClickEmblem(EmblemObjectInfo emblemObjInfo, bool realClick) {
            bool confirmEquipmentChange = false;
            var modeData = MonsterModeData.Inst;
            var monsterData = modeData.FindMonster(selectedMonsterId);
            var clickedEmblem = modeData.FindEmblem(emblemObjInfo.emblemData.id);
            var currentSlotIdx = selectedSlotIdx;
            if (lastSelected == emblemObjInfo) { // clicked twice
                confirmEquipmentChange = true;
            }
            else {
                if (realClick) {
                    CancelEquipmentChangeConfirmation();
                }
                var equipedEmblem = monsterData.GetEmblemData(selectedSlotIdx);
                if (equipedEmblem == null || itemToEmblemData[equipedEmblem.key].Find(eo => eo.displayedOwnerId == selectedMonsterId) == emblemObjInfo) {
                    descPanel2.gameObject.SetActive(false);
                    DisplayEmblemDesc(clickedEmblem, descPanel, emblemObjInfo.displayedOwnerId);
                    descPanel.gameObject.SetActive(true);
                }
                else {
                    descPanel.gameObject.SetActive(true);
                    DisplayEmblemDesc(equipedEmblem, descPanel, selectedMonsterId);

                    if (!monsterData.HasEmblem(emblemObjInfo.emblemData.id)) {
                        descPanel2.gameObject.SetActive(true);
                        DisplayEmblemDesc(clickedEmblem, descPanel2, emblemObjInfo.displayedOwnerId);
                    }
                    else {
                        descPanel2.gameObject.SetActive(false);
                    }
                }

                confirmEquipmentChange = realClick;
            }

            if (confirmEquipmentChange && currentSlotIdx == selectedSlotIdx) {
                if (emblemObjInfo.displayedOwnerId == selectedMonsterId) {
                    ConfirmEuquipmentChange(emblemObjInfo, false);
                }
                else if (!monsterData.HasEmblem(emblemObjInfo.emblemData.id)) {
                    ConfirmEuquipmentChange(emblemObjInfo, true);
                }
            }
        }

        static void DisplayEmblemDesc(EmblemData emblemData, RectTransform targetDescPanel, int ownerMosnterId) {
            var nameTextComponent = targetDescPanel.Find("bg/name").GetComponent<Text>();
            var descTextComponent = targetDescPanel.Find("bg/text").GetComponent<Text>();
            nameTextComponent.text = MonsterRiseResUtil.GetEmblemLocalizedName(emblemData.key);
            descTextComponent.text = MonsterRiseResUtil.GetEmblemDesc(emblemData.key, null);
            UiUtil.AvoidLineEndNumber(descTextComponent);

            var iconContainer = targetDescPanel.Find("bg/item/Image");
            GameUtil.RemoveAllChildren(iconContainer);
            var icon = MonsterRiseResUtil.CreateEmblemIconObj(emblemData.key, iconContainer);
            icon.transform.localPosition = Vector3.zero;
            UiUtil.RectTransformFitIn(icon.GetComponent<RectTransform>(), icon.transform.parent.GetComponent<RectTransform>(), true);

            var ownerContainer = targetDescPanel.Find("bg/item/owner");
            GameUtil.RemoveAllChildren(ownerContainer);

            if (ownerMosnterId > 0) {
                var modeData = MonsterModeData.Inst;
                var monsterData = modeData.FindMonster(selectedMonsterId);

                var ownerMonsterIcon = MonsterRiseResUtil.CreateMonsterUIObj(monsterData.configData, ownerContainer, 1);
                ownerMonsterIcon.transform.localScale = Vector3.one * 0.25f;
                ownerMonsterIcon.transform.localPosition = new Vector3(0, 6, 0);
            }
        }

        static public void Hide() {
            if (bagPanel != null) {
                bagPanel.gameObject.SetActive(false);
            }
            CancelEquipmentChangeConfirmation();
        }

        static public bool IsDisplayed() {
            return bagPanel != null && bagPanel.gameObject.activeSelf;
        }

        public static void UpdateOutsideTouchBlockers() {
            touchBlockers.Clear();
            touchBlockers.Add(emblemListPanel);

            if (descPanel.gameObject.activeSelf) {
                touchBlockers.Add(descPanel);
            }

            if (descPanel2.gameObject.activeSelf) {
                touchBlockers.Add(descPanel2);
            }

            if (equipButton.gameObject.activeSelf) {
                touchBlockers.Add(equipButton);
            }
        }

        public static RectTransform[] GetTouchBlocker() {
            return touchBlockers.ToArray();
        }
    }
}