using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using CommandUtil;

namespace MonsterRise {
    public class UIMonsterRiseBetArena : MonoBehaviour {
        // Start is called before the first frame update

        int betLeft;
        int betRight;
        int moneyRemain;

        void Start() {
            moneyRemain = MonsterModeData.Inst.money;
            var roomData = MonsterModeData.Inst.pveData.GetCurrentRoomData();
            var eventData = roomData.customData;
            UiUtil.AddClickEventToRectTrans(transform.Find("start"), () => {
                SceneMonsterRise.Inst.gameMode.OnStartBattleRequest();
                eventData.intValue2 = System.Math.Min(betLeft, MonsterModeData.Inst.money);
                if (betLeft + betRight > MonsterModeData.Inst.money) {
                    betRight = System.Math.Max(0, MonsterModeData.Inst.money - betLeft);
                }
                eventData.intValue3 = betRight;

                GameObject.Destroy(gameObject);
            });

            var leftDecrease = transform.Find("moneyLeft/decrease") as RectTransform;
            SetupBetButton(leftDecrease, () => { 
                if (betLeft > 0) {
                    AudioUtil.PlaySound("RGSound/effect/fx_bubble.wav");
                    betLeft--;
                    moneyRemain++;
                    UpdateMoneyLabel();
                }
            });

            var leftIncrease = transform.Find("moneyLeft/increase") as RectTransform;
            SetupBetButton(leftIncrease, () => {
                if (moneyRemain > 0) {
                    AudioUtil.PlaySound("RGSound/effect/fx_bubble.wav");
                    betLeft++;
                    moneyRemain--;
                    UpdateMoneyLabel();
                }
            });

            var rightDecrease = transform.Find("moneyRight/decrease") as RectTransform;
            SetupBetButton(rightDecrease, () => {
                if (betRight > 0) {
                    AudioUtil.PlaySound("RGSound/effect/fx_bubble.wav");
                    betRight--;
                    moneyRemain++;
                    UpdateMoneyLabel();
                }
            });

            var rightIncrease = transform.Find("moneyRight/increase") as RectTransform;
            SetupBetButton(rightIncrease, () => {
                if (moneyRemain > 0) {
                    AudioUtil.PlaySound("RGSound/effect/fx_bubble.wav");
                    betRight++;
                    moneyRemain--;
                    UpdateMoneyLabel();
                }
            });

            transform.Find("rounds").GetComponent<Text>().text = string.Format("{0} / {1}", eventData.intValue4, 3);

            UpdateMoneyLabel();
        }

        void SetupBetButton(RectTransform buttonTransform, System.Action callback) {
            UiUtil.SetPointerEventToRectTrans(buttonTransform, new UiUtil.MouseEventCallback {
                onPointerClick = () => {
                    callback();
                },
                onPointerPressDown = () => {
                    var interval = 0.1f;
                    var elapsed = interval;
                    var pressedDuration = 0f;
                    CommandListExecution.Execute(buttonTransform.gameObject, new CommandPersist {
                        onUpdate = (dt) => {
                            elapsed += dt;
                            pressedDuration += dt;
                            if (elapsed >= interval) {
                                elapsed -= interval;
                                callback();
                                if (interval < dt - interval) {
                                    var extraTimes = (int)((dt - interval) / interval);
                                    for (var i = 0; i < extraTimes; ++i) {
                                        callback();
                                    }
                                }
                            }
                            interval = Mathf.Max(0.0001f, 0.1f - pressedDuration * 0.015f);
                            return true;
                        }
                    });
                },
                onPointerPressUp = () => {
                    CommandListExecution.Get(buttonTransform.gameObject).Clear();
                }
            });
        }

        void UpdateMoneyLabel() {
            transform.Find("money/content/text").GetComponent<Text>().text = moneyRemain.ToString();
            transform.Find("moneyLeft/content/text").GetComponent<Text>().text = betLeft.ToString();
            transform.Find("moneyRight/content/text").GetComponent<Text>().text = betRight.ToString();
        }

        // Update is called once per frame
        void Update() {

        }

        public static void Show(Transform parent) {
            var prefab = ResourcesUtil.Load<GameObject>("MonsterRise/Prefab/UI/monster_rise_arena.prefab");
            GameObject.Instantiate(prefab, parent);
        }
    }
}