
using System.Collections.Generic;

namespace MonsterRise {
    public class MonsRiseGPEventRescue : GPEventTypeIdAssignment, HardCodedGPEventHandler  {
        static public string TraitId = "Plague";
        static public int EventTypeId;
        public static void AssignEventTypeId(int idx) {
            EventTypeId = idx + 1;
        }

        static System.Type GPEventType = typeof(MonsRiseGPEventRescue);

        static RaceType[] RescueTarget = new RaceType[] { 
            RaceType.Human,
            RaceType.Dwarf,
            RaceType.Demihuman
        };

        static string MakeEventKey(string key) {
            return GPEventType.Name + "#" + key;
        }

        public static EventContext CreateContext(IGamePlayEventLoop eventLoop, GPEventCustomData evData, string eventKey) {
            EventContext context = null;
            string localizedEventText = null;
            System.Action onEndCallback = null;

            if (eventKey == "start") {
                evData.intValue0 = eventLoop.random.Range(0, RescueTarget.Length);
                evData.randomSeed = eventLoop.random.Next();

                context = new EventFunctionChoice { 
                    eventKey = MakeEventKey("start"),
                    displayLocalizedText = MonsterRiseResUtil.GetText(MakeEventKey("start"), MonsterRiseResUtil.GetText(RescueTarget[evData.intValue0])),
                    choices = new List<EventContext.Choice>{ 
                        new EventContext.Choice{
                            evKey = MakeEventKey("b0_1"),
                            displayLocalizedText = MonsterRiseResUtil.GetText(MakeEventKey("b0_0")),
                            needMonsters = 1
                        },
                        new EventContext.Choice{
                            evKey = MakeEventKey("b1_0"),
                            displayLocalizedText = MonsterRiseResUtil.GetText(MakeEventKey("b1_0"), MonsterRiseResUtil.GetText(RescueTarget[evData.intValue0])),
                        },
                        new EventContext.Choice{
                            evKey = "leave",
                            displayLocalizedText = MonsterRiseResUtil.GetText(MakeEventKey("b2_0"))
                        }
                    }
                };
            }
            else if (eventKey == "b0_1") {
                var selectedMonster = eventLoop.GetLastSelectedMonsters();
                var monsterId = selectedMonster[0];
                var monsterData = MonsterModeData.Inst.FindMonster(monsterId);
                if (monsterData.HasHealingAbility()) {
                    evData.intValue1 = 0;
                    var money = GetMoneyReward(evData);
                    localizedEventText = MonsterRiseResUtil.GetText(MakeEventKey("b0_1"), MonsterRiseResUtil.GetText(RescueTarget[evData.intValue0]), money.count);
                    onEndCallback = () => { 
                        MonsterModeData.Inst.pveData.AddCustomReward(money);
                        eventLoop.PushEvent("get_items");
                    };
                }
                else {
                    MonsterModeData.Inst.MonsterGetTrait(monsterId, TraitId);
                    evData.intValue1 = 1;
                    localizedEventText = MonsterRiseResUtil.GetText(MakeEventKey("b0_2"), MonsterRiseResUtil.GetText(RescueTarget[evData.intValue0]), MonsterRiseResUtil.GetMonsterLocalizedName(monsterData.monsterName));
                    onEndCallback = () => { 
                        var selectedMonster = eventLoop.GetLastSelectedMonsters();
                        var monsterId = selectedMonster[0];
                        MonsterModeData.Inst.MonsterGetTrait(monsterId, TraitId);
                    };
                }                
            }
            else if (eventKey == "b1_0") {  // battle
                var battleIdx = evData.intValue0;
                context = new EventFunctionBattle { 
                    battleCfgData = new BattleConfigData{ 
                        battleKey = GPEventType.Name + battleIdx.ToString(),
                        winEventKey = MakeEventKey("b1_1"),
                    },
                    onEndCallback = ()=> { 
                        var lastSentMonsters = MonsterModeData.Inst.lastSentMonsters;
                        for (var i = 0; i < lastSentMonsters.Count; ++i) {
                            var monsterId = lastSentMonsters[i];
                            MonsterModeData.Inst.MonsterGetTrait(monsterId, TraitId);
                        }
                    }
                };
            }
            else if (eventKey == "b1_1") { // battle win
                var money = GetMoneyReward(evData);
                localizedEventText = MonsterRiseResUtil.GetText(MakeEventKey("b1_1"), money.count);
                onEndCallback = () => { 
                    MonsterModeData.Inst.pveData.AddCustomReward(money);
                    eventLoop.PushEvent("get_items");
                };
            }

            if (context == null) {
                context = new EventFunctionText {
                    eventKey = MakeEventKey(eventKey),
                    displayLocalizedText = localizedEventText,
                    onEndCallback = onEndCallback
                };
                context.choices.Add(new EventContext.Choice {
                    evKey = "go_on"
                });
            }
            return context;
        }

        static ItemData GetMoneyReward(GPEventCustomData evData) {
            var moneyRewardCfg = MonsterRiseResUtil.GetRewardItemsCfg(0, GPEventType.Name + "_Money");
            var randomGen = new FixedNum.FixedRandom(evData.randomSeed);
            var rewardItems = MonsterRiseUtil.ParseRewardItems(SceneMonsterRise.Inst.gameMode, moneyRewardCfg.Items, randomGen);
            var money = rewardItems[0][0];
            return money;
        }

        public static void AfterDisplayMapNodes() {
            var pveData = MonsterModeData.Inst.pveData;
            for (var i = 0; i < pveData.roomDataList.Count; ++i) {
                if (pveData.IsExplored(i)) {
                    continue;
                }
                
                if (pveData.roomDataList[i].roomType == cfg.MonsterRise.RoomType.Event && pveData.roomDataList[i].customData.eventType == EventTypeId) {
                    (SceneMonsterRise.Inst.gameMode as MonsterRisePVE).mapNodes.DisplayEventMark(i, "sos");
                }
            }
        }
    }
}