using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Text.RegularExpressions;
using System.Globalization;
using FixedNum;

namespace MonsterRise {
    public class SkinnedCharacterDefaultBodyParts : ISkinnedCharacterBodyParts {
        ObjectSkin objectSkin;
        FTransform objectTransform;
        UnitStats objectStats;
        Mover objectMover;

        GameObject innerGameObj;
        Transform transform => innerGameObj.transform;
        Transform handTransform;
        Animator bodyAnimator;
        Animator weaponAnimator;
        string weaponName;
        Transform weaponEfxRoot;
        Vector3 weaponEfxRootLoalPos;
        SpriteAnimationProxy handAnimationProxy;
        WeaponHoldType weaponHoldType = WeaponHoldType.Normal;
        float _weaponHoldAngle;
        bool _multiStateAttack;
        string _multiStateAttackActionName;

        MonsterRiseWeaponAnimConfig.Data weaponAnimConfigData;
        int bodyAttackStateID = 0;
        int bodyAttackStateLayer = 0;
        SkinnedCharacter skinnedCharacter;

        public System.Action customUpdateAnimator => null;

        SkinnedBulletHitFx weaponHitFx;


        public void Init(SkinnedCharacter skinnedCharacter) {
            this.skinnedCharacter = skinnedCharacter;
            objectSkin = skinnedCharacter.objectSkin;
            objectTransform = skinnedCharacter.objectTransform;
            objectStats = skinnedCharacter.objectStats;
            objectMover = skinnedCharacter.objectMover;
            innerGameObj = skinnedCharacter.innerGameObj;

            objectSkin.onActionUpdate = UpdateActionProgress;
            objectSkin.onActionComplete = OnActionComplete;

            var unitTraits = objectSkin.GetComponent<UnitTraits>();
            bodyAnimator = innerGameObj.GetComponent<Animator>();

            handTransform = innerGameObj.transform.Find("img/h1");
            if (handTransform != null && handTransform.childCount > 0) {
                weaponAnimator = handTransform.GetComponentInChildren<Animator>();
                if (weaponAnimator != null) {
                    weaponAnimator.speed = 0;
                    weaponEfxRoot = (new GameObject("weaponEfxRoot")).transform;
                    weaponEfxRoot.SetParent(innerGameObj.transform);
                    weaponEfxRootLoalPos = Vector3.right * (float)objectSkin.owner.GetComponent<UnitStats>().bodyRadius;
                }
                handAnimationProxy = handTransform.GetComponent<SpriteAnimationProxy>();

                UpdateWeaponAnimConfig();
            }
        }


        void UpdateWeaponAnimConfig() {
            weaponHoldType = WeaponHoldType.Normal;
            if (weaponAnimator != null) {
                if (weaponAnimator.transform.name.Contains("_FixedHold")) {
                    weaponHoldType = WeaponHoldType.FixedHold;
                    var regexPattern = @"_FixedHold_(\d+)";
                    var match = Regex.Match(weaponAnimator.transform.name, regexPattern);
                    if (match.Success) {
                        _weaponHoldAngle = float.Parse(match.Groups[1].Value, CultureInfo.InvariantCulture);
                    }
                }
                weaponName = weaponAnimator.transform.name;
                if (weaponAnimator.transform.name.Contains("_aim_")) {
                    _multiStateAttackActionName = "aim";
                }
                else if (weaponAnimator.transform.name.Contains("_drill")) {
                    _multiStateAttackActionName = "drill";
                }

                _multiStateAttack = !string.IsNullOrWhiteSpace(_multiStateAttackActionName);

                var splitIdx = weaponAnimator.transform.name.IndexOf('_');
                if (splitIdx >= 0) {
                    weaponName = weaponName.Substring(0, splitIdx);
                }
                weaponAnimConfigData = MonsterRiseWeaponAnimConfig.dict.GetValueOrDefault(weaponName);
                if (weaponAnimConfigData == null && weaponName.Contains("sword")) {
                    weaponAnimConfigData = MonsterRiseWeaponAnimConfig.dict.GetValueOrDefault("sword_default");
                }

                if (weaponName.Contains("shotgun")) {
                    weaponName = "shotgun";
                }

                bodyAttackStateID = Animator.StringToHash(weaponName + "_attack");
                bool hasBodyAttackState = false;
                for (var i = 0; i < 3; ++i) {
                    if (bodyAnimator.HasState(i, bodyAttackStateID)) {
                        bodyAttackStateLayer = i;
                        hasBodyAttackState = true;
                    }
                }

                if (!hasBodyAttackState) {
                    bodyAttackStateID = 0;
                }
                
                weaponHitFx = weaponAnimator.gameObject.GetComponentInChildren<SkinnedBulletHitFx>(true);
            }
        }

        float GetAnimationClipLength(Animator animator, string clipName) {
            var ac = animator.runtimeAnimatorController;
            foreach (AnimationClip clip in ac.animationClips)
            {
                if (clip.name == clipName)
                {
                    return clip.length;
                }
            }
            return 0;
        }

        void UpdateHandRotation(float speed, bool addDirection, bool addWeaponHoldAngle) {
            if (handTransform != null) {
                var handAngle = 0f;

                if (addDirection) {
                    handAngle = (float)objectTransform.angle;
                    if (transform.right.x < 0) {
                        handAngle = Mathf.Sign(handAngle) * (180 - Mathf.Abs(handAngle));
                    }
                }

                if (addWeaponHoldAngle) {
                    handAngle += _weaponHoldAngle;
                }

                if (objectStats.freeze.Value || objectStats.stun) {
                    handAngle = _weaponHoldAngle;
                }

                if (speed > 0) {
                    var nowHandLocalAngle = SkinnedCharacter.NormalizeAngle(handAnimationProxy != null ? handAnimationProxy.runtimeAngle : handTransform.localEulerAngles.z);
                    var diff = handAngle - nowHandLocalAngle;
                    var deltaAngle = Mathf.Sign(diff) * Mathf.Min(speed * Time.deltaTime, Mathf.Abs(diff));
                    if (handAnimationProxy != null) {
                        handAnimationProxy.runtimeAngle = nowHandLocalAngle + deltaAngle;
                    } else {
                        handTransform.localRotation = Quaternion.Euler(0, 0, nowHandLocalAngle + deltaAngle);
                    }
                }
                else {
                    if (handAnimationProxy != null) {
                        handAnimationProxy.runtimeAngle = handAngle;
                    } else {
                        handTransform.localRotation = Quaternion.Euler(0, 0, handAngle);
                    }
                }
            }
        }

        WeaponAnimatorState _weaponAnimatorState = WeaponAnimatorState.Idle;
        float lastAtkActionAmount = 0;
        int lastActionVersion = -1;
        int lastAmmoRemain = 0;
        int lastAttackAnimPhase = 0;

        void UpdateActionProgress(int version, int actionType, int actionedTimes, int actionTimes, FixedNumber actionProgress, FixedNumber duration, bool keyEvent) {
            if (lastActionVersion != version) {
                lastActionVersion = version;
                lastAtkActionAmount = 0;
                lastAttackAnimPhase = 0;
            }
            
            var p = (float)actionProgress;
            if (actionProgress == FixedNumber.one) {
                p = 1;
            }

            if (actionType == Attacker.ActionId) {
                
                if (bodyAttackStateID != 0) {
                    bodyAnimator.speed = 0;
                    bodyAnimator.Play(bodyAttackStateID, bodyAttackStateLayer, p);
                }

                SkinnedObjectAction.CalActionProgress((float)objectStats.configPreAttackTimeLength, 
                    (float)objectStats.configAttackAnimLength,
                    (float)objectStats.configPostAttackTimeLength,
                    actionTimes,
                    (float)actionProgress,
                    (float)duration,
                    out var phase,
                    out var p0,
                    out var p1);

                if (phase == 0 && _multiStateAttack) {
                    bodyAnimator.Play(_multiStateAttackActionName, 1, p0);
                }
                else if(phase == 1 && _multiStateAttack) {
                    bodyAnimator.Play($"{_multiStateAttackActionName}Loop", 1, p0);
                }
                else if(phase == 2 && _multiStateAttack) {
                    bodyAnimator.Play($"{_multiStateAttackActionName}End", 1, p0);
                }
                
                if (weaponAnimator != null) {
                    _weaponAnimatorState = WeaponAnimatorState.Attack;
                    weaponAnimator.speed = 0;
                    weaponAnimator.Play("attack", 0, p1);
                }

                UpdateWeaponEfx();
                PlayAttackEfx(!_multiStateAttack, lastAttackAnimPhase, phase, lastAtkActionAmount, p1, keyEvent);
                lastAtkActionAmount = p1;

                lastAttackAnimPhase = phase;
            }
            else {
                skinnedCharacter.abilityPerforming.UpdateAction(actionType, p, (float)duration, bodyAnimator, weaponAnimator);
            }
            
        }

        void OnActionComplete(int version, int actionType) {
            _weaponAnimatorState = WeaponAnimatorState.Idle;
            if (weaponAnimator != null) {
                weaponAnimator.speed = 0;
                var idleStateID = Animator.StringToHash("idle");
                if(weaponAnimator.HasState(0, idleStateID)) {
                    weaponAnimator.Play("idle");
                }
            }

            if (bodyAnimator != null && !objectStats.dead) {
                bodyAnimator.SetBool("run", objectMover.moving.Value);
                bodyAnimator.speed = !skinnedCharacter.isFrozen ? 1 : 0;
                if (!objectMover.moving.Value) {
                    bodyAnimator.Play("idle", 0, 0);
                }
            }

            if (weaponContinuousEfxObj != null) {
                GameObject.Destroy(weaponContinuousEfxObj);
                weaponContinuousEfxObj = null;
            }
        }

        void UpdateWeaponEfx() {
            if (weaponEfxRoot != null) {
                if (weaponAnimator != null) {
                    weaponEfxRoot.position = weaponAnimator.transform.TransformPoint(weaponEfxRootLoalPos);
                    weaponEfxRoot.rotation = handTransform.rotation;
                } else {
                    weaponEfxRoot.position = handTransform.TransformPoint(weaponEfxRootLoalPos);
                    weaponEfxRoot.rotation = handTransform.rotation;
                }
            }
        }

        GameObject weaponContinuousEfxObj;

        void PlayAttackEfx(bool common, int lastPhase, int phase, float lastProgress, float progress, bool keyEvent) {
            GameObject prefab = null;
            var localPos = Vector3.zero;

            if (weaponAnimator != null) {
                if (objectStats.attackAction.type == CombatActionType.Melee) {
                    if (common) {
                        var atkFxTime = 0f;
                        if (weaponAnimConfigData != null) {
                            atkFxTime = weaponAnimConfigData.atkFxTime;
                        }
                        if (lastProgress <= atkFxTime && progress > atkFxTime) {
                            if (weaponName == "spear") {

                            }
                            else if (weaponName == "fistsword") {

                            }
                            else if (weaponName == "saber") {
                                // nothing
                            }
                            else if (weaponName == "shovel") {
                                // nothing
                            }
                            else if(weaponName == "bat") {

                            }
                            else if (weaponName == "axe") {
                                
                            }
                            else if (weaponName.Contains("sword")) {
                                prefab = ResourcesUtil.Load<GameObject>("MonsterRise/Prefab/SkillEfx/SwordAtkEfx.prefab");
                                MonsterRiseResUtil.PlaySwordWieldSFX();
                            } else {
                                MonsterRiseResUtil.PlaySwordWieldSFX();
                            }
                        }

                        if (keyEvent && weaponName == "shield") {
                            AudioUtil.PlaySound("RGSound/effect/fx_metal_hit01.mp3", 0.7f, UnityEngine.Random.Range(0.8f, 1.2f));
                        }
                    }

                    if (weaponName.Contains("drill")) {
                        if (lastPhase != phase) {
                            if (phase == 1) {
                                weaponContinuousEfxObj = GameObject.Instantiate(ResourcesUtil.Load<GameObject>("MonsterRise/Prefab/Bullet/fx_drill.prefab"));
                                weaponContinuousEfxObj.transform.SetParent(weaponEfxRoot);
                                weaponContinuousEfxObj.transform.localPosition = Vector3.zero;
                                weaponContinuousEfxObj.transform.localEulerAngles = Vector3.zero;
                            }
                            else if (phase == 2) {
                                if (weaponContinuousEfxObj != null) {
                                    GameObject.Destroy(weaponContinuousEfxObj);
                                    weaponContinuousEfxObj = null;
                                }
                            }
                        }
                    }

                } else{
                    if (lastProgress > progress) {
                        lastProgress = 0;
                    }
                    if (weaponName == "firestaff") {

                    } else if (weaponAnimator.runtimeAnimatorController.name.Contains("shot_gun")) {
                        if (lastProgress < 0.1f && progress >= 0.1f) {
                            AudioUtil.PlaySound("RGSound/effect/EpicToonFX/etfx_shoot_shotgun.mp3");
                        }
                    } else if (weaponAnimator.runtimeAnimatorController.name.Contains("uzi")) {

                    } else if (weaponAnimator.runtimeAnimatorController.name.Contains("rifle")) {

                    } else if (weaponAnimator.runtimeAnimatorController.name.Contains("gun")) {
                        if (lastProgress < 0.1f && progress >= 0.1f) {
                            AudioUtil.PlaySound("RGSound/effect/fx_gun_8.mp3");
                        }
                    }  else if (weaponAnimator.runtimeAnimatorController.name.Contains("staff")) {
                        if (lastProgress < 0.45f && progress >= 0.45f) {
                            AudioUtil.PlaySound("RGSound/effect/EpicToonFX/etfx_shoot_mystic_mod.mp3");
                        }
                    }
                    else if (weaponAnimator.runtimeAnimatorController.name.Contains("sword")) {
                        if (lastProgress < 0.1f && progress >= 0.1f) {
                            MonsterRiseResUtil.PlaySwordWieldSFX();
                        }
                    }
                    else if (weaponAnimator.runtimeAnimatorController.name.Contains("throw")) {
                        if (lastProgress < 0.35f && progress >= 0.35f) {
                            MonsterRiseResUtil.PlayThrowSFX();
                        }
                    }
                }
            }

            if (prefab != null) {
                PrefabPool.Inst.Take(prefab, localPos, Vector3.zero, weaponEfxRoot, 0.5f);
            }
        }

        public void Update(float dt) {
            if (handTransform != null && !objectStats.dead) {
                var actionType = skinnedCharacter.objectSkin.actionType;
                var rotateHandByDirection = actionType == 0 && weaponHoldType != WeaponHoldType.FixedHold;
                rotateHandByDirection |= (actionType != 0 && (skinnedCharacter.IsNormalBodyAnimation || skinnedCharacter.abilityPerforming.HandRotateAccordingToTarget(actionType)));
                UpdateHandRotation(1800, rotateHandByDirection, actionType == 0);
                handTransform.gameObject.SetActive(true);
            }

            if (weaponAnimator != null) {
                if (!objectStats.dead) {
                    if (!skinnedCharacter.IsNormalBodyAnimation && !objectStats.attack && weaponAnimator != null && _weaponAnimatorState != WeaponAnimatorState.Idle) {
                        _weaponAnimatorState = WeaponAnimatorState.Idle;
                        weaponAnimator.speed = !objectStats.freeze.Value ? 1 : 0;
                        weaponAnimator.Play("idle");
                    }
                    if (!handTransform.gameObject.activeSelf) {
                        handTransform.gameObject.SetActive(true);
                    }
                }
                else {
                    if (weaponAnimator != null) {
                        weaponAnimator.speed = 1;
                    }
                    // 存活->死亡状态变换
                    handTransform.gameObject.SetActive(false);
                }

                UpdateWeaponEfx();
            }
        }

        public void OnHit(SkinnedCharacter target, DamageInfo damageInfo) {
            if (weaponHitFx != null) {
                weaponHitFx.Play(1, Vector2.zero);
            }
        }

        public void OnDie() {
            
        }
    }
}