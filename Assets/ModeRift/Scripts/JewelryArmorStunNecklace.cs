using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class JewelryArmorStunNecklace : Jewelry {
    public override void TakeEffect(JewelryAttribute attribute, GameObject target_object) {
        if (CheckUsable() && target_object) {
            Instantiate(info.prefabs[0], target_object.transform.position + target_object.GetColliderOffset(),
                Quaternion.identity, RGGameSceneManager.Inst.temp_objects_parent);
            foreach (var target in FindTargets(transform.position, 3)) {
                if (target != null) {
                    var buff = Instantiate(info.prefabs[1], target);
                    buff.name = info.prefabs[1].name;
                }
            }
        }
    }
}