using UnityEngine;

public class JewelryFirePoisonNecklace : Jewelry {
    public float baseCooldown = 4;

    private float FinalCooldown {
        get {
            var skillHaste = 0f;
            if (controller != null) {
                skillHaste = controller.attribute.skillHaste;
            }
            if (skillHaste >= 0) {
                return baseCooldown / (1 + (skillHaste / 100));
            }

            return baseCooldown * (1 - skillHaste / 100);
        }
    }
    
    private bool _ready = true;
    private int _counter;

    protected override void Start() {
        base.Start();
        SimpleEventManager.AddEventListener<PlayerBulletHitEnemyEvent>(OnPlayerBulletHitEnemy);
    }

    private void OnDestroy() {
        SimpleEventManager.RemoveListener<PlayerBulletHitEnemyEvent>(OnPlayerBulletHitEnemy);
    }

    public void OnPlayerBulletHitEnemy(PlayerBulletHitEnemyEvent @event) {
        if (CheckUsable() && @event != null && @event.damageTrigger && @event.enemy && _ready) {
            var attribute = _counter++ % 2 == 0 ? JewelryAttribute.Fire : JewelryAttribute.Poison;
            if (attribute == JewelryAttribute.Fire) {
                Instantiate(info.prefabs[0], @event.enemy.transform);
                Instantiate(info.prefabs[2], @event.enemy.transform.position + @event.enemy.gameObject.GetColliderOffset(),
                    Quaternion.identity, RGGameSceneManager.Inst.temp_objects_parent);
            } else {
                Instantiate(info.prefabs[1], @event.enemy.transform);
                Instantiate(info.prefabs[3], @event.enemy.transform.position + @event.enemy.gameObject.GetColliderOffset(),
                    Quaternion.identity, RGGameSceneManager.Inst.temp_objects_parent);
            }
            
            _ready = false;
            Invoke(nameof(SetReadyTrue), FinalCooldown);
        }
    }

    public override float ProcessValue(JewelryAttribute attribute, float value) {
        if (CheckUsable()) {
            if (attribute == JewelryAttribute.Fire) {
                return value + GetAttributeValue(JewelryAttribute.Fire);
            }

            if (attribute == JewelryAttribute.Poison) {
                return value + GetAttributeValue(JewelryAttribute.Poison);
            }
        }

        return value;
    }

    private void SetReadyTrue() {
        _ready = true;
    }
}