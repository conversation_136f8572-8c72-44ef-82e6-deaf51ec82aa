using RGScript.Net.StateSync.Core;
using UnityEngine;

public class JewelryDestroyBox : Jewelry {
    protected override void Start() {
        base.Start();
        SimpleEventManager.AddEventListener<BoxDestroySimpleEvent>(OnBoxDestroy);
    }

    protected void OnDestroy() {
        SimpleEventManager.RemoveEventListener<BoxDestroySimpleEvent>(OnBoxDestroy);
    }

    private int _chestCount;
    private const int MaxChestCount = 3;
    private const string MarkString = "JewelryItem.DestroyBox";

    private void OnBoxDestroy(BoxDestroySimpleEvent e) {
        if (!CheckUsable() || e.box == null) {
            return;
        }

        if (!GameUtil.InGameScene || !StateSynchronizer.IsHost) {
            return;// 联机模式下只有主机生成后再同步，不需要重复计算
        }

        BattleData.data.AddMark(MarkString);
        if (BattleData.data.GetMark(MarkString) < 25 || _chestCount >= MaxChestCount) {
            return;
        }
        
        BattleData.data.ResetMark(MarkString);
        _chestCount++;
        var pos = e.box.GetPosition();
        var luck = Random.Range(0, 100);
        string chestId = luck switch {
            < 60 => "chest00",
            < 90 => "chest01",
            _ => "chest02"
        };

        Vector3 localPos = pos - RGGameSceneManager.Inst.temp_objects_parent.position;
        var rgChestObjectData = new RgChestObjectData {
            ChestId = chestId,
            Name = chestId,
            Position = new SoulKnight.Runtime.Common.Data.Vector3Data(localPos)
        };
        StateSynchronizer.Instance.ServerDispatchObject(rgChestObjectData);
    }
}