<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Rewired_Linux</name>
    </assembly>
    <members>
        <member name="T:Rewired.Platforms.Linux.Udev.UdevControllerExtension">
            <summary>
            Provides information about a Udev device.
            </summary>
        </member>
        <member name="P:Rewired.Platforms.Linux.Udev.UdevControllerExtension.systemPath">
            <summary>
            System path.
            </summary>
        </member>
        <member name="P:Rewired.Platforms.Linux.Udev.UdevControllerExtension.systemName">
            <summary>
            System name.
            </summary>
        </member>
        <member name="P:Rewired.Platforms.Linux.Udev.UdevControllerExtension.systemNumber">
            <summary>
            System number.
            </summary>
        </member>
        <member name="P:Rewired.Platforms.Linux.Udev.UdevControllerExtension.deviceNodePath">
            <summary>
            Device node path.
            </summary>
        </member>
        <member name="P:Rewired.Platforms.Linux.Udev.UdevControllerExtension.deviceType">
            <summary>
            Device node path.
            </summary>
        </member>
        <member name="P:Rewired.Platforms.Linux.Udev.UdevControllerExtension.address">
            <summary>
            Address.
            </summary>
        </member>
        <member name="P:Rewired.Platforms.Linux.Udev.UdevControllerExtension.version">
            <summary>
            Version.
            </summary>
        </member>
        <member name="P:Rewired.Platforms.Linux.Udev.UdevControllerExtension.busType">
            <summary>
            Bus type.
            </summary>
        </member>
        <member name="P:Rewired.Platforms.Linux.Udev.UdevControllerExtension.serialNumber">
            <summary>
            Serial number.
            </summary>
        </member>
        <member name="P:Rewired.Platforms.Linux.Udev.UdevControllerExtension.productName">
            <summary>
            HID product name string.
            </summary>
        </member>
        <member name="P:Rewired.Platforms.Linux.Udev.UdevControllerExtension.manufacturer">
            <summary>
            HID manufacturer string.
            </summary>
        </member>
        <member name="P:Rewired.Platforms.Linux.Udev.UdevControllerExtension.vendorId">
            <summary>
            HID vendor id.
            </summary>
        </member>
        <member name="P:Rewired.Platforms.Linux.Udev.UdevControllerExtension.productId">
            <summary>
            HID product id.
            </summary>
        </member>
        <member name="P:Rewired.Platforms.Linux.Udev.UdevControllerExtension.isBluetoothDevice">
            <summary>
            Is the device a Bluetooth device?
            </summary>
        </member>
        <member name="P:Rewired.Platforms.Linux.Udev.UdevControllerExtension.usagePage">
            <summary>
            HID usage page.
            </summary>
        </member>
        <member name="P:Rewired.Platforms.Linux.Udev.UdevControllerExtension.usage">
            <summary>
            HID usage.
            </summary>
        </member>
    </members>
</doc>
