using Activities;
using NewDynamicConfig;
using RGScript.CheckIn;
using RGScript.Util.LifeCycle;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace Notification {
    /// <summary>
    /// 本地通知管理器
    /// </summary>
    public class NotifyMgr {
        private const string TAG = "NotifyMgr";
        private static NotificationConfig Config => ConfigManager.GetCurrectUseConfig<NotificationConfig>();
        private static List<DateTime> _prevNotificationDates = new List<DateTime>();
        private static bool _fromPush = false; // 是否通过点击push打开app

        /// <summary>
        /// 游戏启动的时候调用一次，且仅一次有效
        /// </summary>
        public static void InitPushFrom() {
            _fromPush = NotificationSender.IsAppStartFromPush();
        }

        public static bool IsAppStartFromPush() {
            return _fromPush;
        }

        public static void InitScheduleNotifies() {
            LogUtil.Log("InitScheduleNotifies...", TAG);
            // 先清空之前的所有通知
            NotificationSender.ResetAllNotifications();

            LogUtil.Log(Config, TAG);
            if (LogUtil.IsShowLog) {
                LogUtil.Log(Config.ToString(), TAG);
            }

            if (!Config.OpenForAllChannels) {
                var channel = ChannelConfig.GetChannelName();
                if (!Config.AvailableChannels.Contains(channel)) {
                    return;
                }
            }
            
            var accId = LifeCycleManager.Instance.GetChillyUid;
            if (LogUtil.IsShowLog) {
                LogUtil.Log($"InitScheduleNotifies accId: {accId}", TAG);
            }

            if (string.IsNullOrEmpty(accId)) {
                return;
            }

            InitNewbieCheckinNotify();
            InitVersionNotify();
            InitActivityEndNotify();
            InitComebackNotify();
        }

        private static bool InNewbieCheckin() {
            var newbieCheckin = StatisticData.data.open_checkin == (int)CheckInOpenStatus.Opening;
            var hasClaimAllBonus = CheckInManager.HasClaimAllBonuses();
            if (LogUtil.IsShowLog) {
                LogUtil.Log($"newbieCheckin: {newbieCheckin}, hasClaimAllBonus: {hasClaimAllBonus}", TAG);
            }

            return newbieCheckin && !hasClaimAllBonus;
        }

        /// <summary>
        /// 回流通知
        /// 排除新手期间未领取完奖励的用户
        /// </summary>
        private static void InitComebackNotify() {
            if (InNewbieCheckin()) {
                return;
            }

            DateTime today = DateTime.Now;
            DateTime sevenDaysLater = today.AddDays(Config.Next7days);

            // Calculate the number of days until the next Saturday
            int daysUntilSaturday = ((int)DayOfWeek.Saturday - (int)sevenDaysLater.DayOfWeek + 7) % 7;

            // If today is Saturday, we need to add 7 days to get the next Saturday
            if (daysUntilSaturday == 0) {
                daysUntilSaturday = 7;
            }

            // Set the time to 10 AM
            DateTime nextSaturday = sevenDaysLater.AddDays(daysUntilSaturday);
            DateTime notificationTime = new DateTime(nextSaturday.Year, nextSaturday.Month, nextSaturday.Day, Config.TriggerHour, 0, 0);

            if (!_prevNotificationDates.Contains(notificationTime)) {
                var title = I2.Loc.ScriptLocalization.Get("App_Name");
                var text = I2.Loc.ScriptLocalization.Get("notify/text_0", "#周末何不来一场地牢之旅！");
                
                NotificationSender.SendNotification(title, text, notificationTime);
                _prevNotificationDates.Add(notificationTime);

                if (LogUtil.IsShowLog) {
                    LogUtil.Log("[InitComebackNotify] Next Notification Date is: " + notificationTime, TAG);
                }
            }
        }

        /// <summary>
        /// 活动结束通知
        /// </summary>
        private static void InitActivityEndNotify() {
            if (InNewbieCheckin()) {
                return;
            }

            var activityEndTime = Config.ActivityTimeEnd;
            if (string.IsNullOrEmpty(activityEndTime)) {
                if (LogUtil.IsShowLog) {
                    Debug.LogError("[InitActivityEndNotify] Config.ActivityTimeEnd is null!!!");
                }

                return;
            }

            DateTime utcTime = DateTimeOffset.FromUnixTimeSeconds(long.Parse(activityEndTime)).UtcDateTime;
            DateTime localTime = utcTime.ToLocalTime();

            DateTime sevenDaysBefore = localTime.AddDays(-Config.Next7days);
            DateTime notificationTime = new DateTime(sevenDaysBefore.Year, sevenDaysBefore.Month, sevenDaysBefore.Day, Config.TriggerHour, 0, 0);

            if (DateTime.Now < notificationTime && !_prevNotificationDates.Contains(notificationTime)) {
                // 活动结束前7天早上10点提醒
                var title = I2.Loc.ScriptLocalization.Get("App_Name");
                var text = string.Format(I2.Loc.ScriptLocalization.Get("notify/text_1", "#活动还有{0}天就结束了！快来体验有趣的活动新玩法吧！"), 7);
                
                NotificationSender.SendNotification(title, text, notificationTime);
                _prevNotificationDates.Add(notificationTime);

                if (LogUtil.IsShowLog) {
                    LogUtil.Log("[InitActivityEndNotify] 7d Next Notification Date is: " + notificationTime, TAG);
                }
            }

            DateTime sevenDaysBefore2 = localTime.AddDays(-3);
            DateTime notificationTime2 = new DateTime(sevenDaysBefore2.Year, sevenDaysBefore2.Month, sevenDaysBefore2.Day, Config.TriggerHour, 0, 0);

            if (DateTime.Now < notificationTime2 && !_prevNotificationDates.Contains(notificationTime2)) {
                // 活动结束前3天早上10点提醒
                var title = I2.Loc.ScriptLocalization.Get("App_Name");
                var text = string.Format(I2.Loc.ScriptLocalization.Get("notify/text_1", "#活动还有{0}天就结束了！快来体验有趣的活动新玩法吧！"), 3);
                
                NotificationSender.SendNotification(title, text, notificationTime2);
                _prevNotificationDates.Add(notificationTime2);

                if (LogUtil.IsShowLog) {
                    LogUtil.Log("[InitActivityEndNotify] 3d Next Notification Date is: " + notificationTime2, TAG);
                }
            }
        }

        /// <summary>
        /// 版本更新通知
        /// </summary>
        private static void InitVersionNotify() {
            if (InNewbieCheckin()) {
                return;
            }

            var nextVersionPublishTime = Config.NextVersionPublishTime;
            var nextVersion = Config.NextVersion;
            if (string.IsNullOrEmpty(nextVersionPublishTime) || string.IsNullOrEmpty(nextVersion)) {
                if (LogUtil.IsShowLog) {
                    Debug.LogError("[InitVersionNotify] Config.nextVersionPublishTime is null!!!");
                }

                return;
            }

            var currVersion = CloudSaveUtil.version;
            var compareRes = GameUtil.VersionCompare(currVersion, nextVersion);
            if (compareRes >= 0) {
                // 已是最新版本
                if (LogUtil.IsShowLog) {
                    LogUtil.Log($"[InitVersionNotify] App.version:{currVersion} next.version:{nextVersion} res:{compareRes} >= 0: ignore", TAG);
                }

                return;
            }

            DateTime utcTime = DateTimeOffset.FromUnixTimeSeconds(long.Parse(nextVersionPublishTime)).UtcDateTime;
            DateTime localTime = utcTime.ToLocalTime();
            int daysUntilSaturday = ((int)DayOfWeek.Saturday - (int)localTime.DayOfWeek + 7) % 7;
            DateTime nextSaturday = localTime.AddDays(daysUntilSaturday);
            DateTime notificationTime = new DateTime(nextSaturday.Year, nextSaturday.Month, nextSaturday.Day, Config.TriggerHour, 0, 0);
            if (DateTime.Now > notificationTime) {
                // 已过通知时间，不再弹了（已登录旧版已可以收到通知）
                return;
            }

            if (!_prevNotificationDates.Contains(notificationTime)) {
                var title = I2.Loc.ScriptLocalization.Get("App_Name");
                var text = I2.Loc.ScriptLocalization.Get("notify/text_2", "#《元气骑士》新版本上线啦！骑士团等着你一起闯地牢！");
                
                NotificationSender.SendNotification(title, text, notificationTime);
                _prevNotificationDates.Add(notificationTime);

                if (LogUtil.IsShowLog) {
                    LogUtil.Log("[InitVersionNotify] version update Notification Date is: " + notificationTime, TAG);
                }
            }
        }

        /// <summary>
        /// 新手签到通知
        /// </summary>
        private static void InitNewbieCheckinNotify() {
            if (!InNewbieCheckin()) {
                return;
            }

            DateTime now = DateTime.Now;
            DateTime timeAfter24Hours = now.AddHours(Config.Next24Hours);

            // 24小时以后的首个10AM
            DateTime next10AM = new DateTime(timeAfter24Hours.Year, timeAfter24Hours.Month, timeAfter24Hours.Day, Config.TriggerHour, 0, 0);
            if (next10AM <= timeAfter24Hours) {
                next10AM = next10AM.AddDays(1);
            }

            var title = I2.Loc.ScriptLocalization.Get("App_Name");
            var text = I2.Loc.ScriptLocalization.Get("notify/text_3", "#《元气骑士》快乐轻松打枪！上线领取签到奖励！");

            NotificationSender.SendNotification(title, text, next10AM);
            _prevNotificationDates.Add(next10AM);

            if (LogUtil.IsShowLog) {
                LogUtil.Log("[InitNewbieCheckinNotify] next24h Notification Date is: " + next10AM, TAG);
            }

            // 24小时以后的首个周六10AM
            int daysUntilSaturday = ((int)DayOfWeek.Saturday - (int)timeAfter24Hours.DayOfWeek + 7) % 7;

            if (daysUntilSaturday == 0) {
                daysUntilSaturday = 7;
            }

            DateTime nextSaturday = timeAfter24Hours.AddDays(daysUntilSaturday);
            DateTime nextSaturdayAt10AM = new DateTime(nextSaturday.Year, nextSaturday.Month, nextSaturday.Day, Config.TriggerHour, 0, 0);

            // 如果两个日期重合，则只发第一个通知
            if (nextSaturdayAt10AM == next10AM) {
                if (LogUtil.IsShowLog) {
                    LogUtil.Log("[InitNewbieCheckinNotify] next Saturday conflict with next 24h! ignore ", TAG);
                }

                return;
            }

            NotificationSender.SendNotification(title, text, nextSaturdayAt10AM);
            _prevNotificationDates.Add(nextSaturdayAt10AM);
            if (LogUtil.IsShowLog) {
                LogUtil.Log("[InitNewbieCheckinNotify] nextSaturdayAt10AM Notification Date is: " + nextSaturdayAt10AM, TAG);
            }
        }
    }
}