// Unity built-in shader source. Copyright (c) 2016 Unity Technologies. MIT license (see license.txt)

Shader "Custom/Effect/Wobble-SpriteRenderer"
{
    Properties
    {
        _Frequency("Frequency", float) = 20
        _Tile("Tile", float) = 1
        _HShift("HShift", float) = 0
        _VShift("VShift", float) = 0
        _WobbleAmount("WobbleAmount", float) = 0.01
        _NoiseTex("NoiseTex", 2D) = "white" {}
        
        [PerRendererData] _MainTex ("Sprite Texture", 2D) = "white" {}
        _Color ("Tint", Color) = (1,1,1,1)
        [MaterialToggle] PixelSnap ("Pixel snap", Float) = 0
        [HideInInspector] _RendererColor ("RendererColor", Color) = (1,1,1,1)
        [HideInInspector] _Flip ("Flip", Vector) = (1,1,1,1)
        [PerRendererData] _AlphaTex ("External Alpha", 2D) = "white" {}
        [PerRendererData] _EnableExternalAlpha ("Enable External Alpha", Float) = 0
    }

    SubShader
    {
        Tags
        {
            "Queue"="Transparent"
            "IgnoreProjector"="True"
            "RenderType"="Transparent"
            "PreviewType"="Plane"
            "CanUseSpriteAtlas"="True"
        }

        Cull Off
        Lighting Off
        ZWrite Off
        Blend One OneMinusSrcAlpha

        Pass
        {
        CGPROGRAM
            sampler2D _NoiseTex;
            float4 _NoiseTex_ST;
            fixed _Frequency;
            fixed _Tile;
            fixed _HShift;
            fixed _VShift;
            fixed _WobbleAmount;
        
            #pragma vertex vert
            #pragma fragment frag
            #pragma target 2.0
            #pragma multi_compile_instancing
            #pragma multi_compile _ PIXELSNAP_ON
            #pragma multi_compile _ ETC1_EXTERNAL_ALPHA
            #include "UnitySprites.cginc"

            struct custom_v2f
            {
                float4 vertex   : SV_POSITION;
                fixed4 color    : COLOR;
                float2 texcoord : TEXCOORD0;
                float2 noisecoord : TEXCOORD1;
                UNITY_VERTEX_OUTPUT_STEREO
            };

            custom_v2f vert (appdata_t IN)
            {
                custom_v2f OUT;

                UNITY_SETUP_INSTANCE_ID (IN);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(OUT);

                OUT.vertex = UnityFlipSprite(IN.vertex, _Flip);
                OUT.vertex = UnityObjectToClipPos(OUT.vertex);
                OUT.texcoord = IN.texcoord;
                OUT.color = IN.color * _Color * _RendererColor;

                OUT.noisecoord = TRANSFORM_TEX(IN.texcoord, _NoiseTex);

                #ifdef PIXELSNAP_ON
                OUT.vertex = UnityPixelSnap (OUT.vertex);
                #endif

                return OUT;
            }

            fixed2 random(fixed n)
            {
                return fixed2(sin(n * UNITY_PI * 2 + _Time.x * _Frequency), cos(n * UNITY_PI * 2 + _Time.x * _Frequency));
            }

            fixed4 frag(custom_v2f IN) : SV_Target
            {
                fixed noise = tex2D(_NoiseTex, frac(IN.noisecoord * _Tile - fixed2(_HShift, _VShift) * _Time.y));
                fixed2 uvshift = random(noise) * _WobbleAmount;
                fixed4 c = SampleSpriteTexture (IN.texcoord + uvshift) * IN.color;
                c.rgb *= c.a;
                return c;
            }
        ENDCG
        }
    }
}
