using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace ModeSeason.Artifacts {
    public class WeaponComLightningEmitter : WeaponComponentBulletEmitter {
        public int count = 1;
        public int chainLength = 0;
        public float range = 16;
        public float lifeTime = 0.25f;
        public ArtifactBulletInfoInspector bulletConfig;

        protected override ArtifactBulletInfoInspector _GetBulletConfig(int atkSequence, int attackTypeParam) {
            return bulletConfig;
        }

        protected override void OnEmitFromByTrigger(object arg) {
            var emitSource = transform.gameObject;
            RGEController sourceEnemy = null;
            if (arg != null && arg is RGEController enemy) {
                emitSource = enemy.transform.gameObject;
            }
            EmitFromSource(emitSource, sourceEnemy);
        }

        void EmitFromSource(GameObject emitSource, RGEController sourceEnemy) {
            if (emitSource == null)
                emitSource = transform.gameObject;

            var bulletInfo = GetBulletConfig(0, 0);

            if (bulletInfo == null) {
                return;
            }

            var damageInfo = WeaponComponentBulletEmitter.GetDamageInfo(bulletInfo, weapon);

            var targets = GetTargets(emitSource.transform.position, sourceEnemy);
            RandomUtil.ShuffleList(targets, weapon.rg_random);
            for (var i = 0; i < Mathf.Min(targets.Count, count); ++i) {
                var genCount = chainLength + 1;
                Transform currentSource = emitSource.transform;
                RGEController currentTarget = targets[i];
                for (var j = 0; j < genCount; ++j) { // 连锁
                    GenLightningBetween(currentSource, currentTarget, bulletInfo, damageInfo);
                    currentSource = currentTarget.transform;
                    var nextTargets = GetTargets(currentSource.position, currentTarget);
                    if (nextTargets.Count > 0) {
                        RandomUtil.ShuffleList(nextTargets, weapon.rg_random);
                        currentTarget = nextTargets[0];
                    } else {
                        break;
                    }
                }
            }
        }

        void GenLightningBetween(Transform t1, RGEController enemy, BulletInfoInspector bulletInfo, DamageInfo damageInfo) {
            var lightningObj = GameObject.Instantiate(bulletConfig.bulletProto);
            var thunder = lightningObj.GetComponent<Thunder>();
            thunder.SetUp(t1, enemy.transform, lifeTime, bulletInfo.size);
            var damageCarrier = thunder.GetComponent<DamageCarrier>();
            var bInfo = bulletInfo.GetBulletInfo().SetSourceObject(weapon.controller.gameObject).SetSourceWeapon(weapon);
            bInfo.customData = CreateArtifactDamageInfo(0);
            damageCarrier.UpdateInfo(bInfo, damageInfo);
            var offensive = damageCarrier.GetComponent<OffensiveInterface>();
            if (offensive != null) {
                offensive.SetSourceObject(weapon.controller.gameObject);
            }

            var damage = damageInfo.damage;
            var isCrit = weapon.rg_random.Range(0, 100) < damageInfo.critic;
            if (isCrit)
                damage *= 2;
            var hurtInfo = new HurtInfo {
                Source = weapon.controller.gameObject,
                Critic = isCrit,
                Damage = damage,
                SourceWeapon = weapon,
                FingerPrint = damageCarrier.fingerPrint
            };
            DamageCarrierHelper.ProcessEnemyGetHurtDamage(enemy, hurtInfo);
            SimpleEventManager.Raise(PlayerBulletHitEnemyEvent.UseCache(enemy, damage, isCrit, null, damageCarrier, damageCarrier.GetSourceObject(), weapon, enemy.transform.position));
        }

        public override void OnEmit(int atkSeqno, int atkTypeParam) {
            EmitFromSource(null, null);
        }

        private RaycastHit2D[] _hitCache = new RaycastHit2D[1];
        List<RGEController> GetTargets(Vector3 center, RGEController sourceEnemy) {
            var ret = new List<RGEController>();
            var hits = Physics2D.CircleCastAll(center, range, Vector2.zero, 0, LayerMask.GetMask("Body_E"));
            for (var i = 0; i < hits.Length; ++i) {
                var enemy = hits[i].transform.GetComponent<RGEController>();
                Vector2 end = center + Vector3.up * 0.5f;
                Vector2 begin = hits[i].transform.position + Vector3.up * 0.5f;
                if (enemy != sourceEnemy && enemy && !enemy.dead && Physics2D.LinecastNonAlloc(begin, end, _hitCache, PhysicsUtil.AllObstacleMask) == 0)
                    ret.Add(enemy);
            }
            return ret;
        }
    }
}