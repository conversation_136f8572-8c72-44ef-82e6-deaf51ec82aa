using Sirenix.OdinInspector;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace ModeSeason.Artifacts {

    [System.Serializable]
    public class SpecialBulletInfoIndices {
        public int[] indices;
    }

    /// <summary>
    /// 普通子弹发射器
    /// </summary>
    public class WeaponComSimpleBulletEmitter : WeaponComponentBulletEmitter {
        [LabelText("子弹配置")]
        public ArtifactBulletInfoInspector[] bulletInfo = new ArtifactBulletInfoInspector[1] { new ArtifactBulletInfoInspector() };
        public SpecialBulletInfoIndices[] specialBulletIndices = new SpecialBulletInfoIndices[0];
        public ArtifactBulletInfoInspector[] specialBulletInfo = new ArtifactBulletInfoInspector[0];
        


        protected override ArtifactBulletInfoInspector _GetBulletConfig(int atkSequence, int attackTypeParam) {
            if (bulletInfo.Length > 0) {
                var idx = Mathf.Clamp(atkSequence, 0, bulletInfo.Length - 1);
                var ret = bulletInfo[idx];
                if (attackTypeParam > 0) {
                    var specialBulletCfgIdx = attackTypeParam - 1;
                    if (specialBulletIndices.Length > 0) {
                        var indexArray = specialBulletIndices[Mathf.Clamp(specialBulletCfgIdx, 0, specialBulletInfo.Length - 1)].indices;
                        idx = indexArray[Mathf.Clamp(atkSequence, 0, indexArray.Length - 1)];
                        ret = specialBulletInfo[Mathf.Clamp(idx, 0, specialBulletInfo.Length - 1)];
                    }
                }
                return ret;
            }
            
            if (!isDefaultEmitter) {
                var defaultBulletEmitter = weapon.GetDefaultEmitter();
                if (defaultBulletEmitter != this) {
                    var ret = defaultBulletEmitter.GetBulletConfig(atkSequence, attackTypeParam);
                    return ret;
                }
            }

            return null;
        }
    }

}