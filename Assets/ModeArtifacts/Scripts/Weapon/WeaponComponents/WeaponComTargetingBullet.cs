using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace ModeSeason.Artifacts {
    /// <summary>
    /// 神器词条：子弹追踪
    /// </summary>
    public class WeaponComTargetingBullet : ArtifactWeaponComponent {
        public bool randomTarget = false;
        public float angleSpeed = 10;
        public float updateInterval = 0.1f;
        public float followDelay = 0;
        public int limitTimes = 0;

        public override Command OnEquipWeapon() {
            base.OnEquipWeapon();
            SimpleEventManager.AddEventListener<CreateBulletEvent>(ModifyBulletSpeed);
            SetCleanUp(() => {
                SimpleEventManager.RemoveListener<CreateBulletEvent>(ModifyBulletSpeed);
            });
            return null;
        }

        public override Command OnDropWeapon() {
            base.OnDropWeapon();
            DoCleanUp();
            return null;
        }

        void ModifyBulletSpeed(EventBase data) {
            var e = (CreateBulletEvent)data;
            if (e.bullet.camp == 1 && !e.melee) { // 玩家子弹
                BulletMoverFollowAttachment.SetupBulletFollower(e.bullet.gameObject, randomTarget, true, angleSpeed, 0, updateInterval, followDelay, limitTimes);
            }
        }
    }
}