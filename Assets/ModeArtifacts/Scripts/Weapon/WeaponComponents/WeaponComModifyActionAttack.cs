using Sirenix.OdinInspector;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace ModeSeason.Artifacts {
    /// <summary>
    /// 修改攻击模式
    /// </summary>
    public class WeaponComModifyActionAttack : ArtifactWeaponComponent {
        public string modifyKey;
        public bool modifyAsChargeAttack;
        [ShowIf("modifyAsChargeAttack")] public bool noChargeEvent = true;
        [ShowIf("modifyAsChargeAttack")] public float maxChargeTime = 1f;

        public override Command OnEquipWeapon() {
            base.OnEquipWeapon();
            if (!weapon.attackActionComModified) {
                weapon.attackActionComModified = true;
                weapon.attackActionComModifyKey = modifyKey;

                weapon.modifyAsChargeAttack = modifyAsChargeAttack;
                weapon.modifyAsChargeAttack_NoEvent = noChargeEvent;
                weapon.modifyAsChargeAttack_MaxChargeTime = maxChargeTime;
            }

            return null;
        }

        public override Command OnDropWeapon() {
            if (weapon.attackActionComModified && string.Equals(weapon.attackActionComModifyKey, modifyKey)) {
                weapon.attackActionComModified = false;
                weapon.attackActionComModifyKey = null;
            }
            base.OnDropWeapon();
            return null;
        }
    }
}