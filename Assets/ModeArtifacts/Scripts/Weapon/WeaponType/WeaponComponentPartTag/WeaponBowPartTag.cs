using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace ModeSeason.Artifacts {
    public class WeaponBowPartTag : MonoBehaviour, IWeaponPartTag {
        public List<ArtifactBowComPart.Part> parts;
        public int[] GetTags() {
            var list = new List<int>();
            foreach (var tag in parts)
                list.Add((int)tag);
            return list.ToArray();
        }
    }
}