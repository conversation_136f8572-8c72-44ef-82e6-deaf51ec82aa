using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ObjSurroundingFly : MonoBehaviour {
    public Transform center;
    public float minDistanceToCenter = 0.7f;
    public float maxDistanceToCenter = 1;
    public bool flyToInitPos = false;
    public float speed = 2;

    public float turnDirectionProbability = 0.5f;
    public float maxTurnDirectionAngle = 10;

    private float _turnLeftCount = 0;
    private float _turnRightCount = 0;
    private Vector3 currentDestPos;
    void Start() {
        InitPos(flyToInitPos);
    }
    
    

    void InitPos(bool fly) {
        currentDestPos = Quaternion.Euler(0, 0, Random.value * 360) * Vector3.right * Random.Range(minDistanceToCenter, maxDistanceToCenter);
        if (!fly) {
            transform.position = center.transform.position + currentDestPos;
        }

        _turnLeftCount = _turnRightCount = 0;
    }

    void Update() {
        var destPos = center.transform.position + currentDestPos;
        var diff = destPos - transform.position;
        if (speed * Time.deltaTime >= diff.magnitude) {
            transform.position = destPos;
            InitPos(true);
            return;
        }

        var dir = diff.normalized;
        if (Random.value < (diff.magnitude / maxDistanceToCenter) * turnDirectionProbability) {
            // 转向
            var right_p = ((_turnLeftCount - _turnRightCount) / maxTurnDirectionAngle) * 0.1f + 0.5f;
            var turnAngle = Random.value * maxTurnDirectionAngle;
            if (Random.value < right_p) {
                // 转右
                _turnRightCount += turnAngle;
                dir = Quaternion.Euler(0, 0, -turnAngle) * dir;
            } else {
                // 转左
                _turnLeftCount += turnAngle;
                dir = Quaternion.Euler(0, 0, turnAngle) * dir;
            }
        }

        transform.right = Vector3.right * (Vector3.Dot(dir, Vector3.right) >= 0 ? 1 : -1);

        transform.position += speed * Time.deltaTime * dir;
    }
}
