using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace ModeSeason.Artifacts {
    public class ArtifactModeShopPriceMod : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RGContainer.IShopPriceModifier {
        public int Modify(int oldPrice) {
            var ret = oldPrice;
            if (gameObject.GetComponent<TalkStatue>() != null) {
                ret = ArtifactsUtil.GetConstInt("statuePrice");
            }
            return ret;
        }
    }
}