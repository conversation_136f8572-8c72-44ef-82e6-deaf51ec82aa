using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace ModeSeason.Artifacts {
    public class ArtifactSyncedWeaponPart : ArtifactSyncedItem {

        private GameObject fxOnFloor;
        public ArtifactComponentItem weaponPartItem { get; private set; }
        protected override void Start() {
            base.Start();
            if (weaponPartItem == null) {
                var partParams = resParam.Split('|');
                var weaponType = System.Enum.Parse<emWeaponType>(partParams[0]);
                var partName = partParams[1];
                var rarity = System.Int32.Parse(partParams[2]);
                var elementType = System.Int32.Parse(partParams[3]);
                var attrStr = partParams.Length > 4 ? partParams[4] : "";
                var weaponPartPrefab = ArtifactModeConfig.Inst.FindPartByName(weaponType, partName);
                var partObj = ArtifactsLevelData.CreateWeaponPart(weaponPartPrefab, rarity, new GameObject[0], elementType);
                var attrList = new List<ArtifactsModeData.ArtifactsAttributeInfo>();
                var part = partObj.GetComponent<ArtifactComponentPart>();
                if (attrStr.Length > 0) {
                    var attrArr = attrStr.Split(',');
                    for (var i = 0; i < attrArr.Length; i++) {
                        var attr = attrArr[i].Split(':');
                        var attrType = System.Enum.Parse<ArtifactAttributeType>(attr[0]);
                        var index = System.Int32.Parse(attr[1]);
                        attrList.Add(new ArtifactsModeData.ArtifactsAttributeInfo() {
                            attributeType = attrType,
                            index = index
                        });
                    }
                } else {
                    attrList = ArtifactsUtil.RandomAttribute(weaponType, weaponPartItem.part.rarity, new RGRandom(BattleData.data.levelIndex + part.partID));
                }
                part.attributes = attrList;
                SetPartObj(partObj.GetComponent<ArtifactComponentItem>());

                if (RGGameSceneManager.Inst.controller != null && !ControllerCanUse(RGGameSceneManager.Inst.controller)) {
                    gameObject.SetActive(false);
                }
            }
        }

        public void SetPartObj(ArtifactComponentItem part) {
            weaponPartItem = part;
            part.transform.SetParent(transform);
            part.transform.localScale = Vector3.one;
            part.transform.localPosition = Vector3.up * 0.15f;

            if (fxOnFloor == null && ArtifactModeConfig.Inst) {
                fxOnFloor = GameObject.Instantiate(ArtifactModeConfig.Inst.partFXOnFloor, transform);
                fxOnFloor.transform.localPosition = Vector3.zero;
                var partParams = resParam.Split('|');
                var rarity = System.Int32.Parse(partParams[2]);
                for (var i = 0; i < fxOnFloor.transform.childCount; ++i) {
                    if (i == rarity)
                        fxOnFloor.transform.GetChild(i).gameObject.SetActive(true);
                    else
                        fxOnFloor.transform.GetChild(i).gameObject.SetActive(false);
                }

                fxOnFloor.transform.eulerAngles = Vector3.zero;
            }
        }

        public void Recycle(int price) {
            ArtifactsModeData.Inst.AddRecyclePartLog(weaponPartItem.part.name, weaponPartItem.part.rarity);

            var position = transform.position;
            GameObject.Destroy(gameObject);
            ArtifactsUtil.CreateEmbers(position, price);
        }

        protected override bool ControllerCanUse(RGController controller) {
            if (RGGameSceneManager.Inst != null && RGGameSceneManager.Inst.game_state == 1) {
                return false;
            }
            var modeData = ArtifactsUtil.GetModeData(controller);
            if (modeData == null || weaponPartItem == null || weaponPartItem.part == null || modeData.weaponType != weaponPartItem.part.weaponType) {
                return false;
            }
            return true;
        }

        protected override bool IsUseForbidTemporarily(RGController controller) {
            if (controller.skillCasting) {
                return true;
            }

            var w = controller.hand.front_weapon as RGWeaponArtifactBody;
            if (w == null) {
                return true;
            }

            if (w.isAttacking) {
                return true;
            }

            return false;
        }
        
        public override void OnPickUp(RGController controller) {
            if (!ControllerCanUse(controller)) {
                return;
            }
            weaponPartItem.ItemTrigger(controller);
            GameObject.Destroy(gameObject);
        }

        public override void OnDisplayDesciption(bool show) {
            var controller = RGGameSceneManager.Inst.controller as RGController;
            if (controller != null && !ControllerCanUse(controller)) {
                return;
            }
            if (UICanvas.Inst != null) {
                if (show) {
                    if (weaponPartItem != null) {
                        weaponPartItem.DisplayInfoUI(item_value > 0);
                    }
                } 
                else {
                    if (weaponPartItem != null) {
                        weaponPartItem.HideInfoUI();
                    }
                }
            }
        }

        public override string GetItemName() {
            if (weaponPartItem != null) {
                return weaponPartItem.GetItemName();
            }
            return "";
        }

        public static ArtifactSyncedWeaponPart Create(emWeaponType weaponType, string partName, int rarity, List<ArtifactsModeData.ArtifactsAttributeInfo> attributes) {
            var itemPrefab = ResourcesUtil.Load<GameObject>("ModeArtifacts/Prefab/SyncedItems/ArtifactSyncedWeaponPart.prefab");
            var syncedItemObj = GameObject.Instantiate(itemPrefab, RGGameSceneManager.Inst.temp_objects_parent);
            syncedItemObj.name = partName;
            var syncedItem = syncedItemObj.GetComponent<ArtifactSyncedWeaponPart>();
            var attrStr = "";
            if (attributes != null) {
                for (var i = 0; i < attributes.Count; i++) {
                    var attr = attributes[i];
                    attrStr += $"{attr.attributeType}:{attr.index}";
                    if (i < attributes.Count - 1) {
                        attrStr += ",";
                    }
                }
            }
            syncedItem.resParam = $"{weaponType}|{partName}|{rarity}|0|{attrStr}";
            return syncedItem;
        }
    }
}