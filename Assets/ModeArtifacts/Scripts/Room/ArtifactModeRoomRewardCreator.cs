using MapSystem;
using MapSystem.Scriptable;
using RGScript.Map;
using UnityEngine;

/// <summary>
/// 生成神器模式战斗房间奖励
/// </summary>
public class ArtifactModeRoomRewardCreator : MonoBehaviour, BattleRoomRewardCreator {
    public GameObject battleRoomChest;
    public GameObject battleRoomEliteChest;
    public bool OnCreateReward(RGRoomX room) {
        var prefab = battleRoomChest;
        if (room.EMaker.AlwaysBadass)
            prefab = battleRoomEliteChest;
        
        if (prefab == null) {
            Debug.LogError($"CreateRewardChest: {prefab} IS NULL");
            return false;
        }
        
        var roomObj = prefab.GetComponent<IOccupiedRoomObject>();
        if (roomObj == null) {
            Debug.LogError($"宝箱{prefab}没有挂载IOccupiedRoomObject组件!");
        }
        if (room.Grid == null && !BattleData.data.IsSandbox) {
            Debug.LogError($"房间{name}没有挂载RoomGrid组件!");
        }
        
        bool isSuccess = false;
        Vector3 targetLocalPos = new Vector3(20, 16, 0);
        if (room.Grid != null && roomObj != null) {
            if (room.isUsePattern) {// 有预设点位的情况
                var presetPosList = MapManagerLevel.Instance.RoomPatternMgr.GetRoomPresetPosByPattern(room, PresetRoomObjectType.RewardChest);
                if (presetPosList is { Count: > 0 }) {
                    Vector2Int targetGridPos = presetPosList[0];
                    if (room.Grid.CanSetObjectAt(roomObj, targetGridPos)) {
                        isSuccess = true;
                        targetLocalPos = room.Grid.GridPos2LocalPos(targetGridPos);
                    }
                }
            }
            if (!isSuccess) {
                RoomObjectPlacementInfo placementInfo = new RoomObjectPlacementInfo(prefab);
                room.Grid.GetPlacementInfo(placementInfo, room.rg_random);
                if (placementInfo.CanBePlaced) {
                    isSuccess = true;
                    targetLocalPos = placementInfo.LocalPos;
                }
            }
        }
        if (!isSuccess) {
            targetLocalPos = Vector3.zero;
        }
        
        GameObject tempObj = Instantiate(prefab, room.floor_group, true);
        tempObj.transform.localPosition = targetLocalPos;
        Instantiate(PrefabManager.GetPrefab(PrefabName.effect_show_up), tempObj.transform.position, Quaternion.identity);
        return true;
    }
}
