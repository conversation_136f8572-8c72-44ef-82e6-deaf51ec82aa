using AudioPlayer;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace ModeSeason.Artifacts {
    public class ArtifactEmber : MonoBehaviour
    {
        public int emberValue = 1;
        Transform target;
        float speed = 0;
        float delay = 1;
        Vector2 velocity;

        public AudioClip audioclip;
        public void OnTriggerEnter2D(Collider2D other) {
            if(target != null) {
                return;
            }

            var ctrl = other.transform.gameObject.GetComponent<RGController>();
            if (ctrl != null && ctrl.IsLocalPlayer()) {
                target = ctrl.transform;
            }

            speed = 8;
        }

        public void SetDelay(float delay) {
            this.delay = delay;
        }

        public void SetInitVelocity(Vector2 v) {
            velocity = v;
        }

        void Update() {
            if (delay > 0) {
                delay -= Time.deltaTime;
                transform.position += (velocity * Time.deltaTime).Vec3();
                velocity = velocity.normalized * Mathf.Max(0, velocity.magnitude - 30 * Time.deltaTime);
                return;
            }

            if (target != null) {
                GetComponent<Rigidbody2D>().simulated = false;
                var disp = target.position + Vector3.up * 0.5f - transform.position;
                transform.position += disp.normalized * speed * Time.deltaTime;
                if (disp.magnitude <= speed * Time.deltaTime) { 
                    GameObject.Destroy(gameObject);
                    ArtifactsModeData.Inst.embers += emberValue;
                    ArtifactsLevelData.Inst.eventHub.Raise(new ArtifactEventEmberChanged { changeEmbers = emberValue });
                    AnimAudioPlayer.Play(audioclip, 0.8f, 0.9f, 0.15f);
                }
                else {
                    speed += 80 * Time.deltaTime;
                }
            }
        }
    }
}