//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;
using System.Collections.Generic;


namespace cfg.Aram
{
/// <summary>
/// 迷迭岛复刻赛季金杯奖励列表
/// </summary>
public sealed partial class LootList :  Bright.Config.BeanBase 
{
    public LootList(ByteBuf _buf) 
    {
        LootID = _buf.ReadString();
        LootAmount = _buf.ReadInt();
        PostInit();
    }

    public static LootList DeserializeLootList(ByteBuf _buf)
    {
        return new Aram.LootList(_buf);
    }

    /// <summary>
    /// 奖励预制体名
    /// </summary>
    public string LootID { get; private set; }
    /// <summary>
    /// 奖励的数量
    /// </summary>
    public int LootAmount { get; private set; }

    public const int __ID__ = -1724457449;
    public override int GetTypeId() => __ID__;

    public  void Resolve(Dictionary<string, object> _tables)
    {
        PostResolve();
    }

    public  void TranslateText(System.Func<string, string, string> translator)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "LootID:" + LootID + ","
        + "LootAmount:" + LootAmount + ","
        + "}";
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}