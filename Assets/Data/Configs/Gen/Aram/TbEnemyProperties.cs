//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;
using System.Collections.Generic;


namespace cfg.Aram
{
   
public partial class TbEnemyProperties
{
    private readonly Dictionary<string, Aram.AramEnemyProperties> _dataMap;
    private readonly List<Aram.AramEnemyProperties> _dataList;
    
    public TbEnemyProperties(ByteBuf _buf)
    {
        _dataMap = new Dictionary<string, Aram.AramEnemyProperties>();
        _dataList = new List<Aram.AramEnemyProperties>();
        
        for(int n = _buf.ReadSize() ; n > 0 ; --n)
        {
            Aram.AramEnemyProperties _v;
            _v = Aram.AramEnemyProperties.DeserializeAramEnemyProperties(_buf);
            _dataList.Add(_v);
            _dataMap.Add(_v.AramId, _v);
        }
        PostInit();
    }

    public Dictionary<string, Aram.AramEnemyProperties> DataMap => _dataMap;
    public List<Aram.AramEnemyProperties> DataList => _dataList;

    public Aram.AramEnemyProperties GetOrDefault(string key) => _dataMap.TryGetValue(key, out var v) ? v : null;
    public Aram.AramEnemyProperties Get(string key) => _dataMap[key];
    public Aram.AramEnemyProperties this[string key] => _dataMap[key];

    public void Resolve(Dictionary<string, object> _tables)
    {
        foreach(var v in _dataList)
        {
            v.Resolve(_tables);
        }
        PostResolve();
    }

    public void TranslateText(System.Func<string, string, string> translator)
    {
        foreach(var v in _dataList)
        {
            v.TranslateText(translator);
        }
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}