//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;
using System.Collections.Generic;


namespace cfg.Aram
{
/// <summary>
/// 迷迭岛特定敌人类
/// </summary>
public sealed partial class SpecialEnemyWave :  Bright.Config.BeanBase 
{
    public SpecialEnemyWave(ByteBuf _buf) 
    {
        AramID = _buf.ReadString();
        EnemyCount = _buf.ReadInt();
        StartTime = _buf.ReadInt();
        EndTime = _buf.ReadInt();
        PostInit();
    }

    public static SpecialEnemyWave DeserializeSpecialEnemyWave(ByteBuf _buf)
    {
        return new Aram.SpecialEnemyWave(_buf);
    }

    /// <summary>
    /// 敌人ID
    /// </summary>
    public string AramID { get; private set; }
    public Aram.AramEnemyProperties AramID_Ref { get; private set; }
    /// <summary>
    /// 敌人数量
    /// </summary>
    public int EnemyCount { get; private set; }
    /// <summary>
    /// 开始时间
    /// </summary>
    public int StartTime { get; private set; }
    /// <summary>
    /// 结束时间
    /// </summary>
    public int EndTime { get; private set; }

    public const int __ID__ = 485144473;
    public override int GetTypeId() => __ID__;

    public  void Resolve(Dictionary<string, object> _tables)
    {
        this.AramID_Ref = (_tables["Aram.TbEnemyProperties"] as Aram.TbEnemyProperties).GetOrDefault(AramID);
        PostResolve();
    }

    public  void TranslateText(System.Func<string, string, string> translator)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "AramID:" + AramID + ","
        + "EnemyCount:" + EnemyCount + ","
        + "StartTime:" + StartTime + ","
        + "EndTime:" + EndTime + ","
        + "}";
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}