//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;
using System.Collections.Generic;


namespace cfg.Aram
{
/// <summary>
/// 角色技能补正
/// </summary>
public sealed partial class SkillSupplement :  Bright.Config.BeanBase 
{
    public SkillSupplement(ByteBuf _buf) 
    {
        SkillID = _buf.ReadInt();
        BuffSupplementID = _buf.ReadInt();
        {int n0 = System.Math.Min(_buf.ReadSize(), _buf.Size);PassiveProperty = new System.Collections.Generic.List<Aram.PropertySupplement>(n0);for(var i0 = 0 ; i0 < n0 ; i0++) { Aram.PropertySupplement _e0;  _e0 = Aram.PropertySupplement.DeserializePropertySupplement(_buf); PassiveProperty.Add(_e0);}}
        PostInit();
    }

    public static SkillSupplement DeserializeSkillSupplement(ByteBuf _buf)
    {
        return new Aram.SkillSupplement(_buf);
    }

    /// <summary>
    /// 技能的ID
    /// </summary>
    public int SkillID { get; private set; }
    /// <summary>
    /// 天赋补正
    /// </summary>
    public int BuffSupplementID { get; private set; }
    /// <summary>
    /// 属性补正
    /// </summary>
    public System.Collections.Generic.List<Aram.PropertySupplement> PassiveProperty { get; private set; }

    public const int __ID__ = 2066370073;
    public override int GetTypeId() => __ID__;

    public  void Resolve(Dictionary<string, object> _tables)
    {
        foreach(var _e in PassiveProperty) { _e?.Resolve(_tables); }
        PostResolve();
    }

    public  void TranslateText(System.Func<string, string, string> translator)
    {
        foreach(var _e in PassiveProperty) { _e?.TranslateText(translator); }
    }

    public override string ToString()
    {
        return "{ "
        + "SkillID:" + SkillID + ","
        + "BuffSupplementID:" + BuffSupplementID + ","
        + "PassiveProperty:" + Bright.Common.StringUtil.CollectionToString(PassiveProperty) + ","
        + "}";
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}