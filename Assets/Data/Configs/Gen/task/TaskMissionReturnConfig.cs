//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;
using System.Collections.Generic;


namespace cfg.task
{
public sealed partial class TaskMissionReturnConfig :  Bright.Config.BeanBase 
{
    public TaskMissionReturnConfig(ByteBuf _buf) 
    {
        Id = _buf.ReadInt();
        Day = _buf.ReadInt();
        Version = _buf.ReadString();
        Order = _buf.ReadInt();
        MissionType = _buf.ReadString();
        Difficulty = _buf.ReadString();
        MissionDescription = _buf.ReadString();
        {int n0 = System.Math.Min(_buf.ReadSize(), _buf.Size);Checkers = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.Dictionary<string, int>>(n0 * 3 / 2);for(var i0 = 0 ; i0 < n0 ; i0++) { string _k0;  _k0 = _buf.ReadString(); System.Collections.Generic.Dictionary<string, int> _v0;  {int n1 = System.Math.Min(_buf.ReadSize(), _buf.Size);_v0 = new System.Collections.Generic.Dictionary<string, int>(n1 * 3 / 2);for(var i1 = 0 ; i1 < n1 ; i1++) { string _k1;  _k1 = _buf.ReadString(); int _v1;  _v1 = _buf.ReadInt();     _v0.Add(_k1, _v1);}}     Checkers.Add(_k0, _v0);}}
        DescKey = _buf.ReadString();
        {int n0 = System.Math.Min(_buf.ReadSize(), _buf.Size);DescParams = new System.Collections.Generic.List<string>(n0);for(var i0 = 0 ; i0 < n0 ; i0++) { string _e0;  _e0 = _buf.ReadString(); DescParams.Add(_e0);}}
        TargetValue = _buf.ReadInt();
        {int n0 = System.Math.Min(_buf.ReadSize(), _buf.Size);FinishRewards = new System.Collections.Generic.Dictionary<int, System.Collections.Generic.Dictionary<string, int>>(n0 * 3 / 2);for(var i0 = 0 ; i0 < n0 ; i0++) { int _k0;  _k0 = _buf.ReadInt(); System.Collections.Generic.Dictionary<string, int> _v0;  {int n1 = System.Math.Min(_buf.ReadSize(), _buf.Size);_v0 = new System.Collections.Generic.Dictionary<string, int>(n1 * 3 / 2);for(var i1 = 0 ; i1 < n1 ; i1++) { string _k1;  _k1 = _buf.ReadString(); int _v1;  _v1 = _buf.ReadInt();     _v0.Add(_k1, _v1);}}     FinishRewards.Add(_k0, _v0);}}
        IsNewTask = _buf.ReadBool();
        ExtraInfo = _buf.ReadString();
        PostInit();
    }

    public static TaskMissionReturnConfig DeserializeTaskMissionReturnConfig(ByteBuf _buf)
    {
        return new task.TaskMissionReturnConfig(_buf);
    }

    /// <summary>
    /// 任务序号
    /// </summary>
    public int Id { get; private set; }
    /// <summary>
    /// 天数
    /// </summary>
    public int Day { get; private set; }
    /// <summary>
    /// 版本号
    /// </summary>
    public string Version { get; private set; }
    /// <summary>
    /// 任务顺序
    /// </summary>
    public int Order { get; private set; }
    /// <summary>
    /// 任务类型（策划参考）
    /// </summary>
    public string MissionType { get; private set; }
    /// <summary>
    /// 难度（策划参考）
    /// </summary>
    public string Difficulty { get; private set; }
    /// <summary>
    /// 任务说明（策划参考）
    /// </summary>
    public string MissionDescription { get; private set; }
    /// <summary>
    /// 检查器
    /// </summary>
    public System.Collections.Generic.Dictionary<string, System.Collections.Generic.Dictionary<string, int>> Checkers { get; private set; }
    /// <summary>
    /// 备注
    /// </summary>
    public string DescKey { get; private set; }
    /// <summary>
    /// 任务说明的参数
    /// </summary>
    public System.Collections.Generic.List<string> DescParams { get; private set; }
    /// <summary>
    /// 进度目标参数
    /// </summary>
    public int TargetValue { get; private set; }
    /// <summary>
    /// 奖励
    /// </summary>
    public System.Collections.Generic.Dictionary<int, System.Collections.Generic.Dictionary<string, int>> FinishRewards { get; private set; }
    /// <summary>
    /// 新任务
    /// </summary>
    public bool IsNewTask { get; private set; }
    /// <summary>
    /// 备注
    /// </summary>
    public string ExtraInfo { get; private set; }

    public const int __ID__ = 1891334384;
    public override int GetTypeId() => __ID__;

    public  void Resolve(Dictionary<string, object> _tables)
    {
        PostResolve();
    }

    public  void TranslateText(System.Func<string, string, string> translator)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "Id:" + Id + ","
        + "Day:" + Day + ","
        + "Version:" + Version + ","
        + "Order:" + Order + ","
        + "MissionType:" + MissionType + ","
        + "Difficulty:" + Difficulty + ","
        + "MissionDescription:" + MissionDescription + ","
        + "Checkers:" + Bright.Common.StringUtil.CollectionToString(Checkers) + ","
        + "DescKey:" + DescKey + ","
        + "DescParams:" + Bright.Common.StringUtil.CollectionToString(DescParams) + ","
        + "TargetValue:" + TargetValue + ","
        + "FinishRewards:" + Bright.Common.StringUtil.CollectionToString(FinishRewards) + ","
        + "IsNewTask:" + IsNewTask + ","
        + "ExtraInfo:" + ExtraInfo + ","
        + "}";
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}