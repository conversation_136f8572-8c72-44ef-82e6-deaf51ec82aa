//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;
using System.Collections.Generic;


namespace cfg.task
{
   
public partial class TbTaskMissionReturnConfig
{
    private readonly Dictionary<int, task.TaskMissionReturnConfig> _dataMap;
    private readonly List<task.TaskMissionReturnConfig> _dataList;
    
    public TbTaskMissionReturnConfig(ByteBuf _buf)
    {
        _dataMap = new Dictionary<int, task.TaskMissionReturnConfig>();
        _dataList = new List<task.TaskMissionReturnConfig>();
        
        for(int n = _buf.ReadSize() ; n > 0 ; --n)
        {
            task.TaskMissionReturnConfig _v;
            _v = task.TaskMissionReturnConfig.DeserializeTaskMissionReturnConfig(_buf);
            _dataList.Add(_v);
            _dataMap.Add(_v.Id, _v);
        }
        PostInit();
    }

    public Dictionary<int, task.TaskMissionReturnConfig> DataMap => _dataMap;
    public List<task.TaskMissionReturnConfig> DataList => _dataList;

    public task.TaskMissionReturnConfig GetOrDefault(int key) => _dataMap.TryGetValue(key, out var v) ? v : null;
    public task.TaskMissionReturnConfig Get(int key) => _dataMap[key];
    public task.TaskMissionReturnConfig this[int key] => _dataMap[key];

    public void Resolve(Dictionary<string, object> _tables)
    {
        foreach(var v in _dataList)
        {
            v.Resolve(_tables);
        }
        PostResolve();
    }

    public void TranslateText(System.Func<string, string, string> translator)
    {
        foreach(var v in _dataList)
        {
            v.TranslateText(translator);
        }
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}