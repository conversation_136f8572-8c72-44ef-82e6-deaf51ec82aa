//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;
using System.Collections.Generic;


namespace cfg.MonsterRise
{
public sealed partial class CfgBuff :  Bright.Config.BeanBase 
{
    public CfgBuff(ByteBuf _buf) 
    {
        Id = _buf.ReadString();
        Name = _buf.ReadString();
        Prefab = _buf.ReadString();
        Duration = _buf.ReadString();
        Attribute1 = _buf.ReadString();
        Value1 = _buf.ReadString();
        Attribute2 = _buf.ReadString();
        Value2 = _buf.ReadString();
        Attribute3 = _buf.ReadString();
        Value3 = _buf.ReadString();
        PostInit();
    }

    public static CfgBuff DeserializeCfgBuff(ByteBuf _buf)
    {
        return new MonsterRise.CfgBuff(_buf);
    }

    public string Id { get; private set; }
    public string Name { get; private set; }
    public string Prefab { get; private set; }
    public string Duration { get; private set; }
    public string Attribute1 { get; private set; }
    public string Value1 { get; private set; }
    public string Attribute2 { get; private set; }
    public string Value2 { get; private set; }
    public string Attribute3 { get; private set; }
    public string Value3 { get; private set; }

    public const int __ID__ = 445454700;
    public override int GetTypeId() => __ID__;

    public  void Resolve(Dictionary<string, object> _tables)
    {
        PostResolve();
    }

    public  void TranslateText(System.Func<string, string, string> translator)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "Id:" + Id + ","
        + "Name:" + Name + ","
        + "Prefab:" + Prefab + ","
        + "Duration:" + Duration + ","
        + "Attribute1:" + Attribute1 + ","
        + "Value1:" + Value1 + ","
        + "Attribute2:" + Attribute2 + ","
        + "Value2:" + Value2 + ","
        + "Attribute3:" + Attribute3 + ","
        + "Value3:" + Value3 + ","
        + "}";
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}