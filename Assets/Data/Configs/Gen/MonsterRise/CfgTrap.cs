//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;
using System.Collections.Generic;


namespace cfg.MonsterRise
{
public sealed partial class CfgTrap :  Bright.Config.BeanBase 
{
    public CfgTrap(ByteBuf _buf) 
    {
        Name = _buf.ReadString();
        Param0 = _buf.ReadString();
        Param1 = _buf.ReadString();
        Param2 = _buf.ReadString();
        Param3 = _buf.ReadString();
        PostInit();
    }

    public static CfgTrap DeserializeCfgTrap(ByteBuf _buf)
    {
        return new MonsterRise.CfgTrap(_buf);
    }

    public string Name { get; private set; }
    public string Param0 { get; private set; }
    public string Param1 { get; private set; }
    public string Param2 { get; private set; }
    public string Param3 { get; private set; }

    public const int __ID__ = 445987910;
    public override int GetTypeId() => __ID__;

    public  void Resolve(Dictionary<string, object> _tables)
    {
        PostResolve();
    }

    public  void TranslateText(System.Func<string, string, string> translator)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "Name:" + Name + ","
        + "Param0:" + Param0 + ","
        + "Param1:" + Param1 + ","
        + "Param2:" + Param2 + ","
        + "Param3:" + Param3 + ","
        + "}";
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}