//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;
using System.Collections.Generic;


namespace cfg.MonsterRise
{
/// <summary>
/// 怪兽崛起内天赋列表
/// </summary>
public sealed partial class TraitGroup :  Bright.Config.BeanBase 
{
    public TraitGroup(ByteBuf _buf) 
    {
        TraitName = _buf.ReadString();
        TraitWeight = _buf.ReadInt();
        PostInit();
    }

    public static TraitGroup DeserializeTraitGroup(ByteBuf _buf)
    {
        return new MonsterRise.TraitGroup(_buf);
    }

    /// <summary>
    /// MonsterRise.TbMRTrait表格内trait的ID
    /// </summary>
    public string TraitName { get; private set; }
    public MonsterRise.CfgTrait TraitName_Ref { get; private set; }
    /// <summary>
    /// 每个天赋的权重值
    /// </summary>
    public int TraitWeight { get; private set; }

    public const int __ID__ = -1839168932;
    public override int GetTypeId() => __ID__;

    public  void Resolve(Dictionary<string, object> _tables)
    {
        this.TraitName_Ref = (_tables["MonsterRise.TbMRTrait"] as MonsterRise.TbMRTrait).GetOrDefault(TraitName);
        PostResolve();
    }

    public  void TranslateText(System.Func<string, string, string> translator)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "TraitName:" + TraitName + ","
        + "TraitWeight:" + TraitWeight + ","
        + "}";
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}