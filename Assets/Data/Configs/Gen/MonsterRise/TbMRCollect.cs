//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;
using System.Collections.Generic;


namespace cfg.MonsterRise
{
   
public partial class TbMRCollect
{
    private readonly Dictionary<int, MonsterRise.CfgCollect> _dataMap;
    private readonly List<MonsterRise.CfgCollect> _dataList;
    
    public TbMRCollect(ByteBuf _buf)
    {
        _dataMap = new Dictionary<int, MonsterRise.CfgCollect>();
        _dataList = new List<MonsterRise.CfgCollect>();
        
        for(int n = _buf.ReadSize() ; n > 0 ; --n)
        {
            MonsterRise.CfgCollect _v;
            _v = MonsterRise.CfgCollect.DeserializeCfgCollect(_buf);
            _dataList.Add(_v);
            _dataMap.Add(_v.Key, _v);
        }
        PostInit();
    }

    public Dictionary<int, MonsterRise.CfgCollect> DataMap => _dataMap;
    public List<MonsterRise.CfgCollect> DataList => _dataList;

    public MonsterRise.CfgCollect GetOrDefault(int key) => _dataMap.TryGetValue(key, out var v) ? v : null;
    public MonsterRise.CfgCollect Get(int key) => _dataMap[key];
    public MonsterRise.CfgCollect this[int key] => _dataMap[key];

    public void Resolve(Dictionary<string, object> _tables)
    {
        foreach(var v in _dataList)
        {
            v.Resolve(_tables);
        }
        PostResolve();
    }

    public void TranslateText(System.Func<string, string, string> translator)
    {
        foreach(var v in _dataList)
        {
            v.TranslateText(translator);
        }
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}