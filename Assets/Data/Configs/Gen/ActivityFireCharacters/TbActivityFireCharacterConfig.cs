//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;
using System.Collections.Generic;


namespace cfg.ActivityFireCharacters
{
   
public partial class TbActivityFireCharacterConfig
{
    private readonly Dictionary<int, ActivityFireCharacters.ActivityFireCharacterConfig> _dataMap;
    private readonly List<ActivityFireCharacters.ActivityFireCharacterConfig> _dataList;
    
    public TbActivityFireCharacterConfig(ByteBuf _buf)
    {
        _dataMap = new Dictionary<int, ActivityFireCharacters.ActivityFireCharacterConfig>();
        _dataList = new List<ActivityFireCharacters.ActivityFireCharacterConfig>();
        
        for(int n = _buf.ReadSize() ; n > 0 ; --n)
        {
            ActivityFireCharacters.ActivityFireCharacterConfig _v;
            _v = ActivityFireCharacters.ActivityFireCharacterConfig.DeserializeActivityFireCharacterConfig(_buf);
            _dataList.Add(_v);
            _dataMap.Add(_v.Id, _v);
        }
        PostInit();
    }

    public Dictionary<int, ActivityFireCharacters.ActivityFireCharacterConfig> DataMap => _dataMap;
    public List<ActivityFireCharacters.ActivityFireCharacterConfig> DataList => _dataList;

    public ActivityFireCharacters.ActivityFireCharacterConfig GetOrDefault(int key) => _dataMap.TryGetValue(key, out var v) ? v : null;
    public ActivityFireCharacters.ActivityFireCharacterConfig Get(int key) => _dataMap[key];
    public ActivityFireCharacters.ActivityFireCharacterConfig this[int key] => _dataMap[key];

    public void Resolve(Dictionary<string, object> _tables)
    {
        foreach(var v in _dataList)
        {
            v.Resolve(_tables);
        }
        PostResolve();
    }

    public void TranslateText(System.Func<string, string, string> translator)
    {
        foreach(var v in _dataList)
        {
            v.TranslateText(translator);
        }
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}