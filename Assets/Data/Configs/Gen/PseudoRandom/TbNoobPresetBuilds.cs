//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;
using System.Collections.Generic;


namespace cfg.PseudoRandom
{
   
public partial class TbNoobPresetBuilds
{
    private readonly Dictionary<string, PseudoRandom.NoobPresetBuilds> _dataMap;
    private readonly List<PseudoRandom.NoobPresetBuilds> _dataList;
    
    public TbNoobPresetBuilds(ByteBuf _buf)
    {
        _dataMap = new Dictionary<string, PseudoRandom.NoobPresetBuilds>();
        _dataList = new List<PseudoRandom.NoobPresetBuilds>();
        
        for(int n = _buf.ReadSize() ; n > 0 ; --n)
        {
            PseudoRandom.NoobPresetBuilds _v;
            _v = PseudoRandom.NoobPresetBuilds.DeserializeNoobPresetBuilds(_buf);
            _dataList.Add(_v);
            _dataMap.Add(_v.BuildId, _v);
        }
        PostInit();
    }

    public Dictionary<string, PseudoRandom.NoobPresetBuilds> DataMap => _dataMap;
    public List<PseudoRandom.NoobPresetBuilds> DataList => _dataList;

    public PseudoRandom.NoobPresetBuilds GetOrDefault(string key) => _dataMap.TryGetValue(key, out var v) ? v : null;
    public PseudoRandom.NoobPresetBuilds Get(string key) => _dataMap[key];
    public PseudoRandom.NoobPresetBuilds this[string key] => _dataMap[key];

    public void Resolve(Dictionary<string, object> _tables)
    {
        foreach(var v in _dataList)
        {
            v.Resolve(_tables);
        }
        PostResolve();
    }

    public void TranslateText(System.Func<string, string, string> translator)
    {
        foreach(var v in _dataList)
        {
            v.TranslateText(translator);
        }
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}