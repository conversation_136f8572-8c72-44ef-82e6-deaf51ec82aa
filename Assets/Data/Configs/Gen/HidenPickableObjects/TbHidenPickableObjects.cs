//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;
using System.Collections.Generic;


namespace cfg.HidenPickableObjects
{
   
public partial class TbHidenPickableObjects
{
    private readonly Dictionary<string, HidenPickableObjects.HidenPickableObjectsConfig> _dataMap;
    private readonly List<HidenPickableObjects.HidenPickableObjectsConfig> _dataList;
    
    public TbHidenPickableObjects(ByteBuf _buf)
    {
        _dataMap = new Dictionary<string, HidenPickableObjects.HidenPickableObjectsConfig>();
        _dataList = new List<HidenPickableObjects.HidenPickableObjectsConfig>();
        
        for(int n = _buf.ReadSize() ; n > 0 ; --n)
        {
            HidenPickableObjects.HidenPickableObjectsConfig _v;
            _v = HidenPickableObjects.HidenPickableObjectsConfig.DeserializeHidenPickableObjectsConfig(_buf);
            _dataList.Add(_v);
            _dataMap.Add(_v.Id, _v);
        }
        PostInit();
    }

    public Dictionary<string, HidenPickableObjects.HidenPickableObjectsConfig> DataMap => _dataMap;
    public List<HidenPickableObjects.HidenPickableObjectsConfig> DataList => _dataList;

    public HidenPickableObjects.HidenPickableObjectsConfig GetOrDefault(string key) => _dataMap.TryGetValue(key, out var v) ? v : null;
    public HidenPickableObjects.HidenPickableObjectsConfig Get(string key) => _dataMap[key];
    public HidenPickableObjects.HidenPickableObjectsConfig this[string key] => _dataMap[key];

    public void Resolve(Dictionary<string, object> _tables)
    {
        foreach(var v in _dataList)
        {
            v.Resolve(_tables);
        }
        PostResolve();
    }

    public void TranslateText(System.Func<string, string, string> translator)
    {
        foreach(var v in _dataList)
        {
            v.TranslateText(translator);
        }
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}