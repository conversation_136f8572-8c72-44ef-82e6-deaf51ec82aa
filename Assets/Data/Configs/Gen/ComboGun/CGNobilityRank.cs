//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;
using System.Collections.Generic;


namespace cfg.ComboGun
{
public sealed partial class CGNobilityRank :  Bright.Config.BeanBase 
{
    public CGNobilityRank(ByteBuf _buf) 
    {
        NobilityID = _buf.ReadInt();
        BattleID = _buf.ReadString();
        NobilityRank = (ComboGun.NobilityRank)_buf.ReadInt();
        Exp = _buf.ReadInt();
        EffectID = _buf.ReadString();
        PostInit();
    }

    public static CGNobilityRank DeserializeCGNobilityRank(ByteBuf _buf)
    {
        return new ComboGun.CGNobilityRank(_buf);
    }

    /// <summary>
    /// ID
    /// </summary>
    public int NobilityID { get; private set; }
    /// <summary>
    /// 对应cg_battledifficulty.xlsx内的battleId列
    /// </summary>
    public string BattleID { get; private set; }
    /// <summary>
    /// 官爵等级
    /// </summary>
    public ComboGun.NobilityRank NobilityRank { get; private set; }
    public int Exp { get; private set; }
    /// <summary>
    /// 词条ID
    /// </summary>
    public string EffectID { get; private set; }

    public const int __ID__ = 975775958;
    public override int GetTypeId() => __ID__;

    public  void Resolve(Dictionary<string, object> _tables)
    {
        PostResolve();
    }

    public  void TranslateText(System.Func<string, string, string> translator)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "NobilityID:" + NobilityID + ","
        + "BattleID:" + BattleID + ","
        + "NobilityRank:" + NobilityRank + ","
        + "Exp:" + Exp + ","
        + "EffectID:" + EffectID + ","
        + "}";
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}