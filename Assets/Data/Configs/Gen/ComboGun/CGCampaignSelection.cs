//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;
using System.Collections.Generic;


namespace cfg.ComboGun
{
public sealed partial class CGCampaignSelection :  Bright.Config.BeanBase 
{
    public CGCampaignSelection(ByteBuf _buf) 
    {
        Id = _buf.ReadInt();
        BattleID = _buf.ReadString();
        Difficulty = _buf.ReadInt();
        EnemyHpFactor = _buf.ReadFloat();
        EnemyDamageFactor = _buf.ReadFloat();
        SeasonCoinFactor = _buf.ReadFloat();
        RelicID = _buf.ReadString();
        PostInit();
    }

    public static CGCampaignSelection DeserializeCGCampaignSelection(ByteBuf _buf)
    {
        return new ComboGun.CGCampaignSelection(_buf);
    }

    /// <summary>
    /// ID
    /// </summary>
    public int Id { get; private set; }
    /// <summary>
    /// 对应cg_battledifficulty.xlsx内的battleId列
    /// </summary>
    public string BattleID { get; private set; }
    public int Difficulty { get; private set; }
    /// <summary>
    /// 敌人血量倍数
    /// </summary>
    public float EnemyHpFactor { get; private set; }
    /// <summary>
    /// 敌人伤害倍数
    /// </summary>
    public float EnemyDamageFactor { get; private set; }
    /// <summary>
    /// 赛季币收益倍数
    /// </summary>
    public float SeasonCoinFactor { get; private set; }
    /// <summary>
    /// 遗物（春节版本没有，后续版本会增加）
    /// </summary>
    public string RelicID { get; private set; }

    public const int __ID__ = 431431516;
    public override int GetTypeId() => __ID__;

    public  void Resolve(Dictionary<string, object> _tables)
    {
        PostResolve();
    }

    public  void TranslateText(System.Func<string, string, string> translator)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "Id:" + Id + ","
        + "BattleID:" + BattleID + ","
        + "Difficulty:" + Difficulty + ","
        + "EnemyHpFactor:" + EnemyHpFactor + ","
        + "EnemyDamageFactor:" + EnemyDamageFactor + ","
        + "SeasonCoinFactor:" + SeasonCoinFactor + ","
        + "RelicID:" + RelicID + ","
        + "}";
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}