//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;
using System.Collections.Generic;


namespace cfg.WeaponUpgrade
{
public sealed partial class ModExp :  Bright.Config.BeanBase 
{
    public ModExp(ByteBuf _buf) 
    {
        ModRarity = _buf.ReadInt();
        ModDescompose = _buf.ReadInt();
        ModLevelUp2 = _buf.ReadInt();
        ModLevelUp3 = _buf.ReadInt();
        ModLevelUp4 = _buf.ReadInt();
        ModLevelUp5 = _buf.ReadInt();
        PostInit();
    }

    public static ModExp DeserializeModExp(ByteBuf _buf)
    {
        return new WeaponUpgrade.ModExp(_buf);
    }

    /// <summary>
    /// 武器品阶：<br/>1：R<br/>2：SR<br/>3：SSR
    /// </summary>
    public int ModRarity { get; private set; }
    /// <summary>
    /// 在扭蛋机被自动分解的模组碎片数量
    /// </summary>
    public int ModDescompose { get; private set; }
    /// <summary>
    /// 升级到2级的所需模组碎片数量
    /// </summary>
    public int ModLevelUp2 { get; private set; }
    /// <summary>
    /// 升级到3级的所需模组碎片数量
    /// </summary>
    public int ModLevelUp3 { get; private set; }
    /// <summary>
    /// 升级到4级的所需模组碎片数量
    /// </summary>
    public int ModLevelUp4 { get; private set; }
    /// <summary>
    /// 升级到5级的所需模组碎片数量
    /// </summary>
    public int ModLevelUp5 { get; private set; }

    public const int __ID__ = 1276343625;
    public override int GetTypeId() => __ID__;

    public  void Resolve(Dictionary<string, object> _tables)
    {
        PostResolve();
    }

    public  void TranslateText(System.Func<string, string, string> translator)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "ModRarity:" + ModRarity + ","
        + "ModDescompose:" + ModDescompose + ","
        + "ModLevelUp2:" + ModLevelUp2 + ","
        + "ModLevelUp3:" + ModLevelUp3 + ","
        + "ModLevelUp4:" + ModLevelUp4 + ","
        + "ModLevelUp5:" + ModLevelUp5 + ","
        + "}";
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}