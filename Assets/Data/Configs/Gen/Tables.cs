//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;



namespace cfg
{ 
public partial class Tables
{
    public achievement.TbAchievementCategories TbAchievementCategories {get; }
    public achievement.TbAchievementRewards TbAchievementRewards {get; }
    public achievement.TbAchievementGroups TbAchievementGroups {get; }
    public Aram.TbEnemyProperties TbEnemyProperties {get; }
    public Aram.TbEnemyGroups TbEnemyGroups {get; }
    public Aram.TbLevels TbLevels {get; }
    public Aram.TbHeros TbHeros {get; }
    public weapon.TbWeapons TbWeapons {get; }
    public task.TbCareerTaskConfig TbCareerTaskConfig {get; }
    public task.TbWantedTaskTargetConfig TbWantedTaskTargetConfig {get; }
    public task.TbWantedTaskRewardConfig TbWantedTaskRewardConfig {get; }
    public game.TbConstants TbConstants {get; }
    public trialknight.TbCharacter TbCharacter {get; }
    public Irontide.TbInherit TbInherit {get; }
    public progress.TbHonoraryTitle TbHonoraryTitle {get; }
    public AntiCracking.TbAntiCrackingConfig TbAntiCrackingConfig {get; }
    public talk.TbCharacterTalkConfig TbCharacterTalkConfig {get; }
    public game.TbCharacterDrawingConfig TbCharacterDrawingConfig {get; }
    public game.TbSkinShowEffectConfig TbSkinShowEffectConfig {get; }
    public game.TbSkill TbSkill {get; }
    public Store.TbMall TbMall {get; }
    public Store.TbGood TbGood {get; }
    public Store.TbJumpPage TbJumpPage {get; }
    public Store.TbUnlockCondition TbUnlockCondition {get; }
    public Store.TbWeaponFragment TbWeaponFragment {get; }
    public game.TbCommon TbCommon {get; }
    public game.TbAIAttribute TbAIAttribute {get; }
    public gashaponReward.TbGashapon TbGashapon {get; }
    public gashaponType.TbGashaponType TbGashaponType {get; }
    public checkinReward.TbCheckinRewardConfig TbCheckinRewardConfig {get; }
    public MonsterRise.TbMRRewardItems TbMRRewardItems {get; }
    public MonsterRise.TbMRBattle TbMRBattle {get; }
    public MonsterRise.TbMRRoomEvent TbMRRoomEvent {get; }
    public MonsterRise.TbMRText TbMRText {get; }
    public MonsterRise.TbMRRandomRoom TbMRRandomRoom {get; }
    public MonsterRise.TbMREventPool TbMREventPool {get; }
    public MonsterRise.TbMRConst TbMRConst {get; }
    public MonsterRise.TbMRMonsterGroup TbMRMonsterGroup {get; }
    public MonsterRise.TbMRRecruitPrice TbMRRecruitPrice {get; }
    public MonsterRise.TbMRUnit TbMRUnit {get; }
    public MonsterRise.TbMRAbility TbMRAbility {get; }
    public MonsterRise.TbMRBuff TbMRBuff {get; }
    public MonsterRise.TbMRProjectile TbMRProjectile {get; }
    public MonsterRise.TbMRTrait TbMRTrait {get; }
    public MonsterRise.TbMRTrap TbMRTrap {get; }
    public MonsterRise.TbMRTraitGroup TbMRTraitGroup {get; }
    public MonsterRise.TbMRCombatAction TbMRCombatAction {get; }
    public MonsterRise.TbMRMerchant TbMRMerchant {get; }
    public MonsterRise.TbMRMonsterScore TbMRMonsterScore {get; }
    public MonsterRise.TbMRCollect TbMRCollect {get; }
    public game.TbItem TbItem {get; }
    public Level.TbLevelEnemy TbLevelEnemy {get; }
    public halloweenReward.TbHalloweenRewardConfig TbHalloweenRewardConfig {get; }
    public PseudoRandom.TbNoobPresetBuilds TbNoobPresetBuilds {get; }
    public PseudoRandom.TbNoobLevels TbNoobLevels {get; }
    public PseudoRandom.TbTalentGroups TbTalentGroups {get; }
    public PseudoRandom.TbWeaponGroup TbWeaponGroup {get; }
    public ComboGun.TbAtch TbAtch {get; }
    public ComboGun.TbBuff TbBuff {get; }
    public ComboGun.TbWeapon TbWeapon {get; }
    public ComboGun.TbEffect TbEffect {get; }
    public ComboGun.TbLoot TbLoot {get; }
    public ComboGun.TbBattleDifficulty TbBattleDifficulty {get; }
    public ComboGun.TbRoom TbRoom {get; }
    public ComboGun.TbRoomReward TbRoomReward {get; }
    public ComboGun.TbMobGroup TbMobGroup {get; }
    public ComboGun.TbMob TbMob {get; }
    public ComboGun.TbProj TbProj {get; }
    public ComboGun.TbComboGunCharacter TbComboGunCharacter {get; }
    public ComboGun.TbTropyLoot TbTropyLoot {get; }
    public ComboGun.TbChampionTask TbChampionTask {get; }
    public ComboGun.TbOnline TbOnline {get; }
    public ComboGun.TbNobilityRank TbNobilityRank {get; }
    public ComboGun.TbCampaignSelection TbCampaignSelection {get; }
    public ComboGun.TbGachaPool TbGachaPool {get; }
    public ComboGun.TbFragmentStore TbFragmentStore {get; }
    public ComboGun.TbMountAttribute TbMountAttribute {get; }
    public ComboGun.TbFollowerAttribute TbFollowerAttribute {get; }
    public FirstTutorial.TbEnemyWave TbEnemyWave {get; }
    public FirstTutorial.TbPlayScript TbPlayScript {get; }
    public Poster.TbPosterConfig TbPosterConfig {get; }
    public gashaponNewWeapons.TbGashaponNewWeapons TbGashaponNewWeapons {get; }
    public Ad.TbRewardWeapons TbRewardWeapons {get; }
    public Ad.TbRandomAward TbRandomAward {get; }
    public Ad.TbSundryConfig TbSundryConfig {get; }
    public checkinVeteranReward.TbCheckinVeteranRewardConfig TbCheckinVeteranRewardConfig {get; }
    public fusionWeaponActivityRewards.TbFusionWeaponActivityRewards TbFusionWeaponActivityRewards {get; }
    public Troop2.TbT2TropyLoot TbT2TropyLoot {get; }
    public Troop2.TbT2ChampionTask TbT2ChampionTask {get; }
    public Troop2.TbT2Mercenaries TbT2Mercenaries {get; }
    public Troop2.TbT2Traits TbT2Traits {get; }
    public Troop2.TbT2Weapons TbT2Weapons {get; }
    public Troop2.TbT2Cookings TbT2Cookings {get; }
    public Troop2.TbT2EnemyProperties TbT2EnemyProperties {get; }
    public Troop2.TbT2EnemyGroups TbT2EnemyGroups {get; }
    public Troop2.TbT2Good TbT2Good {get; }
    public Troop2.TbT2GoodGroups TbT2GoodGroups {get; }
    public Troop2.TbT2LevelStore TbT2LevelStore {get; }
    public Troop2.TbT2Commanders TbT2Commanders {get; }
    public Troop2.TbT2Levels TbT2Levels {get; }
    public Troop2.TbT2Difficulties TbT2Difficulties {get; }
    public Troop2.TbT2Talents TbT2Talents {get; }
    public Troop2.TbT2Const TbT2Const {get; }
    public Troop2.TbT2CommanderAbility TbT2CommanderAbility {get; }
    public Troop2.TbT2Loot TbT2Loot {get; }
    public zongziActivityRewards.TbZongziActivityRewards TbZongziActivityRewards {get; }
    public ActivityCheckinReward.TbActivityCheckinRewardConfig TbActivityCheckinRewardConfig {get; }
    public BugActivityRewards.TbBugActivityRewards TbBugActivityRewards {get; }
    public WeaponUpgrade.TbWeaponEvolution TbWeaponEvolution {get; }
    public WeaponUpgrade.TbWeaponEvolutionSell TbWeaponEvolutionSell {get; }
    public WeaponUpgrade.TbGachaWeapon TbGachaWeapon {get; }
    public WeaponUpgrade.TbWUGachaPool TbWUGachaPool {get; }
    public WeaponUpgrade.TbWeaponMod TbWeaponMod {get; }
    public WeaponUpgrade.TbWeaponModOnCost TbWeaponModOnCost {get; }
    public WeaponUpgrade.TbWeaponSkin TbWeaponSkin {get; }
    public WeaponUpgrade.TbModExp TbModExp {get; }
    public WeaponUpgrade.TbBossEvolutionWeaponRate TbBossEvolutionWeaponRate {get; }
    public WeaponUpgrade.TbCollectorWeapons TbCollectorWeapons {get; }
    public BattlePass.TbBpLoot TbBpLoot {get; }
    public BattlePass.TbBpTask TbBpTask {get; }
    public BattlePass.TbBpList TbBpList {get; }
    public Activity.TbActivityConfig TbActivityConfig {get; }
    public ActivitySchoolReward.TbActivitySchoolRewardConfig TbActivitySchoolRewardConfig {get; }
    public ActivitySchoolQuestion.TbActivitySchoolQuestionConfig TbActivitySchoolQuestionConfig {get; }
    public ActivitySchoolAnswer.TbActivitySchoolAnswerConfig TbActivitySchoolAnswerConfig {get; }
    public ActivitySchoolBonus.TbActivitySchoolBonusConfig TbActivitySchoolBonusConfig {get; }
    public ActivitySchoolTask.TbActivitySchoolTaskConfig TbActivitySchoolTaskConfig {get; }
    public HalloweenMotorcyleeward.TbHalloweenMotorcyleConfig TbHalloweenMotorcyleConfig {get; }
    public ActivityDreamReward.TbActivityDreamRewardConfig TbActivityDreamRewardConfig {get; }
    public ActivityChristmas2024Reward.TbChristmas2024RewardConfig TbChristmas2024RewardConfig {get; }
    public ActivitySkin.TbActivitySkinConfig TbActivitySkinConfig {get; }
    public Ad.TbRetireRandomAward TbRetireRandomAward {get; }
    public HidenPickableObjects.TbHidenPickableObjects TbHidenPickableObjects {get; }
    public Battle.TbC17Skill1Config TbC17Skill1Config {get; }
    public Atf2.TbAtf2Const TbAtf2Const {get; }
    public Atf2.TbAtf2Enemies TbAtf2Enemies {get; }
    public Atf2.TbAtf2RoomEnemies TbAtf2RoomEnemies {get; }
    public Atf2.TbAtf2Parts TbAtf2Parts {get; }
    public Atf2.TbAtf2Effects TbAtf2Effects {get; }
    public Atf2.TbAtf2ChampionTask TbAtf2ChampionTask {get; }
    public Atf2.TbAtf2TropyLoot TbAtf2TropyLoot {get; }
    public ActivityFireReward.TbActivityFireRewardConfig TbActivityFireRewardConfig {get; }
    public ActivityFireGoods.TbActivityFireGoodConfig TbActivityFireGoodConfig {get; }
    public ActivityFireCharacters.TbActivityFireCharacterConfig TbActivityFireCharacterConfig {get; }
    public game.TbNewPlayerLevel TbNewPlayerLevel {get; }
    public game.TbGameUnlockCondition TbGameUnlockCondition {get; }
    public task.TbFriendsOnlineActivityTaskConfig TbFriendsOnlineActivityTaskConfig {get; }
    public ActivityNewYear2025CheckinReward.TbActivityNewYear2025CheckinRewardConfig TbActivityNewYear2025CheckinRewardConfig {get; }
    public ActivityRebornEggReward.TbActivityRebornEggRewardConfig TbActivityRebornEggRewardConfig {get; }
    public ActivityRebornEggBuff.TbActivityRebornEggBuffConfig TbActivityRebornEggBuffConfig {get; }
    public ActivityRebornEggFollower.TbActivityRebornEggFollowerConfig TbActivityRebornEggFollowerConfig {get; }
    public game.TbForgeableFusionWeapon TbForgeableFusionWeapon {get; }
    public UI.TbRedDotConfigs TbRedDotConfigs {get; }
    public ActivityLegendReward.TbActivityLegendRewardConfig TbActivityLegendRewardConfig {get; }
    public ActivityLegendBuff.TbActivityLegendBuffConfig TbActivityLegendBuffConfig {get; }
    public ActivityLegendEquipDrop.TbActivityLegendEquipDropConfig TbActivityLegendEquipDropConfig {get; }
    public ActivityLegendRanking.TbActivityLegendRankingConfig TbActivityLegendRankingConfig {get; }
    public ActivityLegendConst.TbActivityLegendConstConfig TbActivityLegendConstConfig {get; }
    public task.TbTaskMissionReturnConfig TbTaskMissionReturnConfig {get; }
    public task.TbTaskMissionH5Config TbTaskMissionH5Config {get; }
    public config.TbVersionEggMachineConfig TbVersionEggMachineConfig {get; }
    public config.TbWeeklyTrailKnightConfig TbWeeklyTrailKnightConfig {get; }
    public Store.TbRmbMall TbRmbMall {get; }
    public ActivityReturnPlayerCheckinReward.TbActivityReturnPlayerCheckinRewardConfig TbActivityReturnPlayerCheckinRewardConfig {get; }
    public task.TbTaskBeginnerConfig TbTaskBeginnerConfig {get; }
    public config.TbFishAndSeasonStoreConfigTime TbFishAndSeasonStoreConfigTime {get; }
    public config.TbFishAndSeasonStoreConfig TbFishAndSeasonStoreConfig {get; }
    public Aram.TbAram2ChampionTask TbAram2ChampionTask {get; }
    public Aram.TbAram2TrophyLoot TbAram2TrophyLoot {get; }
    public Aram.TbAram2YaoQiLevels TbAram2YaoQiLevels {get; }
    public Aram.TbAram2YaoQiEffects TbAram2YaoQiEffects {get; }
    public Aram.TbAramShopItems TbAramShopItems {get; }
    public Aram.TbAramConst TbAramConst {get; }
    public game.TbWeaponAffix TbWeaponAffix {get; }
    public game.TbWeaponAffixCompatibility TbWeaponAffixCompatibility {get; }
    public ActivityBugFeatureAffix.TbActivityBugFeatureAffix TbActivityBugFeatureAffix {get; }
    public WeaponUpgrade.TbLevelWeaponCollectorRarity TbLevelWeaponCollectorRarity {get; }
    public ActivityBugFeatureConst.TbActivityBugFeatureConst TbActivityBugFeatureConst {get; }
    public LevelDifficulty.TbLDEnemies TbLDEnemies {get; }
    public LevelDifficulty.TbLDEnemiesWave TbLDEnemiesWave {get; }
    public LevelDifficulty.TbLDEnemyConst TbLDEnemyConst {get; }

    public Tables(System.Func<string, ByteBuf> loader)
    {
        var tables = new System.Collections.Generic.Dictionary<string, object>();
        TbAchievementCategories = new achievement.TbAchievementCategories(loader("achievement_tbachievementcategories")); 
        tables.Add("achievement.TbAchievementCategories", TbAchievementCategories);
        TbAchievementRewards = new achievement.TbAchievementRewards(loader("achievement_tbachievementrewards")); 
        tables.Add("achievement.TbAchievementRewards", TbAchievementRewards);
        TbAchievementGroups = new achievement.TbAchievementGroups(loader("achievement_tbachievementgroups")); 
        tables.Add("achievement.TbAchievementGroups", TbAchievementGroups);
        TbEnemyProperties = new Aram.TbEnemyProperties(loader("aram_tbenemyproperties")); 
        tables.Add("Aram.TbEnemyProperties", TbEnemyProperties);
        TbEnemyGroups = new Aram.TbEnemyGroups(loader("aram_tbenemygroups")); 
        tables.Add("Aram.TbEnemyGroups", TbEnemyGroups);
        TbLevels = new Aram.TbLevels(loader("aram_tblevels")); 
        tables.Add("Aram.TbLevels", TbLevels);
        TbHeros = new Aram.TbHeros(loader("aram_tbheros")); 
        tables.Add("Aram.TbHeros", TbHeros);
        TbWeapons = new weapon.TbWeapons(loader("weapon_tbweapons")); 
        tables.Add("weapon.TbWeapons", TbWeapons);
        TbCareerTaskConfig = new task.TbCareerTaskConfig(loader("task_tbcareertaskconfig")); 
        tables.Add("task.TbCareerTaskConfig", TbCareerTaskConfig);
        TbWantedTaskTargetConfig = new task.TbWantedTaskTargetConfig(loader("task_tbwantedtasktargetconfig")); 
        tables.Add("task.TbWantedTaskTargetConfig", TbWantedTaskTargetConfig);
        TbWantedTaskRewardConfig = new task.TbWantedTaskRewardConfig(loader("task_tbwantedtaskrewardconfig")); 
        tables.Add("task.TbWantedTaskRewardConfig", TbWantedTaskRewardConfig);
        TbConstants = new game.TbConstants(loader("game_tbconstants")); 
        tables.Add("game.TbConstants", TbConstants);
        TbCharacter = new trialknight.TbCharacter(loader("trialknight_tbcharacter")); 
        tables.Add("trialknight.TbCharacter", TbCharacter);
        TbInherit = new Irontide.TbInherit(loader("irontide_tbinherit")); 
        tables.Add("Irontide.TbInherit", TbInherit);
        TbHonoraryTitle = new progress.TbHonoraryTitle(loader("progress_tbhonorarytitle")); 
        tables.Add("progress.TbHonoraryTitle", TbHonoraryTitle);
        TbAntiCrackingConfig = new AntiCracking.TbAntiCrackingConfig(loader("anticracking_tbanticrackingconfig")); 
        tables.Add("AntiCracking.TbAntiCrackingConfig", TbAntiCrackingConfig);
        TbCharacterTalkConfig = new talk.TbCharacterTalkConfig(loader("talk_tbcharactertalkconfig")); 
        tables.Add("talk.TbCharacterTalkConfig", TbCharacterTalkConfig);
        TbCharacterDrawingConfig = new game.TbCharacterDrawingConfig(loader("game_tbcharacterdrawingconfig")); 
        tables.Add("game.TbCharacterDrawingConfig", TbCharacterDrawingConfig);
        TbSkinShowEffectConfig = new game.TbSkinShowEffectConfig(loader("game_tbskinshoweffectconfig")); 
        tables.Add("game.TbSkinShowEffectConfig", TbSkinShowEffectConfig);
        TbSkill = new game.TbSkill(loader("game_tbskill")); 
        tables.Add("game.TbSkill", TbSkill);
        TbMall = new Store.TbMall(loader("store_tbmall")); 
        tables.Add("Store.TbMall", TbMall);
        TbGood = new Store.TbGood(loader("store_tbgood")); 
        tables.Add("Store.TbGood", TbGood);
        TbJumpPage = new Store.TbJumpPage(loader("store_tbjumppage")); 
        tables.Add("Store.TbJumpPage", TbJumpPage);
        TbUnlockCondition = new Store.TbUnlockCondition(loader("store_tbunlockcondition")); 
        tables.Add("Store.TbUnlockCondition", TbUnlockCondition);
        TbWeaponFragment = new Store.TbWeaponFragment(loader("store_tbweaponfragment")); 
        tables.Add("Store.TbWeaponFragment", TbWeaponFragment);
        TbCommon = new game.TbCommon(loader("game_tbcommon")); 
        tables.Add("game.TbCommon", TbCommon);
        TbAIAttribute = new game.TbAIAttribute(loader("game_tbaiattribute")); 
        tables.Add("game.TbAIAttribute", TbAIAttribute);
        TbGashapon = new gashaponReward.TbGashapon(loader("gashaponreward_tbgashapon")); 
        tables.Add("gashaponReward.TbGashapon", TbGashapon);
        TbGashaponType = new gashaponType.TbGashaponType(loader("gashapontype_tbgashapontype")); 
        tables.Add("gashaponType.TbGashaponType", TbGashaponType);
        TbCheckinRewardConfig = new checkinReward.TbCheckinRewardConfig(loader("checkinreward_tbcheckinrewardconfig")); 
        tables.Add("checkinReward.TbCheckinRewardConfig", TbCheckinRewardConfig);
        TbMRRewardItems = new MonsterRise.TbMRRewardItems(loader("monsterrise_tbmrrewarditems")); 
        tables.Add("MonsterRise.TbMRRewardItems", TbMRRewardItems);
        TbMRBattle = new MonsterRise.TbMRBattle(loader("monsterrise_tbmrbattle")); 
        tables.Add("MonsterRise.TbMRBattle", TbMRBattle);
        TbMRRoomEvent = new MonsterRise.TbMRRoomEvent(loader("monsterrise_tbmrroomevent")); 
        tables.Add("MonsterRise.TbMRRoomEvent", TbMRRoomEvent);
        TbMRText = new MonsterRise.TbMRText(loader("monsterrise_tbmrtext")); 
        tables.Add("MonsterRise.TbMRText", TbMRText);
        TbMRRandomRoom = new MonsterRise.TbMRRandomRoom(loader("monsterrise_tbmrrandomroom")); 
        tables.Add("MonsterRise.TbMRRandomRoom", TbMRRandomRoom);
        TbMREventPool = new MonsterRise.TbMREventPool(loader("monsterrise_tbmreventpool")); 
        tables.Add("MonsterRise.TbMREventPool", TbMREventPool);
        TbMRConst = new MonsterRise.TbMRConst(loader("monsterrise_tbmrconst")); 
        tables.Add("MonsterRise.TbMRConst", TbMRConst);
        TbMRMonsterGroup = new MonsterRise.TbMRMonsterGroup(loader("monsterrise_tbmrmonstergroup")); 
        tables.Add("MonsterRise.TbMRMonsterGroup", TbMRMonsterGroup);
        TbMRRecruitPrice = new MonsterRise.TbMRRecruitPrice(loader("monsterrise_tbmrrecruitprice")); 
        tables.Add("MonsterRise.TbMRRecruitPrice", TbMRRecruitPrice);
        TbMRUnit = new MonsterRise.TbMRUnit(loader("monsterrise_tbmrunit")); 
        tables.Add("MonsterRise.TbMRUnit", TbMRUnit);
        TbMRAbility = new MonsterRise.TbMRAbility(loader("monsterrise_tbmrability")); 
        tables.Add("MonsterRise.TbMRAbility", TbMRAbility);
        TbMRBuff = new MonsterRise.TbMRBuff(loader("monsterrise_tbmrbuff")); 
        tables.Add("MonsterRise.TbMRBuff", TbMRBuff);
        TbMRProjectile = new MonsterRise.TbMRProjectile(loader("monsterrise_tbmrprojectile")); 
        tables.Add("MonsterRise.TbMRProjectile", TbMRProjectile);
        TbMRTrait = new MonsterRise.TbMRTrait(loader("monsterrise_tbmrtrait")); 
        tables.Add("MonsterRise.TbMRTrait", TbMRTrait);
        TbMRTrap = new MonsterRise.TbMRTrap(loader("monsterrise_tbmrtrap")); 
        tables.Add("MonsterRise.TbMRTrap", TbMRTrap);
        TbMRTraitGroup = new MonsterRise.TbMRTraitGroup(loader("monsterrise_tbmrtraitgroup")); 
        tables.Add("MonsterRise.TbMRTraitGroup", TbMRTraitGroup);
        TbMRCombatAction = new MonsterRise.TbMRCombatAction(loader("monsterrise_tbmrcombataction")); 
        tables.Add("MonsterRise.TbMRCombatAction", TbMRCombatAction);
        TbMRMerchant = new MonsterRise.TbMRMerchant(loader("monsterrise_tbmrmerchant")); 
        tables.Add("MonsterRise.TbMRMerchant", TbMRMerchant);
        TbMRMonsterScore = new MonsterRise.TbMRMonsterScore(loader("monsterrise_tbmrmonsterscore")); 
        tables.Add("MonsterRise.TbMRMonsterScore", TbMRMonsterScore);
        TbMRCollect = new MonsterRise.TbMRCollect(loader("monsterrise_tbmrcollect")); 
        tables.Add("MonsterRise.TbMRCollect", TbMRCollect);
        TbItem = new game.TbItem(loader("game_tbitem")); 
        tables.Add("game.TbItem", TbItem);
        TbLevelEnemy = new Level.TbLevelEnemy(loader("level_tblevelenemy")); 
        tables.Add("Level.TbLevelEnemy", TbLevelEnemy);
        TbHalloweenRewardConfig = new halloweenReward.TbHalloweenRewardConfig(loader("halloweenreward_tbhalloweenrewardconfig")); 
        tables.Add("halloweenReward.TbHalloweenRewardConfig", TbHalloweenRewardConfig);
        TbNoobPresetBuilds = new PseudoRandom.TbNoobPresetBuilds(loader("pseudorandom_tbnoobpresetbuilds")); 
        tables.Add("PseudoRandom.TbNoobPresetBuilds", TbNoobPresetBuilds);
        TbNoobLevels = new PseudoRandom.TbNoobLevels(loader("pseudorandom_tbnooblevels")); 
        tables.Add("PseudoRandom.TbNoobLevels", TbNoobLevels);
        TbTalentGroups = new PseudoRandom.TbTalentGroups(loader("pseudorandom_tbtalentgroups")); 
        tables.Add("PseudoRandom.TbTalentGroups", TbTalentGroups);
        TbWeaponGroup = new PseudoRandom.TbWeaponGroup(loader("pseudorandom_tbweapongroup")); 
        tables.Add("PseudoRandom.TbWeaponGroup", TbWeaponGroup);
        TbAtch = new ComboGun.TbAtch(loader("combogun_tbatch")); 
        tables.Add("ComboGun.TbAtch", TbAtch);
        TbBuff = new ComboGun.TbBuff(loader("combogun_tbbuff")); 
        tables.Add("ComboGun.TbBuff", TbBuff);
        TbWeapon = new ComboGun.TbWeapon(loader("combogun_tbweapon")); 
        tables.Add("ComboGun.TbWeapon", TbWeapon);
        TbEffect = new ComboGun.TbEffect(loader("combogun_tbeffect")); 
        tables.Add("ComboGun.TbEffect", TbEffect);
        TbLoot = new ComboGun.TbLoot(loader("combogun_tbloot")); 
        tables.Add("ComboGun.TbLoot", TbLoot);
        TbBattleDifficulty = new ComboGun.TbBattleDifficulty(loader("combogun_tbbattledifficulty")); 
        tables.Add("ComboGun.TbBattleDifficulty", TbBattleDifficulty);
        TbRoom = new ComboGun.TbRoom(loader("combogun_tbroom")); 
        tables.Add("ComboGun.TbRoom", TbRoom);
        TbRoomReward = new ComboGun.TbRoomReward(loader("combogun_tbroomreward")); 
        tables.Add("ComboGun.TbRoomReward", TbRoomReward);
        TbMobGroup = new ComboGun.TbMobGroup(loader("combogun_tbmobgroup")); 
        tables.Add("ComboGun.TbMobGroup", TbMobGroup);
        TbMob = new ComboGun.TbMob(loader("combogun_tbmob")); 
        tables.Add("ComboGun.TbMob", TbMob);
        TbProj = new ComboGun.TbProj(loader("combogun_tbproj")); 
        tables.Add("ComboGun.TbProj", TbProj);
        TbComboGunCharacter = new ComboGun.TbComboGunCharacter(loader("combogun_tbcomboguncharacter")); 
        tables.Add("ComboGun.TbComboGunCharacter", TbComboGunCharacter);
        TbTropyLoot = new ComboGun.TbTropyLoot(loader("combogun_tbtropyloot")); 
        tables.Add("ComboGun.TbTropyLoot", TbTropyLoot);
        TbChampionTask = new ComboGun.TbChampionTask(loader("combogun_tbchampiontask")); 
        tables.Add("ComboGun.TbChampionTask", TbChampionTask);
        TbOnline = new ComboGun.TbOnline(loader("combogun_tbonline")); 
        tables.Add("ComboGun.TbOnline", TbOnline);
        TbNobilityRank = new ComboGun.TbNobilityRank(loader("combogun_tbnobilityrank")); 
        tables.Add("ComboGun.TbNobilityRank", TbNobilityRank);
        TbCampaignSelection = new ComboGun.TbCampaignSelection(loader("combogun_tbcampaignselection")); 
        tables.Add("ComboGun.TbCampaignSelection", TbCampaignSelection);
        TbGachaPool = new ComboGun.TbGachaPool(loader("combogun_tbgachapool")); 
        tables.Add("ComboGun.TbGachaPool", TbGachaPool);
        TbFragmentStore = new ComboGun.TbFragmentStore(loader("combogun_tbfragmentstore")); 
        tables.Add("ComboGun.TbFragmentStore", TbFragmentStore);
        TbMountAttribute = new ComboGun.TbMountAttribute(loader("combogun_tbmountattribute")); 
        tables.Add("ComboGun.TbMountAttribute", TbMountAttribute);
        TbFollowerAttribute = new ComboGun.TbFollowerAttribute(loader("combogun_tbfollowerattribute")); 
        tables.Add("ComboGun.TbFollowerAttribute", TbFollowerAttribute);
        TbEnemyWave = new FirstTutorial.TbEnemyWave(loader("firsttutorial_tbenemywave")); 
        tables.Add("FirstTutorial.TbEnemyWave", TbEnemyWave);
        TbPlayScript = new FirstTutorial.TbPlayScript(loader("firsttutorial_tbplayscript")); 
        tables.Add("FirstTutorial.TbPlayScript", TbPlayScript);
        TbPosterConfig = new Poster.TbPosterConfig(loader("poster_tbposterconfig")); 
        tables.Add("Poster.TbPosterConfig", TbPosterConfig);
        TbGashaponNewWeapons = new gashaponNewWeapons.TbGashaponNewWeapons(loader("gashaponnewweapons_tbgashaponnewweapons")); 
        tables.Add("gashaponNewWeapons.TbGashaponNewWeapons", TbGashaponNewWeapons);
        TbRewardWeapons = new Ad.TbRewardWeapons(loader("ad_tbrewardweapons")); 
        tables.Add("Ad.TbRewardWeapons", TbRewardWeapons);
        TbRandomAward = new Ad.TbRandomAward(loader("ad_tbrandomaward")); 
        tables.Add("Ad.TbRandomAward", TbRandomAward);
        TbSundryConfig = new Ad.TbSundryConfig(loader("ad_tbsundryconfig")); 
        tables.Add("Ad.TbSundryConfig", TbSundryConfig);
        TbCheckinVeteranRewardConfig = new checkinVeteranReward.TbCheckinVeteranRewardConfig(loader("checkinveteranreward_tbcheckinveteranrewardconfig")); 
        tables.Add("checkinVeteranReward.TbCheckinVeteranRewardConfig", TbCheckinVeteranRewardConfig);
        TbFusionWeaponActivityRewards = new fusionWeaponActivityRewards.TbFusionWeaponActivityRewards(loader("fusionweaponactivityrewards_tbfusionweaponactivityrewards")); 
        tables.Add("fusionWeaponActivityRewards.TbFusionWeaponActivityRewards", TbFusionWeaponActivityRewards);
        TbT2TropyLoot = new Troop2.TbT2TropyLoot(loader("troop2_tbt2tropyloot")); 
        tables.Add("Troop2.TbT2TropyLoot", TbT2TropyLoot);
        TbT2ChampionTask = new Troop2.TbT2ChampionTask(loader("troop2_tbt2championtask")); 
        tables.Add("Troop2.TbT2ChampionTask", TbT2ChampionTask);
        TbT2Mercenaries = new Troop2.TbT2Mercenaries(loader("troop2_tbt2mercenaries")); 
        tables.Add("Troop2.TbT2Mercenaries", TbT2Mercenaries);
        TbT2Traits = new Troop2.TbT2Traits(loader("troop2_tbt2traits")); 
        tables.Add("Troop2.TbT2Traits", TbT2Traits);
        TbT2Weapons = new Troop2.TbT2Weapons(loader("troop2_tbt2weapons")); 
        tables.Add("Troop2.TbT2Weapons", TbT2Weapons);
        TbT2Cookings = new Troop2.TbT2Cookings(loader("troop2_tbt2cookings")); 
        tables.Add("Troop2.TbT2Cookings", TbT2Cookings);
        TbT2EnemyProperties = new Troop2.TbT2EnemyProperties(loader("troop2_tbt2enemyproperties")); 
        tables.Add("Troop2.TbT2EnemyProperties", TbT2EnemyProperties);
        TbT2EnemyGroups = new Troop2.TbT2EnemyGroups(loader("troop2_tbt2enemygroups")); 
        tables.Add("Troop2.TbT2EnemyGroups", TbT2EnemyGroups);
        TbT2Good = new Troop2.TbT2Good(loader("troop2_tbt2good")); 
        tables.Add("Troop2.TbT2Good", TbT2Good);
        TbT2GoodGroups = new Troop2.TbT2GoodGroups(loader("troop2_tbt2goodgroups")); 
        tables.Add("Troop2.TbT2GoodGroups", TbT2GoodGroups);
        TbT2LevelStore = new Troop2.TbT2LevelStore(loader("troop2_tbt2levelstore")); 
        tables.Add("Troop2.TbT2LevelStore", TbT2LevelStore);
        TbT2Commanders = new Troop2.TbT2Commanders(loader("troop2_tbt2commanders")); 
        tables.Add("Troop2.TbT2Commanders", TbT2Commanders);
        TbT2Levels = new Troop2.TbT2Levels(loader("troop2_tbt2levels")); 
        tables.Add("Troop2.TbT2Levels", TbT2Levels);
        TbT2Difficulties = new Troop2.TbT2Difficulties(loader("troop2_tbt2difficulties")); 
        tables.Add("Troop2.TbT2Difficulties", TbT2Difficulties);
        TbT2Talents = new Troop2.TbT2Talents(loader("troop2_tbt2talents")); 
        tables.Add("Troop2.TbT2Talents", TbT2Talents);
        TbT2Const = new Troop2.TbT2Const(loader("troop2_tbt2const")); 
        tables.Add("Troop2.TbT2Const", TbT2Const);
        TbT2CommanderAbility = new Troop2.TbT2CommanderAbility(loader("troop2_tbt2commanderability")); 
        tables.Add("Troop2.TbT2CommanderAbility", TbT2CommanderAbility);
        TbT2Loot = new Troop2.TbT2Loot(loader("troop2_tbt2loot")); 
        tables.Add("Troop2.TbT2Loot", TbT2Loot);
        TbZongziActivityRewards = new zongziActivityRewards.TbZongziActivityRewards(loader("zongziactivityrewards_tbzongziactivityrewards")); 
        tables.Add("zongziActivityRewards.TbZongziActivityRewards", TbZongziActivityRewards);
        TbActivityCheckinRewardConfig = new ActivityCheckinReward.TbActivityCheckinRewardConfig(loader("activitycheckinreward_tbactivitycheckinrewardconfig")); 
        tables.Add("ActivityCheckinReward.TbActivityCheckinRewardConfig", TbActivityCheckinRewardConfig);
        TbBugActivityRewards = new BugActivityRewards.TbBugActivityRewards(loader("bugactivityrewards_tbbugactivityrewards")); 
        tables.Add("BugActivityRewards.TbBugActivityRewards", TbBugActivityRewards);
        TbWeaponEvolution = new WeaponUpgrade.TbWeaponEvolution(loader("weaponupgrade_tbweaponevolution")); 
        tables.Add("WeaponUpgrade.TbWeaponEvolution", TbWeaponEvolution);
        TbWeaponEvolutionSell = new WeaponUpgrade.TbWeaponEvolutionSell(loader("weaponupgrade_tbweaponevolutionsell")); 
        tables.Add("WeaponUpgrade.TbWeaponEvolutionSell", TbWeaponEvolutionSell);
        TbGachaWeapon = new WeaponUpgrade.TbGachaWeapon(loader("weaponupgrade_tbgachaweapon")); 
        tables.Add("WeaponUpgrade.TbGachaWeapon", TbGachaWeapon);
        TbWUGachaPool = new WeaponUpgrade.TbWUGachaPool(loader("weaponupgrade_tbwugachapool")); 
        tables.Add("WeaponUpgrade.TbWUGachaPool", TbWUGachaPool);
        TbWeaponMod = new WeaponUpgrade.TbWeaponMod(loader("weaponupgrade_tbweaponmod")); 
        tables.Add("WeaponUpgrade.TbWeaponMod", TbWeaponMod);
        TbWeaponModOnCost = new WeaponUpgrade.TbWeaponModOnCost(loader("weaponupgrade_tbweaponmodoncost")); 
        tables.Add("WeaponUpgrade.TbWeaponModOnCost", TbWeaponModOnCost);
        TbWeaponSkin = new WeaponUpgrade.TbWeaponSkin(loader("weaponupgrade_tbweaponskin")); 
        tables.Add("WeaponUpgrade.TbWeaponSkin", TbWeaponSkin);
        TbModExp = new WeaponUpgrade.TbModExp(loader("weaponupgrade_tbmodexp")); 
        tables.Add("WeaponUpgrade.TbModExp", TbModExp);
        TbBossEvolutionWeaponRate = new WeaponUpgrade.TbBossEvolutionWeaponRate(loader("weaponupgrade_tbbossevolutionweaponrate")); 
        tables.Add("WeaponUpgrade.TbBossEvolutionWeaponRate", TbBossEvolutionWeaponRate);
        TbCollectorWeapons = new WeaponUpgrade.TbCollectorWeapons(loader("weaponupgrade_tbcollectorweapons")); 
        tables.Add("WeaponUpgrade.TbCollectorWeapons", TbCollectorWeapons);
        TbBpLoot = new BattlePass.TbBpLoot(loader("battlepass_tbbploot")); 
        tables.Add("BattlePass.TbBpLoot", TbBpLoot);
        TbBpTask = new BattlePass.TbBpTask(loader("battlepass_tbbptask")); 
        tables.Add("BattlePass.TbBpTask", TbBpTask);
        TbBpList = new BattlePass.TbBpList(loader("battlepass_tbbplist")); 
        tables.Add("BattlePass.TbBpList", TbBpList);
        TbActivityConfig = new Activity.TbActivityConfig(loader("activity_tbactivityconfig")); 
        tables.Add("Activity.TbActivityConfig", TbActivityConfig);
        TbActivitySchoolRewardConfig = new ActivitySchoolReward.TbActivitySchoolRewardConfig(loader("activityschoolreward_tbactivityschoolrewardconfig")); 
        tables.Add("ActivitySchoolReward.TbActivitySchoolRewardConfig", TbActivitySchoolRewardConfig);
        TbActivitySchoolQuestionConfig = new ActivitySchoolQuestion.TbActivitySchoolQuestionConfig(loader("activityschoolquestion_tbactivityschoolquestionconfig")); 
        tables.Add("ActivitySchoolQuestion.TbActivitySchoolQuestionConfig", TbActivitySchoolQuestionConfig);
        TbActivitySchoolAnswerConfig = new ActivitySchoolAnswer.TbActivitySchoolAnswerConfig(loader("activityschoolanswer_tbactivityschoolanswerconfig")); 
        tables.Add("ActivitySchoolAnswer.TbActivitySchoolAnswerConfig", TbActivitySchoolAnswerConfig);
        TbActivitySchoolBonusConfig = new ActivitySchoolBonus.TbActivitySchoolBonusConfig(loader("activityschoolbonus_tbactivityschoolbonusconfig")); 
        tables.Add("ActivitySchoolBonus.TbActivitySchoolBonusConfig", TbActivitySchoolBonusConfig);
        TbActivitySchoolTaskConfig = new ActivitySchoolTask.TbActivitySchoolTaskConfig(loader("activityschooltask_tbactivityschooltaskconfig")); 
        tables.Add("ActivitySchoolTask.TbActivitySchoolTaskConfig", TbActivitySchoolTaskConfig);
        TbHalloweenMotorcyleConfig = new HalloweenMotorcyleeward.TbHalloweenMotorcyleConfig(loader("halloweenmotorcyleeward_tbhalloweenmotorcyleconfig")); 
        tables.Add("HalloweenMotorcyleeward.TbHalloweenMotorcyleConfig", TbHalloweenMotorcyleConfig);
        TbActivityDreamRewardConfig = new ActivityDreamReward.TbActivityDreamRewardConfig(loader("activitydreamreward_tbactivitydreamrewardconfig")); 
        tables.Add("ActivityDreamReward.TbActivityDreamRewardConfig", TbActivityDreamRewardConfig);
        TbChristmas2024RewardConfig = new ActivityChristmas2024Reward.TbChristmas2024RewardConfig(loader("activitychristmas2024reward_tbchristmas2024rewardconfig")); 
        tables.Add("ActivityChristmas2024Reward.TbChristmas2024RewardConfig", TbChristmas2024RewardConfig);
        TbActivitySkinConfig = new ActivitySkin.TbActivitySkinConfig(loader("activityskin_tbactivityskinconfig")); 
        tables.Add("ActivitySkin.TbActivitySkinConfig", TbActivitySkinConfig);
        TbRetireRandomAward = new Ad.TbRetireRandomAward(loader("ad_tbretirerandomaward")); 
        tables.Add("Ad.TbRetireRandomAward", TbRetireRandomAward);
        TbHidenPickableObjects = new HidenPickableObjects.TbHidenPickableObjects(loader("hidenpickableobjects_tbhidenpickableobjects")); 
        tables.Add("HidenPickableObjects.TbHidenPickableObjects", TbHidenPickableObjects);
        TbC17Skill1Config = new Battle.TbC17Skill1Config(loader("battle_tbc17skill1config")); 
        tables.Add("Battle.TbC17Skill1Config", TbC17Skill1Config);
        TbAtf2Const = new Atf2.TbAtf2Const(loader("atf2_tbatf2const")); 
        tables.Add("Atf2.TbAtf2Const", TbAtf2Const);
        TbAtf2Enemies = new Atf2.TbAtf2Enemies(loader("atf2_tbatf2enemies")); 
        tables.Add("Atf2.TbAtf2Enemies", TbAtf2Enemies);
        TbAtf2RoomEnemies = new Atf2.TbAtf2RoomEnemies(loader("atf2_tbatf2roomenemies")); 
        tables.Add("Atf2.TbAtf2RoomEnemies", TbAtf2RoomEnemies);
        TbAtf2Parts = new Atf2.TbAtf2Parts(loader("atf2_tbatf2parts")); 
        tables.Add("Atf2.TbAtf2Parts", TbAtf2Parts);
        TbAtf2Effects = new Atf2.TbAtf2Effects(loader("atf2_tbatf2effects")); 
        tables.Add("Atf2.TbAtf2Effects", TbAtf2Effects);
        TbAtf2ChampionTask = new Atf2.TbAtf2ChampionTask(loader("atf2_tbatf2championtask")); 
        tables.Add("Atf2.TbAtf2ChampionTask", TbAtf2ChampionTask);
        TbAtf2TropyLoot = new Atf2.TbAtf2TropyLoot(loader("atf2_tbatf2tropyloot")); 
        tables.Add("Atf2.TbAtf2TropyLoot", TbAtf2TropyLoot);
        TbActivityFireRewardConfig = new ActivityFireReward.TbActivityFireRewardConfig(loader("activityfirereward_tbactivityfirerewardconfig")); 
        tables.Add("ActivityFireReward.TbActivityFireRewardConfig", TbActivityFireRewardConfig);
        TbActivityFireGoodConfig = new ActivityFireGoods.TbActivityFireGoodConfig(loader("activityfiregoods_tbactivityfiregoodconfig")); 
        tables.Add("ActivityFireGoods.TbActivityFireGoodConfig", TbActivityFireGoodConfig);
        TbActivityFireCharacterConfig = new ActivityFireCharacters.TbActivityFireCharacterConfig(loader("activityfirecharacters_tbactivityfirecharacterconfig")); 
        tables.Add("ActivityFireCharacters.TbActivityFireCharacterConfig", TbActivityFireCharacterConfig);
        TbNewPlayerLevel = new game.TbNewPlayerLevel(loader("game_tbnewplayerlevel")); 
        tables.Add("game.TbNewPlayerLevel", TbNewPlayerLevel);
        TbGameUnlockCondition = new game.TbGameUnlockCondition(loader("game_tbgameunlockcondition")); 
        tables.Add("game.TbGameUnlockCondition", TbGameUnlockCondition);
        TbFriendsOnlineActivityTaskConfig = new task.TbFriendsOnlineActivityTaskConfig(loader("task_tbfriendsonlineactivitytaskconfig")); 
        tables.Add("task.TbFriendsOnlineActivityTaskConfig", TbFriendsOnlineActivityTaskConfig);
        TbActivityNewYear2025CheckinRewardConfig = new ActivityNewYear2025CheckinReward.TbActivityNewYear2025CheckinRewardConfig(loader("activitynewyear2025checkinreward_tbactivitynewyear2025checkinrewardconfig")); 
        tables.Add("ActivityNewYear2025CheckinReward.TbActivityNewYear2025CheckinRewardConfig", TbActivityNewYear2025CheckinRewardConfig);
        TbActivityRebornEggRewardConfig = new ActivityRebornEggReward.TbActivityRebornEggRewardConfig(loader("activityreborneggreward_tbactivityreborneggrewardconfig")); 
        tables.Add("ActivityRebornEggReward.TbActivityRebornEggRewardConfig", TbActivityRebornEggRewardConfig);
        TbActivityRebornEggBuffConfig = new ActivityRebornEggBuff.TbActivityRebornEggBuffConfig(loader("activityreborneggbuff_tbactivityreborneggbuffconfig")); 
        tables.Add("ActivityRebornEggBuff.TbActivityRebornEggBuffConfig", TbActivityRebornEggBuffConfig);
        TbActivityRebornEggFollowerConfig = new ActivityRebornEggFollower.TbActivityRebornEggFollowerConfig(loader("activityreborneggfollower_tbactivityreborneggfollowerconfig")); 
        tables.Add("ActivityRebornEggFollower.TbActivityRebornEggFollowerConfig", TbActivityRebornEggFollowerConfig);
        TbForgeableFusionWeapon = new game.TbForgeableFusionWeapon(loader("game_tbforgeablefusionweapon")); 
        tables.Add("game.TbForgeableFusionWeapon", TbForgeableFusionWeapon);
        TbRedDotConfigs = new UI.TbRedDotConfigs(loader("ui_tbreddotconfigs")); 
        tables.Add("UI.TbRedDotConfigs", TbRedDotConfigs);
        TbActivityLegendRewardConfig = new ActivityLegendReward.TbActivityLegendRewardConfig(loader("activitylegendreward_tbactivitylegendrewardconfig")); 
        tables.Add("ActivityLegendReward.TbActivityLegendRewardConfig", TbActivityLegendRewardConfig);
        TbActivityLegendBuffConfig = new ActivityLegendBuff.TbActivityLegendBuffConfig(loader("activitylegendbuff_tbactivitylegendbuffconfig")); 
        tables.Add("ActivityLegendBuff.TbActivityLegendBuffConfig", TbActivityLegendBuffConfig);
        TbActivityLegendEquipDropConfig = new ActivityLegendEquipDrop.TbActivityLegendEquipDropConfig(loader("activitylegendequipdrop_tbactivitylegendequipdropconfig")); 
        tables.Add("ActivityLegendEquipDrop.TbActivityLegendEquipDropConfig", TbActivityLegendEquipDropConfig);
        TbActivityLegendRankingConfig = new ActivityLegendRanking.TbActivityLegendRankingConfig(loader("activitylegendranking_tbactivitylegendrankingconfig")); 
        tables.Add("ActivityLegendRanking.TbActivityLegendRankingConfig", TbActivityLegendRankingConfig);
        TbActivityLegendConstConfig = new ActivityLegendConst.TbActivityLegendConstConfig(loader("activitylegendconst_tbactivitylegendconstconfig")); 
        tables.Add("ActivityLegendConst.TbActivityLegendConstConfig", TbActivityLegendConstConfig);
        TbTaskMissionReturnConfig = new task.TbTaskMissionReturnConfig(loader("task_tbtaskmissionreturnconfig")); 
        tables.Add("task.TbTaskMissionReturnConfig", TbTaskMissionReturnConfig);
        TbTaskMissionH5Config = new task.TbTaskMissionH5Config(loader("task_tbtaskmissionh5config")); 
        tables.Add("task.TbTaskMissionH5Config", TbTaskMissionH5Config);
        TbVersionEggMachineConfig = new config.TbVersionEggMachineConfig(loader("config_tbversioneggmachineconfig")); 
        tables.Add("config.TbVersionEggMachineConfig", TbVersionEggMachineConfig);
        TbWeeklyTrailKnightConfig = new config.TbWeeklyTrailKnightConfig(loader("config_tbweeklytrailknightconfig")); 
        tables.Add("config.TbWeeklyTrailKnightConfig", TbWeeklyTrailKnightConfig);
        TbRmbMall = new Store.TbRmbMall(loader("store_tbrmbmall")); 
        tables.Add("Store.TbRmbMall", TbRmbMall);
        TbActivityReturnPlayerCheckinRewardConfig = new ActivityReturnPlayerCheckinReward.TbActivityReturnPlayerCheckinRewardConfig(loader("activityreturnplayercheckinreward_tbactivityreturnplayercheckinrewardconfig")); 
        tables.Add("ActivityReturnPlayerCheckinReward.TbActivityReturnPlayerCheckinRewardConfig", TbActivityReturnPlayerCheckinRewardConfig);
        TbTaskBeginnerConfig = new task.TbTaskBeginnerConfig(loader("task_tbtaskbeginnerconfig")); 
        tables.Add("task.TbTaskBeginnerConfig", TbTaskBeginnerConfig);
        TbFishAndSeasonStoreConfigTime = new config.TbFishAndSeasonStoreConfigTime(loader("config_tbfishandseasonstoreconfigtime")); 
        tables.Add("config.TbFishAndSeasonStoreConfigTime", TbFishAndSeasonStoreConfigTime);
        TbFishAndSeasonStoreConfig = new config.TbFishAndSeasonStoreConfig(loader("config_tbfishandseasonstoreconfig")); 
        tables.Add("config.TbFishAndSeasonStoreConfig", TbFishAndSeasonStoreConfig);
        TbAram2ChampionTask = new Aram.TbAram2ChampionTask(loader("aram_tbaram2championtask")); 
        tables.Add("Aram.TbAram2ChampionTask", TbAram2ChampionTask);
        TbAram2TrophyLoot = new Aram.TbAram2TrophyLoot(loader("aram_tbaram2trophyloot")); 
        tables.Add("Aram.TbAram2TrophyLoot", TbAram2TrophyLoot);
        TbAram2YaoQiLevels = new Aram.TbAram2YaoQiLevels(loader("aram_tbaram2yaoqilevels")); 
        tables.Add("Aram.TbAram2YaoQiLevels", TbAram2YaoQiLevels);
        TbAram2YaoQiEffects = new Aram.TbAram2YaoQiEffects(loader("aram_tbaram2yaoqieffects")); 
        tables.Add("Aram.TbAram2YaoQiEffects", TbAram2YaoQiEffects);
        TbAramShopItems = new Aram.TbAramShopItems(loader("aram_tbaramshopitems")); 
        tables.Add("Aram.TbAramShopItems", TbAramShopItems);
        TbAramConst = new Aram.TbAramConst(loader("aram_tbaramconst")); 
        tables.Add("Aram.TbAramConst", TbAramConst);
        TbWeaponAffix = new game.TbWeaponAffix(loader("game_tbweaponaffix")); 
        tables.Add("game.TbWeaponAffix", TbWeaponAffix);
        TbWeaponAffixCompatibility = new game.TbWeaponAffixCompatibility(loader("game_tbweaponaffixcompatibility")); 
        tables.Add("game.TbWeaponAffixCompatibility", TbWeaponAffixCompatibility);
        TbActivityBugFeatureAffix = new ActivityBugFeatureAffix.TbActivityBugFeatureAffix(loader("activitybugfeatureaffix_tbactivitybugfeatureaffix")); 
        tables.Add("ActivityBugFeatureAffix.TbActivityBugFeatureAffix", TbActivityBugFeatureAffix);
        TbLevelWeaponCollectorRarity = new WeaponUpgrade.TbLevelWeaponCollectorRarity(loader("weaponupgrade_tblevelweaponcollectorrarity")); 
        tables.Add("WeaponUpgrade.TbLevelWeaponCollectorRarity", TbLevelWeaponCollectorRarity);
        TbActivityBugFeatureConst = new ActivityBugFeatureConst.TbActivityBugFeatureConst(loader("activitybugfeatureconst_tbactivitybugfeatureconst")); 
        tables.Add("ActivityBugFeatureConst.TbActivityBugFeatureConst", TbActivityBugFeatureConst);
        TbLDEnemies = new LevelDifficulty.TbLDEnemies(loader("leveldifficulty_tbldenemies")); 
        tables.Add("LevelDifficulty.TbLDEnemies", TbLDEnemies);
        TbLDEnemiesWave = new LevelDifficulty.TbLDEnemiesWave(loader("leveldifficulty_tbldenemieswave")); 
        tables.Add("LevelDifficulty.TbLDEnemiesWave", TbLDEnemiesWave);
        TbLDEnemyConst = new LevelDifficulty.TbLDEnemyConst(loader("leveldifficulty_tbldenemyconst")); 
        tables.Add("LevelDifficulty.TbLDEnemyConst", TbLDEnemyConst);

        PostInit();
        TbAchievementCategories.Resolve(tables); 
        TbAchievementRewards.Resolve(tables); 
        TbAchievementGroups.Resolve(tables); 
        TbEnemyProperties.Resolve(tables); 
        TbEnemyGroups.Resolve(tables); 
        TbLevels.Resolve(tables); 
        TbHeros.Resolve(tables); 
        TbWeapons.Resolve(tables); 
        TbCareerTaskConfig.Resolve(tables); 
        TbWantedTaskTargetConfig.Resolve(tables); 
        TbWantedTaskRewardConfig.Resolve(tables); 
        TbConstants.Resolve(tables); 
        TbCharacter.Resolve(tables); 
        TbInherit.Resolve(tables); 
        TbHonoraryTitle.Resolve(tables); 
        TbAntiCrackingConfig.Resolve(tables); 
        TbCharacterTalkConfig.Resolve(tables); 
        TbCharacterDrawingConfig.Resolve(tables); 
        TbSkinShowEffectConfig.Resolve(tables); 
        TbSkill.Resolve(tables); 
        TbMall.Resolve(tables); 
        TbGood.Resolve(tables); 
        TbJumpPage.Resolve(tables); 
        TbUnlockCondition.Resolve(tables); 
        TbWeaponFragment.Resolve(tables); 
        TbCommon.Resolve(tables); 
        TbAIAttribute.Resolve(tables); 
        TbGashapon.Resolve(tables); 
        TbGashaponType.Resolve(tables); 
        TbCheckinRewardConfig.Resolve(tables); 
        TbMRRewardItems.Resolve(tables); 
        TbMRBattle.Resolve(tables); 
        TbMRRoomEvent.Resolve(tables); 
        TbMRText.Resolve(tables); 
        TbMRRandomRoom.Resolve(tables); 
        TbMREventPool.Resolve(tables); 
        TbMRConst.Resolve(tables); 
        TbMRMonsterGroup.Resolve(tables); 
        TbMRRecruitPrice.Resolve(tables); 
        TbMRUnit.Resolve(tables); 
        TbMRAbility.Resolve(tables); 
        TbMRBuff.Resolve(tables); 
        TbMRProjectile.Resolve(tables); 
        TbMRTrait.Resolve(tables); 
        TbMRTrap.Resolve(tables); 
        TbMRTraitGroup.Resolve(tables); 
        TbMRCombatAction.Resolve(tables); 
        TbMRMerchant.Resolve(tables); 
        TbMRMonsterScore.Resolve(tables); 
        TbMRCollect.Resolve(tables); 
        TbItem.Resolve(tables); 
        TbLevelEnemy.Resolve(tables); 
        TbHalloweenRewardConfig.Resolve(tables); 
        TbNoobPresetBuilds.Resolve(tables); 
        TbNoobLevels.Resolve(tables); 
        TbTalentGroups.Resolve(tables); 
        TbWeaponGroup.Resolve(tables); 
        TbAtch.Resolve(tables); 
        TbBuff.Resolve(tables); 
        TbWeapon.Resolve(tables); 
        TbEffect.Resolve(tables); 
        TbLoot.Resolve(tables); 
        TbBattleDifficulty.Resolve(tables); 
        TbRoom.Resolve(tables); 
        TbRoomReward.Resolve(tables); 
        TbMobGroup.Resolve(tables); 
        TbMob.Resolve(tables); 
        TbProj.Resolve(tables); 
        TbComboGunCharacter.Resolve(tables); 
        TbTropyLoot.Resolve(tables); 
        TbChampionTask.Resolve(tables); 
        TbOnline.Resolve(tables); 
        TbNobilityRank.Resolve(tables); 
        TbCampaignSelection.Resolve(tables); 
        TbGachaPool.Resolve(tables); 
        TbFragmentStore.Resolve(tables); 
        TbMountAttribute.Resolve(tables); 
        TbFollowerAttribute.Resolve(tables); 
        TbEnemyWave.Resolve(tables); 
        TbPlayScript.Resolve(tables); 
        TbPosterConfig.Resolve(tables); 
        TbGashaponNewWeapons.Resolve(tables); 
        TbRewardWeapons.Resolve(tables); 
        TbRandomAward.Resolve(tables); 
        TbSundryConfig.Resolve(tables); 
        TbCheckinVeteranRewardConfig.Resolve(tables); 
        TbFusionWeaponActivityRewards.Resolve(tables); 
        TbT2TropyLoot.Resolve(tables); 
        TbT2ChampionTask.Resolve(tables); 
        TbT2Mercenaries.Resolve(tables); 
        TbT2Traits.Resolve(tables); 
        TbT2Weapons.Resolve(tables); 
        TbT2Cookings.Resolve(tables); 
        TbT2EnemyProperties.Resolve(tables); 
        TbT2EnemyGroups.Resolve(tables); 
        TbT2Good.Resolve(tables); 
        TbT2GoodGroups.Resolve(tables); 
        TbT2LevelStore.Resolve(tables); 
        TbT2Commanders.Resolve(tables); 
        TbT2Levels.Resolve(tables); 
        TbT2Difficulties.Resolve(tables); 
        TbT2Talents.Resolve(tables); 
        TbT2Const.Resolve(tables); 
        TbT2CommanderAbility.Resolve(tables); 
        TbT2Loot.Resolve(tables); 
        TbZongziActivityRewards.Resolve(tables); 
        TbActivityCheckinRewardConfig.Resolve(tables); 
        TbBugActivityRewards.Resolve(tables); 
        TbWeaponEvolution.Resolve(tables); 
        TbWeaponEvolutionSell.Resolve(tables); 
        TbGachaWeapon.Resolve(tables); 
        TbWUGachaPool.Resolve(tables); 
        TbWeaponMod.Resolve(tables); 
        TbWeaponModOnCost.Resolve(tables); 
        TbWeaponSkin.Resolve(tables); 
        TbModExp.Resolve(tables); 
        TbBossEvolutionWeaponRate.Resolve(tables); 
        TbCollectorWeapons.Resolve(tables); 
        TbBpLoot.Resolve(tables); 
        TbBpTask.Resolve(tables); 
        TbBpList.Resolve(tables); 
        TbActivityConfig.Resolve(tables); 
        TbActivitySchoolRewardConfig.Resolve(tables); 
        TbActivitySchoolQuestionConfig.Resolve(tables); 
        TbActivitySchoolAnswerConfig.Resolve(tables); 
        TbActivitySchoolBonusConfig.Resolve(tables); 
        TbActivitySchoolTaskConfig.Resolve(tables); 
        TbHalloweenMotorcyleConfig.Resolve(tables); 
        TbActivityDreamRewardConfig.Resolve(tables); 
        TbChristmas2024RewardConfig.Resolve(tables); 
        TbActivitySkinConfig.Resolve(tables); 
        TbRetireRandomAward.Resolve(tables); 
        TbHidenPickableObjects.Resolve(tables); 
        TbC17Skill1Config.Resolve(tables); 
        TbAtf2Const.Resolve(tables); 
        TbAtf2Enemies.Resolve(tables); 
        TbAtf2RoomEnemies.Resolve(tables); 
        TbAtf2Parts.Resolve(tables); 
        TbAtf2Effects.Resolve(tables); 
        TbAtf2ChampionTask.Resolve(tables); 
        TbAtf2TropyLoot.Resolve(tables); 
        TbActivityFireRewardConfig.Resolve(tables); 
        TbActivityFireGoodConfig.Resolve(tables); 
        TbActivityFireCharacterConfig.Resolve(tables); 
        TbNewPlayerLevel.Resolve(tables); 
        TbGameUnlockCondition.Resolve(tables); 
        TbFriendsOnlineActivityTaskConfig.Resolve(tables); 
        TbActivityNewYear2025CheckinRewardConfig.Resolve(tables); 
        TbActivityRebornEggRewardConfig.Resolve(tables); 
        TbActivityRebornEggBuffConfig.Resolve(tables); 
        TbActivityRebornEggFollowerConfig.Resolve(tables); 
        TbForgeableFusionWeapon.Resolve(tables); 
        TbRedDotConfigs.Resolve(tables); 
        TbActivityLegendRewardConfig.Resolve(tables); 
        TbActivityLegendBuffConfig.Resolve(tables); 
        TbActivityLegendEquipDropConfig.Resolve(tables); 
        TbActivityLegendRankingConfig.Resolve(tables); 
        TbActivityLegendConstConfig.Resolve(tables); 
        TbTaskMissionReturnConfig.Resolve(tables); 
        TbTaskMissionH5Config.Resolve(tables); 
        TbVersionEggMachineConfig.Resolve(tables); 
        TbWeeklyTrailKnightConfig.Resolve(tables); 
        TbRmbMall.Resolve(tables); 
        TbActivityReturnPlayerCheckinRewardConfig.Resolve(tables); 
        TbTaskBeginnerConfig.Resolve(tables); 
        TbFishAndSeasonStoreConfigTime.Resolve(tables); 
        TbFishAndSeasonStoreConfig.Resolve(tables); 
        TbAram2ChampionTask.Resolve(tables); 
        TbAram2TrophyLoot.Resolve(tables); 
        TbAram2YaoQiLevels.Resolve(tables); 
        TbAram2YaoQiEffects.Resolve(tables); 
        TbAramShopItems.Resolve(tables); 
        TbAramConst.Resolve(tables); 
        TbWeaponAffix.Resolve(tables); 
        TbWeaponAffixCompatibility.Resolve(tables); 
        TbActivityBugFeatureAffix.Resolve(tables); 
        TbLevelWeaponCollectorRarity.Resolve(tables); 
        TbActivityBugFeatureConst.Resolve(tables); 
        TbLDEnemies.Resolve(tables); 
        TbLDEnemiesWave.Resolve(tables); 
        TbLDEnemyConst.Resolve(tables); 
        PostResolve();
    }

    public void TranslateText(System.Func<string, string, string> translator)
    {
        TbAchievementCategories.TranslateText(translator); 
        TbAchievementRewards.TranslateText(translator); 
        TbAchievementGroups.TranslateText(translator); 
        TbEnemyProperties.TranslateText(translator); 
        TbEnemyGroups.TranslateText(translator); 
        TbLevels.TranslateText(translator); 
        TbHeros.TranslateText(translator); 
        TbWeapons.TranslateText(translator); 
        TbCareerTaskConfig.TranslateText(translator); 
        TbWantedTaskTargetConfig.TranslateText(translator); 
        TbWantedTaskRewardConfig.TranslateText(translator); 
        TbConstants.TranslateText(translator); 
        TbCharacter.TranslateText(translator); 
        TbInherit.TranslateText(translator); 
        TbHonoraryTitle.TranslateText(translator); 
        TbAntiCrackingConfig.TranslateText(translator); 
        TbCharacterTalkConfig.TranslateText(translator); 
        TbCharacterDrawingConfig.TranslateText(translator); 
        TbSkinShowEffectConfig.TranslateText(translator); 
        TbSkill.TranslateText(translator); 
        TbMall.TranslateText(translator); 
        TbGood.TranslateText(translator); 
        TbJumpPage.TranslateText(translator); 
        TbUnlockCondition.TranslateText(translator); 
        TbWeaponFragment.TranslateText(translator); 
        TbCommon.TranslateText(translator); 
        TbAIAttribute.TranslateText(translator); 
        TbGashapon.TranslateText(translator); 
        TbGashaponType.TranslateText(translator); 
        TbCheckinRewardConfig.TranslateText(translator); 
        TbMRRewardItems.TranslateText(translator); 
        TbMRBattle.TranslateText(translator); 
        TbMRRoomEvent.TranslateText(translator); 
        TbMRText.TranslateText(translator); 
        TbMRRandomRoom.TranslateText(translator); 
        TbMREventPool.TranslateText(translator); 
        TbMRConst.TranslateText(translator); 
        TbMRMonsterGroup.TranslateText(translator); 
        TbMRRecruitPrice.TranslateText(translator); 
        TbMRUnit.TranslateText(translator); 
        TbMRAbility.TranslateText(translator); 
        TbMRBuff.TranslateText(translator); 
        TbMRProjectile.TranslateText(translator); 
        TbMRTrait.TranslateText(translator); 
        TbMRTrap.TranslateText(translator); 
        TbMRTraitGroup.TranslateText(translator); 
        TbMRCombatAction.TranslateText(translator); 
        TbMRMerchant.TranslateText(translator); 
        TbMRMonsterScore.TranslateText(translator); 
        TbMRCollect.TranslateText(translator); 
        TbItem.TranslateText(translator); 
        TbLevelEnemy.TranslateText(translator); 
        TbHalloweenRewardConfig.TranslateText(translator); 
        TbNoobPresetBuilds.TranslateText(translator); 
        TbNoobLevels.TranslateText(translator); 
        TbTalentGroups.TranslateText(translator); 
        TbWeaponGroup.TranslateText(translator); 
        TbAtch.TranslateText(translator); 
        TbBuff.TranslateText(translator); 
        TbWeapon.TranslateText(translator); 
        TbEffect.TranslateText(translator); 
        TbLoot.TranslateText(translator); 
        TbBattleDifficulty.TranslateText(translator); 
        TbRoom.TranslateText(translator); 
        TbRoomReward.TranslateText(translator); 
        TbMobGroup.TranslateText(translator); 
        TbMob.TranslateText(translator); 
        TbProj.TranslateText(translator); 
        TbComboGunCharacter.TranslateText(translator); 
        TbTropyLoot.TranslateText(translator); 
        TbChampionTask.TranslateText(translator); 
        TbOnline.TranslateText(translator); 
        TbNobilityRank.TranslateText(translator); 
        TbCampaignSelection.TranslateText(translator); 
        TbGachaPool.TranslateText(translator); 
        TbFragmentStore.TranslateText(translator); 
        TbMountAttribute.TranslateText(translator); 
        TbFollowerAttribute.TranslateText(translator); 
        TbEnemyWave.TranslateText(translator); 
        TbPlayScript.TranslateText(translator); 
        TbPosterConfig.TranslateText(translator); 
        TbGashaponNewWeapons.TranslateText(translator); 
        TbRewardWeapons.TranslateText(translator); 
        TbRandomAward.TranslateText(translator); 
        TbSundryConfig.TranslateText(translator); 
        TbCheckinVeteranRewardConfig.TranslateText(translator); 
        TbFusionWeaponActivityRewards.TranslateText(translator); 
        TbT2TropyLoot.TranslateText(translator); 
        TbT2ChampionTask.TranslateText(translator); 
        TbT2Mercenaries.TranslateText(translator); 
        TbT2Traits.TranslateText(translator); 
        TbT2Weapons.TranslateText(translator); 
        TbT2Cookings.TranslateText(translator); 
        TbT2EnemyProperties.TranslateText(translator); 
        TbT2EnemyGroups.TranslateText(translator); 
        TbT2Good.TranslateText(translator); 
        TbT2GoodGroups.TranslateText(translator); 
        TbT2LevelStore.TranslateText(translator); 
        TbT2Commanders.TranslateText(translator); 
        TbT2Levels.TranslateText(translator); 
        TbT2Difficulties.TranslateText(translator); 
        TbT2Talents.TranslateText(translator); 
        TbT2Const.TranslateText(translator); 
        TbT2CommanderAbility.TranslateText(translator); 
        TbT2Loot.TranslateText(translator); 
        TbZongziActivityRewards.TranslateText(translator); 
        TbActivityCheckinRewardConfig.TranslateText(translator); 
        TbBugActivityRewards.TranslateText(translator); 
        TbWeaponEvolution.TranslateText(translator); 
        TbWeaponEvolutionSell.TranslateText(translator); 
        TbGachaWeapon.TranslateText(translator); 
        TbWUGachaPool.TranslateText(translator); 
        TbWeaponMod.TranslateText(translator); 
        TbWeaponModOnCost.TranslateText(translator); 
        TbWeaponSkin.TranslateText(translator); 
        TbModExp.TranslateText(translator); 
        TbBossEvolutionWeaponRate.TranslateText(translator); 
        TbCollectorWeapons.TranslateText(translator); 
        TbBpLoot.TranslateText(translator); 
        TbBpTask.TranslateText(translator); 
        TbBpList.TranslateText(translator); 
        TbActivityConfig.TranslateText(translator); 
        TbActivitySchoolRewardConfig.TranslateText(translator); 
        TbActivitySchoolQuestionConfig.TranslateText(translator); 
        TbActivitySchoolAnswerConfig.TranslateText(translator); 
        TbActivitySchoolBonusConfig.TranslateText(translator); 
        TbActivitySchoolTaskConfig.TranslateText(translator); 
        TbHalloweenMotorcyleConfig.TranslateText(translator); 
        TbActivityDreamRewardConfig.TranslateText(translator); 
        TbChristmas2024RewardConfig.TranslateText(translator); 
        TbActivitySkinConfig.TranslateText(translator); 
        TbRetireRandomAward.TranslateText(translator); 
        TbHidenPickableObjects.TranslateText(translator); 
        TbC17Skill1Config.TranslateText(translator); 
        TbAtf2Const.TranslateText(translator); 
        TbAtf2Enemies.TranslateText(translator); 
        TbAtf2RoomEnemies.TranslateText(translator); 
        TbAtf2Parts.TranslateText(translator); 
        TbAtf2Effects.TranslateText(translator); 
        TbAtf2ChampionTask.TranslateText(translator); 
        TbAtf2TropyLoot.TranslateText(translator); 
        TbActivityFireRewardConfig.TranslateText(translator); 
        TbActivityFireGoodConfig.TranslateText(translator); 
        TbActivityFireCharacterConfig.TranslateText(translator); 
        TbNewPlayerLevel.TranslateText(translator); 
        TbGameUnlockCondition.TranslateText(translator); 
        TbFriendsOnlineActivityTaskConfig.TranslateText(translator); 
        TbActivityNewYear2025CheckinRewardConfig.TranslateText(translator); 
        TbActivityRebornEggRewardConfig.TranslateText(translator); 
        TbActivityRebornEggBuffConfig.TranslateText(translator); 
        TbActivityRebornEggFollowerConfig.TranslateText(translator); 
        TbForgeableFusionWeapon.TranslateText(translator); 
        TbRedDotConfigs.TranslateText(translator); 
        TbActivityLegendRewardConfig.TranslateText(translator); 
        TbActivityLegendBuffConfig.TranslateText(translator); 
        TbActivityLegendEquipDropConfig.TranslateText(translator); 
        TbActivityLegendRankingConfig.TranslateText(translator); 
        TbActivityLegendConstConfig.TranslateText(translator); 
        TbTaskMissionReturnConfig.TranslateText(translator); 
        TbTaskMissionH5Config.TranslateText(translator); 
        TbVersionEggMachineConfig.TranslateText(translator); 
        TbWeeklyTrailKnightConfig.TranslateText(translator); 
        TbRmbMall.TranslateText(translator); 
        TbActivityReturnPlayerCheckinRewardConfig.TranslateText(translator); 
        TbTaskBeginnerConfig.TranslateText(translator); 
        TbFishAndSeasonStoreConfigTime.TranslateText(translator); 
        TbFishAndSeasonStoreConfig.TranslateText(translator); 
        TbAram2ChampionTask.TranslateText(translator); 
        TbAram2TrophyLoot.TranslateText(translator); 
        TbAram2YaoQiLevels.TranslateText(translator); 
        TbAram2YaoQiEffects.TranslateText(translator); 
        TbAramShopItems.TranslateText(translator); 
        TbAramConst.TranslateText(translator); 
        TbWeaponAffix.TranslateText(translator); 
        TbWeaponAffixCompatibility.TranslateText(translator); 
        TbActivityBugFeatureAffix.TranslateText(translator); 
        TbLevelWeaponCollectorRarity.TranslateText(translator); 
        TbActivityBugFeatureConst.TranslateText(translator); 
        TbLDEnemies.TranslateText(translator); 
        TbLDEnemiesWave.TranslateText(translator); 
        TbLDEnemyConst.TranslateText(translator); 
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}