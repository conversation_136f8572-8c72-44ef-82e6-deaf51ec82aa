//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;
using System.Collections.Generic;


namespace cfg.Troop2
{
   
public partial class TbT2Loot
{
    private readonly Dictionary<string, Troop2.T2Loot> _dataMap;
    private readonly List<Troop2.T2Loot> _dataList;
    
    public TbT2Loot(ByteBuf _buf)
    {
        _dataMap = new Dictionary<string, Troop2.T2Loot>();
        _dataList = new List<Troop2.T2Loot>();
        
        for(int n = _buf.ReadSize() ; n > 0 ; --n)
        {
            Troop2.T2Loot _v;
            _v = Troop2.T2Loot.DeserializeT2Loot(_buf);
            _dataList.Add(_v);
            _dataMap.Add(_v.LootID, _v);
        }
        PostInit();
    }

    public Dictionary<string, Troop2.T2Loot> DataMap => _dataMap;
    public List<Troop2.T2Loot> DataList => _dataList;

    public Troop2.T2Loot GetOrDefault(string key) => _dataMap.TryGetValue(key, out var v) ? v : null;
    public Troop2.T2Loot Get(string key) => _dataMap[key];
    public Troop2.T2Loot this[string key] => _dataMap[key];

    public void Resolve(Dictionary<string, object> _tables)
    {
        foreach(var v in _dataList)
        {
            v.Resolve(_tables);
        }
        PostResolve();
    }

    public void TranslateText(System.Func<string, string, string> translator)
    {
        foreach(var v in _dataList)
        {
            v.TranslateText(translator);
        }
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}