//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;
using System.Collections.Generic;


namespace cfg.Troop2
{
public sealed partial class T2Loot :  Bright.Config.BeanBase 
{
    public T2Loot(ByteBuf _buf) 
    {
        LootID = _buf.ReadString();
        ItemType = _buf.ReadString();
        ItemPrefab = _buf.ReadString();
        Parameter = _buf.ReadString();
        PostInit();
    }

    public static T2Loot DeserializeT2Loot(ByteBuf _buf)
    {
        return new Troop2.T2Loot(_buf);
    }

    /// <summary>
    /// 在t2_enemy_properties.xlsx内填写的物品id
    /// </summary>
    public string LootID { get; private set; }
    /// <summary>
    /// 物品类型：局内、局外
    /// </summary>
    public string ItemType { get; private set; }
    /// <summary>
    /// 物品对应的预制体(天赋、饮料、武器对应t2_traits.xlxs/t2_cookings.xlsx/t2_weapons.xlsx内物品ID)
    /// </summary>
    public string ItemPrefab { get; private set; }
    public string Parameter { get; private set; }

    public const int __ID__ = 1721840788;
    public override int GetTypeId() => __ID__;

    public  void Resolve(Dictionary<string, object> _tables)
    {
        PostResolve();
    }

    public  void TranslateText(System.Func<string, string, string> translator)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "LootID:" + LootID + ","
        + "ItemType:" + ItemType + ","
        + "ItemPrefab:" + ItemPrefab + ","
        + "Parameter:" + Parameter + ","
        + "}";
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}