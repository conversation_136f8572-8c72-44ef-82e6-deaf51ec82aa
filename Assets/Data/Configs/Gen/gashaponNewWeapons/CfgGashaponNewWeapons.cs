//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;
using System.Collections.Generic;


namespace cfg.gashaponNewWeapons
{
public sealed partial class CfgGashaponNewWeapons :  Bright.Config.BeanBase 
{
    public CfgGashaponNewWeapons(ByteBuf _buf) 
    {
        WeaponName = _buf.ReadString();
        Weight = _buf.ReadFloat();
        PostInit();
    }

    public static CfgGashaponNewWeapons DeserializeCfgGashaponNewWeapons(ByteBuf _buf)
    {
        return new gashaponNewWeapons.CfgGashaponNewWeapons(_buf);
    }

    /// <summary>
    /// 武器ID
    /// </summary>
    public string WeaponName { get; private set; }
    /// <summary>
    /// 权重
    /// </summary>
    public float Weight { get; private set; }

    public const int __ID__ = -1484897314;
    public override int GetTypeId() => __ID__;

    public  void Resolve(Dictionary<string, object> _tables)
    {
        PostResolve();
    }

    public  void TranslateText(System.Func<string, string, string> translator)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "WeaponName:" + WeaponName + ","
        + "Weight:" + Weight + ","
        + "}";
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}