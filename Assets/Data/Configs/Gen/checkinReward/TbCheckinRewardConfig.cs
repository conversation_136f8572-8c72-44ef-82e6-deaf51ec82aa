//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;
using System.Collections.Generic;


namespace cfg.checkinReward
{
   
public partial class TbCheckinRewardConfig
{
    private readonly Dictionary<int, checkinReward.CheckinRewardConfig> _dataMap;
    private readonly List<checkinReward.CheckinRewardConfig> _dataList;
    
    public TbCheckinRewardConfig(ByteBuf _buf)
    {
        _dataMap = new Dictionary<int, checkinReward.CheckinRewardConfig>();
        _dataList = new List<checkinReward.CheckinRewardConfig>();
        
        for(int n = _buf.ReadSize() ; n > 0 ; --n)
        {
            checkinReward.CheckinRewardConfig _v;
            _v = checkinReward.CheckinRewardConfig.DeserializeCheckinRewardConfig(_buf);
            _dataList.Add(_v);
            _dataMap.Add(_v.Id, _v);
        }
        PostInit();
    }

    public Dictionary<int, checkinReward.CheckinRewardConfig> DataMap => _dataMap;
    public List<checkinReward.CheckinRewardConfig> DataList => _dataList;

    public checkinReward.CheckinRewardConfig GetOrDefault(int key) => _dataMap.TryGetValue(key, out var v) ? v : null;
    public checkinReward.CheckinRewardConfig Get(int key) => _dataMap[key];
    public checkinReward.CheckinRewardConfig this[int key] => _dataMap[key];

    public void Resolve(Dictionary<string, object> _tables)
    {
        foreach(var v in _dataList)
        {
            v.Resolve(_tables);
        }
        PostResolve();
    }

    public void TranslateText(System.Func<string, string, string> translator)
    {
        foreach(var v in _dataList)
        {
            v.TranslateText(translator);
        }
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}