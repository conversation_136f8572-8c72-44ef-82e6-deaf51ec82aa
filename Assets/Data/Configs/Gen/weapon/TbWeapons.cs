//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;
using System.Collections.Generic;


namespace cfg.weapon
{
   
public partial class TbWeapons
{
    private readonly Dictionary<string, weapon.Weapons> _dataMap;
    private readonly List<weapon.Weapons> _dataList;
    
    public TbWeapons(ByteBuf _buf)
    {
        _dataMap = new Dictionary<string, weapon.Weapons>();
        _dataList = new List<weapon.Weapons>();
        
        for(int n = _buf.ReadSize() ; n > 0 ; --n)
        {
            weapon.Weapons _v;
            _v = weapon.Weapons.DeserializeWeapons(_buf);
            _dataList.Add(_v);
            _dataMap.Add(_v.Name, _v);
        }
        PostInit();
    }

    public Dictionary<string, weapon.Weapons> DataMap => _dataMap;
    public List<weapon.Weapons> DataList => _dataList;

    public weapon.Weapons GetOrDefault(string key) => _dataMap.TryGetValue(key, out var v) ? v : null;
    public weapon.Weapons Get(string key) => _dataMap[key];
    public weapon.Weapons this[string key] => _dataMap[key];

    public void Resolve(Dictionary<string, object> _tables)
    {
        foreach(var v in _dataList)
        {
            v.Resolve(_tables);
        }
        PostResolve();
    }

    public void TranslateText(System.Func<string, string, string> translator)
    {
        foreach(var v in _dataList)
        {
            v.TranslateText(translator);
        }
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}