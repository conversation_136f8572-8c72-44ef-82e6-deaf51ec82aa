//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;
using System.Collections.Generic;


namespace cfg.game
{
   
public partial class TbConstants
{
    private readonly Dictionary<int, game.Constants> _dataMap;
    private readonly List<game.Constants> _dataList;
    
    public TbConstants(ByteBuf _buf)
    {
        _dataMap = new Dictionary<int, game.Constants>();
        _dataList = new List<game.Constants>();
        
        for(int n = _buf.ReadSize() ; n > 0 ; --n)
        {
            game.Constants _v;
            _v = game.Constants.DeserializeConstants(_buf);
            _dataList.Add(_v);
            _dataMap.Add(_v.Id, _v);
        }
        PostInit();
    }

    public Dictionary<int, game.Constants> DataMap => _dataMap;
    public List<game.Constants> DataList => _dataList;

    public game.Constants GetOrDefault(int key) => _dataMap.TryGetValue(key, out var v) ? v : null;
    public game.Constants Get(int key) => _dataMap[key];
    public game.Constants this[int key] => _dataMap[key];

    public void Resolve(Dictionary<string, object> _tables)
    {
        foreach(var v in _dataList)
        {
            v.Resolve(_tables);
        }
        PostResolve();
    }

    public void TranslateText(System.Func<string, string, string> translator)
    {
        foreach(var v in _dataList)
        {
            v.TranslateText(translator);
        }
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}