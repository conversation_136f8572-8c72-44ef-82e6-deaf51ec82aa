//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;
using System.Collections.Generic;


namespace cfg.game
{
/// <summary>
/// AIController的默认策略组合
/// </summary>
public sealed partial class WeightedStrategy :  Bright.Config.BeanBase 
{
    public WeightedStrategy(ByteBuf _buf) 
    {
        TargetingStrategy = _buf.ReadString();
        MoveStrategy = _buf.ReadString();
        Weight = _buf.ReadInt();
        PostInit();
    }

    public static WeightedStrategy DeserializeWeightedStrategy(ByteBuf _buf)
    {
        return new game.WeightedStrategy(_buf);
    }

    public string TargetingStrategy { get; private set; }
    public string MoveStrategy { get; private set; }
    public int Weight { get; private set; }

    public const int __ID__ = 2084212806;
    public override int GetTypeId() => __ID__;

    public  void Resolve(Dictionary<string, object> _tables)
    {
        PostResolve();
    }

    public  void TranslateText(System.Func<string, string, string> translator)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "TargetingStrategy:" + TargetingStrategy + ","
        + "MoveStrategy:" + MoveStrategy + ","
        + "Weight:" + Weight + ","
        + "}";
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}