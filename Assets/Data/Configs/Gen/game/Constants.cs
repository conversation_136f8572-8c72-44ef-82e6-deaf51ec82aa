//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Bright.Serialization;
using System.Collections.Generic;


namespace cfg.game
{
public sealed partial class Constants :  Bright.Config.BeanBase 
{
    public Constants(ByteBuf _buf) 
    {
        Id = _buf.ReadInt();
        IntValue = _buf.ReadInt();
        {int n0 = System.Math.Min(_buf.ReadSize(), _buf.Size);StringList = new System.Collections.Generic.List<string>(n0);for(var i0 = 0 ; i0 < n0 ; i0++) { string _e0;  _e0 = _buf.ReadString(); StringList.Add(_e0);}}
        PostInit();
    }

    public static Constants DeserializeConstants(ByteBuf _buf)
    {
        return new game.Constants(_buf);
    }

    /// <summary>
    /// id
    /// </summary>
    public int Id { get; private set; }
    /// <summary>
    /// 值
    /// </summary>
    public int IntValue { get; private set; }
    /// <summary>
    /// 字符串列表
    /// </summary>
    public System.Collections.Generic.List<string> StringList { get; private set; }

    public const int __ID__ = -2009560525;
    public override int GetTypeId() => __ID__;

    public  void Resolve(Dictionary<string, object> _tables)
    {
        PostResolve();
    }

    public  void TranslateText(System.Func<string, string, string> translator)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "Id:" + Id + ","
        + "IntValue:" + IntValue + ","
        + "StringList:" + Bright.Common.StringUtil.CollectionToString(StringList) + ","
        + "}";
    }
    
    partial void PostInit();
    partial void PostResolve();
}

}