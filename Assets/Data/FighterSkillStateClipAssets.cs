using UnityEngine;
using UnityEngine.Serialization;

[CreateAssetMenu(fileName = "fighter_skill_state_clip_assets", menuName = "Data/FighterSkillStateClipAssets")]
public class FighterSkillStateClipAssets: ScriptableObject {
    private static FighterSkillStateClipAssets _assets;

    public static FighterSkillStateClipAssets Assets =>
        _assets
            ? _assets
            : _assets = ResourcesUtil.Load<FighterSkillStateClipAssets>("Data/fighter_skill_state_clip_assets.asset");

    public Color[] ActiveColors;
    public Color[] InActiveColors;
    public float[] ClipAndReloadOffsets;
    public float[] MultiGameClipOffsets;
    public bool[] NeedUseInitWeaponTexture;
}
