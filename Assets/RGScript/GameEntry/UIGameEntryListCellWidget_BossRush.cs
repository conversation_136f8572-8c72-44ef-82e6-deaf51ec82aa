using I2.Loc;
using RGScript.Data;
using RGScript.Util;
using UI.Base;

namespace RGScript.GameEntry {
    public class UIGameEntryListCellWidget_BossRush : BaseUIWidget {
        private UIGameEntryListCellData _data;
        private UIGameEntryListCellWidget _baseWidget;

        protected override void OnInit() {
            base.OnInit();

            InitUI();
            AddListeners();
        }

        private void InitUI() {
            _baseWidget = transform.GetComponent<UIGameEntryListCellWidget>();
        }

        protected override void OnStartUp(params object[] args) {
            base.OnStartUp(args);
            _data = (UIGameEntryListCellData)args[0];
            RegisterUIWidget(_baseWidget);
            _data.onStartClick = OnClickButton;
            _baseWidget.StartUp(_data);
        }

        // 开始游戏/继续游戏
        private void OnClickButton(UIGameEntryStartClickData data) {
            if (StatisticData.data.GetEventCount(RGGameConst.BOSSRUSH_GUIDE_READ) == 0) {
                DataMgr.GameEntryData.HasSelectEntry = false;
                if (global::UIFramework.UIManager.Inst.GetUIView<CommonGuideView>() != null) {
                    return;
                }
                
                GameEntryData.ShowGuideView(_data.entryType);
                return;
            }
            
            var gameMode = emGameMode.BossRush;
            var gameInfo = new NameUtil.SaveData.GameInfo {
                GameMode = gameMode,
                Factors = BattleData.data.factors
            };
            var battleData = BattleDataList.list.GetBattleData(gameInfo);
            if (battleData == null || !battleData.canContinue || GameUtil.InMultiGame()) {
                var leftCount = GameEntryData.GetDailyLeftEnterCount(_data.entryType);
                if (leftCount <= 0) {
                    DataMgr.GameEntryData.HasSelectEntry = false;
                    UICanvas.GetInstance().ShowTempMessage(ScriptLocalization.Get("item/br_times_limit") /*"今日挑战次数已达上限，欢迎明天再来。"*/ , 2f, false);
                    return;
                }
            
                GameEntryData.NewBossRush(data.isBadAss, data.isPure);
            } else {
                GameEntryData.BtnContinueClick(UIGameEntryType.BossRush, gameInfo, data);
            }
        }
        
        private void AddListeners() {
        }


        private void RemoveListeners() {
        }

        protected override void OnClear() {
            base.OnClear();
            UnRegisterUIWidget(_baseWidget);
            _baseWidget.Clear();
        }

        protected override void OnAfterDestroy() {
            base.OnAfterDestroy();
            RemoveListeners();
        }
    }
}