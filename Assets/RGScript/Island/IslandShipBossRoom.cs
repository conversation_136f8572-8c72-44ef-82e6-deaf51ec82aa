using DG.Tweening;
using EnemyGenerator;
using System.Collections.Generic;
using UnityEngine;
using BossShowUpProcess;
using BossSkill;
using Com.LuisPedroFonseca.ProCamera2D;
using CommandUtil;
using System;
using RGScript.Map;
using UI.MiniMap;

/// <summary>
/// 海岛关战舰BOSS房间
/// </summary>
public class IslandShipBossRoom : MonoBehaviour, IBossShowUpProcess
{
    private bool _start = false;
    private RGRoomX _this_room;
    private RGEBossController[] bossList;

    public GameObject darkScreen;

    private List<GameObject> bossToHide = new List<GameObject>();
    private CommandUtil.CommandList commands = new CommandUtil.CommandList(true);
    void Start() {
        _this_room = transform.GetComponent<RGRoomX>();
        var bossCreator = transform.GetComponentInChildren<BossCreator>();
        bossCreator.onCreateBoss.AddListener(bossObj => {
            bossToHide.Add(bossObj);
        });
        var delay = DOTween.Sequence();
        delay.AppendInterval(2); // 等所有BOSS的Start先调用
        delay.AppendCallback(()=>{
            foreach(var b in bossToHide)
                b.SetActive(false);
        });
    }

    public void ShowUp() {
        _start = true;

                
        darkScreen.SetActive(true);
        darkScreen.transform.Find("darkScreen").GetComponent<UnityEngine.UI.Image>().color = new Color(0, 0, 0, 0);
        var sequence = DOTween.Sequence();
        RGGameSceneManager.Inst.controller.StopMove();
        RGGameSceneManager.Inst.controller.move_dir = Vector3.zero;
        RGGameSceneManager.Inst.controller.awake = false;
        // 停止浮游炮
        var funnels = GameObject.FindObjectsOfType<FunnelWeaponContainer>();
        foreach(var funnel in funnels){
            funnel.rigibody.velocity = Vector3.zero;
            funnel.awake = false;
        }

        var bossCreator = transform.GetComponentInChildren<BossCreator>();
        var bossShipObj = bossCreator.createdBoss.Find(bossObj => bossObj.GetComponent<Boss29Skill>() != null);
        var bossShip = bossShipObj.GetComponent<RGEBossController>();
        var distance = 40;
        bossShip.attributeProxy.SetProtectedFromDamage(true, "BossShow");
        
        // 黑屏淡入
        sequence.Append(darkScreen.transform.Find("darkScreen").GetComponent<UnityEngine.UI.Image>().DOFade(1, 1));
        RGMusicManager.Inst.FadeOutBGM(1);
        var darkImages = darkScreen.transform.Find("narrativeStyle").GetComponentsInChildren<UnityEngine.UI.Image>();
        sequence.AppendCallback(()=>{
            DisplayUI(false);
            foreach(var bossObj in bossToHide){
                bossObj.SetActive(true);
                var eController = bossObj.GetComponent<RGEController>();
                if(eController)
                    eController.awake = false;
            }
            bossShip.facing = -1;
            bossShip.FixRotationByFacing();
        });
        sequence.AppendInterval(0.1f);
        sequence.AppendCallback(() => {
            foreach (var img in darkImages)
                img.color = new Color(0, 0, 0, 1);
            ChangeRoomToIsolated();
            ChangePlayerPosition();
            SetBossShipPosition();

            foreach(var bossObj in bossToHide){
                var eController = bossObj.GetComponent<RGEController>();
                if(eController)
                    eController.awake = true;
            }

            bossShip.gameObject.SetActive(true);
            bossShip.transform.position += new Vector3(distance, 0, 0);

            var cameraZ = ProCamera2D.Instance.GameCamera.transform.position.z;
            ProCamera2D.Instance.CameraTargets.Clear();
            commands.Add(CommandUtil.Command.New(dt => {
                var pos = bossShip.transform.position;
                pos.z = cameraZ;
                ProCamera2D.Instance.GameCamera.transform.position = pos; 
                return true;
            }));
        });
        // 黑屏淡出
        sequence.AppendCallback(()=>{
            bossShip.GetComponent<Boss29Skill>().BlowSteamWhistle();
        });
        sequence.Append(darkScreen.transform.Find("darkScreen").GetComponent<UnityEngine.UI.Image>().DOFade(0, 1));
        // 战舰进场过程
        sequence.Join(bossShip.transform.DOBlendableMoveBy(new Vector3(-distance, 0, 0), 6.5f));
        // Boss_Info 登场
        sequence.AppendCallback(() => {
            commands.Clear();
            DisplayBossShowup(() => {
                var bossInfo = darkScreen.transform.Find("boss_info");
                bossInfo.transform.SetParent(GameObject.Find("/Canvas").transform);
                darkScreen.SetActive(false);
                
                DisplayUI(true);
                
                bossShipObj.GetComponent<Boss29Skill>().BattleStart();
                bossShip.attributeProxy.SetProtectedFromDamage(false, "BossShow");
            });
            RGGameSceneManager.Inst.controller.awake = true;
            foreach(var funnel in funnels)
                funnel.awake = true;
        });

        bossShip.onShowGate += ()=>{
            bossShip.GateObject.transform.position += new Vector3(0, 4, 0);
        };
    }

    public bool CanShow => true;

    /// <summary>
    /// 切断房间所有通路
    /// </summary>
    void ChangeRoomToIsolated() {
        // 隐藏其他房间
        var rooms = GameObject.FindObjectsOfType<RGRoomX>();
        foreach (var room in rooms) {
            if (room != _this_room) {
                room.gameObject.SetActive(false);
            }
        }

        // 关闭通路
        _this_room.openDirection = emDirection.None;

        var islandGen = MapManager.Instance.GetComponent<IslandGenerator>();
        islandGen.ClearTiles();
        var count = _this_room.wall_group.childCount;
        for (var i = 0; i < count; ++i) {
            _this_room.wall_group.GetChild(i).gameObject.SetActive(false);
        }
        islandGen.RefreshRoom(_this_room);

        // 房间中央放置少许管道
        
        islandGen.SetPipe(_this_room, new Vector3Int(0, 3, 0));
        islandGen.SetPipe(_this_room, new Vector3Int(0, 2, 0));
        
        // 删除地上的武器 宝箱
        var temp_child_count = RGGameSceneManager.Inst.temp_objects_parent.childCount;
        for (var i = temp_child_count - 1; i >= 0; --i) {
            var obj = RGGameSceneManager.Inst.temp_objects_parent.GetChild(i).gameObject;
            if(!obj.activeSelf)
                continue;
            if(obj.GetComponent<RGWeapon>() != null || obj.GetComponent<IChest>() != null)
                GameObject.Destroy(obj);
        }

        try {
            // 处理小地图,只留下当前房间
            var uiMapView = UICanvas.Inst.transform.GetComponentInChildren<MiniMapUIView>(true);
            if (uiMapView != null) {
                uiMapView.HideOtherRooms(_this_room.name);
            }
        }
        catch (Exception e){
            Debug.LogError(e.Message + "\n" + e.StackTrace);
        }
    }

    /// <summary>
    /// 玩家瞬移到房间中央
    /// </summary>
    void ChangePlayerPosition() {
        var roomCenterPos = _this_room.transform.position;
        var dir = roomCenterPos - RGGameSceneManager.Inst.controller.transform.position;
        float distance = 0;
        if (Mathf.Abs(dir.x) > Mathf.Abs(dir.y)) {
            dir.y = 0;
            distance = _this_room.room_width * 0.5f;
        } else {
            dir.x = 0;
            distance = _this_room.room_height * 0.5f;
        }
        dir = dir.normalized * distance;

        var baseControllers = GameObject.FindObjectsOfType<RGBaseController>();
        foreach(var c in baseControllers){
            c.transform.position += dir;
        }
    }

    /// <summary>
    /// 设置BOSS战舰位置
    /// </summary>
    void SetBossShipPosition() {
        var bossCreator = transform.GetComponentInChildren<BossCreator>();
        var bossShipObj = bossCreator.createdBoss.Find(bossObj => bossObj.GetComponent<Boss29Skill>() != null);
        var bossSkill = bossShipObj.GetComponent<Boss29Skill>();
        bossSkill.transform.position = bossSkill.GetStayPosition(Vector3.up);
    }
    

    void DisplayUI(bool b) {
        UICanvas.Inst.ShowUI(b);
    }

    /// <summary>
    /// BOSS登场动画
    /// </summary>
    void DisplayBossShowup(Action finish) {
        var bossInfo = GameObject.Find("/Canvas").transform.Find("boss_info");
        bossInfo.transform.SetParent(darkScreen.transform);
        bossInfo.gameObject.SetActive(true);
        bossInfo.GetComponent<BossInfo>().ShowBossInfo();
        bossInfo.GetComponent<BossInfo>().OnAnimationFinish += finish;
        //boss击杀计时 开始
        BattleData.data.boss_begin_time = Time.time;
    }

    void Update() {
        commands.Update(Time.deltaTime);
    }
}
