using CustomFactor;
using System.Collections.Generic;

namespace RGScript.CustomFactor.Handler {
    public class NewPlayerGameInitEffect : AbstractFactorEffect<BattleData> {
        public override string EffectName { get; } = "NewPlayerGameInit";
        public const string BulletSpeedParam = "bulletSpeedParam";
        public const string BoxDropItemParam = "boxDropItemParam";

        public override Dictionary<string, ParamConfig> ParamConfigs { get; } = new Dictionary<string, ParamConfig>() {
            {BulletSpeedParam, new ParamConfig(BulletSpeedParam, "", ParamConfig.ParamType.Float)},
            {BoxDropItemParam, new ParamConfig(BoxDropItemParam, "", ParamConfig.ParamType.Float)},
        };

        public override void HandleEvent(string eventName, Dictionary<string, string> paramDic, BattleData target) {
            if (GameUtil.IsMultiGame()) {
                return;
            }

            if (!BattleData.data.marks.TryGetValue(RGGameConst.NEW_PLAYER_FACTOR_COUNT, out var factorCount) ||
                null == target) {
                return;
            }

            //降低子弹速度
            var bulletSpeedFactor = 1f - factorCount * ParamConfigs[BulletSpeedParam].GetFloatValue(paramDic);
            target.SetFloatMark(BulletSpeedParam, bulletSpeedFactor);

            //增加箱子掉落物品的概率
            var boxDropItemFactor = factorCount * ParamConfigs[BoxDropItemParam].GetFloatValue(paramDic);
            BaseModeProcess.ExtraBoxDropItemRate = boxDropItemFactor + factorCount > 0 ? 10 : 0;
        }
    }
}