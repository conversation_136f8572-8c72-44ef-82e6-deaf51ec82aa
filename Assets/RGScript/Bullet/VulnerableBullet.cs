using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class VulnerableBullet : MonoBehaviour
{
    public int hp;
    private DamageCarrier damageCarrier;
    public float size;
    public bool triggerEffect;
    int BulletLayerMask;
    void Awake() {
        damageCarrier = GetComponentInParent<DamageCarrier>();
        BulletLayerMask = LayerMask.GetMask("Bullet");
    }
    
    public void FixedUpdate() {
        if (damageCarrier == null) {
            return;
        }

        RaycastHit2D[] result = null;
        var hitCount = PhysicsUtil.CheckCircleHit(transform.position, size * transform.lossyScale.x, BulletLayerMask, ref result);
        for (var i = 0; i < hitCount; ++i) {
            if (result[i].transform == transform) {
                continue;
            }

            bool hit = false;
            if (result[i].transform.GetComponent<DamageTrigger>() is { } damageTrigger && damageTrigger.GetComponentInParent<DamageCarrier>() is { } parentDamageCarrier &&
                damageCarrier.camp != parentDamageCarrier.camp) {
                hp -= damageTrigger.Damage;
                hit = true;
                damageTrigger.ManuallyTrigger(gameObject.GetComponent<Collider2D>());
            } else if (result[i].transform.GetComponent<DamageCarrier>() is { } otherDamageCarrier && otherDamageCarrier.camp != damageCarrier.camp) {
                hp -= otherDamageCarrier.damageInfo.damage;
                hit = true;
                if (result[i].collider.gameObject.GetComponent<DamageTrigger>() is { } otherDamageTrigger) {
                    otherDamageTrigger.ManuallyTrigger(gameObject.GetComponent<Collider2D>());
                }
            }

            if (hit && hp <= 0) {
                if (triggerEffect) {
                    var effectTriggers = GetComponentsInChildren<RGBulletEffectTrigger>();
                    foreach (var et in effectTriggers) {
                        et.TriggerWith(null, damageCarrier.damageInfo.damage, false, false);
                    }
                }
                damageCarrier.DeleteResourceObject();
                break;
            }
        }
    }
}
