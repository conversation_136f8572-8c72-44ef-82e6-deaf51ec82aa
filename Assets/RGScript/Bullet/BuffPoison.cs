using UnityEngine;
using System.Collections;

public class BuffPoison : BuffDOT {
    protected override int deltaDamage {
        get {
            var bd = GetSourceBattleData();
            var extraDmg = (is_enemy && bd.HasShieldGas() ? 1 : 0 + base.deltaDamage);
            var deltaDmg = bd.GetPoisonDeltaDamage(is_enemy);
            if (deltaDmg >= 100) {
                // 大于100用于表示伤害提高100%
                var originDmg = is_enemy ? enemyDamage : playerDamage;
                deltaDmg = (int)(originDmg * deltaDmg / 100f);
            }
            extraDmg += deltaDmg;
            
            return extraDmg;
        }
    }

    protected override void Start() {
        base.Start();
        if (IsValid() && attribute != null) {
            attribute.ChangeSpeedNE("gas", -0.3f, duration);
        }
    }

    protected override bool IsValid() {
        if(!base.IsValid()){
            return false;
        }
        if (!is_enemy) {
            if (controller == null || controller.thisBattleData.HasShieldGas() || controller.ImmuneGas) {
                return false;
            }
        }
        return true;
    }

    protected override bool TryHandleImmune() {
        return controller && controller.TryHandleIfImmuneGas(false);
    }
}