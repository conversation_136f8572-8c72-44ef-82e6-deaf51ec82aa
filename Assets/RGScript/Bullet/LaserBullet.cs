using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;

/// <summary>
/// 带拖尾的子弹型激光
/// </summary>
public class LaserBullet : RGBullet {
    public float laserSize = 1f;
    internal int reflectCount = 4;
    List<Vector3> targets = new List<Vector3>();

    public override void OnTaken() {
        base.OnTaken();
        rigid2d.velocity = Vector2.zero;
        StopDestroy();
        if (!manual_destroy) {
            DestroySelf(destroy_time);
        }
        DetectTrace();
    }

    /// <summary>
    /// 确定运动路径
    /// </summary>
    RaycastHit2D[] hit_list = new RaycastHit2D[4];
    public void DetectTrace() {
        targets.Add(transform.position);
        FindTarget(transform.position, transform.right, null, reflectCount);
        var sequence = DOTween.Sequence();
        for (int i = 1; i < targets.Count; i++) {
            float distance = (targets[i] - targets[i - 1]).magnitude;
            sequence.Append(transform.DOMove(targets[i], distance / speed));
        }
        sequence.AppendCallback(() => { GetComponentInChildren<RGBulletTrigger>().DestroyBullet(null); });
        //transform.DOMove()
    }
    void FindTarget(Vector3 start, Vector3 direction, Collider2D lastCollider, int reflectCount) {
        RaycastHit2D hit = default(RaycastHit2D);
        if (lastCollider) {
            int hitCount = Physics2D.RaycastNonAlloc(start, direction, hit_list, 50f, 1 << LayerMask.NameToLayer("Wall"));
            if (hitCount > 0) {
                if (hitCount > 1 && (!lastCollider || lastCollider == hit_list[0].collider)) {
                    hit = hit_list[1];
                } else {
                    hit = hit_list[0];
                }
            }
        } else {
            hit = Physics2D.Raycast(start, direction, 50f, 1 << LayerMask.NameToLayer("Wall"));
        }
        if (hit.collider) {
            targets.Add(hit.point);
            if (--reflectCount > 0) {
                var dir = Vector2.Reflect(direction, hit.normal);
                FindTarget(hit.point, dir, hit.collider, reflectCount);
            }
        }
    }

    void UpdateTrail() {
    }
    
}
