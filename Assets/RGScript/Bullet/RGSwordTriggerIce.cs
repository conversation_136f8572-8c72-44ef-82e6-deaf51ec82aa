using UnityEngine;

/// <summary>
/// 晶蟹太刀暴击Trigger
/// </summary>
public class RGSwordTriggerIce : RGSwordTrigger {
    public GameObject ice_obj;
    public AudioClip effect_clip;
    public float createIceBuffPercent;
    public GameObject iceBuff;
    int _iceCount;
    
    private void Awake() {
        var battleData = GameUtil.GetBattleData(carrier);
        criticFactor = battleData.HasMasterElementCritic() ? battleData.criticRate : 1;
    }

    public override void OnTaken() {
        base.OnTaken();
        _iceCount = 0;
    }

    public override void OnHit(Collider2D other, bool isCritic) {
        base.OnHit(other, isCritic);
        if (isCritic && ice_obj != null) {
            CreateIce();
            BuffEffectTrigger buffTrigger = GetComponent<BuffEffectTrigger>();
            if (!buffTrigger) {
                buffTrigger = gameObject.AddComponent<BuffEffectTrigger>();
            }
            buffTrigger.buff = iceBuff;
            buffTrigger.probability = createIceBuffPercent;
        }
    }
    void CreateIce() {
        var damageCarrier = ice_obj.GetComponent<DamageCarrier>();
        damageCarrier.bulletInfo.SetBullet(ice_obj);
        damageCarrier.damageInfo.SetDamage(ice_obj.GetComponent<ExplodeLcicle>().damage);
        if (!the_bullet || !the_bullet.bulletInfo.sourceWeapon) {
            Debug.LogError($"bullet {name} no sourceWeapon");
            return;
        }

        for (int i = 0; i < (4 + _iceCount * 2); i++) {
            GameObject tempObj = BulletFactory.TakeBullet(damageCarrier.bulletInfo,
                the_bullet.bulletInfo.sourceWeapon.DecorateAdditionLevel(damageCarrier.damageInfo));
            tempObj.transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;
            float r = Random.Range(_iceCount * 2, (_iceCount + 1) * 2);
            int a = Random.Range(0, 360);
            if (a is > 90 and < 270) {
                tempObj.transform.localScale = new Vector3(-1, 1, 1);
            }
            tempObj.transform.position = transform.position + new Vector3(r * Mathf.Cos(a * Mathf.Deg2Rad), r * Mathf.Sin(a * Mathf.Deg2Rad), 0);
            //更新武器属性到子弹里
            tempObj.GetComponent<OffensiveInterface>().SetSourceObject(carrier.bulletInfo.sourceObject);
            the_bullet.bulletInfo.sourceWeapon.DecorateAdditionLevel(the_bullet.damageInfo);
        }
        _iceCount++;
        RGMusicManager.GetInstance().PlayEffect(effect_clip);
        if (_iceCount < 3) {
            Invoke(nameof(CreateIce), 0.25f * _iceCount);
        }
    }

}
