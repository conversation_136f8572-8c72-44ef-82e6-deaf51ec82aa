using Com.LuisPedroFonseca.ProCamera2D;
using I2.Loc;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class RGArrowBuffTriggerElf : RGArrowTrigger {
    public GameObject buff;
    ParticleSystem particle;

    void Awake() {
        particle = transform.parent.GetComponent<ParticleSystem>();
        criticFactor = BattleData.data.criticRate;
    }

    private void Start() {
        OnTaken();
    }
    public void OnTaken() {
        base.OnTaken();
        BuffEffectTrigger buffTrigger = GetComponent<BuffEffectTrigger>();
        if (!buffTrigger) {
            buffTrigger = gameObject.AddComponent<BuffEffectTrigger>();
        }
        buffTrigger.buff = buff;
    }

    IEnumerator ParticleDestroy() {
        particle.Stop();
        GetComponent<BoxCollider2D>().enabled = false;
        while (particle.IsAlive()) {
            yield return null;
        }
        Destroy(transform.parent.gameObject);
    }

    IEnumerator ParticleStop() {
        particle.Stop();
        GetComponent<BoxCollider2D>().enabled = false;
        while (particle.IsAlive()) {
            yield return null;
        }
        particle.Stop();
    }

    void HideTrail() {
        if (the_bullet.GetComponent<TrailRenderer>()) {
            the_bullet.GetComponent<TrailRenderer>().enabled = false;
        }
    }

    void StopEffects() {
        if (transform.childCount > 0 && transform.GetChild(0).GetComponent<ParticleSystem>()) {
            transform.GetChild(0).GetComponent<ParticleSystem>().Stop();
        }
        StartCoroutine(ParticleStop());
    }

    public override void TriggerBodyDestroy(Collider2D other) {
        base.TriggerBodyDestroy(other);
        StopEffects();
    }

    protected override void TriggerWallDestroy(Collider2D other) {
        base.TriggerWallDestroy(other);
        StopEffects();
    }
}
