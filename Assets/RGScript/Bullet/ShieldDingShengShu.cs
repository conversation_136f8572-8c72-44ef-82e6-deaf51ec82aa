using RGScript.Weapon.WeaponStateBehavior;
using UnityEngine;

namespace RGScript.Bullet {
    public class ShieldDingShengShu : MonoBehaviour {
        public GunJinGuBang weapon;

        public void OnTriggerEnter2D(Collider2D other) {
            var damageTrigger = other.GetComponent<DamageTrigger>();
            if (damageTrigger == null) {
                return;
            }
            
            if (damageTrigger.camp != 0) {
                return;
            }

            if (GameUtil.GetBattleData(weapon).HasBuff(emBuff.MasterSwordRebound)) {
                weapon.CreateReflectBullet(other.transform);
                PrefabPool.Inst.Store(other.gameObject);
            } else {
                if (damageTrigger.transform.parent.TryGetComponent<RGBullet>(out var bullet)) {
                    bullet.SetBulletSpeed(0);
                }
            }
        }
    }
}