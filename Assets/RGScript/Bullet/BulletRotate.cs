using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public class BulletRotate : RGBullet {
    public Sprite img_T;
    public Sprite img_TC;
    public Sprite img_C;
    public Sprite img_BC;
    public Sprite img_B;
    public bool face_camera = false;

    private SpriteRenderer target_img;
    private Vector3 tv3;

    [Header("x代表时间，y代表速度")] public List<Vector2> bullet_speed;
    protected float this_speed;
    private int move_index;

    protected override void Awake() {
        base.Awake();
        target_img = transform.Find("b").GetComponent<SpriteRenderer>();
        if (target_img != null) {
            tv3 = target_img.transform.localEulerAngles;
            AdjustImg();
        }

        _count = count;
        _lastIndex = _index = -1;
        rigid2d.gravityScale = 0;
        rigid2d.simulated = true;
        
        UpdateTicks();
        StartCoroutine(Following());

        if (bullet_speed[0].x == 0) {
            ChangeTheSpeed();
        } else {
            Invoke("ChangeTheSpeed", bullet_speed[0].x);
        }
    }

    private void ChangeTheSpeed() {
        this_speed = bullet_speed[move_index].y;
        Move();
        move_index++;
        if (move_index < bullet_speed.Count) {
            Invoke("ChangeTheSpeed", bullet_speed[move_index].x);
        }
    }
    
	private void Move(){
        rigid2d.velocity = transform.right * this_speed;
	}
    
    protected override void Destroy() {
		rigid2d.simulated = false;
    }

    # region 跟踪旋转

    [Header("Rotation")] public int count = 24; // adjust count
    public float interval = 0.1f; // adjust interval
    public float followRange = 6; //  超出该范围则不再跟踪
    public float maxRotateAngle = 15f;

    protected int _count;
    private List<float> _ticks; //  执行时间表
    private Vector3 _beginDirection;

    private int _index;
    private int _lastIndex;

    //  跟踪旋转的目标，用于碰撞检测
    protected RGEController _followTarget;
    protected Vector3 _followTargetPos;

    private IEnumerator Following() {
        var _waitForSeconds = new WaitForSeconds(interval);
        while (0 == count || _count-- > 0) {
            UpdateTarget();
            //  执行跟踪，不可以在target为null时直接停止，因为可能存在其它跟踪数据
            UpdateIndex();
            if (0 <= _index) {
                //  进入下个followRotationData时更新_beginDirection等数据
                if (_lastIndex != _index) {
                    _lastIndex = _index;
                    _beginDirection = transform.localEulerAngles;
                }

                if (null != _followTarget && !_followTarget.dead || _followTargetPos != default) {
                    UpdateEulerAngle();
                    AdjustImg();
                    Move();
                }
            }

            yield return _waitForSeconds;
        }
    }

    public void UpdateEulerAngle() {
        var targetDirection = Vector3.right;
        if (_followTarget) {
            targetDirection = (_followTarget.transform.position - transform.position).normalized;
        } else if (_followTargetPos != default) {
            targetDirection = (_followTargetPos - transform.position).normalized;
        }
        var eulerAngles = CalculateEulerAngle(targetDirection, ref _beginDirection);
        transform.localEulerAngles = eulerAngles;
    }

    private Vector3 CalculateEulerAngle(Vector2 targetDirection, ref Vector3 direction) {
        float anglea = Vector2.Angle(Vector2.right, targetDirection);
        if (targetDirection.y < 0)
            anglea = -anglea;

        var x = Mathf.Cos(direction.z * Mathf.PI / 180f);
        var y = Mathf.Sin(direction.z * Mathf.PI / 180f);
        if (direction.y == 180) {
            x *= -1;
        }

        Vector2 v2_form = new Vector2(x, y);
        float angleb = Vector2.Angle(Vector2.right, v2_form);
        if (v2_form.y < 0)
            angleb = -angleb;
        float angle_to = angleb - anglea;
        if (Mathf.Abs(angle_to) > 180)
            if (angle_to > 0)
                angle_to = angle_to - 360;
            else
                angle_to = 360 + angle_to;
        if (Mathf.Abs(angle_to) < maxRotateAngle)
            direction.z -= angle_to;
        else if (angle_to > 0)
            direction.z -= maxRotateAngle;
        else
            direction.z += maxRotateAngle;
        return direction;
    }

    private void UpdateTicks() {
        var time = Time.time;
        _ticks = new List<float>();
        _ticks.Add(time);
    }

    private void UpdateIndex() {
        var index = -1;
        for (var i = 0; i < _ticks.Count; i++)
            if (0 != _ticks[i] && Time.time >= _ticks[i])
                index = i;
        if (index > _index)
            _index = index;
    }

    #endregion

    #region 根据旋转角度，更新对应贴图

    private void AdjustImg() {
        float rota_z = transform.eulerAngles.z;
        target_img.transform.eulerAngles = tv3;
        if (rota_z > 22.5f) {
            if (rota_z > 67.5f) {
                if (rota_z > 112.5f) {
                    if (rota_z > 157.5f) {
                        if (rota_z > 202.5f) {
                            if (rota_z > 247.5f) {
                                if (rota_z > 292.5f) {
                                    if (rota_z > 337.5f) {
                                        target_img.sprite = img_C;
                                        if (face_camera) {
                                            target_img.flipX = true;
                                            target_img.transform.LookAt(Camera.main.transform.position);
                                        } else {
                                            target_img.flipX = false;
                                        }
                                    } else {
                                        target_img.sprite = img_BC;
                                        if (face_camera) {
                                            target_img.flipX = true;
                                            target_img.transform.LookAt(Camera.main.transform.position);
                                        } else {
                                            target_img.flipX = false;
                                        }
                                    }
                                } else {
                                    target_img.sprite = img_B;
                                    if (face_camera) {
                                        target_img.flipX = true;
                                        target_img.transform.LookAt(Camera.main.transform.position);
                                    } else {
                                        target_img.flipX = false;
                                    }
                                }
                            } else {
                                target_img.sprite = img_BC;
                                if (face_camera) {
                                    target_img.flipX = false;
                                    target_img.transform.LookAt(Camera.main.transform.position);
                                } else {
                                    target_img.flipX = true;
                                }
                            }
                        } else {
                            target_img.sprite = img_C;
                            if (face_camera) {
                                target_img.flipX = false;
                                target_img.transform.LookAt(Camera.main.transform.position);
                            } else {
                                target_img.flipX = true;
                            }
                        }
                    } else {
                        target_img.sprite = img_TC;
                        if (face_camera) {
                            target_img.flipX = false;
                            target_img.transform.LookAt(Camera.main.transform.position);
                        } else {
                            target_img.flipX = true;
                        }
                    }
                } else {
                    target_img.sprite = img_T;
                    if (face_camera) {
                        target_img.flipX = true;
                        target_img.transform.LookAt(Camera.main.transform.position);
                    } else {
                        target_img.flipX = false;
                    }
                }
            } else {
                target_img.sprite = img_TC;
                if (face_camera) {
                    target_img.flipX = true;
                    target_img.transform.LookAt(Camera.main.transform.position);
                } else {
                    target_img.flipX = false;
                }
            }
        } else {
            target_img.sprite = img_C;
            if (face_camera) {
                target_img.flipX = true;
                target_img.transform.LookAt(Camera.main.transform.position);
            } else {
                target_img.flipX = false;
            }
        }
    }

    #endregion

    #region 搜索敌人

    [Header("Search")] public float searchRange = 6;
    private RaycastHit2D[] hits = new RaycastHit2D[10];

    protected virtual void UpdateTarget() {
        if (_followTarget != null && Vector2.Distance(_followTarget.transform.position, transform.position) <= followRange) {
            return;
        }

        float min = int.MaxValue;
        var hitCount = Physics2D.CircleCastNonAlloc(transform.position, searchRange, new Vector2(0, 0), hits, 0.0f, 1 << LayerMask.NameToLayer("Body_E"));
        if (0 < hitCount) {
            var characters = hits.Where(hit2D => null != hit2D.transform && hit2D.transform.GetComponent<RGEController>() != null)
                .Select(hit2D => hit2D.transform.GetComponent<RGEController>())
                .ToList();

            if (characters.Any())
                foreach (var character in characters) {
                    float distance = Vector3.Distance(transform.position, character.transform.position);
                    if (min > distance) {
                        min = distance;
                        _followTarget = character;
                    }
                }
        }
    }

    #endregion
}