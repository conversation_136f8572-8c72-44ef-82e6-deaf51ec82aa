using UnityEngine;
using UnityEngine.Serialization;

namespace RGScript.Bullet{
    /// <summary>
    /// 脱离父物体
    /// </summary>
    public class UnParentObject : MonoBehaviour {
        public bool autoStart;

        public float destroyTime = 0.5f;
        
        private void OnEnable() {
            if (autoStart) {
                SetParentToTemp();
            }
        }

        public void SetParentToTemp() {
            transform.SetParent(RGGameSceneManager.Inst.temp_objects_parent);

            if (destroyTime > 0) {
                GameObject.Destroy(gameObject,destroyTime);        
            }
        }
    }
}
