using DG.Tweening;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 显示 buff 状态的图
/// </summary>
public class SkinBuffIndicator : MonoBehaviour {
    public bool fadeInOut = false;
    private SpriteRenderer _icon;
    private float buff_time;

    protected void Start() {
        Destroy(gameObject, buff_time);
    }

    public void SetBuffTime(float duration) {
        buff_time = duration;
        Destroy(gameObject, buff_time);
        if (fadeInOut) {
            if (_icon == null) {
                _icon = transform.GetChild(0).GetComponent<SpriteRenderer>();
            }

            if (_icon != null) {
                _icon.color = new Color(1, 1, 1, 0);
                _icon.DOFade(1, 0.5f);
                DOVirtual.DelayedCall(buff_time - 0.5f, () => {
                    _icon.DOFade(0, 0.5f);
                });
            }
        }
    }
}