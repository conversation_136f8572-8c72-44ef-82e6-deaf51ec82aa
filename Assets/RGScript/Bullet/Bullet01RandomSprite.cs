using System.Collections.Generic;
using UnityEngine;

namespace RGScript.Bullet {
    /// <summary>
    /// 随机贴图子弹
    /// </summary>
    public class Bullet01RandomSprite : Bullet01 {
        public float bulletSize = 24f;
        public List<Sprite> bulletSprites;

        internal virtual void OnEnable() {
            RandomReplaceBulletSrite();
        }

        private void RandomReplaceBulletSrite() {
            if (bulletSprites.Count > 0) {
                var sprite = bulletSprites.GetRandomObject(rg_random);
                transform.Find("b").GetComponent<SpriteRenderer>().sprite = sprite;
            }
        }

        public override void OnTaken() {
            base.OnTaken();
            
            var width = transform.Find("b").GetComponent<SpriteRenderer>().sprite.rect.width;
            var ratio = bulletSize / width;
            transform.Find("b").localScale *= ratio;
        }
    }
}