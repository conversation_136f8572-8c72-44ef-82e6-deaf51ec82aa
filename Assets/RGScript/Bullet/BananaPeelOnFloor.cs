using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;

public class BananaPeelOnFloor : BulletThrowOntoFloor
{
    public float speedMin = 8;
    public float speedMax = 12;
    public float speedTimes = 1.5f;
    public float jumpHeight = 1.5f;
    public float jumpDuration = 0.4f;
    public float crushDetectSize = 0.7f;
    public GameObject hitFx;
    public GameObject speedUpFx;
    public CommonColliderTriggerHandle stepInCheck;

    public BulletInfoInspector crushBulletConfig;
    public AudioClip slideSound;

    public override void Init() {
        base.Init();
        GetComponentInChildren<DamageTrigger>().enabled = true;
        rigid2d.bodyType = RigidbodyType2D.Dynamic;
        rigid2d.velocity = Vector2.zero;
        rigid2d.gravityScale = 0;
        stepInCheck.gameObject.SetActive(false);
    }

    void CreateHitFx(){
        if (null == hitFx) {
            return;
        }

        GameObject temp_hit = PrefabPool.Inst.Take(hitFx, image.transform.position, Quaternion.identity);
        temp_hit.transform.SetParent(RGGameSceneManager.GetInstance().temp_objects_parent, false);
        float fxScale = bulletInfo.size;

        temp_hit.transform.localScale = Vector3.one * fxScale;
    }

    public override void OnFloor(){
        base.OnFloor();
        rigid2d.bodyType = RigidbodyType2D.Static;
        GetComponentInChildren<DamageTrigger>().GetComponent<Collider2D>().enabled = false;
        if(PhysicsUtil.CheckCircleHit(transform.position, 0.1f, LayerMask.GetMask("Fall", "Water", "Edge")) > 0){
            DeleteResourceObject();
        }
        else{
            stepInCheck.onEnter = OnEnter;
            stepInCheck.gameObject.SetActive(true);
        }
    }

    void OnEnter(Collider2D other){
        var rigidbody = other.GetComponent<Rigidbody2D>();
        if(rigidbody != null){
            if(GameUtil.IsSingleGame()){
                if(IsCharacterEnter(rigidbody.transform) && CommonUpdateMono.Find(rigidbody.gameObject, "banana") == null){
                    var dir = rigidbody.velocity.normalized;
                    StopDestroy();
                    gameObject.SetActive(false);
                    Execute(this, rigidbody.transform, rigidbody.transform.position, dir,  ()=>{
                        DeleteResourceObject();
                    });
                    stepInCheck.gameObject.SetActive(false);
                }
            }
            else{
                if(IsCharacterEnter(rigidbody.transform)){
                    DeleteResourceObject();
                    MessageManager.SendBananaMessage(RGGetPath.GetNetId(rigidbody.transform), rigidbody.transform.position, rigidbody.velocity.normalized);
                    stepInCheck.gameObject.SetActive(false);
                }
            }
        }
    }

    static bool IsCharacterEnter(Transform other){
        var ret = false;
        if(other.GetComponent<RGController>() is {} controller && controller.mount == null){
            if(!controller.isFly && controller.rigibody.velocity.magnitude > 0){
                ret = true;
            }
        }
        else if(other.GetComponent<RGEController>() is {} eController && !eController.dead && !eController.isBoss && 
            !eController.HasTag(emEnemyTag.Static) && !eController.HasTag(emEnemyTag.Boss) &&
            eController.attribute.speed > 0){
            if(eController.rigibody.velocity.magnitude > 0){
                ret = true;
            }
        }
        return ret;
    }


    public static void Execute(BananaPeelOnFloor config, Transform trans, Vector2 pos, Vector2 dir, System.Action finished){
        var controller = trans.GetComponent<RGController>();
        var eController = trans.GetComponent<RGEController>();        
        RigidbodySlide(config, trans.GetComponent<Rigidbody2D>(), pos, dir, (v)=>{ 
            if(controller != null){
                controller.SetFixedVelocity(v); 
            }
            else if(eController != null){
                eController.SetFixedVelocity(v);
            }
        }, finished);
        if(config.slideSound != null){
            RGMusicManager.Inst.PlayEffect(config.slideSound);
        }
    }

    static CommonUpdateMono RigidbodySlide(BananaPeelOnFloor config, Rigidbody2D rigidbody, Vector2 startPos, Vector2 dir, System.Action<Vector2> setVelocity, System.Action finishedCallback){
        var exist = CommonUpdateMono.Find(rigidbody.gameObject, "banana");
        if(exist != null){
            return null;
        }

        var updateMono = rigidbody.gameObject.AddComponent<CommonUpdateMono>();
        rigidbody.transform.position = startPos;

        var speedUpFxObj = GameObject.Instantiate(config.speedUpFx, rigidbody.transform);
        speedUpFxObj.transform.localPosition = Vector3.up * 1;
                
        updateMono.cmdTag = "banana";
        
        var v = Mathf.Clamp(rigidbody.velocity.magnitude * config.speedTimes, config.speedMin, Mathf.Max(config.speedMax, rigidbody.velocity.magnitude));
        if(v == 0){
            v = 1;
        }

        GameUtil.ResetCharacterAI(rigidbody.transform, false);

        setVelocity(dir * v);
        speedUpFxObj.transform.right = dir;


        var stop = false;
        var stopMannually = false;
        var _isFly = false;
        var collisionMask = Physics2DCollisionMatrix.MaskForLayer(LayerMask.NameToLayer("Body"));
        var _lastPosition = rigidbody.transform.position;
        var _stickyTime = 0f;
        var _deltaPosition = Vector2.zero;
        updateMono.onUpdate = (dt)=>{
            if(stop){
                return;
            }
            
            if(rigidbody != null){
                // 碰到无法穿过的障碍时停止
                RaycastHit2D[] checkHit = null;
                var hitCount = PhysicsUtil.CheckCircleHit((rigidbody.transform.position + Vector3.up * 0.5f).Vec2(), 0.75f, collisionMask, ref checkHit);
                var hitObstacle = false;
                if(hitCount > 0){
                    for(var i = 0; i < hitCount; ++i){
                        if(!checkHit[i].collider.isTrigger && !rigidbody.transform.IsParentOf(checkHit[i].transform) && 
                            Vector2.Dot((checkHit[i].point - rigidbody.transform.position.Vec2()).normalized, dir) > 0){
                            hitObstacle = true;
                            break;
                        }
                    }
                }

                if(hitObstacle && (rigidbody.velocity.magnitude < Mathf.Epsilon || Vector2.Dot(rigidbody.velocity.normalized, dir) < 0.9f)){
                    stop = true;
                }

                if(rigidbody.velocity.magnitude > v){
                    v = rigidbody.velocity.magnitude;
                }
                
                
                if(rigidbody.transform.GetComponent<RGController>() is {} controller){
                    // 角色上坐骑停止
                    if(controller.mount != null){
                        stop = true;
                        stopMannually = true;
                    }

                    // 角色从空中落地时停止
                    if(controller.isFly){
                        _isFly = true;
                    }
                    else if(_isFly){
                        stop = true;
                    }
                }

                setVelocity(dir * v);
                speedUpFxObj.transform.right = dir;

                // 角色反向移动时停止
                _deltaPosition = rigidbody.transform.position - _lastPosition;
                if(_deltaPosition != Vector2.zero && Vector2.Dot(_deltaPosition.normalized, dir) < 0){
                    stop = true;
                    stopMannually = true;
                }

                if(Vector2.Distance(rigidbody.transform.position, _lastPosition) < Mathf.Epsilon){ // 防止陷入上面的检测条件盲区导致无法停止
                    _stickyTime += dt;
                    if(_stickyTime > 0.5f){
                        stop = true;
                        stopMannually = true;
                    }
                }
                else{
                    _lastPosition = rigidbody.transform.position;
                    _stickyTime = 0;
                }
            }
            else{
                stop = true;
            }
            
            if(stop){
                OnEnd(!stopMannually, _isFly);
            }
        };


        void BodyLiftAndDrop(Transform trans, bool tremble, System.Action finished){
            var localPosition = trans.localPosition;
            var jump = trans.gameObject.AddComponent<CommonUpdateMono>();
            var objJump = ObjectJump.Jump(trans.gameObject, 1.5f, 35);
            var objTremble = ObjectTremble.Tremble(trans.gameObject, 0, 0.1f, 0);
            jump.onUpdateWithResult = (dt)=>{
                if(tremble){
                    objTremble.Update(dt);
                }
                return objJump.Update(dt);
            };
            jump.onDisabled = ()=>{
                objTremble.Stop();
                objJump.Stop();
                finished();
            };
        }


        void OnEnd(bool createShockwave, bool dropFromAir){
            if(speedUpFxObj != null){
                GameObject.Destroy(speedUpFxObj);
                speedUpFxObj = null;
            }
            if(createShockwave){
                if(rigidbody.gameObject == RGGameSceneManager.Inst.gameObject){
                    GameUtil.CameraShake(3);
                }
                var pos = rigidbody.transform.position;
                var crushBulletInfo = config.crushBulletConfig.GetBulletInfo().SetPosition(pos).SetCamp(1).SetSourceObject(rigidbody.gameObject).SetSourceWeapon(null);
                crushBulletInfo.SetBulletSize(crushBulletInfo.size * Mathf.Max(1, config.bulletInfo.size));
                var crushDamageInfo = config.crushBulletConfig.GetDamageInfo().SetCamp(1);
                crushDamageInfo.SetDamage(crushDamageInfo.damage);
                BulletFactory.TakeBullet(crushBulletInfo, crushDamageInfo);

                // 短暂无敌但不能移动
                if(rigidbody != null){
                    var bodyImg = rigidbody.transform.Find("img");
                    if(bodyImg != null){
                        System.Action finishCallback = null;
                        GameUtil.CharacterStopAttack(rigidbody.transform);
                        GameUtil.ResetCharacterAI(rigidbody.transform, false);
                        GameUtil.SetCharacterAnimEnabled(rigidbody.transform, false);
                        GameUtil.SetCharacterAwake(rigidbody.transform, false);
                        finishCallback = ()=>{
                            GameUtil.SetCharacterAwake(rigidbody.transform, true);
                            GameUtil.SetCharacterAnimEnabled(rigidbody.transform, true);
                            GameUtil.CharacterStopMove(rigidbody.transform);
                            GameUtil.StartCharacterAI(rigidbody.transform);
                        };
                        BodyLiftAndDrop(bodyImg, !dropFromAir, finishCallback);
                    }
                    else{
                        
                        GameUtil.CharacterStopMove(rigidbody.transform);
                    }
                }
                else{
                    GameUtil.CharacterStopMove(rigidbody.transform);
                }
            }
            else{
                GameUtil.CharacterStopMove(rigidbody.transform);
            }
        }

        void OnStopMove(){
            stopMannually = true;
            GameObject.Destroy(updateMono);
            if(speedUpFxObj != null){
                GameObject.Destroy(speedUpFxObj);
                speedUpFxObj = null;
            }
            if(rigidbody != null){
                if(rigidbody.GetComponent<RGController>() is {} controller){
                    controller.onStopMove -= OnStopMove;
                }
                else if(rigidbody.GetComponent<RGEController>() is {} eController){
                    eController.onStopMove -= OnStopMove;
                }
            }
            finishedCallback?.Invoke();
        }


        if(rigidbody.GetComponent<RGController>() is {} controller){
            controller.onStopMove += OnStopMove;
        }
        else if(rigidbody.GetComponent<RGEController>() is {} eController){
            eController.onStopMove += OnStopMove;
        }

        return updateMono;
    }
}
