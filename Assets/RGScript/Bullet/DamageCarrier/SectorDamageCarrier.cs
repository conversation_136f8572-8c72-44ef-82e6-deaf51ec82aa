using Sirenix.OdinInspector;
using System;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

/// <summary>
/// 判断扇形区域
/// </summary>
public class SectorDamageCarrier : AreaDamageCarrier {
    [LabelText("使用曲线控制大小")]
    public bool useSizeCurve = false;

    [ShowIf("useSizeCurve"), LabelText("半径曲线")]
    public AnimationCurve sizeCurve;

    protected float _curveSize = 1;

    [LabelText("半径")]
    public float radius = 5;

    private bool flag;

    [NonSerialized]
    public Vector2 dir = new Vector2(1, 0);

    private int layerMask;

    /// <summary>
    /// 扇形总角度
    /// </summary>
    public float angle = 60;

    protected RaycastHit2D[] _cachedHitResult;

    protected CircleCollider2D _collider;
    public Action<GameObject> onDamage;

    public override void OnTaken() {
        base.OnTaken();
        _collider = GetComponentInChildren<CircleCollider2D>();
    }

    protected override void OnDisable() {
        base.OnDisable();
    }

    public override void UpdateInfo(BulletInfo bulletInfo, DamageInfo damageInfo) {
        base.UpdateInfo(bulletInfo, damageInfo);
        if (_collider && _collider.GetComponent<CommonBulletRebound>() != null) {
            _collider.isTrigger = true;
            _collider.radius = bulletInfo.size;
        }
    }

    public void SetData(Vector2 dir, float angle, float radius, float damageInterval, float duration,
        Action<GameObject> onDamage,
        int layerMask) {
        this.dir = dir;
        this.angle = angle;
        this.radius = radius;
        this.damageInterval = damageInterval;
        this.duration = duration;
        this.onDamage = onDamage;
        this.layerMask = layerMask;
    }

    public void UpdateDir(Vector2 dir) {
        this.dir = dir;
    }

    protected override void DealDamage() {
        if (_cachedHitResult == null)
            _cachedHitResult = new RaycastHit2D[16];

        var tf = transform;
        var tfPos = tf.position;
        var hitCount = Physics2D.CircleCastNonAlloc(tfPos, radius, dir, _cachedHitResult, 0, layerMask);
        bool hasHitTarget = false;
        for (var i = 0; i < hitCount; ++i) {
            var hitResult = _cachedHitResult[i];
            Transform hitResultTransform = hitResult.transform;
            flag = InAngleArea(transform, hitResultTransform, dir, angle / 2, radius);
#if UNITY_EDITOR
            if (flag) {
                Debug.DrawLine(transform.position, hitResultTransform.position, Color.green); //画出技能释放者与目标点的连线
            }
#endif
            if (flag) {
                var hasDealDamage = OnDealDamage(_cachedHitResult[i].collider, _cachedHitResult[i].point);
                hasHitTarget |= hasDealDamage;
                if (hasDealDamage) {
// #if UNITY_EDITOR
//                     Debug.Log("[SectorDamageCarrier] OnDealDamage success");
// #endif
                    onDamage?.Invoke(hitResultTransform.gameObject);
                }
            }
        }

        OnDealDamageOver(hasHitTarget);
    }

#if UNITY_EDITOR

    private void OnDrawGizmos() {
        Handles.color = flag ? Color.cyan : Color.red;

        var sourceDir = dir;
        var source = transform;
        var seekDistance = radius;
        Vector2 lockDir = sourceDir;
        var position = source.position;
        Vector2 paEnd = (lockDir * seekDistance);
        Vector2 pb, pc;
        float angleInRad = angle / 2 * Mathf.Deg2Rad;

        float angleSinPb = Mathf.Sin(angleInRad);
        float angleCosPb = Mathf.Cos(angleInRad);

        float angleSinPc = Mathf.Sin(-angleInRad);
        float angleCosPc = Mathf.Cos(-angleInRad);

        pb.x = paEnd.x * angleCosPb - paEnd.y * angleSinPb;
        pb.y = paEnd.x * angleSinPb + paEnd.y * angleCosPb;

        pc.x = paEnd.x * angleCosPc - paEnd.y * angleSinPc;
        pc.y = paEnd.x * angleSinPc + paEnd.y * angleCosPc;

        pb = (Vector2)position + pb;
        pc = (Vector2)position + pc;
        var pa = transform.position.Vec2();
        Handles.DrawLine(pa, pb);
        Handles.DrawLine(pc, pb);
        Handles.DrawLine(pa, pc);
        Handles.DrawLine(position, position + 5 * (Vector3)dir);
    }
#endif


    private bool InAngleArea(Transform source, Transform target, Vector2 sourceDir, float angle, float seekDistance) {
        if (target == null) {
            return false;
        }

        Vector2 targetPos;
        ILockableObject lockableObject = target.GetComponent<ILockableObject>();
        if (lockableObject != null) {
            targetPos = lockableObject.GetColliderCenter();
        } else {
            targetPos = target.transform.position;
        }

        Vector2[] points = GetAngleAreaPoint(source.position, sourceDir, angle, seekDistance);

        return DamageCarrierHelper.IsInTriangle(targetPos, points[0], points[1], points[2]);
    }

    public static Vector2[] GetAngleAreaPoint(Vector2 sourcePos, Vector2 sourceDir, float angle, float seekDistance) {
        Vector2 lockDir = sourceDir;
        var position = sourcePos;
        Vector2 paEnd = (lockDir * seekDistance);
        Vector2 pb, pc;
        float angleInRad = angle * Mathf.Deg2Rad;

        float angleSinPb = Mathf.Sin(angleInRad);
        float angleCosPb = Mathf.Cos(angleInRad);

        float angleSinPc = Mathf.Sin(-angleInRad);
        float angleCosPc = Mathf.Cos(-angleInRad);

        pb.x = paEnd.x * angleCosPb - paEnd.y * angleSinPb;
        pb.y = paEnd.x * angleSinPb + paEnd.y * angleCosPb;

        pc.x = paEnd.x * angleCosPc - paEnd.y * angleSinPc;
        pc.y = paEnd.x * angleSinPc + paEnd.y * angleCosPc;

        pb = position + pb;
        pc = position + pc;
        Vector2[] points = { position, pb, pc };
        return points;
    }
}