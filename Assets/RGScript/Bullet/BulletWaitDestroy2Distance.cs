using System.Collections;
using UnityEngine;

/// <summary>
/// 抵达运动距离后停止运动，等待时间后销毁
/// </summary>
public class BulletWaitDestroy2Distance : BulletDestroyCallback {
    public float waitDestroy;

    protected override IEnumerator Destroying(float totalTime) {
        if (!destroyByDistance) {
            base.Destroying(totalTime);
            yield break;
        }

        float moveDistance = 0;
        var wait = new WaitForFixedUpdate();
        var startTime = Time.time;
        while (Time.time - startTime <= totalTime && moveDistance < destroyDistance) {
            moveDistance = Vector2.Distance(bulletInfo.createPosition, rigid2d.position);
            yield return wait;
        }

        rigid2d.velocity = Vector2.zero;
        yield return new WaitForSeconds(waitDestroy);
        DoDestroy();
    }
}
