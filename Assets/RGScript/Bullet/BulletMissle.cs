using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BulletMissle : RGBullet {
    internal float verticleSpeed;
    internal float launchTime;
    float verticleAcceleration;
    float _verticleSpeed;
    float _horizontalSpeed;
    bool missleLauch;

    public override void UpdateAttribute(GameObject source_object, int damage, float the_speed, bool can_through, int repel, int camp) {
        base.UpdateAttribute(source_object, damage, the_speed, can_through, repel, camp);
        missleLauch = false;
    }
    public override void UpdateInfo(BulletInfo bulletInfo, DamageInfo damageInfo) {
        base.UpdateInfo(bulletInfo, damageInfo);
        missleLauch = false;
    }
    public void SetMissleSpeed(float verticleSpeed, float launchTime) {
        this.verticleSpeed = verticleSpeed;
        this.launchTime = launchTime;
        verticleAcceleration = -verticleSpeed / launchTime;
        missleLauch = true;
    }

    public override void OnTaken() {
        base.OnTaken();
        if (missleLauch) {
            if (awake) {
                _verticleSpeed = verticleSpeed;
                _horizontalSpeed = 0;
                rigid2d.velocity = initial_velocity + (Vector2)transform.right * _horizontalSpeed + (Vector2)transform.up * _verticleSpeed;
                if (rotate_angle != 0) {
                    float scale = Mathf.Abs(transform.GetChild(0).localScale.x);
                    transform.GetChild(0).localScale = new Vector3(scale * (rigid2d.velocity.x >= 0 ? 1 : -1), scale, 1);
                }
                StartCoroutine(AdjustingSpeed());
            }
        }
    }

    protected bool updateVelocity = true;
    IEnumerator AdjustingSpeed() {
        do {
            updateVelocity = false;
            if (awake) {
                if (Mathf.Abs(_verticleSpeed) > 0) {
                    if (_verticleSpeed * (_verticleSpeed + verticleAcceleration * Time.deltaTime) > 0) {
                        _verticleSpeed += verticleAcceleration * Time.deltaTime;
                    } else {
                        _verticleSpeed = 0;
                    }
                    updateVelocity = true;
                }
                if (_horizontalSpeed < speed) {
                    _horizontalSpeed = Mathf.Min(speed, _horizontalSpeed += Time.deltaTime * speed / launchTime);
                    updateVelocity = true;
                }
                if (updateVelocity) {
                    float tempHorizontalSpeed = Mathf.Max(0, _horizontalSpeed - Mathf.Abs(_verticleSpeed) / 2f);
                    rigid2d.velocity = initial_velocity + (Vector2)transform.right * tempHorizontalSpeed + (Vector2)transform.up * _verticleSpeed;
                }
            }
            yield return null;
        } while (updateVelocity && this && gameObject);
    }


}
