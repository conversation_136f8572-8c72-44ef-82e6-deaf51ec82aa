
using UnityEngine;

/// <summary>
/// 通用的敌对子弹反弹器
/// </summary>
public class CommonBulletRebound : MonoBehaviour, IBulletRebound, IBulletEliminator {
    [Header("是否有效")]
    public bool isRun = true;
    [Header("是否反弹,否则消除")]
    public bool rebound = true;
    
    public int fromCamp;
    public int camp { get => fromCamp; }

    public System.Action<RGBullet> onRebound;
    public System.Action<RGBullet> onEliminate;

    public bool CanRebound(RGBullet bullet) {
        RefreshCamp();
        return isRun && bullet.IsEnemy(fromCamp);
    }

    public void OnRebound(RGBullet bullet) {
        if(onRebound != null){
            onRebound(bullet);
        }
    }

    public bool CanEliminate(RGBullet bullet) {
        RefreshCamp();
        return isRun&&!rebound && bullet.IsEnemy(fromCamp);
    }
    public void OnEliminate(RGBullet bullet){
        if(onEliminate != null){
            onEliminate(bullet);
        }
    }

    void RefreshCamp(){
        if(transform.GetComponent<DamageCarrier>() is {} damageCarrier){
            fromCamp = damageCarrier.camp;
        }
        else if(transform.parent != null && transform.parent.GetComponentInParent<DamageCarrier>() is {} parentDamageCarrier){
            fromCamp = parentDamageCarrier.camp;
        }
    }
}
