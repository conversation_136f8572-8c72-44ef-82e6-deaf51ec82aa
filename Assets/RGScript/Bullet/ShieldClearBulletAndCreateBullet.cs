using RGScript.Weapon.WeaponStateBehavior;
using UnityEngine;

namespace RGScript.Bullet {
    public class ShieldClearBulletAndCreateBullet : MonoBehaviour {
        public GunJinGuBang weapon;

        public void OnTriggerEnter2D(Collider2D other) {
            var damageTrigger = other.GetComponent<DamageTrigger>();
            if (damageTrigger != null && damageTrigger.camp == 0) {
                weapon.CreateReflectBullet(other.transform);
                PrefabPool.Inst.Store(other.gameObject);
            }
        }
    }
}