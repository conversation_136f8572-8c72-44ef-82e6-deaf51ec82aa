using DG.Tweening;
using UnityEngine;
using System.Collections;

public class VioletGas : MonoBeh<PERSON>our, OffensiveInterface, IPrefabPoolObject
{
    protected Animator anim;
    bool can_hit;
    bool is_end = false;
    public bool hit_player = true;
    public bool hit_enemy = true;

    public float duration = 6;
    public GameObject buff;
    GameObject source_object;
    RaycastHit2D[] hit_list = new RaycastHit2D[8];
    // Use this for initialization
    void Start() {
        anim = gameObject.GetComponent<Animator>();
    }
    private GameObject enemyCircle;
    public void OnTaken() {
        can_hit = true;
        is_end = false;
        CancelInvoke("GasEnd");
        CancelInvoke("TurnCanHit");
    }
    void FixedUpdate() {
        if (can_hit && !is_end) {
            CreateAlertCircle();
            can_hit = false;
            if (hit_enemy) {
                int hitCount = Physics2D.CircleCastNonAlloc(transform.position, 3, new Vector2(0, 0), hit_list, 0f,
                    1 << LayerMask.NameToLayer("Body_E"));
                for (int i = 0; i < hitCount; i++) {
                    if (hit_list[i].transform.GetComponent<BossAI05>() != null) {
                        hit_list[i].transform.GetComponent<BossAI05>().TurnInvisible();
                    }
                }
            }

            if (hit_player) {
                int hitCount = Physics2D.CircleCastNonAlloc(transform.position, 3, new Vector2(0, 0), hit_list, 0f,
                    1 << LayerMask.NameToLayer("Body_P"));
                for (int i = 0; i < hitCount; i++) {
                    var ctrl = hit_list[i].transform.GetComponent<RGBaseController>();
                    var controller = ctrl as RGController;
                    bool ctrlValid = controller != null || ctrl is TroopMercenary;
                    var immuneGas = controller != null && controller.TryHandleIfImmuneGas(false);
                    if (ctrlValid && !immuneGas && !hit_list[i].transform.Find(buff.name)) {
                        GameObject temp_buff =
                            Instantiate(buff, hit_list[i].transform.position, Quaternion.identity) as GameObject;
                        temp_buff.name = buff.name;
                        temp_buff.GetComponent<RGBuff>().is_enemy = false;
                        temp_buff.GetComponent<OffensiveInterface>().SetSourceObject(source_object);
                        temp_buff.transform.parent = hit_list[i].transform;
                    }
                }
            }
        }
    }
    public void SetSourceObject(GameObject value) {
        source_object = value;
        TurnCanHit();
        Invoke("GasEnd", duration);
        DestroySelf(duration + 1);
    }

    void CreateAlertCircle(){
        if (enemyCircle == null) {
            enemyCircle = gameObject.CreateHitWarnCircle();
        }

        if (enemyCircle != null) {
            enemyCircle.SetActive(hit_player);
            enemyCircle.GetComponent<SpriteRenderer>().color = GasUtil.WarnColor;
        }
    }

    public GameObject GetSourceObject() {
        return source_object;
    }
    void TurnCanHit() {
        can_hit = true;
        Invoke("TurnCanHit", 0.5f);
    }
    void GasEnd() {
        is_end = true;
        anim.SetTrigger("end");
        if (enemyCircle != null) {
            enemyCircle.GetComponent<SpriteRenderer>().DOColor(Color.clear, 1);
        }
    }
    public void DestroySelf(float totalTime) {
        StartCoroutine("Destroying", totalTime);
    }
    protected virtual IEnumerator Destroying(float totalTime) {
        yield return new WaitForSeconds(totalTime);
        PrefabPool.Inst.Store(gameObject);
    }
}
