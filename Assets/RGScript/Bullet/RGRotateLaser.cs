using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class RGRotateLaser : RGShortLaser {
    Animator anim;
    bool setup;
    private AudioSource _audioSource;
    protected override void Awake() {
        base.Awake();
        anim = GetComponent<Animator>();
        _audioSource = GetComponent<AudioSource>();
    }

    public void OnTaken() {
        UpdateShape();
    }
    public override void UpdateInfo(BulletInfo bulletInfo, DamageInfo damageInfo) {
        base.UpdateInfo(bulletInfo, damageInfo);
        UpdateShape(true);
        setup = true;
    }
    protected void Update() {
        UpdateShape();
        _audioSource.enabled = RGMusicManager.GetInstance().open_effect;
    }
    void UpdateShape(bool forceUpdate = false) {
        if (setup && (forceUpdate || anim.GetCurrentAnimatorStateInfo(0).IsName("b_laser"))) {
            RaycastHit2D hit = IsEnemyBullet ? Physics2D.Raycast(transform.position, transform.TransformDirection(Vector2.right), 50f, LayerMask.GetMask(enemyMask)) :
                Physics2D.Raycast(transform.position, transform.TransformDirection(Vector2.right), 50f, PhysicsUtil.StaticWallMask);
            if (hit.collider != null) {
                //Debug.DrawLine (transform.position,hit.point, Color.red);
                float distance = Vector2.Distance(hit.point, (Vector2)transform.position);
                bullet.localScale = new Vector3(distance, bulletInfo.size == 0 ? 1 : bulletInfo.size, 1);
                end.localPosition = new Vector3(distance, 0, 0);
            } else {
                bullet.localScale = new Vector3(30, bulletInfo.size == 0 ? 1 : bulletInfo.size, 1);
                end.localPosition = new Vector3(30, 0, 0);
            }
            bullet.GetComponent<RGShortLaserTrigger>().colliderSize = bullet.localScale;
        }
    }


    public void EndLaser() {
        if (anim.GetBool("atk")) {
            anim.SetBool("atk", false);
        }
    }
    public void DestoryLaser() {
        PrefabPool.Inst.Store(gameObject);
    }

}
