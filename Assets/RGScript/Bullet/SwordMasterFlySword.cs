using DG.Tweening;
using MycroftToolkit.DiscreteGridToolkit;
using RGScript.Character.Player;
using Sirenix.OdinInspector;
using System.Collections;
using UnityEngine;

//剑宗飞剑
public class SwordMasterFlySword : RGBullet {

    public TrailRenderer[] trailRenderers;

    public AudioClip audioClip;

    //自定义运动轨迹速度
    public float startSpeed = 35;

    private float endSpeed = 45;

    private float backSpeed = -0.2f;

    private float moveTime = 0.2f;

    private float endMoveTime = 0.3f;

    private float rotateTime = 0.18f;

    private float backTime = 0.3f;

    public bool isReverseOnWait;

    private float lookOffsetY = 0.3f;

    [Header("runtime data")]
    public bool isEnd;

    public Vector3 centerPos;

    private Collider2D _col;

    private Animator _animator;

    public C29Controller controller;

    internal bool isPlayClip;


    public override void OnTaken() {
        manual_destroy = true;
        dont_destroy = true;
        isEnd = false;
        if (awake) {
            ClearTrails();
            rigid2d.velocity = Vector2.zero;
            StopDestroy();
            bulletMover = null;
            StopCoroutine(nameof(BulletMove));
            StartCoroutine(nameof(BulletMove));
            _col = b.GetComponent<Collider2D>();
            _animator = GetComponent<Animator>();
        }
    }

    private void ClearTrails() {
        foreach (var trailRenderer in trailRenderers) {
            trailRenderer.Clear();
        }
    }

    public override void UpdateInfo(BulletInfo bulletInfo, DamageInfo damageInfo) {
        base.UpdateInfo(bulletInfo, damageInfo);
        if (b != null) {
            var dmgTrigger = b.GetComponent<DamageTrigger>();
            if (dmgTrigger != null) {
                dmgTrigger.SetInfo(this.damageInfo);
            }
        }
    }

    public void SetSwordBack() {
        isEnd = true;
    }


    IEnumerator BulletMove() {
        yield return new WaitForEndOfFrame();
        //直线
        yield return Takeoff();
        //停留
        yield return Waitting();
        //瞄准索敌攻击
        yield return BackAttack();
    }



    IEnumerator Takeoff() {
        var wait = new WaitForFixedUpdate();
        //最大时间 或者 碰墙上
        rigid2d.velocity = transform.TransformDirection(Vector2.right) * startSpeed;
        float timer = 0;

        float curMoveTime = isEnd ? endMoveTime : moveTime;

        while (timer < curMoveTime) {
            yield return wait;
            timer += Time.fixedDeltaTime;
        }
        if (!isEnd && _animator != null) {
            _animator.SetBool("isWait", true);
        }
    }

    IEnumerator Waitting() {
        var wait = new WaitForFixedUpdate();

        _col.enabled = false;

        var tempSpeed = rigid2d.velocity;

        Quaternion startRotation = transform.rotation;
        float angle = isReverseOnWait ? 90 : -90f;
        Quaternion targetRotation = Quaternion.Euler(new Vector3(0, 0, angle));
        float timer = 0;
        float totalTime = 10f;

        while (timer < totalTime) {
            yield return wait;
            if (isEnd) {
                rigid2d.velocity = Vector2.zero;
                yield break;
            }

            float p = Mathf.Min(1, timer / rotateTime);
            transform.rotation = Quaternion.Lerp(startRotation, targetRotation, p);
            rigid2d.velocity = Vector2.Lerp(tempSpeed, Vector2.zero, p);
            timer += Time.fixedDeltaTime;
        }
    }

    void DestorySelf() {
        CallDestroyCallback();
        PrefabPool.Inst.Store(gameObject);
    }

    IEnumerator BackAttack() {
        if (_animator != null) {
            _animator.SetBool("isWait", false);
        }

        var wait = new WaitForFixedUpdate();

        //搜索范围内敌人,没有则搜索玩家敌人
        Transform findEnemy = PhysicsUtil.FindNearestAliveByPoint(centerPos, 6, LayerMask.GetMask("Body_E"));

        if (null == findEnemy) {
            findEnemy = controller.target_obj;
        }

        Vector3 lookTo = centerPos;
        if (findEnemy) {
            lookTo = findEnemy.transform.position + new Vector3(0, lookOffsetY, 0);
        }

        //瞄准并后退
        Quaternion startRotation = transform.rotation;


        float timer = 0;
        float totalTime = backTime;

        _col.enabled = true;
        while (timer < totalTime) {
            yield return wait;
            timer += Time.fixedDeltaTime;
            float p = Mathf.Min(1, 2 * timer / totalTime);
            Vector3 dir = (lookTo - transform.position).normalized;
            Quaternion targetRotation = Quaternion.Euler(new Vector3(0, 0, dir.DirToAngle()));
            transform.rotation = Quaternion.Lerp(startRotation, targetRotation, p);
            transform.position += transform.right * backSpeed;
        }
        if (isPlayClip) {
            RGMusicManager.Inst.PlayEffect(audioClip);
        }

        rigid2d.velocity = transform.TransformDirection(Vector2.right) * endSpeed;
        speed = endSpeed;
        bulletMover = new BulletMover();

        yield return new WaitForSeconds(2f);
        DestorySelf();
    }

#if UNITY_EDITOR
    [Button("查找子物体拖尾")]
    private void AutoFindTails() {
        var trs = transform.GetComponentsInChildren<TrailRenderer>(true);
        trailRenderers = trs;
    }

#endif

}