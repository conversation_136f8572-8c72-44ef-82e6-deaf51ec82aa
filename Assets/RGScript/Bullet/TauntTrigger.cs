using System.Collections.Generic;
using UnityEngine;

//只针对敌人的嘲讽效果
public class TauntTrigger : MonoBehaviour {
    private Transform _target;
    public Transform Target {
        get {
            if (_target == null) {
                var controller = GetComponentInParent<RGBaseController>();
                if (controller != null)
                    _target = controller.transform;
            }
            if (_target == null) {
                _target = transform;
            }
            return _target;
        }
    }
    public Collider2D col;
    private Collider2D Collider {
        get {
            if (col == null) {
                col = GetComponent<Collider2D>();
            }
            return col;
        }
    }

    public bool startOnAwake = true;
    public float duration = -1;
    private List<RGEController> _enemies = new List<RGEController>(); 
   

    private void Start() {
        if (startOnAwake) {
            StartTaunt();
        } else {
            Collider.enabled = false;
        }
    }

    public void StartTaunt() {
        Collider.enabled = true;
        if (duration > 0) {
            Invoke(nameof(StopTaunt), duration);
        }
    }

    public void StopTaunt() {
        CancelInvoke(nameof(StopTaunt));
        Collider.enabled = false;
        foreach (var enemy in _enemies) {
            if (enemy != null) {
                enemy.FixedTarget = null;
            }
        }
        _enemies.Clear();
    }
   
    public void SetTarget(Transform target) {
        _target = target;
    }
    
    private void OnTriggerEnter2D(Collider2D other) {
        if (other.TryGetComponent<RGEController>(out var enemy)) {
            if (enemy.awake) {
                enemy.FixedTarget = Target;
                _enemies.Add(enemy);
            }
        }
    }

    private void OnTriggerExit2D(Collider2D other) {
        if (other.TryGetComponent<RGEController>(out var enemy)) {
            enemy.FixedTarget = null;
            _enemies.Remove(enemy);
        }
    }

    private void OnDisable() {
        StopTaunt();
    }
}
