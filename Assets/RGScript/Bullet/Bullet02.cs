using UnityEngine;
using Sirenix.OdinInspector;

/// <summary>
/// 跟踪
/// </summary>
public class Bullet02 : RGBullet {
    public bool needFollowSourceObj;
    [LabelText("检测间隔")]
    public float interval = 0.1f;
    public float delay_time;
    [LabelText("检测次数限制")]
    public bool has_limit;
    [ShowIf("has_limit")]
    [LabelText("检测次数")]
    public int limit_time = 24;
    protected Transform TargetTf;
    protected bool HasTarget = false;
    public int angle_speed = 15;
    internal bool tracking;
    public GameObject custom_target;
    private TrailRenderer _trail;

    protected override void Awake() {
        base.Awake();
        _trail = GetComponent<TrailRenderer>();
    }

    protected override void Start() {
        OnTaken();
    }

    public override void OnTaken() {
        base.OnTaken();
        if (_trail) {
            _trail.Clear();
        }
        tracking = false;
    }
    
    protected override void CreateBulletMover() {
        float bulletAngle = transform.eulerAngles.z;
        bulletMover = new BulletMoverFollow();
        (bulletMover as BulletMoverFollow)?.Setup(bulletAngle, angle_speed, 0, interval, delay_time, has_limit ? limit_time : 0);
        bulletMover.needFollowSourceObj = needFollowSourceObj;
        if (custom_target) {
            (bulletMover as BulletMoverFollow)?.SetTarget(custom_target.transform);
        }
    }

    public override void SetAwakeTrue() {
        awake = true;
        rigid2d.isKinematic = false;
        var boxCollider2D = transform.GetChild(0).GetComponent<BoxCollider2D>();
        if (boxCollider2D) boxCollider2D.enabled = true;
        rigid2d.velocity = transform.TransformDirection(Vector2.right) * speed;
        OnTaken();
    }

    public void UpdateAngleSpeed(float changeRate) {
        if (bulletMover != null && bulletMover is BulletMoverFollow) {
            (bulletMover as BulletMoverFollow).UpdateAngleSpeed(changeRate);
        }
    }
}
