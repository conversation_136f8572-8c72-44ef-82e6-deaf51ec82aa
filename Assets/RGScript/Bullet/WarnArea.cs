using DG.Tweening;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class WarnArea : MonoBehaviour {
    [NonSerialized] public float Angle;
    [NonSerialized] public Vector2 Size;
    private static readonly int HorizontalSpeed = Shader.PropertyToID("_HorizontalSpeed");
    private static readonly int Tex2D = Shader.PropertyToID("_Tex2D");
    public static readonly int LengthAmount = Shader.PropertyToID("_LengthAmount");
    public static readonly int WidthAmount = Shader.PropertyToID("_WidthAmount");
    public static readonly int CornerAmount = Shader.PropertyToID("_CornerAmount");
    private static readonly int OuterRadius = Shader.PropertyToID("_OuterRadius");
    private static readonly int InnerRadius = Shader.PropertyToID("_InnerRadius");

    private bool _close;
    public void Close() {
        if (_close) {
            return;
        }

        _close = true;
        GetComponent<SpriteRenderer>().DOColor(Color.clear, .25f).OnComplete(() => Destroy(gameObject));
        foreach (var sr in GetComponentsInChildren<SpriteRenderer>()) {
            sr.DOColor(Color.clear, .25f);
        }
    }

    public WarnArea BoxDirectionWarn(float speed = 4) {
        var o = Instantiate(ResourcesUtil.Load<GameObject>("RGPrefab/Bullet/Effect/warn_box_dir.prefab"), transform);
        o.transform.localPosition = Vector3.zero;
        o.transform.localRotation = Quaternion.identity;
        o.transform.localScale = Vector3.one;
        var length = Mathf.Max(Size.x, Size.y);
        var sr = o.GetComponent<SpriteRenderer>();
        sr.material.SetTexture(Tex2D, sr.sprite.texture);
        sr.material.SetFloat(LengthAmount, Size.x / length);
        sr.material.SetFloat(WidthAmount, Size.y / length);
        sr.material.SetFloat(CornerAmount, Mathf.Min(Size.x, Size.y) / length * .05f);
        sr.material.SetFloat(HorizontalSpeed, -speed);
        return this;
    }

    public static WarnArea Ray(Vector2 startPos, float angle, int masks = 0, float duration = -1) {
        var ray = Instantiate(ResourcesUtil.Load<GameObject>("RGPrefab/Bullet/Effect/warn_ray.prefab"),
            RGGameSceneManager.Inst.temp_objects_parent);
        ray.transform.position = startPos;
        ray.transform.rotation = Quaternion.Euler(0, 0, angle);
        var dir = ray.transform.TransformDirection(Vector2.right);
        var length = 50f;
        RaycastHit2D hit = Physics2D.Raycast(startPos, dir, length, masks);
        if (hit.collider != null) {
            length = Vector2.Distance(hit.point, startPos);
        }

        ray.transform.localScale = new Vector3(length, length, 1);

        var wa = ray.GetComponent<WarnArea>();
        wa.Angle = angle;

        var sr = ray.GetComponent<SpriteRenderer>();
        sr.material.SetFloat(LengthAmount, length);
        sr.material.SetFloat(WidthAmount, .1f / length);
        sr.material.SetFloat(CornerAmount, .05f / length);

        sr.color = Color.clear;
        sr.DOColor(Color.red.Alpha(.5f), .2f);
        if (duration > 0) {
            wa.Invoke(nameof(Close), duration);
        }

        return wa;
    }

    public static WarnArea Box(Vector2 center, float angle, Vector2 size, float duration = -1) {
        var box = Instantiate(ResourcesUtil.Load<GameObject>("RGPrefab/Bullet/Effect/warn_box.prefab"),
            RGGameSceneManager.Inst.temp_objects_parent);
        box.transform.position = center;
        box.transform.rotation = Quaternion.Euler(0, 0, angle);
        var length = Mathf.Max(size.x, size.y);
        box.transform.localScale = new Vector3(length, length, 1);

        var wa = box.GetComponent<WarnArea>();
        wa.Angle = angle;
        wa.Size = size;

        var sr = box.GetComponent<SpriteRenderer>();
        sr.material.SetTexture(Tex2D, sr.sprite.texture);
        sr.material.SetFloat(LengthAmount, size.x / length);
        sr.material.SetFloat(WidthAmount, size.y / length);
        sr.material.SetFloat(CornerAmount, Mathf.Min(size.x, size.y) / length * .05f);

        sr.color = Color.clear;
        sr.DOColor(Color.red.Alpha(.35f), .2f);
        if (duration > 0) {
            wa.Invoke(nameof(Close), duration);
        }

        return wa;
    }

    public static WarnArea Circle(Vector2 center, float radius, float duration) {
        var scale = radius * 2;
        var circle = Instantiate(ResourcesUtil.Load<GameObject>("RGPrefab/Bullet/Effect/warn_circle.prefab"),
            RGGameSceneManager.Inst.temp_objects_parent);
        circle.transform.position = center;
        circle.transform.rotation = Quaternion.identity;
        circle.transform.localScale = new Vector3(scale, scale, 1);

        var wa = circle.GetComponent<WarnArea>();
        wa.Size = new Vector2(scale, scale);

        var sr = circle.GetComponent<SpriteRenderer>();
        sr.material.SetTexture(Tex2D, sr.sprite.texture);

        circle.transform.Find("spread").DOScale(Vector3.one, duration).SetEase(Ease.Linear);
        if (duration > 0) {
            wa.Invoke(nameof(Close), duration);
        }

        return wa;
    }

    public static WarnArea BoxDirSpread(Vector2 center, float angle, Vector2 size, float duration) {
        var box = Instantiate(ResourcesUtil.Load<GameObject>("RGPrefab/Bullet/Effect/warn_box_spread.prefab"),
            RGGameSceneManager.Inst.temp_objects_parent);
        box.transform.position = center;
        box.transform.rotation = Quaternion.Euler(0, 0, angle);
        var length = Mathf.Max(size.x, size.y);
        box.transform.localScale = new Vector3(length, length, 1);

        var wa = box.GetComponent<WarnArea>();
        wa.Angle = angle;
        wa.Size = size;

        var sr = box.GetComponent<SpriteRenderer>();
        sr.material.SetTexture(Tex2D, sr.sprite.texture);
        sr.material.SetFloat(LengthAmount, size.x / length);
        sr.material.SetFloat(WidthAmount, size.y / length);
        sr.material.SetFloat(CornerAmount, Mathf.Min(size.x, size.y) / length * .05f);

        var spreadSr = box.transform.Find("spread").GetComponent<SpriteRenderer>();
        spreadSr.material.SetTexture(Tex2D, spreadSr.sprite.texture);
        spreadSr.material.SetFloat(LengthAmount, size.x / length);
        spreadSr.material.SetFloat(WidthAmount, size.y / length);
        spreadSr.material.SetFloat(CornerAmount, Mathf.Min(size.x, size.y) / length * .05f);
        spreadSr.size = new Vector2(0, spreadSr.size.y);
        DoBoxSpread(spreadSr, 1, duration);
        if (duration > 0) {
            wa.Invoke(nameof(Close), duration);
        }
        return wa;
    }

    private static void DoBoxSpread(SpriteRenderer value, float endValue, float duration) {
        DOTween.To(() => value.size.x, x => value.size = new Vector2(x, value.size.y), endValue, duration).SetEase(Ease.Linear);
    }

    public static WarnArea Ring(Vector2 center, float outerRadius, float innerRadius, float duration) {
        var scale = outerRadius * 2;
        var outer = 0.5f;
        var inner = outer * (innerRadius / outerRadius);
        var ring = Instantiate(ResourcesUtil.Load<GameObject>("RGPrefab/Bullet/Effect/warn_ring.prefab"),
            RGGameSceneManager.Inst.temp_objects_parent);
        ring.transform.position = center;
        ring.transform.rotation = Quaternion.identity;
        ring.transform.localScale = new Vector3(scale, scale, 1);

        var wa = ring.GetComponent<WarnArea>();
        wa.Size = new Vector2(scale, scale);

        var sr = ring.GetComponent<SpriteRenderer>();
        sr.material.SetTexture(Tex2D, sr.sprite.texture);
        sr.material.SetFloat(InnerRadius, inner);
        sr.material.SetFloat(OuterRadius, outer);
        
        
        ring.transform.Find("spread").DOScale(Vector3.one, duration).SetEase(Ease.Linear);
        var spread = ring.transform.Find("spread").GetComponent<SpriteRenderer>().material;
        spread.SetFloat(InnerRadius, inner);
        spread.SetFloat(OuterRadius, inner);

        DOTween.To(() => spread.GetFloat(OuterRadius), x => spread.SetFloat(OuterRadius, x), outer, duration);
        if (duration > 0) {
            wa.Invoke(nameof(Close), duration);
        }

        return wa;
    }
}