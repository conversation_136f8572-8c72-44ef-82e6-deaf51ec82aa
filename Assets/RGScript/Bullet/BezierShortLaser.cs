using System;
using System.Collections.Generic;
using Sirenix.OdinInspector;
using System.Collections;
using UnityEngine;
using Random = UnityEngine.Random;
using UnityRandom = UnityEngine.Random;

public class BezierShortLaser : DamageCarrier, IPrefabPoolObject {
    // private RGRandom _random;
    [Title("LineRenderer上每一段贝塞尔曲线的点的数量")] public int resolution = 50;
    public GameObject head;
    public GameObject tail;
    private SpriteRenderer headSpriteRenderer, tailSpriteRenderer, headLight, tailLight;
    private Material lineRendererMat, lineRendererLightMat;
    private int lineRendererTexId, linRendererLightColorId;
    public float fadeInTime = .3f;
    public float idleTime = 1f;
    public float fadeOutTime = .3f;
    public float idlePeriod = 0.3f;
    public float idleScaleRange = 0.2f;
    [Range(0, 1f)] public float pointRange = 0.8f;
    [Range(0, 1f)] public float lerpValue = 0.2f;

    public event Action OnHurtTarget;

    // public List<GameObject> TargetList => _chainTargetCaster.TargetList;
    public float customScaleFactor = 1;
    private LineRenderer _lineRenderer;
    private readonly List<GameObject> _prevTargetList = new List<GameObject>();
    private readonly List<Vector3[]> _bezierPointLists = new List<Vector3[]>();
    private Vector3[] _lineBezierPoints;
    private Vector3[] result1;
    private Vector3[] result2;
    [NonSerialized] public BezierLaser.LaserState currentState;
    private float _animationStateStartTime;
    private float _prevHurtTime;
    private float _scaleFactor;
    public GameObject targetObj;
    private BezierPolygonTrigger _polyTrigger;

    public float HurtDuration {
        get => bulletInfo.speed;
        set => bulletInfo.speed = value;
    }

    public Action OnTakeAction;

    private readonly Collider2D[] _hurtBoxColliderBuffer = new Collider2D[16];
    // internal List<RGBulletEffectTrigger> effectTriggers = new List<RGBulletEffectTrigger>();

    private void Awake() {
        _lineRenderer = GetComponent<LineRenderer>();
        _lineBezierPoints = new Vector3[resolution];
        result1 = new Vector3[resolution];
        result2 = new Vector3[resolution];
        _lineRenderer.positionCount = resolution;
        _polyTrigger = GetComponent<BezierPolygonTrigger>();
        headSpriteRenderer = head.GetComponent<SpriteRenderer>();
        tailSpriteRenderer = tail.GetComponent<SpriteRenderer>();
        headLight = head.transform.Find("light").GetComponent<SpriteRenderer>();
        tailLight = tail.transform.Find("light").GetComponent<SpriteRenderer>();
        var lineMaterials = _lineRenderer.sharedMaterials;
        lineRendererLightMat = lineMaterials.Length > 0 ? lineMaterials[0] : null;
        lineRendererMat = lineMaterials.Length > 1 ? lineMaterials[1] : null;

        lineRendererTexId = Shader.PropertyToID("_MainTex");
        linRendererLightColorId = Shader.PropertyToID("_Color");

    }

    public void OnTaken() {
        OnTakeAction?.Invoke();
        var lineMaterials = _lineRenderer.sharedMaterials;
        lineRendererLightMat = lineMaterials.Length > 0 ? lineMaterials[0] : null;
        lineRendererMat = lineMaterials.Length > 1 ? lineMaterials[1] : null;
        DestroySelf(fadeInTime + fadeOutTime + idleTime + 0.1f);
        AnimationStateInit();
        _lineRenderer.widthMultiplier = 0;
        var scale = Vector3.zero;
        head.transform.localScale = scale;
        tail.transform.localScale = scale;
        lastPoint2 = Vector3.zero;
        lastPoint3 = Vector3.zero;
        // effectTriggers = new List<RGBulletEffectTrigger>(GetComponentsInChildren<RGBulletEffectTrigger>());
    }
    
    public void DestroySelf(float totalTime) {
        StartCoroutine("Destroying", totalTime);
    }

    IEnumerator Destroying(float totalTime) {
        yield return new WaitForSeconds(totalTime);
        PrefabPool.Inst.Store(gameObject);
    }

    public override void UpdateInfo(BulletInfo bulletInfo, DamageInfo damageInfo) {
        base.UpdateInfo(bulletInfo, damageInfo);
        _scaleFactor = bulletInfo.size;
        GetComponent<DamageTrigger>().SetInfo(this.damageInfo);
    }

    private Vector3 lastPoint2, lastPoint3;

    private void Update() {
        UpdateLaser();
    }

    public void UpdateLaser() {
        if (currentState == BezierLaser.LaserState.End) {
            return;
        }
        UpdateCurve();
        UpdateAnimation();
    }

    #region 贝塞尔曲线

    /// <summary>
    /// 贝塞尔曲线更新
    /// </summary>
    private void UpdateCurve() {
        if (null != _lineRenderer) {
            _lineRenderer.enabled = false;
        }

        tail.SetActive(false);
        

        _lineRenderer.enabled = true;
        scaleFactor = idleScaleRange + _scaleFactor * customScaleFactor;
        // 贝塞尔点的数量小于目标数乘每一段的点数时，更新点数组
        if (_lineBezierPoints.Length != resolution) {
            _lineBezierPoints = new Vector3[resolution];
            _lineRenderer.positionCount = _lineBezierPoints.Length;
        }

        var point1 = transform.position;
        Vector3 point4 = bulletInfo.sourceWeapon.controller.transform.position;
        if (null != bulletInfo.targetObj) {
            point4 = bulletInfo.targetObj.position;    
        }
        
        var dir = point4 - point1;
        var distance = dir.magnitude;
        var normalizedDir = dir.normalized;
        var dirNormal = new Vector3(-normalizedDir.y, normalizedDir.x, 0);
        var randomDir = UnityRandom.Range(-1f, 1f);
        var randomPos = dirNormal * distance * pointRange;
        var point2 = dir * 0.33f + point1 + randomPos * randomDir;
        randomDir = UnityRandom.Range(-1f, 1f);
        var point3 = dir * 0.67f + point1 - randomPos * randomDir;
        
        if (lastPoint2 == Vector3.zero) {
            lastPoint2 = point2;
        }

        if (lastPoint3 == Vector3.zero) {
            lastPoint3 = point3;
        }
        
        if (lastPoint2 != Vector3.zero) {
            point2 = Vector3.Lerp(lastPoint2, point2, lerpValue);
        }

        if (lastPoint3 != Vector3.zero) {
            point3 = Vector3.Lerp(lastPoint3, point3, lerpValue);
        }

        lastPoint2 = point2;
        lastPoint3 = point3;

        GameUtil.GetBezierCurve(point1, point2, point3, point4, resolution, _lineBezierPoints);
        var linrenderWidth = _lineRenderer.widthMultiplier * 0.5f;
        var offset1 = dirNormal * linrenderWidth;
        var offset2 = -dirNormal * linrenderWidth;
        var transformPos = transform.position;
        Vector3 tmpPos = Vector3.zero;
        for (int i = 0; i < result1.Length; i++) {
            tmpPos = _lineBezierPoints[i] - transformPos;
            result1[i] = tmpPos + offset1;
            result2[i] = tmpPos + offset2;
        }

        _polyTrigger.UpdateCurveCollider(result1, result2);
        tail.transform.position = point4;
        tail.SetActive(true);
        _lineRenderer.SetPositions(_lineBezierPoints);
        // tail.SetActive(false);
    }

    #endregion

    #region 动画

    private void AnimationStateInit() {
        _animationStateStartTime = Time.time;
        currentState = BezierLaser.LaserState.FadeIn;
    }

    private void UpdateAnimation() {
        switch (currentState) {
            case BezierLaser.LaserState.FadeIn:
                FadeInAnimation();
                break;
            case BezierLaser.LaserState.Idle:
                IdleAnimation();
                break;
            case BezierLaser.LaserState.FadeOut:
                FadeOutAnimation();
                break; 
        }
    }

    private void FadeOutAnimation() {
        if (Time.time - _animationStateStartTime > fadeOutTime) {
            currentState = BezierLaser.LaserState.End;
            _animationStateStartTime = Time.time;
            return;
        }

        var progress = (Time.time - _animationStateStartTime) / fadeOutTime;
        var scale = Mathf.Lerp(_scaleFactor * customScaleFactor, 0, progress);
        _lineRenderer.widthMultiplier = scale;
        head.transform.localScale = Vector3.one * scale;
        tail.transform.localScale = Vector3.one * scale;
    }

    private float scaleFactor = 1f;
    private void IdleAnimation() {
        if (Time.time - _animationStateStartTime > idleTime) {
            EndAttack();
            return;
        }
        // scaleFactor = Mathf.Sin(Time.time * Mathf.PI * 2 / idlePeriod) * idleScaleRange + _scaleFactor * customScaleFactor;
        _lineRenderer.widthMultiplier = scaleFactor;
        var scale = Vector3.one * scaleFactor;
        head.transform.localScale = scale;
        tail.transform.localScale = scale;
    }

    private void FadeInAnimation() {
        if (Time.time - _animationStateStartTime > fadeInTime) {
            currentState = BezierLaser.LaserState.Idle;
            _animationStateStartTime = Time.time;
            return;
        }

        var progress = (Time.time - _animationStateStartTime) / fadeInTime;
        var scale = Mathf.Lerp(0, _scaleFactor * customScaleFactor, progress);
        _lineRenderer.widthMultiplier = scale;
        head.transform.localScale = Vector3.one * scale;
        tail.transform.localScale = Vector3.one * scale;
    }


    public void EndAttack() {
        currentState = BezierLaser.LaserState.FadeOut;
        _animationStateStartTime = Time.time;
    }


    public void OnLineRenderSkinChange(Sprite headTailSp, Texture lineTexture, Color lightColor) {
        headSpriteRenderer.sprite = headTailSp;
        tailSpriteRenderer.sprite = headTailSp;
        lineRendererLightMat.SetColor("_Color", lightColor);
        lineRendererMat.SetTexture("_MainTex", lineTexture);
    }


    #endregion
}