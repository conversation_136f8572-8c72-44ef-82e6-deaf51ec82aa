using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class FelFabricBlock : MonoBehaviour, ICustomBulletModifier, IPrefabPoolObject
{
    public float surroundAngleSpeed = 180;
    public float surroundRadius = 3.8f;
    public int blockMaxHp = 10;

    DamageCarrier _damageCarrier;
    int _enemyLayer;
    public int blockHp { get; private set; }

    [HideInInspector] public bool turnToFireBallOnDestroy;
    [HideInInspector] public float damageFactor;
    [HideInInspector] public bool throwFireball;

    public void SetHp(int value) {
        blockHp = value;
        if (blockHp <= 0) {
            gameObject.GetComponent<AreaDamageCarrier>().ImmediatelyFinish();
        }
        else {
            SetBlockCounter(blockHp);
        }
    }

    public DamageCarrier damageCarrier { 
        get{
            if (_damageCarrier == null) {
                _damageCarrier = gameObject.GetComponent<DamageCarrier>();
            }
            return _damageCarrier;
        }
    }

    HashSet<string> _marks = new HashSet<string>();

    public System.Action onHitTarget;

    public void OnTaken() {
        onHitTarget = null;
        turnToFireBallOnDestroy = false;
        damageFactor = 1;
        throwFireball = false;
        _marks.Clear();
        blockHp = blockMaxHp;
        gameObject.GetComponent<AreaDamageCarrier>().onHitTarget = OnBlockHitTarget;
        SetBlockCounter(blockHp);
        //SetBlockMover(null, 0);
    }

    public void Enlarge(int addDamage, float addDamageFactor, int critic, float addBulletSize, string mark) {
        _marks.Add(mark);
        damageCarrier.damageInfo.SetDamage(Mathf.RoundToInt((1 + addDamageFactor) * damageCarrier.damageInfo.damage + addDamage));
        damageCarrier.damageInfo.critic += critic;
        damageCarrier.bulletInfo.AddSubBulletSize(addBulletSize);
        var scale = transform.localScale;
        transform.localScale = new Vector3(Mathf.Sign(scale.x) * (Mathf.Abs(scale.x) + addBulletSize), Mathf.Sign(scale.y) * (Mathf.Abs(scale.y) + addBulletSize));
    }
    public bool IsLargable(string mark) {
        return !_marks.Contains(mark);
    }

    public int BulletCamp => damageCarrier.bulletInfo.camp;

    void SetBlockCounter(int value) {
        var container = transform.GetChild(0).GetChild(0);
        var childCount = container.childCount;
        if (childCount < blockMaxHp) {
            while (childCount < blockMaxHp) {
                var prefab = container.GetChild(0).gameObject;
                GameObject.Instantiate(prefab, prefab.transform.parent);
                ++childCount;
            }
            // rearrange
            for (var i = 0; i < childCount; ++i) {
                var obj = container.GetChild(i);
                obj.transform.localPosition = Quaternion.Euler(0, 0, i * 360f / blockMaxHp + 90) * Vector3.right * 0.45f;
            }
        }

        for (var i = 0; i < container.childCount; ++i) {
            container.GetChild(i).gameObject.SetActive(i < value);
        }
    }

    void OnHitTarget() {
        onHitTarget?.Invoke();
    }

    void OnBlockHitTarget(GameObject target) {
        if (_enemyLayer == 0) {
            _enemyLayer = LayerMask.GetMask("Body_E", "Body_Dead");
        }
        if (target == null || ((1 << target.layer) & _enemyLayer) == 0) {
            return;
        }
        --blockHp;
        OnHitTarget();
        SetBlockCounter(blockHp);

        if (blockHp <= 0) {
            gameObject.GetComponent<AreaDamageCarrier>().ImmediatelyFinish();
        }
    }

    public void SetBlockMover(Transform parent, float initAngleBias) {
        var dmgCarrier = gameObject.GetComponent<AreaDamageCarrier>();
        var targetRadius = surroundRadius;
        float finalSpeedupFactor = 1;
        if (GameUtil.GetBattleData(dmgCarrier).HasBuff(emBuff.MasterSpeedAtk)) {
            finalSpeedupFactor += 0.5f + DataUtil.CalBuffMasterSpeedAtkExtra();
        }

        var angleSpeed = surroundAngleSpeed * finalSpeedupFactor;
        var bulletMover = new BulletMoverSurround();
        dmgCarrier.SetBulletMover(bulletMover, 1);
        bulletMover.Setup(null, Vector3.up * 0.5f, 0, 0.5f);
        bulletMover.Setup(angleSpeed, 5, targetRadius);
        bulletMover.SetParent(parent);
        bulletMover.moveAngle += initAngleBias;
    }
}
