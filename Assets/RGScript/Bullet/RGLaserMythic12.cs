using System;
using UnityEngine;
using System.Collections.Generic;
using UnityEngine.Serialization;

// ReSharper disable MemberCanBePrivate.Global
// ReSharper disable InconsistentNaming

public class RGLaserMythic12 : DamageCarrier, IPrefabPoolObject {
    protected bool setup = false;
    public GameObject hit_object;
    protected Transform bullet;
    protected Transform end;
    protected Transform start;
    protected RGWeapon weapon;
    private MythicWeapon12 _mythic;
    protected Vector2 direction;
    public Animator anim;
    protected float rate = 0.2f;
    protected int damage = 1;
    protected float repel = 3; //击退
    internal float speedFactor = 1;
    public ElementalType elementalType = ElementalType.None;
    internal List<RGBulletEffectTrigger> effectTriggers = new();

    protected override void Awake() {
        bullet = transform.Find("img/bullet");
        start = transform.Find("head");
        end = transform.Find("end");
        anim = transform.GetComponent<Animator>();
    }

    internal float shootDelay = .4f;

    public void OnTaken() {
        CancelInvoke(nameof(ShowCollider));
        if (!RGMusicManager.GetInstance().open_effect)
            transform.GetComponent<AudioSource>().enabled = false;
        Invoke(nameof(ShowCollider), shootDelay / speedFactor);
        effectTriggers = new List<RGBulletEffectTrigger>(GetComponentsInChildren<RGBulletEffectTrigger>());
    }

    protected readonly RaycastHit2D[] hit_list = new RaycastHit2D[16];

    private bool Convert => anim.GetBool(Convert1);
    private float Width => Convert ? 2 : 0.5f;

    protected void ShowCollider() {
        var right = end.position - start.position;
        float angle = Vector2.Angle(Vector2.right, right);
        if (right.y < 0) {
            angle = -angle;
        }

        var scale = Mathf.Min(transform.localScale.x, transform.localScale.y);
        Vector3 tv3 = transform.position + right.normalized * (4 * scale);
        var targetLayer = IsPlayerBullet
            ? 1 << LayerMask.NameToLayer("Body_E")
            : 1 << LayerMask.NameToLayer("Body_P") | 1 << LayerMask.NameToLayer("Body_pet");
        int hitCount = Physics2D.BoxCastNonAlloc(tv3,
            new Vector2(8, Width) * scale,
            angle, Vector2.zero,
            hit_list, 0, (targetLayer | (1 << LayerMask.NameToLayer("Obstacle"))));
        for (int i = 0; i < hitCount; i++) {
            bool ignoreHit = IsContainIgnoreGetHurtGoList(hit_list[i].collider.gameObject);
            if (ignoreHit) continue;

            var currentHitTransform = hit_list[i].transform;
            var box = currentHitTransform.GetComponent<IRGBox>();
            if (box != null) {
                HitBox(box);
            } else if (IsPlayerBullet) {
                HitEnemy(currentHitTransform);
            } else if (currentHitTransform.CompareTag("Body_P")) {
                currentHitTransform.GetComponent<RGController>().GetHurt(new HurtInfo {
                    Critic = false,
                    Damage = damage,
                    DamageType = DamageType.Ranged | DamageType.Aoe,
                    ElementalType = ElementalType.None,
                    Source = bulletInfo.sourceObject,
                    SourceWeapon = bulletInfo.sourceWeapon,
                    FingerPrint = fingerPrint
                });
            }
        }

        if (weapon != null) {
            weapon.MakeConsume();
            weapon.AfterAttackEvent();
        }

        if (anim.GetBool(Atk)) {
            Invoke(nameof(ShowCollider), rate / speedFactor);
        }
    }

    private void HitBox(IRGBox box) {
        if (!box.Hit(damage, bulletInfo.sourceObject, camp)) return;
        GameObject temp_hit = PrefabPool.Inst.Take(hit_object);
        temp_hit.transform.position = box.GetPosition() + new Vector3(0, 0.5f, 0);
        temp_hit.transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;
    }

    private void HitEnemy(Transform currentHitTransform) {
        var hit_enemy = currentHitTransform.GetComponent<RGEController>();
        if (!hit_enemy || hit_enemy.invincible_mode) return;
        hit_enemy.GetForce(direction, RGGameProcess.ProcessRepelForce(damage * repel));

        CaculateLaserDamage(hit_enemy);

        GameObject temp_hit = PrefabPool.Inst.Take(hit_object) as GameObject;
        temp_hit.transform.position = currentHitTransform.position + new Vector3(0, 0.5f, 0);
        temp_hit.transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;
        foreach (var effectTrigger in effectTriggers) {
            effectTrigger.TriggerWith(hit_enemy.GetComponent<Collider2D>(), damage, false, true);
        }

        OnHit(hit_enemy);
    }

    protected void CaculateLaserDamage(RGEController hit_enemy) {
        var hurtInfo = new HurtInfo {
            Source = bulletInfo.sourceObject,
            Damage = Mathf.RoundToInt(damage *
                                      (Convert
                                          ? ((_mythic != null && _mythic.MythicLevelInBattle >= 2) ? 1.75f : 1.25f)
                                          : 1)),
            Critic = false,
            SourceWeapon = bulletInfo.sourceWeapon,
            FingerPrint = fingerPrint,
            DamageType = DamageType.Ranged | DamageType.Aoe,
            ElementalType = elementalType
        };

        if (Convert && _mythic != null && _mythic.MythicLevelInBattle >= 1 && weapon.rg_random.Range(0, 100) < 10) {
            BuffEffectTrigger.EnemyAddBuff(bulletInfo.sourceObject, bulletInfo.sourceWeapon, hit_enemy.gameObject,
                ResourcesUtil.Load<GameObject>("RGPrefab/Bullet/Buff/buff_lightning.prefab"));
        }

        SimpleEventManager.Raise(PlayerBulletPreHitEnemyEvent.UseCache(hit_enemy, null, this, hurtInfo.Source,
            hurtInfo.SourceWeapon, hurtInfo.Damage, hurtInfo.Critic));
        if (PlayerBulletPreHitEnemyEvent.isModified) {
            hurtInfo.Damage = PlayerBulletPreHitEnemyEvent.finalDamage;
            hurtInfo.Critic = PlayerBulletPreHitEnemyEvent.finalCritical;
        }

        DamageCarrierHelper.ProcessEnemyGetHurtDamage(hit_enemy, hurtInfo);
        SimpleEventManager.Raise(PlayerBulletHitEnemyEvent.UseCache(hit_enemy, hurtInfo.Damage, false, null, this,
            bulletInfo.sourceObject, bulletInfo.sourceWeapon, hit_enemy.GetColliderCenter()));
        this.actionInfo?.OnHit(this, hit_enemy.gameObject, new BulletHitData {
            isCritical = hurtInfo.Critic
        });
        this.actionInfo = null; // clean
    }

    public override void UpdateInfo(BulletInfo bulletInfo, DamageInfo damageInfo) {
        base.UpdateInfo(bulletInfo, damageInfo);
        damage = this.damageInfo.damage;
        rate = this.damageInfo.damageInterval;
        repel = this.damageInfo.repel;
        weapon = this.bulletInfo.sourceWeapon;
        if (weapon is MythicWeapon12 m) {
            _mythic = m;
        }

        setup = true;
    }

    public void EndLaser() {
        anim.SetBool(Atk, false);
    }

    public void DestoryLaser() {
        CancelInvoke(nameof(ShowCollider));
        setup = false;
        PrefabPool.Inst.Store(gameObject);
        OnAtkEnd();
    }

    public void OnHit(RGEController enemy) {
    }

    public void FlushInfo() {
        UpdateInfo(bulletInfo, damageInfo);
    }

    public Action<GameObject> OnAttackEndAction;

    public void OnAtkEnd() {
        OnAttackEndAction?.Invoke(gameObject);
    }

    public Action<GameObject, Vector2> OnHitEnemyAction;
    private static readonly int Atk = Animator.StringToHash("atk");
    private static readonly int Convert1 = Animator.StringToHash("convert");

    public void OnHitEnemy(GameObject enemy, Vector2 hitPoint) {
        OnHitEnemyAction?.Invoke(enemy, hitPoint);
    }
}