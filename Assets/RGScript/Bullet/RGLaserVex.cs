using UnityEngine;
using System.Collections;

public class RGLaserVex : RG<PERSON>aser, IPrefabPoolObject {
    public GameObject buff;
    public float hitNumber = 10;

    private int nowHitNumber = 0;
    
    public override ElementalType ElementalType {
        get {
            var type = ElementalType.None;
            if (buff != null && buff.GetComponent<RGBuff>() is { } buffComponent) {
                type = buffComponent.elementalType;
            }

            return type;
        }
    }

    protected override void Awake() {
        base.Awake();
        showColliderDelay = 0.7f;
    }

     public override void OnHit(RGEController enemy) {
        base.OnHit(enemy);
        //添加buff
        if (buff != null) {
            if (!enemy.dead) {
                Transform child = enemy.transform.Find(buff.name);
                if (child == null) {
                    GameObject temp_buff = Instantiate(buff, enemy.transform.position, Quaternion.identity) as GameObject;
                    temp_buff.name = buff.name;
                    temp_buff.transform.parent = enemy.transform;
                    temp_buff.GetComponent<OffensiveInterface>().SetSourceObject(bulletInfo.sourceObject);
                    child = temp_buff.transform;
                }

                if (child.GetComponent<BuffVex>() is { } buffVex) {
                    buffVex.AddLevel();
                    buffVex.camp = camp;
                }
            }
        }
    }

     public override void OnTaken() {
        MasterLaserBuffWidthFactor = 1;
        anim.SetFloat("holdSpeed", holdSpeed);
        
        if (GetComponent<AudioSource>() is { } audioSource) {
            audioSource.pitch = Random.Range(0.95f, 1.15f);
        }
        
        base.OnTaken();

    }

    protected override void ShowCollider() {
        nowHitNumber += 1;
        if (nowHitNumber > hitNumber) {
            EndLaser();
            if (weapon) weapon.atk_b  = false;

            return;
        }
        
        base.ShowCollider();
        
        var right = end.position - start.position;
        float angle = Vector2.Angle(Vector2.right, right);
        if (right.y < 0) {
            angle = -angle;
        }

        Vector3 tv3 = transform.position + right.normalized * (bullet.localScale.x / 2f);
        var casts = Physics2D.BoxCastAll(tv3, new Vector2(bullet.localScale.x * bulletLengthDenominator, bullet.localScale.y) + colliderExtraSize, angle, Vector2.zero, 0, LayerMask.GetMask(new string[] { "Bullet", "Default" }));
        
        foreach (var cast in casts) {
            if (cast.collider.GetComponent<RGBulletTrigger>() is { } rgBulletTrigger && rgBulletTrigger.transform.parent != transform) {
                if (rgBulletTrigger.GetComponentInParent<DamageCarrier>() is {} dc && dc.bulletInfo.camp == GetComponentInParent<DamageCarrier>().bulletInfo.camp) continue;
                
                rgBulletTrigger.CreateHitEffect(null, false, 0);
                rgBulletTrigger.CheckDestroyState();
                rgBulletTrigger.DestroyBullet(null);
            }
        }
    }

    protected override HurtInfo CaculateLaserDamage(RGEController hit_enemy, float extraDamageFactor) {
        
        var hurtInfo = new HurtInfo { 
            Source = bulletInfo.sourceObject,
            Critic = false,
            SourceWeapon = bulletInfo.sourceWeapon,
            FingerPrint = fingerPrint,
            DamageType = DamageType.Ranged | DamageType.Aoe,
            ElementalType = ElementalType
        };
        
        hurtInfo.Damage = damage + (nowHitNumber >= shoot_max_time ? 1 : 0);

        SimpleEventManager.Raise(PlayerBulletPreHitEnemyEvent.UseCache(hit_enemy, null, this, hurtInfo.Source, hurtInfo.SourceWeapon, hurtInfo.Damage, hurtInfo.Critic));
        if(PlayerBulletPreHitEnemyEvent.isModified){
            hurtInfo.Damage = PlayerBulletPreHitEnemyEvent.finalDamage;
            hurtInfo.Critic = PlayerBulletPreHitEnemyEvent.finalCritical;
        }
        DamageCarrierHelper.ProcessEnemyGetHurtDamage(hit_enemy, hurtInfo);
        SimpleEventManager.Raise(PlayerBulletHitEnemyEvent.UseCache(hit_enemy, hurtInfo.Damage, false, null, this, bulletInfo.sourceObject, bulletInfo.sourceWeapon, hit_enemy.GetColliderCenter()));
        this.actionInfo?.OnHit(this, hit_enemy.gameObject, new BulletHitData {
            isCritical = hurtInfo.Critic 
        });
        this.actionInfo = null; // clean
        return hurtInfo;
    }

    public override void EndLaser() {
        CancelInvoke("ShowCollider");
        
        base.EndLaser();
        nowHitNumber = 0;
    }
}