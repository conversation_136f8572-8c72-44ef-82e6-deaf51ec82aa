using System.Collections;
using UnityEngine;

namespace RGScript.Bullet
{
    public class BuffRotate : R<PERSON><PERSON>,OffensiveInterface {
        private RGEController _enemy;
        private Transform _body;
        public float rotateSpeed;
        private Transform _img;
        private Vector2 _size;
        private Vector2 _scale;
        private Transform _shadow;
        private Transform _shadowLock;
        // Start is called before the first frame update
        protected override void Start()
        {
            base.Start();
            _enemy = transform.parent.GetComponent<RGEController>();
            if (_enemy&&is_enemy){
                if (_enemy.enemyTag.HasFlag(emEnemyTag.Boss)
                    || _enemy.enemyTag.HasFlag(emEnemyTag.Static)
                    ||_enemy.enemyTag.HasFlag(emEnemyTag.Friendly)) {
                    Destroy(gameObject);
                } else {
                    _body = _enemy.transform.Find("img/body");
                    _img = _enemy.transform.Find("img");
                    _size = _body.gameObject.GetComponent<SpriteRenderer>().size;
                    _scale = _body.transform.localScale;
                    _shadow = _enemy.transform.Find("shadow");
                    _shadowLock = _enemy.transform.Find("shadow_lock");
                    StartCoroutine(Rotate());
                }
            } 
            Destroy(gameObject, buff_time);
        }

        private void Update() {
            if (_enemy.dead) 
                Destroy(gameObject);
        }

        IEnumerator Rotate() {
            if(_body)
                _body.localPosition -= new Vector3(0,_size.y/2*Mathf.Abs(_scale.y) , 0);
            if(_shadow)
                _shadow.gameObject.GetComponent<SpriteRenderer>().enabled = false;
            if(_shadowLock)
                _shadowLock.gameObject.GetComponent<SpriteRenderer>().enabled = false;
            while (true) {
                _enemy.transform.eulerAngles += new Vector3(0, 0, rotateSpeed);
                yield return new WaitForSeconds(0.05f);
            }
        }
        public void SetSourceObject(GameObject value){
        }
        public GameObject GetSourceObject(){
            return null;
        }

        private void OnDestroy() {
            StopCoroutine(nameof(Rotate));
            _enemy.transform.eulerAngles =Vector3.zero;
            if(_body)
                _body.localPosition=Vector3.zero;
            if(_shadow)
                _shadow.gameObject.GetComponent<SpriteRenderer>().enabled = true;
            if(_shadowLock)
                _shadowLock.gameObject.GetComponent<SpriteRenderer>().enabled = true;
        }
    }
}
