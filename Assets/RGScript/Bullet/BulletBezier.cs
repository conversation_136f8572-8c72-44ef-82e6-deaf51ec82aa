using UnityEngine;

/// <summary>
/// 贝塞尔子弹
/// </summary>
public class BulletBezier : Bullet01 {
    
    protected override void CreateBulletMover() {

    }

    public void SetUpData(Vector3 start, Vector3 end, float handlerHigh,float handlerHor = 0.5f) {
        BulletMoverBezier moverBezier = CheckMover();
        bulletMover = moverBezier;
        
        moverBezier.Setup(speed, start, end, handlerHigh,handlerHor);
    }

    private BulletMoverBezier CheckMover() {
        if (bulletMover is not BulletMoverBezier moverBezier) {
            return new BulletMoverBezier();
        }
        return moverBezier;
    }


    public override void ReboundBullet(bool changeDirection) {
        if (bulletMover != null) {
            (bulletMover as BulletMoverBezier).Stop();
        }
        base.ReboundBullet(changeDirection);
    }
}