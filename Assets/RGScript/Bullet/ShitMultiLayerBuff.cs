using DG.Tweening;
using UnityEngine;

namespace RGScript.Bullet {
    public sealed class ShitMultiLayerBuff : MultiLayerBuffBase {
        public GameObject buffPrefab;
        public Sprite shitSprite;
        private Transform _iconChild;

        protected override void Awake() {
            base.Awake();
            if (transform.childCount > 0) {
                _iconChild = transform.GetChild(0);
            }

            SetupIconAnimation();
        }

        private void SetupIconAnimation() {
            Sequence scaleSequence = DOTween.Sequence();
            scaleSequence.Append(_iconChild.DOScale(1.1f, 0.3f))
                .Append(_iconChild.DOScale(1f, 0.3f))
                .Append(_iconChild.DOScale(1.05f, 0.3f))
                .Append(_iconChild.DOScale(1f, 0.3f))
                .SetLoops(-1, LoopType.Restart);

            scaleSequence.SetAutoKill(false);
            onBuffEnd += () => scaleSequence.Kill();
        }

        protected override void OnLayerChanged(int oldLayers, int newLayers) {
            if (newLayers == 1) {
                BindEnemyDead();
            }

            int currentChildCount = _iconChild.childCount;

            if (newLayers > currentChildCount) {
                for (int i = currentChildCount; i < newLayers; i++) {
                    GameObject layerIndicator = new($"Layer_{i + 1}");
                    layerIndicator.transform.SetParent(_iconChild);

                    SpriteRenderer sr = layerIndicator.AddComponent<SpriteRenderer>();
                    sr.sortingLayerName = "Effect";
                    sr.sortingOrder = i;

                    layerIndicator.transform.localPosition = new Vector3(0, i * 0.25f, 0);
                    layerIndicator.transform.localScale = Vector3.one;

                    sr.sprite = shitSprite;
                }
            } else if (newLayers < currentChildCount) {
                for (int i = currentChildCount - 1; i >= newLayers; i--) {
                    Transform child = _iconChild.GetChild(i);
                    Destroy(child.gameObject);
                }
            }
        }

        private void HandleEnemyDead(GameObject o) {
            if (AttachEnemy != null) {
                AttachEnemy.DeadEvents.RemoveListener(HandleEnemyDead);
            }

            OnEnemyDeadEffect();
            BuffEnd();
        }

        public void BindEnemyDead() {
            if (AttachEnemy != null) {
                AttachEnemy.DeadEvents.RemoveListener(HandleEnemyDead);
                AttachEnemy.DeadEvents.AddListener(HandleEnemyDead);
            }
        }

        protected override void OnMaxLayersReached() {
            AttachEnemy.DeadEvents.RemoveListener(HandleEnemyDead);
            var gas = PrefabPool.Inst.Take(buffPrefab, transform.parent.transform.position, Quaternion.identity);
            gas.transform.localScale = Vector3.one * (1 + (currentLayers * 0.1f));
            BuffEnd();
        }

        private void OnEnemyDeadEffect() {
            var gas = PrefabPool.Inst.Take(buffPrefab, transform.parent.transform.position, Quaternion.identity);
            gas.transform.localScale = Vector3.one * (1 + (currentLayers * 0.1f));
        }
    }
}