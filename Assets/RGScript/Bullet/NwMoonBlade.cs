using UnityEngine;

public class NwMoonBlade : RGBullet {
    protected override void CreateBulletMover() {
    }

    public override void OnTaken() {
        base.OnTaken();
        _shadowTimer = 0;
    }

    public float shadowInterval = .05f;
    public float shadowDuration = .5f;
    private float _shadowTimer;
    
    private void Update() {
        if (awake) {
            _shadowTimer += Time.deltaTime;
            if (_shadowTimer >= shadowInterval) {
                _shadowTimer = 0;
                if (b != null) {
                    var sr = b.GetComponent<SpriteRenderer>();
                    if (sr != null) {
                        sr.CreateShadow(shadowDuration);
                    }
                }
            }
        }
    }
}