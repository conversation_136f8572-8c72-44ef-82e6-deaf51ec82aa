using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 改变方向的Task
/// </summary>
public class ChangeDirTask : BulletTask {

	float target_angle = 0f;
	float last_angle = 0f;
	float old_angle = 0f;
	public float Duration {
		get;
		set;
	}
	public ChangeDirTask(ITaskParam task_param, BulletTask owner) : base(task_param, owner) {}

	public override void SetupTask(RGTaskBullet bullet) {
		base.SetupTask(bullet);
		this.Duration = (this.task_param as ChangeDirParam).Duration;
	}

	public override TaskRunStatus ExecuteTask() {
		var tmp_task_param = this.task_param as ChangeDirParam;
		float rad = tmp_task_param.Angle * Mathf.Deg2Rad;
		switch (tmp_task_param.RunType) {
			case BulletRunType.Sequence:
				target_angle = tmp_task_param.Angle;
				break;
			case BulletRunType.Absolute: //bullet.angle ->  angle
				// target_angle = (1 - (this.Duration / tmp_task_param.Duration)) * tmp_task_param.Angle;
				target_angle = (1 - (this.Duration / tmp_task_param.Duration)) * (tmp_task_param.Angle - this.bullet.transform.eulerAngles.z);
				// Debug.Log(string.Format("delta_angle: {0} | duration: {1} | tmp_task_param.Duration{2}", target_angle, this.Duration, tmp_task_param.Duration));
				target_angle += this.bullet.transform.eulerAngles.z;
				break;
			case BulletRunType.Relative: //bullet.angle -> bullet.angle + angle
				target_angle = (1 - (this.Duration / tmp_task_param.Duration)) * tmp_task_param.Angle;
				float tmp_angle = target_angle;
				target_angle -= last_angle;
				// Debug.Log(string.Format("target_angle: {0} | last_angle: {1}", target_angle, last_angle));
				last_angle = tmp_angle;
				target_angle += this.bullet.transform.eulerAngles.z;
				break;
			default:
				target_angle = tmp_task_param.Angle;
				// Debug.LogError("ChangeDirTask not support BulletRunType: " + tmp_task_param.RunType.ToString());
				break;
		}
		if (target_angle > 360f) {
			target_angle -= 360f;
		}
		this.bullet.UpdateBulletAngle(target_angle);
		// this.Duration -= Time.deltaTime;
		this.Duration -= Time.fixedDeltaTime;
		if (this.Duration <= 0.0f) {
			TaskFinished = true;
			return TaskRunStatus.End;
		}
		return TaskRunStatus.Continue;
	}
}