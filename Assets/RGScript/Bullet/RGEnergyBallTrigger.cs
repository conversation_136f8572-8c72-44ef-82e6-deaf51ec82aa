using UnityEngine;
using I2.Loc;
using RGScript.Map;

/// <summary>
/// Boss06祖蓝技能
/// </summary>
public class RGEnergyBallTrigger : RGBulletTrigger {
    public GameObject buff;
    public float disableTime = 1.5f;
    
    public override ElementalType ElementalType {
        get {
            var type = ElementalType.None;
            if (buff != null && buff.GetComponent<RGBuff>() is { } buffComponent) {
                type = buffComponent.elementalType;
            }

            return base.ElementalType | type;
        }
    }
    
    protected readonly GameObjectSingleCache<BoxCollider2D> _colliderCache = 
        new GameObjectSingleCache<BoxCollider2D>();
    protected readonly GameObjectSingleCache<SpriteRenderer> _spriteRendererCache = 
        new GameObjectSingleCache<SpriteRenderer>();
    /// <summary>
    /// 是否是怪物子弹
    /// </summary>
    private bool IsPlayerBullet => (camp != 0);

    /// <summary>
    /// 是否是怪物子弹
    /// </summary>
    private bool IsEnemyBullet => (camp == 0);

    private bool IsHitEnemy(Component other) {
        return other.gameObject.CompareTag("Body_E") && (the_bullet && the_bullet.IsPlayerBullet || IsPlayerBullet);
    }

    private bool IsHitPlayer(Component other) {
        if (null == the_bullet) {
            return false;
        }

        //old
        // return other.gameObject.CompareTag("Body_P") && (the_bullet && the_bullet.IsEnemyBullet || IsEnemyBullet && other.gameObject.GetComponent<RGController>() && other.gameObject.GetComponent<RGController>().can_hurt);
        return DamageUtil.IsHitPlayer(other, the_bullet.bulletInfo);
    }

    private bool IsHitPet(Component other) {
        return other.gameObject.CompareTag("Pet") && (the_bullet && the_bullet.IsEnemyBullet || IsEnemyBullet);
    }

    private void HitEnemy(Component other, out bool hasCritical) {
        hasCritical = Random.Range(0, 100) < critical;
        var enemyController = other.gameObject.GetComponent<RGEController>();
        var enemyTransform = other.transform;
        var finalDamage = GetFinalDamage();
        var hurtInfo = new HurtInfo {
            Source = transform.parent.gameObject,
            Damage = finalDamage,
            SourceWeapon = carrier.bulletInfo.sourceWeapon,
            FingerPrint = fingerPrint,
            DamageType = DamageType,
            ElementalType = ElementalType
        };
        if (!hasCritical) {
            float tempRepel = Mathf.Min(finalDamage * repel, 30);
            enemyController.GetForce(transform.TransformDirection(Vector2.right), RGGameProcess.ProcessRepelForce(tempRepel));
            if (carrier != null && (finalDamage > 0 || show_zero_dmg)) {
                DamageCarrierHelper.ProcessEnemyGetHurtDamage(enemyController, hurtInfo);
            }
        } else {
            var enemyPosition = enemyTransform.position;
            if (buff == null) { // 暴击加伤害
                finalDamage *= 2;
            } else { // 暴击加额外效果
                if (enemyController.role_attribute.hp > 0 && enemyTransform.Find(buff.name) == null) {
                    var tempBuff = Instantiate(buff, enemyPosition, Quaternion.identity);
                    tempBuff.name = buff.name;
                    tempBuff.transform.parent = other.transform;
                }
            }
            UICanvas.Inst.ShowTextCritical(other.transform, 1);
            float tempRepel = Mathf.Min(finalDamage * repel, 30);
            enemyController.GetForce(transform.TransformDirection(Vector2.right), RGGameProcess.ProcessRepelForce(tempRepel));
            hurtInfo.Damage = finalDamage;
            hurtInfo.Critic = true;
            DamageCarrierHelper.ProcessEnemyGetHurtDamage(enemyController, hurtInfo);
        }
        OnHitEnemy(hasCritical, finalDamage, enemyController, enemyController ? enemyController.gameObject : null);
        need_destory = emDestroyState.Destroy;
    }

    private void HitPlayer(Component other) {
        var playerController = other.gameObject.GetComponent<RGController>();
        var finalDamage = GetFinalDamage();
        var isCritical = Random.Range(0, 100) < critical;
        playerController.GetForce(transform.TransformDirection(Vector2.right), RGGameProcess.ProcessRepelForce(finalDamage * repel));
        if (playerController.role_attribute.hp > 0) {
            if (isCritical && buff) {
                if (other.transform.Find(buff.name) == null) {
                    var tempBuff = Instantiate(buff, other.transform.position, Quaternion.identity);
                    tempBuff.name = buff.name;
                    tempBuff.GetComponent<RGBuff>().is_enemy = false;
                    tempBuff.transform.parent = other.transform;
                }
            }
        }
        playerController.GetHurt(new HurtInfo {
            Critic = isCritical,
            Damage = finalDamage,
            DamageType = DamageType,
            ElementalType = ElementalType,
            Source = transform.parent.gameObject,
            SourceWeapon = null,
            FingerPrint = fingerPrint
        });
        need_destory = emDestroyState.Destroy;
    }

    private void HitPet(Component other) {
        var pet = other.gameObject.GetComponent<RGBaseController>();
        if (!pet) {
            return;
        }
        var finalDamage = GetFinalDamage();
        pet.GetForce(transform.TransformDirection(Vector2.right), RGGameProcess.ProcessRepelForce(finalDamage * repel));
        pet.GetHurt(new HurtInfo {
            Critic = false,
            Damage = finalDamage,
            DamageType = DamageType,
            ElementalType = ElementalType,
            Source = transform.parent.gameObject,
            SourceWeapon = null,
            FingerPrint = fingerPrint
        });
        need_destory = emDestroyState.Destroy;
    }

    protected void HasHitSomeThing(Collider2D other, bool hasCritical) {
        var tempHitEffectGO = Instantiate(hit_object, transform.position, Quaternion.identity);
        tempHitEffectGO.transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;
        if (hasCritical) {
            tempHitEffectGO.transform.localScale = new Vector3(2, 2, 1);
            RGMusicManager.GetInstance().PlayEffect(5);
        }
        foreach (var effectTrigger in effectTriggers) {
            effectTrigger.TriggerWith(other, GetFinalDamage(), hasCritical, can_through);
        }

        var shouldDestroy = need_destory == emDestroyState.Destroy ||
                            need_destory == emDestroyState.ForceDestroy;
        if (!shouldDestroy) return;
        StartHide();
        Invoke(nameof(EndHide), disableTime);
    }

    private bool IsHitBox(Component other) {
        return other.gameObject.CompareTag("Box");
    }

    private void HitBox(Component other, out bool hasHit) {
        var finalDamage = GetFinalDamage();
        hasHit = other.gameObject.GetComponent<IRGBox>().Hit(finalDamage, transform.parent.gameObject, camp);
        if (!hasHit) return;
        hasHit = true;
        need_destory = emDestroyState.Destroy;
    }
    
    protected override void OnTriggerEnter2D(Collider2D other) {
        if (transform == null) {
            return;
        }
        var hasHit = false;
        var hasCritical = false;
        if (IsHitEnemy(other)) {
            hasHit = true;
            HitEnemy(other, out hasCritical);
        } else if (IsHitPlayer(other)) {
            hasHit = true;
            HitPlayer(other);
        } else if (IsHitPet(other)) {
            hasHit = true;
            HitPet(other);
        } else if (IsHitBox(other) && 
            (carrier.damageInfo.damageType & emDamageType.IgnoreBox) == 0) {
            HitBox(other, out hasHit);
        }
        if (hasHit) {
            HasHitSomeThing(other, hasCritical);
        }
    }

    protected virtual void EndHide() {
        _colliderCache.GetCache(gameObject).enabled = true;
        _spriteRendererCache.GetCache(gameObject).enabled = true;
    }

    protected virtual void StartHide() {
        _colliderCache.GetCache(gameObject).enabled = false;
        _spriteRendererCache.GetCache(gameObject).enabled = false;
    }
    
    public override void SetInfo(int damage, int camp, bool can_through, int repel, int critical) {
        damage += MapManager.Instance.GetAdditionDamage(camp, the_bullet.GetSourceObject());
        this.Damage = damage;
        this.camp = camp;
        this.can_through = can_through;
        this.repel = repel;
        this.critical = critical;
        HandleCritical();
    }
}
