using RGScript.Bullet;

/// <summary>
/// 跟踪
/// </summary>
public class Bullet02JumpingBoom : Bullet02 {
    private BulletKeepHorizontal _keepHorizontal;

    protected override void Awake() {
        base.Awake();
        _keepHorizontal = transform.GetChild(0).GetComponent<BulletKeepHorizontal>();
    }

    public void SetFaceLeft(bool faceLeft) {
        _keepHorizontal.SetFaceLeft(faceLeft);
    }
}