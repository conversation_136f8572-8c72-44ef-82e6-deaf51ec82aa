using System.Linq;
using UnityEngine;

public class BulletHatOnCharacter : MonoBehaviour {
    public int maxCount = -1;
    public GameObject hatBuff;
    private GameObject _sourceObject;

    private void Start() {
        var bullet = transform.GetComponent<RGBulletTrigger>();
        if (bullet) {
            _sourceObject = bullet.the_bullet.GetSourceObject();
        }
    }

    private void OnTriggerEnter2D(Collider2D col) {
        var rgController = col.GetComponent<RGController>();
        if (!rgController) {
            return;
        }

        if (maxCount > 0) {
            var buffs = rgController.gameObject.GetComponentsInChildren<RGBuff>(false);
            var count = buffs.Count(item => item.name.StartsWith(hatBuff.name));
            if (count >= maxCount) {
                return;
            }
        }
        
        GameObject tempBuff = Instantiate(hatBuff, rgController.transform.position, Quaternion.identity) as GameObject;
        tempBuff.GetComponent<RGBuff>().is_enemy = true;
        tempBuff.name = hatBuff.name;
        tempBuff.transform.parent = rgController.transform;
        tempBuff.GetComponent<OffensiveInterface>().SetSourceObject(_sourceObject);
        Destroy(transform.parent.gameObject);
    }
}