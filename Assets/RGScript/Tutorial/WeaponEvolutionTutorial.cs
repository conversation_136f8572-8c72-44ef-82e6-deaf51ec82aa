using RGScript.UI.Forge;
using RGScript.WeaponEvolution;
using System.Collections;
using UIFramework;
using UnityEngine;

namespace RGScript.Tutorial {
    public abstract class WeaponEvolutionTutorial {
        public const string WeaponEvolutionTutorialWeapon = "weapon_007";

        public static IEnumerator TryStartTutorial() {
            if (StatisticData.data.GetEventCount(RGGameConst.WEAPON_EVOLUTION_TUTORIAL_TRIGGERED_TODAY) > 0) {
                yield break;
            }

            if (StatisticData.data.GetEventCount(RGGameConst.WEAPON_EVOLUTION_TUTORIAL_TRIGGER_TIMES) >= RGGameConst.WEAPON_EVOLUTION_TUTORIAL_TRIGGER_MAX_TIMES) {
                yield break;
            }

            if (StatisticData.data.IsEventRecord(RGGameConst.WEAPON_EVOLUTION_TUTORIAL)) {
                yield break;
            }

            if (WeaponEvolutionModule.CanGetWeaponEvolutionMaterial(WeaponEvolutionTutorialWeapon)) {
                yield break;
            }

            if (WeaponEvolutionModule.HasEvolvedWeapon()) {
                yield break;
            }

            if (RoomTrainerPet.HasTeach()) {
                yield break;
            }

            if (UIManager.Inst.GetUIView<UITeaching>() != null) {
                yield break;
            }

            if (!RoomItemUnlockThroughBattle.GetUnlock(emRoomUnlockItem.work_shop)) {
                yield break;
            }

            var itemForge = Object.FindObjectOfType<ItemForge>();
            if (itemForge == null) {
                yield break;
            }

            var container = itemForge.transform.Find("right/container_0");
            if (container == null) {
                yield break;
            }

            StatisticData.data.AddEventCount(RGGameConst.WEAPON_EVOLUTION_TUTORIAL_TRIGGER_TIMES, 1, false);
            StatisticData.data.AddEventCount(RGGameConst.WEAPON_EVOLUTION_TUTORIAL_TRIGGERED_TODAY, 1, true);
            var tempObj = Object.Instantiate(ResourcesUtil.Load<GameObject>("RGPrefab/LevelObject/Item/target_point.prefab"), RGGameSceneManager.GetInstance().temp_objects_parent, true);
            tempObj.transform.position = container.position;
            var targetPoint = tempObj.GetComponent<TargetPoint>();
            targetPoint.SetDestroyWhenReach(false);
            var sortingLayer = SortingLayer.NameToID("Effect");
            targetPoint.SetCircleIndicatorLayer(sortingLayer);
            UICanvas.Inst.ShowTempMessage("weapon_evolution_tutorial/go_to_forge", -1f);
            yield return new WaitUntil(() => UIManager.Inst.GetUIView<UIForgeWindow>() != null);
            UITeaching.TrackTutorial("weapon_evolution_go_to_forge", UITeaching.Teach.weapon_evolution);
            UICanvas.Inst.EndTempMessage();
            var uiTeaching = UIManager.Inst.OpenUIView<UITeaching>("ui_teaching");
            uiTeaching.current_teach = UITeaching.Teach.weapon_evolution;
            yield return new WaitUntil(() => UIManager.Inst.GetUIView<UIForgeWindow>() == null);
            UITeaching.TrackTutorial("weapon_evolution_finish_tutorial", UITeaching.Teach.weapon_evolution);
            Object.Destroy(tempObj);
        }
    }
}