using System;
using System.Collections;
using System.Collections.Generic;
using I2.Loc;
using UnityEngine;
using Util.TAUtil;

public class ObjectBossRushEngineer : RGItem {

    public const int MAX_LEVEL = 3;
    public AudioClip audio_clip;
    int stateIndex = 0;

    bool unlock = false;
    public ParticleSystem effectParticle;
    private Animator _animator;
    private static readonly int FinishedId = Animator.StringToHash("finished");
    private bool _isUnlock = false;

    private void Awake() {
        _animator = GetComponent<Animator>();
    }

    // protected override void Start() {
    // 	base.Start();
    // 	int count = StatisticData.data.GetEventCount(RGGameConst.STATIC_DATA_BOSSRUSH);
    // 	item_value = (int)(count > 0 ? item_value * (count * 0.5f + 1) : item_value);
    // }

    protected override void OnTriggerEnter2D(Collider2D other) {
        base.OnTriggerEnter2D(other);
        if (other.CompareTag("Body_P")) {
            stateIndex = 0;
        }
    }

    protected override bool Triggerable(RGController controller) {
        stateIndex++;
        if (stateIndex == 2) {
            if (RGGameProcess.Inst.coin_value >= HelpValkyrieCost()) {
                if (CanTrain()) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    protected override void OnItemTriggerSuccess(RGController controller, string extraInfo = "") {
        if (controller.IsLocalPlayer()) {
            RGGameProcess.Inst.ConsumeCoin(HelpValkyrieCost(), emStatisticsType.HelpValkyrie);
            UICanvas.GetInstance().ShowTextTalk(controller.transform,
                GetStringWithColor(-HelpValkyrieCost(), ItemLevel.GoldCoin), 2.5f, 1f);
            int count = StatisticData.data.GetEventCount(RGGameConst.STATIC_DATA_BOSSRUSH);
            if (count == MAX_LEVEL - 1) {
                effectParticle.Play();
                _animator.SetTrigger(FinishedId);
                _isUnlock = true;
                UICanvas.GetInstance().ShowTextTalk(transform,
                    ScriptLocalization.Get("item/br_thanks2") /*"谢谢你的帮助，离我的目标又进了一步！"*/, 2.5f, 1f);
            } else {
                UICanvas.GetInstance().ShowTextTalk(transform,
                    ScriptLocalization.Get("item/br_thanks") /*"谢谢你的帮助，离我的目标又进了一步！"*/, 2.5f, 1f);
            }

            UICanvas.GetInstance().UpdateCoin();
            UICanvas.GetInstance().HideObjectInfo();
            RGMusicManager.GetInstance().PlayEffect(audio_clip);
        }

        StartCoroutine(GetMoney(controller));
        base.OnItemTriggerSuccess(controller, extraInfo);
    }

    protected override void OnItemTriggerFail(RGController controller) {
        if (controller.IsLocalPlayer()) {
            if (stateIndex == 1) {
                //询问
                Vector3 tempv3 = transform.position;
                tempv3.y += label_height;
                if (!CanTrain()) {
                    Invoke("TalkMaxLevel", 0.75f);
                    UICanvas.GetInstance().HideObjectInfo();
                    controller.SetItemtf(null);
                } else {
                    UICanvas.GetInstance().ShowObjectInfo(tempv3, GetPaymentDialogText(ScriptLocalization.Get("item/help_valkyrie"), HelpValkyrieCost(), ItemLevel.GoldCoin));
                }
            } else if (stateIndex == 2) {
                //金币不足
                UICanvas.GetInstance()
                    .ShowTextTalk(controller.transform, ScriptLocalization.Get("I_no_money"), 2.5f, 1f);
                UICanvas.GetInstance().HideObjectInfo();
                controller.SetItemtf(null);
            }
        }
    }

    void TalkMaxLevel() {
        UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get("item/br_thanks2") /*"谢谢你的帮助，我们会再见面的！"*/,
            2.5f, 1f);
    }

    public IEnumerator GetMoney(RGController controller) {
        yield return new WaitForSeconds(0.5f);
        GetMoney();
    }

    public void GetMoney() {
        StatisticData.data.AddEventCount(RGGameConst.STATIC_DATA_BOSSRUSH, 1, false);
        int count = StatisticData.data.GetEventCount(RGGameConst.STATIC_DATA_BOSSRUSH);
        if (count <= MAX_LEVEL) {
            GameObject ticket_obj = ResourcesUtil.CreateItem("material_ticket_bossrush",
                RGGameSceneManager.GetInstance().temp_objects_parent);
            ticket_obj.transform.position = transform.Find("point" + 2).position;
        }
    }

    public bool CanTrain() {
        return !(StatisticData.data.GetEventCount(RGGameConst.STATIC_DATA_BOSSRUSH) >= MAX_LEVEL);
    }

    public override bool CanUse() {
        return true;
    }

    private int HelpValkyrieCost() {
        int count = StatisticData.data.GetEventCount(RGGameConst.STATIC_DATA_BOSSRUSH);
        switch (count) {
            case 0:
                return 10;
            case 1:
                return 20;
            case 2:
                return 30;
        }
        return 0;
    }
}

    



