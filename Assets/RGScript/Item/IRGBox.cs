using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

public interface IRGBox {
    BoxDestroyEvent boxDestroyEvent { get; set; }
    bool CanHit(int damage, GameObject source_object, int camp);
    bool Hit(int damage, GameObject source_object, int camp);

    Vector3 GetPosition();
}

[Serializable]
public class BoxDestroyEvent : UnityEvent<IRGBox> {
    public BoxDestroyEvent() {
        AddListener(box => {
            SimpleEventManager.Raise(new BoxDestroySimpleEvent {
                box = box,
            });
        });
    }
}