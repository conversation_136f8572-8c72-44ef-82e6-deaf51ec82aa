using I2.Loc;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class PlantPet : ItemPlant {
	public AudioClip audio_clip;
	public Sprite petSprite;
	public string petName;
    public string plantDesc;

	protected override void Awake() {
		base.Awake();
		var pet = trState.GetChild(trState.childCount - 1).GetComponentInChildren<RGPetController>();
		pet.GetComponent<Rigidbody2D>().isKinematic = true;
        petName = pet.name;
	}
    
    public override string GetItemInfoStr() {
        return ScriptLocalization.Get(plantDesc, plantDesc);
    }

    protected override void OnItemTriggerSuccess(RGController controller, string extraInfo = "") {
		var pet = trState.GetChild(trState.childCount - 1).GetComponentInChildren<RGPetController>();
        if (pet == null) {
            return;
        }
        
		pet.SetMaster(controller.transform);
		pet.transform.SetParent(null);
		pet.awake = true;
		pet.GetComponent<Rigidbody2D>().isKinematic = false;
        pet.GetComponentInChildren<CircleCollider2D>().enabled = true;
		pet.Scout();
		BattleData.data.AddPetAddition(pet.name);
		RGMusicManager.GetInstance().PlayEffect(audio_clip);
		base.OnItemTriggerSuccess(controller, extraInfo);
	}

	protected override void OnItemTriggerFail(RGController controller) {
		base.OnItemTriggerFail(controller);
        if (BattleData.data.CompareFactor(emBattleFactor.AllAlone)) {
            ShowTalk(controller.transform, I2.Loc.ScriptLocalization.Get("I_allalone"));
        }
		RGMusicManager.GetInstance().PlayEffect(8);
	}

	protected override bool Triggerable(RGController controller) {
		return base.Triggerable(controller) && !BattleData.data.CompareFactor(emBattleFactor.AllAlone) && !BattleData.data.petAdditions.Contains(petName);
	}
}
