using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using RGScript.Battle;
/// <summary>
/// 可拾取的道具自动吸附到玩家身上自动拾取
/// </summary>
public class RGAutoCollectItem : MonoBehaviour
{
    public float delay = 1;
    float _passedTime = 0;
    int _state = 0;
    public System.Func<bool> canCollect{get; set;}
    public System.Action<GameObject> onCollect{get; set;}

    public float collectRange = 999;

    void Start(){
        collectRange += RGScript.Battle.BuffStackCfgExpertForce.GetRangeParam(5);
    }

    void Update(){
        if(_state == 0){
            _passedTime += Time.deltaTime;
            if(_passedTime >= delay){
                _state = 1;
                var collider = transform.GetComponent<Collider2D>();
                if(collider)
                    collider.enabled = false;
            }
        }
        else if(_state == 1){
            if(RGGameSceneManager.Inst != null && RGGameSceneManager.Inst.controller != null && 
                !ItemFlyInAir(transform, RGGameSceneManager.Inst.controller.transform, collectRange, canCollect, onCollect)){
                _state = 2;
            }
        }
    }

    public static bool ItemFlyInAir(Transform transform, Transform target, float collectRange, System.Func<bool> canCollect, System.Action<GameObject> onCollect){
        if(transform != null && target != null){
            var displacement = target.position - transform.position;
            if(displacement.magnitude < collectRange){
                if(displacement.magnitude < 0.5f){
                    var item = transform.GetComponent<RGItem>();
                    if(item){
                        if(canCollect == null || canCollect()){
                            if(target.GetComponent<RGController>() is var controller && controller != null){
                                item.ItemTrigger(controller);
                            }
                        }
                        else
                            GameObject.Destroy(item.gameObject);
                    }
                    else{
                        onCollect?.Invoke(transform.gameObject);
                    }
                    return false;
                }
                else{
                    transform.position += displacement.normalized * Mathf.Min(18 * Time.deltaTime, displacement.magnitude);
                }
            }
        }
        return true;
    }
}
