using I2.Loc;
using ModeSeason.ComboGun;
using RGScript.Data;
using RGScript.Data.GameItemData;
using RGScript.UI;
using RGScript.Util;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using UnityEngine;

public interface IRewardable {
    void GetReward(bool showUi);
    int GetCount();//正值表数量, 负值表不显示
    string GetName();
    string GetDesc();
    Sprite GetSprite();
    Sprite GetFlag();
}

[Serializable]
public class SkinRewardInfo : IRewardable {
    public emHero hero;
    public int skinIndex;

    public void GetReward(bool showUi) {
        DataUtil.SetSkinUnlock(hero, skinIndex, true);
        if (showUi) {
            UIWindowShowObject.ShowUIWindowObject(GetSprite(), GetName());
        }
    }
    public int GetCount() {
        return -1;
    }
    public string GetName() {
        return NameUtil.GetSkinName(hero, skinIndex);
    }
    public string GetDesc() {
        return NameUtil.GetSkinDesc(hero,skinIndex);
    }
    public Sprite GetSprite() {
        return RGSaveManager.Inst.GetHeroUISprite((int)hero, skinIndex);
    }
    public Sprite GetFlag() {
        return null;
    }
}

public class PetRewardInfo : IRewardable {
    public emPet pet;

    public void GetReward(bool showUi) {
        DataUtil.SetPetUnlock(pet, true);
        if (showUi) {
            UIWindowShowObject.ShowUIWindowObject(GetSprite(), GetName());
        }
    }
    public int GetCount() {
        return -1;
    }    
    public string GetName() {
        return NameUtil.GetPetName(pet);
    }
    public string GetDesc() {
        return $"<color=#FF0000>{ScriptLocalization.Get("G_Pet")}</color> {GetName()}";
    }
    public Sprite GetSprite() {
        return RGSaveManager.Inst.GetPetUISprite((int)pet);
    }
    public Sprite GetFlag() {
        return null;
    }
}
public class HeroSkillRewardInfo : IRewardable {
    public emHero hero;
    /// <summary>
    /// 技能index 从0开始计算 即1代表二技能
    /// </summary>
    public int skillIndex;
    public int GetCount() {
        return -1;
    }
    public Sprite GetFlag() {
        return null;
    }
    public string GetName() {
        return NameUtil.GetSkillName(hero, skillIndex);
    }
    public string GetDesc() {
        return $"<color=#FF0000>{ScriptLocalization.Get("G_Skill")}</color><color=#FFD300>{NameUtil.GetHeroName(hero)}</color>{GetName()}";
    }
    public void GetReward(bool showUi) {
        RGSaveManager.Inst.SetSkillUnlock(hero, skillIndex, true);
        if (showUi) {
            UIWindowShowObject.ShowUIWindowObject(GetSprite(), GetName());
        }
    }
    public Sprite GetSprite() {
        return RGSaveManager.Inst.GetSkillInfo(hero, skillIndex).icon;
    }
}
public class HeroLevelRewardInfo : IRewardable {
    public emHero hero;
    /// <summary>
    /// level 从0开始计算
    /// </summary>
    public int level;
    public int GetCount() {
        return -1;
    }
    public Sprite GetFlag() {
        return null;
    }
    public string GetName() {
        return ScriptLocalization.Get(string.Format("Character{0}_name_skin0", (int)hero)) + level + ScriptLocalization.Get("LEVEL") + ScriptLocalization.Get("UNLOCK");
    }
    public string GetDesc() {
        return GetName();
    }
    public void GetReward(bool showUi) {
        if (level > 0 && !DataUtil.GetHeroUnlock(hero)) {
            DataUtil.SetHeroUnlock(hero, true);
        }
        
        if (DataUtil.GetHeroLevel(hero) < level) {
            DataUtil.SetHeroLevel(hero, level);
            if (showUi) {
                UIWindowShowObject.ShowUIWindowObject(GetSprite(), GetName());
            }
        }
    }
    public Sprite GetSprite() {
        return RGSaveManager.Inst.GetHeroUISprite((int)hero, 0);
    }
}
public class HeroRewardInfo : IRewardable {
    public emHero hero;
    public int GetCount() {
        return -1;
    }
    public Sprite GetFlag() {
        return null;
    }
    public string GetName() {
        return NameUtil.GetHeroName(hero) + ScriptLocalization.Get("UNLOCK");
    }
    public string GetDesc() {
        return $"<color=#FF0000>{ScriptLocalization.Get("G_Character")}</color>{GetName()}";
    }
    public void GetReward(bool showUi) {
        DataUtil.SetHeroUnlock(hero, true);
        if (showUi) {
            UIWindowShowObject.ShowUIWindowObject(GetSprite(), GetName());
        }
    }

    public Sprite GetSprite() {
        return RGSaveManager.Inst.GetHeroUISprite((int)hero, 0);
    }
}

public class WeaponRewardInfo : IRewardable {

    public string weaponName;
    // public WeaponItemBase weapon_item;

    public int GetCount() {
        return -1;
    }
    public Sprite GetFlag() {
        return null;
    }
    public string GetName() {
        string local_name = ScriptLocalization.Get("weapon/" + weaponName);
        if (WeaponInfo.info.name2Weapon.ContainsKey(weaponName)) {
            local_name = RGItem.GetStringWithColor(local_name, (ItemLevel)WeaponInfo.info.name2Weapon[weaponName].level);
        }
        return local_name;
    }
    public string GetDesc() {
        return $"<color=#FF0000>{ScriptLocalization.Get("I_weapon")}</color>{GetName()}";
    }
    public void GetReward(bool showUi) {
        GameUtil.PlayerPickUpWeapon(weaponName, emWeaponSource.Award);
        if (showUi) {
            UIWindowShowObject.ShowUIWindowObject(GetSprite(), GetName());
        }
    }
    public Sprite GetSprite() {
        return SpriteMap.weaponSprite.GetSprite(weaponName);
    }
}

public class MultiRoomRewardInfo : IRewardable {

    public int index;
    public void GetReward(bool showUi) {
        DataUtil.SetMultiRoomSkinUnlock(index);
        if (showUi) {
            UIWindowShowObject.ShowUIWindowObject(GetSprite(), GetName());
        }
    }

    public int GetCount() {
        return 1;
    }

    public string GetName() {
        return I2.Loc.ScriptLocalization.Get($"multi_room_skin_name_{index}");
    }

    public string GetDesc() {
        //new_multi_room_skin
        return $"{ScriptLocalization.Get("new_multi_room_skin")} {ScriptLocalization.Get($"multi_room_skin_name_{index}")}";
    }

    public Sprite GetSprite() {
        return SpriteMap.multiRoomSkinSprite.GetSprite($"multi_room_skin_name_{index}");
    }

    public Sprite GetFlag() {
        return null;
    }
}
public class HeroRoomRewardInfo : IRewardable {
    public HeroRoomSkinType heroRoomSkinType;
    public int index;
    public void GetReward(bool showUi) {
        DataUtil.SetHeroRoomSkinUnlock(heroRoomSkinType, index, "gift_code");
        if (showUi) {
            UIWindowShowObject.ShowUIWindowObject(GetSprite(), GetName());
        }
    }

    public int GetCount() {
        return 1;
    }

    public string GetName() {
        return I2.Loc.ScriptLocalization.Get($"{DataUtil.GetHeroRoomSkinKey(heroRoomSkinType)}_name_{index}");
    }

    public string GetDesc() {
        //new_multi_room_skin
        return $"{ScriptLocalization.Get($"{DataUtil.GetHeroRoomSkinKey(heroRoomSkinType)}_title")} {GetName()}";
    }

    public Sprite GetSprite() {
        string key = $"{DataUtil.GetHeroRoomSkinKey(heroRoomSkinType)}_{index}";
        var sprite = SpriteMap.heroRoomSkinSprite.GetSprite(key);
        if (sprite == null) {
            //返回fallback的sprite 因为此资源还未补齐
            return SpriteMap.heroRoomSkinSprite.GetSprite($"{DataUtil.GetHeroRoomSkinKey(heroRoomSkinType)}");
        }
        return SpriteMap.heroRoomSkinSprite.GetSprite(key);
    }

    public Sprite GetFlag() {
        return null;
    }
}

/// <summary>
/// 特定武器兑换券
/// </summary>
[Serializable]
public class WeaponTokenRewardInfo : IRewardable {
    public string tokenKey;
    public int count;

    public void GetReward(bool showUi) {
    ItemData.data.AddTokenTicket(tokenKey, count);
    if (showUi) {
        UIWindowShowObject.ShowUIWindowObject(GetSprite(), GetName());
    }
    }
    public int GetCount() {
        return count;
    }
    public string GetName() {
        return NameUtil.GetTokenTicketName(tokenKey, true);
        // var locKey = ItemUtility.GetTypeNameKey(ItemType.WeaponToken);
        // var wNameWithColor = GetWeaponName();
        // return $"{ScriptLocalization.Get(locKey)}[{wNameWithColor}]";
    }

    // private string GetWeaponName() {
    //     var weaponName = NameUtil.GetTokenTickWeaponPrefabName(tokenKey);
    //     var weaponLv = GameUtil.GetSpecialWeaponTokenLevel(tokenKey);
    //     string local_name = ScriptLocalization.Get("weapon/" + weaponName);
    //     return RGItem.GetStringWithColor(local_name, (ItemLevel)weaponLv);
    // }

    public string GetDesc() {
        return $"{GetName()}{(count <= 1 ? "" : "×" + GetCount())}";
    }
    public Sprite GetSprite() {
        return SpriteMap.weaponSprite.GetSprite("weapon_token");
    }
    public Sprite GetFlag() {
        var weaponName = NameUtil.GetTokenTickWeaponPrefabName(tokenKey);
        return SpriteMap.weaponSprite.GetSprite(weaponName);
    }
}


/// <summary>
/// 武器皮肤
/// </summary>
[Serializable]
public class WeaponSkinRewardInfo : IRewardable {
    public string id;
    public void GetReward(bool showUi) {
        if (ItemUtility.GetWeaponSkinInfo(id, out var info)) {
            DataMgr.WeaponEvolutionModule.UnlockWeaponSkin(info.WeaponID, info.SkinIndex);
            if (showUi) {
                UIWindowShowObject.ShowUIWindowObject(GetSprite(), GetName());
            }
        } else {
            Debug.LogError($"[WeaponSkinRewardInfo] 获取武器皮肤失败 : {id}");
        }
    }

    public int GetCount() {
        return 1;
    }
    public string GetName() {
        return ItemUtility.GetItemTitle(id);
    }
    public string GetDesc() {
        return ItemUtility.GetItemTitle(id);
    }
    public Sprite GetSprite() {
        return SpriteUtility.GetSprite(id);
    }
    public Sprite GetFlag() {
        return null;
    }
}

/// <summary>
/// 组枪赛季随从坐骑
/// </summary>
[Serializable]
public class ComboGunFollowerMountRewardInfo : IRewardable {
    public string key;
    public int count;

    public void GetReward(bool showUi) {
        var itemType = ItemUtility.GetItemType(key);
        if (itemType != ItemType.ComboGunMount && itemType != ItemType.ComboGunFollower) {
            Debug.LogError("Invalid itemType for ComboGunFollowerMountRewardInfo " + key);
            return;
        }

        for (int i = 0; i < count; i++) {
            DataMgr.CGStoreData.AddFollowerOrMount(key);
        }

        ComboGunData.Save();

        if (showUi) {
            UIWindowShowObject.ShowUIWindowObject(GetSprite(), GetName());
        }
    }

    public int GetCount() {
        return count;
    }

    public string GetName() {
        return ItemUtility.GetItemTitle(key);
    }

    public string GetDesc() {
        return $"{GetName()}{(count <= 1 ? "" : "×" + GetCount())}";
    }

    public Sprite GetSprite() {
        return SpriteUtility.GetSprite(key);
    }

    public Sprite GetFlag() {
        return null;
    }
}

/// <summary>
/// 神话武器
/// </summary>
[Serializable]
public class MythicWeaponRewardInfo : IRewardable {
    public string key;
    public int count;

    public void GetReward(bool showUi) {
        var itemType = ItemUtility.GetItemType(key);
        if (itemType != ItemType.MythicWeapon) {
            Debug.LogError("Invalid itemType for MythicWeaponRewardInfo " + key);
            return;
        }

        var indexStr = key.Replace("weapon_mythic_", "");
        var index = int.Parse(indexStr);
        for (int i = 0; i < count; i++) {
            ItemData.data.UnlockMythicWeapon(index, false);
        }
        ItemData.Save();

        if (showUi) {
            UIWindowShowObject.ShowUIWindowObject(GetSprite(), GetName());
        }
    }

    public int GetCount() {
        return count;
    }

    public string GetName() {
        return ItemUtility.GetItemTitle(key);
    }

    public string GetDesc() {
        return ScriptLocalization.Get($"weapon/{key}/desc");
    }

    public Sprite GetSprite() {
        return SpriteUtility.GetSprite(key);
    }

    public Sprite GetFlag() {
        return null;
    }
}
