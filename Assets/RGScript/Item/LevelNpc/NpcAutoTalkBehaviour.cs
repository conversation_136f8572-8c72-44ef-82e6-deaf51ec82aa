using I2.Loc;
using Sirenix.OdinInspector;
using System;
using UnityEngine;

namespace RGScript.Item.LevelNpc {
    [RequireComponent(typeof(RGItem))]
    public class NpcAutoTalkBehaviour : MonoBehaviour {
        [LabelText("要说的话（本地化key）")] public string autoTalkKey = "mode_loop/npc/gambler_talk";

        [LabelText("自动说话间隔时间（秒）")] [Range(1, 10)]
        public float autoTalkInterval = 4f;

        private RGItem _item;
        private Timer _talkTimer;

        private void Awake() {
            _item = GetComponent<RGItem>();
            if (null == _item) {
                return;
            }

            _talkTimer = Timer.Register(autoTalkInterval, true, false, () => {
                _item.ShowTalk(transform, ScriptLocalization.Get(autoTalkKey, "来来来，试一试"));
            });

            _item.OnStartInteractEvent += controller => {
                _talkTimer?.Pause();
            };

            _item.OnEndInteractEvent += controller => {
                _talkTimer?.Resume();
            };
        }

        public void StopTalk() {
            _talkTimer?.Cancel();
        }

        void OnDestroy() {
            StopTalk();
        }
    }
}