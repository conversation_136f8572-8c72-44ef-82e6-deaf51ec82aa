using Activities.Fire.Scripts;
using I2.Loc;
using UnityEngine;

namespace RGScript.Item {
    public class NpcSellRefreshItem : RGItem {
        public AudioClip audio_clip;

        private Transform _root;
        private int stateIndex = 0;

        // var str2 = "I_businessman";
        // var str = "ui/ad_get_video";
        // var str1 = "I_businessman_talk";
        private const int GemBaseValue = 50;
        private int _gemValue = GemBaseValue;
        protected override void Start() {
            base.Start();
            _root = transform.parent.parent;
        }

        protected override void OnTriggerEnter2D(Collider2D other) {
            base.OnTriggerEnter2D(other);
            if (other.CompareTag("Body_P")) {
                stateIndex = 0;
            }
        }

        protected override void OnGetClose(RGController controller) {
            base.OnGetClose(controller);
            if (!controller.IsLocalPlayer()) {
                return;
            }
            UICanvas.Inst.ShowGemCoin(true);
        }

        protected override void OnGetAway(RGController controller) {
            base.OnGetAway(controller);
            if (!controller.IsLocalPlayer()) {
                return;
            }

            if (BattleData.data.HasActivityEnabled(ActivityFireManager.TAG)) {
                return;
            }
            
            UICanvas.Inst.ShowGemCoin(false);
        }

        protected override bool Triggerable(RGController controller) {
            var soldOutCount = 0;
            var array = _root.GetComponentsInChildren<RGContainer>();
            foreach (var container in array) {
                if (container.State == RGContainer.ContainerState.SoldOut) {
                    soldOutCount++;
                }
            }
            if (soldOutCount == array.Length) {
                return false;
            }

            stateIndex++;
            if (stateIndex == 2) {
                return RGSaveManager.Inst.GetGem() >= _gemValue;
            }
            return false;
        }

        protected override void OnItemTriggerSuccess(RGController controller, string extraInfo = "") {
            if (controller.IsLocalPlayer()) {
                RGSaveManager.Inst.ConsumeGem(_gemValue);
                // 减少货币
                UICanvas.GetInstance().ShowTextTalk(controller.transform, $"<color=#4FC2FBFF>-{_gemValue}</color>", 2.5f, 1f);
                UICanvas.GetInstance().UpdateGemText();
                UICanvas.GetInstance().HideObjectInfo();
                RGMusicManager.GetInstance().PlayEffect(audio_clip);
                _gemValue *= 2;
            }

            RefreshItems();
            base.OnItemTriggerSuccess(controller, extraInfo);
        }

        protected override void OnItemTriggerFail(RGController controller) {
            if (!controller.IsLocalPlayer()) {
                return;
            }

            if (stateIndex == 0) {
                TriggerFailSoldOut();
                HideTap(controller);
            } else if (stateIndex == 1) {
                TriggerFailShowCost();
            } else if (stateIndex == 2 && RGSaveManager.Inst.GetGem() < _gemValue) {
                TriggerFailMoneyNotEnough(controller);
                HideTap(controller);
            }
        }

        private void TriggerFailShowCost() {
            string talkText = ScriptLocalization.Get("object/sell_refresh");
            string dialog = GetPaymentDialogText(talkText, _gemValue, ItemLevel.Gem);
            ShowTap(dialog, false);
        }

        private void TriggerFailMoneyNotEnough(RGController controller) {
            Talk("I_no_gem", transform);
        }

        private void TriggerFailSoldOut() {
            Talk("ui/seasonstore_soldout", transform);
        }

        private static void Talk(string talkKey, Transform talkTransform) {
            var talkStr = ScriptLocalization.Get(talkKey);
            if (Debug.isDebugBuild && string.IsNullOrEmpty(talkStr)) {
                talkStr = talkKey;
            }
            UICanvas.GetInstance().ShowTextTalk(talkTransform, talkStr, 2.5f, 1f);
        }

        private void RefreshItems() {
            var rgContainerDiffCtrl = GetComponentInParent<RGContainerDiffCtrl>();

            if (rgContainerDiffCtrl) {
                rgContainerDiffCtrl.Clear();
            }

            var array = _root.GetComponentsInChildren<RGContainer>();
            foreach (var container in array) {
                container.RefreshItem();
            }
        }
    }
}