using cfg.config;
using I2.Loc;
using NewDynamicConfig;
using RGScript.Data;
using RGScript.Data.Mall;
using RGScript.Item.RoomItem;
using RGScript.Manager.SDK;
using RGScript.Other.Globalize;
using RGScript.Util;
using Sirenix.OdinInspector;
using Sirenix.Utilities;
using System.Collections;
using System.Collections.Generic;
using UIFramework;
using UnityEngine;
using Util.TAUtil;

public class ItemEggMachine : RGItem {
    public GameObject tab;
    public GameObject eggProto;
    public AudioClip buyAudio;
    public int mode;
    public static readonly int UnitEggMachineTimes = 80;
    public static readonly int OneShootCostGem = 200;
    
    private Transform _gunPoint;
    private RGController _controller;
    private List<string> _rewardList = new();
    private int _openEggNumber;
    private bool _eggMachineUIOpen;
    private bool _limitTimes;
    private int _minTimes;
    private string _festivalEggMachineTag = "defaultTag";

    private bool _shootingEgg;
    public GameObject tv_tab;

    private VersionEggMachineConfig Config => EggMachineUtil.GetEggMachineConfig();
    
    public bool NeedShowRedPoint() {
        return mode == 1 && !StatisticData.data.IsEventRecord(_festivalEggMachineTag);
    }

    private void OnEnable() {
        SimpleEventManager.AddEventListener<EggMachineUIEvent>(OnGetEggMachineUICommand);
        SimpleEventManager.AddEventListener<ItemRoomEggOpenEvent>(OnItemRoomEggOpen);
        SimpleEventManager.AddEventListener<SelectHeroEvent>(OnHeroSelect);
    }

    private void OnDisable() {
        SimpleEventManager.RemoveListener<EggMachineUIEvent>(OnGetEggMachineUICommand);
        SimpleEventManager.RemoveListener<ItemRoomEggOpenEvent>(OnItemRoomEggOpen);
        SimpleEventManager.RemoveListener<SelectHeroEvent>(OnHeroSelect);
    }

    protected override void Start() {
        base.Start();
        _gunPoint = transform.Find("gun_point");
        label_height = 4f;
        mode = 0;
        _limitTimes = true;
        
        if (HasNetWorkAndGetConfig()) {
            var time = MallUtility.CurrentTime;
            var startTime = TimeUtil.ChineseTimestampToLocalDateTime(Config.TimeStart);
            var endTime = TimeUtil.ChineseTimestampToLocalDateTime(Config.TimeEnd);
            if (time > startTime && time < endTime) {
                mode = Config.EggMachineMode;
                _limitTimes = Config.IsLimitTimes;
                _minTimes = UnitEggMachineTimes - Config.EggNumber;
                _festivalEggMachineTag = Config.FestivalEggMachineTag;
            } else {
                ItemData.data.easterEggTempPets.Clear();
            }
        } else {
            mode = 0;
        }
        
        tab.SetActive(mode == 1 && !StatisticData.data.IsEventRecord(_festivalEggMachineTag));
        
        if (!StatisticData.data.IsEventRecord("new_egg_machine_times")) {
            ItemData.data.eggMachineTimes = UnitEggMachineTimes;
            StatisticData.data.RecordEvent("new_egg_machine_times", true);
        }

        TryClearActivityData();
        tv_tab = transform.Find("tv_tab").gameObject;
    }

    private bool HasNetWorkAndGetConfig() {
        var hasNetWork = Application.internetReachability != NetworkReachability.NotReachable;
        return hasNetWork && !string.IsNullOrEmpty(Config.TimeStart) && !string.IsNullOrEmpty(Config.TimeEnd);
    }
    
    private void TryClearActivityData() {
        if (!HasNetWorkAndGetConfig()) {
            return;
        }

        var time = NetTimeUtil.CurrentTime.Item2;
        var startTime = TimeUtil.ChineseTimestampToLocalDateTime(Config.TimeStart);
        var endTime = TimeUtil.ChineseTimestampToLocalDateTime(Config.TimeEnd);
        if (time <= startTime || time >= endTime) {
            ItemData.data.easterEggTempPets.Clear();
        }

        if (Config.FestivalEggMachineTag.IsNullOrWhitespace()) {
            return;
        }
        
        if (!Config.FestivalEggMachineTag.Equals("2024FoolDayEggMachine")) {
            ItemData.data.easterEggTempPets.Clear();
        }
    }
    
    public override void ItemTrigger(RGController controller) {
        if (RGItemExtensions.ShowCantUseTipsInPureMultiGameMode(transform, 2f, controller)) {
            return;
        }
        
        base.ItemTrigger(controller);
    }
    
    protected override bool Triggerable(RGController controller) {
        triggerState++;
        _controller = controller;
        return false;
        return triggerState == 2 && (ItemData.data.eggMachineTimes > _minTimes || !_limitTimes) &&
               RGSaveManager.Inst.ConsumeGem(OneShootCostGem, false);
    }
    
    protected override void OnStartInteract(RGController controller) {
        base.OnStartInteract(controller);
        _controller = controller;
        if (tv_tab != null && tv_tab.activeInHierarchy) {
            tv_tab.gameObject.SetActive(false);
        }
        if (mode == 0) {
            return;
        }
        
        if (_eggMachineUIOpen || _shootingEgg) {
            triggerState = 0;
            EndInteract(controller);
            return;
        }
        
        UICanvas.GetInstance().ShowReReadGuildInfo(ShowGuideUI);
    }
    
    protected override void EndInteract(RGController controller) {
        base.EndInteract(controller);
        if (mode == 0) {
            return;
        }
        if (UICanvas.GetInstance() != null) {
            UICanvas.GetInstance().HideReReadGuildInfo();
        }
    }
    
    protected override void OnItemTriggerSuccess(RGController controller, string extraInfo = "") {
        base.OnItemTriggerSuccess(controller, extraInfo);
        if (_eggMachineUIOpen || _shootingEgg) {
            triggerState = 0;
            EndInteract(controller);
            return;
        }

        if (mode == 1) {
            OpenEggMachineUI();
            EndInteract(controller);
            return;
        }
        
        GetEggOnce(true);
    }

    public void GetEggOnce(bool needShowGemTalkText , int continueGetEgg = 1) {
        if (needShowGemTalkText) {
            UICanvas.GetInstance()
                .ShowTextTalk(_controller.transform, GetStringWithColor(-OneShootCostGem*continueGetEgg, ItemLevel.Gem), 2.5f, 1);
        }
        RGMusicManager.GetInstance().PlayEffect(buyAudio);
        ShootEgg(_controller);
        if (_limitTimes) {
            ItemData.data.AddEggMachineTime(-1,true);
        }
        
        ConsumeStatistics.TrackConsumeType(OneShootCostGem, emStatisticsType.EggMachine);
    }
    
    void ShowGuideUI() {
        var chineseDetail =
            ResourcesUtil.Load<GameObject>("RGPrefab/UI/GuideView/WindowEggMachineDetail.prefab");
        if (chineseDetail && LanguageUtil.IsChineseSystem(true)) {
            UIManager.Inst.OpenUIView<UIWindowMutiTextDialog>(chineseDetail);
        }
        
        CommonGuideView.ShowGuideView("RGPrefab/UI/GuideView/NewEggMachineGuideView.prefab",
            UICanvas.Inst.transform, null);
        StatisticData.data.RecordEvent(_festivalEggMachineTag, true);
        tab.SetActive(false);
    }
    
    protected override void OnItemTriggerFail(RGController controller) {
        if (mode == 0) {
            if (_eggMachineUIOpen || _shootingEgg) {
                return;
            }

            OpenEggMachineUI();
            EndInteract(controller);
            return;
        }
        if (mode == 1) {
            if (!StatisticData.data.IsEventRecord(_festivalEggMachineTag)) {
                ShowGuideUI();
                return;
            }
            
            if (_eggMachineUIOpen || _shootingEgg) {
                return;
            }

            OpenEggMachineUI();
            EndInteract(controller);
            return;
        }

        if (triggerState == 1) {
            string talk = string.Empty;
            if (_limitTimes) {
                var lastTime = ItemData.data.eggMachineTimes - _minTimes;
                if (lastTime < 0) {
                    lastTime = 0;
                }
                talk = GetPaymentDialogText(ScriptLocalization.Get("talk/gashapon"), GetItemValue(), ItemLevel.Gem) +
                       "<size=24>\n" + I2.Loc.ScriptLocalization.Get("item/egg_machine_left") +
                       (lastTime) + "</size>";
            } else {
                string talkText = ScriptLocalization.Get("talk/gashapon");
                talk = GetPaymentDialogText(talkText, GetItemValue(), ItemLevel.Gem);
            }

            UICanvas.GetInstance().ShowObjectInfo(transform.position + new Vector3(0, label_height, 0), talk,mode==1);

            //交互埋点
            BehaviourPathStatistics.TrackBehaviour(emBehaviourPoint.EggMachine);
        } else if (triggerState == 2 && ItemData.data.eggMachineTimes > _minTimes) {
            UICanvas.ShowUIWindowShop(null, buyWay: ConsumeStatistics.BuyWay.EggMachine);
        } else if (ItemData.data.eggMachineTimes <= _minTimes) {
            UICanvas.GetInstance().ShowTextTalk(controller.transform, ScriptLocalization.Get("item/egg_machine_nomore"),
                2.5f, 1);
        } else {
            triggerState = 0;
        }
    }

    private void OpenEggMachineUI() {
        var ui = UIManager.Inst.OpenUIView<UIWindowEggMachine>("RGPrefab/UI/Hall/window_egg_machine.prefab");
        UIWindowEggMachine machine = ui.GetComponent<UIWindowEggMachine>();
        machine.eggMachine = this;
        machine.RefreshEggMachineTimes(GetEggMachineTodayLeftTimes());
        machine.InitByMode(mode);
        triggerState = 0;
        _eggMachineUIOpen = true;
    }
    
    public bool IsLimitedByEggMachineTimes(int times = 1) {
        return ItemData.data.eggMachineTimes - _minTimes < times;
    }

    public int GetEggMachineTodayLeftTimes() {
        return ItemData.data.eggMachineTimes - _minTimes;
    }
    
    public void ShootEgg(RGController controller, bool openEggOnAwake = true) {
        GameObject temp = GameObject.Instantiate<GameObject>(eggProto);
        temp.GetComponent<ItemRoomEgg>().Mode = mode;
        _gunPoint.transform.eulerAngles = new Vector3(0, 0, Random.Range(-90 + 20, -90 - 20));
        temp.transform.eulerAngles = new Vector3(0, 0, 90 + 70);
        temp.transform.position = _gunPoint.position;
        var velocity = _gunPoint.right * Random.Range(2.5f, 4.0f);
        StartCoroutine(ShootingObject(temp, velocity,controller, openEggOnAwake));
    }

    IEnumerator ShootingObject(GameObject obj, Vector3 velocity,RGController controller, bool openEggOnAwake = true) {
        float speedY = 7f;
        float totalAngle = obj.transform.eulerAngles.z;
        float totalTime = 0.7f;
        float gravity = speedY * 2 / totalTime;
        float time = 0;
        while (time < totalTime) {
            time += Time.deltaTime;
            obj.transform.position = obj.transform.position + velocity * Time.deltaTime +
                                     new Vector3(0, speedY, 0) * Time.deltaTime;
            speedY -= gravity * Time.deltaTime;
            obj.transform.eulerAngles = new Vector3(0, 0, totalAngle * (1 - time / totalTime));
            yield return null;
        }

        obj.transform.Find("col").GetComponent<Collider2D>().enabled = true;
        obj.GetComponent<Collider2D>().enabled = true;
        
        var roomEgg = obj.GetComponent<ItemRoomEgg>();
        roomEgg.rgController = controller;
        if (openEggOnAwake) {
            roomEgg.OpenEgg();
        }
    }
    
    private void OnGetEggMachineUICommand(EggMachineUIEvent e) {
        switch (e.command) {
            case "ad":
                if (e.buyEggNumber < 1) {
                    Debug.LogError("egg machine ui command wrong:egg number can't less than 1");
                    break;
                }
        
                _openEggNumber = e.buyEggNumber;  
                if (e.buyEggNumber == 1) {
                    GetEggOnce(false);
                    _eggMachineUIOpen = false;
                } else {
                    StartCoroutine(nameof(GetMoreEgg),e.buyEggNumber);
                    _shootingEgg = true;
                }
                break;
            case "buy":
                if (e.buyEggNumber < 1) {
                    Debug.LogError("egg machine ui command wrong:egg number can't less than 1");
                    break;
                }
        
                _openEggNumber = e.buyEggNumber;  
                if (e.buyEggNumber == 1) {
                    GetEggOnce(true);
                    _eggMachineUIOpen = false;
                } else {
                    StartCoroutine(nameof(GetMoreEgg),e.buyEggNumber);
                    _shootingEgg = true;
                }
                break;
            case "close":
                _eggMachineUIOpen = false;
                break;
            case "reopen":
                OpenEggMachineUI();
                break;
        }
    }

    private void OnItemRoomEggOpen(ItemRoomEggOpenEvent e) {
        _rewardList.Add(e.rewardName);
        var rewardCount = _rewardList.Count;
        if (rewardCount != _openEggNumber) {
            return;
        }

        TAUtil.Track("egg_machine_activity", new Dictionary<string, object>() {
            { "egg_rewards", _rewardList },
            { "last_times_today", ItemData.data.eggMachineTimes },
            { "drawn_number", _openEggNumber }
        });

        _rewardList.Clear();
    }
    
    private IEnumerator GetMoreEgg(int buyNumber) {
        int i = 0;
        while (i < buyNumber) {
            GetEggOnce(true, buyNumber);
            yield return new WaitForSeconds(0.3f);
            i++;
        }
        
        _eggMachineUIOpen = false;
        _shootingEgg = false;
    }

    private void OnHeroSelect(SelectHeroEvent data) {
        if (!tab.activeInHierarchy && Config.EggMachineMode != 1 && !ChannelConfig.IsTapTap && AdsUtil.SupportVideos() && AdsUtil.IsRewardVideoReady(AdsUtil.EggMachine) && AdOptimizeUtil.CanEggMachineAd()) {
            tv_tab.gameObject.SetActive(true);
        }
    }
    
    #region TestTools
    
    private bool _hasStart;
    private Coroutine _eggCoroutine; 
    private Coroutine _fourFactorCoroutine;
    
    public void StartCreatEggForLua(int count)
    {
        if (_hasStart) return;

        _eggCoroutine = StartCoroutine(WaitCreatEggForLua(count));

        _hasStart = true;

        Invoke(nameof(StopCreatEggForLua), count * 0.3f);
    }
    
    private void StopCreatEggForLua()
    {
        if (!_hasStart) return;

        if (_eggCoroutine != null) StopCoroutine(_eggCoroutine);
        if (_fourFactorCoroutine != null) StopCoroutine(_fourFactorCoroutine);

        _hasStart = false;
    }
    
    IEnumerator WaitCreatEggForLua(int count)
    {
        var rg = GameObject.Find("p1").GetComponent<RGController>();

        for (int i = 0; i < count; i++)
        {
            ShootEgg(rg);
            yield return new WaitForSeconds(0.3f);
        }
    }
    
    IEnumerator WaitCreatEggUtilFourFactor()
    {
        var rg = GameObject.Find("p1").GetComponent<RGController>();

        while (BattleData.data.GetMark("eggFacterNumber") < 4)
        {
            ShootEgg(rg);
            yield return new WaitForSeconds(0.3f);
        }
    }

    #if UNITY_EDITOR
    public bool festivalMode = true;
    [Button(Name = "设置扭蛋机模式")]
    public void SetEggMachineMode() {
        mode = festivalMode ? 1 : 0;
        _limitTimes = true;
        _minTimes = UnitEggMachineTimes - 200;
        _festivalEggMachineTag = "test";
    }
    
    [Range(0, 200)] public int number;
    [Button(Name = "连续获取指定数量的蛋并打开↑")]
    public void StartCreatEgg() {
        if (_hasStart) {
            return;
        }

        StartCoroutine(nameof(WaitCreatEgg));
        _hasStart = true;
        Invoke(nameof(StopCreatEgg), number * 0.3f);
    }
    
    IEnumerator WaitCreatEgg() {
        int i = 0;
        while (i < number) {
            ShootEgg(GameObject.Find("p1").GetComponent<RGController>());
            yield return new WaitForSeconds(0.3f);
            i++;
        }
    }

    [Button(Name = "连续获取蛋并打开直到出现4个因子")]
    public void StartCreatEggUtilFourFactor() {
        if (_hasStart) {
            return;
        }

        StartCoroutine(nameof(WaitCreatEggUtilFourFactor));
        _hasStart = true;
    }

    [Button(Name = "停止连续获得蛋")]
    public void StopCreatEgg() {
        if (!_hasStart) {
            return;
        }

        StopCoroutine(nameof(WaitCreatEgg));
        StopCoroutine(nameof(WaitCreatEggUtilFourFactor));
        _hasStart = false;
    }

    [Button("清空临时宠物列表")]
    public void ClearTempEasterPets() {
        ItemData.data.easterEggTempPets = new List<string>();
    }
    #endif

    #endregion
}
