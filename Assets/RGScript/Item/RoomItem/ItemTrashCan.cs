using RGScript.Data;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 垃圾桶
/// </summary>
public class ItemTrashCan : RGStateItem {
    protected override void OnTriggerEnter2D(Collider2D other) {
        base.OnTriggerEnter2D(other);
        if (other.CompareTag("DailyPackage")) {
            Destroy(other.gameObject);
            AchieveInfos.info.CheckUnlock(AchieveInfos.AchievementType.EnvironmentDefender, 0, null, emHero.Airbender);
        }
    }
    public System.Action<RGController, GameObject> onDropItem;

	protected override void OnItemTriggerSuccess(RGController controller, string extraInfo = "") {
		RGMusicManager.GetInstance().PlayEffect(8);
        if (controller.hand.front_weapon) {
            if(controller.hand.front_weapon.isCreateFromSkill){
                return;
            }
            RGWeapon weapon = controller.DropWeapon();
            if (weapon) {
                if (weapon.weapon_item) {
                    weapon.weapon_item.Drop();
                }
                
                if (weapon.weapon_item_activity) {
                    weapon.weapon_item_activity.Drop();
                }
                
                onDropItem?.Invoke(controller, weapon.gameObject);
                GameObject.Destroy(weapon.gameObject);
                // Debug.Log("recycle success!");
                StatisticData.data.AddEventCount(RGGameConst.WEAPON_RECYCLE, 1, true);
                int count = StatisticData.data.GetEventCount(RGGameConst.WEAPON_RECYCLE);
                AchieveInfos.info.CheckUnlock(AchieveInfos.AchievementType.CCleaner, count, "", emHero.None);
            }

            controller.SwitchToLastWeapon();
        }
        else if (controller.hand.transform.childCount > 0 &&
            controller.hand.transform.GetChild(0).GetComponent<ItemTrash>() is ItemTrash itemTrash && itemTrash) {
            itemTrash.Drop(null);
            onDropItem?.Invoke(controller, itemTrash.gameObject);
            GameObject.Destroy(itemTrash.gameObject);
        }
    }

    RGController _controller;
    RGWeapon _currentWeapon;
    protected override void OnGetClose(RGController controller) { 
        _controller = controller;
        _currentWeapon = controller.hand!.front_weapon;
    }

    protected override void OnGetAway(RGController controller) { 
        _controller = null;
        _currentWeapon = null;
    }

    void Update(){
        if(_controller != null && _controller.hand!.front_weapon != _currentWeapon){
            gameObject.GetComponent<Collider2D>().enabled = false; // 重新触发碰撞
            Invoke(nameof(_Enable), 0.01f);
        }
    }

    void _Enable(){
        gameObject.GetComponent<Collider2D>().enabled = true;
    }
}