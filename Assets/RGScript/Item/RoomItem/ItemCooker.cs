using I2.Loc;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Util.TAUtil;
using Random = UnityEngine.Random;

namespace RGScript.Item.RoomItem {
    public class ItemCooker : RGItem {
        private Transform _exclamationMark;

        private readonly List<(emBattleFactor factor, int id)> _factors = new List<(emBattleFactor, int)> {
            (emBattleFactor.RebornTwice, 0), // 党参乌鸡汤-复活苏生
            (emBattleFactor.GoodLuck, 1), // 糖醋鲤鱼-好运气
            (emBattleFactor.Cruel, 2), // 胡辣汤-疯狂匕首
            (emBattleFactor.FullHouse, 3), // 凉拌藕片-神灯
            (emBattleFactor.WeaponOverheating, 4), // 朝天椒炒肉-武器过热
            (emBattleFactor.SleepWalking, 5), // 致幻蘑菇-梦游
            (emBattleFactor.BadLuck, 6), // 三不沾-坏运气
            (emBattleFactor.ExEnemy, 7), // 苦瓜鸡蛋饼-全面戒备
            (emBattleFactor.Huge, 8), // 宝塔肉-巨大化
            (emBattleFactor.Tiny, 9), // 过桥排骨-缩小隧道
        };

        private readonly List<string> triggerNotEnoughGemTalks = new List<string>() {
            "npc/cooker_talk_5",
            "npc/cooker_talk_6",
            "npc/cooker_talk_7",
        };

        private readonly List<string> triggerAfterSuccessTalks = new List<string>() {
            "npc/cooker_talk_2",
            "npc/cooker_talk_3",
            "npc/cooker_talk_4",
        };

        private GameObject _foodProto;
        private void Awake() {
            _exclamationMark = transform.Find("exclamation_mark");
            _exclamationMark.gameObject.SetActive(false);
            _foodProto = ResourcesUtil.Load<GameObject>("RGPrefab/LevelObject/Food/food.prefab");
        }

        protected override void Start() {
            base.Start();
            SetRGRandomSeed(PlayerSaveData.Inst.cooker_random_seed);
        }

        protected override bool Triggerable(RGController controller) {
            triggerState++;
            bool result = GameUtil.IsSingleGame() && triggerState == 2 && RoomObjectManager.Inst.CanTakeFoodFromCooker && RGSaveManager.Inst.ConsumeGem(item_value, false);
            if (result) {
                SimpleEventManager.Raise(CookerCookEvent.UseCache());
                ConsumeStatistics.TrackConsumeType(item_value, emStatisticsType.Cook);
            }
            return result;
        }

        protected override void OnItemTriggerSuccess(RGController controller, string extraInfo = "") {
            base.OnItemTriggerSuccess(controller, extraInfo);
            // UICanvas.GetInstance().ShowTextTalk(controller.transform, GetStringWithColor(-item_value, ItemLevel.Gem), 2.5f, 1);
            StartCoroutine(CookFood(controller));
        }

        protected override void OnItemTriggerFail(RGController controller) {
            int index;
            switch (triggerState) {
                case 1:
                    if (!RoomObjectManager.Inst.CanTakeFoodFromCooker) {
                        index = Random.Range(0, triggerAfterSuccessTalks.Count);
                        UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get(triggerAfterSuccessTalks[index], triggerAfterSuccessTalks[index]), label_height, 1);
                        Track(false, "AlreadyGetFood");
                        HideTap(controller);
                    } else if (GameUtil.IsSingleGame()) {
                        var str = ScriptLocalization.Get("npc/cooker_talk_0", "{0}来点新菜品？");
                        str = str.Replace("{0}", "");
                        str = GetPaymentDialogText(str, item_value, ItemLevel.Gem);
                        UICanvas.GetInstance().ShowObjectInfo(transform.position + new Vector3(0, label_height, 0), str, true);
                    } else {
                        var str = ScriptLocalization.Get("I_single_game"); // 需要单机模式
                        UICanvas.GetInstance().ShowTextTalk(transform, str, label_height, 1f);
                        HideTap(controller);
                    }
                    break;
                case 2:
                    index = Random.Range(0, triggerNotEnoughGemTalks.Count);
                    UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get(triggerNotEnoughGemTalks[index], triggerNotEnoughGemTalks[index]), label_height, 1);
                    Track(false, "NotEnoughGem");
                    HideTap(controller);
                    break;
                default:
                    triggerState = 0;
                    break;
            }
        }

        private IEnumerator CookFood(RGController controller) {
            // 删除已有
            for (int i = 0; i < _factors.Count; i++) {
                if (!BattleData.data.factors.Contains(_factors[i].factor)) {
                    continue;
                }

                _factors.RemoveAt(i);
                i--;
            }

            if (_factors.Count == 0) {
                if(LogUtil.IsShowLog){LogUtil.LogError("食物效果全部已有");}
                yield break;
            }

            // 确定因子
            var randomIndex = rg_random.Range(0, _factors.Count);
            var item = _factors[randomIndex];
            RoomObjectManager.Inst.CanTakeFoodFromCooker = false;
            Track(true, item.factor.ToString());

            //生成小食
            var food = Instantiate(_foodProto, controller.hand.transform.parent);
            food.transform.Find("img").GetComponent<SpriteRenderer>().sprite = LoadFoodSprite(item.factor);

            food.transform.localPosition = new Vector3(0, 0.2f, 0);
            food.transform.localRotation = Quaternion.identity;
            food.transform.localScale = Vector3.one * 0.8f;

            UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get("npc/cooker_talk_1", "npc/cooker_talk_1"), 2.5f, 1);
            StartCoroutine(EatTalk(controller, item.factor));
            yield return new WaitForSeconds(1);
            Destroy(food);

            // 应用因子
            BattleData.data.SetFactor(item.factor, true, true);
            BattleData.data.ResetFactorsEffect();
            var factorChanger = new BattleFactorChanger();
            factorChanger.OnFactorChanged(item.factor, true);
            BattleData.data.ResetFactorsEffect();
            SimpleEventManager.Raise(new UpdateFactorBarEvent());
            UICanvas.GetInstance().ShowObjectWithImage(controller.transform, ChallengeInfo.info.GetSprite(item.factor), 2.25f, 1.5f);
            UICanvas.GetInstance().UpdatePauseInfo();
        }

        private IEnumerator EatTalk(RGController controller, emBattleFactor factor) {
            yield return new WaitForSeconds(0.15f);
            ShowTalk(controller.transform, ScriptLocalization.Get($"cooker/food_{GetFoodIndex(factor)}", $"cooker/food_{GetFoodIndex(factor)}"));
        }

        private Sprite LoadFoodSprite(emBattleFactor factor) {
            return ResourcesUtil.Load<Sprite>($"RGTexture/items/food/food_{GetFoodIndex(factor)}.png");
        }

        private int GetFoodIndex(emBattleFactor factor) {
            return _factors.Find(i => i.factor == factor).id;
        }

        private static void Track(bool success, string result) {
            TAUtil.Track("trigger_cooker", new Dictionary<string, object>() {
                {"trigger_cooker_success", success},
                {"trigger_cooker_result", result},
            });
        }
    }
}