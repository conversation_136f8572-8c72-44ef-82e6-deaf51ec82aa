using System.Collections.Generic;
using UnityEngine;

public class ObjectFertilizer : RGItem {
    public GameObject fertilizerWeapon;

    List<GameObject> fertilizers = new List<GameObject>();
    public int count {
        get {
            for (int i = fertilizers.Count - 1; i >= 0; i--) {
                if (!fertilizers[i]) {
                    fertilizers.RemoveAt(i);
                }
            }
            return ItemData.data.GetMaterialCount(ItemData.FertilizeName) - fertilizers.Count;
        }
    }

    public void RefreshAppearance() {
        transform.GetChild(0).gameObject.SetActive(count > 0);
        transform.GetChild(1).gameObject.SetActive(count <= 0);
    }

    private void OnWatchAdForFertilizer(WatchAdForFertilizerEvent e) {
        RefreshAppearance();
    }

    private void Awake() {
        SimpleEventManager.AddEventListener<WatchAdForFertilizerEvent>(OnWatchAdForFertilizer);
        RefreshAppearance();
    }

    protected override void OnDestroy() {
        SimpleEventManager.RemoveListener<WatchAdForFertilizerEvent>(OnWatchAdForFertilizer);
        DestroyAllPickedUpFertilizer();
    }

    private void PickUpFertilizer(RGController controller) {
        var weapon = Instantiate(
            fertilizerWeapon,
            transform.position,
            Quaternion.identity,
            RGGameSceneManager.GetInstance().temp_objects_parent);
        weapon.name = fertilizerWeapon.name;
        weapon.GetComponent<RGWeapon>().item_value = 100;
        fertilizers.Add(weapon);
        weapon.GetComponent<RGWeapon>().StatisticPickCount();
        controller.PickUpItem(weapon.transform);
        transform.GetChild(0).gameObject.SetActive(count > 0);
        transform.GetChild(1).gameObject.SetActive(count <= 0);
        RGMusicManager.GetInstance().PlayEffect(8);
    }

    protected override void OnItemTriggerSuccess(RGController controller, string extraInfo = "") {
        base.OnItemTriggerSuccess(controller, extraInfo);
        if (count > 0) {
            PickUpFertilizer(controller);
        } else {
            RGMusicManager.GetInstance().PlayEffect(8);
        }
    }

    public override string GetItemName() {
        return I2.Loc.ScriptLocalization.Get(ItemData.FertilizeName) + " × " + count;
    }

    public void DestroyPickedUpFertilizer(int destroyCount) {
        if (destroyCount >= fertilizers.Count) {
            DestroyAllPickedUpFertilizer();
            return;
        }

        for (int i = 0; i < destroyCount; i++) {
            var fertilizer = fertilizers[i];
            DestroyFertilizer(fertilizer);
        }
    }

    private void DestroyAllPickedUpFertilizer() {
        foreach (var fertilizer in fertilizers) {
            DestroyFertilizer(fertilizer);
        }
    }

    private void DestroyFertilizer(GameObject fertilizer) {
        if (fertilizer == null) {
            return;
        }

        var weapon = fertilizer.GetComponent<RGWeapon>();
        if (weapon.InHeroHand()) {
            var ctrl = (RGController)weapon.controller;
            ctrl.hand.DropWeapon(weapon);
            weapon.gameObject.SetActive(false);
            ctrl.SetItemtf(null);
        }
        Destroy(fertilizer, 0.1f);
    }
}