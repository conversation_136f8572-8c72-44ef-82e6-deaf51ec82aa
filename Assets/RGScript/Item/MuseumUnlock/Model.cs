using System;
using System.Collections;
using System.Collections.Generic;
using I2.Loc;
using MuseumUnlock;
using RGScript.Util;
using Sirenix.OdinInspector;
using UnityEngine;

namespace MuseumUnlock {
    [Serializable]
    public class UnlockItem {
        public string itemId;
        [ValueDropdown("RewardTypeDropDownList")]
        public string rewardType;
        public emPassGameLevel unlockLevel;
        public string unlockContent;

        public ValueDropdownList<string> RewardTypeDropDownList {
            get {
                var list = new ValueDropdownList<string>();
                foreach (var type in Const.rewardTypes) {
                    list.Add(type);
                }
                return list;
            }
        }
    }
    /// <summary>
    /// 用来吧unlockItem转换为便于解析和处理的类型
    /// </summary>
    public abstract class UnlockModel {
        public abstract Sprite sprite { get; }
        public abstract String rewardText { get; }
        protected string UnlockTipKey { get; }
        public UnlockItem Item { get; }

        public UnlockModel(UnlockItem item, string unlockTipKey) {
            Item = item;
            UnlockTipKey = unlockTipKey;
        }

        public bool CanUnlockItem(StatisticData data) {
            var obtainCount = data.GetObtainTime(Item.itemId);
            var enemyInfo = EnemyInfos.info.GetEnemyById(Item.itemId);
            var levelClause = ObjectExhibition.GetLevelClause(enemyInfo.rank, enemyInfo.rareLevel);
            var enemyLevel = 0;
            for (var i = 0; i < levelClause.Length; i++) {
                if (obtainCount >= levelClause[i]) {
                    enemyLevel = i;
                } else {
                    break;
                }
            }
            var passLevel =
                ObjectExhibition.GetEnemyLevel(enemyLevel, obtainCount, levelClause[levelClause.Length - 1]);
            return !HasUnlockItem() && passLevel >= Item.unlockLevel;
        }

        public string UnlockTipText {
            get {
                var unlockTip = ScriptLocalization.Get(UnlockTipKey);
                if (string.IsNullOrEmpty(unlockTip) && Debug.isDebugBuild) {
                    return $"{ScriptLocalization.Get(Item.itemId)}金框达成";
                }
                if (!string.IsNullOrEmpty(unlockTip)) {
                    try {
                        return string.Format(unlockTip, ScriptLocalization.Get(Item.itemId));
                    } catch (Exception e) {
                        if (Debug.isDebugBuild) {
                            Debug.LogError(e);
                        }

                        return "";
                    }
                }

                return "";
            }
        }
        
        public abstract void Unlock();

        protected abstract bool HasUnlockItem();
    }

    public sealed class SkinUnlockModel : UnlockModel {
        public emHero UnlockHero { get; }
        public int SkinIndex { get; }

        public SkinUnlockModel(UnlockItem item, string unlockTipKey) : base(item, unlockTipKey) {
            var contentSplit = Item.unlockContent.Split('_');
            UnlockHero = (emHero)int.Parse(contentSplit[0].Substring(1));
            SkinIndex = int.Parse(contentSplit[1].Substring(1));
        }

        public override Sprite sprite {
            get {
                return RGSaveManager.Inst.hero_skin_list[(int)UnlockHero].sprite_list[SkinIndex];
            }
        }
        
        

        public override string rewardText {
            get {
                return NameUtil.GetSkinName(UnlockHero, SkinIndex);
            }
        }

        public override void Unlock() {
            DataUtil.SetSkinUnlock(UnlockHero, SkinIndex, true);
        }

        protected override bool HasUnlockItem() {
            return DataUtil.GetSkinUnlock(UnlockHero, SkinIndex);
        }
    }
}
