using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// diff控制器
/// </summary>
public class RGContainerDiffCtrl: MonoBehaviour {
    private List<string> _itemNames = new List<string>();
    public bool AddItem(string name) {
        if (_itemNames.Contains(name)) {
            return false;
        }
        
        _itemNames.Add(name);
        return true;
    }

    public void Clear() {
        _itemNames.Clear();
    }
}
