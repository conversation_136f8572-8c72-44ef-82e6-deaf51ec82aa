using UnityEngine;
using I2.Loc;
public class TalkTriggerGarden : MonoBehaviour, ItemInterface {
    public string npc_name = "npc";
    public string[] talk_list;
    public int[] talk_time;
    public float height = 1.6f;

    void OnTriggerEnter2D(Collider2D other) {
        if (other.gameObject.CompareTag("Body_P")) {
            if (other.GetComponent<RGController>().IsLocalPlayer()) {
                other.GetComponent<RGController>().SetItemtf(transform);
                Vector3 tempv3 = transform.position;
                tempv3.y += height;
                var str = GetGardenUnlockTipString();
                UICanvas.GetInstance().ShowObjectInfo(tempv3, str, 0);
            }
        }
    }

    public static string GetGardenUnlockTipString() {
        if (GameUtil.IsPureMultiGame()) {
            return RGItemExtensions.GetPureMultiModeCantUseTip();
        }

        var statementsCount = StatisticData.data.GetEventCount(RGGameConst.ROOM_UNLOCK_STATEMENTS_COUNT);
        var str = ScriptLocalization.Get("tip/room_unlock_0", "进行{0}次关卡({1})");
        const int unlockCount = 3;
        str = str.Replace("{0}", $"{unlockCount}");
        str = str.Replace("{1}", $"{statementsCount}/{unlockCount}");
        return str;
    }

    void OnTriggerExit2D(Collider2D other) {
        if (other.gameObject.CompareTag("Body_P")) {
            if (other.GetComponent<RGController>().IsLocalPlayer()) {
                if (other.GetComponent<RGController>().CompareItem(transform)) {
                    other.GetComponent<RGController>().SetItemtf(null);
                    UICanvas.GetInstance().HideObjectInfo();
                }
            }
        }
    }
    public void ItemTrigger(RGController controller) {
        if (controller.IsLocalPlayer() && talk_list.Length > 0) {
            int r1 = Random.Range(0, talk_list.Length);
            RGMusicManager.GetInstance().PlayEffect(10);
            UICanvas.GetInstance().ShowTextTalk(transform.parent, ScriptLocalization.Get(talk_list[r1]),  height + 0.4f, talk_time[r1]);
            UICanvas.GetInstance().HideObjectInfo();
            controller.SetItemtf(null);
        } else {
            RGMusicManager.GetInstance().PlayEffect(10);
            UICanvas.GetInstance().HideObjectInfo();
            controller.SetItemtf(null);
        }
    }
    public int GetItemValue() {
        return 0;
    }
    public string GetItemName() {
        return "name";
    }
    public int GetItemLevel() {
        return 0;
    }
    public bool CanUse() {
        return true;
    }
    public void SyncItemTrigger(RGController controller, string extraInfo) {
    }
}
