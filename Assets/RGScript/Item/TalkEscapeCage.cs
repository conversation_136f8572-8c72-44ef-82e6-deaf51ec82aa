using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using I2.Loc;

public class TalkEscapeCage : MonoBehaviour {
    public string escape_talk;
    public Transform talk_transform;
    public float label_height = 2.5f;
    public float duration = 1f;

    public virtual void Start() {
        if (null == talk_transform) {
            talk_transform = transform;
        }
        //Invoke("ShowEscapeTalk", 1f);
        Invoke("ShowEscapeTalk", 0.5f);
    }

    public void ShowEscapeTalk() {
        string talk_content = ScriptLocalization.Get(escape_talk);
        if (string.IsNullOrEmpty(talk_content)) {
            talk_content = escape_talk;
        }
        UICanvas.GetInstance().ShowTextTalk(talk_transform, talk_content, label_height, duration);
        //Invoke("GetHelp", 2f);
        Invoke("GetHelp", 0.8f);
    }

    public virtual void GetHelp() {

    }
}
