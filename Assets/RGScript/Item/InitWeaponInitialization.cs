using System;
using UnityEngine;
using Sirenix.OdinInspector;

/// <summary>
/// 用于给初始武器动态调节字段值，放在需要调整rgweapon字段的prefab上并设置对应的英雄和等级
/// </summary>
[RequireComponent(typeof(WeaponExhibition))]
public class InitWeaponInitialization : MonoBehaviour {
	public emHero hero;
	public int update_level;
	public RGWeapon update_weapon;
	
	[InlineEditor(InlineEditorModes.SmallPreview)]
	public Sprite[] updateSprites;
	public bool should_update {
		get {
			try {
				return RGSaveManager.Inst.char_list[(int)hero].level >= update_level;
			} catch (System.Exception) {
				return false;
			}
		}
	}
	void Awake() {
		var bgSpriteRenderer = transform.Find("bg").GetComponent<SpriteRenderer>();
		var spriteMiddle = Vector3.up * bgSpriteRenderer.sprite.rect.height / bgSpriteRenderer.sprite.pixelsPerUnit * .5f;
		// var weaponIndex = StatisticData.data.GetInitWeapon((int)hero);
		var iconTransform = transform.Find("icon");
		var spriteRenderer = iconTransform.GetComponent<SpriteRenderer>();
		if (should_update) {
			GetComponent<WeaponExhibition>().forgeItem = update_weapon;
			spriteRenderer.sprite = updateSprites[0];
		}

		var weaponSprite = spriteRenderer.sprite;
		iconTransform.localPosition = spriteMiddle + new Vector3(
          (weaponSprite.pivot.x - 0.5f * weaponSprite.rect.width) / weaponSprite.pixelsPerUnit * iconTransform.localScale.x,
          (weaponSprite.pivot.y - 0.5f * weaponSprite.rect.height) / weaponSprite.pixelsPerUnit * iconTransform.localScale.y
        );
	}
}
