using RGScript.Data;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using static RGScript.Data.AchieveInfos;
using Random = UnityEngine.Random;

namespace RGScript.Item {
    public class TimeStopCircle : MonoBehaviour, OffensiveInterface {
        private struct BulletData {
            public Vector2 speed;
            public int camp;
            public RigidbodyType2D rigidbodyType;
        }

        public float checkSpaceTime = 0.5f;
        public float checkLimitTime = 0.375f;
        public float bulletCheckLimitTime = 0.375f;
        public float radius = 15;
        public float rotateSpeed = 15;
        public GameObject buff;
        public int limitDamageTo = -1;
        public float liveTime = 5;

        private static Dictionary<DamageCarrier, BulletData> carrierHash = new Dictionary<DamageCarrier, BulletData>();
        private Collider2D[] _results = new Collider2D[100];
        private GameObject _sourceObject;
        private int controlCounter;

        private void Start() {
            Destroy(gameObject, liveTime);
            transform.Rotate(Vector3.forward, Random.Range(0, 180));
        }

        private void FixedUpdate() {
            checkLimitTime -= Time.fixedDeltaTime;
            if (checkLimitTime <= 0) {
                checkLimitTime = checkSpaceTime;
                FindFollower();
            }

            bulletCheckLimitTime -= Time.fixedDeltaTime;
            if (bulletCheckLimitTime <= 0) {
                FindBullet();
            }
            CheckAchieve();
            transform.Rotate(Vector3.forward, rotateSpeed * Time.fixedDeltaTime);
        }

        private void OnDestroy() {
            foreach (var keyValue in carrierHash) {
                if (keyValue.Key != null) {
                    PlayDamageCarrier(keyValue.Key, keyValue.Value.camp == keyValue.Key.camp ? keyValue.Value.speed : -keyValue.Value.speed, keyValue.Value.rigidbodyType);
                }
            }
            carrierHash.Clear();
        }

        private void CheckAchieve() {
            //成就:硬控大师
            if (controlCounter + carrierHash.Count >= 99) {
                AchieveInfos.info.CheckUnlock(AchievementType.HardControlMaster, 0, "", 0);
            }
        }

        private void FindFollower() {
            controlCounter = 0;
            var size = Physics2D.OverlapCircleNonAlloc((Vector2)transform.position, radius, _results, LayerMask.GetMask("Body_E"));
            for (int i = 0; i < size; i++) {
                Collider2D reCollider = _results[i];
                if (reCollider.gameObject.TryGetComponent<RGEController>(out RGEController target)) {
                    if (target.dead) return;
                    controlCounter++;
                    if (target.transform.Find("buff_time_stop") == null) {
                        BuffEffectTrigger.EnemyAddBuff(_sourceObject, null, target.gameObject, buff, limitDamageTo);
                    }
                }
            }
        }

        private void FindBullet() {
            var size = Physics2D.OverlapCircleNonAlloc((Vector2)transform.position, radius, _results, LayerMask.GetMask("Bullet"));
            for (int i = 0; i < size; i++) {
                Collider2D reCollider = _results[i];
                if (reCollider.GetComponentInParent<DamageCarrier>() is { } damageCarrier and not Explode && !carrierHash.ContainsKey(damageCarrier)) {
                    carrierHash.Add(damageCarrier, StopDamageCarrier(damageCarrier));
                }
            }

            var position = transform.position;
            foreach (DamageCarrier damageCarrier in carrierHash.Keys.ToArray()) {
                if (damageCarrier == null) {
                    carrierHash.Remove(damageCarrier);
                } else if (!damageCarrier.gameObject.activeInHierarchy || Vector3.Distance(position, damageCarrier.transform.position) > radius) {
                    var data = carrierHash[damageCarrier];
                    PlayDamageCarrier(damageCarrier, data.camp == damageCarrier.camp ? data.speed : -data.speed, data.rigidbodyType);
                    carrierHash.Remove(damageCarrier);
                }
            }
        }

        private BulletData StopDamageCarrier(DamageCarrier damageCarrier) {
            var animators = damageCarrier.GetComponentsInChildren<Animator>();
            foreach (var animator in animators) {
                animator.enabled = false;
            }

            Vector2 speed = Vector2.zero;
            RigidbodyType2D rigidbodyType = RigidbodyType2D.Static;
            if (damageCarrier.TryGetComponent<Rigidbody2D>(out Rigidbody2D rigidbody2D)) {
                if (rigidbody2D.velocity.magnitude >= 1) {
                    speed = rigidbody2D.velocity;
                } else {
                    speed = Mathf.Max(damageCarrier.bulletInfo.speed, 1) * damageCarrier.transform.right;
                }
                rigidbodyType = rigidbody2D.bodyType;
                if (rigidbody2D.bodyType == RigidbodyType2D.Dynamic) {
                    rigidbody2D.bodyType = RigidbodyType2D.Static;
                }
            }

            if (damageCarrier.TryGetComponent<RGBullet>(out RGBullet rgBullet)) {
                rgBullet.StopBulletMover();
            }

            return new BulletData() { speed = speed, camp = damageCarrier.camp, rigidbodyType = rigidbodyType };
        }

        private void PlayDamageCarrier(DamageCarrier damageCarrier, Vector2 speed, RigidbodyType2D rigidbodyType) {
            var animators = damageCarrier.GetComponentsInChildren<Animator>();
            foreach (var animator in animators) {
                animator.enabled = true;
            }

            if (IsFilterBullet(damageCarrier)) {
                return;
            }
            
            if (damageCarrier.TryGetComponent<Rigidbody2D>(out Rigidbody2D rigidbody2D)) {
                rigidbody2D.bodyType = rigidbodyType;
                if (damageCarrier.gameObject.activeInHierarchy) {
                    damageCarrier.StartCoroutine(SetSpeed(rigidbody2D, speed));
                }
            }

            if (damageCarrier.TryGetComponent<RGBullet>(out RGBullet rgBullet)) {
                rgBullet.StartBulletMover();
            }
        }

        IEnumerator SetSpeed(Rigidbody2D rigidbody2D, Vector2 speed) {
            yield return 0;
            if (rigidbody2D != null) {
                rigidbody2D.velocity = speed;
            }
        }

        public void SetSourceObject(GameObject value) {
            _sourceObject = value;
        }
        public GameObject GetSourceObject() {
            return _sourceObject;
        }

        private bool IsFilterBullet(DamageCarrier damageCarrier) {
            return damageCarrier is BulletAstrolabe;
        }
    }
}