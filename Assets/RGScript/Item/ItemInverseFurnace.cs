using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// 还原熔炉
/// </summary>
public class ItemInverseFurnace : RGStateItem {
    Transform item;
    int weaponSortOrder = 1;
    public AudioClip drop_clip;
    public AudioClip rise_clip;

    private void Awake() {
        item = transform.Find("item");
        totalState = 2;
    }

    protected override bool Triggerable(RGController controller) {
        return controller.hand.front_weapon != null;
    }

    protected override void OnItemTriggerState(RGController controller, int state) {
        switch (state) {
            case 1:
                ShowTap("item/furence_inverse_talk", true);
                break;
            default:
                break;
        }
    }

    protected override void OnItemTriggerFail(RGController controller) {
        if (controller.IsLocalPlayer()) {
            base.OnItemTriggerFail(controller);
            if(controller.hand.transform.childCount == 0)
                ShowTalk(controller.transform, I2.Loc.ScriptLocalization.Get("I_no_weapon"));
            else
                ShowTalk(controller.transform, I2.Loc.ScriptLocalization.Get("item_is_not_weapon", "这不是武器"));
        }
    }

    RGWeapon oldWeapon;

    protected override void OnItemTriggerSuccess(RGController controller, string extraInfo = "") {
        base.OnItemTriggerSuccess(controller, extraInfo);
        if (controller.hand.front_weapon && !controller.hand.front_weapon.HasTag(emWeaponTag.NotReforge)) {
            oldWeapon = controller.hand.DropWeapon(true);
        }

        if (!oldWeapon) {
            ShowTalk(controller.transform, I2.Loc.ScriptLocalization.Get("object/furnace_invalid"));
            RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Message);
            return;
        }

        if (oldWeapon.weapon_item) {
            oldWeapon.weapon_item.Drop();
        }
        
        if (oldWeapon.weapon_item_activity) {
            oldWeapon.weapon_item_activity.Drop();
        }

        oldWeapon.GetComponent<RGWeapon>().SpriteLayerSetting(weaponSortOrder, "Wall");
        oldWeapon.transform.SetParent(item);
        oldWeapon.transform.localPosition = Vector3.zero;
        oldWeapon.GetComponent<RGWeapon>().AdjustAnchor();
        GetComponent<Animator>().SetTrigger("open");
        oldWeapon.GetComponent<Collider2D>().enabled = false;
        GetComponent<Collider2D>().enabled = false;
        RGMusicManager.GetInstance().PlayEffect(drop_clip);
        GameUtil.RaseTriggerSuccess(this.GetType(), transform);
    }

    public void DropWeaponComplete() {
        Dictionary<string, int> materials;
        Dictionary<string, int> dropMaterials = new();
        materials = DataUtil.GetWeaponForgeMaterials(oldWeapon);
        
        // Remove gem
        foreach (var pair in materials.Where(pair => pair.Key == ItemData.GemName)) {
            materials.Remove(pair.Key);
            break;
        }

        if (dropMaterials.Count == 0 && materials.Count > 0) {
            foreach (var pair in materials) {
                float count = pair.Value / 2f;
                float probability = count - (int)count;
                if (probability > 0) {
                    count = (int)count + (rg_random.Range(0, 1f) < probability ? 1 : 0);
                }

                if (count >= 1) {
                    dropMaterials[pair.Key] = Mathf.CeilToInt(count);
                }
            }

            if (dropMaterials.Count == 0) {
                //熔炉保底逻辑
                var matList = materials.Keys.ToList();
                matList.Sort(((o, o1) => String.Compare(o, o1, StringComparison.Ordinal)));
                var mat = matList.GetRandomObject(rg_random);
                var count = materials[mat];
                dropMaterials[mat] = Mathf.Max(1, Mathf.CeilToInt(count / 2f));
            }
        }

        if (GameUtil.NotNeedDropMat()) {
            //沙盒模式不掉落材料
            dropMaterials.Clear();
        }

        foreach (var pair in dropMaterials) {
            var materialGo = ResourcesUtil.CreateItem(pair.Key, item);
            materialGo.transform.localPosition = Vector3.zero;
            materialGo.GetComponent<BoxCollider2D>().enabled = false;
            materialGo.GetComponent<BoxCollider2D>().size += new Vector2(0, 1.5f);
            materialGo.GetComponent<ItemPickable>().count = Mathf.CeilToInt(pair.Value);
            oldWeapon.GetComponent<RGWeapon>().SpriteLayerSetting(weaponSortOrder, "Wall");
        }

        Destroy(oldWeapon.gameObject);
        RGMusicManager.GetInstance().PlayEffect(rise_clip);
    }

    public void RiseWeaponComplete() {
        for (int i = 0; i < item.childCount; i++) {
            item.GetChild(i).GetComponent<Collider2D>().enabled = true;
            int r3 = item.childCount == 1 ? 0 : 3 + item.childCount;
            if (item.GetChild(i).GetComponent<ItemPickable>()) {
                //float rad = (i / (float)item.childCount) * 360 * Mathf.Deg2Rad;
                float rad = ((i - (item.childCount % 2 == 0 ? 0 : 0.25f)) / (float)item.childCount) * 360 * Mathf.Deg2Rad;
                item.GetChild(i).GetComponent<ItemPickable>().GetForce(new Vector2(Mathf.Cos(rad), Mathf.Sin(rad)), r3);
            }
        }

        for (int i = item.childCount - 1; i >= 0; i--) {
            item.GetChild(i).transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;
        }
    }
}