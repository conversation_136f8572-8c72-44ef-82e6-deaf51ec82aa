using MapSystem;
using System;
using UnityEngine;

public class ObjectSpeedUp : RoomObject {
    public float speed_rate = 0.4f;
    public bool speed_down;
    
    private void OnTriggerEnter2D(Collider2D other) {
        if (other.gameObject.layer == LayerMask.NameToLayer("Body_E")) {
            RGEController rgeController = other.GetComponent<RGEController>();
            if (rgeController != null) {
                rgeController.attributeProxy.ChangeSpeed("speedup", speed_rate / 2, 1.5f);
            }
        }

        if (!speed_down || !GameUtil.GetBattleData(other).HasShieldGas()) {
            if (other.gameObject.layer == LayerMask.NameToLayer("Body_P")) {
                RGController rgController = other.gameObject.GetComponent<RGController>();
                if (rgController != null && (!speed_down || !rgController.ImmuneGas))
                    rgController.attribute.ChangeSpeed("speedup", speed_rate, 1.5f);
            }
        }
    }
}
