using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Random = UnityEngine.Random;

public class RoomTable : MonoBehaviour
{
    
    private void Awake() {
        if (RGGameConst.GameConfig.EnableMahjongTable && Random.Range(0, 20) < 1) {
            ChangeToMahjong();
        } else if (ShowInChannelConfig.ConfigValid(ShowInChannelConfig.emChannelConfig.FN43Box, false) &&
                   NetTime.Time < new DateTime(2025, 7, 20)) {
            //满足开启4399盒子蛋糕的条件
            //麻将和4399盒子蛋糕不共存
            ChangeTo4399BoxCake();
        }
    }

    private void ChangeTo4399BoxCake() {
        transform.GetChild(0).gameObject.SetActive(false);
        transform.GetChild(1).gameObject.SetActive(false);
        transform.GetChild(2).gameObject.SetActive(true);
    }

    int[] mahjongHeros = new int[] { (int)emHero.Assassin, (int)emHero.Alchemist, (int)emHero.Viking, (int)emHero.Elves };
    private void ChangeToMahjong() {
        bool allUnlock = true;
        foreach (var heroIndex in mahjongHeros) {
            allUnlock = allUnlock && DataUtil.GetHeroUnlock((emHero)heroIndex);
        }
        if (allUnlock) {//开启麻将桌
            transform.GetChild(0).gameObject.SetActive(false);
            transform.GetChild(1).gameObject.SetActive(true);
            transform.GetChild(2).gameObject.SetActive(true);
            foreach (var heroIndex in mahjongHeros) {
                string heroName = "c" + heroIndex.ToString().PadLeft(2, '0');
                var talkTrigger = GameObject.Find("characters/" + heroName + "/talk").GetComponent<CharacterTalkTrigger>();
                talkTrigger.isMahJongTable = true;
            }
        }
    }
}
