using System;
using System.Collections;
using System.Collections.Generic;

namespace RGScript.Item {
    using UnityEngine;

    public class LineGenerator : MonoBehaviour, IPrefabPoolObject, OffensiveInterface {
        [Header("端点设置")] public Transform startPoint; // 线段起点
        public Transform endPoint; // 线段终点

        [Header("预制体设置")] public GameObject segmentPrefabA; // 图片A的预制体
        public GameObject segmentPrefabB; // 图片B的预制体
        [Header("火焰设置")]
        public GameObject RoadFirePrefab; // 火焰的预制体

        [Header("生成选项")] [Tooltip("如果为true，将在运行时动态更新线段")]
        public bool updateInRealtime = false;

        private GameObject lineContainer; // 用于存放所有线段分段的容器

        public float OffsetDistance;
        public float nodeRunSpeed;

        private List<SpriteRenderer> _nodeAnimations;
        private Queue<GameObject> _nodeACache;
        private Queue<GameObject> _nodeBCache;

        public Vector3 startPointPos, endPointPos;
        private bool _alreadyFire;
        private GameObject sourceObj;
        private const string NodeA = "NodeA";
        private const string NodeB = "NodeB";
        /// <summary>
        /// 根据后面桶子的连线自定义颜色
        /// </summary>
        public bool CustomColor;
        public Color IceBoxColor;
        public Color FireBoxColor;
        public Color EletricBoxColor;
        private Color targetColor;

        private void Awake() {
            _nodeAnimations = new List<SpriteRenderer>();
            _nodeACache = new Queue<GameObject>();
            _nodeBCache = new Queue<GameObject>();
        }

        public void SetBoxCustomColor(RGCaptainBox.CaptainBoxType boxType) {
            if (!CustomColor) {
                return;
            }
            
            switch (boxType) {
                case RGCaptainBox.CaptainBoxType.Ice:
                    targetColor = IceBoxColor;
                    break;
                case RGCaptainBox.CaptainBoxType.Fire:
                    targetColor = FireBoxColor;
                    break;
                case RGCaptainBox.CaptainBoxType.Electric:
                    targetColor = EletricBoxColor;
                    break;
            }
        }

        public void DoFire(Transform from) {
            if (_alreadyFire) {
                return;
            }
            _alreadyFire = true;
            StartCoroutine(ShowFire(from));
        }

        private bool FromStartPos(Transform from) {
            // 判定from的位置和startPos, endPos哪个更近，如果离startPos更近就返回true，反之返回false
            var position = from.position;
            float distanceToStart = Vector3.Distance(position, startPointPos);
            float distanceToEnd = Vector3.Distance(position, endPointPos);
            return distanceToStart < distanceToEnd;
        }

        private IEnumerator ShowFire(Transform from) {
            var frame = new WaitForSeconds(nodeRunSpeed);
            Transform targetPoint;
            if (FromStartPos(from)) {
                // 从起点开始播放动画
                for (int i = 0; i < _nodeAnimations.Count; i++) {
                    FireNode(i);
                    yield return frame;
                }

                targetPoint = endPoint;
            } else {
                // 从终点开始播放动画
                for (int i = _nodeAnimations.Count - 1; i >= 0; i--) {
                    FireNode(i);
                    yield return frame;
                }
                
                targetPoint = startPoint;
            }

            if (targetPoint != null) {
                RGBox rgBox = targetPoint.GetComponent<RGBox>();
                if (rgBox != null) {
                    rgBox.Hit(5, sourceObj, 1);
                }
            }
            PrefabPool.Inst.Store(this.gameObject);
        }

        private void FireNode(int i) {
            var node = _nodeAnimations[i];
            node.enabled = false;
            
            var tempCreation=  PrefabPool.Inst.Take(RoadFirePrefab, RGGameSceneManager.Inst.temp_objects_parent);
            var transform1 = node.transform;
            if (CustomColor) {
                tempCreation.transform.Find("normal").GetComponent<SpriteRenderer>().color = targetColor;
            }
            tempCreation.transform.position = transform1.position;
            tempCreation.transform.rotation = transform1.rotation;
            var bulletGas = tempCreation.GetComponent<BulletGas>();
            if (bulletGas) {
                bulletGas.damage = 5 ;
                bulletGas.duration = 10;
                bulletGas.camp = 1;
                bulletGas.ResetState();
            }

            bulletGas.transform.localScale = Vector3.one;
            var ppo = tempCreation.GetComponent<IPrefabPoolObject>();
            if (ppo != null) {
                ppo.OnTaken();
            }
        }

        void Update() {
            if (updateInRealtime) {
                GenerateLine();
            }
        }

        /// <summary>
        /// 生成线段的核心方法
        /// </summary>
        public void GenerateLine() {
            if (!startPoint || !endPoint || !segmentPrefabA || !segmentPrefabB) {
                Debug.LogError("请在Inspector中设置所有必要的字段！");
                return;
            }
            

            if (lineContainer == null) {
                lineContainer = new GameObject("GeneratedLineSegments");
                lineContainer.transform.SetParent(this.transform); // 将容器作为子对象，方便管理
            }

            // 2. 计算线段的向量、方向、长度和旋转角度
            startPointPos = startPoint.position;
            endPointPos = endPoint.position;
            Vector3 vector = endPointPos - startPointPos;
            Vector3 direction = vector.normalized;
            // float angle = Vector2.Angle(Vector2.up, new Vector2(-direction.y, direction.x)) -
            //               (direction.x < 0 ? 180 : 0);
            var offset = OffsetDistance ;
            var startPos = startPointPos + direction * offset;
            // var endPos = endPointPos - direction * offset;
            float distance = vector.magnitude - 2 * offset;

            // 3. 获取单个图片分段的宽度
            // 我们假设A和B宽度相同，并且使用Sprite的Bounds来获取世界单位下的宽度
            // 注意：这要求预制体的缩放(scale)为1
            float segmentWidth = segmentPrefabA.GetComponent<SpriteRenderer>().bounds.size.x;
            if (segmentWidth <= 0) {
                Debug.LogError("分段预制体的宽度为0，无法生成线段。请检查Sprite是否正确设置。");
                return;
            }

            // 4. 循环生成和排列图片
            float currentDistance = 0;
            bool usePrefabA = true; // 用于交替的布尔开关

            while (currentDistance + segmentWidth <= distance) {
                // 计算当前分段的位置
                // 我们从起点开始，沿着方向移动 currentDistance + 半个分段宽度 的距离
                Vector3 segmentPosition = startPos + direction * (currentDistance + segmentWidth / 2f);

                // 实例化预制体
                GameObject segment = GetNodeFromCache(usePrefabA, segmentPosition);
                
                // segment.transform.right = direction.x > 0 ? direction : -direction;

                var spriteRender = segment.GetComponent<SpriteRenderer>();
                if (CustomColor) {
                    spriteRender.color = targetColor;
                }
                _nodeAnimations.Add(spriteRender);

                // 将生成的分段放入容器中
                segment.transform.SetParent(lineContainer.transform);

                // 更新下一个分段的起始距离
                currentDistance += segmentWidth;
                // 切换到另一个预制体
                usePrefabA = !usePrefabA;
            }
        }

        private GameObject GetNodeFromCache(bool usePrefabA, Vector3 segmentPosition) {
            var nodeQueue = usePrefabA ? _nodeACache: _nodeBCache;
            if (nodeQueue.Count > 0) {
                var node = nodeQueue.Dequeue();
                node.gameObject.SetActive(true);
                node.GetComponent<SpriteRenderer>().enabled = true;
                node.transform.position = segmentPosition;
                return node;
            } else {
                var nodePrefab = usePrefabA ? segmentPrefabA : segmentPrefabB;
                var node = Instantiate(nodePrefab, segmentPosition, Quaternion.identity);
                node.name = usePrefabA ? NodeA : NodeB;
                return node;
            }
        }

        private void OnDisable() {
            foreach (var node in _nodeAnimations) {
                node.gameObject.SetActive(false);
                if (node.name == NodeA) {
                    _nodeACache.Enqueue(node.gameObject);
                }else if (node.name == NodeB) {
                    _nodeBCache.Enqueue(node.gameObject);
                }
            }
            _nodeAnimations.Clear();
        }

        public void OnTaken() {
            _alreadyFire = false;
        }


        public void SetSourceObject(GameObject value) {
            sourceObj = value;
        }

        public GameObject GetSourceObject() {
            return sourceObj;
        }
    }
}