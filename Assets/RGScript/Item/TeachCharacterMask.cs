using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class TeachCharacterMask : MonoBehaviour
{
    public void Init() {
        // var c = RGGameSceneManager.Inst.controller;
        // var sprite = CharacterSprites
        //     .GetSpriteModel(
        //         c.role_attribute.c_index,
        //         c.GetSkinIndex()
        //     ).idleSprites[0];
        // GetComponent<SpriteRenderer>().sprite = sprite;
        // transform.Find("activeEffect").GetComponent<SpriteRenderer>().sprite = sprite;
        GetComponent<Animator>().SetBool("active", true);
    }

    private void OnTriggerEnter2D(Collider2D other) {
        if (other.CompareTag("Body_P"))
            Destroy(gameObject);
    }
}
