using System;
using UnityEngine;

/// <summary>
/// 天赋书, 替换一个buff，一般卖出去后立马destroy
/// ballbuff, buffball, 天赋球
/// </summary>
public class ItemBuffBookReplace : ItemBuffBook {
    public emBuff oldBuff;

    private void Awake() {
        label_height = 0f;
    }

    public override void OnSetRGRandomSeed(int seed) {
        // base.OnSetRGRandomSeed(seed);
    }

    protected override void AfterOnTriggerSuccess(RGController controller) {
        if (buff == emBuff.None || oldBuff == emBuff.None) {
            Debug.LogError("oldBuff or newBuff is none");
            return;
        }

        BattleData.data.ReplaceBuff(buff, oldBuff, true);

        RGMusicManager.GetInstance().PlayEffect(RGMusicManager.emEffect.Buy);
        //RGGameProcess.Inst.SaveBuffChoice();//注释防止刷关
        UICanvas.GetInstance().ShowBuff(controller.transform, buff, 2.25f, 1.5f);
        UICanvas.GetInstance().UpdatePauseInfo();
        Destroy(gameObject);
    }

    public override void ItemTrigger(RGController controller) {
        OnItemTriggerSuccess(controller);
    }
}