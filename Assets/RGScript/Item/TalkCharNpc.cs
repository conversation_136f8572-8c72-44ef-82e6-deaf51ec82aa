using ChillyRoom.Services.Core.Libs;
using I2.Loc;
using UnityEngine;
using DG.Tweening;
using MapSystem;
using RGScript.Character;
using RGScript.Character.Player;
using RGScript.Character.Player.SkinSkillEffect;
using RGScript.Data;
using RGScript.Data.YearReview;
using RGScript.Map;
using RGScript.Util;
using RGScript.Util.AssetBundle;
using RGScript.Weapon;
using Sirenix.OdinInspector;
using SoulKnight.Runtime.RandomObject;
using SoulKnight.Runtime.Skill;
using UnityEngine.Serialization;
using Util.TAUtil;

public class TalkCharNpc : Sirenix.OdinInspector.SerializedMonoBehaviour, ItemInterface {
    public enum CharNpcType {
        MERCENARY,
        WEAPON,
        ENERGY,
        POT,
        OBJECT,
        GATE,
        TICKEY,
        HP,
        WOLF,
        SEED,
        MATERIAL,// 矿工
        DRINK,
        EQUIPMENT,  //配件
        BATTLE_FACTOR,//挑战因子
        MOUNT,//坐骑
        DEMON,//恶魔
        AIRBENDER,//气宗打通任督二脉
        Gift, // 陷阱大师
        ElementalAreaE<PERSON><PERSON><PERSON><PERSON>,// 皮套王子
        <PERSON>,
        Astrologist,
        TenCriticalBuff,//武斗家
        Joker,
        Bard,  //吟游诗人
        FocusOnBuff,//枪手
        AiGirlWeapon,//机械姬的武器池
    }
    public CharNpcType char_npc_type;
    public AudioClip audio_clip;
    public int char_index;
    public string talk_name;
    public string talk_string1;
    public string talk_string2;
    public string talk_string3;
    int state = 0;
    bool can_use = true;
    bool has_trigger = false;
    RGRandom rg_random = new RGRandom();
    public GameObject the_object;
    public int item_value;
    
    [SerializeField]
    [HideLabel]
    private RandomObjectMaker randomObject = new();

    void Awake() {
        if (char_npc_type == CharNpcType.BATTLE_FACTOR) {//警官播放坐下动画
            GetComponentInParent<Animator>()?.SetBool("seat", true);
        }
    }

    void Start() {
        rg_random.SetRandomSeed(RGGameInfo.Inst.SampleRandomSeed);
        if (char_npc_type != CharNpcType.GATE) {
            //多人游戏最后一关不出现吸血鬼
            return;
        }

        var cantCreateGate = GameUtil.IsMultiGame() &&
                             RGGameProcess.Inst.this_index == LevelSelector.GetMaxLevelIndex(BattleData.data.gameMode) &&
                             BattleData.data.IsNormalMode && !BattleData.data.IsLoopTravel;
        if (cantCreateGate) {
            gameObject.SetActive(false);
        }
    }

    void OnTriggerEnter2D(Collider2D other) {
        if (other.gameObject.CompareTag("Body_P")) {
            other.GetComponent<RGController>().SetItemtf(transform);
            if (other.GetComponent<RGController>().IsLocalPlayer()) {
                Vector3 tempv3 = transform.position;
                tempv3.y += 2f;
                UICanvas.GetInstance().ShowObjectInfo(tempv3, ScriptLocalization.Get(talk_name), 0);
            }
            state = 0;
        }
    }

    void OnTriggerExit2D(Collider2D other) {
        if (other.gameObject.CompareTag("Body_P")) {
            if (other.GetComponent<RGController>().CompareItem(transform)) {
                other.GetComponent<RGController>().SetItemtf(null);
                if (other.GetComponent<RGController>().IsLocalPlayer()) {
                    UICanvas.GetInstance().HideObjectInfo();
                }
            }
        }
    }

    public void ItemTrigger(RGController controller) {
        if (controller.IsLocalPlayer()) {
            if (DataUtil.GetHeroUnlock((emHero)char_index)) {
                RGMusicManager.GetInstance().PlayEffect(10);
                if (char_npc_type == CharNpcType.MERCENARY && BattleData.data.CompareFactor(emBattleFactor.AllAlone)) {
                    Vector3 tempv3 = transform.position;
                    tempv3.y += 2f;
                    controller.SetItemtf(null);
                    UICanvas.GetInstance().ShowTextTalk(controller.transform, I2.Loc.ScriptLocalization.Get("I_allalone"), 2.5f, 1f);
                    UICanvas.GetInstance().HideObjectInfo();
                    return;
                }
                if (has_trigger) {
                    controller.SetItemtf(null);
                    UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get(talk_string2), 2.5f, 1);
                    UICanvas.GetInstance().HideObjectInfo();
                } else if (state == 0) {
                    Vector3 tempv3 = transform.position;
                    tempv3.y += 2f;
                    if (item_value == 0) {
                        if (controller && char_npc_type == CharNpcType.BATTLE_FACTOR) {//警官,动态更换靓仔/靓女
                            UICanvas.GetInstance().ShowObjectInfo(tempv3, 
                                ScriptLocalization.Get(talk_string1).Replace("{pretty}", NameUtil.GetPrettyName(controller.GetHeroType())), 0);
                        } else {
                            UICanvas.GetInstance().ShowObjectInfo(tempv3, ScriptLocalization.Get(talk_string1), 0);
                        }
                    } else {
                        string talkText = ScriptLocalization.Get(talk_string1);
                        string priceText = "<color=#ffff00ff>" + GetItemValue() + "</color>";
                        string dialog = LanguageUtil.PriceRTL ? priceText + " " + talkText : talkText + " " + priceText;
                        UICanvas.GetInstance().ShowObjectInfo(tempv3, dialog, 0);
                    }
                    state++;
                } else {
                    bool triggerable = true;
                    if (item_value > 0 && RGGameProcess.Inst.coin_value < item_value) {
                        UICanvas.Inst.ShowTextTalk(transform, ScriptLocalization.Get("I_no_money"), 2.5f, 1f);
                        triggerable = false;
                    }
                    if (char_npc_type == CharNpcType.EQUIPMENT) {
                        var _rg_random = new RGRandom();
                        _rg_random.SetRandomSeed(RGGameInfo.Inst.SampleRandomSeed);
                        if (!controller.hand.front_weapon ||
                            !new WeaponItemFactory(WeaponItemStore.level_weight, _rg_random).HasWeaponItem(controller.hand.front_weapon, _rg_random, WeaponItemFactory.NormalWeaponItemFilter)) {
                            controller.SetItemtf(null);
                            UICanvas.GetInstance().ShowTextTalk(transform, I2.Loc.ScriptLocalization.Get("Character5_talk3"), 2.5f, 1f);
                            UICanvas.GetInstance().HideObjectInfo();
                            triggerable = false;
                        }
                    }
                    if (triggerable) {
                        if (NetControllerManager.Inst.playerCount == 0) {
                            UICanvas.GetInstance().HideObjectInfo();
                            TriggerSuccess(controller);
                        } else {
                            MessageManager.Inst.SendItemTriggerMessage(RGGetPath.GetNetId(transform), NetControllerManager.Inst.localNetId);
                        }
                    } else {
                        UICanvas.GetInstance().HideObjectInfo();
                    }
                    controller.SetItemtf(null);
                }
            } else {
                RGMusicManager.GetInstance().PlayEffect(10);
                UICanvas.GetInstance().ShowTempMessage("Character_lock", 1);
            }
        } else {
            controller.SetItemtf(null);
        }
    }

    public void SyncItemTrigger(RGController controller, string extraInfo) {
        if (controller.IsLocalPlayer()) {
            UICanvas.GetInstance().HideObjectInfo();
        }
        TriggerSuccess(controller);
    }

    void TriggerSuccess(RGController controller) {
        if (char_npc_type == CharNpcType.MERCENARY) {
            GetMerenary(controller);
        } else if (char_npc_type == CharNpcType.WEAPON) {
            GetWeapon(controller);
        } else if (char_npc_type == CharNpcType.ENERGY) {
            GetEnergy(controller);
        } else if (char_npc_type == CharNpcType.POT) {
            GetPot(controller);
        } else if (char_npc_type == CharNpcType.OBJECT) {
            GetObject(controller);
        } else if (char_npc_type == CharNpcType.GATE) {
            GetGate(controller);
        } else if (char_npc_type == CharNpcType.TICKEY) {
            GetTickey(controller);
        } else if (char_npc_type == CharNpcType.HP) {
            GetHP(controller);
        } else if (char_npc_type == CharNpcType.WOLF) {
            GetWolf(controller);
        } else if (char_npc_type == CharNpcType.SEED) {
            GetSeed(controller);
        } else if (char_npc_type == CharNpcType.MATERIAL) {
            GetMaterial(controller);
        } else if (char_npc_type == CharNpcType.DRINK) {
            GetDrink(controller);
        } else if (char_npc_type == CharNpcType.EQUIPMENT) {
            GetEquipment(controller);
        } else if (char_npc_type == CharNpcType.BATTLE_FACTOR) {
            GetBattleFactor(controller);
        } else if (char_npc_type == CharNpcType.MOUNT) {
            GetMount(controller);
        } else if (char_npc_type == CharNpcType.DEMON) {
            GetDemon(controller);
        } else if (char_npc_type == CharNpcType.AIRBENDER) {
            GetAirbender(controller);
        }else if (char_npc_type == CharNpcType.Gift) {
            GetGift(controller);
        }else if (char_npc_type == CharNpcType.ElementalAreaEnhanceBuff) {
            GetElementalAreaEnhanceBuff(controller);
        }else if (char_npc_type == CharNpcType.TenCriticalBuff) {
            GetTenCriticalBuff(controller);
        }else if (char_npc_type == CharNpcType.Doctor) {
            GetDoctor(controller);
        }else if (char_npc_type == CharNpcType.Astrologist) {
            GetAstrologist(controller);
        }else if (char_npc_type == CharNpcType.Joker) {
            GetJoker(controller);
        }else if (char_npc_type == CharNpcType.Bard) {
            GetBardBuff(controller);
        }else if (char_npc_type == CharNpcType.FocusOnBuff) {
            GetFocusOnBuff(controller);
        }else if (char_npc_type == CharNpcType.AiGirlWeapon) {
            GetAiGirlWeapon(controller);
        }
        
        if (controller.IsLocalPlayer()) {
            var consume = item_value;
            RGGameProcess.Inst.ConsumeCoin(consume, emStatisticsType.CharNpc);
            if (RGGameProcess.Inst.coin_value < 0)
                RGGameProcess.Inst.coin_value = 0;
            if (consume > 0) {
                UICanvas.GetInstance().ShowTextTalk(controller.transform, $"<color=#ffff00ff>-{consume}</color>", 2.5f, 1f);
                UICanvas.GetInstance().UpdateCoin();
            }
        }
    }

    void GetMerenary(RGController controller) {
        UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get(talk_string2), 2.5f, 1);
        transform.parent.GetComponent<RGPetController>().SetMaster( controller.transform);
        foreach(var mercenary in transform.parent.GetComponentsInChildren<NpcMercenaryController>()){
            mercenary.GetMercenary(true, false);
        }
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        Destroy(gameObject);
    }

    void GetWeapon(RGController controller) {
        int temp_level = MapManager.Instance.ChestLevel + 1;
        if (temp_level > 5)
            temp_level = 5;
        string weaponName = WeaponDropInfo.Instance.GetWeaponWithDropLevel(temp_level, rg_random, null, true);
        var rgWeapon = WeaponFactory.CreateWeapon(weaponName, emWeaponSource.LevelNpc);
        rgWeapon.name = weaponName;
        rgWeapon.name = weaponName;
        rgWeapon.transform.position = controller.transform.position;
        UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get(talk_string2), 2.5f, 1);
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        has_trigger = true;
    }
    
    void GetAiGirlWeapon (RGController controller) {
        //如果拥有只能使用近战武器因子，则使用默认随机武器规则
        if (BattleData.data.CompareFactor(emBattleFactor.MelleOnly)) {
            GetWeapon(controller);
            return;
        }
        var randomObjectMaker = new RandomObjectMaker("aigirl_npc_weapons");
        var weaponName = randomObjectMaker.GetObjectDistributionList().GetRandomObject(rg_random).gameObject.name;
        var rgWeapon = WeaponFactory.CreateWeapon(weaponName, emWeaponSource.LevelNpc);
        rgWeapon.name = weaponName;
        rgWeapon.transform.position = controller.transform.position;
        UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get(talk_string2), 2.5f, 1);
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        has_trigger = true;
    }
    void GetHP(RGController controller) {
        controller.role_attribute.RestoreHealth(4);
        UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get(talk_string2), 2.5f, 1);
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        has_trigger = true;
    }
    void GetEnergy(RGController controller) {
        controller.attribute.RestoreEnergy(200);
        UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get(talk_string2), 2.5f, 1);
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        has_trigger = true;
    }

    void GetWolf(RGController controller) {
        GameObject temp_obj =
            Instantiate(the_object, (transform.position + new Vector3(0.5f, 0, 0)), Quaternion.identity) as GameObject;
        var component = temp_obj.AddComponent<FoceSkinChangerIndex>();
        component.skillIndex = 0;
        component.skinIndex = RGSaveManager.Inst.GetCurrentSkin(emHero.Druid);
        temp_obj.GetComponent<RGPetController>().SetMaster(controller.transform);
        if (component.skinIndex == 13) {
            AssetBundleRefCountLoader.Instance.LoadAssetBundle("skin/pet/wolf_1/skin_13", () => {
                var emojiTransform = temp_obj.transform.Find("img/body/emoji");
                var emojiPrefab =
                    Instantiate(ResourcesUtil.Load("Skin/Pet/Wolf_1/Skin_13/emoji.prefab") as GameObject,
                        emojiTransform,
                        true);
                emojiPrefab.transform.localPosition = Vector3.zero;
            });
        }

        if (component.skinIndex == 22 && !GameUtil.IsMultiGame()) {
            InitPetAnimSkin22(temp_obj);
        }

        temp_obj.transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        has_trigger = true;
    }

    private void InitPetAnimSkin22(GameObject tempObj) {
        Transform animPetParent = tempObj.transform.Find("img");
        string prefabPath = "Skin/Pet/Wolf_1/Skin_22/petAnim22.prefab";
        GameObject subObject = Instantiate(AssetBundleLoader.Inst.Load(prefabPath), animPetParent) as GameObject;
        subObject.name = "petAnim22";
        var subAnim = subObject.GetComponent<WolfDruidAnimStateSkin22>();
        WolfController wolf = tempObj.GetComponent<WolfController>();
        audio_clip = subAnim.clip;
        subAnim.Init(wolf);
    }

    void GetPot(RGController controller) {
        int value = rg_random.Range(0, 3);
        if (value == 0) {
            GameObject temp_buff = Instantiate(the_object, controller.transform.position, Quaternion.identity) as GameObject;
            temp_buff.name = the_object.name;
            temp_buff.GetComponent<RGBuff>().is_enemy = false;
            temp_buff.GetComponent<OffensiveInterface>().SetSourceObject(gameObject);
            temp_buff.transform.parent = controller.transform;
            UICanvas.GetInstance().ShowTextTalk(transform, I2.Loc.ScriptLocalization.Get("Character5_talk3"), 2.5f, 1f);
        }
        controller.role_attribute.RestoreHealth(value);
        UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get(talk_string2), 2.5f, 1);
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        has_trigger = true;
    }

    void GetObject(RGController controller) {
        GameObject temp_obj = Instantiate(the_object) as GameObject;
        temp_obj.name = the_object.name;
        temp_obj.transform.position = controller.transform.position;
        UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get(talk_string2), 2.5f, 1);
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        has_trigger = true;
    }

    void GetGate(RGController controller) {
        GameObject temp_obj = Instantiate(
            ResourcesUtil.Load("RGPrefab/LevelObject/Item/transfer_gate.prefab")) as GameObject;
        if (BattleData.data.IsNormalMode && BattleData.data.levelIndex % 5 == 0) {
            temp_obj.GetComponent<RGTransferGate>().creatorName = "bosstransfergate";
        }
        temp_obj.transform.parent = transform.parent.parent;
        temp_obj.transform.localPosition = Vector3.zero;
        if (!transform.parent.parent) {
            temp_obj.transform.position = transform.position + Vector3.down*2;
        }
        UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get(talk_string2), 2.5f, 1);
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        has_trigger = true;

        if ((BattleData.data.GetMark("lastMeetVampireLevel") + 1) == BattleData.data.levelIndex &&
            BattleData.data.GetMark("lastMeetVampireDay") == NetTime.Time.Subtract2000_01_01Day()) {
            BattleData.data.AddMark(YearReviewManager.GamePlayContinueMeetVampireNpcCount);
        } else {
            BattleData.data.SetMark(YearReviewManager.GamePlayContinueMeetVampireNpcCount, 1);
        }
        
        BattleData.data.AddMark("lastMeetVampireLevel", BattleData.data.levelIndex);
        BattleData.data.AddMark("lastMeetVampireDay", NetTime.Time.Subtract2000_01_01Day());
    }

    void GetTickey(RGController controller) {
        float battleFactor = (BattleData.data.CompareFactor(emBattleFactor.GoodLuck) ? 2 : 1) * (BattleData.data.CompareFactor(emBattleFactor.BadLuck) ? 0.5f : 1);
        if (rg_random.Range(0, 100) < 10) {
            GameObject temp_obj = Instantiate(the_object) as GameObject;
            temp_obj.name = the_object.name;
            temp_obj.transform.position = controller.transform.position;
            UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get("Character10_talk3"), 2.5f, 2);
        } else {
            UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get(talk_string2), 2.5f, 1);
        }
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        has_trigger = true;
    }

    void GetSeed(RGController controller) {
        int seedLevel = Mathf.Min(rg_random.Range(0, 3), rg_random.Range(0, 3), rg_random.Range(0, 3));
        /*
         * 0: 19/27
         * 1: 7/27
         * 2: 1/27
         */
        var proto = PickableDistribution.GetRandomSeed(rg_random, seedLevel);
        if (proto == null) {
            return;
        }
        
        GameObject tempObj = ResourcesUtil.CreateItem(proto.Value.Key, null);
        tempObj.transform.position = controller.transform.position;
        UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get(talk_string2), 2.5f, 1);
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        has_trigger = true;
    }

    void GetMaterial(Component controller) {
        int ran = rg_random.Range(0, 100);
        string materialName = ran switch {
            < 25 => ItemData.IronName,
            < 50 => ItemData.GearName,
            _ => ItemData.BatteryName
        };

        GameObject tempObj = ResourcesUtil.CreateItem(materialName, null);
        tempObj.transform.position = controller.transform.position;
        UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get(talk_string2), 2.5f, 1);
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        has_trigger = true;
    }

    void GetDrink(RGController controller) {
        var drinkList = randomObject.GetObjectDistributionList();
        GameObject drink_proto = drinkList.GetRandomObject(rg_random).gameObject;
        GameObject temp_obj = Instantiate<GameObject>(drink_proto);
        temp_obj.name = drink_proto.name;
        temp_obj.transform.position = transform.position + new Vector3(0, 0.5f);
        temp_obj.transform.eulerAngles = new Vector3(0, 0, 180);
        temp_obj.GetComponent<Collider2D>().enabled = false;
        var targetPosition = controller.transform.position;
        temp_obj.transform.Find("img").GetComponent<SpriteRenderer>().sortingOrder = 10;
        RGMusicManager.GetInstance().PlayEffect(audio_clip);

        temp_obj.transform.DORotate(new Vector3(0, 0, targetPosition.x > transform.position.x ? -180 : 180), 1.25f, RotateMode.WorldAxisAdd);
        temp_obj.transform.DOJump(targetPosition, 2.5f, 1, 1.25f).onComplete = () =>
        {
            //UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get(talk_string2), 2.5f, 1);
            temp_obj.GetComponent<Collider2D>().enabled = true;
        };

        has_trigger = true;
    }

    void GetEquipment(RGController controller) {
        Debug.Log(" === 获取配件 ===");
        var weaponItemFactory = new WeaponItemFactory(WeaponItemStore.level_weight, rg_random);
        if (controller.hand.front_weapon && weaponItemFactory.HasWeaponItem(controller.hand.front_weapon, controller.hand.front_weapon.rg_random, WeaponItemFactory.NormalWeaponItemFilter)) {
            var position = transform.position + new Vector3(2.1f, 0.5f);
            weaponItemFactory.InstantiateWeaponItem(position, RGGameSceneManager.Inst.temp_objects_parent);
            Instantiate<GameObject>(PrefabManager.GetPrefab(PrefabName.effect_show_up), position, Quaternion.identity, RGGameSceneManager.Inst.temp_objects_parent);
            UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get(talk_string2), 2.5f, 1);
            has_trigger = true;
        } else {
            if (controller.IsLocalPlayer()) {
                UICanvas.GetInstance().ShowTextTalk(transform, I2.Loc.ScriptLocalization.Get("Character5_talk3"), 2.5f, 1f);
            }
        }
    }

    private void GetMount(RGController controller) {
        RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Buy);
        var startPosition = transform.position + new Vector3(3, 3);
        var endPosition = transform.position + new Vector3(3, 0);
        
        var mountList = randomObject.GetObjectDistributionList();
        var mountProto = mountList.GetRandomObject(rg_random).gameObject;
        var mountGo = Instantiate<GameObject>(mountProto, startPosition, Quaternion.identity, RGGameSceneManager.Inst.temp_objects_parent);
        mountGo.name = mountProto.name;
        mountGo.GetComponent<Collider2D>().enabled = false;
        UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get(talk_string2), 2.5f, 1);
        mountGo.transform.DOMove(endPosition, 1f).onComplete = ()=> { mountGo.GetComponent<Collider2D>().enabled = true; };
        has_trigger = true;
    }

    private void GetDemon(RGController controller) {
        var list = SkillConfigLoader.GetSkillInfoList(emHero.Warlock.ToInt());
        var skinIndex = RGSaveManager.Inst.GetCurrentSkin(emHero.Warlock);
        if (GameUtil.IsMultiGame()) {
            skinIndex = 0;
        }
        
        var skinData = list[0].skinSkillCfg.data.GetSkinData<GameObjectListSkinData>(skinIndex);
        var proto = skinData.gameObjectList[0].GetData();
        GameObject temp_obj = Instantiate(proto, (transform.position + new Vector3(0.5f, 0, 0)), Quaternion.identity);
        var demonController = temp_obj.GetComponent<PetWarlockDemonController>();
        demonController.SetMaster(controller.transform);
        demonController.isNpcGift = true;
        var demonHP = 25;
        demonController.role_attribute.Client.max_hp = demonHP;
        demonController.role_attribute.Client.hp = demonHP;
        temp_obj.transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        has_trigger = true;
    }

    [LabelText("经脉错乱概率"), Range(0, 99)] public int airbenderFactorRate = 1;
    
    private void GetAirbender(RGController controller) {
        var r = rg_random.Range(0, 100);
        emBattleFactor factor = emBattleFactor.RenduErmai;
        if (r < airbenderFactorRate) {
            factor = emBattleFactor.MeridianDisorder;
            UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get(talk_string3), 2.5f, 1);
        } else {
            UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get(talk_string2), 2.5f, 1);
        }
        BattleData.data.SetFactor(factor, true, true);
        var factorChanger = new BattleFactorChanger();
        factorChanger.OnFactorChanged(factor, true);
        UICanvas.Inst.UpdateBadassmode();
        
        RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Buy);
        has_trigger = true;
    }

    private void GetGift(RGController controller) {
        string weaponName = "weapon_293";
        var rgWeapon = WeaponFactory.CreateWeapon(weaponName, emWeaponSource.LevelNpc);
        rgWeapon.name = weaponName;
        rgWeapon.name = weaponName;
        rgWeapon.transform.position = controller.transform.position;
        UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get(talk_string2), 2.5f, 1);
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        has_trigger = true;
    }

    private void GetElementalAreaEnhanceBuff(RGController controller) {
        var obj = Instantiate(ResourcesUtil.Load<GameObject>("RGPrefab/LevelObject/DrinkAndBook/ball_buff.prefab"));
        var itemBuffBook = obj.GetComponent<ItemBuffBook>();
        itemBuffBook.setupOnStart = false;
        itemBuffBook.buff = emBuff.ElementalAreaEnhance;
        itemBuffBook.RefreshBuffIcon();
        obj.transform.position = controller.transform.position;
        
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        has_trigger = true;
    }
    
    private void GetTenCriticalBuff(RGController controller) {
        var obj = Instantiate(ResourcesUtil.Load<GameObject>("RGPrefab/LevelObject/DrinkAndBook/ball_buff.prefab"));
        var itemBuffBook = obj.GetComponent<ItemBuffBook>();
        itemBuffBook.setupOnStart = false;
        itemBuffBook.buff = emBuff.TenCritical;
        itemBuffBook.RefreshBuffIcon();
        obj.transform.position = controller.transform.position;
        
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        has_trigger = true;
    }
    private void GetBardBuff(RGController controller) {
        var obj = Instantiate(ResourcesUtil.Load<GameObject>("RGPrefab/LevelObject/DrinkAndBook/ball_buff.prefab"));
        var itemBuffBook = obj.GetComponent<ItemBuffBook>();
        itemBuffBook.setupOnStart = false;
        itemBuffBook.buff = emBuff.IncreaseVolume;
        itemBuffBook.RefreshBuffIcon();
        obj.transform.position = controller.transform.position;
        
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        has_trigger = true;
    }
    private void GetFocusOnBuff(RGController controller) {
        var obj = Instantiate(ResourcesUtil.Load<GameObject>("RGPrefab/LevelObject/DrinkAndBook/ball_buff.prefab"));
        var itemBuffBook = obj.GetComponent<ItemBuffBook>();
        itemBuffBook.setupOnStart = false;
        itemBuffBook.buff = emBuff.FocusOn;
        itemBuffBook.RefreshBuffIcon();
        obj.transform.position = controller.transform.position;
        
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        has_trigger = true;
    }

    private void GetDoctor(RGController controller) {
        
        string[] weaponNames = { "weapon_237", "weapon_328", "weapon_297" };
        int randomIndex = rg_random.Range(0, 3);
        string weaponName = weaponNames[randomIndex];
    
        var rgWeapon = WeaponFactory.CreateWeapon(weaponName, emWeaponSource.LevelNpc);
        rgWeapon.name = weaponName;
        rgWeapon.name = weaponName;
        rgWeapon.transform.position = controller.transform.position;
        
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        has_trigger = true;
    }
    
    private void GetAstrologist(RGController controller) {
        var itemDistribution = randomObject.GetItemDistributionList().Random(rg_random);
        if (itemDistribution == null) {
            return;
        }

        var magicMaterial = ResourcesUtil.CreateItem(itemDistribution.Item, null);
        magicMaterial.transform.position = controller.transform.position;
        UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get(talk_string2), 2.5f, 1);
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        has_trigger = true;
    }

    private void GetJoker(RGController controller) {
        for (int i = 0; i < 2; i++) {
            CreateJokerPhantom(controller);
        }
        has_trigger = true;
    }

    private void CreateJokerPhantom(RGController controller) {
        var list = SkillConfigLoader.GetSkillInfoList(emHero.Joker.ToInt());
        var skinIndex = RGSaveManager.Inst.GetCurrentSkin(emHero.Joker);
        var skillData = list[0].skinSkillCfg.data.GetSkinData<GameObjectListSkinData>(skinIndex);
        if (GameUtil.IsMultiGame()) {
            skinIndex = 0;
        }
        
        var weaponProto = skillData.gameObjectList[3].GetData();
        var tmpCreatePos = transform.position;

        var phantomProto = skillData.gameObjectList[2].GetData();
        var newPhantom = Instantiate(phantomProto).GetComponent<JokerMercenaryController>();
        newPhantom.StartLimitTimer();
        newPhantom.master = null;
        newPhantom.name = $"phantom_joker_npc_s{skinIndex}";

        newPhantom.attribute.MaxHpValue.SetOriginalValue(6 + controller.attribute.max_hp);
        var battleData = GameUtil.GetBattleData(controller);
        if (battleData.HasBuff(emBuff.ExpertPet)) {
            newPhantom.attribute.MaxHpValue.AddMultiplicationValue(RoleAttributeValueEnum.BuffExpertPet, 1.5f);
        }

        newPhantom.transform.position = tmpCreatePos;
        newPhantom.SetMaster(controller.transform);
        Destroy(newPhantom.hand.front_weapon);
        if (weaponProto) {
            var weapon = Instantiate(weaponProto);
            weapon.name = weaponProto.name;
            if (weapon.GetComponent<RGNetBehaviour>()) {
                weapon.GetComponent<RGNetBehaviour>().enabled = false;
            }

            newPhantom.hand.SetWeapon(weapon.GetComponent<RGWeapon>(), false);
            newPhantom.ResetIsCharge(weapon.GetComponent<RGWeapon>());
            newPhantom.is_melee = newPhantom.hand.front_weapon.IsMelleIndeed();
        }

        newPhantom.attribute.SpeedValue.SetOriginalValue(6);
        newPhantom.attribute.hp = newPhantom.attribute.max_hp;
        newPhantom.state = 1;
        newPhantom.ShowHpBar(true);
    }

#if UNITY_EDITOR
    private bool IsOfficer => char_npc_type == CharNpcType.BATTLE_FACTOR;
    [ShowInInspector, ShowIf(nameof(IsOfficer))]
    private bool _hasSpecificFactor;
    [ShowInInspector, ShowIf(nameof(IsOfficer))]
    private emBattleFactor _specificFactor;
#endif
    
    private void GetBattleFactor(RGController controller) {
        emBattleFactor factor = ChallengeInfo.info.GetChallenge(rg_random);
#if UNITY_EDITOR
        if (_hasSpecificFactor) {
            factor = _specificFactor;
        }
#endif
        BattleData.data.SetFactor(factor, true, true);
        var factorChanger = new BattleFactorChanger();
        factorChanger.OnFactorChanged(factor, true);
        switch (factor) {
            case emBattleFactor.InfiniteEnergy:
                if (GameUtil.IsSingleGame()) {
                    var c = RGGameSceneManager.Inst.controller;
                    c.role_attribute.Client.StartCoroutine(c.role_attribute.RestoringEnergy());
                }

                if (GameUtil.IsMultiGame()) {
                    var controllers = NetControllerManager.Inst.controllers;
                    foreach (var playerController in controllers) {
                        if (!playerController.controller) {
                            continue;
                        }
                        var attribute = playerController.controller.role_attribute;
                        attribute.Client.StartCoroutine(attribute.RestoringEnergy());
                    }
                }
                break;
        }
        UICanvas.Inst.UpdateBadassmode();
        UICanvas.GetInstance().ShowTextTalk(transform, ScriptLocalization.Get(talk_string2), 2.5f, 1);
        RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Buy);
        has_trigger = true;
    }

    public int GetItemValue() {
        return item_value;
    }
    public string GetItemName() {
        return "name";
    }
    public int GetItemLevel() {
        return 0;
    }
    public bool CanUse() {
        if (can_use) {
            can_use = false;
            return true;
        } else {
            return false;
        }
    }
}