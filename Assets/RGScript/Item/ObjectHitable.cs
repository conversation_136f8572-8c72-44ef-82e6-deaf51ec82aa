using System.Collections;
using System.Collections.Generic;
using UnityEngine;

// 可被子弹击中的物体
public class ObjectHitable : MonoBehaviour, IRGBox, ILockableObject {
    public float lockMaxDistance;
    public bool dead { get; set; }
    public int camp { get; set; }

    Collider2D hitTrigger;

    void Start() {
        hitTrigger = GetComponent<Collider2D>();
    }

    public BoxDestroyEvent boxDestroyEvent { get; set; }
    public bool CanHit(int damage, GameObject source_object, int camp) {
        return this.camp != camp && !dead;
    }

    public bool Hit(int damage, GameObject source_object, int camp) {
        if (CanHit(damage, source_object, camp)) {
            Blink();
            onHit?.Invoke();
            if (dead) {
                boxDestroyEvent?.Invoke(this);
                GetComponent<Collider2D>().enabled = false;
                if (gameObject.TryGetComponent<Animator>(out var anim)) {
                    anim.SetTrigger("dead");
                }
            }
            return true;
        }
        return false;
    }

    GameUtil.ObjectBlinkInfo blinkInfo;
    public void Blink() {
        if (blinkInfo == null) {
            blinkInfo = new GameUtil.ObjectBlinkInfo();
            blinkInfo.Init(gameObject);
        }
        if (!blinkInfo.IsBlinking()) {
            blinkInfo.Blink();
        }
    }


    public Vector3 GetPosition() {
        return transform.position;
    }

    public System.Action onHit;

    public bool Lockable() {
        return hitTrigger != null && hitTrigger.enabled && RGGameSceneManager.Inst != null && RGGameSceneManager.Inst.controller != null &&
            Vector2.Distance(RGGameSceneManager.Inst.controller.transform.position, transform.position) < lockMaxDistance;
    }

    public bool CanOffLock(GameObject character) {
        return hitTrigger == null || !hitTrigger.enabled || (RGGameSceneManager.Inst != null && RGGameSceneManager.Inst.controller != null &&
            Vector2.Distance(RGGameSceneManager.Inst.controller.transform.position, transform.position) > (lockMaxDistance + 0.5f));
    }

    bool _isLocked;
    public bool IsLocked() {
        return _isLocked;
    }

    public void BeLock() {
        _isLocked = true;
    }

    public void OffLock() {
        _isLocked = false;
    }

    public Vector2 GetColliderCenter() {
        return hitTrigger.transform.TransformPoint(hitTrigger.offset);
    }

    void Update() {
        if (lockMaxDistance > 0 && RGGameSceneManager.Inst != null && RGGameSceneManager.Inst.controller != null) {
            var ctrl = RGGameSceneManager.Inst.controller;
            if (Lockable() &&
                (ctrl.target_obj == null || (ctrl.target_obj != null && Vector2.Distance(ctrl.transform.position, ctrl.target_obj.position) > lockMaxDistance))) {
                ctrl.target_obj = transform;
            }
        }
    }
}
