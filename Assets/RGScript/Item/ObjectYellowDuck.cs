using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ObjectYellowDuck : RGItem {
    public List<AudioClipWithWeight> audioClips;
    
    public override void ItemTrigger(RGController controller) {
        RGMusicManager.Inst.PlayEffect(audioClips.GetRandomWeightObject(null).audioClip);
        HideTap(controller);
    }

    public override void SyncItemTrigger(RGController controller, string extraInfo = "") {
        RGMusicManager.Inst.PlayEffect(audioClips.GetRandomWeightObject(null).audioClip);
        HideTap(controller);
    }

    public override bool CanUse() {
        return true;
    }

    public override int GetItemValue() {
        return 0;
    }

    public override int GetItemLevel() {
        return 0;
    }

    public override string GetItemName() {
        return "?";
    }

    [Serializable]
    public class AudioClipWithWeight : IWeightObject {
        public AudioClip audioClip;
        public int weight;
        public int Weight => weight;
    }
}
