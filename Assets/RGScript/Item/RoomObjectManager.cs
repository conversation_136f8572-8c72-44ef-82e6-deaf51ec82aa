using Activities.Dream.Scripts;
using Activities.ReturnPlayerH5.Scripts;
using cfg.config;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using ChillyRoom;
using ChillyRoom.Services.Core.Libs;
using CustomFactor;
using DG.Tweening;
using FestivalDecorate;
using FishChipStore;
using I2.Loc;
using MuseumUnlock;
using NewDynamicConfig;
using RGScript.CheckIn;
using RGScript.Config.Manager;
using RGScript.Data;
using RGScript.Data.GameItemData;
using RGScript.Data.Mall;
using RGScript.Item.Pickable;
using RGScript.Item.Plant;
using RGScript.Item.RoomItem;
using RGScript.Other.Globalize;
using RGScript.Manager.SDK;
using RGScript.Other.NewSDK;
using RGScript.Tutorial;
using RGScript.UI;
using RGScript.Util;
using RGScript.Util.LifeCycle;
using RGScript.Util.NewConfigs.Configs;
using RGScript.Weapon;
using SoulKnight.Runtime.Config2Code.Config;
using UIFramework;
#if UNITY_EDITOR
using UnityEditor;
#endif
using UnityEngine;
using UnityEngine.UI;
using Util.TAUtil;
using RGScript.Util.QuickCode;
using SeasonStore;
using System.IO;
using SoulKnight.Runtime.Item;
using Object = UnityEngine.Object;
using Random = UnityEngine.Random;

public enum FloorEnum {
    First = 0,
    Second = 1,
}

public class RoomObjectManager : MonoBehaviour {
    public readonly Dictionary<ItemPlantPot,int> PlantPotOneClickCount = new Dictionary<ItemPlantPot, int>();// 一键成熟次数记录
    public const int OneClickCountLimit = 1;
    protected List<ItemPlantPot> plantPots = new List<ItemPlantPot>();
    protected Transform forgeContainer;

    protected Transform hallSkinTransform;
    protected Transform hallSkinSlots;

    bool ShowHireBoard {
        get {
#if UNITY_SWITCH
            return Mathf.Abs(BattleData.data.gameSeed) % 100 < 10 && PlayerSaveData.Inst.total_indexes > 0;
#endif

#if !UNITY_EDITOR
            return Mathf.Abs(BattleData.data.gameSeed) % 100 < 10 && PlayerSaveData.Inst.total_indexes > 0;
#endif
#if UNITY_ANDROID
            return false;
            return (Application.isEditor) || (ChannelConfig.TapTap && LanguageUtil.IsChineseStrong()) &&
                PlayerSaveData.Inst.total_indexes > 0;
#elif UNITY_IOS
            return ((Application.isEditor) || LanguageUtil.IsChineseStrong()) &&
                PlayerSaveData.Inst.total_indexes > 0;
#endif
            // 方便测试
            return Random.Range(0f, 1f) > 0.5f;
        }
    }

    public static bool OpenNewDayDebugMode;

    private List<emRoomUnlockItem> _unlockItems = new List<emRoomUnlockItem>();
    private void Awake() {
        PlantPotOneClickCount.Clear();
        AssetBundleLoader.Inst.UnloadUnnecessaryAssetBundles();
        if (GameUtil.IsSingleGame()) {
            _unlockItems = RoomItemUnlockThroughBattle.CheckUnlock();
        }
    }

    void Start() {
        Debug.Log(GameConfigManager.GetInstance().Config.IsDebug ? "[GameConfig]内网包" : "[GameConfig]外网包");
        SimpleEventManager.Raise(new BeforeSelfEnterHeroRoomEvent());

        // 检查流程配置
        LifeCycleConfig.CheckConfig();
        // 进入客厅检查游戏的状态 如果状态异常就上报
        InitSundry.CheckState();
        LoadHeroRoomSkins();
        WeaponDropInfo.Instance.Load();
        CheckTryUnlockBlueprintSkin();
        TryGetRewardOfExpiredGameMode();
        MailBoxManager.Instance.TryGetServerEmail();
        DelayLoadAllAd();
    }

    private static RoomObjectManager _instance;
    public static RoomObjectManager Inst {
        get {
            if (DataUtil.GetCurSceneName() != RGGameConst.SCENE_HERO_ROOM) {
                if(LogUtil.IsShowLog){LogUtil.LogError($"Current Scene is {DataUtil.GetCurSceneName()}, RoomObjectManager is null");}
                return null;
            }
            
            if (_instance == null) {
                _instance = GameObject.Find("/room").GetComponent<RoomObjectManager>();
            }

            return _instance;
        }
    }
    
    #region 英雄之家装扮

    #region 功能控制

    [NonSerialized] public bool CanRewardStick = true;
    [NonSerialized] public bool CanUseStatue = true;
    [NonSerialized] public bool CanUseFishBowl = true;
    [NonSerialized] public int FishBowlRewardIndex = -1;
    [NonSerialized] public bool PetHasEat = false;
    [NonSerialized] public bool CanUseDrinkSeller = true;
    [NonSerialized] public bool CanTakeFoodFromCooker = true;
    [NonSerialized] public FloorEnum ChooseHeroCameraFocusFloor = FloorEnum.First;

    #endregion
    
    public enum DecorateTag {
        none = 0,
        christmas = 1,
        spring_festival = 2,
        from_player = 3,
        // easter = 4,
        season_irontide = 5,
        halloween = 6,
        april_fool = 7,
    }

    public DecorateTag SkinTag {
        get {
            return HeroRoomDecorateTag;
        }
    }
    [NonSerialized] public DecorateTag HeroRoomDecorateTag = DecorateTag.none;
    public DecorateTag testTag = DecorateTag.spring_festival;
    public int testEggMachineMode;
    public bool testDecorate;
    public const string SecondHallMaskPath = "second_hall_slot/second_hall/function/mask_second_hall";
    private const string HeroRoomCommonName = "hero_room/common";
    private const string DefaultTag = "normal";
    private const string FromPlayerTag = "from_player";
    
    private const string HallSkinName = "hall";
    private const string HallSlotPath = "hall_slot";
    public const string WorkShopSkinName = "work_shop";
    public const string WorkShopSlotPath = "work_shop_slot";
    private const string GardenSkinName = "garden";
    private const string GardenSlotPath = "garden_slot";
    private const string MagicAreaSkinName = "magic_area";
    private const string MagicAreaSlotPath = "magic_area_slot";
    private const string SecondHallSkinName = "second_hall";
    private const string SecondHallSlotPath = "second_hall_slot";
    public const float SecondHallYOffset = 30;
    private VersionEggMachineConfig EggMachineConfig => EggMachineUtil.GetEggMachineConfig();
    private int _eggMachineMode;
    
    private void LoadHeroRoomSkins() {
        var timeProvider = GetComponent<ITimeProvider>();
        var festivalDecorateConfig = ScriptableConfigUtil.Load<FestivalDecorateConfig>();

        #region 处理网络配置

        #if !UNITY_EDITOR
            // 避免覆盖本地调试配置
            var netRoomDecorateConfig = ConfigManager.GetCurrectUseConfig<RoomDecorateConfig>();
            if (null != netRoomDecorateConfig && null != netRoomDecorateConfig.Config &&
                netRoomDecorateConfig.needUseConfig) {
                festivalDecorateConfig.configs = new List<FestivalDecorateConfig.ConfigItem>
                    { netRoomDecorateConfig.Config };
            }
        #endif

        var hasNetWork = Application.internetReachability != NetworkReachability.NotReachable;
        var time = MallUtility.CurrentTime;
        if (hasNetWork && !string.IsNullOrEmpty(EggMachineConfig.TimeStart) && !string.IsNullOrEmpty(EggMachineConfig.TimeEnd)) {
            var startTime = TimeUtil.ChineseTimestampToLocalDateTime(EggMachineConfig.TimeStart);
            var endTime = TimeUtil.ChineseTimestampToLocalDateTime(EggMachineConfig.TimeEnd);
            if (time > startTime && time < endTime) {
                _eggMachineMode = EggMachineConfig.EggMachineMode;
            }
        } else {
            _eggMachineMode = 0;
        }
        
        #endregion

        foreach (var c in festivalDecorateConfig.configs) {
            if(LogUtil.IsShowLog){LogUtil.Log($"[RoomObjectManager]festivalDecorateConfig : {c.key} : from {c.fromDate.ToString()} to {c.toDate}");}
        }
        if(LogUtil.IsShowLog){LogUtil.Log($"[RoomObjectManager]current time : {timeProvider.DateTime}");}

        foreach (var enumName in Enum.GetNames(typeof(DecorateTag))) {
            if (!festivalDecorateConfig.IsSuit(enumName, timeProvider.DateTime)) {
                continue;
            }

            if (!Enum.TryParse(enumName, out DecorateTag decTag)) {
                continue;
            }

            HeroRoomDecorateTag = decTag;
            if(LogUtil.IsShowLog){LogUtil.Log($"[RoomObjectManager]HeroRoomDecorateTag : {HeroRoomDecorateTag}");}
            break;
        }

#if UNITY_EDITOR
        if (testDecorate) {
            HeroRoomDecorateTag = testTag;
            _eggMachineMode = testEggMachineMode;
        }
#endif

        UICanvas.GetInstance().anim.SetBool(ShowCurtain, true);
        transform.Find("objects/masks").gameObject.SetActive(true);
        
        AssetBundleLoader.Inst.StartCoroutine(LoadCoroutine());
    }

    private IEnumerator LoadCoroutine() {
        var hallSkinIndex = DataUtil.GetHallSkinIndex();
        var hallSkinBundleName = GetSkinBundleName(HeroRoomSkinType.Hall, hallSkinIndex);
        var workShopSkinIndex = DataUtil.GetWorkShopSkinIndex();
        var workShopSkinBundleName = GetSkinBundleName(HeroRoomSkinType.WorkShop, workShopSkinIndex);
        var gardenSkinIndex = DataUtil.GetGardenSkinIndex();
        var gardenSkinBundleName = GetSkinBundleName(HeroRoomSkinType.Garden, gardenSkinIndex);
        var magicAreaSkinIndex = DataUtil.GetMagicAreaSkinIndex();
        var magicAreaSkinBundleName = GetSkinBundleName(HeroRoomSkinType.MagicArea, magicAreaSkinIndex);
        var secondHallSkinIndex = DataUtil.GetSecondHallSkinIndex();
        var secondHallSkinBundleName = GetSkinBundleName(HeroRoomSkinType.SecondHall, secondHallSkinIndex);

        var bundleList = new List<string> {
            HeroRoomCommonName,
            hallSkinBundleName,
            workShopSkinBundleName,
            gardenSkinBundleName,
            magicAreaSkinBundleName,
            secondHallSkinBundleName,
            AssetBundleLoader.SkillDemo,
        };

        yield return AssetBundleLoader.Inst.LoadMultipleBundles(bundleList);

        UICanvas.GetInstance().anim.SetBool(ShowCurtain, false);

        if (DataUtil.CurIsTargetScene(RGGameConst.SCENE_HERO_ROOM)) {
            InstantiateSkin(HeroRoomSkinType.Hall, hallSkinIndex);
            InstantiateSkin(HeroRoomSkinType.WorkShop, workShopSkinIndex);
            InstantiateSkin(HeroRoomSkinType.Garden, gardenSkinIndex);
            InstantiateSkin(HeroRoomSkinType.MagicArea, magicAreaSkinIndex);
            InstantiateSkin(HeroRoomSkinType.SecondHall, secondHallSkinIndex);
            AfterLoadSkins();
        }
    }

    private Transform CreateSlot(string slotName) {
        var slot = new GameObject(slotName).transform;
        slot.SetParent(transform);
        slot.localPosition = Vector3.zero;
        slot.localRotation = Quaternion.identity;
        slot.localScale = Vector3.one;
        return slot;
    }
    
    private static readonly int DecorateTransitionEnd = Animator.StringToHash("decorate_transition_end");

    public void ChangeHeroRoomSkin(HeroRoomSkinType heroRoomSkinType, int previousSkinIndex, int skinIndex, Action onFinished) {
        StartCoroutine(ChangeHeroRoomSkinCoroutine(heroRoomSkinType, previousSkinIndex, skinIndex, onFinished));
    }

    private IEnumerator ChangeHeroRoomSkinCoroutine(HeroRoomSkinType skinType, int previousSkinIndex, int skinIndex, Action onFinished) {
        yield return new WaitForEndOfFrame();
        var soundClip = ResourcesUtil.Load<AudioClip>("RGSound/effect/fx_decorate_transition.mp3");
        var decorateTransition =
            Instantiate(ResourcesUtil.Load<GameObject>("RGPrefab/UI/window_decoration_transition.prefab"),
                UICanvas.Inst.transform);
        decorateTransition.transform.SetAsLastSibling();
        decorateTransition.transform.localPosition = Vector3.zero;
        var decorateAnim = decorateTransition.GetComponent<Animator>();
        decorateAnim.enabled = false;
        var images = decorateTransition.GetComponentsInChildren<Image>();
        foreach (var image in images) {
            image.SetAlpha(0);
            image.DOFade(1, 0.5f).SetUpdate(true);
        }

        yield return new WaitForSecondsRealtime(0.5f);
        DestroySkin(skinType);

        yield return new WaitForEndOfFrame();
        decorateAnim.enabled = true;
        var skinBundleName = GetSkinBundleName(skinType, skinIndex);
        var previousSkinBundleName = GetSkinBundleName(skinType, previousSkinIndex);
        StartCoroutine(LoadSkinBundle(skinBundleName));
        yield return new WaitForSecondsRealtime(0.5f);
        RGMusicManager.Inst.PlayEffect(soundClip);
        yield return new WaitForSecondsRealtime(1.5f);
        while (!_skinBundleLoaded) {
            yield return new WaitForSecondsRealtime(0.5f);
            RGMusicManager.Inst.PlayEffect(soundClip);
            yield return new WaitForSecondsRealtime(1.5f);
        }

        yield return new WaitForEndOfFrame();
        InstantiateSkin(skinType, skinIndex, true);
        yield return new WaitForEndOfFrame();
        TransparentOrGrayBack();
        
        decorateAnim.enabled = false;
        if (previousSkinIndex != 0) {
            AssetBundleLoader.Inst.UnLoadBundle(previousSkinBundleName);
        }

        decorateTransition.GetComponent<Animator>().SetTrigger(DecorateTransitionEnd);
        foreach (var image in images) {
            image.SetAlpha(1);
            image.DOFade(0, 0.5f).SetUpdate(true);
        }

        yield return new WaitForSecondsRealtime(0.5f);
        Destroy(decorateTransition);

        onFinished?.Invoke();
        SimpleEventManager.Raise(new ChangeHeroRoomSkinEvent() {
            SkinType =  skinType,
            previousSkinIndex = previousSkinIndex,
            skinIndex = skinIndex,
        });
    }

    private void TransparentOrGrayBack() {
        var list = GetComponentsInChildren<NPCStateWhenChooseSkin>();
        foreach (var item in list) {
            switch(item.state) {
                case NPCState.gray:
                    item.GrayBack(null);
                    break;
                case NPCState.transparent:
                    item.TransparentBack(null);
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }
    }

    private static string GetSkinBundleName(HeroRoomSkinType skinType, int skinIndex) {
        return skinType switch {
            HeroRoomSkinType.Hall => $"hero_room/hall/skin_{skinIndex}",
            HeroRoomSkinType.WorkShop => $"hero_room/work_shop/skin_{skinIndex}",
            HeroRoomSkinType.Garden => $"hero_room/garden/skin_{skinIndex}",
            HeroRoomSkinType.MagicArea => $"hero_room/magic_area/skin_{skinIndex}",
            HeroRoomSkinType.SecondHall => $"hero_room/second_hall/skin_{skinIndex}",
            _ => throw new ArgumentOutOfRangeException(nameof(skinType), skinType, null)
        };
    }

    private void InstantiateSkin(HeroRoomSkinType skinType, int skinIndex, bool changeSkin = false) {
        var decorateTag = "";
        if (skinIndex == 0) {
            decorateTag = $"_{HeroRoomDecorateTag.ToString()}";
            if (!DecorateSkinTable.Data.ContainsKey(
                    $"{DataUtil.GetHeroRoomSkinKey(skinType)}_{skinIndex}{decorateTag}")) {
                decorateTag = $"_{DefaultTag}";
            }
        }

        Debug.Log($"[HeroRoomSkin]InstantiateSkin : skinType : {skinType.ToString()} DecorateTag : {decorateTag}");
        switch (skinType) {
            case HeroRoomSkinType.Hall:
                InstantiateHallSkin(skinIndex, decorateTag);
                if (changeSkin) {
                    InitPostman();
                }
                break;
            case HeroRoomSkinType.WorkShop:
                InstantiateWorkShopSkin(skinIndex, decorateTag);
                break;
            case HeroRoomSkinType.Garden:
                InstantiateGardenSkin(skinIndex, decorateTag, changeSkin);
                break;
            case HeroRoomSkinType.MagicArea:
                InstantiateMagicAreaSkin(skinIndex, decorateTag);
                break;
            case HeroRoomSkinType.SecondHall:
                InstantiateSecondHallSkin(skinIndex, decorateTag);
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(skinType), skinType, null);
        }
    }
    
    private void DestroySkin(HeroRoomSkinType skinType) {
        switch (skinType) {
            case HeroRoomSkinType.Hall:
                DestroyHallSkin();
                break;
            case HeroRoomSkinType.WorkShop:
                DestroyWorkShopSkin();
                break;
            case HeroRoomSkinType.Garden:
                DestroyGardenSkin();
                break;
            case HeroRoomSkinType.MagicArea:
                DestroyMagicArea();
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(skinType), skinType, null);
        }
    }

    #region 大厅装扮
    
    private static void InstantiateObjectsInsideSlot(string path, Transform slot) {
        if (path.IsNullOrEmpty()) {
            Debug.LogError($"{slot.name}未找到配置，请检查{nameof(HallObjectSkinTable)}");
        }

        try {
            var obj = Instantiate(ResourcesUtil.Load<GameObject>(path), slot);
            obj.name = slot.name.Replace("_slot", "");
            obj.transform.localPosition = Vector3.zero;
        } catch (Exception e) {
            Debug.LogError($"{path}\n{e}");
        }
    }

    private void InstantiateHallSkin(int skinIndex, string decorateTag) {
        var hallSlot = CreateSlot(HallSlotPath);
        var hallSkinPath = $"RGPrefab/HeroRoom/Hall/Skin_{skinIndex}/hall_{skinIndex}{decorateTag}.prefab";
        var hallSkinIndex = DataUtil.GetHallSkinIndex();
        hallSkinTransform = Instantiate(ResourcesUtil.Load<GameObject>(hallSkinPath)).transform;
        hallSkinTransform.transform.SetParent(hallSlot);
        hallSkinTransform.name = HallSkinName;
        hallSkinSlots = hallSkinTransform.Find("function/slots");

        //实例化
        var config = HallObjectSkinTable.Data;
        var slots = hallSkinTransform.Find("function/slots");
        if (hallSkinIndex == 0) {
            var specifiedTag = HeroRoomDecorateTag.ToString();
            int fromPlayerNpcSkinIndex = RGGameConst.GameConfig.FromPlayerNpcSkinIndex;
            var fromPlayerTag = fromPlayerNpcSkinIndex > 0 ? $"{FromPlayerTag}_{fromPlayerNpcSkinIndex}" : "";
            var limitedTimeTag = _eggMachineMode > 0 ? $"limited_time_mode_{_eggMachineMode}" : "";
            for (int i = 0; i < slots.childCount; i++) {
                var slot = slots.GetChild(i);
                if (!slot.gameObject.activeSelf) {
                    continue;
                }
                
                var itemDefaultKey = $"{slot.name}-0-{DefaultTag}";
                var itemSpecifiedKey = $"{slot.name}-0-{specifiedTag}";
                var itemFromPlayerKey = $"{slot.name}-0-{fromPlayerTag}";
                var itemEggMachineActivityKey = $"{slot.name}-0-{limitedTimeTag}";
                var path = "";

                if (config.TryGetValue(itemFromPlayerKey, out var fromPlayerData)) {
                    //玩家征集皮肤
                    path = fromPlayerData.Path;
                } else if (config.TryGetValue(itemSpecifiedKey, out var specifiedData)) {
                    //指定tag皮肤
                    path = specifiedData.Path;
                }else if (config.TryGetValue(itemEggMachineActivityKey, out var eggMachineActivityData)) {
                    //扭蛋机活动期间皮肤
                    path = eggMachineActivityData.Path;
                } else if (config.TryGetValue(itemDefaultKey, out var defaultData)) {
                    //默认皮肤
                    path = defaultData.Path;
                }

                InstantiateObjectsInsideSlot(path, slot);
            }
        } else {
            for (int i = 0; i < slots.childCount; i++) {
                var slot = slots.GetChild(i);
                var itemKey = $"{slot.name}-{hallSkinIndex}";
                var itemDefaultKey = $"{slot.name}-0-{DefaultTag}";

                var path = "";
                if (config.TryGetValue(itemKey, out var skinData)) {
                    //皮肤
                    path = skinData.Path;
                } else if (config.TryGetValue(itemDefaultKey, out var defaultData)) {
                    //默认皮肤
                    path = defaultData.Path;
                }

                InstantiateObjectsInsideSlot(path, slot);
            }
        }

        var hireBoardSlot = hallSkinSlots.Find("hire_board_slot");
        var gallerySlot = hallSkinSlots.Find("gallery_slot");
        var showHireBoard = ShowHireBoard;
        bool configHideHireBoard = false;
        var hireBoardRule = GlobalizeManager.Instance.UseGlobalizeByName(RGGameConst.CommonRules, "HideHireBoard");
        if (hireBoardRule != null && hireBoardRule.Open) {
            configHideHireBoard = true;
        }
        bool configHideGallerySlot = false;
        var galleryRule = GlobalizeManager.Instance.UseGlobalizeByName(RGGameConst.CommonRules, "HideGallery");
        if (galleryRule != null && galleryRule.Open) {
            configHideGallerySlot = true;
        }
        
        hireBoardSlot.gameObject.SetActive(showHireBoard && !configHideHireBoard);
        gallerySlot.gameObject.SetActive(!showHireBoard && !configHideGallerySlot);

        InitUnlockObjects();
        
        ShowBattleIcon(hallSkinTransform);

        InitSwitchFloor();
    }

    private void InitFridge() {
        var slots = hallSkinTransform.Find("function/slots");
        var fridgeSlot = slots.transform.Find("fridge_slot");
        if (!fridgeSlot) {
            if (LogUtil.IsShowLog) { LogUtil.LogError("找不到fridge_slot"); }

            return;
        }

        if (DataUtil.HasUnlockABing()) {
            fridgeSlot.gameObject.SetActive(false);
            var aBing = Instantiate(
                ResourcesUtil.Load<GameObject>("RGPrefab/HeroRoom/Hall/Common/abing/abing_0_normal.prefab"),
                slots.parent);
            aBing.transform.position = fridgeSlot.position;
            aBing.name = "abing";
        } else {
            fridgeSlot.gameObject.SetActive(true);
        }
    }
    
    private void InitPostman() {
        var postman = hallSkinSlots.Find("postman_slot/postman").GetComponent<ObjectPostman>();
        postman.gameObject.SetActive(false);
        if (PlayerSaveData.Inst.show_post_man == 1 ||
            (PostManUtil.GetValidReward() != null && PostManUtil.GetValidReward().IsValid())) {
            //处理快递员
            if (ItemData.data.GetSpecialTicket(RGGameConst.POSTMAN_UPGRADE) > 0) postman.Upgrade();
            postman.gameObject.SetActive(true);
        }

        //处理快递员是否显示显示
        var netRoomDecorateConfig = ConfigManager.GetCurrectUseConfig<RoomDecorateConfig>();
        if (null != netRoomDecorateConfig && null != netRoomDecorateConfig.Config &&
            netRoomDecorateConfig.needUseConfig && netRoomDecorateConfig.postmanAlwaysShow) {
            postman.gameObject.SetActive(true);
        }
        
        // 处理快递员宝石广告点
        if (AdOptimizeUtil.CourierAdOpen 
            && PlayerSaveData.Inst.show_post_man != 1 
            && !postman.gameObject.activeInHierarchy
            && AdOptimizeUtil.CanShowGemAd) {
            postman.gameObject.SetActive(true);
            postman.SetAdGemMode();
        }
    }
    
    /// <summary>
    /// 新手签到npc入口
    /// </summary>
    private void InitNewbieCheckIn() {
        var needShow = CheckInManager.NeedShowCheckInEntry();
        transform.Find("objects/object_checkin").gameObject.SetActive(needShow);
    }
    
    private void DestroyHallSkin() {
        var hall = transform.Find(HallSlotPath);
        DestroyImmediate(hall.gameObject);
    }

    #endregion

    #region 工坊装扮

    private void InstantiateWorkShopSkin(int workShopSkinIndex, string decorateTag) {
        var slot = CreateSlot("work_shop_slot");
        var workShopSkinPath =
            $"RGPrefab/HeroRoom/WorkShop/Skin_{workShopSkinIndex}/work_shop_{workShopSkinIndex}{decorateTag}.prefab";
        var skinGo = Instantiate(ResourcesUtil.Load<GameObject>(workShopSkinPath));
        var workShopSkinTransform = skinGo.transform;
        workShopSkinTransform.SetParent(slot);
        workShopSkinTransform.name = WorkShopSkinName;
        
        var forgeContainerPath = $"{WorkShopSlotPath}/{WorkShopSkinName}/function/forge_slot/forge/right";
        forgeContainer = transform.Find(forgeContainerPath);
        Debug.Log($"forgeContainerPath : {forgeContainerPath}, find : {forgeContainer != null}");

        StartCoroutine(InitForgeWeapons());
    }

    private void DestroyWorkShopSkin() {
        var player = RGGameSceneManager.Inst.controller;
        foreach (var weapon in forgeItems) {
            if (weapon.Item2 == null) {
                ItemData.ConsumeForgeWeapon(weapon.Item1);
            } else {
                if (weapon.Item2.TryGetComponent(out RGWeapon rgWeapon)) {
                    if (player != null && player.HasWeapon(weapon.Item2.name)) {
                        // 丢配件
                        if (rgWeapon.weapon_item) {
                            if (!rgWeapon.weapon_item.gameObject.activeInHierarchy) {
                                rgWeapon.weapon_item.gameObject.SetActive(true);
                            }
                            rgWeapon.weapon_item.Drop();
                        }
                        
                        if (rgWeapon.weapon_item_activity) {
                            if (!rgWeapon.weapon_item_activity.gameObject.activeInHierarchy) {
                                rgWeapon.weapon_item_activity.gameObject.SetActive(true);
                            }
                            rgWeapon.weapon_item_activity.Drop();
                        }
                        
                        player.hand.DropWeapon(rgWeapon);
                    }
                } else if (weapon.Item2.TryGetComponent(out FishRod fishRod)) {
                    BattleData.data.fishRod.fishRodName = "";
                    BattleData.data.fishRod.fishRodLevel = 0;
                    fishRod.Drop(null);
                }

                Destroy(weapon.Item2);
            }
        }

        if (player != null && player.mount != null) {
            player.mount.Land();
        }

        if (mechInRoom != null) {
            Destroy(mechInRoom);
        }

        var workShop = transform.Find(WorkShopSlotPath);
        var tokenMachineItemSlot = workShop.Find($"{WorkShopSkinName}/function/token_machine_slot/token_machine/img/item_container");
        if (tokenMachineItemSlot.childCount > 0) {
            tokenMachineItemSlot.GetChild(0).SetParent(RGGameSceneManager.Inst.temp_objects_parent);
        }
        
        DestroyImmediate(workShop.gameObject);
    }
    
    private bool _skinBundleLoaded;
    private IEnumerator LoadSkinBundle(string skinBundleName) {
        _skinBundleLoaded = false;
        yield return AssetBundleLoader.Inst.LoadBundle(skinBundleName);
        _skinBundleLoaded = true;
    }
    
    #endregion

    #region 花园装扮

    private void InstantiateGardenSkin(int gardenSkinIndex, string decorateTag, bool changeSkin) {
        var slot = CreateSlot("garden_slot");

        var gardenSkinPath =
            $"RGPrefab/HeroRoom/Garden/Skin_{gardenSkinIndex}/garden_{gardenSkinIndex}{decorateTag}.prefab";
        var skinGo = Instantiate(ResourcesUtil.Load<GameObject>(gardenSkinPath));
        var gardenSkinTransform = skinGo.transform;
        gardenSkinTransform.SetParent(slot);
        gardenSkinTransform.name = GardenSkinName;

        var plantRoot = skinGo.transform.Find($"function/plant_pot_slots")
            .GetComponent<PlantPotSlots>();
        plantRoot.Init();
        plantPots.Clear();
        for (int i = 0; i < plantRoot.transform.childCount; i++) {
            plantPots.Add(plantRoot.transform.GetChild(i).GetComponentInChildren<ItemPlantPot>());
        }

        if (changeSkin) {
            RefreshPlantPots();
        }
    }

    private void RefreshPlantPots() {
        var infos = ItemData.data.plants;
        for (int i = 0; i < infos.Count; i++) {
            var info = infos[i];
            if (info == null) {
                continue;
            }
            
            if (plantPots.Count > i) {
                plantPots[i].info = info;
            }
        }
    }
    
    
    private void DestroyGardenSkin() {
        var garden = transform.Find(GardenSlotPath);
        DestroyImmediate(garden.gameObject);
    }
    
    #endregion

    #region 魔法区装扮
    
    private void InstantiateMagicAreaSkin(int magicAreaSkinIndex, string decorateTag) {
        var slot = CreateSlot("magic_area_slot");
        
        var magicAreaSkinPath =
            $"RGPrefab/HeroRoom/MagicArea/Skin_{magicAreaSkinIndex}/magic_area_{magicAreaSkinIndex}{decorateTag}.prefab";
        var skinGo = Instantiate(ResourcesUtil.Load<GameObject>(magicAreaSkinPath));
        var magicAreaSkinTransform = skinGo.transform;
        magicAreaSkinTransform.SetParent(slot);
        magicAreaSkinTransform.name = MagicAreaSkinName;
        
        UnlockBossRush();
    }

    private void DestroyMagicArea() {
        var magicArea = transform.Find(MagicAreaSlotPath);
        DestroyImmediate(magicArea.gameObject);
    }
    
    /// <summary>
    /// 这个方法如果出现报错，则可能是npc_bossrush_mgr.prefab在生成出来后被unity给改名了导致路径不对找不到，原因是guid相关的missing，解决方案是reimport npc_bossrush_mgr.prefab
    /// </summary>
    void UnlockBossRush() {
        bool unlock = DataUtil.IsBossRushUnlock();
        transform.Find($"{MagicAreaSlotPath}/{MagicAreaSkinName}/function/bossrush_slot/npc_bossrush_mgr").gameObject.SetActive(unlock);
        transform.Find($"{MagicAreaSlotPath}/{MagicAreaSkinName}/function/bossrush_slot/bossrush_unlock_tip").gameObject.SetActive(!unlock);
    }

    #endregion
    
    #region 二层大厅装扮

    private readonly Dictionary<string, string> _objectPath = new Dictionary<string, string>() {
        {"kitchen","RGPrefab/HeroRoom/SecondHall/Common/kitchen.prefab"},
        {"arcade_machine","RGPrefab/HeroRoom/SecondHall/Common/arcade_machine.prefab"},
        {"npc_cooker","RGPrefab/HeroRoom/SecondHall/Common/npc_cooker.prefab"},
        {"warliege_seat","RGPrefab/HeroRoom/SecondHall/Common/WarliegeSeat/warliege_seat_root.prefab"},
        {"star_light","RGPrefab/HeroRoom/SecondHall/Common/StarLight/star_light_root.prefab"},
        {"woodpile","RGPrefab/HeroRoom/SecondHall/Common/Woodpile/woodpile_root.prefab"},
        {"decorate_doctor","RGPrefab/HeroRoom/SecondHall/Common/DecorateDoctor/decorate_doctor_root.prefab"},
        {"shooter_box","RGPrefab/HeroRoom/SecondHall/Common/ShooterBox/shooter_box_root.prefab"},
        {"bard_seat","RGPrefab/HeroRoom/SecondHall/Common/BardSeat/bard_seat_root.prefab"}
    };

    private void InstantiateSecondHallSkin(int skinIndex, string decorateTag) {
        var secondHallSlot = CreateSlot(SecondHallSlotPath);
        var secondHallSkinPath = $"RGPrefab/HeroRoom/SecondHall/Skin_{skinIndex}/second_hall_{skinIndex}{decorateTag}.prefab";
        var skinGo = Instantiate(ResourcesUtil.Load<GameObject>(secondHallSkinPath));
        var secondHallSkinTransform = skinGo.transform;
        secondHallSkinTransform.SetParent(secondHallSlot);
        secondHallSkinTransform.name = SecondHallSkinName;

        ShowBattleIcon(secondHallSkinTransform);
        InitSwitchFloor();

        InstantiateFunctionObjects(secondHallSkinTransform);
    }

    private void InstantiateFunctionObjects(Transform skinTransform) {
        Transform function = skinTransform.Find("function");
        foreach (var pair in _objectPath) {
            var slot = function.Find($"{pair.Key}_slot");
            if (!slot) {
                continue;
            }

            var obj = Instantiate(ResourcesUtil.Load<GameObject>(pair.Value), function);
            obj.transform.position = slot.transform.position;
            obj.name = pair.Key;
            Destroy(slot.gameObject);
        }

        // 处理mask
        var mask = transform.Find(SecondHallMaskPath).GetComponent<SpriteRenderer>();
        mask.color = Color.black;
    }

    public static bool IsPositionInSecondHall(Vector3 target) {
        var secondHallMinCorner = new Vector2(-22.4f, 19);
        var secondHallMaxCorner = new Vector2(17, 40);
        return target.x >= secondHallMinCorner.x && target.y >= secondHallMinCorner.y && target.x <= secondHallMaxCorner.x && target.y <= secondHallMaxCorner.y;
    }

    #endregion
    
    #endregion

    private void AfterLoadSkins() {
        Debug.Log("AfterLoadSkins");
        if (RGGameConst.GameConfig.EnableSeasonFastUnlock) {
            CheckIfNeedUnlockSeason();
        }

        ItemMechFragment.CheckAndUnlockAllMechaBlueprint();
        ItemWeaponFragment.CheckAndUnlockAllBlueprint();

        UICanvas.Inst?.UpdateSeasonStoreButton();

        Loom.QueueOnMainThread(CheckIfSeason, 0.1f);

        HonoraryTitle.UnsubscribeEvents();
        HonoraryTitle.SubscribeEvents();
        HonoraryTitle.CalculateAll();
        
        //初始化荣誉称号佩戴，默认佩戴上版本已经达到铜等级的称号
        if (StatisticData.data.GetEventCount("init_honorary_title_equip") == 0) {
            if (HonoraryTitle.GetHonoraryTitle(0)?.level > HonoraryTitleLevel.Stone) {
                StatisticData.data.equippedHonoraryTitles[0] = 0;
            }
            if (HonoraryTitle.GetHonoraryTitle(1)?.level > HonoraryTitleLevel.Stone) {
                StatisticData.data.equippedHonoraryTitles[1] = 1;
            }
            if (HonoraryTitle.GetHonoraryTitle(2)?.level > HonoraryTitleLevel.Stone) {
                StatisticData.data.equippedHonoraryTitles[2] = 2;
            }
            StatisticData.data.SetEventCount("init_honorary_title_equip", 1, false);
        }
        
        
        InitObjects();

        if (GameUtil.InMultiGame()) {
            MultiGameManager.Inst.theRoomInfo.isNewGame = true;
        }

        //新的一局，重新广播
        bool mul_game = GameUtil.IsLocalMultiGame();
        if (mul_game && RGNetWorkManager.singleton.numPlayers < 4 && NetControllerManager.Inst.isServer) {
            var roomInfo = MultiGameManager.GetInstance().GetRoomInfoWithBattleData(true);
            MultiGameManager.Inst.theRoomInfo = roomInfo;
            MyDiscovery.Inst.BroadcastRoomInfo(roomInfo);
        }

        SimpleEventManager.Raise(new GoReadyEvent());

        MuseumUnlockConfig.config.UnlockProcess(StatisticData.data, unlockModel => {
            UIWindowShowObject.ShowUIWindowObject(unlockModel.sprite, unlockModel.rewardText);
            var unlockTip = unlockModel.UnlockTipText;
            if (!string.IsNullOrEmpty(unlockTip)) {
                UIWindowMuseumUnlockTips.ShowWindow(unlockTip);
            }
        });
        if (mul_game || GameUtil.IsRemoteGame()) {
            MultiGameManager.Inst.ResetNetworkLatency();
        }

        if (GameUtil.InMultiGame()) {
            SimpleEventManager.Raise(new MultiSelfEnterHeroRoomEvent());
        }

        TAUtil.UserSet(new Dictionary<string, object>() {
            { "total_skin", DataUtil.GetUnlockSkinCount() },
            { "total_hero", DataUtil.GetUnlockHeroCount() },
        });
        CommonStatistics.SetAllHero();
        CommonStatistics.SetAllSkin();
        CommonStatistics.SetAllSkill();
        CommonStatistics.SetAllAchievements();
        CommonStatistics.SetPlayerMaterials();
        CommonStatistics.SetLoopTravelStatementCount();
        CommonStatistics.SetFishChip();
        CommonStatistics.SetRoomObjUnlock();
        CommonStatistics.SetPetUnlock();
        CommonStatistics.SetPetSkillUnlock();
        CommonStatistics.SetPetIntimacy();
        CommonStatistics.SetSeasonData();
        CommonStatistics.SetAllHeroRoomSkin();
        CommonStatistics.SetPatchVersionCode();
        CommonStatistics.SetAllWeaponSkins();
        
        CommonStatistics.TrackAndSetAllHonoraryTitleList();
        
        if (RoomItemUnlockThroughBattle.GetUnlock(emRoomUnlockItem.work_shop)) {
            //记录进入客厅后，工坊解锁的时间
            if (string.IsNullOrWhiteSpace(PlayerSaveData.Inst.check_unlock_work_shop_time)) {
                PlayerSaveData.Inst.check_unlock_work_shop_time =
                    NetTime.Time.Ticks.ToString(System.Globalization.CultureInfo.InvariantCulture);
            }
        }

        //调用进入HeroRoom事件
        SimpleEventManager.Raise(new SelfEnterHeroRoomEvent());
        
        if (!MiscData.data.isReturnPlayerInit) {
            InitReturnPlayer();
        } else {
            ShowReturnPlayerHeroTrial();
        }


        ReturnPlayerFactor();
        
        DeleteErrorToken();
        
        //恶魔术士优化皮肤后，如果恶魔术士解锁自动获得优化前的皮肤
        if (DataUtil.GetHeroUnlock(emHero.Warlock) && !DataUtil.GetSkinUnlock(emHero.Warlock, 3)) {
            DataUtil.SetSkinUnlock(emHero.Warlock, 3, true);
        }
        
        //判断是否已解锁风林火山 自动获取新版风林火山
        if (DataUtil.GetSkinUnlock(emHero.Assassin, 6) && !DataUtil.GetSkinUnlock(emHero.Assassin, 21)) {
            DataUtil.SetSkinUnlock(emHero.Assassin, 21, true);
        }

        //判断是否已解锁夜莺 自动获取新版夜莺
        if (DataUtil.GetSkinUnlock(emHero.Ranger, 5) && !DataUtil.GetSkinUnlock(emHero.Ranger, 20)) {
            DataUtil.SetSkinUnlock(emHero.Ranger, 20, true);
        }
        SimpleEventManager.Raise(new AfterSelfEnterHeroRoomEvent());
    }

    private void CheckTryUnlockBlueprintSkin() {
        for (emHero hero = emHero.Knight; hero < emHero.Count; hero++) {
            var heroIdx = (int)hero;
            int skinCount = DataUtil.GetHeroSkinCount(hero);
            for (int skinIdx = 0; skinIdx < skinCount; skinIdx++) {
                var skinListValue = RGSaveManager.Inst.char_list[heroIdx].skin_list[skinIdx];
                int skinValue = PlayerSaveData.GetInt("c" + heroIdx + "_skin" + skinIdx, skinListValue);
                bool unlock = (skinListValue == 1) || (skinValue == 1);
                bool blueprintUnlock =
                    ItemData.data.GetBluePrintStatus($"blueprint_skin_{hero.ToString()}_{skinIdx}".ToLower()) ==
                    emBluePrintStatus.Researched;
                if (!unlock && blueprintUnlock) {
                    DataUtil.SetSkinUnlock(hero, skinIdx, true, false);
                }
            }
        }
    }

    private void TryGetRewardOfExpiredGameMode() {
    }
    
    private void CheckIfNeedUnlockSeason() {
        //登录三天后解锁赛季模式。
        if (!RoomItemUnlockThroughBattle.GetUnlock(emRoomUnlockItem.season_mode)) {
            var lastDay = StatisticData.data.GetEventCount(RGGameConst.ROOM_UNLOCK_SEASON_LAST_LOGIN_DAY);

            var thisDay = DateTime.Now.Subtract2000_01_01Day();

            if (lastDay < thisDay) {
                //新的一天
                StatisticData.data.AddEventCount(RGGameConst.ROOM_UNLOCK_SEASON_LOGIN_COUNT, 1, false);
                StatisticData.data.SetEventCount(RGGameConst.ROOM_UNLOCK_SEASON_LAST_LOGIN_DAY, thisDay, true);
            }

            if (StatisticData.data.GetEventCount(RGGameConst.ROOM_UNLOCK_SEASON_LOGIN_COUNT) >= 3) {
                RoomItemUnlockThroughBattle.TrySetUnlock(emRoomUnlockItem.season_mode);

                UIWindowShowObject.ShowUIWindowObject(SpriteMap.commonSprite.GetSprite("season_mode"),
                    ScriptLocalization.Get("object/season_mode"));
            }
        }
    }

    private void CheckIfSeason() {
        //检测是否由赛季模式入口进入
        if (GameUtil.IsSingleGame() && StatisticData.data.GetEventCount(RGGameConst.TITLE_NEW_GAME_MODE) == 1) {
            GameUtil.SetSeasonMode(true, false);
            var season = ConfigManager.GetCurrectUseConfig<SeasonConfig>().Season;
            if(season == Season.IronTide){
                SimpleEventManager.Raise(new SwitchToSeasonModeIronTide());
            }
        }
    }

    /// <summary>
    /// 删除bug刷到的更多token
    /// </summary>
    void DeleteErrorToken() {
        if (!SenstiveConfig.Config.needKillToken) {
            return;
        }

        Dictionary<int, int> specialWeaponTokenLimitDic = new Dictionary<int, int>() {
            { 0, 100 }, //白
            { 1, 100 }, //绿
            { 2, 80 }, //蓝
            { 3, 60 }, //紫
            { 4, 50 }, //橙
            { 5, 40 }, //红
        };
        var heroTokenLimit = 15;
        string repairErrorTokenEvent = SenstiveConfig.Config.killTokenEvent;
        if (StatisticData.data.IsEventRecord(repairErrorTokenEvent)) {
            return;
        }

        if (null == ItemData.data || null == ItemData.data.tokenTickets) {
            return;
        }

        try {
            var tickets = ItemData.data.tokenTickets.ToList();
            foreach (var mat in tickets) {
                var matKey = mat.Key;
                var matCount = mat.Value;
                int limitCount = 100;
                bool needLimit = false;
                if (string.IsNullOrWhiteSpace(matKey)) {
                    continue;
                }

                if (GameUtil.IsWeaponToken(matKey)) {
                    int weaponLv = -1;
                    if (GameUtil.IsSpecialWeaponToken(matKey)) {
                        weaponLv = GameUtil.GetSpecialWeaponTokenLevel(matKey);
                    } else {
                        weaponLv = GameUtil.GetNormalWeaponTokenLevel(matKey);
                    }

                    if (weaponLv >= 0 && specialWeaponTokenLimitDic.ContainsKey(weaponLv) &&
                        matCount > specialWeaponTokenLimitDic[weaponLv]) {
                        needLimit = true;
                        limitCount = specialWeaponTokenLimitDic[weaponLv];
                    }
                } else if ((GameUtil.IsBlueprintToken(matKey) || GameUtil.IsSeedToken(matKey) ||
                            matKey.StartsWith("token_equipment_none_")) &&
                           matCount > limitCount) {
                    needLimit = true;
                } else if (matKey.StartsWith("token_hero") && matCount > heroTokenLimit) {
                    needLimit = true;
                    limitCount = heroTokenLimit;
                }

                if (needLimit) {
                    ItemData.data.tokenTickets[matKey] = limitCount;
                    Debug.Log($"limit token: {matKey} -> {limitCount}");
                }
            }

            ItemData.Save();
            StatisticData.data.RecordEvent(repairErrorTokenEvent, true);
            Debug.Log("rep token succ");
        } catch (Exception e) {
            Debug.LogError(e);
        }
    }


    #region Init

    public void InitObjects() {
        CheckCondition();
        InitNewDay();
        try {
            GameUtil.InitNetworkNewDay();
        } catch (Exception e) {
            BuglyUtil.ReportException("InitNetworkNewDay", e);
        }

        InitPlants();
        CheckAchiement();
        SendHeroCharacterMail();
        CheckPromotePet();
        //H5联动
        OtherworldH5Promotion.Check_OtherworldH5();

        NewPlayerFactor();

        NewPlayerLevel();
        
        InitSkinDemonstration();
        
        GameUtil.ClearDirtyData();

        GameUtil.ClearDirtyData();

        CopyPlayerSaveData();
        
        FunctionForHotfix();

        UnlockDefaultHeroKnightAndPet();

        InitGameEvent();
        
        GameUtil.TryAddGameTestAwardEmail();

        FixStoreConfig();

        UnlockSnowFoxForgeIfNeed();
    }

    private static List<emHero> _removedHeroCharacter = new List<emHero>() {
        emHero.Ninja
    };

    private int GetRemovedHeroLevel8Cost(emHero hero) {
        var finalCost = 0;
        switch (hero) {
            case emHero.Ninja:
                if (DataUtil.GetHeroLevel(emHero.Assassin) > 7) {
                    finalCost += 10000;
                }

                if (DataUtil.GetHeroLevel(emHero.Engineer) > 7) {
                    finalCost += 10000;
                }
                
                break;
        }

        return finalCost;
    }
    
    private void SendHeroCharacterMail() {
        foreach (var hero in _removedHeroCharacter) {
            if (!StatisticData.data.IsEventRecord($"removed_{hero.ToString()}")) {
                var titleKey = "mail/title_key_hero";
                var detailKey = "mail/detail_key_hero";
                var senderKey = "mail/opera_sender";
                
                var title = ScriptLocalization.Get(titleKey).Replace("{0}", $"{NameUtil.GetHeroName(hero)}");
                var detail = ScriptLocalization.Get(detailKey).Replace("{0}", $"{NameUtil.GetHeroName(hero)}");
                var sender = ScriptLocalization.Get(senderKey);
                
                Dictionary<string, int> rewards = new();

                if (DataUtil.IsBlueprintResearched($"blueprint_transform_weapon_{hero.ToString().ToLower()}")) {
                    rewards = new Dictionary<string, int> {
                        { $"heroLevel_{hero.ToString()}", 7 },
                        { "material_gem", 4000 }
                    };
                }else if (GetRemovedHeroLevel8Cost(hero) > 0) {
                    rewards = new Dictionary<string, int> {
                        { "material_gem", GetRemovedHeroLevel8Cost(hero) }
                    };
                }

                if (rewards.Count == 0) {
                    return;
                }
                
                var email = BaseEmail.CreatBaseEmail(
                    title,
                    detail,
                    rewards);
                email.sender = sender;
                email.NeedLocalize = false;
                email.Star = true;

                MailBoxManager.Instance.AddEmail(email);
                StatisticData.data.RecordEvent($"removed_{hero.ToString()}", true);
            }
        }
    }
    
    private void UnlockSnowFoxForgeIfNeed() {
        UIForge.ForceUnlockForgeByAddObtainTimes(WeaponEvolutionTutorial.WeaponEvolutionTutorialWeapon);
    }

    void UnlockDefaultHeroKnightAndPet() {
        DataUtil.SetHeroUnlock(emHero.Knight, true);
        DataUtil.SetPetUnlock(emPet.Cat, true);
    }

    void InitGameEvent() {
        if (!GameEventConfig.Config.showGhostDog) {
            return;
        }
        var ghostDog = Instantiate(ResourcesUtil.Load<GameObject>("RGPrefab/HeroRoom/Hall/Common/ghost_dog/ghost_dog.prefab"), transform);
        ghostDog.transform.position = new Vector3(-60.5f, 0f, 0f);
        ghostDog.name = "ghost_dog";
    }
    

    bool newDay;
    private static readonly int ShowCurtain = Animator.StringToHash("show_curtain");

    void InitNewDay() {
        try {
            #region 判断是否过了一天

            int this_day = NetTime.Time.Subtract2000_01_01Day();
            int last_day = PlayerSaveData.Inst.last_play_time;
            newDay = false;
            if (NetTime.GotNetworkTime) {
                //拿到的是网络时间, 则直接判断
                if (this_day > last_day) {
                    PlayerSaveData.Inst.last_day_index = 0;
                    PlayerSaveData.Inst.show_post_man = 1;
                    newDay = true;
                }
            } else {
                //拿到的是本地时间, 则需判断关卡
                if (PlayerSaveData.Inst.last_day_index <= -3) {
                    //今天已经进入过客厅,且打过了1-3
                    PlayerSaveData.Inst.last_day_index = 0;
                    PlayerSaveData.Inst.show_post_man = 1;
                    newDay = true;
                }

                if (last_day != 0) {
                    //不是第一次进入游戏
                    if (this_day > last_day) {
                        if (PlayerSaveData.Inst.last_day_index >= 3) {
                            //昨天已经打过了1-3
                            PlayerSaveData.Inst.last_day_index = 0;
                            PlayerSaveData.Inst.show_post_man = 1;
                            newDay = true;
                        } else {
                            //记录:今天已进入过客厅
                            PlayerSaveData.Inst.last_day_index = -1;
                        }
                    }
                } else {
                    //第一次进入游戏, 一切都是新的
                    newDay = true;
                    PlayerSaveData.Inst.show_post_man = 1;
                }
            }

            #endregion

            #region 处理过天逻辑

            if (newDay) {
                PlayerSaveData.Inst.last_play_time = this_day;
                PlayerSaveData.Inst.show_ad_time = 0;

                if (RoomItemUnlockThroughBattle.GetUnlock(emRoomUnlockItem.quest_board)) {
                    TaskDatas.data.AddDailyTask(); //添加每日悬赏并刷新挑战
                }

                ItemData.data.sellerRefreshTime = 0;
                ItemData.data.sellerWeaponRefreshTime = 0;
                ItemData.data.bossrush_time = 0;
                ItemData.data.got_bossrush_ticket = false;
                
                ItemData.data.eggMachineTimes = ItemEggMachine.UnitEggMachineTimes;
                ItemData.data.easterEggTempPets = new List<string>();
                ItemData.data.commodities = new List<RoomComodityData> { null, null, null };
                ItemData.data.TodayStatueTimes = 0;
                ItemData.data.todayTriggerActivityNpcCount = 0;
                
                ItemData.data.magicWellFishedTimes = 0; // 重置魔法井钓鱼次数


                // 通票刷新
                ItemData.data.hopperEnteredModes.Clear();
                ObjectTVGold.PromoWatchedToday = false;

                PlayerSaveData.Inst.retired_knight_today_exchange_item_num = 0;

                StatisticData.data.SetEventCount(RGGameConst.WEAPON_EVOLUTION_TUTORIAL_TRIGGERED_TODAY, 0, true);

                if (NetTime.GotNetworkTime) {
                    ItemData.data.todayPlantMaterialAdTimes = 0;
                    ItemData.data.todayTvRandomGiftAdTimes = 0;
                    ItemData.data.todayEggMachineAdTimes = 0;
                }
            } else {
                TaskDatas.data.RefreshChallenge(); //刷新挑战
                if (Debug.isDebugBuild && OpenNewDayDebugMode) {
                    ItemData.data.eggMachineTimes = ItemEggMachine.UnitEggMachineTimes;
                    ItemData.data.TodayStatueTimes = 0;
                }
            }

            InitPostman();
            InitNewbieCheckIn();

            if (newDay) {
                FirebaseUtil.LogEvent("MaxLevel", "Level", StatisticData.data.maxLevelIndex);
                StatisticData.Save();
            }

            #endregion
        } catch (Exception e) {
            Debug.LogError(e);
        }
    }


    void InitUnlockObjects() {
        try {
            ItemData.data.UnlockRoomDecorateItem(emRoomDecorateItem.ItemEggMachine, 0);

            //新房间物品解锁规则
            NewInitUnlockObjects();

            //旧房间物品解锁规则
            //OldInitUnlockObjects();
        } catch (Exception e) {
            Debug.LogError(e);
        }
    }

    private Transform fenceLeft = null;
    private Transform fenceRight = null;
    private Transform fenceDown = null;
    private Transform fenceMagic = null;
    
    /// <summary>
    /// 新解锁逻辑
    /// </summary>
    void NewInitUnlockObjects() {
        fenceLeft = transform.Find("objects/masks/fence_left");
        fenceRight = transform.Find("objects/masks/fence_right");
        fenceDown = transform.Find("objects/masks/fence_down");
        fenceMagic = transform.Find("objects/masks/fence_magic");
        //新:emRoomUnlockItem 老:emRoomDecorateItem 关系映射
        //Dictionary<GameObject, bool> <要设置的GameObject, 解锁时该GameObject的状态>
        var oldToNewMap = new Dictionary<emRoomUnlockItem, (emRoomDecorateItem, Dictionary<GameObject, bool>)> {
            {
                emRoomUnlockItem.cellar, (emRoomDecorateItem.None, new Dictionary<GameObject, bool> {
                    { hallSkinTransform.Find("function/cellar").gameObject, true },
                    { transform.Find("room_tilemap/floor_cellar").gameObject, true },
                    { transform.Find("objects/cellar_lock_obstacle").gameObject, false },
                })
            }, {
                emRoomUnlockItem.work_shop, (emRoomDecorateItem.None, new Dictionary<GameObject, bool> {
                    { fenceRight.gameObject, false },
                    { transform.Find("objects/masks/mask_work_shop").gameObject, false },
                })
            }, {
                emRoomUnlockItem.safe, (emRoomDecorateItem.ObjectSafe, new Dictionary<GameObject, bool> {
                    { hallSkinSlots.Find("safe_slot").gameObject, true },
                })
            }, {
                emRoomUnlockItem.garden, (emRoomDecorateItem.None, new Dictionary<GameObject, bool> {
                    { fenceLeft.gameObject, false },
                    { transform.Find("objects/masks/mask_garden").gameObject, false },
                })
            }, {
                emRoomUnlockItem.books, (emRoomDecorateItem.ObjectBooks, new Dictionary<GameObject, bool> {
                    { hallSkinSlots.Find("books_slot").gameObject, true },
                })
            }, {
                emRoomUnlockItem.quest_board, (emRoomDecorateItem.None, new Dictionary<GameObject, bool> {
                    { transform.Find("objects/room_task/officer_slot").gameObject, true },
                })
            }, {
                emRoomUnlockItem.pet_food, (emRoomDecorateItem.PetFood, new Dictionary<GameObject, bool> {
                    { hallSkinSlots.Find("pet_food_slot").gameObject, RGSaveManager.Inst.GetLastPartner().Item1 == emPartner.Pet },
                })
            }, {
                emRoomUnlockItem.trash_can, (emRoomDecorateItem.ItemTrashCan, new Dictionary<GameObject, bool> {
                    { hallSkinSlots.Find("trash_can_slot").gameObject, true },
                })
            }, {
                emRoomUnlockItem.plant, (emRoomDecorateItem.ObjectPlant, new Dictionary<GameObject, bool> {
                    { hallSkinSlots.Find("plant_slot").gameObject, true },
                })
            }, {
                emRoomUnlockItem.badass, (emRoomDecorateItem.None, new Dictionary<GameObject, bool>())
            }, {
                emRoomUnlockItem.season_mode, (emRoomDecorateItem.None, new Dictionary<GameObject, bool>())
            }, {
                emRoomUnlockItem.magic_area, (emRoomDecorateItem.None, new Dictionary<GameObject, bool> {
                    { fenceDown.gameObject, false },
                    { fenceMagic.gameObject, false },
                    { transform.Find("objects/masks/mask_magic_area").gameObject, false },
                })
            },
        };

        if (_unlockItems.Count > 0) {
            //展示解锁项
            var infoList = new List<UIWindowShowItemInfo>();
            foreach (var item in _unlockItems) {
                infoList.Add(new UIWindowShowItemInfo() {
                    count = 0,
                    itemName = $"{RoomItemUnlockThroughBattle.RoomUnlockItemPrefix}{RoomItemUnlockThroughBattle.Unlock2I2Key[item]}",
                });
            }
            ItemUtility.ShowRewardWindow(infoList);
            _unlockItems.Clear();
        }

        foreach (emRoomUnlockItem em in Enum.GetValues(typeof(emRoomUnlockItem))) {
            if (oldToNewMap.TryGetValue(em, out var value)) {
                var unlock = RoomItemUnlockThroughBattle.GetUnlock(em);
                if (unlock && value.Item1 != emRoomDecorateItem.None) {
                    ItemData.data.UnlockRoomDecorateItem(value.Item1, 0);
                }

                foreach (var pair in value.Item2) {
                    pair.Key.SetActive(unlock ? pair.Value : !pair.Value);
                }
            }
        }

        var hostess = hallSkinSlots.Find("hostess_slot/hostess");
        var unlockBadass = RoomItemUnlockThroughBattle.GetUnlock(emRoomUnlockItem.badass);
        var unlockSeason = RoomItemUnlockThroughBattle.GetUnlock(emRoomUnlockItem.season_mode);
        var showHostess = unlockBadass || unlockSeason;
        hostess.gameObject.SetActive(showHostess);

        if (!DataUtil.HasUnlockABing()) {
            var statementsCount = StatisticData.data.GetEventCount(RGGameConst.ROOM_UNLOCK_STATEMENTS_COUNT);
            if (statementsCount > 1) {
                DataUtil.SetABingUnlock();
            }
        }

        InitFridge();

        bool multiGame = GameUtil.InMultiGame();
        if (multiGame && BattleData.data.netSyncData.enablePureMultiGame) {
            EnablePureMultiGameLimit();
        }
    }

    void EnablePureMultiGameLimit() {
        EnablePureMultiGameLimitWithFence(fenceLeft, "tips/pure_multi_game/lock_fence_garden");
        EnablePureMultiGameLimitWithFence(fenceRight, "tips/pure_multi_game/lock_fence_workshop");
        EnablePureMultiGameLimitWithFence(fenceDown, "tips/pure_multi_game/lock_fence_magic");
        EnablePureMultiGameLimitWithFence(fenceMagic, "tips/pure_multi_game/lock_fence_magic");
        
    }

    void EnablePureMultiGameLimitWithFence(Transform fenceTransform, string talkKey) {
        if (null == fenceTransform) {
            return;
        }

        if (null != fenceTransform.gameObject) {
            fenceTransform.gameObject.SetActive(true);
        }

        var talkTrigger = fenceTransform.GetComponent<TalkTrigger>();
        if (null == talkTrigger) {
            return;
        }

        talkTrigger.npc_name = talkKey;
    }

    
    void ShowFirstEnterArrow() {
        transform.Find("objects/first_arrow").gameObject.SetActive(PlayerSaveData.Inst.first_in_room < 3);
    }

    /// <summary>
    /// 初始化植物信息
    /// </summary>
    void InitPlants() {
        try {
            var infos = ItemData.data.plants;
            for (int i = 0; i < infos.Count; i++) {
                if (infos[i] != null) {
#if UNITY_EDITOR
                    infos[i].state += infos[i].watered ? 1 : 0;
                    infos[i].watered = false;
#endif
                    if (newDay) {
                        infos[i].state += infos[i].watered ? 1 : 0;
                        infos[i].watered = false;
                    }

                    if (infos[i].fertilized) {
                        infos[i].state++;
                        infos[i].fertilized = false;
                    }

                    var info = infos[i];
                    if (plantPots.Count > i) {
                        plantPots[i].info = info;
                    }
                }
            }

            ItemData.Save();
        } catch (Exception e) {
            Debug.LogError(e);
        }
    }

    #endregion

    /// <summary>
    /// 保存植物信息
    /// </summary>
    public void SavePlants() {
        List<PlantInfo> infos = new List<PlantInfo>();
        for (int i = 0; i < plantPots.Count; i++) {
            infos.Add(plantPots[i].info);
        }

        ItemData.data.plants = infos;
    }

    [HideInInspector]
    public (string, GameObject)[] forgeItems = new (string, GameObject)[4];

    [HideInInspector] public GameObject mechInRoom;

    private IEnumerator InitForgeWeapons() {
        forgeItems = new (string, GameObject)[4];
        for (int i = 0; i < forgeContainer.childCount; i++) {
            if (ItemData.data.forgeWeapons.Count <= i || ItemData.data.forgeWeapons[i] == null) {
                continue;
            }

            var forgeItemName = ItemData.data.forgeWeapons[i];
            var temp = i;
            yield return WeaponFactory.CreateForgeItemCoroutine(
                forgeItemName, emWeaponSource.Forge, forgeItem => {
                    try {
                        var forgeObject = forgeItem.forgeObject;
                        forgeItems[temp] = (ItemData.data.forgeWeapons[temp], forgeObject);
                        forgeObject.transform.SetParent(forgeContainer.GetChild(temp));
                        forgeObject.transform.localPosition = Vector3.zero;
                        forgeObject.transform.localEulerAngles = Vector3.zero;
                        forgeObject.GetComponent<BoxCollider2D>().offset = Vector3.zero;
                        forgeObject.GetComponent<BoxCollider2D>().size = new Vector2(3.3f, 3.3f);
                        forgeItem.need_record = true;
                        if (forgeItem is RGWeapon weapon) {
                            weapon.GetComponent<RGWeapon>().SpriteLayerSetting(12);
                            if (IsRemovedHeroTransformWeapon(weapon)) {
                                CreateReplaceWeapon(weapon, forgeContainer.GetChild(temp));
                            }
                        } else {
                            var renderers = forgeObject.GetComponentsInChildren<SpriteRenderer>();
                            foreach (var r in renderers)
                                r.sortingOrder = 12;
                        }
                        forgeContainer.GetChild(temp).localEulerAngles = new Vector3(0, 0, 270);
                    } catch (Exception e) {
                        Debug.LogError(e);
                    }
                });
        }

        try {
            forgeContainer.GetComponentInParent<ItemForge>().RearrangeWeapons();
            if (ActivityDreamManager.NeedHideCoinMech(ItemData.data.mechInRoom)) {
                ItemData.data.mechInRoom = "m_mech_0";
            }
            
            var mech = ItemMechRoom.GetInstance().CreateMech(ItemData.data.mechInRoom);
            mechInRoom = mech;
        } catch (Exception e) {
            Debug.LogError(e);
        }
    }

    private bool IsRemovedHeroTransformWeapon(RGWeapon weaapon) {
        return weaapon.GetID() == "transform_weapon_ninja";
    }

    private void CreateReplaceWeapon(RGWeapon weapon,Transform parent) {
        Destroy(weapon.gameObject);
        var newWeapon = WeaponFactory.CreateWeapon("weapon_240", emWeaponSource.None);
        Transform newWeaponTf = newWeapon.transform;
        newWeaponTf.SetParent(parent);
        newWeaponTf.localPosition = Vector3.zero;
        newWeaponTf.eulerAngles = Vector3.zero;
        newWeapon.GetComponent<BoxCollider2D>().offset = Vector3.zero;
        newWeapon.GetComponent<BoxCollider2D>().size = new Vector2(3.3f, 3.3f);
        newWeapon.GetComponent<RGWeapon>().SpriteLayerSetting(12);
    }
    
    public void SaveForgeWeapons() {
        // 根据consumeInfo删除锻造台上的武器
        if (BattleData.data.consumeInfos != null) {
            for (var i = BattleData.data.consumeInfos.Count - 1; i >= 0; --i) {
                var info = BattleData.data.consumeInfos[i];
                if (info.consumeType == emConsumeType.Weapon) {
                    ItemData.ConsumeForgeWeapon(info.name, false);
                }
            }

            ItemData.Save();
        }

        ItemData.data.mechInRoom = ItemMechRoom.GetMechName();
    }

    // 处理预约新游戏 宠物奖励未发放
    public void CheckPromotePet() {
        // 战魂 前传 两个都是宠物 前传是胡椒 战魂是布拉尼 火蜥蜴
        CheckPromotePet("otherworld", emPet.Blagny);
        CheckPromotePet("SKprequel", emPet.Cat5);
    }

    private void CheckPromotePet(string gameId, emPet pet) {
        var hasWatchPromoteVideo = PlayerSaveData.GetInt(gameId + "adversion", 0) > 0;
        var petUnlock = DataUtil.GetPetUnlock(pet);
        if (hasWatchPromoteVideo && !petUnlock) {
            UnlockPet(pet);
        }
    }

    private void UnlockPet(emPet pet) {
        int pet_idx = (int)pet;
        if (RGSaveManager.Inst.PetUnLock(pet_idx)) {
            UICanvas.ShowUIWindowObject(RGSaveManager.Inst.pet_list[pet_idx].icon,
                ScriptLocalization.Get("Pet_name_" + (pet_idx)));
            // GoogleAnalyticsV4.instance.LogEvent("Game", "unlock_pet", "pet" + pet_idx, 1);
            FirebaseUtil.LogEvent("unlock_pet", "pet_name", ((emPet)pet_idx).ToString());
        }
    }

    //检查一些成就
    public void CheckAchiement() {
        //解锁联机房
        var multiRoomSkinConfig = ScriptableConfigUtil.Load<MultiRoomSkinConfig>();
        var skinDatas = multiRoomSkinConfig.data;
        for (int i = 0; i < skinDatas.Count; i++) {
            if (skinDatas[i].unlockMethod == DecorateSkinUnlockMethodEnum.skin_series) {
                //系列皮肤解锁
                if (DataUtil.GetSkinSeriesUnlock(skinDatas[i].unlockValue, out _, out _, out _)) {
                    DataUtil.SetMultiRoomSkinUnlock(i);
                }
            } else if (skinDatas[i].unlockMethod == DecorateSkinUnlockMethodEnum.all_characters) {
                //全(普通)角色皮肤解锁
                if (DataUtil.GetAllCommonCharacterUnlock()) {
                    DataUtil.SetMultiRoomSkinUnlock(i);
                }
            }else if (skinDatas[i].unlockMethod == DecorateSkinUnlockMethodEnum.skin_list) {
                //获取指定皮肤后解锁
                if (DataUtil.GetSkinListUnlock(skinDatas[i].unlockValue, out var _, out var _, out var _)) {
                    DataUtil.SetMultiRoomSkinUnlock(i);
                }
            }
        }
        
        //猫头鹰
        AchieveInfo infoOwl = StatisticData.data.GetAchievementRecordById(38);
        if (infoOwl.unlock) {
            bool petUnlock = DataUtil.GetPetUnlock(emPet.Owl);
            if (!petUnlock) {
                UnlockPet(emPet.Owl);
            }
        }
        
        //taptap宠物
        AchieveInfo infoTap = StatisticData.data.GetAchievementRecordById(10);
        if (infoTap.unlock && !DataUtil.GetPetUnlock(emPet.TapTap)  && ChannelConfig.IsChannel("TapTap")) {
            DataUtil.SetPetUnlock(emPet.TapTap, isUnlock: true);
            var text = ScriptLocalization.Get($"Pet_name_8");
            var sprite = RGSaveManager.Inst.GetPetUISprite(emPet.TapTap.ToInt());
            UICanvas.ShowUIWindowObject(sprite, text);   
        }

        bool has_defence_tape = ItemData.data.GetMaterialCount("material_tape_defence") > 0;
        if (!has_defence_tape) {
            // var tape = ResourcesUtil.Load<GameObject>("material_tape_defence");
            // var tapeComp = tape.GetComponent<ItemTape>();
            var _info = StatisticData.data.GetAchievementRecordById(42);
            if (null != _info && _info.got_award) {
                ItemData.data.AddMaterial("material_tape_defence", 1, GetItemSource.Achievement);
                ItemData.Save();
            }
        }

        //兼容解锁土地
        AchieveInfo pot3_acinfo = StatisticData.data.GetAchievementRecordById(42);
        bool unlock_pot3_achievement = pot3_acinfo.got_award;
        bool has_pot3 = ItemData.data.IsItemUnlock("plant_pot6");
        if (!has_pot3 && unlock_pot3_achievement) {
            ItemData.data.UnlockItem("plant_pot6");
            ItemPlantPot.UnlockPlantPot(6);
            if(LogUtil.IsShowLog){LogUtil.Log("unlock_plant_pot6");}
        }

        //戏曲系列皮肤全解锁，奖励隐藏磁带
        var skinArray = new List<int> { 11, 10, 11, 10, 8, 10, 9, 10, 9, 13, 10, 8, 9, 11, 5, 4, 4 };
        int unlockOperaCount = 0;
        for (int i = 0; i < 17; i++) {
            if (DataUtil.GetSkinUnlock((emHero)i, skinArray[i])) {
                unlockOperaCount++;
            }
        }

        string titleKey = "mail/opera_title";
        string detailKey = "mail/opera_detail";
        string senderKey = "mail/opera_sender";

        if (unlockOperaCount == 17
            && !StatisticData.data.IsEventRecord(RGGameConst.OPERA_TAPE_MAIL)
            && !MailBoxManager.Instance.HasCurrentEmail(titleKey, senderKey, detailKey)
            && ItemData.data.GetMaterialCount("material_tape_opera") <= 0) {
            var email = BaseEmail.CreatBaseEmail(
                titleKey,
                detailKey,
                new Dictionary<string, int>() { { "material_tape_opera", 1 } }
            );
            email.sender = senderKey;
            email.NeedLocalize = true;
            email.Star = true;

            MailBoxManager.Instance.AddEmail(email);
            StatisticData.data.RecordEvent(RGGameConst.OPERA_TAPE_MAIL, true);
        }

        //解锁群星系列联机房获取磁带
        var starSeries = ScriptLocalization.Get("skin/subtitle_stars");
        var starTitle = ScriptLocalization.Get("mail/skin_series_tape_title").Replace("{0}", starSeries);
        var starDetail = ScriptLocalization.Get("mail/skin_series_tape_detail").Replace("{0}", starSeries);
        var starSender = ScriptLocalization.Get(senderKey);
        if (DataUtil.GetMultiRoomSkinUnlock(14) && !StatisticData.data.IsEventRecord(RGGameConst.STARS_TAPE_MAIL)
            && !MailBoxManager.Instance.HasCurrentEmail(starTitle, starSender, starDetail)
            && ItemData.data.GetMaterialCount("material_tape_stars") <= 0) {
            var email = BaseEmail.CreatBaseEmail(
                starTitle,
                starDetail,
                new Dictionary<string, int>() { { "material_tape_stars", 1 } }
            );
            email.sender = starSender;
            email.NeedLocalize = false;
            email.Star = true;
            
            MailBoxManager.Instance.AddEmail(email);
            StatisticData.data.RecordEvent(RGGameConst.STARS_TAPE_MAIL, true);
        }
        
        //解锁神仙妖怪系列联机房获取磁带
        var monsterSeries = ScriptLocalization.Get("skin/subtitle_monster");
        var monsterTitle = ScriptLocalization.Get("mail/skin_series_tape_title").Replace("{0}", monsterSeries);
        var monsterDetail = ScriptLocalization.Get("mail/skin_series_tape_detail").Replace("{0}", monsterSeries);
        var monsterSender = ScriptLocalization.Get(senderKey);
        if (DataUtil.GetMultiRoomSkinUnlock(13) 
            && !MailBoxManager.Instance.HasCurrentEmail(monsterTitle, monsterSender, monsterDetail) 
            && ItemData.data.GetMaterialCount("material_tape_monster") <= 0) {
            var email = BaseEmail.CreatBaseEmail(
                starTitle,
                starDetail,
                new Dictionary<string, int>() { { "material_tape_monster", 1 } }
            );
            email.sender = starSender;
            email.NeedLocalize = false;
            email.Star = true;
            
            MailBoxManager.Instance.AddEmail(email);
        }
        
        
        //解锁三国系列联机房获取磁带
        var kingdomsSeries = ScriptLocalization.Get("skin/subtitle_three_kingdoms");
        var kingdomsTitle = ScriptLocalization.Get("mail/skin_series_tape_title").Replace("{0}", kingdomsSeries);
        var kingdomsDetail = ScriptLocalization.Get("mail/skin_series_tape_detail").Replace("{0}", kingdomsSeries);
        var kingdomsSender = ScriptLocalization.Get(senderKey);
        if (DataUtil.GetMultiRoomSkinUnlock(17) && !StatisticData.data.IsEventRecord(RGGameConst.KINGDOMS_TAPE_MAIL)
            && !MailBoxManager.Instance.HasCurrentEmail(kingdomsTitle, kingdomsSender, kingdomsDetail)
            && ItemData.data.GetMaterialCount("material_tape_bgm_multi_room_skin_17") <= 0) {
            var email = BaseEmail.CreatBaseEmail(
                kingdomsTitle,
                kingdomsDetail,
                new Dictionary<string, int>() { { "material_tape_bgm_multi_room_skin_17", 1 } }
            );
            email.sender = kingdomsSender;
            email.NeedLocalize = false;
            email.Star = true;
            
            MailBoxManager.Instance.AddEmail(email);
            StatisticData.data.RecordEvent(RGGameConst.KINGDOMS_TAPE_MAIL, true);
        }
        
        
        
        

        //系列皮肤全解锁，奖励隐藏磁带
        var autoTapeMailConfig = ScriptableConfigUtil.Load<AutoTapeMailConfig>();
        var tapeMailDatas = autoTapeMailConfig.data;
        for (int i = 0; i < tapeMailDatas.Count; i++) {
            var tape = tapeMailDatas[i].tape;
            //已有磁带
            if (ItemData.data.GetMaterialCount(tape) > 0) {
                continue;
            }

            var skin_series_key = tapeMailDatas[i].skin_series_key;
            if (DataMgr.UnlockConditionData.IsTapeMailLockedByConfig(skin_series_key)) {
                continue;
            }

            if (StatisticData.data.IsEventRecord($"{RGGameConst.TAPE_MAIL}_{skin_series_key}")) {
                continue;
            }

            var skin_series = ScriptLocalization.Get(skin_series_key);
            var title = ScriptLocalization.Get("mail/skin_series_tape_title").Replace("{0}", skin_series);
            var detail = ScriptLocalization.Get("mail/skin_series_tape_detail").Replace("{0}", skin_series);
            var sender = ScriptLocalization.Get(senderKey);

            //未收到该邮件
            if (MailBoxManager.Instance.HasCurrentEmail(title, sender, detail))
                continue;

            //未全解锁系列皮肤
            if (!DataUtil.GetSkinSeriesUnlock(skin_series_key, out _, out _, out _))
                continue;

            var email = BaseEmail.CreatBaseEmail(title, detail, new Dictionary<string, int>() { { tape, 1 } });
            email.sender = sender;
            email.NeedLocalize = false;
            email.Star = true;

            MailBoxManager.Instance.AddEmail(email);
            StatisticData.data.RecordEvent($"{RGGameConst.TAPE_MAIL}_{skin_series_key}", true);
        }

        var paidSkinTapeMailDatas = autoTapeMailConfig.paidSkinData;
        foreach (var data in paidSkinTapeMailDatas) {
            var tapes = data.tapes;
            var notGetTapes = tapes.Where(tape => ItemData.data.GetMaterialCount(tape) == 0).ToList();
            //已有磁带
            if (notGetTapes.Count == 0) {
                continue;
            }

            var title = ScriptLocalization.Get(data.emailTitleKey);

            if (string.IsNullOrEmpty(title)) {
                // 还没有本地化，本地化有了再执行
                if (LogUtil.IsShowLog) {
                    LogUtil.LogError($"--- no title, break {data.emailTitleKey}");
                }
                break;
            }

            var skinKey = $"c{(int)data.hero}_{data.skinIndex}";
            var eventName = $"{RGGameConst.PAID_SKIN_TAPE_MAIL}_{skinKey}";
            if (StatisticData.data.IsEventRecord(eventName)) {
                continue;
            }

            StringBuilder sb = new StringBuilder();
            for (var j = 0; j < notGetTapes.Count; j++) {
                string notGetTape = notGetTapes[j];
                sb.Append(ScriptLocalization.Get(notGetTape));
                if (j != notGetTapes.Count - 1) {
                    sb.Append("][");
                }
            }

            var tapesDesc = sb.ToString();
            // var title = emailTitle;
            var detail = ScriptLocalization.Get(data.emailContentKey).Replace("{0}", tapesDesc);
            var sender = ScriptLocalization.Get(senderKey);
            detail += "\n";

            if (MailBoxManager.Instance.HasCurrentEmail(title, sender, detail))
                continue;

            //未解锁皮肤
            if (!DataUtil.GetSkinUnlock(data.hero, data.skinIndex))
                continue;

            var rewards = new Dictionary<string, int>();
            foreach (var tape in notGetTapes) {
                if (!rewards.ContainsKey(tape)) {
                    rewards.Add(tape, 1);
                }
            }

            var email = BaseEmail.CreatBaseEmail(title, detail, rewards);
            email.sender = sender;
            email.NeedLocalize = false;
            email.Star = true;

            MailBoxManager.Instance.AddEmail(email);
            StatisticData.data.RecordEvent(eventName, true);
        }
        
        //Bug成就
        AchieveInfo ac_info = StatisticData.data.GetAchievementRecordById(37);
        if (null != ac_info && ac_info.unlock && !DataUtil.GetPetUnlock(emPet.Bug)) {
            DataUtil.SetPetUnlock(emPet.Bug, true);
            if(LogUtil.IsShowLog){LogUtil.Log("unlock pet bug");}
        }


        //精灵 奥林匹斯山的月光皮肤
        CheckAchievementSkinReward(75, emHero.Elves, 11);
        
        //法师 穆桂英皮肤
        CheckAchievementSkinReward(46, emHero.Mage, 11);

        // 警官解锁保底
        if (StatisticData.data.GetAchievementRecordById(10) is { } officerUnlockAchieve && officerUnlockAchieve != null && 
            officerUnlockAchieve.unlock && officerUnlockAchieve.got_award && !DataUtil.GetHeroUnlock(emHero.Officer)) {
            DataUtil.SetHeroUnlock(emHero.Officer, true);
        }

        // 警官 钟馗皮肤
        var achievementData = StatisticData.data.GetAchievementRecordById(47);
        if (!achievementData.unlock) {
            var achievement = AchieveInfos.info.GetAchievementInfoById(47);
            foreach (var condition in achievement.unlock_condis) {
                var count = DataUtil.GetEnemyKillCountById(condition.target_str);
                AchieveInfos.info.CheckUnlock(AchieveInfos.AchievementType.KillCountWithSpecialEnemy, count,
                    condition.target_str, emHero.None);
            }
        }

        CheckAchievementSkinReward(47, emHero.Officer, 4);
        
        achievementData = StatisticData.data.GetAchievementRecordById(8);
        if (!achievementData.unlock) {
            int passBadassTimes = StatisticData.data.GetEventCount(RGGameConst.PASS_BADASS);
            AchieveInfos.info.CheckUnlock(AchieveInfos.AchievementType.PassCount2,
                passBadassTimes, "", emHero.None);
        }

        achievementData = StatisticData.data.GetAchievementRecordById(18);
        // 悠悠球再次检查解锁
        if (!achievementData.unlock) {
            int skillUseTimes = StatisticData.data.c02_skill_times;
            AchieveInfos.info.CheckUnlock(AchieveInfos.AchievementType.SkillUseCount, skillUseTimes, "",
                emHero.Ranger);
        }

        achievementData = StatisticData.data.GetAchievementRecordById(6);
        if (!achievementData.unlock) {
            int passTimes = StatisticData.data.GetEventCount(RGGameConst.PASS);
            int passBadassTimes = StatisticData.data.GetEventCount(RGGameConst.PASS_BADASS);
            AchieveInfos.info.CheckUnlock(AchieveInfos.AchievementType.PassCount,
                passTimes + passBadassTimes, "", emHero.None);
        }

        ItemDecorationCaptain.TryUnlockHeroRoomSkinLockedBySkinList(null);
        
        CheckAchievementUnlockGuarentee();
        
        //刺客表情包皮肤恢复逻辑
        var achievementInfoAssassin = StatisticData.data.GetAchievementRecordById(90);
        if (achievementInfoAssassin.unlock &&
            !RGSaveManager.Inst.IsHeroSkinUnlock((int)emHero.Assassin, 15)) {
            RGSaveManager.Inst.HeroSkinUnlock((int)emHero.Assassin, 15);
            var text =
                ScriptLocalization.Get($"Character3_name_skin15");
            var sprite = RGSaveManager.Inst.GetHeroUISprite((int)emHero.Assassin, 15);
            UICanvas.ShowUIWindowObject(sprite, text);
        }
        
        //圣骑士耐力训练成就皮肤补发
        var achievementInfoPaladin = StatisticData.data.GetAchievementRecordById(27);
        if (achievementInfoPaladin.unlock &&
            !RGSaveManager.Inst.IsHeroSkinUnlock((int)emHero.Paladin, 6)) {
            RGSaveManager.Inst.HeroSkinUnlock((int)emHero.Paladin, 6);
        }
        //检查部分皮肤解锁方式
        CheckSkinUnlock();
        
        //骑士鸮鹦鹉解锁方式调整逻辑
        if (!DataUtil.GetSkinUnlock(emHero.Knight, 17)) {
            RGSaveManager.Inst.char_list[0].skin_list[17] = 9999;
        }
        
        //戏法师解锁方式调整
        if (!RGSaveManager.Inst.char_list[34].unlock && !ActivityDreamManager.IsActivityEnableChannel()) {
            RGSaveManager.Inst.char_list[34].unlock_gem = 12000;
        }
        
        //荒诞之梦解锁方式调整
        if (!RGSaveManager.Inst.char_list[34].skin_list[1].Equals(1) &&
            !ActivityDreamManager.IsActivityEnableChannel()) {
            RGSaveManager.Inst.char_list[34].skin_list[1] = -8;
        }

        //矿工掘金骑手解锁方式调整逻辑
        if (!DataUtil.GetSkinUnlock(emHero.Miner, 5)) {
            RGSaveManager.Inst.char_list[24].skin_list[5] = -1;
        }
        
        //骑士 圣骑士我们走 皮肤解锁方式调整逻辑
        if (!DataUtil.GetSkinUnlock(emHero.Knight, 15)) {
            RGSaveManager.Inst.char_list[0].skin_list[15] = 0;
        }
        
        // 领主 获取方式调整
        if (!DataUtil.GetHeroUnlock(emHero.Warliege)) {
            RGSaveManager.Inst.char_list[30].unlock_gem = 12000;
        }
        
        // 宠物招财 获取方式调整
        if (!DataUtil.GetPetUnlock(emPet.Cat7)) {
            RGSaveManager.Inst.pet_list[39].unlock_gem = -4;
        }
        
        //处理老存档里超时空忍者等级问题
        if (!DataUtil.GetHeroUnlock(emHero.Ninja)) {
            DataUtil.SetHeroLevel(emHero.Ninja,0);
        }
        
        // 补发漏掉的宠物成就
        StatisticUtil.FixLostPetAches();

        // 检查 蜘蛛训练师 成就
        achievementData = StatisticData.data.GetAchievementRecordById(25);
        if (!achievementData.unlock) {
            var enemyID = "e_spider01";
            var killCount = DataUtil.GetEnemyKillCountById(enemyID);
            var achievement_info = AchieveInfos.info.CheckUnlock(AchieveInfos.AchievementType.UnlockMountSpider,
                killCount,
                enemyID, emHero.None);
            if (null != achievement_info) //解锁蜘蛛
            {
                ItemData.data.ResearchBlueprint("mspider");
            }
        }

        //累计解锁8把神话武器
        var unlockMythicWeaponCount = DataUtil.GetUnlockMythicWeaponCount();
        AchieveInfos.info.CheckUnlock(AchieveInfos.AchievementType.UnlockEnoughMythicWeapon, unlockMythicWeaponCount, string.Empty, emHero.None);

        // 把当前已解锁的次数都计算到购买次数
        if (!StatisticData.data.IsEventRecord(RGGameConst.RECORD_UNLOCKED_2_BUY_TIMES)) {
            try {
                StatisticData.data.RecordEvent(RGGameConst.RECORD_UNLOCKED_2_BUY_TIMES, true);
                int _unlockCount = RestoreRole.GetBuyTimesByUnlocked();
                StatisticData.data.buy_good_times += _unlockCount;
                if (StatisticData.data.buy_good_times >= RGGameConst.UNLOCK_PET36_BUY_TIMES) {
                    AchieveInfos.info.CheckUnlock(AchieveInfos.AchievementType.BuyGood30Times, StatisticData.data.buy_good_times, "", emHero.None);
                }

                StatisticData.Save();
                if(LogUtil.IsShowLog){LogUtil.Log($"InCreaseBuyTimes buy_times:{StatisticData.data.buy_good_times} unlockedCount:{_unlockCount}");}
            } catch (Exception e) {
                Debug.LogError(e);
            }
        }

        // 修复周挑战成就数据达成却没有完成
        AchieveInfos.info.CheckUnlock(emGameMode.Normal, emGameType.RemoteMulti, AchieveInfos.AchievementType.CompleteWeeklyFactorTypes, 0, "", 0);
        
        //检查"小心感冒"成就
        achievementData = StatisticData.data.GetAchievementRecordById(111);
        if (!achievementData.unlock) {
            SimpleEventManager.AddEventListener<ChoseSkinConfirm>(CheckAchieveCarefulForCold);
        }

        //检查成就-扭蛋爱好者
        AchieveChecker119.Check();

        // 气宗解锁了但是"不打不相识"成就没解锁，这里补一下成就
        if (DataUtil.GetHeroUnlock(emHero.Airbender) && !StatisticData.data.GetAchievementRecordById(85).unlock) {
            AchieveInfos.info.CheckUnlock(AchieveInfos.AchievementType.KillAirbender, 1, null, emHero.None);
        }

        CheckEmailAndAchievementConsistency();
    }

    private void FixWeapon200() {
        // 替换之前的网瘾少女为少年，现在不补发了
        if (!StatisticData.data.IsEventRecord("give_weapon_200_s_1") &&
            DataMgr.WeaponEvolutionModule.HasUnlockWeaponSkin("weapon_121", 1) &&
            !DataMgr.WeaponEvolutionModule.HasUnlockWeaponSkin("weapon_200", 1)) {
            DataMgr.WeaponEvolutionModule.UnlockWeaponSkin("weapon_200", 1);
            var sprite = SpriteUtility.GetWeaponSkinSprite("weapon_200", 1, false);
            var text = ItemUtility.GetWeaponSkinName("weapon_200", 1);
            UIWindowShowObject.ShowUIWindowObject(sprite, text);
            Debug.Log("give weapon_200_s_1");
        }

        StatisticData.data.RecordEvent("give_weapon_200_s_1", true);
    }
    
    static void CheckEmailAndAchievementConsistency() {
        foreach (var email in MailBoxManager.Instance.AlreadyGetEmails) {
            if (email.EmailType == EmailType.AchievementAward) {
                var achievementId = email.Uid;
                if (achievementId == 30 && !StatisticData.data.GetAchievementRecordById(30).unlock) {
                    StatisticData.data.GetAchievementRecordById(30).unlock = true;
                } else if (achievementId == 31 && !StatisticData.data.GetAchievementRecordById(31).unlock) {
                    StatisticData.data.GetAchievementRecordById(31).unlock = true;
                } else if (achievementId == 92 && !StatisticData.data.GetAchievementRecordById(92).unlock) {
                    StatisticData.data.GetAchievementRecordById(92).unlock = true;
                } else if (achievementId == 93 && !StatisticData.data.GetAchievementRecordById(93).unlock) {
                    StatisticData.data.GetAchievementRecordById(93).unlock = true;
                } else if (achievementId == 116 && !StatisticData.data.GetAchievementRecordById(116).unlock) {
                    StatisticData.data.GetAchievementRecordById(116).unlock = true;
                }
            }
        }
    }

    private static void CheckSkinUnlock() {
        //骑士鸮鹦鹉解锁方式调整逻辑
        if (!DataUtil.GetSkinUnlock(emHero.Knight, 17)) {
            RGSaveManager.Inst.char_list[0].skin_list[17] = 9999;
        }
        
        //戏法师解锁方式调整
        if (!RGSaveManager.Inst.char_list[34].unlock) {
            RGSaveManager.Inst.char_list[34].unlock_gem = 12000;
        }
        
        //荒诞之梦解锁方式调整
        if (!RGSaveManager.Inst.char_list[34].skin_list[1].Equals(1) &&
            !ActivityDreamManager.IsActivityEnableChannel()) {
            RGSaveManager.Inst.char_list[34].skin_list[1] = -8;
        }

        //矿工掘金骑手解锁方式调整逻辑
        if (!DataUtil.GetSkinUnlock(emHero.Miner, 5)) {
            RGSaveManager.Inst.char_list[24].skin_list[5] = -1;
        }
        
        //骑士 圣骑士我们走 皮肤解锁方式调整逻辑
        if (!DataUtil.GetSkinUnlock(emHero.Knight, 15)) {
            RGSaveManager.Inst.char_list[0].skin_list[15] = 0;
        }
        
        // 领主 获取方式调整
        if (!DataUtil.GetHeroUnlock(emHero.Warliege)) {
            RGSaveManager.Inst.char_list[30].unlock_gem = 12000;
        }
        
        // 宠物招财 获取方式调整
        if (!DataUtil.GetPetUnlock(emPet.Cat7)) {
            RGSaveManager.Inst.pet_list[39].unlock_gem = -4;
        }
        
        //狼人-驯鹿 对于兑换过的玩家解锁
        if (StatisticData.data.Christmas2024RedeemRecords.ContainsKey(1) &&
            StatisticData.data.Christmas2024RedeemRecords[1] >= 1){
            RGSaveManager.Inst.char_list[(int)emHero.Werewolf].skin_list[23] = 1;
        } else {
            //如果未解锁则改为隐藏
            if (!RGSaveManager.Inst.char_list[(int)emHero.Werewolf].skin_list[23].Equals(1)) {
                RGSaveManager.Inst.char_list[(int)emHero.Werewolf].skin_list[23] = -8;
            }
        }
        
        // 补发漏掉的宠物成就
        StatisticUtil.FixLostPetAches();
    }

    void CheckAchieveCarefulForCold(ChoseSkinConfirm e) {
        SimpleEventManager.RemoveListener<ChoseSkinConfirm>(CheckAchieveCarefulForCold);
        StartCoroutine(IECarefulForCold());
    }
    
    IEnumerator IECarefulForCold() {
        WaitForSeconds oneSecond = new WaitForSeconds(1);
        int cur = 0;
        //5分钟
        while (cur <= 300) {
            var localPlayer = RGGameSceneManager.Inst.controller; 
            if (localPlayer && IsPositionInSecondHall(localPlayer.transform.position)) {
                cur++;
                yield return oneSecond;
            } else {
                cur = 0;
                yield return oneSecond;
            }
        }
        //达成
        AchieveInfos.info.CheckUnlock(AchieveInfos.AchievementType.CarefulForCold, 0, "", 0);
    }
    
    private static void CheckAchievementSkinReward(int achievementId, emHero heroType, int skinIndex) {
        AchieveInfo achievementInfo = StatisticData.data.GetAchievementRecordById(achievementId);
        if (null != achievementInfo && achievementInfo.unlock && !DataUtil.GetSkinUnlock(heroType, skinIndex)) {
            DataUtil.SetSkinUnlock(heroType, skinIndex, true);
            if(LogUtil.IsShowLog){LogUtil.Log($"unlock {heroType} skin {skinIndex}");}
        }
    }

    // 由于一些未知原因，一些结算时检测的成就达成条件了但没有解锁，这里触发一次检测
    private void CheckAchievementUnlockGuarentee(){
        AchieveInfos.info.CheckUnlock(AchieveInfos.AchievementType.ForgeDustWeapons, RGGameConst.DUST_WEAPON_UNLOCK,null, emHero.None);
    }
    
    private const string WayBattlePath = "function/way_battle";

    private void ShowBattleIcon(Transform skinTransform) {
        Transform wayBattleSingle = skinTransform.Find($"{WayBattlePath}/root/way_battle_single");
        Transform wayBattleMulti = skinTransform.Find($"{WayBattlePath}/root/way_battle_multi");
        Transform wayBattleMultiRemote = skinTransform.Find($"{WayBattlePath}/root/way_battle_multi_remote");

        if (null != wayBattleSingle && null != wayBattleMulti && null != wayBattleMultiRemote) {
            bool multiGame = GameUtil.InMultiGame();
            bool remoteGame = GameUtil.IsRemoteGame();
            wayBattleSingle.gameObject.SetActive(!multiGame);
            wayBattleMulti.gameObject.SetActive(multiGame && !remoteGame);
            wayBattleMultiRemote.gameObject.SetActive(remoteGame);
        }
    }

    private void InitSwitchFloor() {
        var items = transform.GetComponentsInChildren<ItemSwitchFloor>();
        if (items == null || items.Length == 0) {
            return;
        }

        var floorDict = new Dictionary<FloorEnum, Transform>();
        foreach (var item in items) {
            floorDict[item.from] = item.transform;
        }

        foreach (var item in items) {
            if (floorDict.TryGetValue(item.to, out Transform target)) {
                item.Target = target;
            }
        }
    }

    private static void NewPlayerFactor() {
        if (BattleData.data.IsLoopTravel) {
            return;
        }

        var factorName = RGGameConst.NewPlayerFactorID;
        var factorCount = StatisticData.data.GetEventCount(RGGameConst.NEW_PLAYER_FACTOR_COUNT);
        if (factorCount <= 0) {
            return;
        }

        StatisticData.data.SetEventCount(RGGameConst.NEW_PLAYER_FACTOR_SPRITE, factorCount, true);
        StaticCustomFactorManager.AddCustomFactor(factorName);
        var factorShow = StatisticData.data.IsEventRecord(RGGameConst.NEW_PLAYER_TUTORIAL_SHOW_WINDOW);
        var localizedText = ScriptLocalization.Get("new_player_factor_desc");
        if (GameUtil.CompleteAdvTutorial())
            localizedText = ScriptLocalization.Get("new_player_factor_desc2", "新手因子会帮助你更轻松的开始地牢的旅程！");
        else if (StatisticData.data.IsEventRecord(RGGameConst.SKIP_NEW_PLAYER_TUTORIAL))
            localizedText = ScriptLocalization.Get("new_player_factor_skip_desc",
                "我猜你一定是老手了，\n虽然你跳过了新手教学，我们还是为你奉上这些奖励");
        if (factorCount == RGGameConst.NEW_PLAYER_ENTER_NORMAL_MODE_VALUE && !factorShow) {
            StatisticData.data.RecordEvent(RGGameConst.NEW_PLAYER_TUTORIAL_SHOW_WINDOW, true);
            var uiWinodwShowObject =
                UIManager.Inst.OpenUIView<UIWindowShowObject>("window_show_object_swamp_factor");
            uiWinodwShowObject.name = "new_player_factor_window";
            uiWinodwShowObject.SetUpWindow(StaticCustomFactorManager.GetCurrentSprite(), "");
            var descText = uiWinodwShowObject.transform.Find("desc_text").GetComponent<Text>();
            descText.text = localizedText;
            uiWinodwShowObject.transform.Find("ban_buff").gameObject.SetActive(false);
            if (!StatisticData.data.IsEventRecord(RGGameConst.NEW_PLAYER_TUTORIAL_GEM_REWARD)) {
                StatisticData.data.RecordEvent(RGGameConst.NEW_PLAYER_TUTORIAL_GEM_REWARD, true);
                RGSaveManager.Inst.AddGem(1000, emObtainGemType.Tutorial);
                uiWinodwShowObject.OnWindowCloseClick += () => {
                    var uIWinodwShowObjects =
                        UIManager.Inst.OpenUIView<UIWindowShowObjects>("window_show_objects_desc");
                    uIWinodwShowObjects.awake = true;
                    uIWinodwShowObjects.AddItem(ItemData.GemName,
                        ScriptLocalization.Get(ItemData.GemName) + "×1000");
                    uIWinodwShowObjects.SetUpWindow(new PickableInfo[0]);
                    var desc = uIWinodwShowObjects.transform.Find("desc_text").GetComponent<Text>();
                    desc.text = ScriptLocalization.Get("teaching/new_player_tutorial_reward", "完成新手教程，获得宝石奖励");
                    GameManager.Inst.PauseGame();
                };
                // 新手第一次进入检查邀请码
                ActivityReturnPlayerH5Manager.HandleActivityReturnPlayerH5();
            }
        }

        if(LogUtil.IsShowLog){LogUtil.Log($"新手因子log : 进入房间，添加新手因子");}
        SimpleEventManager.Raise(new UpdateFactorBarEvent());
    }

    #region ReturnPlayer

    private void InitReturnPlayer() {
        if (!GameUtil.IsSingleGame()) {
            return;
        }
        
        if (!DataMgr.ReturnPlayerData.IsReturnPlayer()) {
            return;
        }

        if (MiscData.data.isReturnPlayerInit) {
            return;
        }

        if (GameUtil.HasNewPlayerFactor) {
            return;
        }

        MiscData.data.isReturnPlayerInit = true;
        MiscData.data.returnPlayerFactorCount = RGGameConst.RETURN_PLAYER_FACTOR_MAX_COUNT;
        MiscData.Save();

        var uiWinodwShowObject = UIManager.Inst.OpenUIView(new UIViewOpenSetting<UIWindowShowObject>() {
            uiViewAssetPath = "window_show_object_swamp_factor",
            OnAfterHide = _ => { ShowReturnPlayerHeroTrial(); },
        });
        uiWinodwShowObject.name = "return_player_factor_window";
        uiWinodwShowObject.SetUpWindow(DataMgr.ReturnPlayerData.GetReturnPlayerFactorIcon(), "");
        var descText = uiWinodwShowObject.transform.Find("desc_text").GetComponent<Text>();
        descText.text = ScriptLocalization.Get("task/ReturnPlayer_desc", "欢迎回来，这个因子将在持续时间内赋予你梦幻开局和2次额外复活机会（仅对关卡模式生效）");
        uiWinodwShowObject.transform.Find("ban_buff").gameObject.SetActive(false);
    }

    private static void ShowReturnPlayerHeroTrial() {
        if (!GameUtil.IsSingleGame()) {
            return;
        }

        if (!DataMgr.ReturnPlayerData.IsReturnPlayer()) {
            return;
        }

        if (MiscData.data.returnPlayerTrailHero != emHero.None) {
            return;
        }

        var trialHeroShortlist = DataMgr.ReturnPlayerData.TrialHeroShortlist;
        if (trialHeroShortlist.Count <= 0) {
            return;
        }

        UIWindowReturnPlayerHeroTrial.ShowWindow();
    }

    private static void ReturnPlayerFactor() {
        if (!GameUtil.IsSingleGame()) {
            return;
        }

        if (!DataMgr.ReturnPlayerData.IsReturnPlayer()) {
            return;
        }

        var factorCount = DataMgr.ReturnPlayerData.GetReturnPlayerFactorCount();

        if (factorCount <= 0) {
            return;
        }

        BattleData.data.SetFactor(emBattleFactor.ReturnPlayer, true, true);
    }

    #endregion

    private static void NewPlayerLevel() {
        if (!GameUtil.NeedEnterNewPlayerLevel()) {
            return;
        }

        var count = GameUtil.GetNewPlayerLevelCount();
        var rewarded = StatisticData.data.IsEventRecord(RGGameConst.NEW_PLAYER_TUTORIAL_GEM_REWARD);
        var group = ABTest.GetNewPlayerLevelTestGroup();
        if (rewarded || count < NewPlayerLevelTestUtil.LevelMaxCount[group]) {
            return;
        }

        RGSaveManager.Inst.AddGem(1000, emObtainGemType.Tutorial);
        StatisticData.data.RecordEvent(RGGameConst.NEW_PLAYER_TUTORIAL_GEM_REWARD, true);

        var str = ScriptLocalization.Get("teaching/new_player_tutorial_reward", "完成新手教程，获得宝石奖励");
        if (StatisticData.data.IsEventRecord(RGGameConst.SKIP_NEW_PLAYER_TUTORIAL)) {
            str = ScriptLocalization.Get("new_player_factor_skip_desc", "我猜你一定是老手了，\n虽然你跳过了新手教学，我们还是为你奉上这些奖励");
        }

        var windowShowObjects =
            UIManager.Inst.OpenUIView<UIWindowShowObjects>("window_show_objects_desc");
        windowShowObjects.awake = true;
        windowShowObjects.AddItem(ItemData.GemName,
            ScriptLocalization.Get(ItemData.GemName) + "×1000");
        windowShowObjects.SetUpWindow(new PickableInfo[0]);
        var desc = windowShowObjects.transform.Find("desc_text").GetComponent<Text>();
        desc.text = str;
        
        // 新手第一次进入检查邀请码
        ActivityReturnPlayerH5Manager.HandleActivityReturnPlayerH5();
    }

    public void InitSkinDemonstration() {
       GameObject temp = ResourcesUtil.Load<GameObject>("RGPrefab/Decorate/Room/skin_demonstration.prefab");
       GameObject go = Instantiate(temp, transform);
       go.name = "skin_demonstration";
       go.transform.position = new Vector3(65, 65, 0);
    }
    
    //新手任务和tap限定活动的完成状态判断之前是写在PlayerSaveData中，现在迁移到StatisticData里。
    private void CopyPlayerSaveData() {
        TaskSystem.TaskManager.Instance.CreateNewBeginnerTaskByConfig();
        var isCopyedPlayerSaveData = "isCopyedPlayerSaveData";
        var activityComplete = Activities.StarFire.Scripts.ActivityStarFireManager.ActivityComplete;
        if (!StatisticData.data.IsEventRecord(isCopyedPlayerSaveData)) {
            //tap限定活动
            if (PlayerSaveData.GetInt(activityComplete, 0) == 1 && DataUtil.GetSkinUnlock(emHero.Engineer, 25)) {
                StatisticData.data.RecordEvent(activityComplete, true);
            }

            if (PlayerSaveData.Inst.BeginnerTaskState == 0) {
                StatisticData.data.SetEventCount("BeginnerTaskState", PlayerSaveData.Inst.BeginnerTaskState, true);
                StatisticData.data.RecordEvent(isCopyedPlayerSaveData, true);
            }
        }
    }
    
    // 方便测热更
    public void FunctionForHotfix() {
    }

    public void FixStoreConfig() {
        try {
            bool needFetchConfig = false;

            if (!StaticFishChipStoreService.HasGetStoreItem) {
                var errStr = FishChipStoreItemService.GetFishChipStoreErrCode();

                var fishChipStoreConfig = ConfigManager.GetCurrectUseConfig<NewDynamicConfig.FishChipStoreConfig>();
                string myLocalConfigPath = fishChipStoreConfig.GetLocalConfigPath();

                if (!string.IsNullOrWhiteSpace(myLocalConfigPath)) {
                    var localManifestPath = Path.Combine(ConfigManager.LocalFileRoot, myLocalConfigPath,
                        "manifest.json");
                    var localPath = Path.Combine(ConfigManager.LocalFileRoot, myLocalConfigPath,
                        "FishChipStoreConfig.json");

                    if (File.Exists(localPath)) {
                        File.Delete(localPath);
                    }

                    if (File.Exists(localManifestPath)) {
                        errStr += $"\r\n {File.ReadAllText(localManifestPath)} \r\n";
                        File.Delete(localManifestPath);
                    }

                    PlayerSaveData.SetInt("RenewFishStoreConfig", 1);
                    PlayerSaveData.Save();
                }

                needFetchConfig = true;

                BuglyUtil.ReportException("fishChipStoreExp", new Exception(errStr));
            } else if (PlayerSaveData.GetInt("RenewFishStoreConfig", 0) == 1) {
                PlayerSaveData.SetInt("RenewFishStoreConfig", 2);
                BuglyUtil.ReportException("RenewFishStoreConfigSuc",
                    new Exception("already finish store config renew 1"));
            }


            if (!StaticSeasonStoreService.HasGetStoreItem) {
                var errStr = SeasonStoreItemService.GetSeasonStoreErrCode();

                var seasonStoreConfig = ConfigManager.GetCurrectUseConfig<NewDynamicConfig.SeasonStoreConfig>();
                string myLocalConfigPath = seasonStoreConfig.GetLocalConfigPath();

                if (!string.IsNullOrWhiteSpace(myLocalConfigPath)) {
                    var localPath = Path.Combine(ConfigManager.LocalFileRoot, myLocalConfigPath,
                        "SeasonStoreConfig.json");
                    if (File.Exists(localPath)) {
                        File.Delete(localPath);
                    }

                    var localManifestPath = Path.Combine(ConfigManager.LocalFileRoot, myLocalConfigPath,
                        "manifest.json");
                    if (File.Exists(localManifestPath)) {
                        errStr += $"\r\n {File.ReadAllText(localManifestPath)} \r\n";
                        File.Delete(localManifestPath);
                    }

                    PlayerSaveData.SetInt("RenewSeasonStoreConfig", 1);
                    PlayerSaveData.Save();
                }

                needFetchConfig = true;

                BuglyUtil.ReportException("SeasonStoreExp", new Exception(errStr));
            } else if (PlayerSaveData.GetInt("RenewSeasonStoreConfig", 0) == 1) {
                PlayerSaveData.SetInt("RenewSeasonStoreConfig", 2);
                Debug.Log("RenewSeasonStoreConfigSuc");
                BuglyUtil.ReportException("RenewSeasonStoreConfigSuc",
                    new Exception("already finish season store config renew 2"));
            }


            if (needFetchConfig) {
                // Debug.Log("try fetchconfig again");
                ConfigManager.isUseBase = true;
                ConfigManager.FetchConfig();
            }
        } catch (Exception exception) {
            Debug.LogError(exception);
            BuglyUtil.ReportException("FixStoreConfig", exception);
        }
    }

    public void CheckCondition() {
        SimpleEventManager.Raise(BeforeInitObjectsEvent.UseCache());
    }
    
    #region 延迟加载广告逻辑
    private float _delayLoadAllAdTime = 2f;
    /// <summary>
    /// 进入客厅后延迟加载所有广告
    /// </summary>
    private void DelayLoadAllAd() {
        Timer.Register(_delayLoadAllAdTime, false, true, () => {
            NewSDKManager.Inst.TryLoadAllAd(false);
        });
    }
    #endregion
    
    private void OnDestroy() {
        ItemData.data.ClearDeletingForgeItems();
        SimpleEventManager.RemoveListener<ChoseSkinConfirm>(CheckAchieveCarefulForCold);
        if (AssetBundleLoader.Inst == null) {
            return;
        }
        
        var hallSkinIndex = DataUtil.GetHallSkinIndex();
        var hallSkinBundleName = GetSkinBundleName(HeroRoomSkinType.Hall, hallSkinIndex);
        var workShopSkinIndex = DataUtil.GetWorkShopSkinIndex();
        var workShopSkinBundleName = GetSkinBundleName(HeroRoomSkinType.WorkShop, workShopSkinIndex);
        var gardenSkinIndex = DataUtil.GetGardenSkinIndex();
        var gardenSkinBundleName = GetSkinBundleName(HeroRoomSkinType.Garden, gardenSkinIndex);
        var magicAreaSkinIndex = DataUtil.GetMagicAreaSkinIndex();
        var magicAreaSkinBundleName = GetSkinBundleName(HeroRoomSkinType.MagicArea, magicAreaSkinIndex);
        var secondHallSkinIndex = DataUtil.GetSecondHallSkinIndex();
        var secondHallSkinBundleName = GetSkinBundleName(HeroRoomSkinType.SecondHall, secondHallSkinIndex);
        
        AssetBundleLoader.Inst.UnLoadBundle(hallSkinBundleName);
        AssetBundleLoader.Inst.UnLoadBundle(workShopSkinBundleName);
        AssetBundleLoader.Inst.UnLoadBundle(gardenSkinBundleName);
        AssetBundleLoader.Inst.UnLoadBundle(magicAreaSkinBundleName);
        AssetBundleLoader.Inst.UnLoadBundle(secondHallSkinBundleName);
        AssetBundleLoader.Inst.UnLoadBundle(AssetBundleLoader.Cellar);
        AssetBundleLoader.Inst.UnLoadBundle(HeroRoomCommonName);
        AssetBundleLoader.Inst.UnLoadBundle(AssetBundleLoader.SkillDemo);
    }

    private void OnApplicationPause(bool pauseStatus) {
        if (!pauseStatus) {
            ActivityReturnPlayerH5Manager.HandleActivityReturnPlayerH5();
        }
    }
}