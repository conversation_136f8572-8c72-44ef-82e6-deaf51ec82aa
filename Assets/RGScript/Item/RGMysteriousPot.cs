using UnityEngine;
using System.Collections;
using I2.Loc;
using RGScript.Battle;

public class RGMysteriousPot : MonoBehaviour, ItemInterface {
    public emRoleAttribute attribute;
    public float effectValue;
    public float effectDuration;

    public int item_value = 20;
    public string item_name = "Mysterious Pot";
    public AudioClip audio_clip;
    bool can_use = true;

    void OnTriggerEnter2D(Collider2D other) {
        if (other.gameObject.CompareTag("Body_P")) {
            other.GetComponent<RGController>().SetItemtf(transform);
            if (other.GetComponent<RGController>().IsLocalPlayer()) {
                Vector3 tempv3 = transform.position;
                tempv3.y += 1.5f;
                UICanvas.GetInstance().ShowObjectInfo(tempv3, ScriptLocalization.Get(item_name), 0);
            }
        }
    }

    void OnTriggerExit2D(Collider2D other) {
        if (other.gameObject.CompareTag("Body_P")) {
            if (other.GetComponent<RGController>().CompareItem(transform)) {
                other.GetComponent<RGController>().SetItemtf(null);
                if (other.GetComponent<RGController>().IsLocalPlayer()) {
                    if (UICanvas.GetInstance() != null) {
                        UICanvas.GetInstance().HideObjectInfo();
                    }
                }
            }
        }
    }

    public void ItemTrigger(RGController controller) {
        if (controller.IsLocalPlayer()) {
            if (NetControllerManager.Inst.playerCount == 0 || (GameUtil.IsMultiGame() && DataUtil.IsHeroRoom())) {
                RGMusicManager.GetInstance().PlayEffect(audio_clip);
                SimpleEventManager.Raise(new StartEatHpPotEvent() {rgCtrl = controller});
                TakeEffect(controller);
                UICanvas.GetInstance().HideObjectInfo();
                Destroy(gameObject);
            } else {
                MessageManager.Inst.SendItemTriggerMessage(RGGetPath.GetNetId(transform), NetControllerManager.Inst.localNetId);
            }
        }
        // controller.SetItemtf (null);
    }

    public int GetItemValue() {
        return item_value;
    }

    public string GetItemName() {
        return ScriptLocalization.Get(item_name);
    }

    public int GetItemLevel() {
        return 0;
    }

    public bool CanUse() {
        if (can_use) {
            can_use = false;
            return true;
        } else {
            return false;
        }
    }

    public void SyncItemTrigger(RGController controller, string extraInfo) {
        if (controller.IsLocalPlayer()) {
            UICanvas.GetInstance().HideObjectInfo();
        }

        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        TakeEffect(controller);
        Destroy(gameObject);
    }

    void TakeEffect(RGController controller){
        RGScript.Battle.BuffUtil.AddCommonBuff(controller, controller, 
            CommonBuffTypeID.AttributePotion, 
            attribute, 
            effectValue * controller.attribute.potionEffectFactor.GetFinalValue(), 
            effectDuration, GetComponent<SpriteRenderer>().sprite);
    }
}