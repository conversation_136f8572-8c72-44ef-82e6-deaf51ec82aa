/*--------------------------------------------------------------------------------*
  Copyright (C)Nintendo All rights reserved.

  These coded instructions, statements, and computer programs contain proprietary
  information of Nintendo and/or its licensed developers and are protected by
  national and international copyright laws. They may not be disclosed to third
  parties or copied or duplicated in any form, in whole or in part, without the
  prior written consent of Nintendo.

  The content herein is highly confidential and should be handled accordingly.
 *--------------------------------------------------------------------------------*/
#if UNITY_SWITCH && !UNITY_EDITOR
using System.Runtime.InteropServices;
using nn;

public static class VibrationFramework {
    public enum HidVibrationSlot {
        Slot0 = 0,
        Slot1,
        Slot2,
        Slot3,

        CountMax
    }

#if !UNITY_SWITCH || UNITY_EDITOR
    public static void StartThread() { }
    public static void StopThread() { }
    public static void Init(nn.hid.NpadId id, nn.hid.NpadStyle style) { }
    public static void Shutdown(nn.hid.NpadId id) { }
    public static void Play(nn.hid.NpadId id, HidVibrationSlot slot, byte[] address) { }
    public static void Play(nn.hid.NpadId id, HidVibrationSlot slot, byte[] address,
        float gainLow, float gainHigh, float pitchLow, float pitchHigh, float pan) { }
    public static void PlayLoop(nn.hid.NpadId id, HidVibrationSlot slot, byte[] address) { }
    public static void PlayLoop(nn.hid.NpadId id, HidVibrationSlot slot, byte[] address,
        float gainLow, float gainHigh, float pitchLow, float pitchHigh, float pan) { }
    public static void Stop(nn.hid.NpadId id, HidVibrationSlot slot) { }
    public static void Stop(nn.hid.NpadId id) { }
#else
    [DllImport(Nn.DllName,
        CallingConvention = CallingConvention.Cdecl,
        EntryPoint = "nnhidvibrationStartThread")]
    public static extern void StartThread();

    [DllImport(Nn.DllName,
        CallingConvention = CallingConvention.Cdecl,
        EntryPoint = "nnhidvibrationStopThread")]
    public static extern void StopThread();

    [DllImport(Nn.DllName,
        CallingConvention = CallingConvention.Cdecl,
        EntryPoint = "nnhidvibrationInit")]
    public static extern void Init(nn.hid.NpadId id, nn.hid.NpadStyle style);

    [DllImport(Nn.DllName,
        CallingConvention = CallingConvention.Cdecl,
        EntryPoint = "nnhidvibrationShutdown")]
    public static extern void Shutdown(nn.hid.NpadId id);

    [DllImport(Nn.DllName,
        CallingConvention = CallingConvention.Cdecl,
        EntryPoint = "nnhidvibrationPlay")]
    private static extern void Play(nn.hid.NpadId id, HidVibrationSlot slot, byte[] address, long fileSize);

    public static void Play(nn.hid.NpadId id, HidVibrationSlot slot, byte[] address) {
        Play(id, slot, address, address.LongLength);
    }

    [DllImport(Nn.DllName,
        CallingConvention = CallingConvention.Cdecl,
        EntryPoint = "nnhidvibrationPlayOption")]
    private static extern void Play(nn.hid.NpadId id, HidVibrationSlot slot, byte[] address, long fileSize,
        float gainLow, float gainHigh, float pitchLow, float pitchHigh, float pan);

    public static void Play(nn.hid.NpadId id, HidVibrationSlot slot, byte[] address,
        float gainLow, float gainHigh, float pitchLow, float pitchHigh, float pan) {
        Play(id, slot, address, address.LongLength, gainLow, gainHigh, pitchLow, pitchHigh, pan);
    }

    [DllImport(Nn.DllName,
        CallingConvention = CallingConvention.Cdecl,
        EntryPoint = "nnhidvibrationPlayLoop")]
    private static extern void PlayLoop(nn.hid.NpadId id, HidVibrationSlot slot, byte[] address, long fileSize);

    public static void PlayLoop(nn.hid.NpadId id, HidVibrationSlot slot, byte[] address) {
        PlayLoop(id, slot, address, address.LongLength);
    }

    [DllImport(Nn.DllName,
        CallingConvention = CallingConvention.Cdecl,
        EntryPoint = "nnhidvibrationPlayLoopOption")]
    private static extern void PlayLoop(nn.hid.NpadId id, HidVibrationSlot slot, byte[] address, long fileSize,
        float gainLow, float gainHigh, float pitchLow, float pitchHigh, float pan);

    public static void PlayLoop(nn.hid.NpadId id, HidVibrationSlot slot, byte[] address,
        float gainLow, float gainHigh, float pitchLow, float pitchHigh, float pan) {
        PlayLoop(id, slot, address, address.LongLength, gainLow, gainHigh, pitchLow, pitchHigh, pan);
    }

    [DllImport(Nn.DllName,
        CallingConvention = CallingConvention.Cdecl,
        EntryPoint = "nnhidvibrationStop")]
    public static extern void Stop(nn.hid.NpadId id, HidVibrationSlot slot);

    public static void Stop(nn.hid.NpadId id) {
        for (HidVibrationSlot slot = HidVibrationSlot.Slot0; slot < HidVibrationSlot.CountMax; slot++) {
            Stop(id, slot);
        }
    }
#endif
}
#endif