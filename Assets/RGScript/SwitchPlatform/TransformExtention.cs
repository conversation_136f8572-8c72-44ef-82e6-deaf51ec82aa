using System.Collections;
using System.Collections.Generic;
using UnityEngine;

static public class TransformExtention {
    static public void MoveSelfWorld(this Transform transform, Vector3 position) {
        var distance = position - transform.position;
        transform.position += distance;
        for (int i = 0; i < transform.childCount; i++) {
            transform.GetChild(i).transform.position -= distance;
        }
    }

    static public void MoveSelfLocal(this Transform transform, Vector3 localPosition) {
        var distance = localPosition - transform.localPosition;
        transform.localPosition += distance;
        for (int i = 0; i < transform.childCount; i++) {
            transform.GetChild(i).transform.localPosition -= distance;
        }
    }

    static public void MoveSelfWorld(this RectTransform rectTransform, Vector3 position) {
        var distance = position - rectTransform.position;
        rectTransform.position += distance;
        for (int i = 0; i < rectTransform.childCount; i++) {
            rectTransform.GetChild(i).transform.position -= distance;
        }
    }

    static public void MoveSelfLocal(this RectTransform rectTransform, Vector3 localPosition) {
        var distance = localPosition - rectTransform.localPosition;
        rectTransform.localPosition += distance;
        for (int i = 0; i < rectTransform.childCount; i++) {
            rectTransform.GetChild(i).transform.localPosition -= distance;
        }
    }
}