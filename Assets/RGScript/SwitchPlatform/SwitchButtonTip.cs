using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
public class SwitchButtonTip : MonoBehaviour {
    [SerializeField] Image buttonImage;
    Vector2 originalSize;
    RectTransform rectButton;
    RectTransform rectLayout;
    // Start is called before the first frame update
    void Start() {
#if UNITY_SWITCH
        originalSize = transform.parent.GetComponent<RectTransform>().sizeDelta;
        rectButton = transform.parent.GetComponent<RectTransform>();
        rectLayout = transform.GetComponent<RectTransform>();
#endif
    }

    // Update is called once per frame
    void Update() {
#if UNITY_SWITCH
        LayoutRebuilder.MarkLayoutForRebuild(transform as RectTransform);
        int expand = 32;
        if (originalSize.x < rectLayout.sizeDelta.x + expand) {
            rectButton.sizeDelta = new Vector2((int)(rectLayout.sizeDelta.x + expand), rectButton.sizeDelta.y);
        } else {
            rectButton.sizeDelta = originalSize;
        }
#endif
    }

#if UNITY_SWITCH
    public void SetButtonImage(Sprite sprite) {
        buttonImage.sprite = sprite;
    }
#endif

#if UNITY_SWITCH
    public Image GetButtonImage() {
        return buttonImage;
    }
#endif
}