
using System.Collections.Generic;

namespace FixedNum {
    public class EventHub {

        public class Listeners<T> {
            private List<System.Action<T>> _listeners = new();
            public void Add(System.Action<T> listener) { 
                if (!_listeners.Contains(listener)) {
                    _listeners.Add(listener);
                }
            }
            public void Remove(System.Action<T> listener) { 
                _listeners.Remove(listener);
            }
            public void Raise(T obj) {
                for (var i = 0; i < _listeners.Count; ++i) {
                    _listeners[i]?.Invoke(obj);
                }
            }
        }


        Dictionary<System.Type, object> _type2Listeners = new Dictionary<System.Type, object>();
        public void Raise<T>(T obj) {
            try {
                if (!_type2Listeners.TryGetValue(typeof(T), out var listeners)) {
                    _type2Listeners[typeof(T)] = listeners = new Listeners<T>();
                }
                ((Listeners<T>)listeners).Raise(obj);
            }
            catch (System.Exception e) {
                FDebug.LogError(e.ToString());
                FDebug.LogError(e.<PERSON>ackTrace);
            }
        }
        
        public void AddListener<T>(System.Action<T> listener) {
            if (!_type2Listeners.TryGetValue(typeof(T), out var listeners)) {
                _type2Listeners[typeof(T)] = listeners = new Listeners<T>();
            }
            ((Listeners<T>)listeners).Add(listener);
        }

        public void RemoveListener<T>(System.Action<T> listener) {
            if (_type2Listeners.TryGetValue(typeof(T), out var listeners)) {
                ((Listeners<T>)listeners).Remove(listener);
            }
        }
    }
}
