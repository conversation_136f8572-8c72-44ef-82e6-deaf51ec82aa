using System.Collections.Generic;
using FixedNum;
namespace FixedNum.FSM {
    
    public class FiniteStateMachine : IFSMNode {
        public int fsmIndex;
        public System.Action onEnter;
        public System.Action onExit;
        public bool exitWhenNoTransition = false;
        public bool debug;

        public int CurrentState { get; private set; } = -1;

        int _startStateIndex = -1;
        int _nextStateIndex = -1;

        struct TransitionData{
            public int stateIndex;
            public FSMTransition transition;
        }

        readonly List<IFSMNode> _states = new List<IFSMNode>();
        readonly List<List<TransitionData>> _transitions = new List<List<TransitionData>>();

        public int debugSubStateIndex = -1;
        public System.Action<FiniteStateMachine, int, int, int> debugStateTransition;

        public void OnEnter(){
            _nextStateIndex = _startStateIndex;
            onEnter?.Invoke();
        }

        public bool OnUpdate(FixedNumber deltaTime){
            if(_nextStateIndex >= 0 && _nextStateIndex < _states.Count){
                if(CurrentState >= 0 && CurrentState < _states.Count){
                    _states[CurrentState].OnExit();
                }
                CurrentState = _nextStateIndex;
                _nextStateIndex = -1;
                if (_states[CurrentState] is FiniteStateMachine subFsm) {
                    subFsm.debugSubStateIndex = CurrentState;
                    subFsm.debugStateTransition = debugStateTransition;
                }
                _states[CurrentState].OnEnter();
            }
            if(CurrentState >= 0 && CurrentState < _states.Count){
                var exit = !_states[CurrentState].OnUpdate(deltaTime);
                // Check transitions
                var transitions = _transitions[CurrentState];
                if(transitions != null){
                    for (int i = 0; i < transitions.Count; i++) {
                        var transition = transitions[i];
                        if (transition.transition.IsTriggered()) {
                            _nextStateIndex = transition.stateIndex;
                            debugStateTransition?.Invoke(this, CurrentState, i, _nextStateIndex);
                            break;
                        }
                    }

                    if(exit && _nextStateIndex < 0){
                        // no transition found, check for default transition when state exit
                        for (var i = 0; i < transitions.Count; ++i){
                            var transition = transitions[i];
                            if(transition.transition == FSMTransition.Default){
                                _nextStateIndex = transition.stateIndex;
                                debugStateTransition?.Invoke(this, CurrentState, i, _nextStateIndex);
                                break;
                            }
                        }
                    }
                }

                // state calls for an exit, but no valid transition found, then loop the state
                if(exit && _nextStateIndex < 0) {
                    if (!exitWhenNoTransition) {
                        _nextStateIndex = CurrentState;
                        debugStateTransition?.Invoke(this, CurrentState, -1, _nextStateIndex);
                    }
                    else{
                        return false;
                    }
                }
                
            }
            return true;
        }

        public void OnExit(){ 
            onExit?.Invoke();
        }

        public IFSMNode AddState(IFSMNode state){
            var existIdx = _states.IndexOf(state);
            if(existIdx >= 0){
                return _states[existIdx];
            }
            _states.Add(state);
            _transitions.Add(null);
            return state;
        }

        public IFSMNode GetCurrentState(){
            if (CurrentState >= 0) {
                return _states[CurrentState];
            }
            return null;
        }

        public IFSMNode GetState(int idx){
            if(idx >= 0 && idx < _states.Count){
                return _states[idx];
            }
            return null;
        }

        public FSMTransition GetTransitionOfCurrentState(int index){
            return _transitions[CurrentState][index].transition;
        }

        public void SetAsStartState(IFSMNode state){
            _startStateIndex = _states.IndexOf(state);
        }

        public void AddTransition(IFSMNode stateA, FSMTransition transition, IFSMNode stateB){
            var indexA = _states.IndexOf(stateA);
            var indexB = _states.IndexOf(stateB);
            if(indexA < 0 || indexB < 0){
                return;
            }
            if(_transitions[indexA] == null){
                _transitions[indexA] = new List<TransitionData>();
            }
            var existIdx = _transitions[indexA].FindIndex(trans => trans.stateIndex == indexB);

            if (existIdx < 0) {
                _transitions[indexA].Add(new TransitionData {
                    stateIndex = indexB,
                    transition = transition
                });
            }
            else{
                _transitions[indexA][existIdx] = new TransitionData {
                    stateIndex = indexB,
                    transition = transition
                };
            }
        }
    }
}
