using ModeSeason.ComboGun;
using System.Collections.Generic;
using TeamInfo.TeamModel;

namespace RGScript.Team.TeamInfo {
    public class UIPanelTeamInfoComboGun : UIPanelTeamInfo {
        private readonly List<TeammateComboGun> _teammateComboGuns = new();

        protected void OnEnable() {
            SimpleEventManager.AddEventListener<AmmoChangedEvent>(OnAmmoChange);
        }

        protected void OnDisable() {
            SimpleEventManager.RemoveListener<AmmoChangedEvent>(OnAmmoChange);
        }

        private void SetTeammateAmmoNum(int playerIndex, int ammoCount) {
            var teammateComboGun = GetTeammateComboGun(playerIndex - 1);
            if (!teammateComboGun) {
                return;
            }
            
            teammateComboGun.SetAmmoNum(ammoCount);
        }

        private void OnAmmoChange(AmmoChangedEvent e) {
            if (e.player) {
                SetTeammateAmmoNum(e.player.p_index, e.count);   
            }
        }

        private void InitAmmoNum() {
            var otherControllers = NetControllerManager.Inst.GetOtherControllers();
            foreach (var netPlayerController in otherControllers) {
                if (!netPlayerController) {
                    continue;
                }
                
                if (!netPlayerController.controller) {
                    continue;
                }

                var hand = netPlayerController.controller.hand;
                if (!hand) {
                    continue;
                }

                var frontWeapon = hand.front_weapon;
                if (!frontWeapon || frontWeapon is not CGWeapon cgWeapon) {
                    continue;
                }

                SetTeammateAmmoNum(netPlayerController.controller.p_index, cgWeapon.GetAmmoCount());
            }
        }

        protected override void InitInfoPanel() {
            // Empty, do nothing
            // Forbidden this function
        }

        private void InitTeammateComboGunList() {
            _teammateComboGuns.Clear();
            
            const int teammateMaxNum = 4;
            for (var i = 1; i <= teammateMaxNum; ++i) {
                var teammateNode = transform.Find($"teammate{i}");
                var teammateComboGun = teammateNode.GetComponent<TeammateComboGun>();
                teammateComboGun.Init();
                _teammateComboGuns.Add(teammateComboGun);
            }

            InitAmmoNum();
        }

        private TeammateComboGun GetTeammateComboGun(int index) {
            return index < _teammateComboGuns.Count ? _teammateComboGuns[index] : null;
        }
        
        public override void UpdateTeamItemView(TeamItem teamItem) {
            if (_teammateComboGuns.Count == 0) {
                InitTeammateComboGunList();
            }

            var teammateComboGun = GetTeammateComboGun(teamItem.teamIdx);
            if (!teammateComboGun) {
                return;
            }
            
            if (teamItem.netId == NetControllerManager.Inst.localNetId ||
                (!DataUtil.IsMultiRoom() && ( teamItem.playerIdx < 0 || teamItem.skinIdx < 0))) {
                teammateComboGun.gameObject.SetActive(false);
                return;
            }

            teammateComboGun.SetTeamIndexSprite(teamItem);
            teammateComboGun.SetHeroImage(teamItem);
            teammateComboGun.SetIsOnline(teamItem, backgroundState);
            teammateComboGun.SetRank(teamItem);
            teammateComboGun.SetHonoraryTitleList(teamItem);
        }
        
        public override void UpdateTeamItemHp(TeamItem teamItem) {
            var teammateComboGun = GetTeammateComboGun(teamItem.teamIdx);
            if (!teammateComboGun) {
                return;
            }
            
            teammateComboGun.SetHpPercent(teamItem.hpPercent);
        }

        public override void UpdateTeamItemArmor(TeamItem teamItem) {
            var teammateComboGun = GetTeammateComboGun(teamItem.teamIdx);
            if (teammateComboGun == null) {
                return;
            }
            
            teammateComboGun.SetArmorPercent(teamItem.armorPercent);
        }
    }
}