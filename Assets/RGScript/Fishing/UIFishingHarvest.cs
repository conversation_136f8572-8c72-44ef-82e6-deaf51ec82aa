using DG.Tweening;
using RGScript.Config;
using Sirenix.OdinInspector;
using SoulKnight.Runtime.Item;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityCommon.UI;
using UnityEngine;
using UnityEngine.UI;
using Util.TAUtil;

/// <summary>
/// 钓鱼收获显示界面
/// </summary>
public class UIFishingHarvest : BaseUIView {
    public Text label_YOU_GOT;
    public GameObject panel1;
    public GameObject panel2;
    public GameObject panelFeed;
    public Image itemSprite;
    public Image blueprintIconSprite;
    private GameObject _panel;
    private System.Action<bool> onClose;
    /// <summary>
    /// trash_yellow_duck
    /// </summary>
    public AudioClip audioClip;
    private GameObject gotObj;

    public void SetHarvest(string fishRodBluePrintName, GameObject obj, bool succeed) {
        if (succeed) {
            if (!string.IsNullOrWhiteSpace(fishRodBluePrintName) && fishRodBluePrintName.StartsWith(RGGameConst.BlueprintFishRodPrefix)) {
                var printName = fishRodBluePrintName;
                var itemConfig = ItemConfigLoader.GetItemConfig(printName);
                string pickableName = ItemPickable.GetBluePrintName(printName); 
                _panel = panel1;
                itemSprite.sprite = itemConfig.Background.GetSprite();
                itemSprite.SetNativeSize();
                blueprintIconSprite.sprite = itemConfig.Icon.GetSprite();
                blueprintIconSprite.SetNativeSize();
                blueprintIconSprite.gameObject.SetActive(true);
                label_YOU_GOT.text = string.Format(I2.Loc.ScriptLocalization.Get("fishing_tips2", "你钓到了...{0}!"), pickableName);
                label_YOU_GOT.GetComponent<RectTransform>().anchoredPosition = new Vector2(0, -83);
                panel1.transform.Find("desc").GetComponentInChildren<Text>().text = pickableName;
                ItemData.data.GetBlueprint(fishRodBluePrintName);
            } else if (obj) {
                gotObj = obj;
                if (obj.GetComponent<SpawnEnemyToken>() is SpawnEnemyToken spawnEnemyToken && spawnEnemyToken) {
                    var text = I2.Loc.ScriptLocalization.Get(obj.transform.GetChild(0).name);
                    _panel = panel2;
                    panel2.transform.Find("desc").GetComponentInChildren<Text>().text = text;
                } else if (obj.GetComponent<SpawnMountToken>() is SpawnMountToken spawnMountToken && spawnMountToken) {
                    var text = I2.Loc.ScriptLocalization.Get(obj.transform.GetChild(0).name);
                    _panel = panel1;
                    itemSprite.sprite = obj.GetComponentInChildren<SpriteRenderer>()?.sprite;
                    itemSprite.SetNativeSize();
                    label_YOU_GOT.text = "";
                    panel1.transform.Find("desc").GetComponentInChildren<Text>().text = text;
                } else {
                    var itemName = "";
                    var item = obj.GetComponent<ItemInterface>();

                    if (obj.GetComponent<PetUnlockToken>() is PetUnlockToken petUnlockToken && petUnlockToken) {
                        itemName = I2.Loc.ScriptLocalization.Get("Pet_name_" + petUnlockToken.petIndex);
                    } else {
                        itemName = I2.Loc.ScriptLocalization.Get(obj.name);
                        if (string.IsNullOrWhiteSpace(itemName))
                            itemName = item.GetItemName();
                    }

                    var isTrash = obj.GetComponent<ItemTrash>() != null;

                    if (string.IsNullOrWhiteSpace(itemName) && isTrash)
                        itemName = I2.Loc.ScriptLocalization.Get("trash_common", "腐烂的残骸");

                    if (item != null) {
                        if (item is WeaponItemBase weaponItemBase) {
                            // 武器配件 添加适配描述
                            var fitType = weaponItemBase.GetSupportWeaponType();
                            if (fitType != null) {
                                itemName += fitType;
                            }
                        }
                        
                        try {
                            var color = item.GetItemLevel().ToItemLevel().ToColor();
                            itemName = string.Format("<color=#{0}>{1}</color>", NumericUtil.ColorToHexString(color),
                                itemName);
                        } catch { }
                    }

                    itemSprite.sprite = obj.GetComponentInChildren<SpriteRenderer>()?.sprite;
                    itemSprite.SetNativeSize();
                    label_YOU_GOT.text = string.Format(I2.Loc.ScriptLocalization.Get("fishing_tips2", "你钓到了...{0}!"), itemName);

                    var descText = I2.Loc.ScriptLocalization.Get("desc_" + obj.name);
                    if (string.IsNullOrWhiteSpace(descText)) {
                        if (isTrash)
                            descText = I2.Loc.ScriptLocalization.Get("desc_trash_common", "很难分辨出这是什么东西");
                        else {
                            descText = label_YOU_GOT.text;
                            label_YOU_GOT.text = "";
                        }
                    }

                    panel1.transform.Find("desc").GetComponentInChildren<Text>().text = descText;

                    _panel = panel1;

                    // 检测是否可喂食给宠物
                    if (GameUtil.CanPartnerDoFeed(obj.name)) {
                        panelFeed.SetActive(true);
                        panelFeed.transform.Find("feed_cancel").GetComponentInChildren<Text>().text = I2.Loc.ScriptLocalization.Get("pet_feed_cancel", "留下自用");
                        panelFeed.transform.Find("feed_confirm").GetComponentInChildren<Text>().text = I2.Loc.ScriptLocalization.Get("pet_feed_confirm", "喂给宠物");
                    }
                }

                if (obj.name == "trash_yellow_duck" && audioClip != null) {
                    RGMusicManager.Inst.PlayEffect(audioClip);
                }
            } else {
                _panel = panel2;
                panel2.transform.Find("desc").GetComponentInChildren<Text>().text = I2.Loc.ScriptLocalization.Get("fishing_tips3", "什么也没钓到!");
            }
        } else {
            _panel = panel2;
            panel2.transform.Find("desc").GetComponentInChildren<Text>().text = I2.Loc.ScriptLocalization.Get("fishing_tips5", "鱼逃走了!");
        }
    }

    private bool _canClose = true;

    public void SetOnClose(System.Action<bool> onClose) {
        this.onClose = onClose;
    }
    
    void Start() {
        _panel.SetActive(true);
        _canClose = false;
        _panel.transform.localScale = Vector3.zero;
        _panel.transform.DOScale(1, 0.25f).SetEase(Ease.InOutBack);
        Timer.Register(1, false, false, () => {
            _canClose = true;
        });
    }

    public void OnClickAir(bool hasFeed = false) {
        if (_canClose) {
            onClose?.Invoke(hasFeed);
            RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Button);
        }
    }

    public void OnClickFeedCancel() {
        _canClose = true;
        OnClickAir(false);
    }

    /// <summary>
    /// 给宠物喂食
    /// </summary>
    public void OnClickFeedConfirm() {
        _canClose = true;
        if (!gotObj || !PetIntimacyConfig.Data.ContainsKey(gotObj.name)) {
            OnClickAir(false);
            return;
        }

        var grainName = gotObj.name;
        var data = PetIntimacyConfig.Data[grainName];
        var partner = RGGameSceneManager.Inst.partner;
        ConsumeStatistics.FeedPetType feedType = gotObj.GetComponent<RGWeapon>() != null ? ConsumeStatistics.FeedPetType.FeedWeapon : ConsumeStatistics.FeedPetType.MatGrain;
        if (partner.GetComponent<RGPetWithSkillController>() is { } rgPetWithSkillController) {
            rgPetWithSkillController.DoFeed(data, feedType, grainName);
            OnClickAir(true);
        }
    }
}