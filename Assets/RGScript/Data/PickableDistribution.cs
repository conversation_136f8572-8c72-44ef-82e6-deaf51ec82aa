using SoulKnight.Runtime.RandomObject;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Random = UnityEngine.Random;
using StringIntPair = System.Collections.Generic.KeyValuePair<string, int>;

public struct ComodityInfo {
    public string item;
    public int minCount;
    public int maxCount;
    public int weight;
}

public class PickableDistribution {
    public List<ComodityInfo> seedDistribute = new();
    public List<ComodityInfo> materialDistribute = new();
    public Dictionary<string, int> blueprintDistribute = new();

    private static PickableDistribution[] _datas;
    public static PickableDistribution[] datas {
        get {
            if (_datas != null) {
                return _datas;
            }
            
            _datas = new PickableDistribution[3];
            for (var i = 0; i < _datas.Length; ++i) {
                _datas[i] = LoadFromRandomObjectConfig(i + 1);
            }
            return _datas;
        }
    }

    private static PickableDistribution LoadFromRandomObjectConfig(int index) {
        var ret = new PickableDistribution();

        var seedConfig = RandomObjectConfigLoader.GetRandomObjectConfig($"seed_{index}");
        if (seedConfig != null) {
            foreach (var itemDistributionData in seedConfig.Items) {
                ret.seedDistribute.Add(new ComodityInfo {
                    item = itemDistributionData.item,
                    maxCount = itemDistributionData.maxNum,
                    minCount = itemDistributionData.minNum,
                    weight = itemDistributionData.weight
                });
            }
        }
        
        var materialConfig = RandomObjectConfigLoader.GetRandomObjectConfig($"material_{index}");
        if (materialConfig != null) {
            foreach (var itemDistributionData in materialConfig.Items) {
                ret.materialDistribute.Add(new ComodityInfo {
                    item = itemDistributionData.item,
                    maxCount = itemDistributionData.maxNum,
                    minCount = itemDistributionData.minNum,
                    weight = itemDistributionData.weight
                });
            }
        }
        
        var blueprintConfig = RandomObjectConfigLoader.GetRandomObjectConfig($"blueprint_{index}");
        // ReSharper disable once InvertIf
        if (blueprintConfig != null) {
            foreach (var itemDistributionData in blueprintConfig.Items) {
                ret.blueprintDistribute.Add(itemDistributionData.item, itemDistributionData.weight);
            }
        }
        
        return ret;
    }

    public static string GetBlueBoxExtraMustDrop() {
        return "material_ancient_weapon_fragment";
    }

    public static StringIntPair? GetRandomObject(emItemType type, RGRandom rgRandom, int level) {
        return type switch {
            emItemType.Seed => GetRandomSeed(rgRandom, level),
            emItemType.BluePrint => GetRandomBlueprint(rgRandom, level),
            emItemType.Material => GetRandomMaterial(rgRandom, level),
            _ => null
        };
    }
    
    public static StringIntPair? GetRandomObject(RGRandom rgRandom, int weight0, int weight1, int weight2) {
        int total = weight0 + weight1 + weight2;
        int ran = rgRandom?.Range(0, total) ?? Random.Range(0, total);
        return ran < weight0 ?
            GetRandomObject(rgRandom, 0) :
            GetRandomObject(rgRandom, ran < weight0 + weight1 ? 1 : 2);
    }
    
    public static StringIntPair? GetRandomObject(RGRandom rgRandom, int level) {
        int ran = rgRandom?.Range(0, 100) ?? Random.Range(0, 100);
        return ran switch {
            < 35 => GetRandomSeed(rgRandom, level),
            < 70 => GetRandomMaterial(rgRandom, level),
            _ => GetRandomBlueprint(rgRandom, level)
        };
    }
    
    public static StringIntPair? GetRandomSeed(RGRandom rgRandom, int level) {
        return GetRandomCommodity(datas[level].seedDistribute, rgRandom);
    }
    
    public static StringIntPair? GetRandomMaterial(RGRandom rgRandom, int level) {
        return GetRandomCommodity(datas[level].materialDistribute, rgRandom);
    }
    
    public static StringIntPair? GetRandomBlueprint(RGRandom rgRandom, int level, bool isNullable = false) {
        Dictionary<string, int> newBlueprintDistribute = new();
        int total = 0;
        // ReSharper disable once ForeachCanBePartlyConvertedToQueryUsingAnotherGetEnumerator
        foreach (var pair in datas[level].blueprintDistribute) {
            if (ItemData.data.blueprints.ContainsKey(pair.Key) &&
                ItemData.data.blueprints[pair.Key] != emBluePrintStatus.None) {
                continue;
            }

            newBlueprintDistribute[pair.Key] = pair.Value;
            total += pair.Value;
        }
        
        string result = string.Empty;
        if (newBlueprintDistribute.Count > 0) {
            int ran = rgRandom?.Range(0, total) ?? Random.Range(0, total);
            foreach (var pair in newBlueprintDistribute) {
                ran -= pair.Value;
                if (ran >= 0) {
                    continue;
                }

                result = pair.Key;
                break;
            }
        }
        
        if (!string.IsNullOrEmpty(result)) {
            return new StringIntPair(result, 1);
        }

        if (isNullable) {
            return null;
        }

        // ReSharper disable once ConvertIfStatementToReturnStatement
        if ((rgRandom?.Range(0, 2) ?? Random.Range(0, 2)) < 1) {
            return GetRandomSeed(rgRandom, level);
        }

        return GetRandomMaterial(rgRandom, level);
    }
    
    static StringIntPair? GetRandomCommodity(List<ComodityInfo> commodities, RGRandom rgRandom) {
        int total = commodities.Sum(item => item.weight);
        int ran = rgRandom?.Range(0, total) ?? Random.Range(0, total);
        foreach (var info in commodities) {
            ran -= info.weight;
            if (ran >= 0) {
                continue;
            }

            int count;
            if (rgRandom == null) {
                count =
                    Mathf.Min(Random.Range(info.minCount, info.maxCount + 1),
                        Random.Range(info.minCount, info.maxCount + 1));
            } else {
                count =
                    Mathf.Min(rgRandom.Range(info.minCount, info.maxCount + 1),
                        rgRandom.Range(info.minCount, info.maxCount + 1));
            }
            return new StringIntPair(info.item, count);
        }
        return null;
    }
}