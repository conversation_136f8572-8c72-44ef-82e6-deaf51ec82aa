using Activities;
using Activities.GiveBack.Scripts;
using Activities.RebornEgg.Scripts;
using GameStatisticSystem;
using RGScript.CheckIn;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using RGScript.Data;
using RGScript.Item;
using RGScript.Manager.SDK;
using RGScript.Other.NewSDK;
using RGScript.Util.LifeCycle;
using System;
using UnityEngine;


// ReSharper disable Unity.RedundantSerializeFieldAttribute

/// <summary>
/// 通关类型
/// </summary>
public enum emPassGameLevel {
    Not,
    Normal,
    Badass,
    Bossrush, //bossrush( may be not used)
    BossrushBadass, //bossrush dzt( may be not used)
}

public struct WeaponStatisticInfo {
    public emPassGameLevel passLevel;
}

[System.Serializable]
public class AchieveInfo {
    public int id;
    public bool unlock;
    public bool got_award;
    public bool has_show; //解锁后是否被看过
}

[System.Serializable]
public class RebornEggFollower {
    public string id;
    public string name;
    public string enemyId;
    public ActivityRebornEggData.emEnemyType eType;
    public int level;
    public int tmpUpgradelevel; // 局内临时升级的等级，带出地牢要回退
    public bool isNew; // 局内新获得的随从
    public List<string> buffs = new List<string>(); // 羁绊

    public RebornEggFollower CopySelf() {
        return new RebornEggFollower() {
            id = id,
            name = name,
            enemyId = enemyId,
            eType = eType,
            level = level,
            tmpUpgradelevel = tmpUpgradelevel,
            isNew = isNew,
            buffs = buffs.Select(x => x).ToList()
        };
    }
}

[System.Serializable]
public class StatisticData : IData {
    /// <summary>
    /// 全新统计系统，请使用GameStatisticManager.GetStatisticData调用
    /// </summary>
    [SerializeField]
    public Dictionary<string, GameStatisticData> StatisticsData;
    
    [SerializeField]
    private Dictionary<string, emPassGameLevel> object2PassLevel = new Dictionary<string, emPassGameLevel>();
    [SerializeField]
    private Dictionary<string, int> object2PassTime = new Dictionary<string, int>();
    
    #region UIOpenTimes
    [SerializeField]
    private Dictionary<string, int> _uiOpenTimes = new Dictionary<string, int>();

    public void AddUIOpenTimes(string uiName, int count = 1) {
        if (_uiOpenTimes.ContainsKey(uiName)) {
            _uiOpenTimes[uiName] += count;
        } else {
            _uiOpenTimes.Add(uiName, count);
        }
    }

    public int GetUIOpenTimes(string uiName) {
        return _uiOpenTimes.ContainsKey(uiName) ? _uiOpenTimes[uiName] : 0;
    }
    #endregion

    #region EnemyKillCount
    
    [SerializeField]
    private Dictionary<string, int> _enemyKilledCount = new Dictionary<string, int>();

    public void AddEnemyKilledCount(emGameMode gameMode, string enemyID, int count = 1) {
        if (_enemyKilledCount.ContainsKey($"{gameMode}_{enemyID}")) {
            _enemyKilledCount[$"{gameMode}_{enemyID}"] += count;
        } else {
            _enemyKilledCount.Add($"{gameMode}_{enemyID}",count);
        }
    }
    
    public int GetEnemyKilledCount(emGameMode gameMode, string enemyID) {
        return _enemyKilledCount.ContainsKey($"{gameMode}_{enemyID}") ? _enemyKilledCount[$"{gameMode}_{enemyID}"] : 0;
    }

    #endregion
    
    
    #region useEmoji
    [SerializeField]
    private Dictionary<int, int> _usedEmoji = new Dictionary<int, int>();
    
    public void AddEmojiUsedTimes(int emojiId) {
        if (_usedEmoji.ContainsKey(emojiId)) {
            _usedEmoji[emojiId] ++;
        } else {
            _usedEmoji.Add(emojiId,1);
        }
        
    }
    
    public bool CheckIfUsedAllEmoji() {
        for (int i = 1; i <= EmoticonInfo.info.GetEmoticonDatas().Count; i++) {
            if (!_usedEmoji.ContainsKey(i)) {
                return false;
            }
        }
        return true;
    }   

    #endregion
    
    
    /// <summary>
    /// 击杀数量
    /// </summary>
    /// <typeparam name="string"></typeparam>
    /// <typeparam name="int"></typeparam>
    /// <returns></returns>
    [SerializeField]
    private Dictionary<string, int> object2ObtainTime = new Dictionary<string, int>();
    
    /// <summary>
    /// 火力全开已通关难度次数
    /// </summary>
    [SerializeField]
    public Dictionary<int, int> firePassDifficultyLevel = new Dictionary<int, int>();
    
    public int fireExtraReplaceHeroSeatCount; // 火力全开额外增加选人位的个数

    /// <summary>
    /// 元气传奇活动局外领取的buff奖励
    /// </summary>
    [SerializeField] public List<string> legendClaimOutBuffs = new();
    
    public static (long,int) CalculateEnemyAndBossTotal() {
        long enemyTotal = 0;
        int bossTotal = 0;
        
        var list = DataMgr.HandbookEnemyData.allEnemyList;
        foreach (var e in list) {
            var count = data.GetObtainTime(e.EnemyId);
            if (e.Rank == 3) {
                bossTotal += count;
            } else {
                enemyTotal += count;
            }
        }

        return (enemyTotal, bossTotal);
    }
    
    /// <summary>
    /// 事件
    /// </summary>
    /// <typeparam name="string"></typeparam>
    /// <typeparam name="int"></typeparam>
    /// <returns></returns>
    [SerializeField]
    private Dictionary<string, int> event2Count = new Dictionary<string, int>();//事件发生次数
    [SerializeField]
    private List<string> recordedEvents = new List<string>();//记录的事件

    [SerializeField]
    private Dictionary<string, string> passInfo = new Dictionary<string, string>(); //通关信息(用于通关证书)

    [SerializeField]
    private Dictionary<Season, SeasonPrize> season2Prizes = new Dictionary<Season, SeasonPrize>();

    //成就新添
    [SerializeField]
    public int c02_skill_times; //游侠技能次数
    [SerializeField]
    public int explode_kill_enemy; //炸弹杀死怪物数量
    [SerializeField]
    public int one_pounch_kill_boss_count; //一拳击杀Boss数量

    [SerializeField]
    public int hand_cut_index; //手刀类型 普通手刀还是金手刀[SerializeField]

    [SerializeField]
    public bool manual_change_hand_cut_index;
    
    [SerializeField]
    public int buy_good_times; // 商城购买次数

    [SerializeField]
    public Dictionary<int, AchieveInfo> achieve_record = new Dictionary<int, AchieveInfo>();

    [SerializeField]
    public List<BaseEmail> alreadyGetEmails = new List<BaseEmail>();
    [System.Obsolete("废弃的字段，仅可用于数据兼容逻辑，不可用于逻辑判断。如果需要用于逻辑判断，请用alreadyGetRemoteEmailUniqueIds替代。")]
    [SerializeField]
    public List<int> alreadyGetRemoteEmailUids = new List<int>();
    [SerializeField]
    public List<string> alreadyGetRemoteEmailUniqueIds = new List<string>();
    /// <summary>
    /// 远端邮件在存档里的数据（因为不想将RemoteEmail完整的序列化，会有很多浪费的无用数据结构）
    /// </summary>
    [SerializeField]
    public Dictionary<Guid, RemoteEmailRecord> remoteEmailRecords = new Dictionary<Guid, RemoteEmailRecord>();
    [SerializeField]
    public Dictionary<BattleDataList.emSavePlace, long> mode2SaveGameIdDic = new Dictionary<BattleDataList.emSavePlace, long>();//记录当前存档的BattleData.saveGameId，给云存档扣除材料使用
    [SerializeField]
    public Dictionary<BattleDataList.emSavePlace, long> statementMode2SaveGameIdDic = new Dictionary<BattleDataList.emSavePlace, long>();//记录上次结算存档的BattleData.saveGameId，防止重复结算
    [SerializeField]
    public Dictionary<BattleDataList.emSavePlace, long> battleDataSavePlac2SaveGameIdDic = new Dictionary<BattleDataList.emSavePlace, long>();//当前所有存档槽位对应的BattleData.saveGameId，给结算可继续游戏的BattleData做对比
    [SerializeField]
    public List<PassInfo> _pass_history_list = new List<PassInfo>();
    public List<PassInfo> pass_history_list {
        get {
            if (null == _pass_history_list) {
                _pass_history_list = new List<PassInfo>();
            }
            return _pass_history_list;
        }
    }

    public const int PassHistoryMaxCount = 4;
    
    [SerializeField]
    public List<MultiGameHistoryData> _multiGameHistoryList = new List<MultiGameHistoryData>();
    public List<MultiGameHistoryData> MultiGameHistoryList {
        get {
            if (null == _multiGameHistoryList) {
                _multiGameHistoryList = new List<MultiGameHistoryData>();
            }
            return _multiGameHistoryList;
        }
    }
    public const int MultiGameHistoryMaxCount = 4;

    public int[] equippedHonoraryTitles = new int[3] {-1, -1, -1};
    
    [SerializeField]
    public Dictionary<int, HonoraryTitleProgress> HonoraryTitleSnapshot = new Dictionary<int, HonoraryTitleProgress>();

    #region 新手签到

    // 新手签到开启状态
    [SerializeField] 
    public CheckInOpenStatus open_checkin = CheckInOpenStatus.None;
    
    // 新手签到7天奖励领取状态
    [SerializeField] public List<string> NewbieCheckInStatusKeys = new List<string>();
    [SerializeField] public List<CheckStatus> NewbieCheckInStatusValues = new List<CheckStatus>();
    // 可能被替换的奖励(提前解锁的皮肤，技能)
    [SerializeField] public Dictionary<string, CheckInReward> ReplaceRewards = new Dictionary<string, CheckInReward>();
    
    // 学院活动任务奖励领取记录
    [SerializeField] public List<string> ActivitySchoolTakeTaskKeys = new List<string>();
    
    // 2025学院活动复刻任务奖励领取记录
    [SerializeField] public List<string> Activity202508SchoolTakeTaskKeys = new List<string>();
    #endregion
    
    #region 回归签到

    // 签到开启状态
    [SerializeField] 
    public CheckInOpenStatus open_veteran_checkin = CheckInOpenStatus.None;
    
    // 签到7天奖励领取状态
    [SerializeField] public List<string> VeteranCheckInStatusKeys = new List<string>();
    [SerializeField] public List<CheckStatus> VeteranCheckInStatusValues = new List<CheckStatus>();
    // 可能被替换的奖励(提前解锁的皮肤，技能)
    [SerializeField] public Dictionary<string, CheckInReward> VeteranReplaceRewards = new Dictionary<string, CheckInReward>();
    
    #endregion
    
    #region 暑期签到

    // 首次打开的时间戳
    [SerializeField] public string ActivityCheckInOpenTimestamp;
    // 签到7天奖励领取状态
    [SerializeField] public List<string> ActivityCheckInStatusKeys = new List<string>();
    [SerializeField] public List<CheckStatus> ActivityCheckInStatusValues = new List<CheckStatus>();
    // 可能被替换的奖励(提前解锁的皮肤，技能)
    [SerializeField] public Dictionary<string, CheckInReward> ActivityCheckInReplaceRewards = new Dictionary<string, CheckInReward>();
    // 小鱼干周年庆签到开启状态
    [SerializeField] 
    public CheckInOpenStatus open_activity_checkin = CheckInOpenStatus.None;
    
    #endregion
    
    #region 2025小鱼干周年庆签到 7月版本

    // 首次打开的时间戳
    [SerializeField] public string ActivityCheckIn2OpenTimestamp;
    // 签到7天奖励领取状态
    [SerializeField] public List<string> ActivityCheckIn2StatusKeys = new List<string>();
    [SerializeField] public List<CheckStatus> ActivityCheckIn2StatusValues = new List<CheckStatus>();
    // 可能被替换的奖励(提前解锁的皮肤，技能)
    [SerializeField] public Dictionary<string, CheckInReward> ActivityCheckIn2ReplaceRewards = new Dictionary<string, CheckInReward>();
    // 小鱼干周年庆签到开启状态
    [SerializeField] 
    public CheckInOpenStatus open_activity_checkin2 = CheckInOpenStatus.None;
    
    #endregion
    
    // 万圣节活动奖励兑换记录: key:count,count表示已兑换次数
    [SerializeField] 
    public Dictionary<string, int> HalloweenRedeemRecords = new Dictionary<string, int>();
    
    // 每日圣诞节活动击打圣诞树出现boss，武器奖励次数记录
    [SerializeField]
    public List<string> ChristmasRecords = new List<string>();

    public List<int> honoraryTitleNeedToShowRedDot = new List<int>();
    
    // 打铁节活动奖励兑换记录: key:count,count表示已兑换次数
    [SerializeField] 
    public Dictionary<string, int> FusionRedeemRecords = new Dictionary<string, int>();
    
    // 粽子大作战活动奖励兑换记录: key:count,count表示已兑换次数
    [SerializeField] 
    public Dictionary<string, int> ZongziRedeemRecords = new Dictionary<string, int>();
    
    // 特性活动奖励兑换记录: key:count,count表示已兑换次数
    [SerializeField] 
    public Dictionary<string, int> BugFeatureRedeemRecords = new Dictionary<string, int>();
    
    // 元气学院活动奖励兑换记录: key:count,count表示已兑换次数
    [SerializeField] 
    public Dictionary<int, int> SchoolRedeemRecords = new Dictionary<int, int>();    
    
    [SerializeField] 
    // 元气学院202508复刻奖励兑换记录: key:count,count表示已兑换次数
    public Dictionary<int, int> SchoolRedeemRecords202508 = new Dictionary<int, int>();    
    
    // 圣诞节2024
    [SerializeField] 
    public Dictionary<int, int> Christmas2024RedeemRecords = new Dictionary<int, int>();
    
    // 火焰狂欢活动奖励兑换记录: key:count,count表示已兑换次数
    [SerializeField] 
    public Dictionary<string, int> NewHalloweenRedeemRecords = new Dictionary<string, int>();
    
    // 火力全开活动奖励兑换记录: key:count,count表示已兑换次数
    [SerializeField] 
    public Dictionary<int, int> FireRedeemRecords = new Dictionary<int, int>();    
    
    // 记录下各个活动兑换得到的蓝图，方便蓝图研发的时候溯源
    [SerializeField] 
    public List<string> ActivityRedeemBlueprints = new List<string>();

    // 无尽梦回活动奖励兑换记录: key:count,count表示已兑换次数
    [SerializeField] 
    public Dictionary<string, int> DreamRedeemRecords = new Dictionary<string, int>();
    #region 感恩回馈

    [SerializeField]
    public bool pop_review = false; // 领取奖励后，是否已弹过评分弹窗

    [SerializeField] public List<string> GiveBackStatusKeys = new List<string>();
    // 奖励领取状态
    [SerializeField] public List<RewardStatus> GiveBackClaimStatus = new List<RewardStatus>();
    
    #endregion
    
    #region 2025春节签到

    // 首次打开的时间戳
    [SerializeField] public string ActivityNewYear2025CheckInOpenTimestamp;
    // 签到7天奖励领取状态
    [SerializeField] public List<string> ActivityNewYear2025CheckInStatusKeys = new List<string>();
    [SerializeField] public List<CheckStatus> ActivityNewYear2025CheckInStatusValues = new List<CheckStatus>();
    // 可能被替换的奖励(提前解锁的皮肤，技能)
    [SerializeField] public Dictionary<string, CheckInReward> ActivityNewYear2025CheckInReplaceRewards = new Dictionary<string, CheckInReward>();
    // 2025春节签到开启状态
    [SerializeField] public CheckInOpenStatus open_activity_newyear2025_checkin = CheckInOpenStatus.None;

    #endregion

    #region 2025骑士归来签到

    // 首次打开的时间戳
    [SerializeField] public string ActivityReturnPlayerCheckInOpenTimestamp;

    // 签到7天奖励领取状态
    [SerializeField] public List<string> ActivityReturnPlayerCheckInStatusKeys = new List<string>();

    [SerializeField] public List<CheckStatus> ActivityReturnPlayerCheckInStatusValues = new List<CheckStatus>();

    // 可能被替换的奖励(提前解锁的皮肤，技能)
    [SerializeField] public Dictionary<string, CheckInReward> ActivityReturnPlayerCheckInReplaceRewards =
        new Dictionary<string, CheckInReward>();

    // 2025骑士归来签到开启状态
    [SerializeField] public CheckInOpenStatus open_activity_return_player_checkin = CheckInOpenStatus.None;

    #endregion

    /// <summary>
    /// 后续活动统一处理活动奖励兑换数据的存储
    /// </summary>
    [SerializeField]
    public Dictionary<string, Dictionary<int, int>> ActivityRedeemRecords = new Dictionary<string, Dictionary<int, int>>();

    /// <summary>
    /// 复活蛋活动背包的怪兽随从
    /// </summary>
    [SerializeField] 
    public List<RebornEggFollower> RebornEggFollowers = new List<RebornEggFollower>();
    
    /// <summary>
    /// 战斗存档随从消耗，避免下载存档白嫖
    /// </summary>
    /// <param name="followerId"></param>
    /// <param name="needRecord"></param>
    public void ConsumeFollower(string followerId, bool needRecord) {
        var findIndex = data.RebornEggFollowers.FindIndex(item => item.id == followerId);
        if (findIndex < 0) {
            Debug.LogError($"not found followerId:{followerId}");
            return;
        }
        
        data.RebornEggFollowers.RemoveAt(findIndex);
        if (needRecord) {
            //防止白嫖
            BattleData.data.AddConsumeInfo(new ConsumeInfo {
                name = followerId,
                count = 1,
                consumeType = emConsumeType.ActivityInventory,
                extra = ActivityRebornEggManager.TAG,
            });
        }
        Save();
    }
    
    #region 不再使用的字段
    [SerializeField]
    public Dictionary<int, int> _hero_init_weapons = new Dictionary<int, int>();
    public Dictionary<int, int> hero_init_weapons {
        get {
            if (_hero_init_weapons == null) {
                _hero_init_weapons = new Dictionary<int, int>();
            }
            return _hero_init_weapons;
        }
    }
    [SerializeField]
    public List<BaseEmail> alreadyGetRemoteEmails = new List<BaseEmail>();
    #endregion

    /// <summary>
    /// 活动每日对局次数
    /// </summary>
    public int TodayActivityPlayTime;

    public Dictionary<string, int> ToDayActivityPlayCounts = new Dictionary<string, int>();

    public void AddToDayActivityPlayCount(string activityTag, int count = 1) {
        if (ToDayActivityPlayCounts.ContainsKey(activityTag)) {
            ToDayActivityPlayCounts[activityTag] += count;
        } else {
            ToDayActivityPlayCounts.Add(activityTag, count);
        }
    }

    public int GetToDayActivityPlayCount(string activityTag) {
        return ToDayActivityPlayCounts.TryGetValue(activityTag, out var playTime) ? playTime : 0;
    }

    public int purchaseCount {
        get {
            return GetEventCount("purchase");
        }
        set {
            SetEventCount("purchase", value, true);
        }
    }
    public int maxLevelIndex { //最高闯到的关卡数(此处已弃用)
        get {
            return GetEventCount("maxLevelIndex");
        }
        set {
            int maxLevel = GetEventCount("maxLevelIndex");
            event2Count["event2Count"] = Mathf.Max(value, maxLevel);
        }
    }
    public int maxDefenceWave;//防守模式最大波数
    public int defenceRemakePass;
    public int defenceRemakeBadassPass;

    public int normalMaxDamage; // 普通关卡模式造成的最大伤害

    public Dictionary<string, int> GuaranteeDrop = new Dictionary<string, int>();

    public Dictionary<string, int> _plantTimes = new Dictionary<string, int>(); // 种植次数
    
    [SerializeField]
    public bool prequel_task1_finished; // 前传联动任务1已达成
    [SerializeField]
    public int prequel_task2_kill_boss_times; // 前传联动任务2，活动开始击杀boss次数
    [SerializeField]
    public bool prequel_task3_finished; // 前传联动任务3已达成
    
    [SerializeField]
    public List<string> promotionGotRewardIds = new List<string>(); // 已领取过奖励的推广视频

    public static string _43OldInternalPersistentPath => Abo.FileUtil.InternalPersistentPath + "/statistic.data";
    public static string Path => NewSDKManager.IsLogined() ? Abo.FileUtil.InternalPersistentPath + $"/statistic_{PrefixUID.uid}.data" : _43OldInternalPersistentPath;

    private static string RijPath => Path + RGGameConst.NewDataSuffix;
    private const string StatisticLoad = "StatisticLoad";
    private static string cy = "crst1";
    private static StatisticData _data;
    public static StatisticData data {
        get {
            if (_data != null) {
                return _data;
            }
            Load();
            return _data;
        }
        set {
            SetData(value);
        }
    }

    public static StatisticData RawData => _data;


    public static void FixData(string uid) {
        Abo.FileUtil.CopyStringFile(_43OldInternalPersistentPath, $"{Abo.FileUtil.InternalPersistentPath}/statistic_{uid}_.data");
        SetData(null);
    }
    
    /// <summary>
    /// 账号相关目录下不存在数据文件才拷贝，避免错误覆盖
    /// </summary>
    public static void FixDataIfNotExist() {
        if (LogUtil.IsShowLog) {
            LogUtil.Log($"FixDataIfNotExist start {Path} {ChannelConfig.IsNeedFixDataIfNotExist}", "Statistic");
        }
        if (!ChannelConfig.IsNeedFixDataIfNotExist) {
            return;
        }
        var oldExits = Abo.FileUtil.Exists(_43OldInternalPersistentPath);
        var exits = Abo.FileUtil.Exists(Path);
        if (!exits && oldExits) {
            if(LogUtil.IsShowLog){LogUtil.Log($"FixDataIfNotExist {_43OldInternalPersistentPath} => {Path}", "Move_Data");}
            Abo.FileUtil.CopyStringFile(_43OldInternalPersistentPath, Path);
            if (LogUtil.IsShowLog) {
                LogUtil.Log($"FixDataIfNotExist do move {Path} {ChannelConfig.IsNeedFixDataIfNotExist}", "Statistic");
            }
        }
    }
    
    /// <param name="objectNames">包含武器名、皮肤名、宠物名, 亲卫名</param>
    public void SavePassObjects(emPassGameLevel level, params string[] objectNames) {
        if (level != emPassGameLevel.Not) {
            foreach (var objectName in objectNames) {
                WeaponPassCheck(objectName);
                if (object2PassLevel.ContainsKey(objectName)) {
                    if (level > object2PassLevel[objectName]) {
                        object2PassLevel[objectName] = level;
                    }
                } else {
                    object2PassLevel[objectName] = level;
                }

                object2PassTime.TryAdd(objectName, 0);
                object2PassTime[objectName]++;
            }
            Save();
        }
    }
    /// <summary>
    /// 如果携带未拾取的武器通关（武器被别的玩家、随从首先拾取过之后再被玩家拾取），拾取数变为1
    /// </summary>
    private void WeaponPassCheck(string weaponName) {
        if (!weaponName.StartsWith("weapon_")) return;
        if (!object2ObtainTime.ContainsKey(weaponName) || object2ObtainTime[weaponName] == 0) {
            object2ObtainTime[weaponName] = 1;
        }
    }
    public emPassGameLevel GetPassLevel(string name) {
        if (object2PassLevel.TryGetValue(name, out emPassGameLevel level)) {
            return level;
        } else {
            return emPassGameLevel.Not;
        }
    }
    public int GetPassTime(string name) {
        if (object2PassTime.TryGetValue(name, out int time)) {
            return time;
        }
        return 0;
    }
    public int GetObtainTime(string name) {
        if (object2ObtainTime.TryGetValue(name, out int time)) {
            return time;
        }
        return 0;
    }
    public void AddObtainTimes(string name, int addCount, bool save) {
        if (BattleData.data.IsSandbox) { //沙盒模式不统计次数
            return;
        }
        
        if (BattleData.data.factors.Contains(emBattleFactor.ReturnPlayer) && RGGameProcess.Inst.this_index == -4) {
            return;
        }
        
        if (object2ObtainTime.ContainsKey(name)) {
            object2ObtainTime[name] += addCount;
        } else {
            object2ObtainTime[name] = addCount;
        }
        if (save) {
            Save();
        }
    }
    public void DebugResetObtainTimes(string name){
        object2ObtainTime.Remove(name);
    }

    public int GetEventCount(string name) {
        if (event2Count.TryGetValue(name, out int count)) {
            return count;
        }
        return 0;
    }
    public void AddEventCount(string name, int addCount, bool save) {
        if (BattleData.data.IsSandbox) { //沙盒模式不统计次数
            return;
        }
        if (event2Count.ContainsKey(name)) {
            event2Count[name] += addCount;
        } else {
            event2Count[name] = addCount;
        }
        if (save) {
            Save();
        }
    }
    public void SetEventCount(string name, int count, bool save) {
        if (BattleData.data.IsSandbox) { //沙盒模式不统计次数
            return;
        }
        event2Count[name] = count;
        if (save) {
            Save();
        }
    }
    public void RecordEvent(string eventName, bool save) {
        if (BattleData.data.IsSandbox) { //沙盒模式不统计次数
            return;
        }
        if (!recordedEvents.Contains(eventName)) {
            recordedEvents.Add(eventName);
            if (save) {
                Save();
            }
        }
    }
    public bool IsEventRecord(string eventName) {
        return recordedEvents.Contains(eventName);
    }

    public void RemoveRecordEvent(string eventName, bool save) {
        if (!recordedEvents.Contains(eventName)) {
            return;
        }

        recordedEvents.Remove(eventName);
        if (save) {
            Save();
        }
    }

#if UNITY_EDITOR
    public void RemoveRecordEventDebug(string eventName, bool save) {
        if (recordedEvents.Contains(eventName)) {
            recordedEvents.Remove(eventName);
            if (save) {
                Save();
            }
        }

        if (event2Count.ContainsKey(eventName)) {
            event2Count.Remove(eventName);
            if (save) {
                Save();
            }
        }
    }
#endif

    public void UpdatePassInfoStr(string key, string time, bool save = false) {
        passInfo[key] = time;
        if (save) {
            Save();
        }
    }

    public string GetPassInfoStr(string key) {
        if (passInfo.ContainsKey(key)) {
            return passInfo[key];
        }
        return "";
    }

    public static void Save() {
        if (LogUtil.IsShowLog) {
            LogUtil.Log($"Save start {Path}", "Statistic");
        }
        try {
            SaveUtil.SaveCryptData(data, Path, RijPath, cy);
            PlayerSaveData.Inst.has_statistic_data = true;
            //string content = Abo.CryptUtil.EncryptXor(fsUtil.ToJson<StatisticData>(data), cy);
            //Abo.FileUtil.WriteFile(FilePath, content);
        } catch (System.Exception e) {
            BuglyUtil.ReportException("StatisticSave", e);
        }
        if (LogUtil.IsShowLog) {
            LogUtil.Log($"Save end {Path}", "Statistic");
        }
    }

    static void NewData() {
        if (LogUtil.IsShowLog) {
            LogUtil.Log($"NewData start {Path}", "Statistic");
        }
        if(_data != null) {
            SetData(null);
        }
        SetData(new StatisticData());
        Save();
        if (LogUtil.IsShowLog) {
            LogUtil.Log($"NewData end {Path}", "Statistic");
        }
    }
    
    // 获取账户的数据位置
    public static string GetDataPathByAccountID(string accountID) {
        return Abo.FileUtil.InternalPersistentPath + $"/statistic_{accountID}_.data";
    }
        
    // 获取账户数据
    public static IData GetAccountData(string dataPath) {
        if (Abo.FileUtil.Exists(dataPath)) {
            return SaveUtil.LoadCryptData<StatisticData>(dataPath, dataPath + RGGameConst.NewDataSuffix, cy, StatisticLoad);
        }

        return null;
    }
    
    // 设置数据
    public static void SetData(IData iData) {
        if (iData == null) {
            // 解除当前数据的事件订阅
            if (_data != null) {
                GameStatisticManager.Instance.UnsubscribeAllEvents();
            }
            _data = null;
            return;
        }
        
        if (iData is StatisticData statisticData) {
            // 解除当前数据的事件订阅
            if (_data != null) {
                GameStatisticManager.Instance.UnsubscribeAllEvents();
            }
            _data = statisticData;
            // 订阅新数据的事件
            GameStatisticManager.Instance.SubscribeAllEvents();
        }
    }
    
    static void Load() {
        if (LogUtil.IsShowLog) {
            LogUtil.Log($"Load start {Path}", "Statistic");
        }
#if UNITY_SWITCH && !UNITY_EDITOR
        if (!Abo.FileUtil.Exists(Path)) {
            NewData();
            return;
        }
        _data = Abo.JsonUtil.LoadJsonWithCrypt<StatisticData>(Path, cy, PlayerSaveData.Inst.has_statistic_data ? "StatisticLoad" : "");
        GameStatisticManager.Instance.SubscribeAllEvents();
        if (_data == null) {
            NewData();
            return;
        }
        return;
#endif
        var accountData = GetAccountData(Path);
        if (accountData != null && accountData is StatisticData statisticData) {
            SetData(statisticData);
        }

        if (_data == null) {
            NewData();
        }
        
        if (LogUtil.IsShowLog) {
            LogUtil.Log($"Load end {Path}", "Statistic");
        }
    }
        
    public static void ClearData(string prefix) {
        File.Delete(Abo.FileUtil.InternalPersistentPath + $"/statistic_{prefix}.data");
    }

    public void Merge(StatisticData other) {
        if (other != null) {
            foreach (var passLevel in other.object2PassLevel) {
                if (this.object2PassLevel.ContainsKey(passLevel.Key)) {
                    this.object2PassLevel[passLevel.Key] = (emPassGameLevel)Mathf.Max((int)passLevel.Value, (int)this.object2PassLevel[passLevel.Key]);
                } else {
                    this.object2PassLevel[passLevel.Key] = passLevel.Value;
                }
            }
            foreach (var passTimes in other.object2PassTime) {
                if (this.object2PassTime.ContainsKey(passTimes.Key)) {
                    this.object2PassTime[passTimes.Key] = Mathf.Max(passTimes.Value, this.object2PassTime[passTimes.Key]);
                } else {
                    this.object2PassTime[passTimes.Key] = passTimes.Value;
                }
            }
            foreach (var obtainTimes in other.object2ObtainTime) {
                if (this.object2ObtainTime.ContainsKey(obtainTimes.Key)) {
                    this.object2ObtainTime[obtainTimes.Key] = Mathf.Max(obtainTimes.Value, this.object2ObtainTime[obtainTimes.Key]);
                } else {
                    this.object2ObtainTime[obtainTimes.Key] = obtainTimes.Value;
                }
            }
            foreach (var eventTimes in other.event2Count) {
                if (this.event2Count.ContainsKey(eventTimes.Key)) {
                    this.event2Count[eventTimes.Key] = Mathf.Max(eventTimes.Value, this.event2Count[eventTimes.Key]);
                } else {
                    this.event2Count[eventTimes.Key] = eventTimes.Value;
                }
            }
        }
    }
    public void CopyFromGame() {
        try {
            foreach (var enemy in TaskInfo.info.enemyTasks1) {
                int oldTime = PlayerSaveData.GetInt("score_" + enemy.enemyId, 0);
                int newTime = object2ObtainTime.ContainsKey(enemy.enemyId) ? object2ObtainTime[enemy.enemyId] : 0;
                int time = Mathf.Max(oldTime, newTime);
                if (time > 0) {
                    object2ObtainTime[enemy.enemyId] = time;
                }
            }
            foreach (var enemy in TaskInfo.info.enemyTasks2) {
                int oldTime = PlayerSaveData.GetInt("score_" + enemy.enemyId, 0);
                int newTime = object2ObtainTime.ContainsKey(enemy.enemyId) ? object2ObtainTime[enemy.enemyId] : 0;
                int time = Mathf.Max(oldTime, newTime);
                if (time > 0) {
                    object2ObtainTime[enemy.enemyId] = time;
                }
            }
            foreach (var enemy in TaskInfo.info.enemyTasks3) {
                int oldTime = PlayerSaveData.GetInt("score_" + enemy.enemyId, 0);
                int newTime = object2ObtainTime.ContainsKey(enemy.enemyId) ? object2ObtainTime[enemy.enemyId] : 0;
                int time = Mathf.Max(oldTime, newTime);
                object2ObtainTime[enemy.enemyId] = time;
            }
            foreach (var enemy in TaskInfo.info.enemyTasks4) {
                int oldTime = PlayerSaveData.GetInt("score_" + enemy.enemyId, 0);
                int newTime = object2ObtainTime.ContainsKey(enemy.enemyId) ? object2ObtainTime[enemy.enemyId] : 0;
                int time = Mathf.Max(oldTime, newTime);
                object2ObtainTime[enemy.enemyId] = time;
            }
            foreach (var weapon in WeaponInfo.info.weapons) {
                int oldTime = PlayerSaveData.GetInt("score_" + weapon.name, 0);
                int newTime = object2ObtainTime.ContainsKey(weapon.name) ? object2ObtainTime[weapon.name] : 0;
                int time = Mathf.Max(oldTime, newTime);
                object2ObtainTime[weapon.name] = time;
            }
            foreach (var plant in ItemData.data.plants) {
                if (plant != null && !string.IsNullOrEmpty(plant.plantName)) {
                    int oldTime = object2ObtainTime.ContainsKey(plant.plantName) ? object2ObtainTime[plant.plantName] : 0;
                    int newTime = 1;
                    int time = Mathf.Max(oldTime, newTime);
                    object2ObtainTime[plant.plantName] = time;
                }
            }
            foreach (var pair in ItemData.data.seeds) {
                string plantName = pair.Key.Replace("_seed", "");
                int oldTime = object2ObtainTime.ContainsKey(plantName) ? object2ObtainTime[plantName] : 0;
                int newTime = pair.Value;
                int time = Mathf.Max(oldTime, newTime);
                object2ObtainTime[plantName] = time;
            }
            int passTime = 0;
            int passBadassTime = 0;
            for (emHero hero = 0; hero < emHero.Count; hero++) {
                for (int i = 0; i < 8; i++) {
                    var itemId = hero.ToString() + i.ToString();
                    passTime += GetPassTime(itemId);
                    if (GetPassLevel(itemId) == emPassGameLevel.Badass) {
                        passBadassTime += 1;
                    }
                }
            }
            event2Count["pass"] = Mathf.Max(passTime, GetEventCount("pass"));
            event2Count["pass_badass"] = Mathf.Max(passBadassTime, GetEventCount("pass_badass"));
        } catch (System.Exception e) {
            Debug.LogError(e);
        } finally {
            Save();
        }
    }
    public void CopyAddtionFromGame() {
        foreach (var enemy in TaskInfo.info.enemyTasksAddition) {
            int oldTime = PlayerSaveData.GetInt("score_" + enemy.enemyId, 0);
            int newTime = object2ObtainTime.ContainsKey(enemy.enemyId) ? object2ObtainTime[enemy.enemyId] : 0;
            int time = oldTime + newTime;
            if (time > 0) {
                object2ObtainTime[enemy.enemyId] = time;
            }
        }
    }
    public void CopyAddtionFromGame2() {
        foreach (var enemy in TaskInfo.info.enemyTasksAddition) {
            int oldTime = PlayerSaveData.GetInt("score_" + enemy.enemyId, 0);
            int newTime = object2ObtainTime.ContainsKey(enemy.enemyId) ? object2ObtainTime[enemy.enemyId] : 0;
            int time = newTime < oldTime ? oldTime + newTime : newTime;
            if (time > 0) {
                object2ObtainTime[enemy.enemyId] = time;
            }
        }
    }
    public void RestoreWeaponTimes() {
        foreach (var weapon in WeaponInfo.info.weapons) {
            int oldTime = PlayerSaveData.GetInt("score_" + weapon.name, 0);
            int newTime = object2ObtainTime.ContainsKey(weapon.name) ? object2ObtainTime[weapon.name] : 0;
            if (oldTime > newTime) {
                int time = oldTime + newTime;
                object2ObtainTime[weapon.name] = time;
            }
        }
    }
    public void ResetPassTimes() {
        int passTime = 0;
        int passBadassTime = 0;
        for (emHero hero = 0; hero < emHero.Count; hero++) {
            for (int i = 0; i < 8; i++) {
                var itemId = hero.ToString() + i.ToString();
                passTime += GetPassTime(itemId);
                if (GetPassLevel(itemId) == emPassGameLevel.Badass) {
                    passBadassTime += 1;
                }
            }
        }
        event2Count["pass"] = Mathf.Max(passTime, GetEventCount("pass"));
        event2Count["pass_badass"] = Mathf.Max(passBadassTime, GetEventCount("pass_badass"));
    }

    /// <summary>
    /// 更新本地的成就记录
    /// </summary>
    /// <param name="id">Identifier.</param>
    /// <param name="info">Info.</param>
    public void UpdateAchievementRecord(int id, AchieveInfos.AchievementInfo info) {
        AchieveInfo old_info = null;
        if (!achieve_record.ContainsKey(id)) {
            AchieveInfo new_info = new AchieveInfo();
            achieve_record.Add(id, new_info);
        }

        old_info = achieve_record[id];
        old_info.id = id;
        // if (info.unlock) {
        //     old_info.unlock = true;
        // }
        // if (info.got_award) {
        //     old_info.got_award = true;
        // }
    }

    public AchieveInfo GetAchievementRecordById(int id) {

        if (!achieve_record.ContainsKey(id)) {
            AchieveInfos.AchievementInfo ac_info = AchieveInfos.info.GetAchievementInfoById(id);
            UpdateAchievementRecord(id, ac_info);
        }
        AchieveInfo info = achieve_record[id];
        return info;
    }

    public bool IsAchievementUnlocked(int id) {
        var info = GetAchievementRecordById(id);
        return info.unlock;
    }

    public void AchievementGotAward(int id, bool save) {
        AchieveInfo info = GetAchievementRecordById(id);
        info.got_award = true;
        info.has_show = true;
        if (save) {
            StatisticData.Save();
        }
    }

    public static void ResetData() {
        SetData(new StatisticData());
    }

    public void AddNewPassHistory(PassInfo info) {
        if (null == _pass_history_list) {
            _pass_history_list = new List<PassInfo>();
        }
        Queue<PassInfo> queue = new Queue<PassInfo>(_pass_history_list);
        queue.Enqueue(info);
        while (queue.Count > PassHistoryMaxCount) {
            queue.Dequeue();
        }
        _pass_history_list = queue.ToList();
    }

    public void AddNewMultiGameHistory(MultiGameHistoryData info) {
        if (null == _multiGameHistoryList) {
            _multiGameHistoryList = new List<MultiGameHistoryData>();
        }
        Queue<MultiGameHistoryData> queue = new Queue<MultiGameHistoryData>(_multiGameHistoryList);
        queue.Enqueue(info);
        while (queue.Count > MultiGameHistoryMaxCount) {
            queue.Dequeue();
        }
        _multiGameHistoryList = queue.ToList();
    }
    
    /// <summary>
    /// 记录当前游戏的id
    /// </summary>
    public void RecordSaveId() {
        // BattleDataList.Load();
        var battleDatas = BattleDataList.list.BattleDatas;
        if (null == battleDatas) {
            return;
        }
        foreach (var pair in battleDatas) {
            mode2SaveGameIdDic[pair.Key] = pair.Value.savedataGameId;
        }
    }
    public void RecordStatementSaveId(BattleDataList.emSavePlace place, long id) {
        statementMode2SaveGameIdDic[place] = id;
    }

    public bool AlreadyRecordStatementSaveId(BattleDataList.emSavePlace place, long id) {
        if (statementMode2SaveGameIdDic.TryGetValue(place, out long savedId)) {
            return savedId == id;
        }

        return false;
    }

    public SeasonPrize GetSeasonPrize(Season season) {
        if (season2Prizes.ContainsKey(season)) {
            return (SeasonPrize)Mathf.Clamp((int)season2Prizes[season], (int)SeasonPrize.NotAward, (int)SeasonPrize.Gold);
        }
        return SeasonPrize.NotAward;
    }

    public void SetSeasonPrize(Season season, SeasonPrize prize, bool save = true) {
        if (!season2Prizes.ContainsKey(season) || season2Prizes[season] < prize || Application.isEditor) {
            season2Prizes[season] = prize;
        }

        if (save) {
            Save();
        }
    }

    /// <summary>
    /// 是否解锁金手刀
    /// </summary>
    public bool UnlockHandSwordAc() {
        var handSwordAc = GetAchievementRecordById(31);
        bool unlock_hand_sword = null != handSwordAc && handSwordAc.unlock;
        return unlock_hand_sword;
    }

    /// <summary>
    /// 是否解锁手刀 成就无招胜有招92
    /// </summary>
    public bool UnlockHandSwordAc1() {
        var handSwordAc = GetAchievementRecordById(92);
        bool unlock_hand_sword = null != handSwordAc && handSwordAc.unlock;
        return unlock_hand_sword;
    }
    /// <summary>
    /// 是否解锁手刀 成就七步之内93
    /// </summary>
    public bool UnlockHandSwordAc2() {
        var handSwordAc = GetAchievementRecordById(93);
        bool unlock_hand_sword = null != handSwordAc && handSwordAc.unlock;
        return unlock_hand_sword;
    }

    public bool UnlockHandSwordAc3() {
        var handSwordAc = GetAchievementRecordById(116);
        bool unlock_hand_sword = null != handSwordAc && handSwordAc.unlock;
        return unlock_hand_sword;
    }

    public void SetHandSwordIndex(int index) {
        manual_change_hand_cut_index = true;
        if (IsHandCutUnlocked(index)) {
            hand_cut_index = index;
        }
        Save();
    }

    public static List<int> GetUnlockedHandStyles() {
        var ret = new List<int>() { 0 };
        if (data.UnlockHandSwordAc()) {
            ret.Add(1);
        }
        if (data.UnlockHandSwordAc1()) {
            ret.Add(2);
        }
        if (data.UnlockHandSwordAc2()) {
            ret.Add(3);
        }
        if (data.UnlockHandSwordAc3()) {
            ret.Add(4);
        }
        if (ItemData.data.handStyleIndexList != null) {
            ret.AddRange(ItemData.data.handStyleIndexList);
        }
        return ret;
    }

    public bool IsHandCutUnlocked(int index) {
        return GetUnlockedHandStyles().Contains(index);
    }

    /// <summary>
    /// 金手刀类型
    /// </summary>
    public int GetHandSwordIndex() {
        var unlockGoldHandcut = UnlockHandSwordAc();
        // 如果没有手动切换过手刀，就按成就来
        if (!manual_change_hand_cut_index) {
            return unlockGoldHandcut ? 1 : 0;
        }
        // 如果手动切换过手刀，就按玩家设定来
        var index = hand_cut_index;
        if (index == 1 && unlockGoldHandcut) {
            return index;
        }

        if (index == 2 && UnlockHandSwordAc1()) {
            return index;
        }

        if (index == 3 && UnlockHandSwordAc2()) {
            return index;
        }

        if (index == 4 && UnlockHandSwordAc3()) {
            return index;
        }

        return index;
    }

    public void SetGuaranteeDropCount(string key, int count) {
        if (count == 0) {
            GuaranteeDrop.Remove(key);
            return;
        }
        
        if (GuaranteeDrop.ContainsKey(key)) {
            GuaranteeDrop[key] = count;
        } else {
            GuaranteeDrop.Add(key, count);
        }
    }

    public int GetGuaranteeDropCount(string key) {
        return GuaranteeDrop.ContainsKey(key) ? GuaranteeDrop[key] : 0;
    }

    public void AddPlantTimes(string plantName, bool save){
        if (_plantTimes.ContainsKey(plantName)) {
            _plantTimes[plantName] += 1;
        } else {
            _plantTimes.Add(plantName, 1);
        }
        if (save) {
            Save();
        }
    }

    public int GetPlantTimes(string plantName){
        return _plantTimes.TryGetValue(plantName, out var times) ? times : 0;
    }

    public bool HasPlantSeed() {
        return _plantTimes.Count > 0;
    }
    [SerializeField]
    private Dictionary<BattleDataList.emSavePlace, ulong> _autoIncrementSaveIdDic = new Dictionary<BattleDataList.emSavePlace, ulong>();

    /// <summary>
    /// 获取当前玩法存档槽位对应的id
    /// </summary>
    /// <param name="battleData"></param>
    /// <returns></returns>
    public ulong GetCurrentSaveId(BattleData battleData) {
        if (null == battleData) {
            Debug.LogError("GetCurrentSaveId battleData is null");
            return 0;
        }
        var savePlace = BattleDataList.GetSavePlace(battleData);
        return GetCurrentSaveIdWithSavePlace(savePlace);
    }

    public ulong GetCurrentSaveIdWithSavePlace(BattleDataList.emSavePlace savePlace) {
        return _autoIncrementSaveIdDic.TryGetValue(savePlace, out ulong value) ? value : (ulong)0;
    }

    public void SetCurrentSaveId(BattleData battleData, ulong id, bool save = true) {
        if (null == battleData) {
            Debug.LogError("battleData battleData is null");
            return;
        }
        var savePlace = BattleDataList.GetSavePlace(battleData);
        SetCurrentSaveIdWithSavePlace(savePlace, id, save);
    }

    public void SetCurrentSaveIdWithSavePlace(BattleDataList.emSavePlace savePlace, ulong id, bool save = true) {
        _autoIncrementSaveIdDic[savePlace] =  id;
        Save();
    }

    public void AddActivityRedeemBlueprint(string blueprint, bool save) {
        if (!ActivityRedeemBlueprints.Contains(blueprint)) {
            ActivityRedeemBlueprints.Add(blueprint);
            if (save) {
                Save();
            }
        }
    }

    public bool IsBlueprintFromActivity(string blueprint) {
        return ActivityRedeemBlueprints.Contains(blueprint);
    }

    public static void Reload() {
        if (LogUtil.IsShowLog) {
            LogUtil.Log($"Reload start {Path}", "Statistic");
        }
        SetData(null);
        Load();
    }
}