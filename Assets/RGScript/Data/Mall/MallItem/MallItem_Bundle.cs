using cfg.Store;
using RGScript.Data.GameItemData;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace RGScript.Data.Mall {
    /// <summary>
    /// 礼包
    /// </summary>
    public class MallItem_Bundle : MallItem {
        public int id;
        public int pkgIndex;

        public List<MallGameItem> items { get; protected set; }

        // 只有弹性小鱼干礼包有用
        public List<MallGameItem> notOwnedItems { get; protected set; }

        public MallItem_Bundle(StoreGood config) : base(config) {
            if (itemList.Count == 0) {
                Debug.LogError($"config.ItemList.Count ==0 {config.GoodID}");
                return;
            }

            id = config.GoodID;
            MallItemType = MallItemType.Bundle;
            pkgIndex = MallUtility.GetNewPkgIndex(this);
            if (config.GoodUIType == GoodUIType.MediumUi) {
                itemUISize = ItemUISize.Medium;
            } else if (config.GoodUIType == GoodUIType.LargeUI) {
                itemUISize = ItemUISize.Large;
            } else if (config.GoodUIType == GoodUIType.SmallUI) {
                itemUISize = ItemUISize.Small;
            }

            items = new List<MallGameItem>();
            foreach (var goodItem in itemList) {
                items.Add(goodItem);
            }

            UpdateBlindBoxItemOrder();

            notOwnedItems = new List<MallGameItem>();
            UpdateFinalPrice();
        }

        private void UpdateBlindBoxItemOrder() {
            if (!MallData.BlindBoxIds.Contains(Config.GoodID)) {
                return;
            }

            // DataMgr.ConfigData.Tables.TbWeaponFragment.DataMap[itemId].GoodOrder
            // sort
            items.Sort((a, b) => {
                var aOrder = GetBlindBoxItemOrder(a.id);
                var bOrder = GetBlindBoxItemOrder(b.id);
                return bOrder - aOrder;
            });
        }

        private int GetBlindBoxItemOrder(string itemId) {
            if (!DataMgr.MallData.weaponFragmentDict.TryGetValue(itemId, out var weaponFragment)) {
                return 0;
            }

            return weaponFragment.GoodOrder;
        }

        public void UpdateFinalPrice() {
            var config = Config;
            if (MallUtility.IsDynamicPackage(config)) {
                UpdateDynamicPackage(config);
            }

            // 前三次1小鱼干的盲盒
            var freeBlindBoxIds = new List<int>() { 7010090 };
            foreach (int blindBoxId in freeBlindBoxIds) {
                if (config.GoodID != blindBoxId) {
                    continue;
                }

                var purchaseTime = GetBlindBoxPurchaseTime(config);
                if (purchaseTime < 0) {
                    finalPrice = 9999999;
                } else if (purchaseTime <= 2) {
                    finalPrice = 1;
                } else {
                    finalPrice = 3;
                }

                break;
            }

            if (config.GoodID == 7010122) {
                var purchaseTime = 0;
                if (MallReloadData.data.blindBoxBuyCountDict.TryGetValue(122, out var count)) {
                    purchaseTime = count;
                }

                if (purchaseTime < 0) {
                    finalPrice = 9999999;
                } else if (purchaseTime == 0) {
                    finalPrice = 0;
                } else if (purchaseTime == 1) {
                    finalPrice = 3;
                }
            }

            if (config.GoodID == 7010144) {
                var purchaseTime = 0;
                if (MallReloadData.data.blindBoxBuyCountDict.TryGetValue(144, out var count)) {
                    purchaseTime = count;
                }
                var canBuyFree7010144 = StatisticData.data.GetEventCount("can_buy_free_7010144_time") > 0;
                var canBuy3Fish7010144 = StatisticData.data.GetEventCount("can_buy_3_7010144_time") > 0;

                if (purchaseTime < 0) {
                    finalPrice = 9999999;
                } else if (purchaseTime == 0 || canBuyFree7010144) {
                    finalPrice = 0;
                } else if (purchaseTime == 1 || canBuy3Fish7010144) {
                    finalPrice = 3;
                } else {
                    finalPrice = Config.PriceReal;
                }
            }
            // 卡皮巴拉盲盒改成首抽3鱼干 后续7鱼干（把首抽免费干掉）
            if (config.GoodID == 7010157) {
                var purchaseTime = 0;
                if (MallReloadData.data.blindBoxBuyCountDict.TryGetValue(157, out var count)) {
                    purchaseTime = count;
                }

                if (purchaseTime < 0) {
                    finalPrice = 9999999;
                } else if (purchaseTime == 0) {
                    finalPrice = 3;
                }
            }
        }

        private int GetBlindBoxPurchaseTime(StoreGood config) {
            // 1~2小鱼干（每周前三抽1小鱼干, 随后2小鱼干）
            var history = MallUtility.GetPurchaseHistory(config.GoodID);
            // 没购买过，因此没有记录
            int purchaseTime = 0;
            if (history != null) {
                long lastBuyTimestamp = history.lastTimestamp;
                var lastTimestampIsInBuyPeriod = MallUtility.IsInBuyPeriod(lastBuyTimestamp,
                    MallData.GetPeriodStartTime(config), TimeLimitType.EveryWeek);
                if (lastTimestampIsInBuyPeriod) {
                    purchaseTime = history.count;
                }
            }

            return purchaseTime;
        }

        public override float GetShowPriceDiscount() {
            var config = Config;
            // 前三次1小鱼干的盲盒
            var freeBlindBoxIds = new List<int>() { 7010090 };
            foreach (int blindBoxId in freeBlindBoxIds) {
                if (config.GoodID != blindBoxId) {
                    continue;
                }

                var purchaseTime = GetBlindBoxPurchaseTime(config);
                if (purchaseTime > 2) {
                    return 0;
                }
            }

            if (config.GoodID == 7010144) {
                var purchaseTime = 0;
                if (MallReloadData.data.blindBoxBuyCountDict.TryGetValue(144, out var count)) {
                    purchaseTime = count;
                }

                var canBuyFree7010144 = StatisticData.data.GetEventCount("can_buy_free_7010144_time") == 1;
                var canBuy3Fish7010144 = StatisticData.data.GetEventCount("can_buy_3_7010144_time") == 1;

                if (purchaseTime == 0 || canBuyFree7010144) {
                    return -1f;
                } else if (purchaseTime == 1 || canBuy3Fish7010144) {
                    return -0.7f;
                }
            }

            return base.GetShowPriceDiscount();
        }

        private void UpdateDynamicPackage(StoreGood config) {
            notOwnedItems.Clear();
            var price = 0;
            for (int i = 0; i < items.Count; i++) {
                MallGameItem gameItem = items[i];
                if (!ItemUtility.HasOwnItem(gameItem.config)) {
                    if (i < config.FishPriceList.Count) {
                        price += config.FishPriceList[i];
                    }

                    notOwnedItems.Add(gameItem);
                }
            }

            finalPrice = price;
        }

        public override string GetShowItemRawTitle() {
            return ItemUtility.GetBundleName(id);
        }

        public override string GetShowItemPrice() {
            if (currencyType == CurrencyType.Cash) {
                if (string.IsNullOrEmpty(Config.CommercialID)) {
                    Debug.LogError($"MallItem_Bundle, {GetDebugDescription()} Config.CommercialID is null or empty");
                    return "";
                }

                return FuckCurrencyBuyUtil.GetPriceDescription(Config.CommercialID);
            }

            return base.GetShowItemPrice();
        }

        protected override List<MallGameItem> GetMallGameItems() {
            int price = 0;
            // 可以获得的物品
            List<MallGameItem> canGetItems = new List<MallGameItem>();
            for (int i = 0; i < items.Count; i++) {
                MallGameItem mallGameItem = items[i];
                var gameItem = mallGameItem.config;
                string key = gameItem.key;
                var ty = gameItem.ty;
                bool hasOwned = ItemUtility.HasOwnItem(key, ty);
                if (hasOwned) {
                    if (Config.GoodType == 1) {
                        price += Config.PriceComp[i];
                    }
                } else {
                    canGetItems.Add(mallGameItem);
                }
            }

            if (price > 0) {
                canGetItems.Add(new MallGameItem(ItemData.GemName, price));
            }

            return canGetItems;
        }

        /// <summary>
        /// 是否有能兑换成宝石的物品
        /// </summary>
        /// <returns></returns>
        public bool HasCanConvertGemItems() {
            foreach (var mallGameItem in items) {
                var gameItem = mallGameItem.config;
                string key = gameItem.key;
                var ty = gameItem.ty;
                bool hasOwned = ItemUtility.HasOwnItem(key, ty);
                if (hasOwned) {
                    return true;
                }
            }

            return false;
        }

        public override Dictionary<GameItem, int> GetConvertedGemDict() {
            if (items.Count != Config.PriceComp.Count) {
                return null;
            }

            Dictionary<GameItem, int> dict = new Dictionary<GameItem, int>();
            for (int i = 0; i < items.Count; i++) {
                MallGameItem mallGameItem = items[i];
                var gameItem = mallGameItem.config;
                var price = Config.PriceComp[i];
                dict.Add(gameItem, price);
            }

            return dict;
        }
    }
}