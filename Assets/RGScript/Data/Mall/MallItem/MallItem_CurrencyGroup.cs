using RGScript.Mall;
using System.Collections.Generic;

namespace RGScript.Data.Mall {
    /// <summary>
    /// 货币购买
    /// </summary>
    public class MallItem_CurrencyGroup : MallItem {
        public List<MallItem_Currency> gems { get; protected set; }
        public List<MallItem_Currency> fishChip { get; protected set; }
        public List<MallItem_Currency> seasons { get; protected set; }
        public MallItem_Currency rebornCard { get; protected set; }

        public MallItem_CurrencyGroup(List<MallItem_Currency> gems, List<MallItem_Currency> fishChip,
            List<MallItem_Currency> seasons, MallItem_Currency rebornCard) : base() {
            MallId = MallData.CurrencyMallId;
            itemUISize = ItemUISize.Custom;
            MallItemType = MallItemType.CurrencyGroup;
            defaultWidth = UIMallListCellWidget_CurrencyGroup.GetWidth();
            this.gems = gems;
            this.fishChip = fishChip;
            this.seasons = seasons;
            this.rebornCard = rebornCard;
        }
    }
}