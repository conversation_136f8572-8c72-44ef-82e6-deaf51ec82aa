using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Sirenix.OdinInspector;
using System.IO;
using System;
using I2.Loc;
using System.Reflection;
#if UNITY_EDITOR
using UnityEditor;
#endif

[System.Serializable]
public class NpcInfo {
    [PreviewField]
    public Sprite icon;//图标
    public string npcName;//中文名
    public string name;//对应gameObject名
    public string i2Key;//对应本地化Key
    public int level;//等级
    public Sprite tokenIcon;//代币图标
    public int price;//基础价格
    public bool showInCell = true;//是否在地窖展示
}
[CreateAssetMenu(fileName = "npc_info", menuName = "Data/NPCInfo")]
public class NPCInfoTable : SerializedScriptableObject {
    private static NPCInfoTable _info;
    public static NPCInfoTable info {
        get {
            if (_info == null) {
                if (Application.isEditor && !Application.isPlaying) {
#if UNITY_EDITOR
                    _info = AssetDatabase.LoadAssetAtPath<NPCInfoTable>("Assets/Data/npc_info.asset");
#endif
                } else {
                    _info = ResourcesUtil.Load<NPCInfoTable>("Data/npc_info.asset");
                }
            }
            return _info;
        }
    }
    public List<NpcInfo> npcs = new List<NpcInfo>();
    public Sprite coinIcon;
    public Sprite gemIcon;
    public Sprite hpIcon;

    public NpcInfo GetNpc(string name) {
        for (int i = 0; i < npcs.Count; i++) {
            if (npcs[i].name == name) {
                return npcs[i];
            }
        }
        return null;
    }
#if UNITY_EDITOR
    /// <summary>
    /// 看起来不是特别好做自动化.可能还是需要手动维护
    /// </summary>
    //[Button("更新NPC配置")]
    public static void UpdateNpcTable() {
        Action<GameObject> updateInfo = (go) => {
            if (go.GetComponent<NpcMercenaryController>()) {
                var mercenary = go.GetComponent<NpcMercenaryController>();
                NpcInfo npcInfo = info.GetNpc(go.name);
                if (npcInfo == null) {
                    npcInfo = new NpcInfo();
                    npcInfo.name = go.name;
                    if (go.transform.Find("talk") && go.transform.Find("talk").GetComponent<TalkMercenary>()) {
                        npcInfo.price = go.transform.Find("talk").GetComponent<TalkMercenary>().GetItemValue();
                    }
                    npcInfo.npcName = ScriptLocalization.GetCN(mercenary.name);
                    npcInfo.icon = go.transform.Find("img/body").GetComponent<SpriteRenderer>().sprite;
                    npcInfo.tokenIcon = info.coinIcon;
                    info.npcs.Add(npcInfo);
                }
            } else if (go.GetComponentInChildren<RGItem>()) {
                var item = go.GetComponentInChildren<RGItem>();
                NpcInfo npcInfo = info.GetNpc(go.name);
                if (npcInfo == null) {
                    npcInfo = new NpcInfo();
                    npcInfo.name = go.name;
                    var itemValueField = item.GetType().GetField("item_value", BindingFlags.GetField | BindingFlags.Public | BindingFlags.Instance);
                    if (itemValueField != null) {
                        npcInfo.price = (int)itemValueField.GetValue(item);
                    }
                    npcInfo.npcName = ScriptLocalization.GetCN(item.item_name);
                    npcInfo.level = item.GetItemLevel();
                    npcInfo.tokenIcon = info.coinIcon;
                    if (go.GetComponentInChildren<SpriteRenderer>()) {
                        npcInfo.icon = go.GetComponentInChildren<SpriteRenderer>().sprite;
                    }
                    info.npcs.Add(npcInfo);
                }
            } else if (go.GetComponent<ItemInterface>() != null) {
                var item = go.GetComponent<ItemInterface>();
                NpcInfo npcInfo = info.GetNpc(go.name);
                if (npcInfo == null) {
                    npcInfo = new NpcInfo();
                    npcInfo.name = go.name;
                    var itemValueField = item.GetType().GetField("item_value", BindingFlags.GetField | BindingFlags.Public | BindingFlags.Instance);
                    if (itemValueField != null) {
                        npcInfo.price = (int)itemValueField.GetValue(item);
                    }
                    npcInfo.npcName = item.GetItemName();
                    npcInfo.level = item.GetItemLevel();
                    npcInfo.tokenIcon = info.coinIcon;
                    if (go.GetComponentInChildren<SpriteRenderer>()) {
                        npcInfo.icon = go.GetComponentInChildren<SpriteRenderer>().sprite;
                    }
                    info.npcs.Add(npcInfo);
                }
            }
        };
        List<string> paths = new List<string>() {
            "Assets/RGPrefab/Pet/Mercenary",
            "Assets/RGPrefab/LevelObject/Event",
            "Assets/RGPrefab/LevelObject/Hall",
            "Assets/RGPrefab/LevelObject/Item",
            "Assets/RGPrefab/LevelObject/Statue",
            "Assets/ModZombie/Prefabs",
        };
        foreach (var path in paths) {
            ScanPath(path, updateInfo);
        }
    }
    public static void ScanPath(string path, Action<GameObject> action) {
        if (action != null) {
            var files = Directory.GetFiles(path);
            foreach (var f in files) {
                if (f.EndsWith(".prefab")) {
                    var go = AssetDatabase.LoadAssetAtPath<GameObject>(f);
                    if (go) {
                        action(go);
                    }
                }
            }
        }
    }

#endif
}
