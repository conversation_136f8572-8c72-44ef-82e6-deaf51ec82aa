using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Sirenix.OdinInspector;

[System.Serializable]
public struct ToturialInfo {
    public string eventType;
    public List<string> param_list;

    // public string call_method;//调用的函数
    public List<TriggerInfo> trigger_list;
    public bool is_complete;

}

[System.Serializable]
public struct TriggerInfo {
    public string trigger_method;
    public List<string> param_list;
}

