using NewDynamicConfig;
using Newtonsoft.Json;
using RGScript.Util;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.U2D;

namespace RGScript.Data {
    /// <summary>
    /// 元气手册 荣誉称号
    /// </summary>
    public class HonoraryTitleData : BaseData {
        public Dictionary<int, cfg.progress.HonoraryTitle> DataMap =>
            DataMgr.ConfigData.Tables.TbHonoraryTitle.DataMap;

        public List<cfg.progress.HonoraryTitle> DataList =>
            DataMgr.ConfigData.Tables.TbHonoraryTitle.DataList;

        private Dictionary<int, HonoraryTitleProgress> progressDict;
        private SpriteAtlas _honoraryTitleAtlas;

        public override void Clear() {
            base.Clear();
        }

        public HonoraryTitleData() {
            InitData();
        }

        public void Reload() {
            HonoraryTitle.CalculateAll();
            progressDict = HonoraryTitle.GetAllHonoraryTitle();
        }

        public HonoraryTitleProgress GetProgress(int key) {
            if (progressDict == null) {
                Reload();
            }

            return progressDict[key];
        }


        /// <summary>
        /// 获取荣耀称号的图标
        /// </summary>
        /// <param name="titleIndex">配置表中的id</param>
        /// <param name="level"></param>
        /// <returns></returns>
        public Sprite GetHonoraryTitleSprite(int titleIndex, HonoraryTitleLevel level) {
            if (_honoraryTitleAtlas == null) {
                _honoraryTitleAtlas =
                    ResourcesUtil.Load<SpriteAtlas>("RGTexture/sprite_atlas/ui/honorary_title.spriteatlasv2");
            }

            var levelStr = level.ToString().ToLower();
            return _honoraryTitleAtlas.GetSprite($"honorary_title_{titleIndex}_{levelStr}");
        }

        private void InitData() {
        }

        public static string PlayerPrefKey = "has_enter_hb_honorary_title";

        public bool EntryShowRedDot() {
            bool hasEnterEntry = PlayerPrefs.GetInt(PlayerPrefKey, 0) == 0;
            bool hasUpdate = HonoraryTitle.IsHonoraryTitleNeedRedDot();
            return hasEnterEntry || hasUpdate;
        }
    }
}