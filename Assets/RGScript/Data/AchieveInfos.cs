using cfg.achievement;
using I2.Loc;
using RGScript.Util;
using Sirenix.OdinInspector;
using SoulKnight.Runtime.Achievement;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using RGScript.Other;

namespace RGScript.Data {
    public class AchieveInfos {
        public enum AchievementType {
            None = 0, //默认直接解锁
            KillCount = 1, //杀敌数
            PassCount = 2, //普通和吊炸天通用
            PassCount1 = 3, //普通过关次数
            PassCount2 = 4, //吊炸天过关次数
            RewardTaskCount = 5, //悬赏任务次数
            ArrowCount = 6, //一只怪物身上箭数量
            OneGameCoinCount = 7, //一次冒险获得的金币数
            SpecialWeaponKillCount = 8, //特殊武器杀敌数量
            TimePass = 9, //通关时间
            SkillUseCount = 10, //技能使用次数
            BoomKillCount = 11, //炸弹杀敌数
            SpecialWeaponPass = 12, //携带浇花水壶通关
            GetSpecialWeapon = 13, //获取对应的道具
            WatchTV5Min = 14, //站电视前电视5分钟
            BeHitCountLess = 15, //受击次数小于
            KillCountWithSpecialEnemy = 16, //击杀指定的怪物数量
            UnlockMountPig = 17, //解锁暴走野猪
            UnlockMountWorm = 18, //解锁水晶虫
            UnlockMountSpider = 19, //解锁蜘蛛
            UnlockMountWakeen = 20, //解锁瓦克恩
            TimePass1 = 21, //普通通关时间
            TimePass2 = 22, //吊炸天通关时间
            TimePass3 = 23, //挑战通关时间

            UnlockC08MasterSkin = 24, //炼金大师皮肤解锁
            UnlockC14MasterSkin = 25, //狂战士大师皮肤解锁
            UnlockC13MasterSkin = 26, //机器大师皮肤解锁
            SpecialWeaponNamePass = 27, //指定武器通过普通模式
            SpecialWeaponNamePassDZT = 28, //指定武器通过吊炸天模式
            PassCountBossrush = 29, //boss rush通过次数

            PassCountBossrushDZT = 30, //boss rush DZT通过次数

            TimePassBossrush = 31, //boss rush通关时间

            TimePassBossrushDZT = 32, //boss rush吊炸天通关时间

            CCleaner = 33, //环保卫士
            UnlockC08Skin8 = 35, //解锁圣骑士极道皮肤

            DefencePassLevel = 36, //守护神殿模式
            Boss24NodeCount = 37, // 击败冰虫时冰虫的长度
            TapeCollect = 38, // 收集磁带
            VoidIceNail = 39, // 没有被冰刺砸中
            WeaponSacrifice = 40, // 献祭红武
            RelicPuzzleComplete = 41, // 墓穴入口解密
            RelicOnePunchOpen = 42, // 墓穴入口一拳开门
            VoidRelicFall = 43, // 墓穴没有掉坑
            AllHiddenLevel = 44, // 通关所有隐藏关
            SaveOtherPlayer = 45,//拯救其他玩家
            PlayerDeadCount = 46,//死亡总数
            PlayerRebornVal = 47,//是否有下一关然后复活
            MultiPassDZTCount = 48,//多人吊炸天数量

            Buffed = 49, //获得过20个不同的天赋
            EmbraceChallenges = 50, //携带超过30个不同挑战因子通关关卡模式
            Surprise = 51, //抓抓乐里抽中大宝贝
            CherryBomb = 52, //海岛关卡头目累计被自己的炸弹造成伤害10次
            UnlockHero = 53, //解锁角色

            Fish = 54, // 钓到鱼
            FishTrash = 55, // 钓到垃圾

            DefenceFinalWeapon = 56, //守护神殿中，强化出+15红武
            DefenceMiner = 57, //守护神殿中，采完所有金矿
            DefenceMaster = 58, //守护神殿中，每一种防御塔都进阶过最高品质，升级过最高星级
            AlienCarrierKiller = 59, //守护神殿中，击败外星母舰
            AlienCarrierKillerBadass = 60, //守护神殿吊炸天模式中，击败外星母舰
            InvisibleBossKiller = 61, //守护神殿中，击败一个携带隐身基因的领主

            OnePetFullIntimacy = 62, // 和一只宠物达成满亲密度
            TwoCatFullIntimacy = 63, // 和2只猫达成满亲密度
            TwoDogFullIntimacy = 64, // 和2只狗达成满亲密度
            TenPetFullIntimacy = 65, // 和10只宠物达成满亲密度
            PassWithWaterMonkeyItem = 66, // 佩戴水猴子配件通关

            CompleteWeeklyFactorTypes = 67, // 完成的周挑战不同种类
            ForgeDustWeapons = 68, // 打造尘封武器

            KillAirbender = 69,
            ConvoyTaro = 70,
            LoopTravelPressureLv = 71,
            LoopTravelSaveWorld = 72,
            LoopTravelRunWithTime = 73,

            EmojiKing = 74,
            EnvironmentDefender = 75, //环保卫士2 使用气宗清理快递盒
            WithOutWeaponPassBossRushDzt = 76, //不使用武器通关试炼之地吊炸天
            WithOutWeaponPassBossRush = 77, //不使用武器通关试炼之地
            GetNoDamageInBossRushRoom = 78, //在试炼之地不掉盾掉血击杀任意一只boss
            GetNoDamageInBossRushGame = 79, //在试炼之地不掉血掉盾击杀所有boss
            MultiGameMostValuablePlayer = 80, //联机模式不倒地并最终通关 
            MultiGameBossKiller = 81, //联机模式下累计击杀超过20个boss

            //2023暑假7月版本
            UnlockEnoughMythicWeapon = 82, //累计解锁8把神话武器
            FirstEnterLevel4 = 83, //第一次进入关卡模式的第四关
            LoveShopping = 84, //在商城单次停留30秒以上
            HurricaneOperator = 85, //使用飓风拳套操作第四关飓风的位移，持续5秒
            BraisedSquidTentacles = 86, //关卡模式中单局内使用火元素伤害击败“触手怪”40次
            FirstEnterHiddenRoom = 87, //在关卡模式中进入一次四大关的隐藏房间
            GiantSlayer = 88, //关卡模式中分别击败以下巨型头目一次，巨人，远古巨像，钢铁破浪者号，石械·岩祖
            WaterTankerTruck = 89, //携带武器“水壶”情况下使用矿工2技能“矿车速递”在关卡模式中，一次技能持续时间内清理房间内所有怪物

            //2023暑假8月版本
            BuyGood30Times = 90, // 在商城累计购物满30次（包括商城里的小鱼干商店，赛季商店，包括免费商品，排除看视频获取蓝币这种）
            SkilledButcher = 91,//使用武器”杀猪刀“击败一头石械·牛
            TrialRoomEndDifficult3 = 92,//在关卡模式第四大关的隐藏试炼房间，成功通过任意1次难度等级3的挑战。

            //2024新年1月版本
            HappyNewYear = 93, //新年快乐:使用新年礼物连续4次开出新年礼物
            EnvironmentDefender3 = 94, //环保卫士3:关闭二楼厨房水龙头
            CarefulForCold = 95, //小心感冒:二楼14度连续待5分钟
            HurryMan = 96,//赶路达人：一局单人游戏中，战斗结束后3秒内赶到下一个房间，累计触发5次。
            MasterFight = 97, //宗师对决
            GunStoneDragonSlayer = 98, //石龙炮击杀石龙
            //2024新年1月修复版本
            Thunderstorm = 99, //雷暴 使用武器风眼将闪电施加到领主的战意风暴中

            // 2024年4月版本
            WithOutWeaponDefeatBoss = 100, //不使用任何武器击Boss

            // 2024年7月版本 
            QiFortuneFormula = 101,//秘技·气运诀 (成就号118)
            CapsuleToyEnthusiast = 102,//扭蛋爱好者 119
            SweepTheArmy = 103,//横扫千军 120
            MysticCeremony = 104,//神秘仪式 121
            FusionEvolution = 105,//融合进化，地牢爆炸！ 122
            HardControlMaster = 106, //硬控大师 123
            BestEmployer = 107,//最佳雇主 124
            GallopingOnHorseback = 108, //策马奔腾 125
            StrengthInNumbers = 109,//人多力量大 126
            ShrimpHead = 110,//不是,哥们(道士皮肤) 127

            // 2025年春节版本 
            SeabedOxygenEqualZero = 111, //氧气剩余量 128
            SeabedOxygenGreaterZero = 112, //氧气剩余量 129
            SingerBuffFriend = 113, //诗人激励 130
            DamageOverHundred = 114, //在关卡模式中，1次攻击造成伤害超过100点。131
            TaskComplete4 = 115, //完成骑士之路前4章所有任务。132
            C31DiedWhenSkill0 = 116, //使用领主角色，释放技能1过程中被冲锋敌人击败。133
            
            //2025年8月版本
            PlantDiversity = 117, // 134五花八门  种植5种不同的植物
            IronPalm = 118, // 135铁砂掌  通关火山场景大关未受到岩浆地板伤害
            EatHammer = 119, // 136吃个锤子   给花园里的蹦蹦喂锤子
            WeaponCollector = 120, // 137武器收藏家  与武器收藏家击败10次首领
            MakeInSoulKnight = 121, // 138元气智造   锻造50次武器
            BasicAttackGod = 122, // 139普攻的神  全程不使用技能通关关卡模式-炸天
        }

        [Serializable]
        public class UnlockCondition {
            public AchievementType achievement_type;
            public int target_int;
            public string target_str;
            public emHero target_hero;
            [NonSerialized]
            internal bool is_enough; //是否满足条件
        }

        [Serializable]
        public class AchievementInfo {
            public int id; //唯一不可重复
            public string name;
            [PreviewField]
            public Sprite icon; //成就图标
            public bool is_hide;
            [Obsolete("AchievementInfo.unlock is deprecated, please use AchieveInfo.unlock instead.")]
            public bool unlock; //成就是否解锁

            public List<AchievementAward> awards = new();
            public bool got_award; //是否可以领取奖励
            public int unlock_time; //成就解锁时的时间戳
            public List<UnlockCondition> unlock_condis = new();
            public bool series; //系列成就

            public List<emGameMode> unlock_gamemode = new();
            public List<emGameType> unlock_gametype = new();

            private AchievementConfig Config => AchievementConfigLoader.GetAchievementConfig(id);

            public bool CheckAllConditions() {
                return unlock_condis.All(condition => condition.is_enough);
            }

            public string GetLocalizedName() {
                return ScriptLocalization.Get(Config.NameKey);
            }

            public string GetLocalizedDesc() {
                return is_hide ? ScriptLocalization.Get("ac/desc_hide") : ScriptLocalization.Get(Config.DescKey);
            }

#if UNITY_EDITOR
            [Button(Name = "Unlock")]
            public void TestUnlock() {
                foreach (UnlockCondition unlockCondition in this.unlock_condis) {
                    unlockCondition.is_enough = true;
                }
                info.CheckUnlock(unlock_condis[0].achievement_type, 0, "", emHero.None, true);
            }

            [Button(Name = "Lock")]
            public void TestLock() {
                foreach (UnlockCondition unlockCondition in this.unlock_condis) {
                    unlockCondition.is_enough = false;
                }
                got_award = false;
                StatisticData.data.UpdateAchievementRecord(id, this);
                StatisticData.data.GetAchievementRecordById(id).unlock = false;
                StatisticData.data.GetAchievementRecordById(id).got_award = false;
                StatisticData.data.GetAchievementRecordById(id).has_show = false;
                StatisticData.Save();
            }
#endif
        }

        public List<AchievementInfo> achievement_Infos;
        public Dictionary<int, AchievementInfo> achieve_dic = new();
        Dictionary<AchievementType, List<AchievementInfo>> achieve_type_dic = new();
        private static AchieveInfos _info;
        public static AchieveInfos info {
            get {
                if (null != _info) {
                    return _info;
                }

                _info = new AchieveInfos();
                NewCloudSaveAgent.BeforeDownloadGameAction += success => {
                    // 避免内存中缓存了数据
                    if (!success) {
                        _info.Init();
                    }
                };

                _info.Init();
                return _info;
            }
        }

        private static AchievementInfo AchievementConfigToInfo(AchievementConfig config) {
            var achievementInfo = new AchievementInfo {
                id = config.Id,
                icon = config.Icon.GetSprite(),
                is_hide = config.IsHide,
                series = config.Series
            };

            foreach (var condition in config.UnlockConditionList) {
                achievementInfo.unlock_condis.Add(new UnlockCondition {
                    achievement_type = condition.achievementType,
                    target_int = condition.targetInt,
                    target_str = condition.targetStr,
                    target_hero = condition.targetHero
                });
            }

            achievementInfo.awards.AddRange(config.AwardList);
            achievementInfo.unlock_gamemode.AddRange(config.UnlockGameMode);
            achievementInfo.unlock_gametype.AddRange(config.UnlockGameType);

            return achievementInfo;
        }

        //初始化
        private void Init() {
            achievement_Infos = new List<AchievementInfo>();

            var allConfig = AchievementConfigLoader.GetAllAchievementConfig();
            foreach (var pair in allConfig) {
                achievement_Infos.Add(AchievementConfigToInfo(pair.Value));
            }

            foreach (AchievementInfo val in achievement_Infos) {
                int key = val.id;
                achieve_dic[key] = val;
                foreach (UnlockCondition condition in val.unlock_condis) {
                    if (!achieve_type_dic.ContainsKey(condition.achievement_type)) {
                        achieve_type_dic[condition.achievement_type] = new List<AchievementInfo>();
                    }

                    List<AchievementInfo> list = achieve_type_dic[condition.achievement_type];
                    if (!list.Contains(val)) {
                        list.Add(val);
                    }
                }
            }
        }

        public AchievementInfo GetAchievementInfoById(int id) {
            return achieve_dic.TryGetValue(id, out AchievementInfo achievementInfo) ? achievementInfo : null;
        }

        /// <summary>
        /// 获取对应类型的成就
        /// </summary>
        /// <returns>The achievements by type.</returns>
        /// <param name="achievement_type">Achievement type.</param>
        public List<AchievementInfo> GetAchievementsByType(AchievementType achievement_type) {
            List<AchievementInfo> result = null;
            if (achieve_type_dic.TryGetValue(achievement_type, out List<AchievementInfo> value)) {
                result = value;
            }
            return result;
        }


        public AchievementInfo CheckUnlock( AchievementType achType, long paramInt, string paramStr, 
            emHero heroType, bool fort = false) {
            return CheckUnlock(BattleData.data.gameMode, BattleData.data.gametype, 
                achType, paramInt, paramStr, heroType, fort);
        }

        //检查对应类型的成就是否解锁
        public AchievementInfo CheckUnlock(emGameMode gameMode, emGameType gameType,
            AchievementType achType, long paramInt, string paramStr, emHero heroType, bool fort = false) {
            List<AchievementInfo> achievementList = GetAchievementsByType(achType);
            if (null == achievementList) {
                return null;
            }

            foreach (AchievementInfo achievement in achievementList) {
                AchieveInfo acInfo = StatisticData.data.GetAchievementRecordById(achievement.id);
                if (acInfo.unlock == false) {
                    //不包含可解锁模式则跳过解锁
                    // ReSharper disable once ConvertIfStatementToSwitchStatement
                    if (!fort &&
                        null != achievement.unlock_gamemode &&
                        0 != achievement.unlock_gamemode.Count &&
                        !achievement.unlock_gamemode.Contains(gameMode)) {
                        continue;
                    }

                    if (!fort && null != achievement.unlock_gametype &&
                        0 != achievement.unlock_gametype.Count &&
                        !achievement.unlock_gametype.Contains(gameType)) {
                        continue;
                    }

                    List<UnlockCondition> conditionList = achievement.unlock_condis;
                    if (null == conditionList) {
                        continue;
                    }

                    // ReSharper disable once ForeachCanBePartlyConvertedToQueryUsingAnotherGetEnumerator
                    foreach (UnlockCondition unlockCondition in conditionList) {
                        if (unlockCondition.achievement_type != achType) {
                            continue;
                        }

                        switch (achType) {
                            case AchievementType.None:
                                if (paramStr == unlockCondition.target_str || fort) {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            case AchievementType.KillCount: //杀敌数
                            case AchievementType.PassCount: //普通和吊炸天通用
                            case AchievementType.PassCount1: //普通过关次数
                            case AchievementType.PassCount2: //吊炸天过关次数
                            case AchievementType.RewardTaskCount: //悬赏任务次数
                            case AchievementType.ArrowCount: //一只怪物身上箭数量
                            case AchievementType.OneGameCoinCount: //一次冒险获得的金币数
                            case AchievementType.PassCountBossrush: //通关一次boss rush
                            case AchievementType.PassCountBossrushDZT: //通关一次DZTboss rush
                            case AchievementType.CCleaner: //环保卫士
                            case AchievementType.WeaponSacrifice: // 沼泽入口献祭武器
                            case AchievementType.RelicPuzzleComplete: // 墓穴入口解密
                            case AchievementType.RelicOnePunchOpen: // 墓穴入口解密
                            case AchievementType.AllHiddenLevel: // 通关所有隐藏关
                            case AchievementType.SaveOtherPlayer://拯救其他玩家
                            case AchievementType.PlayerDeadCount://死亡总数
                            case AchievementType.MultiPassDZTCount://多人DZT通关数
                            case AchievementType.Buffed:
                            case AchievementType.EmbraceChallenges:
                            case AchievementType.Surprise:
                            case AchievementType.CherryBomb:
                            case AchievementType.DefenceFinalWeapon:
                            case AchievementType.AlienCarrierKiller:
                            case AchievementType.AlienCarrierKillerBadass:
                            case AchievementType.InvisibleBossKiller:
                            case AchievementType.KillAirbender:
                            case AchievementType.ConvoyTaro:
                            case AchievementType.LoopTravelPressureLv:
                            case AchievementType.LoopTravelSaveWorld:
                            case AchievementType.LoopTravelRunWithTime:
                            case AchievementType.UnlockEnoughMythicWeapon:
                            case AchievementType.BraisedSquidTentacles:
                            case AchievementType.BuyGood30Times:
                                if (paramInt >= unlockCondition.target_int || fort) {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            case AchievementType.SpecialWeaponKillCount: //特殊武器杀敌数量
                                if (paramInt >= unlockCondition.target_int || fort) {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            case AchievementType.TimePass: //通关时间
                            case AchievementType.TimePass1:
                            case AchievementType.TimePass2:
                            case AchievementType.TimePass3:
                            case AchievementType.TimePassBossrush:
                            case AchievementType.TimePassBossrushDZT:
                                if (paramInt < unlockCondition.target_int || fort) {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            case AchievementType.SkillUseCount: //技能使用次数
                                if (fort || (heroType >= unlockCondition.target_hero &&
                                             paramInt >= unlockCondition.target_int)) {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            case AchievementType.BoomKillCount: //炸弹杀敌数
                                if (fort || paramInt >= unlockCondition.target_int) {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            case AchievementType.SpecialWeaponPass: //携带特定武器通关
                                if (fort) {
                                    unlockCondition.is_enough = true;
                                }
                                GameObject weapon = ResourcesUtil.LoadWeapon(paramStr);
                                if (null != weapon) {
                                    WaterWeaponFlag flag = weapon.GetComponent<WaterWeaponFlag>();
                                    if (null != flag) {
                                        unlockCondition.is_enough = true;
                                    }
                                }
                                break;
                            case AchievementType.GetSpecialWeapon: //获取对应的道具
                                if (fort || paramStr == unlockCondition.target_str) {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            case AchievementType.WatchTV5Min: //站电视前电视5分钟
                                if (fort || paramInt >= unlockCondition.target_int) //单位毫秒
                                {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            case AchievementType.BeHitCountLess: //过关受击次数不超过指定值
                                if (fort || paramInt <= unlockCondition.target_int) {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            case AchievementType.KillCountWithSpecialEnemy: //击杀指定的怪物数量
                                if (fort || (paramStr == unlockCondition.target_str &&
                                             paramInt >= unlockCondition.target_int)) {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            case AchievementType.UnlockMountPig:
                                if (fort || (paramStr == unlockCondition.target_str &&
                                             paramInt >= unlockCondition.target_int)) {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            case AchievementType.UnlockMountWorm:
                                if (fort || (paramStr == unlockCondition.target_str &&
                                             paramInt >= unlockCondition.target_int)) {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            case AchievementType.UnlockMountSpider:
                                if (fort || (paramStr == unlockCondition.target_str &&
                                             paramInt >= unlockCondition.target_int)) {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            case AchievementType.UnlockMountWakeen:
                                if (fort || (paramStr == unlockCondition.target_str &&
                                             paramInt >= unlockCondition.target_int)) {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            case AchievementType.UnlockC08MasterSkin:
                                if (fort || (heroType == unlockCondition.target_hero &&
                                             paramInt >= unlockCondition.target_int)) {
                                    unlockCondition.is_enough = true;
                                    UnlockC08MasterSkin();
                                }
                                break;
                            case AchievementType.UnlockC13MasterSkin:
                                if (fort || (heroType == unlockCondition.target_hero &&
                                             paramInt <= unlockCondition.target_int)) {
                                    unlockCondition.is_enough = true;
                                    UnlockC13MasterSkin();
                                }
                                break;
                            case AchievementType.UnlockC14MasterSkin:
                                if (fort || (heroType == unlockCondition.target_hero &&
                                             paramInt <= unlockCondition.target_int && paramInt > 0)) {
                                    unlockCondition.is_enough = true;
                                    UnlockC14MasterSkin();
                                }
                                break;
                            case AchievementType.UnlockC08Skin8:
                                if (fort || (heroType == unlockCondition.target_hero)) {
                                    unlockCondition.is_enough = true;
                                    this.UnlockC08Skin8JiDaoSkin();
                                }
                                break;
                            case AchievementType.SpecialWeaponNamePass:
                            case AchievementType.SpecialWeaponNamePassDZT:
                                if (fort || (paramStr == unlockCondition.target_str)) {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            case AchievementType.DefencePassLevel:
                                if (fort || (paramInt >= unlockCondition.target_int)) {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            case AchievementType.Boss24NodeCount:
                            case AchievementType.TapeCollect:
                                if (fort || paramInt >= unlockCondition.target_int) {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            case AchievementType.VoidIceNail:
                            case AchievementType.VoidRelicFall:
                            case AchievementType.TrialRoomEndDifficult3:
                                if (fort || paramInt == unlockCondition.target_int) {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            case AchievementType.PlayerRebornVal:
                                if (fort || paramInt != unlockCondition.target_int) {
                                    unlockCondition.is_enough = true;
                                }

                                break;
                            case AchievementType.UnlockHero:
                                if (fort || heroType == unlockCondition.target_hero) {
                                    unlockCondition.is_enough = true;
                                }

                                break;
                            case AchievementType.Fish:
                            case AchievementType.FishTrash:
                                if (fort || paramInt >= unlockCondition.target_int) unlockCondition.is_enough = true;
                                break;
                            case AchievementType.DefenceMiner:
                                if (fort || paramInt <= unlockCondition.target_int) {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            case AchievementType.DefenceMaster:
                                if (fort || paramInt >= unlockCondition.target_int) {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            case AchievementType.OnePetFullIntimacy: // 一只宠物达成满亲密度
                            case AchievementType.TwoCatFullIntimacy: // 2只猫达成满亲密度
                            case AchievementType.TwoDogFullIntimacy: // 2只狗达成满亲密度
                            case AchievementType.TenPetFullIntimacy: // 10只宠物达成满亲密度
                            case AchievementType.PassWithWaterMonkeyItem: // 携带水猴子配件通关
                                if (fort || paramInt >= unlockCondition.target_int) {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            case AchievementType.CompleteWeeklyFactorTypes: // 周任务完成类型
                                var weeklyFactorTypeCount = 0;
                                for (var i = (int)emBattleFactor.WeeklyMissionFactorBegin;
                                    i < (int)emBattleFactor.WeeklyMissionFactorEnd;
                                    ++i) {
                                    var factor = (emBattleFactor)i;
                                    if (StatisticData.data.GetEventCount(
                                           RGGameConst.WEEKLY_FACTOR_COMPLETE_TYPE + factor) > 0) {
                                        ++weeklyFactorTypeCount;
                                    }
                                }
                                if (fort || unlockCondition.target_int <= weeklyFactorTypeCount)
                                    unlockCondition.is_enough = true;
                                break;
                            case AchievementType.ForgeDustWeapons: // 尘封武器
                                var weaponDust = new List<Tuple<string, string>> {
                                    Tuple.Create("weapon_348", "weapon_349"),
                                    Tuple.Create("weapon_350", "weapon_351"),
                                    Tuple.Create("weapon_352", "weapon_353")
                                };
                                int count = 0;
                                // ReSharper disable once ForeachCanBeConvertedToQueryUsingAnotherGetEnumerator
                                foreach (var pair in weaponDust) {
                                    bool ok =
                                        StatisticData.data.GetObtainTime(pair.Item2) >=
                                        UIForge.UnlockCountDict[2];
                                    if (ok) {
                                        count++;
                                    }
                                }

                                unlockCondition.is_enough = count >= paramInt;
                                break;
                            case AchievementType.EmojiKing:
                                if (StatisticData.data.CheckIfUsedAllEmoji()) {
                                    unlockCondition.is_enough = true;
                                    UnlockC02EmojiSkin();
                                }
                                break;
                            case AchievementType.EnvironmentDefender:
                            case AchievementType.FirstEnterLevel4:
                            case AchievementType.LoveShopping:
                            case AchievementType.HurricaneOperator:
                            case AchievementType.FirstEnterHiddenRoom:
                            case AchievementType.GiantSlayer:
                            case AchievementType.WaterTankerTruck:
                            case AchievementType.SkilledButcher:
                            case AchievementType.HappyNewYear:
                            case AchievementType.EnvironmentDefender3:
                            case AchievementType.CarefulForCold:
                            case AchievementType.HurryMan:
                            case AchievementType.MasterFight:
                            case AchievementType.GunStoneDragonSlayer:
                            case AchievementType.Thunderstorm:
                            case AchievementType.QiFortuneFormula:
                            case AchievementType.CapsuleToyEnthusiast:
                            case AchievementType.SweepTheArmy:
                            case AchievementType.MysticCeremony:
                            case AchievementType.FusionEvolution:
                            case AchievementType.HardControlMaster:
                            case AchievementType.BestEmployer:
                            case AchievementType.GallopingOnHorseback:
                            case AchievementType.StrengthInNumbers:
                            case AchievementType.ShrimpHead:
                                unlockCondition.is_enough = true;
                                break;
                            case AchievementType.SeabedOxygenEqualZero:
                            case AchievementType.SeabedOxygenGreaterZero:
                            case AchievementType.SingerBuffFriend:
                            case AchievementType.PlantDiversity:
                            case AchievementType.IronPalm:
                            case AchievementType.EatHammer:
                            case AchievementType.WeaponCollector:
                            case AchievementType.MakeInSoulKnight:
                            case AchievementType.BasicAttackGod:
                                if (paramInt >= unlockCondition.target_int || fort) {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            case AchievementType.DamageOverHundred:
                            case AchievementType.TaskComplete4:
                            case AchievementType.C31DiedWhenSkill0:
                            case AchievementType.WithOutWeaponPassBossRush:
                            case AchievementType.WithOutWeaponPassBossRushDzt:
                                if (fort || (paramStr == unlockCondition.target_str)) {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            case AchievementType.GetNoDamageInBossRushRoom:
                            case AchievementType.GetNoDamageInBossRushGame:
                            case AchievementType.MultiGameMostValuablePlayer:
                            case AchievementType.MultiGameBossKiller:
                                if (fort) {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            case AchievementType.WithOutWeaponDefeatBoss:
                                if (fort || paramStr == unlockCondition.target_str) {
                                    unlockCondition.is_enough = true;
                                }
                                break;
                            default:
                                throw new ArgumentOutOfRangeException(nameof(achType), achType, null);
                        }

                        if (!unlockCondition.is_enough) {
                            continue;
                        }

                        bool unlock = achievement.CheckAllConditions();
                        if (!unlock) {
                            continue;
                        }

                        if (LogUtil.IsShowLog) { LogUtil.Log("成功解锁成就：" + achievement.name); }
                        acInfo.unlock = true;
                        try {
                            SimpleEventManager.Raise(UpdateAchievementRecordEvent.UseCache(achievement.id));
                        } catch (Exception e) {
                            BuglyUtil.ReportException("UpdateAchievementRecordEvent", e);
                        }

                        StatisticData.data.UpdateAchievementRecord(achievement.id, achievement);
                        StatisticData.Save();
                        UIWindowAchievementTips.ShowWindow(achievement.id);
                        AchievementExhibition.UpdateAchieveExhById(achievement.id);
                        //添加成就的邮件
                        CreateAchievementEmail(achievement);
                        StatisticAchievement(achievement);
                        if (!achievement.series) {
                            return achievement;
                        }
                        if (fort) {
                            return achievement;
                        }
                    }
                } else {
                    //兼容回档皮肤不在的情况
                    List<UnlockCondition> conditionList = achievement.unlock_condis;
                    if (null == conditionList) {
                        continue;
                    }

                    foreach (UnlockCondition unlockCondition in conditionList) {
                        // ReSharper disable once SwitchStatementMissingSomeEnumCasesNoDefault
                        switch (unlockCondition.achievement_type) {
                            case AchievementType.UnlockC08MasterSkin:
                                UnlockC08MasterSkin();
                                break;
                            case AchievementType.UnlockC13MasterSkin:
                                UnlockC13MasterSkin();
                                break;
                            case AchievementType.UnlockC14MasterSkin:
                                UnlockC14MasterSkin();
                                break;
                            case AchievementType.UnlockC08Skin8:
                                UnlockC08Skin8JiDaoSkin();
                                break;
                        }
                    }
                }
            }

            return null;
        }

        #region 邮件相关

        public static void OnEmailsGetAward(BaseEmail[] baseEmail) {
            foreach (var email in baseEmail) {
                OnEmailGetAward(email);
            }
        }

        public static void OnEmailGetAward(BaseEmail baseEmail) {
            if (baseEmail.EmailType != EmailType.AchievementAward) {
                return;
            }

            AchieveInfo achieveInfo = StatisticData.data.GetAchievementRecordById(baseEmail.Uid);
            if (achieveInfo != null) {
                achieveInfo.got_award = true;
                achieveInfo.has_show = true;
                AchievementExhibition.UpdateAchieveExhById(achieveInfo.id);
                StatisticData.Save();
            } else {
                Debug.LogError("get unknown achievement" + baseEmail.Uid);
            }
        }

        private static void CreateAchievementEmail(AchievementInfo achievement) {
            //创建默认邮件
            BaseEmail email = BaseEmail.CreatBaseEmail(achievement);
            MailBoxManager.Instance.AddEmail(email);
            //创建额外邮件
            if (achievement.id == 10) {
                CheckOfficerEmail();
            }

        }

        public static void CheckOfficerEmail() {
            if (!StatisticData.data.GetAchievementRecordById(10).unlock ||
                DataUtil.GetHeroUnlock(emHero.Officer)) {
                return;
            }

            if (MailBoxManager.Instance.HasEmail(BaseEmail.UidOfficerUnlock.ToString())) {//因为警官邮件是特殊处理的本地生成的远端邮件
                var email = MailBoxManager.Instance.GetEmail(BaseEmail.UidOfficerUnlock);
                email.IsAwarded = false;
                StatisticData.Save();
            } else {
                string title = $"<color=#FF0000>{ScriptLocalization.Get("mail/officer_title")}</color>";
                string content = ScriptLocalization.Get("mail/officer_content");
                Dictionary<string, int> reward = new() { { "hero_Officer", 1 } };
                var officerMail = BaseEmail.CreatBaseEmail(title, content, reward);
                officerMail.Sender = NameUtil.GetSkinName(emHero.Officer, 0);
                officerMail.SpriteId = 1;
                officerMail.Uid = BaseEmail.UidOfficerUnlock;
                officerMail.Star = true;
                MailBoxManager.Instance.AddEmail(officerMail);
            }
        }
        #endregion

        #region 解锁大师皮肤

        static void UnlockC08MasterSkin() {
            const int charId = (int)emHero.Paladin;
            const int skinIdx = 6;
            if (!RGSaveManager.Inst.IsHeroSkinUnlock(charId, skinIdx)) {
                RGSaveManager.Inst.HeroSkinUnlock(charId, skinIdx);
            }
        }

        static void UnlockC14MasterSkin() {
            const int charId = (int)emHero.Viking;
            const int skinIdx = 2;
            if (!RGSaveManager.Inst.IsHeroSkinUnlock(charId, skinIdx)) {
                RGSaveManager.Inst.HeroSkinUnlock(charId, skinIdx);
            }
        }

        static void UnlockC13MasterSkin() {
            const int charId = (int)emHero.Robot;
            const int skinIdx = 3;
            if (!RGSaveManager.Inst.IsHeroSkinUnlock(charId, skinIdx)) {
                RGSaveManager.Inst.HeroSkinUnlock(charId, skinIdx);
            }
        }

        #endregion

        #region 表情包皮肤解锁

        static void UnlockC02EmojiSkin() {
            const int charID = (int)emHero.Assassin;
            const int skinIdx = 15;
            if (!RGSaveManager.Inst.IsHeroSkinUnlock(charID, skinIdx)) {
                RGSaveManager.Inst.HeroSkinUnlock(charID, skinIdx);
            }
        }

        #endregion
        [Button(Name = "UnlockC08Skin8JiDaoSkin")]
        // 解锁圣骑士极道皮肤
        public void UnlockC08Skin8JiDaoSkin() {
            const int charID = (int)emHero.Paladin;
            const int skinIdx = 8;
            if (!RGSaveManager.Inst.IsHeroSkinUnlock(charID, skinIdx)) {
                RGSaveManager.Inst.HeroSkinUnlock(charID, skinIdx);
            }
        }

        /// <summary>
        /// 检测鹰类爱好者
        /// </summary>
        public static void CheckUnlockEagleLover() {
            if (
                StatisticData.data.GetObtainTime("weapon_010") > 0 &&
                StatisticData.data.GetObtainTime("weapon_257") > 0 &&
                StatisticData.data.GetObtainTime("weapon_011") > 0 &&
                StatisticData.data.GetObtainTime("weapon_012") > 0 &&
                StatisticData.data.GetObtainTime("weapon_013") > 0 &&
                StatisticData.data.GetObtainTime("weapon_014") > 0 &&
                StatisticData.data.GetObtainTime("weapon_182") > 0 &&
                StatisticData.data.GetObtainTime("weapon_170") > 0 &&
                StatisticData.data.GetObtainTime("weapon_248") > 0 &&
                StatisticData.data.GetObtainTime("weapon_175") > 0
            ) {
                info.CheckUnlock(AchievementType.None, 0, "eagle_lover", emHero.None);
            }
        }

        /// <summary>
        /// 统计成就解锁
        /// </summary>
        private void StatisticAchievement(AchievementInfo achievementInfo) {
            var properties = TAUtil.GetDicGameProcess();
            var id = achievementInfo.id;
            properties.Add("id", id);
            if (DataMgr.AchievementData.achievementDict.TryGetValue(id, out var configData) && configData != null) {
                properties.Add("achievement_category_id", configData.category.Id);
                properties.Add("achievement_group_id", configData.group.Id);
                properties.Add("achievement_point", configData.point);
                properties.Add("achievement_rewards", ConvertItems(achievementInfo.awards));
                properties.Add("achievement_group_rewards", ConvertGroupItems(configData.group.Reward_Ref.RewardList));
                properties.Add("achievement_category_rewards",
                    ConvertCategoryItems(configData.category.Id, configData.category.RewardList));
                var totalPoint = DataMgr.AchievementData.categoryTotalPointDict[configData.category.Id];
                DataMgr.AchievementData.categoryPointsDict.TryGetValue(id, out var point);
                float percentage = ((float)point) / totalPoint;
                percentage = Mathf.Clamp01(percentage);
                properties.Add("achievement_category_progress", percentage);
            } else {
                Debug.LogError("AchievementConfigData is null, id = " + id);
            }
            properties.Add("name", NameUtil.GetAchievementName(achievementInfo.id, true));
            TAUtil.Track("unlock_achievement", properties);
        }

        private static List<object> ConvertGroupItems(List<Reward> rewards) {
            var list = new List<object>();

            // ReSharper disable once ForeachCanBeConvertedToQueryUsingAnotherGetEnumerator
            foreach (var reward in rewards) {
                var dic = new Dictionary<string, object> {
                    { "item_name", reward.ItemName },
                    { "item_count", reward.Count },
                };

                list.Add(dic);
            }

            return list;
        }

        private static List<object> ConvertItems(List<AchievementAward> rewards) {
            var list = new List<object>();

            // ReSharper disable once ForeachCanBeConvertedToQueryUsingAnotherGetEnumerator
            foreach (var reward in rewards) {
                var itemData = reward.itemData;
                if (itemData == null || string.IsNullOrEmpty(itemData.key)) {
                    continue;
                }

                var dic = new Dictionary<string, object> {
                    { "item_name", itemData.key },
                    { "item_count", reward.num }
                };

                list.Add(dic);
            }

            return list;
        }

        private static List<object> ConvertCategoryItems(int categoryId, List<CategoryReward> categoryRewards) {
            var list = new List<object>();

            var totalPoint = DataMgr.AchievementData.categoryTotalPointDict[categoryId];
            foreach (var categoryReward in categoryRewards) {
                var rewardList = new List<object>();

                var dic = new Dictionary<string, object> {
                    { "achievement_category_step", categoryReward.Step / totalPoint },
                    { "achievement_category_step_rewards", rewardList },
                };

                list.Add(dic);

                // ReSharper disable once ForeachCanBeConvertedToQueryUsingAnotherGetEnumerator
                foreach (var reward in categoryReward.Reward_Ref.RewardList) {
                    rewardList.Add($"{reward.ItemName}_{reward.Count}");
                }
            }

            return list;
        }
    }
}