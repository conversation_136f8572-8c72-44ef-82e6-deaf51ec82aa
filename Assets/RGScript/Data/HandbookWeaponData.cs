using Activities;
using I2.Loc;
using RGScript.UI.Handbook;
using RGScript.UI.Handbook.Achievement.Widgets;
using System.Collections.Generic;
using UIFramework;
using UnityEngine;

namespace RGScript.Data {
    public enum ShareItemGoldType {
        Gold,
        Silver,
        Bronze,
        None,
    }

    /// <summary>
    /// 元气手册 武器
    /// </summary>
    public class HandbookWeaponData : BaseData {
        public List<int> goldFrameRankList;
        public List<int> silverFrameRankList;
        public List<int> totalRankList;

        private bool hasUpdateData = false;
        private int findCount = 0;
        private int goldCount = 0;
        private int silverCount = 0;
        private int totalCount = 0;
        public List<string> filteredExhibiteWeaponNames = new List<string>();
        private HashSet<string> _weaponNameSet = new HashSet<string>();

        public override void Clear() {
            base.Clear();
            SimpleEventManager.RemoveListener<SelfEnterHeroRoomEvent>(OnEnterHall);
            SimpleEventManager.RemoveListener<UpgradeHeroInitWeaponEvent>(OnUpgradeHeroInitWeaponEvent);
        }

        public HandbookWeaponData() {
            InitData();
        }

        public void Reload() {
        }

        private void InitData() {
            var map = DataMgr.ConfigData.Tables.TbConstants.DataMap;
            goldFrameRankList = ParseList(map[10001].StringList);
            silverFrameRankList = ParseList(map[10002].StringList);
            totalRankList = ParseList(map[10003].StringList);

            SimpleEventManager.AddEventListener<SelfEnterHeroRoomEvent>(OnEnterHall);
            SimpleEventManager.AddEventListener<UpgradeHeroInitWeaponEvent>(OnUpgradeHeroInitWeaponEvent);
        }

        private void OnUpgradeHeroInitWeaponEvent(UpgradeHeroInitWeaponEvent e) {
            hasUpdateData = false;
        }

        private void OnEnterHall(SelfEnterHeroRoomEvent e) {
            hasUpdateData = false; // 每次进客厅重置
        }

        public void RequestUpdateData() {
            UpdateData();
        }

        private void UpdateData() {
            if (hasUpdateData) {
                return;
            }


            hasUpdateData = true;
            findCount = CellerAisleCreator.GetUnlockedWeaponCount(StatisticData.data);
            goldCount = 0;
            silverCount = 0;
            totalCount = 0;
            _weaponNameSet.Clear();
            filteredExhibiteWeaponNames.Clear();

            var exhibiteWeaponNames = CellerAisleCreator.GetExhibiteWeaponNames(true, true);
            foreach (var weaponName in exhibiteWeaponNames) {
                if (weaponName == RGGameConst.WEAPON_NULL) {
                    continue;
                }

                if (!WeaponInfo.info.name2Weapon.TryGetValue(weaponName, out var info)) {
                    continue;
                }

                if (info == null) {
                    continue;
                }

                if (DataMgr.UnlockConditionData.IsWeaponLockedByConfig(weaponName)) {
                    continue;
                }

                var normalizedWeaponName = RGWeapon.WeaponNameNormalized(weaponName);

                if (_weaponNameSet.Contains(normalizedWeaponName)) {
                    continue;
                }

                filteredExhibiteWeaponNames.Add(normalizedWeaponName);

                _weaponNameSet.Add(normalizedWeaponName);

                totalCount++;
                emPassGameLevel passLevel = GetWeaponPassLevel(normalizedWeaponName);
                if (passLevel >= emPassGameLevel.Normal) {
                    silverCount++;
                }

                if (passLevel >= emPassGameLevel.Badass) {
                    goldCount++;
                }
            }
        }

        public void OpenShareWindow() {
            UpdateData();

            var desc = new UIHBShareWindowDesc {
                widgetDescs = new List<UIHBShareItemWidgetDesc>(),
                title = ScriptLocalization.Get("handbook_weapon_title", "#武器图鉴")
            };

            desc.widgetDescs.Add(new UIHBShareItemWidgetDesc {
                icon = SpriteMap.mallSprite.GetSprite("weapon_share_0"),
                title = ScriptLocalization.Get("handbook_weapon_share_gold", "#金框武器"),
                content = $"{goldCount}/{totalCount}",
                ty = GetGoldType(goldCount, DataMgr.HandbookWeaponData.goldFrameRankList)
            });
            desc.widgetDescs.Add(new UIHBShareItemWidgetDesc {
                icon = SpriteMap.mallSprite.GetSprite("weapon_share_1"),
                title = ScriptLocalization.Get("handbook_weapon_share_sliver", "#银框武器"),
                content = $"{silverCount}/{totalCount}",
                ty = GetGoldType(silverCount, DataMgr.HandbookWeaponData.silverFrameRankList)
            });
            desc.widgetDescs.Add(new UIHBShareItemWidgetDesc {
                icon = SpriteMap.mallSprite.GetSprite("weapon_share_2"),
                title = ScriptLocalization.Get("handbook_weapon_share_find", "#发现武器"),
                content = $"{findCount}/{totalCount}",
                ty = GetGoldType(findCount, DataMgr.HandbookWeaponData.totalRankList)
            });

            UIManager.Inst.OpenUIView<UIHBShareWindow>("Handbook/window_hb_share", new object[] { desc });
            HandbookData.TrackEnterSharePage(HandbookEntryType.Weapon);
        }

        public static emPassGameLevel GetWeaponPassLevel(string weaponName) {
            emPassGameLevel passLevel;
            if (weaponName == "weapon_135") {
                passLevel = (emPassGameLevel)Mathf.Max(
                    (int)StatisticData.data.GetPassLevel("weapon_135x"),
                    (int)StatisticData.data.GetPassLevel("weapon_135"));
            } else if (weaponName.StartsWith("weapon_fish_rod_")) {
                var blueprintStatus = ItemData.data.GetBluePrintStatus("blueprint_" + weaponName);
                passLevel = emPassGameLevel.Not;
                if (blueprintStatus == emBluePrintStatus.Researched) {
                    passLevel = emPassGameLevel.Normal;
                }

                if (StatisticData.data.GetAchievementRecordById(68).unlock) {
                    passLevel = emPassGameLevel.Badass;
                }
            } else {
                var normalizedWeaponName = RGWeapon.WeaponNameNormalized(weaponName);
                passLevel = StatisticData.data.GetPassLevel(normalizedWeaponName);
            }

            return passLevel;
        }

        public static bool IsUnlocked(string weaponName) {
            var obtainCount = StatisticData.data.GetObtainTime(weaponName);
            bool hasObtain = obtainCount > 0;
            if (ActivityUtil.IsMythicWeapon(weaponName)) {
                var mythicWeapon = ItemData.data.GetMythicWeapon(weaponName);
                if (mythicWeapon == null) {
                    return false;
                }

                return mythicWeapon.IsUnlock;
            }

            if (hasObtain) {
                return true;
            }

            var normalCheck = UIForge.IsUnlocked(weaponName);

            return normalCheck;
        }

        public static ShareItemGoldType GetGoldType(int count, List<int> list) {
            if (count >= list[2]) {
                return ShareItemGoldType.Gold;
            }

            if (count >= list[1]) {
                return ShareItemGoldType.Silver;
            }

            if (count >= list[0]) {
                return ShareItemGoldType.Bronze;
            }

            return ShareItemGoldType.None;
        }

        public static List<int> ParseList(List<string> list) {
            if (list == null || list.Count == 0) {
                Debug.LogError("HandbookWeaponData parse list error, null or empty");
                return null;
            }

            List<int> result = new List<int>(3);
            for (int i = 0; i < 3; i++) {
                var str = list[i];
                var num = int.Parse(str);
                result.Add(num);
            }

            return result;
        }

        public static string PlayerPrefKey = "has_enter_hb_weapon";

        public bool EntryShowRedDot() {
            bool hasEnterEntry = PlayerPrefs.GetInt(PlayerPrefKey, 0) == 0;
            return hasEnterEntry;
        }
    }
}