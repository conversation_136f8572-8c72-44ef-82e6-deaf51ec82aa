using ChillyRoom;
using ChillyRoom.IM;
using ChillyRoom.IM.Controller;
using ChillyRoom.IM.DataModel;
using ChillyRoom.IM.V1;
using ChillyRoom.SoulKnight.CloudPackage.V1;
using Extensions.Unity.ImageLoader;
using Generated.ChillyRoomSdkClient;
using I2.Loc;
using IM.Scripts;
using Newtonsoft.Json;
using RGScript.Other;
using RGScript.Other.NewSDK;
using RGScript.Util;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using UIFramework;
using UnityEngine;
using ErrorResponse = ChillyRoom.Services.Core.Errors.ErrorResponse;
using IMConfig = NewDynamicConfig.IMConfig;

namespace RGScript.Data {
    /// <summary>
    /// 聊天数据
    /// 这里可以获得所有的聊天信息
    /// 单个聊天用 IMConversationHandler 来跟踪
    /// </summary>
    public class IMData : BaseData {
        private bool _isInit = false;
        private FriendClient _friendClient;
        public IMClientState ServiceStatus { get; private set; } = IMClientState.Disconnected;
        private IMClientController _playerClient;
        private List<string> _availableProfileIcons;
        private float _nextActionAllowTime;
        public IMConfig Config => ConfigManager.GetCurrectUseConfig<IMConfig>();

        public Dictionary<string, string> Sentences = new Dictionary<string, string>() {
            { "im/preset_0", "你好~" },
            { "im/preset_1", "嗨" },
            { "im/preset_2", "好的！" },
            { "im/preset_3", "你玩的真棒！" },
            { "im/preset_4", "下次吧" },
            { "im/preset_5", "不好意思，正在忙" },
            { "im/preset_6", "一起来玩吧" },
            { "im/preset_7", "有事先走了，再见~" },
            { "im/preset_8", "拜拜" },
            { "im/preset_9", "谢谢你" },
            { "im/preset_10", "选什么角色？" },
            { "im/preset_11", "选几技能？" },
            { "im/preset_12", "稍等" },
            { "im/preset_13", "快点，我们要出发了！" },
        };

        // 多语言服务器下发的预设文本
        private Dictionary<string, string> _presetFromServer = new Dictionary<string, string>();
        private List<string> _oldLocSentences = new List<string>();
        
        public Dictionary<string, string> GetPresets() {
            if (Config.EnableKeyMessage) {
                // 使用服务器下发的多语言本地化来发送key
                return _presetFromServer;
            }

            return Sentences;
        }

        public enum CustomMessageType {
            TeamInvite = 4001,
            Emoji = 4002,
            InviteCode = 4003,
        }

        public IMData() {
            if (!IsOpenForBetaTesting()) { return; }

            ChillyRoomService.GetSdk().OnImSdkConnectSuccess += OnInit;
            ImageLoader.Init();
            
            SimpleEventManager.AddEventListener<H5Fission2025FriendInvitationEvent>(OnH5Fission2025FriendInvitationEvent);
            SimpleEventManager.AddEventListener<ReturnPlayerH5FriendInvitationEvent>(OnReturnPlayerH5FriendInvitationEvent);
            NewCloudSaveAgent.DownLoadGameAction += AfterLoadGameEvent;
        }

        private void AfterLoadGameEvent(bool success, CloudSaveGameData data) {
            if (success && data != null && DataUtil.CurIsTargetScene(RGGameConst.SCENE_TITLE)) {
                // title场景下载存档后刷新下im入口的可见性
                GameObject.Find("Canvas/title_scene/button_group_left/btn_entry_im").SetActive(true);
            }
        }

        private FriendClient GetClient() {
            if (_friendClient == null) {
                _friendClient = GetClientNew();
            }

            return _friendClient;
        }

        public IMClientController PlayerClient => _playerClient;

        public bool IsValidText(string content) {
            return _oldLocSentences.Contains(content);
        }

        /// <summary>
        /// 未注册的新玩家
        /// </summary>
        /// <returns></returns>
        public bool IsNewPlayer() {
            return _playerClient is { State: IMClientState.NotRegistered };
        }
        
        /// <summary>
        /// 是否更新过资料，以此区分新老玩家
        /// </summary>
        /// <returns></returns>
        public bool HasUpdateProfile() {
            var myProfile = GetMyProfile();
            return myProfile.LastProfileUpdate != null;
        }

        private FriendClient GetClientNew() {
            return new FriendClient("",
                Generated.ChillyRoomSdkClient.ChillyRoomService.GetSdk().Core.Client.Client.AsHttpClient());
        }

        public bool IsOpenForBetaTesting() {
#if UNITY_EDITOR
            return true;
#endif

            return ConfigManager.GetCurrectUseConfig<NewDynamicConfig.IMConfig>().EnableIm;
        }

        public async Task Logout() {
            if (!IsOpenForBetaTesting()) { return; }

            Debug.Log($"bbbbbbbbbbbbbbb Logout");

            //等待登录
            while (ServiceStatus == IMClientState.Connecting)
                await Task.Delay(50);
            ServiceStatus = IMClientState.Connecting;

            if (_playerClient != null) {
                _playerClient.OnClientStateChanged -= this.OnClientStateChanged;
                _playerClient = null;
            }

            ServiceStatus = IMClientState.Disconnected;
            SimpleEventManager.RemoveEventListener<SelectHeroEvent>(OnSelectedHero);
            SimpleEventManager.RemoveEventListener<PlayerEnterMultiRoomEvent>(OnEnterMultiRoom);
            NewCloudSaveAgent.DownLoadGameAction -= AfterLoadGameEvent;
        }

        public override void OnAccountLogin(string obj) {
            base.OnAccountLogin(obj);
            Login();
        }

        public override void OnAccountLogout() {
            base.OnAccountLogout();
            Logout();
        }

        public async Task Login() {
            if (!IsOpenForBetaTesting()) return;

            SimpleEventManager.AddEventListener<SelectHeroEvent>(OnSelectedHero);
            SimpleEventManager.AddEventListener<PlayerEnterMultiRoomEvent>(OnEnterMultiRoom);
        }

        private void SetupClientEvents() {
            _playerClient.ContactController.OnContactAdded += contact => {
                Loom.QueueOnMainThread(() => {
                    OnContactAdded(contact);
                });
            };

            _playerClient.ContactController.OnContactChanged += contact => {
                Loom.QueueOnMainThread(() => {
                    OnContactChanged(contact);
                });
            };

            _playerClient.ContactController.OnUserProfileChanged += contact => {
                Loom.QueueOnMainThread(() => {
                    OnUserProfileChanged(contact);
                });
            };

            _playerClient.MessageController.OnConversationUpdated += model => {
                Loom.QueueOnMainThread(() => {
                    OnConversationUpdated(model);
                });
            };
        }

        /// <summary>
        /// IM初始化成功回调
        /// </summary>
        /// <param name="chillyUid"></param>
        private void OnInit(long chillyUid) {
            if (_isInit) return;

            _isInit = true;
            _playerClient = ChillyRoomService.GetSdk().ImClientController;
            _playerClient.OnClientStateChanged += this.OnClientStateChanged;
            OnClientStateChanged(_playerClient.State);
            SetupClientEvents();
            // sdk处已经处理了
            // CleanOldTestData();
            CachePresets();
            InitLocPresets();

            SimpleEventManager.AddEventListener<EnterSceneEvent>(OnChangeScene);
        }

        /// <summary>
        /// 老版本的预设文本非法过滤
        /// </summary>
        private void InitLocPresets() {
            var sentenceDic = DataMgr.IMData.Sentences;
            var locKeys = sentenceDic.Keys.ToArray();
            var languages = LocalizationManager.GetAllLanguages();
            for (var i = 0; i < locKeys.Length; i++) {
                foreach (var language in languages) {
                    var locText = LocalizationManager.GetTermTranslation(locKeys[i], LocalizationManager.IsRight2Left, 0, false, false, null, language).Replace("\\n", "\n");
                    _oldLocSentences.Add(locText);
                }
            }
        }

        private void CachePresets() {
            var presets = _playerClient.KeyedMessages;
            _presetFromServer = new Dictionary<string, string>(presets);
        }

        public string GetLocSentence(string key) {
            if (_presetFromServer.TryGetValue(key, out string value)) {
                return value;
            }

            return null;
        }

        private void OnChangeScene(EnterSceneEvent e) {
            if (_playerClient == null) return;
            if (!FriendUtils.IsImAvailable()) return;
            if (IsNewPlayer()) return;

            var myProfile = GetMyProfile();
            if (myProfile == null) return;
            
            var currStatus = myProfile.MasterStatus;
            var updateStatus = ContactMasterStatus.Offline;
            switch (e.enterScene) {
                case emScene.Game:
                case emScene.Loading:
                    updateStatus = ContactMasterStatus.Busy;
                    break;
                default:
                    updateStatus = ContactMasterStatus.Online;
                    break;
            }

            if (currStatus != updateStatus) {
                if (LogUtil.IsShowLog) {
                    LogUtil.Log($"OnChangeScene scene:{e.enterScene} {currStatus} -> {updateStatus}", "bbbbbbbbb");
                }
                
                UpdateMyMasterStatus(updateStatus);
            }
        }

        private async void CleanOldTestData() {
            // 清除好友之前测试的本地脏数据
            if (!HasCleanUp()) {
                try {
                    var cutoffDate = new System.DateTimeOffset(2024, 10, 1, 0, 0, 0, TimeSpan.Zero);
                    var dbPath = $"{Application.persistentDataPath}/{_playerClient.UserId}/msg.db";
                    var sqLiteAsyncConnection = new SQLite.SQLiteAsyncConnection(dbPath);
                    await sqLiteAsyncConnection.DeleteAllAsync<ContactModel>();
                    await sqLiteAsyncConnection.Table<MessageModel>().Where(m => m.Timestamp < cutoffDate).DeleteAsync();
                    await sqLiteAsyncConnection.CloseAsync();
                    CacheCleanUp();
                } catch (Exception e) {
                    Debug.LogError(e);
                    BuglyUtil.ReportException("im_CleanUp", e);
                }
            }
        }

        private bool HasCleanUp() {
            var cloudCache = StatisticData.data.IsEventRecord("im_clean_record");
            var localCache = PlayerSaveData.GetInt("im_clean_record", 0) == 1;
            return cloudCache || localCache;
        }

        private void CacheCleanUp() {
            StatisticData.data.RecordEvent("im_clean_record", true);
            PlayerSaveData.SetInt("im_clean_record", 1);
        }

        private async void OnClientStateChanged(IMClientState state) {
            var imClientController = _playerClient;
            ServiceStatus = imClientController.State;
            if (ServiceStatus == IMClientState.Connected) {
                OnLoginSuccess();
            } else if (ServiceStatus == IMClientState.NotRegistered) {
                await InitUserNickName(imClientController);
            }

            OnStateChange(state);
        }

        private async Task InitUserNickName(IMClientController imClientController) {
            var avatars = await GetAvailableAvatarUrl();
            if (avatars == null || avatars.Count == 0) {
                Debug.LogError("bbbbbbbbbbbbbbb InitUserNickName avatars is null");
                return;
            }

            var nickname = ScriptLocalization.Get("Character0_name_skin0");
            await CreateProfile(nickname, avatars[0], false);
            await imClientController.Initialize();
        }

        /// <summary>
        /// 玩家当前是否是默认的昵称
        /// </summary>
        public bool ShouldCreateProfile() {
            var imClientController = _playerClient;
            var profile = imClientController.ContactController.GetMyProfile();
            if (string.IsNullOrEmpty(profile.NickName) || profile.NickName == "UID" + imClientController.UserId) {
                return true;
            }

            return false;
        }

        private void OnStateChange(IMClientState state) {
            // 长连接断开
            if (state == IMClientState.Disconnected && _playerClient != null) {
                BuglyUtil.ReportException("im_Disconnected", new Exception(_playerClient.UserId.ToString()));
            }
            SimpleEventManager.Raise(IMStateChangeEvent.UseCache(state));
        }

        private void OnLoginSuccess() {
            if (!IsOpenForBetaTesting()) {
                return;
            }
        }

        private void OnEnterMultiRoom(PlayerEnterMultiRoomEvent e) {
            InitEntryBtn(UICanvas.Inst.transform.Find("info_bar/vertical_bar/btn_im_slot"));
        }

        private void OnSelectedHero(SelectHeroEvent e) {
            if (_playerClient == null) {
                ChillyRoomService.GetSdk().ConnectToMessageBrokerAgain();
            }
            InitEntryBtn(UICanvas.Inst.transform.Find("info_bar/vertical_bar/btn_im_slot"));
        }

        public void InitEntryBtn(Transform parent) {
            try {
                parent.gameObject.SetActive(true);
                var proto = ResourcesUtil.Load<GameObject>("IM/Prefabs/btn_entry_im.prefab");
                var btn = GameObject.Instantiate(proto, parent);
                btn.transform.GetComponent<RectTransform>().localPosition = Vector3.zero;
            } catch (Exception e) {
                Debug.LogError(e);
            }
        }

        private void OnConversationUpdated(ConversationModel model) {
            SimpleEventManager.Raise(new IMConversationUpdatedEvent(model));
        }

        private void OnContactAdded(ContactModel contact) {
            SimpleEventManager.Raise(new IMContactAddedEvent(contact));
        }

        private void OnContactChanged(ContactModel contact) {
            SimpleEventManager.Raise(new IMContactChangedEvent(contact));
        }

        private void OnUserProfileChanged(ContactModel contact) {
            SimpleEventManager.Raise(new IMUserProfileChangedEvent(contact));
        }

        public override void Clear() {
            base.Clear();
            _isInit = false;
            SimpleEventManager.RemoveListener<EnterSceneEvent>(OnChangeScene);
            SimpleEventManager.RemoveEventListener<H5Fission2025FriendInvitationEvent>(OnH5Fission2025FriendInvitationEvent);
            SimpleEventManager.RemoveEventListener<ReturnPlayerH5FriendInvitationEvent>(OnReturnPlayerH5FriendInvitationEvent);
        }

        private void OnH5Fission2025FriendInvitationEvent(H5Fission2025FriendInvitationEvent e) {
            if (!IsOpenForBetaTesting()) {
                return;
            }

            if (!FriendUtils.IsImAvailable()) {
                return;
            }

            if (PlayerClient == null) {
                return;
            }

            var code = e.InvitationCode;
            UIManager.Inst.OpenUIView<UIWindowCopyInviteCode>("IM/Prefabs/window_copy_invite_code.prefab",
                new object[] { code });
        }

        private void OnReturnPlayerH5FriendInvitationEvent(ReturnPlayerH5FriendInvitationEvent e) {
            if (!IsOpenForBetaTesting()) {
                return;
            }

            if (!FriendUtils.IsImAvailable()) {
                return;
            }

            if (PlayerClient == null) {
                return;
            }

            var code = e.InvitationCode;
            UIManager.Inst.OpenUIView<UIWindowCopyInviteCode>("IM/Prefabs/window_copy_invite_code.prefab",
                new object[] { code });
        }

        public class NetPlayerWrapper {
            public string AccountId;
            public emHero hero;
            public int skinId;
        }

        public void RecordNetPlayers(Dictionary<int, string> netUserDataDic,
            Dictionary<int, (emHero, int, bool)> netUserHeroDic) {
            List<NetPlayerWrapper> list = new List<NetPlayerWrapper>();
            foreach (var userData in netUserDataDic) {
                var memberInfoWrapper = JsonConvert.DeserializeObject<MemberInfoWrapper>(userData.Value);
                int netId = userData.Key;
                var heroData = netUserHeroDic[netId];
                list.Add(new NetPlayerWrapper {
                    AccountId = memberInfoWrapper.AccountId,
                    hero = heroData.Item1,
                    skinId = heroData.Item2
                });
            }

            var historyRecords = GetRecentNetPlayers();
            historyRecords.InsertRange(0, list);
            historyRecords = historyRecords
                .Where(p => p.AccountId != NewSDKManager.Inst.GetChillyUidStr())
                .GroupBy(p => p.AccountId)
                .Select(g => g.First())
                .Take(20)
                .ToList();
            PlayerSaveData.SetStringList<NetPlayerWrapper>("IMNetPlayers", historyRecords, JsonConvert.SerializeObject);
        }

        /// <summary>
        /// 获取之前联机过的玩家 userId/AccountId
        /// </summary>
        /// <returns></returns>
        public List<NetPlayerWrapper> GetRecentNetPlayers() {
            List<NetPlayerWrapper> list = new List<NetPlayerWrapper>();
            try {
                list = PlayerSaveData.GetStringList<NetPlayerWrapper>("IMNetPlayers",
                    JsonConvert.DeserializeObject<NetPlayerWrapper>, "");
            } catch (Exception e) {
                Debug.LogError($"bbbbbbbbbbbbbbb GetRecentNetPlayers Error {e}");
                PlayerSaveData.SetStringList<NetPlayerWrapper>("IMNetPlayers", new List<NetPlayerWrapper>(),
                    JsonConvert.SerializeObject);
            }

            return list;
        }

        private bool HasReachedFriendLimit() {
            var currFriendCount = GetFriendCount();
            return currFriendCount >= _playerClient.Settings.MaxFriendCount;
        }
        
        /// <summary>
        /// 是否达到黑名单上限
        /// </summary>
        /// <returns></returns>
        private bool HasReachedBlacklistLimit() {
            var blockings = GetBlockedUsers();
            return blockings.Count >= _playerClient.Settings.MaxBlacklistCount;
        }

        public List<long> GetAllFriendContactsUid() {
            if (!IsOpenForBetaTesting()) {
                return new List<long>();
            }

            return GetAllContacts().Where(model => model.RelationType == UserRelationType.Friend)
                .Select(model => model.UserId).ToList();
        }

        /// <summary>
        /// 更新 profile，头像随便换，昵称七天改一次
        /// 客户端只要在 在线和游戏中 两个状态之间互相切换
        /// </summary>
        /// <param name="nickName"></param>
        public async Task<bool> UpdateMyProfile(string nickName, string avatarUrl) {
            if (IsActionTooFrequent()) { return false; }

            var profile = GetMyProfile();
            if (profile == null) {
                Debug.LogError("bbbbbbbbbbbbbbb UpdateMyProfile profile is null");
                return false;
            }

            if (!string.IsNullOrEmpty(nickName) && nickName != profile.NickName) {
                if (HasReachedProfileNameUpdateThrottle()) {
                    return false;
                }
            }

            var success = false;
            await FriendUtils.ShowWhenError(async () => {
                try {
                    await _playerClient.ContactController.UpdateMyProfile(nickName, avatarUrl);
                    success = true;
                } catch (ChillyRoom.Services.Core.Errors.ApiException<ErrorResponse> ex) {
                    Debug.LogError(ex);
                    if (HandleErrorCode(ex.Result)) {
                        return;
                    }

                    throw;
                }
            });
            return success;
        }

        public async Task<List<string>> GetAvailableAvatarUrl() {
            if (_availableProfileIcons != null)
                return _availableProfileIcons;

            _availableProfileIcons = await _playerClient.ContactController.ListAvailableAvatars();
            return _availableProfileIcons;
        }

        /// <summary>
        /// 请求添加好友
        /// </summary>
        /// <param name="accountId"></param>
        public async Task RequestAddFriend(long accountId, IMTrackFrom from) {
            await FriendUtils.ShowWhenError(async () => {
                try {
                    if (HasReachedFriendLimit()) {
                        FriendUtils.ShowToastOnMain(ScriptLocalization.Get("im/tip/firend_overflow"));
                        return;
                    }

                    var contact = GetContact(accountId);
                    if (contact != null && contact.RelationType == UserRelationType.BlockFriendRequestByPeer) {
                        FriendUtils.ShowToastOnMain(ScriptLocalization.Get("im/tip/already_in_blacklist", "#添加好友失败，该玩家已经在黑名单中，请从黑名单中移除后再添加"));
                        return;
                    }

                    await _playerClient.ContactController.RequestAddFriend(accountId);
                    FriendUtils.TrackRequestFriend(accountId, from);
                } catch (ChillyRoom.Services.Core.Errors.ApiException<ErrorResponse> ex) {
                    Debug.LogError(ex);
                    if (HandleErrorCode(ex.Result)) {
                        return;
                    }

                    throw;
                }
            });
        }

        /// <summary>
        /// 拒绝添加好友
        /// </summary>
        /// <param name="accountId"></param>
        public async Task<bool> DeclineFriendRequest(long accountId) {
            var success = false;
            await FriendUtils.ShowWhenError(async () => {
                try {
                    if (LogUtil.IsShowLog) {
                        LogUtil.Log("[IMData] bbbbbbbbbbbbbbb DeclineFriendRequest");
                    }

                    await _playerClient.ContactController.DeclineFriendRequest(accountId);
                    success = true;
                } catch (ChillyRoom.Services.Core.Errors.ApiException<ErrorResponse> ex) {
                    success = false;
                    Debug.LogError($"bbbbbbbbbbbbbbb DeclineFriendRequest Error {ex.Result.Error}");
                    if (HandleErrorCode(ex.Result)) {
                        return;
                    }

                    throw;
                }
            });

            if (success) {
                // UIPanelDetailInfo.singleton.ShowDetailInfo("Friend_Function_ChangeNameSuccess");
            }

            return success;
        }

        /// <summary>
        /// 接受添加好友请求
        /// </summary>
        /// <param name="accountId"></param>
        public async Task<bool> AcceptFriendRequest(long accountId) {
            var success = false;
            await FriendUtils.ShowWhenError(async () => {
                try {
                    if (LogUtil.IsShowLog) {
                        LogUtil.Log("[IMData] bbbbbbbbbbbbbbb AcceptFriendRequest");
                    }

                    await _playerClient.ContactController.AcceptFriendRequest(accountId);
                    success = true;
                    FriendUtils.TrackAcceptFriend(accountId, IMTrackFrom.Request);
                } catch (ChillyRoom.Services.Core.Errors.ApiException<ErrorResponse> ex) {
                    success = false;
                    Debug.LogError($"bbbbbbbbbbbbbbb AcceptFriendRequest Error {ex.Result.Error}");
                    if (HandleErrorCode(ex.Result)) {
                        return;
                    }

                    throw;
                }
            });
            return success;
        }

        /// <summary>
        /// 拉黑玩家
        /// </summary>
        /// <param name="accountId"></param>
        public async Task<bool> BlockUser(long accountId) {
            var success = false;
            await FriendUtils.ShowWhenError(async () => {
                try {
                    if (LogUtil.IsShowLog) {
                        LogUtil.Log("[IMData] bbbbbbbbbbbbbbb BlockUser");
                    }
                    
                    if (HasReachedBlacklistLimit()) {
                        FriendUtils.ShowToastOnMain(ScriptLocalization.Get("im/tip/blacklist_overflow"));
                        return;
                    }

                    await _playerClient.ContactController.BlockUser(accountId);
                    success = true;
                    FriendUtils.ShowToastOnMain(ScriptLocalization.Get("im/tip/add_blacklist"));
                } catch (ChillyRoom.Services.Core.Errors.ApiException<ErrorResponse> ex) {
                    success = false;
                    Debug.LogError($"bbbbbbbbbbbbbbb BlockUser Error {ex.Result.Error}");
                    if (HandleErrorCode(ex.Result)) {
                        return;
                    }

                    throw;
                }
            });
            return success;
        }

        /// <summary>
        /// 取消拉黑玩家
        /// </summary>
        /// <param name="accountId"></param>
        public async Task<bool> UnblockUser(long accountId) {
            var success = false;
            await FriendUtils.ShowWhenError(async () => {
                try {
                    if (LogUtil.IsShowLog) {
                        LogUtil.Log("[IMData] bbbbbbbbbbbbbbb UnblockUser");
                    }

                    await _playerClient.ContactController.UnblockUser(accountId);
                    success = true;
                } catch (ChillyRoom.Services.Core.Errors.ApiException<ErrorResponse> ex) {
                    success = false;
                    Debug.LogError($"bbbbbbbbbbbbbbb UnblockUser Error {ex.Result.Error}");
                    if (HandleErrorCode(ex.Result)) {
                        return;
                    }

                    throw;
                }
            });
            return success;
        }

        /// <summary>
        /// 删除好友
        /// </summary>
        /// <param name="accountId"></param>
        public async Task<bool> RemoveFriend(long accountId) {
            var success = false;
            await FriendUtils.ShowWhenError(async () => {
                try {
                    if (LogUtil.IsShowLog) {
                        LogUtil.Log("[IMData] bbbbbbbbbbbbbbb RemoveFriend");
                    }

                    await _playerClient.ContactController.RemoveFriend(accountId);

                    var key = "";
                    var contact = GetContact(accountId);
                    if (contact != null && contact.RelationType == UserRelationType.Friend) {
                        key = "Err/Net_err_try_again";
                    } else {
                        key = "im/tip/remove_friend";
                        success = true;
                    }
                    FriendUtils.ShowToastOnMain(ScriptLocalization.Get(key));
                } catch (ChillyRoom.Services.Core.Errors.ApiException<ErrorResponse> ex) {
                    success = false;
                    Debug.LogError($"bbbbbbbbbbbbbbb RemoveFriend Error {ex.Result.Error}");
                    if (HandleErrorCode(ex.Result)) {
                        return;
                    }

                    throw;
                }
            });
            return success;
        }

        /// <summary>
        /// IM相关错误码处理提示
        /// </summary>
        /// <param name="error"></param>
        /// <returns></returns>
        public bool HandleErrorCode(ErrorResponse error) {
            var errCode = (IMErrorCodes)error.Error;
            switch (errCode) {
                case IMErrorCodes.NickNameTooLong:
                case IMErrorCodes.NickNameCannotBeEmpty:
                case IMErrorCodes.NickNameContainsSensitiveWords:
                    // 名字不合法
                    FriendUtils.ShowToastOnMain(ScriptLocalization.Get("im/tip/nickname_invalid"));
                    return true;
                case IMErrorCodes.ContatNotExist:
                    // 未找到玩家
                    FriendUtils.ShowToastOnMain(ScriptLocalization.Get("im/tip/not_found"));
                    return true;
                case IMErrorCodes.InvalidRecipient:
                    // 非好友
                    FriendUtils.ShowToastOnMain(ScriptLocalization.Get("im/tip/been_delete", "#消息发送失败，您已被对方删除"));
                    return true;
                case IMErrorCodes.InvalidRecipientBlocked:
                    // 给对方发消息，但是已经被对方拉黑
                    FriendUtils.ShowToastOnMain(ScriptLocalization.Get("im/tip/in_blacklist", "#消息发送失败，您存在对方的黑名单中"));
                    return true;
                case IMErrorCodes.FriendOrBlackListLimitExceeded:
                    // 好友或黑名单数量达到上限
                    // FriendUtils.ShowToastOnMain(ScriptLocalization.Get("im/tip/not_found"));
                    return false;
                case IMErrorCodes.SendMessageTooFast:
                    // 操作频繁
                    FriendUtils.ShowToastOnMain(ScriptLocalization.Get("error/too_frequent"));
                    return true;
                case IMErrorCodes.FriendLimitExceeded:
                    // 好友已达上限
                    FriendUtils.ShowToastOnMain(ScriptLocalization.Get("im/tip/firend_overflow"));
                    return true;
                case IMErrorCodes.BlackListLimitExceeded:
                    // 黑名单已达上限
                    FriendUtils.ShowToastOnMain(ScriptLocalization.Get("im/tip/blacklist_overflow"));
                    return true;
                case IMErrorCodes.TargetFriendLimitExceeded:
                    // 对方好友已达上限
                    FriendUtils.ShowToastOnMain(ScriptLocalization.Get("im/tip/other_friend_overflow", "#添加好友失败，对方好友数已达到上限。"));
                    return true;

                default:
                    // 外部处理其他类型错误码
                    return false;
            }
        }

        /// <summary>
        /// 更新在线状态：在线/离线/游戏中（单人or多人）
        /// 客户端只要在 在线和游戏中 两个状态之间互相切换
        /// </summary>
        /// <param name="status"></param>
        public async Task<bool> UpdateMyMasterStatus(ContactMasterStatus status) {
            var success = false;
            await FriendUtils.ShowWhenError(async () => {
                try {
                    if (LogUtil.IsShowLog) {
                        LogUtil.Log("[IMData] bbbbbbbbbbbbbbb UpdateMyMasterStatus");
                    }

                    await _playerClient.ContactController.UpdateMyMasterStatus(status);
                    success = true;
                } catch (ChillyRoom.Services.Core.Errors.ApiException<ErrorResponse> ex) {
                    success = false;
                    Debug.LogError($"bbbbbbbbbbbbbbb UpdateMyMasterStatus Error {ex.Result.Error}");
                    if (HandleErrorCode(ex.Result)) {
                        return;
                    }

                    throw;
                }
            }, false);
            return success;
        }

        /// <summary>
        /// 创建 profile，只有自己的 nickName 和 avatarUrl 都为空才可以创建
        /// 其他情况只能调用 UpdateMyProfile
        /// </summary>
        /// <param name="nickName"></param>
        /// <param name="avatarUrl"></param>
        public async Task<bool> CreateProfile(string nickName, string avatarUrl, bool showToast = true) {
            var success = false;
            await FriendUtils.ShowWhenError(async () => {
                try {
                    if (LogUtil.IsShowLog) {
                        LogUtil.Log("[IMData] bbbbbbbbbbbbbbb CreateProfile");
                    }

                    await _playerClient.ContactController.CreateProfile(nickName, avatarUrl);
                    success = true;
                } catch (ChillyRoom.Services.Core.Errors.ApiException<ErrorResponse> ex) {
                    success = false;
                    Debug.LogError($"bbbbbbbbbbbbbbb CreateProfile Error {ex.Result.Error}");
                    if (HandleErrorCode(ex.Result)) {
                        return;
                    }

                    throw;
                }
            }, showToast);
            return success;
        }

        /// <summary>
        /// CreateProfile以后要调用这个方法
        /// </summary>
        public async Task InitPlayerClient() {
            try {
                if (LogUtil.IsShowLog) {
                    LogUtil.Log("[IMData] bbbbbbbbbbbbbbb InitPlayerClient");
                }

                await _playerClient.Initialize();
            } catch (Exception e) {
                BuglyUtil.ReportException("InitPlayerClient", e);
            }
        }

        /// <summary>
        /// 获取所有可用的头像 url
        /// url 使用  ImageLoader.LoadSprite 来加载，.ThenSet(image) 来异步设置图像
        /// ImageLoader 的使用参考：https://github.com/IvanMurzak/Unity-ImageLoader
        /// </summary>
        /// <returns></returns>
        public async Task<List<string>> ListAvailableAvatars() {
            List<string> result = new List<string>();
            await FriendUtils.ShowWhenError(async () => {
                try {
                    if (LogUtil.IsShowLog) {
                        LogUtil.Log("[IMData] bbbbbbbbbbbbbbb ListAvailableAvatars");
                    }

                    result = await _playerClient.ContactController
                        .ListAvailableAvatars();
                } catch (ChillyRoom.Services.Core.Errors.ApiException<ErrorResponse> ex) {
                    Debug.LogError($"bbbbbbbbbbbbbbb ListAvailableAvatars Error {ex.Result.Error}");
                    result = null;
                    if (HandleErrorCode(ex.Result)) {
                        return;
                    }

                    throw;
                }
            });
            return result;
        }
        
        /// <summary>
        /// 根据id查用户信息，这里查询不会导致UpdateContact及相关监听回调
        /// </summary>
        /// <param name="userIds"></param>
        /// <returns></returns>
        public async Task<List<ContactModel>> QueryContactByIdsPreferCache(params long[] userIds) {
            if (LogUtil.IsShowLog) {
                LogUtil.Log("[IMData] bbbbbbbbbbbbbbb QueryContactByIds: " + string.Join(",", userIds));
            }

            List<ContactModel> result = new List<ContactModel>();
            await FriendUtils.ShowWhenError(async () => {
                try {
                    result = await _playerClient.ContactController
                        .QueryContactByIdsPreferCache(userIds);
                } catch (ChillyRoom.Services.Core.Errors.ApiException<ErrorResponse> ex) {
                    Debug.LogError($"bbbbbbbbbbbbbbb QueryContactByIds Error {ex.Result.Error}");
                    result = null;
                    if (HandleErrorCode(ex.Result)) {
                        return;
                    }

                    throw;
                }
            }, false);
            return result;
        }

        /// <summary>
        /// 根据id查用户信息
        /// </summary>
        /// <param name="userIds"></param>
        /// <returns></returns>
        public async Task<List<ContactModel>> QueryContactByIds(params long[] userIds) {
            if (LogUtil.IsShowLog) {
                LogUtil.Log("[IMData] bbbbbbbbbbbbbbb QueryContactByIds: " + string.Join(",", userIds));
            }

            List<ContactModel> result = new List<ContactModel>();
            await FriendUtils.ShowWhenError(async () => {
                try {
                    result = await _playerClient.ContactController
                        .QueryContactByIds(userIds);
                } catch (ChillyRoom.Services.Core.Errors.ApiException<ErrorResponse> ex) {
                    Debug.LogError($"bbbbbbbbbbbbbbb QueryContactByIds Error {ex.Result.Error}");
                    result = null;
                    if (HandleErrorCode(ex.Result)) {
                        return;
                    }

                    throw;
                }
            }, false);
            return result;
        }


        /// <summary>
        /// 根据id查用户信息
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<ContactModel> QueryContactById(long userId) {
            var result = await QueryContactByIdsPreferCache(userId);
            if (result == null || result.Count < 1) {
                return null;
            }

            return result[0];
        }
        
        public async Task<ContactModel> QueryContactAndUpdate(long userId) {
            var result = await QueryContactByIds(userId);
            if (result == null || result.Count < 1) {
                return null;
            }

            return result[0];
        }

        /// <summary>
        /// 获取玩家信息
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public ContactModel GetContact(long userId) {
            return _playerClient.ContactController.GetContact(userId);
        }

        /// <summary>
        /// 是否好友
        /// </summary>
        /// <param name="uid"></param>
        /// <returns></returns>
        public bool IsFriend(int uid) {
            var myFriend = DataMgr.IMData.GetContact(uid);
            return myFriend is { RelationType: UserRelationType.Friend };
        }

        /// <summary>
        /// 获得玩家的游戏状态
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        private GameStatus GetContactGameStatus(long userId) {
            var contact = GetContact(userId);
            if (contact == null)
                return null;

            var gameId = Convert.ToInt32(CoreKit.Config.SdkConfigManager.gameConfig.Id);
            return contact.GameStatus.GetValueOrDefault(gameId);
        }

        /// <summary>
        /// 获得最近的消息
        /// </summary>
        /// <param name="conversationId"></param>
        /// <returns></returns>
        public async Task<List<MessageModel>> GetRecentMessages(string conversationId, int count = 500) {
            List<MessageModel> messages = null;
            await FriendUtils.ShowWhenError(async () => {
                try {
                    messages = await _playerClient.MessageController.GetRecentMessagesFromConversation(conversationId,
                        count);
                } catch (ChillyRoom.Services.Core.Errors.ApiException<ErrorResponse> ex) {
                    Debug.LogError($"bbbbbbbbbbbbbbb GetRecentMessages Error {ex.Result.Error}");
                    if (HandleErrorCode(ex.Result)) {
                        return;
                    }

                    throw;
                }
            }, false);
            return messages ?? new List<MessageModel>();
        }

        /// <summary>
        /// 设置整个对话所有消息已读
        /// </summary>
        /// <param name="conversation"></param>
        public async ValueTask MarkConversationAsRead(ConversationModel conversation) {
            await FriendUtils.ShowWhenError(async () => {
                try {
                    await _playerClient.MessageController.MarkAllMessagesAsRead(conversation);
                } catch (ChillyRoom.Services.Core.Errors.ApiException<ErrorResponse> ex) {
                    Debug.LogError($"bbbbbbbbbbbbbbb MarkConversationAsRead Error {ex.Result.Error}");
                    if (HandleErrorCode(ex.Result)) {
                        return;
                    }

                    throw;
                }
            }, false);
        }

        /// <summary>
        /// 设置单个消息已读
        /// </summary>
        /// <param name="messageModel"></param>
        public async ValueTask MarkMessageAsRead(MessageModel messageModel) {
            await FriendUtils.ShowWhenError(async () => {
                try {
                    await _playerClient.MessageController.MarkMessageAsRead(messageModel);
                } catch (ChillyRoom.Services.Core.Errors.ApiException<ErrorResponse> ex) {
                    Debug.LogError($"bbbbbbbbbbbbbbb MarkMessageAsRead Error {ex.Result.Error}");
                    if (HandleErrorCode(ex.Result)) {
                        return;
                    }

                    throw;
                }
            }, false);
        }

        /// <summary>
        /// 获得好友关系
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        private UserRelationType GetContactRelationType(long userId) {
            foreach (var model in _playerClient.ContactController.GetAllContacts()) {
                if (model.UserId == userId) {
                    return model.RelationType;
                }
            }

            return UserRelationType.None;
        }

        /// <summary>
        /// 是否到达改名上限
        /// </summary>
        /// <returns></returns>
        private bool HasReachedProfileNameUpdateThrottle() {
            var myProfile = GetMyProfile();
            if (myProfile != null && myProfile.LastProfileUpdate != null && myProfile.LastProfileUpdate.HasValue) {
                var now = TimeUtil.ConvertToUnixTimestamp(NetTimeUtil.CurrentTime.Item2);
                var lastUpdatedTime = myProfile.LastProfileUpdate.Value.ToUnixTimeSeconds();
                var nextAllowedUpdateTime = lastUpdatedTime + _playerClient.Settings.ProfileUpdateIntervalSeconds;
                var diff = nextAllowedUpdateTime - now;
                if (diff > 0) {
                    var timeSpan = TimeSpan.FromSeconds(diff);
                    var msg = ScriptLocalization.Get("im/nickname_cooldown", "#修改昵称剩余冷却时间：{0}天{1}时{2}分{3}秒");
                    msg = string.Format(msg, timeSpan.Days, timeSpan.Hours, timeSpan.Minutes, timeSpan.Seconds);
                    FriendUtils.ShowToastOnMain(msg);
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 获取所有玩家信息
        /// </summary>
        /// <param name="withSelf"></param>
        /// <returns></returns>
        public List<ContactModel> GetAllContacts(bool withSelf = false) {
            return _playerClient.ContactController.GetAllContacts(withSelf);
        }

        /// <summary>
        /// 获取所有好友的聊天记录
        /// </summary>
        /// <returns></returns>
        public List<ConversationModel> GetAllConversations() {
            if (_playerClient != null) {
                return _playerClient.MessageController.GetConversations();
            }

            return new List<ConversationModel>();
        }

        /// <summary>
        /// 获取某个好友的聊天记录
        /// </summary>
        /// <returns></returns>
        public ConversationModel GetConversation(long remoteUserId) {
            var conversations = GetAllConversations();
            var conversation = conversations.Find(c => c.RemoteUserId == remoteUserId);
            if (conversation == null) {
                return null;
            }

            return conversation;
        }
        
        /// <summary>
        /// 查找某个对话未读消息数量（排除自己发的消息）
        /// conversationModel.UnreadCount是表示所有未读的消息，包含自己发的
        /// </summary>
        /// <param name="selfUserId"></param>
        /// <param name="conversationModel"></param>
        /// <returns></returns>
        public async Task<int> GetConversationUnreadCount(long selfUserId, ConversationModel conversationModel) {
            if (_playerClient == null) return 0;
            if (conversationModel.UnreadCount == 0) return 0;

            int count = 0;
            var messages = await _playerClient.MessageController.GetRecentMessagesFromConversation(conversationModel.Id, conversationModel.UnreadCount);
            foreach (MessageModel messageModel in messages) {
                if (!messageModel.IsRead && messageModel.SenderId != selfUserId) {
                    count++;
                }
            }

            return count;
        }

        /// <summary>
        /// 获取好友信息 + 被好友拉黑后的数据
        /// </summary>
        /// <returns></returns>
        public List<ContactModel> GetFriends() {
            return GetAllContacts().Where(model => 
                model.RelationType == UserRelationType.Friend || 
                model.RelationType == UserRelationType.BlockByPeer).ToList();
        }
        
        public int GetFriendCount() {
            return GetFriends().Count;
        }

        /// <summary>
        /// 获取等待添加的好友
        /// </summary>
        /// <returns></returns>
        public List<ContactModel> GetPendingFriends() {
            return _playerClient.ContactController.GetPendingFriends();
        }

        /// <summary>
        /// 获取被拉黑的玩家
        /// </summary>
        /// <returns></returns>
        public List<ContactModel> GetBlockedUsers() {
            return _playerClient.ContactController.GetBlockedUsers();
        }

        /// <summary>
        /// 发送组队邀请
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        public async Task<SendMessageTeamInviteResp> SendMessageTeamInviteAsync(SendMessageTeamInviteReq req) {
            if (req.RemoteUserId == 0) {
                return null;
            }

            SendMessageTeamInviteResp resp = null;
            await FriendUtils.ShowWhenError(async () => {
                try {
                    if (LogUtil.IsShowLog) {
                        LogUtil.Log("[IMData] bbbbbbbbbbbbbbb SendMessageTeamInviteAsync");
                    }

                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
                    resp = await GetClient().SendMessageTeamInviteAsync(req, cts.Token);

                    SimpleEventManager.Raise(new IMSendTeamInviteEvent(resp));
                } catch (ChillyRoom.Services.Core.Errors.ApiException<ErrorResponse> ex) {
                    Debug.LogError($"bbbbbbbbbbbbbbb SendMessageTeamInviteAsync Error {ex.Result.Error}");
                    if (HandleErrorCode(ex.Result)) {
                        return;
                    }

                    throw;
                }
            });
            return resp;
        }

        /// <summary>
        /// 发送表情
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        public async Task<SendMessageEmojiResp> SendMessageEmojiAsync(SendMessageEmojiReq req) {
            if (req.RemoteUserId == 0) {
                return null;
            }

            SendMessageEmojiResp resp = null;
            await FriendUtils.ShowWhenError(async () => {
                try {
                    if (LogUtil.IsShowLog) {
                        LogUtil.Log("[IMData] bbbbbbbbbbbbbbb SendMessageEmojiAsync");
                    }

                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
                    resp = await GetClient().SendMessageEmojiAsync(req, cts.Token);

                    SimpleEventManager.Raise(new IMSendEmojiEvent(resp));
                } catch (ChillyRoom.Services.Core.Errors.ApiException<ErrorResponse> ex) {
                    Debug.LogError($"bbbbbbbbbbbbbbb SendMessageEmojiAsync Error {ex.Result.Error}");
                    if (HandleErrorCode(ex.Result)) {
                        return;
                    }

                    throw;
                }
            });
            return resp;
        }

        /// <summary>
        /// 发送邀请码
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        public async Task<SendMessageInviteCodeResp> SendMessageInviteCodeAsync(SendMessageInviteCodeReq req) {
            if (req.RemoteUserId == 0) {
                return null;
            }

            SendMessageInviteCodeResp resp = null;
            await FriendUtils.ShowWhenError(async () => {
                try {
                    if (LogUtil.IsShowLog) {
                        LogUtil.Log("[IMData] bbbbbbbbbbbbbbb SendMessageInviteCodeAsync");
                    }

                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
                    resp = await GetClient().SendMessageInviteCodeAsync(req, cts.Token);

                    SimpleEventManager.Raise(new IMSendInviteCodeEvent(resp));
                } catch (ChillyRoom.Services.Core.Errors.ApiException<ErrorResponse> ex) {
                    Debug.LogError($"bbbbbbbbbbbbbbb SendMessageInviteCodeAsync Error {ex.Result.Error}");
                    if (HandleErrorCode(ex.Result)) {
                        return;
                    }

                    throw;
                }
            });
            return resp;
        }

        /// <summary>
        /// 昵称格式校验（from 前传)
        /// </summary>
        /// <param name="f_value"></param>
        /// <returns></returns>
        public string LimitStringLength(string f_value) {
            try {
                //起名字长度限制，中文2，数字2，其他1，总长不超过10
                int byte_count = 0;
                for (int i = 0; i < f_value.Length; i++) {
                    var sub_str = f_value.Substring(i, 1);
                    if (int.TryParse(sub_str, out int v)) {
                        byte_count += 2;
                    } else {
                        byte_count += Encoding.Default.GetByteCount(sub_str) > 1 ? 2 : 1;
                    }

                    if (byte_count > 10) {
                        f_value = f_value.Substring(0, i);
                        break;
                    }
                }

                return f_value;
            } catch (Exception e) {
                Debug.LogError(e);
            }

            return f_value;
        }

        /// <summary>
        /// 获得自己的个人信息
        /// </summary>
        /// <returns></returns>
        public ContactModel GetMyProfile() {
            if (_playerClient == null) {
                return null;
            }
            
            return _playerClient.ContactController.GetMyProfile();
        }


        private bool IsActionTooFrequent() {
            var now = Time.realtimeSinceStartup;
            if (now < _nextActionAllowTime) {
                FriendUtils.ShowToastOnMain(ScriptLocalization.Get("error/too_frequent"));
                return true;
            }

            _nextActionAllowTime = Time.realtimeSinceStartup + GetActionFrequency();
            return false;
        }

        public static int GetActionFrequency() {
            return 2;
        }
        
        public bool IsMySelfMsg(long remoteUid) {
            var myProfile = DataMgr.IMData.GetMyProfile();
            var myUid = myProfile?.UserId ?? 0;
            return remoteUid == myUid;
        }

    }
}