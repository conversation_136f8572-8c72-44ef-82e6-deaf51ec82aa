using Activities;
using Activities.CheckIn.Scripts;
using Activities.ReturnPlayer.Scripts;
using cfg.task;
using ChillyRoom.Services.Core.Libs;
using ChillyRoom.Utils;
using RGScript.Config.Manager;
using RGScript.Data.Mall;
using System;
using System.Collections;
using System.Collections.Generic;
using TaskSystem;
using System.Linq;
using UnityEngine;

namespace RGScript.Data {
    /// <summary>
    /// 回流玩家
    /// </summary>
    public class ReturnPlayerData : BaseData {
        // 回流状态开启的时间
        // 不在回流的时候是 DateTime.Default
        private DateTime _statusStartTime;

        // 回流状态结束的时间
        // 不在回流的时候是 DateTime.Default
        private DateTime _statusEndTime;

        // 回流期间：这是回流第几天
        private int _returnDay;

        // 触发过回流时候的客户端版本号
        private List<string> _triggerVersions;

        private readonly List<emBattleFactor> _factorCache = new List<emBattleFactor>();
        public const int ReturnPlayerThresholdDays = 45; // 回流判定的天数阈值
        public const int ReturnStatusDurationDays = 30; // 回流状态持续天数
        private const long TrialHeroDuration = 259200;

        private static readonly DateTime
            DefaultEffectiveLoginTime = new DateTime(2025, 4, 28, 2, 0, 0, DateTimeKind.Utc); // 默认时间 UTC

        public Dictionary<int, List<TaskMissionReturnConfig>> taskDict;
        private emHero _trialHero;
        public List<emHero> TrialHeroShortlist = new List<emHero>();
        private const int MaxTrialHeroCount = 3;

        public override void Clear() {
            base.Clear();
            SimpleEventManager.RemoveListener<BeforeSelfEnterHeroRoomEvent>(OnBeforeEnterHall);
            SimpleEventManager.RemoveListener<GameFinishEvent>(OnGameFinishEvent);
            SimpleEventManager.RemoveListener<EnterGameStatement>(OnEnterGameStatement);
        }

        /// <summary>
        /// 是否回流玩家
        /// </summary>
        /// <returns></returns>
        public bool IsEnable() {
            return false;
            if (_statusStartTime == default || _statusEndTime == default) return false;
            if (ChannelConfig.IsHuaWeiOutseas) {
                // 华为海外屏蔽
                return false;
            }

            var isGardenUnlocked = RoomItemUnlockThroughBattle.GetUnlock(emRoomUnlockItem.garden);
            if (!isGardenUnlocked) return false;
            return MallUtility.CurrentTime >= GetStatusStartTime() && MallUtility.CurrentTime <= GetStatusEndTime();
        }

        /// <summary>
        /// 回流期间这是回流第几天
        /// </summary>
        /// <returns></returns>
        public int GetReturnDay() {
            if (!IsEnable()) return int.MaxValue;
            var day = Math.Clamp(_returnDay, 1, 5);
            return day;
        }
        
        public void RefreshData(ChillyRoom.SoulKnight.CloudPackage.V1.ReturnPlayerData data) {
            if (data == null) return;
            
            _returnDay = data.ReturnDay;
            _triggerVersions = data.TriggerVersions;
            _statusStartTime = data.StatusStartTime.GetValueOrDefault();
            _statusEndTime = data.StatusEndTime.GetValueOrDefault();
            var forceClean = MiscData.data.currentReturnCount != _triggerVersions.Count;
            var timeExpire = MallUtility.CurrentTime > _statusEndTime;
            MiscData.data.currentReturnCount = _triggerVersions.Count;
            // 如果客户端回流次数和服务器回流次数对不上，或者当前时间已经在结束时间之后，则清理数据
            if (forceClean || timeExpire) {
                Debug.Log($"ReturnPlayerData.CleanData() forceClean {forceClean} timeExpire: {timeExpire}");
                CleanData();
            }
        }

        /// <summary>
        /// 回归过几次，如果当前就在回流，则也算
        /// </summary>
        /// <returns></returns>
        public int GetReturnTimes() {
            if (_triggerVersions == null) {
                return 0;
            }

            return _triggerVersions.Count;
        }


        private void OnBeforeEnterHall(BeforeSelfEnterHeroRoomEvent e) {
            TaskManager.Instance.LoadReturnPlayerTask();
            TaskManager.Instance.CheckReturnPlayerTasks();
            
            InitTrialHeroList();
            _trialHero = MiscData.data.returnPlayerTrailHero;
            _factorCache.Clear();
        }

        /// <summary>
        /// 回流后每日首次进入选人界面时弹出
        /// </summary>
        /// <returns></returns>
        public bool GetNeedPopUpCheckin() {
            if (!IsEnable()) return false;
            var isNewDay = MiscData.data.lastCheckInDay < PlayerSaveData.Inst.last_play_time_with_network_time;
            if (!isNewDay) return false;

            return ActivityReturnPlayerManager.GetCenGetCheckInReward();
        }

        public void SetPopUpCheckIn() {
            var isNewDay = MiscData.data.lastCheckInDay < PlayerSaveData.Inst.last_play_time_with_network_time;
            if (!isNewDay) return;
            MiscData.data.lastCheckInDay = PlayerSaveData.Inst.last_play_time_with_network_time;
            MiscData.Save();
        }

        private bool _needShowAfterGameFinish;

        /// <summary>
        /// 回流后首次结算游戏(无论成功还是失败)，然后选完角色进入大厅时弹出
        /// </summary>
        /// <returns></returns>
        public bool GetNeedShowAfterGameFinish() {
            if (!IsEnable()) return false;
            var isNewDay = MiscData.data.lastPopupGameFinishDay < PlayerSaveData.Inst.last_play_time_with_network_time;
            if (!isNewDay) return false;
            return _needShowAfterGameFinish;
        }

        public void SetShowAfterGameFinish() {
            var isNewDay = MiscData.data.lastPopupGameFinishDay < PlayerSaveData.Inst.last_play_time_with_network_time;
            if (!isNewDay) return;
            MiscData.data.lastPopupGameFinishDay = PlayerSaveData.Inst.last_play_time_with_network_time;
            MiscData.Save();
            _needShowAfterGameFinish = false;
        }

        private void CheckCleanData() {
        }

        private void CleanData() {
            CleanReturnPlayerEventData();
        }

        public ReturnPlayerData() {
            InitData();

            SimpleEventManager.AddEventListener<BeforeSelfEnterHeroRoomEvent>(OnBeforeEnterHall);
            SimpleEventManager.AddEventListener<GameFinishEvent>(OnGameFinishEvent);
            SimpleEventManager.AddEventListener<EnterGameStatement>(OnEnterGameStatement);
        }

        private void InitData() {
            taskDict = new Dictionary<int, List<TaskMissionReturnConfig>>();
            foreach (var config in DataMgr.ConfigData.Tables.TbTaskMissionReturnConfig.DataList) {
                if (taskDict.TryGetValue(config.Day, out var list)) {
                    list.Add(config);
                } else {
                    taskDict.Add(config.Day, new List<TaskMissionReturnConfig>() {config});
                }
            }
        }

        public DateTime GetStatusStartTime() {
            return _statusStartTime;
        }

        public DateTime GetStatusEndTime() {
#if UNITY_EDITOR
            if (UnityEditor.EditorPrefs.GetBool("ReturnPlayerEndDebugTime", false)) {
                return new DateTime(long.Parse(UnityEditor.EditorPrefs.GetString("ReturnPlayerEndDebugTicks", "0")));
            }
#endif
            return _statusEndTime;
        }

        public List<string> GetTriggerVersions() { return _triggerVersions; }

        public bool IsReturnPlayer() {
            return IsEnable();
        }

        public bool NeedEnterReturnPlayerIntro() {
            return IsReturnPlayer() && GetReturnPlayerFactorCount() > 0;
        }

        public int GetReturnPlayerFactorCount() {
            return MiscData.data.returnPlayerFactorCount;
        }

        public void SetReturnPlayerFactorCount(int count) {
            MiscData.data.returnPlayerFactorCount = count;
            MiscData.Save();
        }

        public bool IsReturnPlayerBattle() {
            if (BattleData.data.marks.TryGetValue(RGGameConst.RETURN_PLAYER_FACTOR_COUNT, out var count)) {
                return count > 0;
            }

            return false;
        }

        public int GetReturnPlayerFactorCountInBattle() {
            return BattleData.data.marks.GetValueOrDefault(RGGameConst.RETURN_PLAYER_FACTOR_COUNT, 0);
        }

        public Sprite GetReturnPlayerFactorIcon() {
            var count = GetReturnPlayerFactorCountInBattle();

            if (count == 0) {
                count = GetReturnPlayerFactorCount();
            }

            return ResourcesUtil.Load<Sprite>($"RGTexture/ui/common/BattleFactors/return_player_factor_{count}.png");
        }

        public bool IsHeroInTrial(emHero hero) {
            if (!GameUtil.IsSingleGame()) {
                return false;
            }

            if (hero == emHero.None) {
                return false;
            }

            if (hero != _trialHero) {
                return false;
            }

            if (!IsReturnPlayer()) {
                return false;
            }

            return !AlreadyChooseReturnPlayerTrialHero() || DuringHeroTrial();
        }

        private bool AlreadyChooseReturnPlayerTrialHero() {
            return MiscData.data.returnPlayerTrailHeroStartTimestamp > 0;
        }

        public bool DuringHeroTrial() {
            return IsReturnPlayer() && GetHeroTrialRemainTime() > 0;
        }

        public int GetHeroTrialRemainTime() {
            var duration = MallUtility.CurrentTime.Unix() - MiscData.data.returnPlayerTrailHeroStartTimestamp;
            var remain = TrialHeroDuration - duration;
            return remain > 0 ? (int)remain : 0;
        }

        public bool IsHeroInTrial(emHero hero, out List<int> trialSkillIds) {
            trialSkillIds = new List<int>();
            if (!IsHeroInTrial(hero)) {
                return false;
            }

            trialSkillIds.Add(0);
            trialSkillIds.Add(1);
            trialSkillIds.Add(2);
            return true;
        }

        public bool IsHeroInTrialShortlist(emHero hero) {
            return IsReturnPlayer() && TrialHeroShortlist.Contains(hero);
        }

        public void SetTrialHero(emHero hero) {
            _trialHero = hero;
        }

        public void SaveTrialHero(emHero hero) {
            _trialHero = hero;
            MiscData.data.returnPlayerTrailHeroStartTimestamp = MallUtility.CurrentTime.Unix();
            MiscData.data.returnPlayerTrailHero = hero;
            MiscData.Save();
        }

        private void InitTrialHeroList() {
            if (MiscData.data.returnPlayerTrailHero > 0) {
                return;
            }

            var heroList = new List<emHero>();
            heroList = GetHeroWithPriceType(heroList, 1);
            heroList = GetHeroWithPriceType(heroList, 4);

            if (heroList.Count > MaxTrialHeroCount) {
                heroList = heroList.Take(MaxTrialHeroCount).ToList();
            }

            TrialHeroShortlist = heroList;
        }

        private List<emHero> GetHeroWithPriceType(List<emHero> heroList, int priceType) {
            for (int i = (int)emHero.Count - 1; i >= 0; i--) {
                var hero = (emHero)i;
                if (CharactersLevelUpConfigManager.IsHeroChar(i)) {
                    continue;
                }

                var unlock = DataUtil.GetHeroUnlock(hero);
                if (unlock) {
                    continue;
                }

                var heroPriceType = MallUtility.GetHeroPriceType(hero);

                if (heroPriceType == priceType) {
                    heroList.Add(hero);
                }
            }

            return heroList;
        }

        private void OnGameFinishEvent(GameFinishEvent e) {
            if (!IsEnable()) return;
            var isNewDay = MiscData.data.lastPopupGameFinishDay < PlayerSaveData.Inst.last_play_time_with_network_time;
            if (!isNewDay) return;
            _needShowAfterGameFinish = true;
        }

        private const string ReturnPlayerBundleName = "return_player_intro";

        public IEnumerator EnterReturnPlayerIntro() {
            var bundleList = new List<string> {
                "level/1/b",
                "bgm/bgm_5Low",
                ReturnPlayerBundleName,
                AssetBundleLoader.PatternRoom,
            };
            var bossBundles = AssetBundleLoader.Inst.GetBossBundle("boss10");
            bundleList.AddRange(bossBundles);
            yield return AssetBundleLoader.Inst.LoadMultipleBundles(
                AssetBundleLoader.Inst.GetAllDependencies(ReturnPlayerBundleName));
            yield return AssetBundleLoader.Inst.LoadMultipleBundles(bundleList);
            RoomObjectManager roomManager = RoomObjectManager.Inst;
            roomManager.SavePlants();
            roomManager.SaveForgeWeapons();
            PlayerSaveData.Save();
            RGSaveManager.Inst.SaveGameData();
            ItemData.Save();
            BattleData.data.RefreshWeapon();
            BattleData.data.RefreshMount();
            BattleData.data.statueIndex = 0;
            BattleData.data.additionStatues.Clear();
            _factorCache.AddRange(BattleData.data.factors);
            _factorCache.Remove(emBattleFactor.ReturnPlayer);
            BattleData.data.factors.Clear();
            BattleData.data.season = Season.None;
            BattleData.data.ResetBuffCount();
            BattleData.data.petAdditions.Clear();
            BattleData.data.activityNames.Clear();

            RGGameProcess.Inst.use_br_ticket_flag = false;
            RGGameProcess.Inst.this_index = -4;
            BattleData.data.SetFactor(emBattleFactor.ReturnPlayer, true);

            var returnPlayerFactorCount = GetReturnPlayerFactorCount();
            BattleData.data.SetMark(RGGameConst.RETURN_PLAYER_FACTOR_COUNT, returnPlayerFactorCount);
            returnPlayerFactorCount--;
            SetReturnPlayerFactorCount(returnPlayerFactorCount);

            BattleData.data.SetSaveGameId(GameUtil.GenGameId());
            BattleData.data.has_reborn = false;
            BattleData.data.has_ad_reborn_twice = false;
            BattleData.data.SetCurrentSaveId();
            RGGameProcess.Inst.UpdateGameTime();
            SimpleEventManager.Raise(new EnterModeEvent {
                game_mode = emGameMode.Normal,
                isBadAss = BattleData.data.isBadass,
            });
            TAUtil.Track("ui_entry_game_start", new Dictionary<string, object>() {
                { "ui_entry_game_mode", BattleData.data.gameMode },
                { "ui_entry_is_badass", BattleData.data.isBadass },
                { "is_activity_on", false },
                { "activity_name", "" },
                { "ui_entry_is_pure_br", BattleData.data.IsBossRushMode && BossRushGameModeProcess.IsPureMode() },
                { "ui_entry_season", BattleData.data.season },
            });
            SimpleEventManager.Raise(new BeforeSwitchSceneEvent());
            FuncForHotFix();
            yield return null;
            Debug.Log("goto Scene_Tutorial");
            AssetBundleLoader.Inst.LoadScene("Scene_Tutorial");
        }

        private void FuncForHotFix() {
        }

        public void CleanReturnPlayerEventData() {
            MiscData.data.returnPlayerFactorCount = 0;
            MiscData.data.isReturnPlayerInit = false;
            MiscData.data.returnPlayerRewardList.Clear();
            CleanReturnPlayerCheckInData();
            CleanReturnPlayerTrialData();
            TaskManager.Instance.Debug_ResetAllReturnPlayerTask();
            MiscData.Save();
        }

        private void CleanReturnPlayerTrialData() {
            MiscData.data.returnPlayerTrailHero = emHero.None;
            MiscData.data.returnPlayerTrailHeroStartTimestamp = 0;
            MiscData.Save();
        }

        // 清除签到数据
        private void CleanReturnPlayerCheckInData() {
            StatisticData.data.ActivityReturnPlayerCheckInOpenTimestamp = null;

            // 签到7天奖励领取状态
            StatisticData.data.ActivityReturnPlayerCheckInStatusKeys = new List<string>();

            StatisticData.data.ActivityReturnPlayerCheckInStatusValues = new List<CheckStatus>();

            // 可能被替换的奖励(提前解锁的皮肤，技能)
            StatisticData.data.ActivityReturnPlayerCheckInReplaceRewards =
                new Dictionary<string, CheckInReward>();

            // 2025骑士归来签到开启状态
            StatisticData.data.open_activity_return_player_checkin = CheckInOpenStatus.None;
            StatisticData.Save();
        }

        public void ProcessReturnPlayerBattleFactor() {
            if (_factorCache.Count <= 0) {
                return;
            }

            foreach (var factor in _factorCache) {
                BattleData.data.SetFactor(factor, true);
            }

            _factorCache.Clear();
        }

        private void OnEnterGameStatement(EnterGameStatement e) {
            var countInBattle = GetReturnPlayerFactorCountInBattle();
            var count = GetReturnPlayerFactorCount();
            if (countInBattle <= 0 || count < countInBattle) {
                return;
            }

            countInBattle--;
            SetReturnPlayerFactorCount(countInBattle);
        }
    }
}