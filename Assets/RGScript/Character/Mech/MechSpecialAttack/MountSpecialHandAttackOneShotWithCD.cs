using System;
using System.Collections;
using UnityEngine;


/// <summary>
/// 按下后触发单次攻击，有CD限制
/// </summary>
public class MountSpecialHandAttackOneShotWithCD : MountSpecialHandAttackOneShot {
    public Func<bool> canExecuteSpecialDown;
    public Action<bool> onReady;
    public bool isReady = true;
    public float cd = 5f;
    private Coroutine _coroutine;

    private void OnEnable() {
        isReady = true;
    }

    private void OnDisable() {
        if (_coroutine != null) {
            StopCoroutine(_coroutine);
        }
    }

    public override void SpecialDown() {
        if (isReady) {
            if (canExecuteSpecialDown != null && !canExecuteSpecialDown()) {
                UICanvas.Inst.ShowTextTalk(multiHand.controller.transform, "...", 3f, 1f, 0);
                return;
            }
            
            var hasAtked = multiHand.Attack();
            if (hasAtked) {
                isReady = false;
                onReady?.Invoke(false);
                StartCooldownTimer();
            }
        }
    }

    private void StartCooldownTimer() {
        if (_coroutine != null) {
            StopCoroutine(_coroutine);
        }
        _coroutine = StartCoroutine(CooldownRoutine());
    }
    
    private IEnumerator CooldownRoutine() {
        float endTime = Time.time + cd;
        while (Time.time < endTime) {
            yield return null;
        }

        isReady = true;
        onReady?.Invoke(true);
    }
}