using System.Collections.Generic;

namespace RGScript.Character.Player {
    public class OfficerAndShooterCombinationSkillTriggerResult {
        public List<int> NetIds { get; private set; }
        public int OfficerNetId { get; private set; }
        public int ShooterNetId { get; private set; }
        
        public OfficerAndShooterCombinationSkillTriggerResult(int officerNetId, int shooterNetId) {
            NetIds = new List<int>() { officerNetId, shooterNetId};
            OfficerNetId = officerNetId;
            ShooterNetId = shooterNetId;
        }
    }
}