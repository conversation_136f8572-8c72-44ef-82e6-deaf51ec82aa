using System;
using System.Collections;
using System.Collections.Generic;
using Sirenix.OdinInspector;
using UnityEngine;

/// <summary>
/// 坐骑使用,适用于一个hand下有多个武器的情况. 
/// </summary>
public class MultiHand : RGBaseHand {
    public Action onSetupCompleted;
	public int max_angle = 90;
	public RGController controller;
	public bool allShoot;

    [LabelText("两把武器轮流开火")]
    public bool shootInSequence;
	public bool playAni;//是否播放子武器的动画
	public bool needLock;
	public bool continuous;//武器是否播放自己的动画
	/// <summary>
	/// 通过特殊按键使用的手
	/// </summary>
	[Title("通过特殊按键使用的手")]
    public bool autoUse;
    public bool autoSwitch;
    public float weaponSpeed = 1f;
	[HideInInspector]
    public RGWeapon[] weapons;
	int shootIndex;
	Animator ani;
	private SpriteRenderer[] spriteRenderers;
	private int stateListenerCount = 0;
	private int attackStartCount = 0;
	private int attackFinishedCount = 0;
	public event Action onWeaponAttackStart;
	public event Action onWeaponAttackFinished;

	protected override void Awake() {
		target_layer = 7;
        if (!continuous) {
            if (BattleData.data.CompareFactor(emBattleFactor.FastShooter)) {
                weaponSpeed *= 2;
            }
            if (BattleData.data.CompareFactor(emBattleFactor.SlowShooter)) {
                weaponSpeed /= 2;
            }
            ani = GetComponent<Animator>();
            ani.speed = weaponSpeed;
        }
		weapons = new RGWeapon[transform.childCount];
		for (int i = 0; i < transform.childCount; i++) {
			weapons[i] = transform.GetChild(i).GetComponentInChildren<RGWeapon>();
            if(weapons[i]) {
                var weaponStateListener = weapons[i].GetComponent<WeaponStateListener>();
                if (weaponStateListener) {
                    stateListenerCount += 1;
                    weaponStateListener.onWeaponAttackStart += OnWeaponsAttackStart;
                    weaponStateListener.onWeaponAttackFinished += OnWeaponAttackFinished;
                }
            }
		}
        if (weapons.Length > 0) {
		    front_weapon = weapons[0];
        }
        onSetupCompleted?.Invoke();
	}

    public void SetAttack(bool isAttack) {
        if (continuous) {
            if (isAttack && !CheckEnergyEnough()) {
                UICanvas.GetInstance().ShowTextTalk(controller.transform, "...", 2.5f, 1f, 0);
            } else {
                foreach (var w in weapons) {
                    if(w){
                        w.SetAttack(isAttack);
                    }
                }

                IsAttacking = isAttack;
            }
        } else {
            this.ani.SetBool("atk_b", isAttack);
            IsAttacking = isAttack;
        }
    }
    
    public bool IsAttacking { get; set; }

	internal bool Attack() {
		if (CheckEnergyEnough()) {
			if (allShoot) {
				foreach (var w in weapons) {
					if (playAni) {
						w.SetPetAttackTrigger(false);
					} else {
						w.SendMessage("Attack");
					}
				}
			} else {
				if (shootIndex >= weapons.Length) {
					shootIndex = 0;
				}
				if (playAni) {
					weapons[shootIndex].SetPetAttackTrigger(false);
				} else {
					weapons[shootIndex].SendMessage("Attack");
				}
				shootIndex++;
			}

            return true;
        } 
        
        UICanvas.GetInstance().ShowTextTalk(controller.transform, "...", 3f, 1f, 0);
        return false;
    }

	public void SetAngle(float angle) {
		if (needLock) {
			if (angle > max_angle) {
				angle = max_angle;
			} else if (angle < -max_angle) {
				angle = -max_angle;
			}
			//for (int i = 0; i < transform.childCount; i++) {
				//transform.GetChild(i).localEulerAngles = new Vector3(0, 0, angle);
			//}
            for(var i = 0; i < weapons.Length; ++i){
                if(weapons[i]){
                    weapons[i].SetLocalRotation(angle);
                }
            }
		}
	}

	public void SetController(RGController driver) {
		controller = driver;
		foreach (var w in weapons) {
            if(w){
			    w.SetController(driver);
                w.OwnerType = emOwnerType.Mount;
            }
        }
	}

	public int _consume = -1;//若为-1则自动计算消耗
	public int GetConsume() {
		if (_consume != -1) {
			return _consume;
		}
		int consume = 0;
		if (allShoot && !shootInSequence) {
			foreach (var w in weapons) {
                if(w) {
				    consume += w.realConsume;
                }
			}
			_consume = consume;
		} else {
			consume = weapons[0] ? weapons[0].realConsume : 0;
		}
		return consume;
	}

    public void SetWeaponFront() {
        foreach (var w in weapons) {
            if(w) {
                w.SetWeaponFront(transform, -1);
            }
        }
    }
    public void SetWeaponBack() {
        foreach (var w in weapons) {
            if(w) {
                w.SetWeaponBack(transform);
            }
        }
    }

    
    private void _SetWeapon(RGWeapon weapon, int idx){
        spriteRenderers = null; //重置
        if(idx >= weapons.Length) {
            System.Array.Resize(ref weapons, weapons.Length + 1);
            idx = weapons.Length - 1;
        }

        if(weapons[idx] != null) {
            DropWeapon(weapons[idx]);
        }
        weapons[idx] = weapon;
        var childIdx = Mathf.Min(transform.childCount - 1, idx);
        var weaponPositionNode = transform.GetChild(childIdx);
        var rd = weaponPositionNode.GetComponent<Renderer>();
        int sortingOrder = idx;
        if(rd) {
            sortingOrder = rd.sortingOrder;
        }
        weapon.SetWeaponFront(weaponPositionNode, sortingOrder);
        weapon.SetController(controller);
    }

    /// <summary>
    /// 设置指定武器槽中的武器
    /// </summary>
    /// <param name="weapon"></param>
    /// <param name="idx"></param>
    public void SetWeapon(RGWeapon weapon, int idx, bool soundFx = true){ 
        _SetWeapon(weapon, idx);
        if(soundFx){
            RGMusicManager.GetInstance().PlayEffect(9); // 音效
        }
    }

    /// <summary>
    /// 拾取武器,自动轮替
    /// </summary>
    /// <param name="weapon"></param>
    public void PickupWeapon(RGWeapon weapon, bool playSound){
        var weaponCount = WeaponCount();
        if(weaponCount < WeaponSlots()) {
            // 找一个空的槽位
            var slot = 0;
            for(var i = 0; i < weapons.Length; ++i){
                if(!weapons[i]){
                    slot = i;
                    break;
                }
            }
            SetWeapon(weapon, slot, playSound);
        } else {
            DropWeapon(weapons[weapons.Length - 1]); // 丢弃最后一把
            // 现有武器全部后移
            for(var i = weapons.Length - 2; i >= 0; --i){
                var w = weapons[i];
                DropWeapon(w);
                SetWeapon(w, i + 1, playSound);
            }
            // 新来的武器放第一
            SetWeapon(weapon, 0, playSound);
        }
        front_weapon = weapon;
    }

    public void ExpandWeaponSlots(){
        var newWeaponSlot = new GameObject("slot_" + weapons.Length);
        newWeaponSlot.transform.SetParent(transform);
        newWeaponSlot.transform.localPosition = Vector3.zero;
        newWeaponSlot.transform.localEulerAngles = Vector3.zero;
        newWeaponSlot.transform.localScale = Vector3.one;
        if(transform.childCount > 1){
            var lastSlot = transform.GetChild(transform.childCount - 2);
            newWeaponSlot.transform.localPosition = lastSlot.localPosition;
            newWeaponSlot.transform.localEulerAngles = lastSlot.localEulerAngles;
            newWeaponSlot.transform.localScale = lastSlot.localScale;
            if(lastSlot.GetComponent<SpriteRenderer>() != null){
                var spr = newWeaponSlot.AddComponent<SpriteRenderer>();
                spr.sortingOrder = lastSlot.GetComponent<SpriteRenderer>().sortingOrder;
            }
        }
        Array.Resize(ref weapons, weapons.Length + 1);
    }

    public int WeaponCount(){
        var ret = 0;
        if(weapons != null) {
            for(var i = 0; i < weapons.Length; ++i){
                if(weapons[i]) {
                    ++ret;
                }
            }
        }
        return ret;
    }

    public int MaxWeaponCount(){
        return weapons.Length;
    }

    public int WeaponSlots() {
        return weapons != null ? weapons.Length : 0;
    }

    public void EnlargeWeaponSlot(int size){
        if(weapons == null) {
            weapons = new RGWeapon[size];
        }
        else {
            System.Array.Resize(ref weapons, size);
        }
    }

    public void DropWeapon(RGWeapon weapon){
        if(weapon != null){
            spriteRenderers = null; //重置
            for(var i = weapons.Length - 1; i >= 0; --i) {
                if(weapons[i] == weapon) {
                    weapon.DropWeapon(1);
                    weapons[i] = null;
                    if (weapon == front_weapon) {
                        front_weapon = null;
                    }
                    break;
                }
            }
        }
    }

    public virtual void Activate() {

    }
    public virtual void InActivate() {

    }
    bool CheckEnergyEnough() {
        if (controller.role_attribute.energy < GetConsume()) {
            return false;
        }
        foreach (var weapon in weapons) {
            if (weapon && controller.role_attribute.energy < weapon.realConsume) {
                return false;
            }
        }
        return true;
    }
    public override void OnEnergyRunout() {
        base.OnEnergyRunout();
        foreach (var weapon in weapons) {
            if(weapon) {
                weapon.SetAttack(false);
            }
        }
    }
    /// <summary>
    /// 控制所有SpriteRenderer的显示
    /// </summary>
    /// <param name="isEnabled">是否显示</param>
    public void SetSpriteRenderer(bool isEnabled) {
	    if (spriteRenderers == null) {
		    spriteRenderers = GetComponentsInChildren<SpriteRenderer>();
	    }
	    foreach (var spriteRenderer in spriteRenderers) {
		    spriteRenderer.enabled = isEnabled;
	    }
    }

    public void OnWeaponsAttackStart() {
	    attackStartCount += 1;
	    if (attackStartCount != stateListenerCount) return;
	    attackStartCount = 0;
	    if (onWeaponAttackStart != null) {
		    onWeaponAttackStart();
	    }
    }
    public void OnWeaponAttackFinished() {
	    attackFinishedCount += 1;
	    if (attackFinishedCount != stateListenerCount) return;
	    attackFinishedCount = 0;
	    if (onWeaponAttackFinished != null) {
		    onWeaponAttackFinished();
	    }
    }
}
