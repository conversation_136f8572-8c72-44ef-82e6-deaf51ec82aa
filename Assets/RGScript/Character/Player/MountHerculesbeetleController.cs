using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 机甲 - 独角仙
/// </summary>
public class MountHerculesbeetleController : FlyMountController {
    public AudioClip flyClip;
    public List<Sprite> markSprites;
    private static readonly int AtkT = Animator.StringToHash("atk_t");
    private static readonly int Flying = Animator.StringToHash("flying");
    private bool _isFlying = false;
    private bool _canDropdown = false;
    private SpriteRenderer _markSR;
    private Coroutine _checkCoroutine;
    private MountSpecialHandAttackOneShotWithCD _mountSpecialAttack;
    public bool IsFlying => _isFlying;
    private int _checkLayerMask;
    private Vector2 _checkSize = new Vector2(1.1f, 1.1f);
    private float _flyingOffset = 2f;

    // 被动恢复hp
    private const float RECOVER_COOLDOWN = 40f;
    private Coroutine _recoverCoroutine = null;

    protected override void Start() {
        base.Start();
        _markSR = transform.Find("mark").GetComponent<SpriteRenderer>();
        _markSR.gameObject.SetActive(false);
        if (hand.front_weapon == null) {
            hand.onSetupCompleted += OnHandSetUpCompleted;
        } else {
            OnHandSetUpCompleted();
        }

        _checkLayerMask = PhysicsUtil.StaticWallMask | PhysicsUtil.DefaultMask;
    }

    private void OnHandSetUpCompleted() {
        hand.front_weapon.onAttackStart += OnNormalAtkStart;
        DoFlyListen();
    }

    private void DoFlyListen() {
        if (hand.front_weapon is GunHerculesbeetle gunHerculesbeetle) {
            gunHerculesbeetle.onBeginFly += OnBeginFly;
        }
    }

    public override void SetUp(RGController controller) {
        base.SetUp(controller);
        ResetDriverOffset();
    }

    private void ResetDriverOffset(float timeout = 0.5f) {
        StartCoroutine(DelayResetOffset(timeout));
    }

    private IEnumerator DelayResetOffset(float timeout) {
        var time = 0f;
        while (time <= timeout) {
            time += Time.deltaTime;
            DoResetOffset();

            yield return null;
            if (this == null) yield break;
        }
    }

    private void DoResetOffset() {
        var mountImg = transform.Find("img");
        var pos = new Vector2(drivePosition.transform.localPosition.x, mountImg.localPosition.y);
        hpBar.SetDriverOffset(pos);
    }

    protected override void OnEnable() {
        base.OnEnable();
        _mountSpecialAttack = (MountSpecialHandAttackOneShotWithCD)MountSpecialAttack;
        _mountSpecialAttack.onReady += OnSpecialAtkReady;
        _mountSpecialAttack.canExecuteSpecialDown += CheckSpecialDown;
    }

    private bool CheckSpecialDown() {
        return driver != null && driver.has_target;
    }

    protected override void OnDisable() {
        if (hand.front_weapon != null) {
            hand.front_weapon.onAttackStart -= OnNormalAtkStart;
            if (hand.front_weapon is GunHerculesbeetle gunHerculesbeetle) {
                gunHerculesbeetle.onBeginFly -= OnBeginFly;
            }
        }

        if (_checkCoroutine != null) {
            StopCoroutine(_checkCoroutine);
        }

        if (_recoverCoroutine != null) {
            StopCoroutine(_recoverCoroutine);
        }

        _mountSpecialAttack.onReady -= OnSpecialAtkReady;
        _mountSpecialAttack.canExecuteSpecialDown -= CheckSpecialDown;
        base.OnDisable();
    }

    protected override void DoSwitchWeapon() {
    }

    private IEnumerator AutoRecoverRoutine() {
        while (true) {
            if (attribute.hp < attribute.max_hp) {
                attribute.RestoreHealth(2);
            }

            yield return new WaitForSeconds(RECOVER_COOLDOWN);
        }
    }

    public override int GetHurt(int damage) {
        var realDamage = base.GetHurt(damage * 4);
        if (realDamage > 0) {
            if (_recoverCoroutine == null) {
                _recoverCoroutine = StartCoroutine(AutoRecoverRoutine());
            }
        }

        return realDamage;
    }

    public override void SetAttack(bool isAttack) {
        if (_isFlying) {
            // 飞行过程中，再次按攻击键。下砸攻击
            if (isAttack) {
                if (_canDropdown) {
                    DropDownAtk();
                } else {
                    RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Error);
                }
            }

            return;
        }

        base.SetAttack(isAttack);
    }

    private Vector2 _driverColPos = Vector2.zero;

    /// <summary>
    /// 骑乘的时候不会直接起飞
    /// </summary>
    /// <param name="controller"></param>
    protected override void DoFlyAfterMount(RGController controller) {
        _driverColPos = driver.bodyCol.transform.localPosition;
        _isFlying = false;
        DoResetOffset();
    }

    /// <summary>
    /// 蓄力后开始起飞
    /// </summary>
    private void OnBeginFly() {
        _isFlying = true;
        anim.SetBool(Flying, true);
        driver.SetFlyable(true);
        driver.role_attribute.speed_rate -= speedRate;
        driver.bodyCol.transform.localPosition = _driverColPos - new Vector2(0, _flyingOffset);
        ResetDriverOffset(0.75f);

        _markSR.gameObject.SetActive(true);
        if (_checkCoroutine != null) {
            StopCoroutine(_checkCoroutine);
        }

        _checkCoroutine = StartCoroutine(CheckDownPointSafe());
        RGMusicManager.Inst.PlayEffect(flyClip);
    }

    /// <summary>
    /// 执行普攻动画
    /// </summary>
    private void OnNormalAtkStart() {
        anim.SetTrigger(AtkT);
    }

    /// <summary>
    /// 普攻动画回调，创建子弹
    /// </summary>
    public void OnNormalAtk() {
        CreateBullet(0);
    }

    /// <summary>
    /// 下落匝地攻击
    /// </summary>
    private void DropDownAtk() {
        DoDropdown();
        CreateBullet(1);
    }

    private void CreateBullet(int index) {
        var bulletInfo = hand.front_weapon.GetBulletInfo(index);
        var damageInfo = hand.front_weapon.GetDamageInfo(index);
        bulletInfo.directionAngle = driver.Facing > 0 ? 0f : -180;
        var tmpObj = BulletFactory.TakeBullet(bulletInfo, damageInfo);
        if (driver.facing <= 0) {
            tmpObj.transform.localScale = Vector3.Scale(tmpObj.transform.localScale, new Vector3(1, -1, 1));
        }

        if (index != 0) {
            tmpObj.transform.position = transform.position;
        }
    }

    private IEnumerator CheckDownPointSafe() {
        while (_isFlying) {
            yield return null;
            CheckCanDropdown();
            _markSR.sprite = markSprites[_canDropdown ? 0 : 1];
        }
    }

    private void CheckCanDropdown() {
        var center = transform.position;
        var hit = Physics2D.OverlapBox(center, _checkSize, 0, _checkLayerMask);
        _canDropdown = hit == null || hit.transform.GetComponentInParent<ItemPlantPot>() == null;
    }

    protected override void UpdateSpecialButton(RGController controller) {
        if (!(controller != null && controller.IsLocalPlayer())) {
            return;
        }

        if (_mountSpecialAttack.isReady) {
            UICanvas.GetInstance().ShowBtnSpecial(0);
        } else {
            UICanvas.GetInstance().ShowBtnSpecial(-1);
        }
    }

    private void OnSpecialAtkReady(bool ready) {
        UpdateSpecialButton(driver);
    }


    private void DoDropdown() {
        if (_isFlying) {
            _isFlying = false;
            anim.SetBool(Flying, false);
            driver.SetFlyable(false);
            driver.role_attribute.speed_rate += speedRate;
            driver.bodyCol.transform.localPosition = _driverColPos;
            ResetDriverOffset();
        }
    }

    protected override void DoLand() {
        DoDropdown();
        base.DoLand();
    }
}