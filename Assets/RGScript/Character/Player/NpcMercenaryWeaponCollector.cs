using cfg.WeaponUpgrade;
using I2.Loc;
using MapSystem;
using RGScript.Data;
using RGScript.Weapon;
using System;
using System.Collections.Generic;
using UnityEngine;

public class NpcMercenaryWeaponCollector : NpcMercenaryController {
    private bool _recovering;
    public int recoveringHp;

    private Dictionary<int,CollectorWeapons> Weapons => DataMgr.ConfigData.Tables.TbCollectorWeapons.DataMap;

    private void OnEnable() {
        SimpleEventManager.AddEventListener<ClearRoomEvent>(OnBattleRoomEnd);
    }

    private void OnDisable() {
        SimpleEventManager.RemoveListener<ClearRoomEvent>(OnBattleRoomEnd);
    }

    private void OnBattleRoomEnd(ClearRoomEvent e) {
        int count = StatisticData.data.GetEventCount(RGGameConst.FIGHT_WITH_WEAPON_COLLECTOR);
        count += 1;
        StatisticData.data.SetEventCount(RGGameConst.FIGHT_WITH_WEAPON_COLLECTOR, count, true);
        if (LogUtil.IsShowLog) {
            LogUtil.Log($"-- FIGHT_WITH_WEAPON_COLLECTOR {count}");
        }
        AchieveCheckers.CheckWeaponCollector();
    }

    public void SetUpWeapon() {
        var text = ScriptLocalization.Get("npc_weapon_collector_talk_1");
        UICanvas.GetInstance().ShowTextTalk(transform, text, 4f, 1f);
        
        var currentBigLevel = LevelSelector.GetBigLevel(BattleData.data.gameMode, BattleData.data.levelIndex);
        if (!Weapons.TryGetValue(currentBigLevel, out var weapons)) {
            return;
        }

        var count = weapons.WeaponName.Count;
        var index = rg_random.Range(0, count);
        var weaponName = weapons.WeaponName[index];
        var weapon = WeaponFactory.CreateWeaponNormalSkin(weaponName, emWeaponSource.LevelNpc);
        var rgWeapon = weapon.GetComponent<RGWeapon>();
        rgWeapon.SetWeaponEvolved(true);
        

        if (hand && rgWeapon) {
            hand.SetWeapon(rgWeapon, false);
            hand.transform.localPosition = new Vector3(0, 0.8f, 0);
            ResetIsCharge(rgWeapon);
            is_melee = hand.front_weapon.IsMelleIndeed();
            DealWeapon(rgWeapon);
            
        }

        atk_cd = currentBigLevel switch {
            1 => 1.5f,
            2 => 1f,
            3 => 0.5f,
            _ => 1f
        };
        
        can_atk = true;
    }

    private void DealWeapon(RGWeapon  weapon) {
        if (weapon is WeaponBasaltDark weaponBasaltDark) {
            weaponBasaltDark.WeaponSpecial(true);
        }
    }
    
    public override void GetHurt(HurtInfo hurtInfo) {
        if (!dead && !attribute.isProtectedFromDamage) {
            UICanvas.GetInstance().ShowTextHurt(transform, hurtInfo, 2, gameObject.name);
            role_attribute.RestoreHealth(-hurtInfo.Damage);
            body_render.material.EnableKeyword("HIT_ON");
            if (role_attribute.hp <= 0) {
                role_attribute.hp = 1;
                gameObject.GetComponent<Collider2D>().enabled = false;
                atk_rate = 0;
                hpBar.UpdateHpBar(role_attribute.hp,role_attribute.max_hp,true);
                hpBar.TurnColor(UIHpBar.red);
                _recovering = true;
            } else {
                StartHitTrigger(0.5f);
            }

            Invoke("HitBack", 0.05f);
        }
    }

    protected override void FixedUpdate() {
        if (awake) {
            if (inertial_vel > 1f) {
                rigibody.velocity = force_direction.normalized * inertial_vel;
                inertial_vel *= friction;
            } else {
                if (dead) {
                    rigibody.velocity = Vector2.zero;
                    var colliderTransform = transform.Find("collider");
                    if (colliderTransform) {
                        var bodyCol = colliderTransform.GetComponent<CircleCollider2D>();
                        if (bodyCol) {
                            if (!bodyCol.enabled) {
                                awake = false;
                            } else {
                                bodyCol.enabled = false;
                            }
                        }
                    }
                } else {
                    rigibody.velocity = move_direction.normalized * role_attribute.speed *
                                        (1 + role_attribute.speed_rate);
                }
            }

            if (!has_target && master_tf) {
                if (Vector2.Distance(transform.position, master_tf.position) > max_follow_distance) {
                    transform.position = master_tf.position;
                } else if (Vector2.Distance(transform.position, master_tf.position) < min_follow_distance) {
                    move_direction = Vector2.zero;
                    anim.SetBool("run", false);
                }
            }

            FixedRotation();
        }

        if (!dead && null != target_obj && can_atk && has_target &&
            Vector2.Distance(target_obj.position, transform.position) < atk_range) {
            ShootReflection();
        }

        if (_recovering) {
            can_atk = false;
            gameObject.GetComponent<Collider2D>().enabled = false;
            hpBar.TurnColor(UIHpBar.red);
            state = 0;
            ReplyingHP();
        }
        
        if(weaponSpecialHandle != null){
            weaponSpecialHandle.FixedUpdate(Time.fixedDeltaTime);
        }
    }

    public override void ReplyingHP() {
        if (role_attribute.hp < role_attribute.max_hp) {
            this_reply_time += Time.deltaTime;
            if (this_reply_time >= reply_time2 + reply_time1 && _recovering) {
                role_attribute.hp += recoveringHp;
                this_reply_time = reply_time1;
                if (role_attribute.hp >= role_attribute.max_hp) {
                    Recovered();
                }

                role_attribute.HpChanged();
                hpBar.UpdateHpBar(role_attribute.hp,role_attribute.max_hp,true);
            }
        }
    }

    protected override void Recovered() {
        base.Recovered();
        gameObject.GetComponent<Collider2D>().enabled = true;
        _recovering = false;
        atk_rate = 8;
        state = 1;
        hpBar.TurnColor(UIHpBar.blue);
        can_atk = true;
    }
}
