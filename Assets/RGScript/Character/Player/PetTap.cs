using UnityEngine;
using Sirenix.OdinInspector;

public class PetTap : MonoBehaviour {
    public Sprite tap;
    public Sprite tapPressed;
    public AudioClip tapAudio;
    SpriteRenderer spriteRenderer;
    bool isPressed;
    bool skinValid = true;
    BoxCollider2D click_collider;
    bool touch_down = false; //标记当前是否按下
    private RGPetController controller;

    void Start() {
        spriteRenderer = transform.parent.Find("img/body").GetComponent<SpriteRenderer>();
        click_collider = GetComponent<BoxCollider2D>();
        controller = transform.parent.GetComponent<RGPetController>();
    }

    void Update() {
        if (skinValid) {
            Sprite sprite = isPressed ? tapPressed : tap;
            if (spriteRenderer.sprite != sprite) {
                spriteRenderer.sprite = sprite;
            }
        }

        CheckClick();
    }

    void CheckClick() {
        Vector3 tmp_vec3 = Vector3.zero;
        Vector3 pos = Vector3.zero;
        if (null == Camera.main) {
            return;
        }

        foreach (Touch touch in Input.touches) {
            tmp_vec3.x = touch.position.x;
            tmp_vec3.y = touch.position.y;
            pos = Camera.main.ScreenToWorldPoint(new Vector3(touch.position.x, touch.position.x, 10.0f));
            tmp_vec3.x = pos.x;
            tmp_vec3.y = pos.y;
            //Debug.Log(tmp_vec3.ToString());
            if (click_collider.bounds.Contains(tmp_vec3)) {
                OnMouseDown();
                return;
            }
        }

        if (Input.GetMouseButtonDown(0)) {
            touch_down = true;
            pos = Camera.main.ScreenToWorldPoint(new Vector3(Input.mousePosition.x, Input.mousePosition.y, 10.0f));
            tmp_vec3.x = pos.x;
            tmp_vec3.y = pos.y;
            //Debug.Log("mouse_" + tmp_vec3.ToString());
            var bounds2d = Get2DBounds(click_collider.bounds);
            if (bounds2d.Contains(tmp_vec3)) {
                OnMouseDown();
                return;
            }
        } else if (Input.GetMouseButtonUp(0)) {
            touch_down = false;
            return;
        }

        if (!touch_down) {
            OnMouseUp();
        }
    }

    private  Bounds Get2DBounds(Bounds aBounds) {
        var ext = aBounds.extents;
        ext.z = float.PositiveInfinity;
        aBounds.extents = ext;
        return aBounds;
    }

    [Button(Name = "UnLockPet")]
    public void UnLockPet() {
        RGSaveManager.Inst.PetUnLock(8);
    }

    [Button(Name = "TapDown")]
    public void TapDown() {
        OnMouseDown();
    }

    [Button(Name = "TapUp")]
    public void TapUp() {
        OnMouseUp();
    }

    private void OnMouseDown() {
        Debug.Log("OnMouseDown");
        isPressed = true;
        RGMusicManager.GetInstance().PlayEffect(tapAudio);
        controller?.OnTapDown();
    }

    private void OnMouseUp() {
        //Debug.Log("OnMouseUp");
        isPressed = false;
        controller?.OnTapUp();
    }


    public void OnSkinChanged(int index) {
        skinValid = index == 8;
        if (index != 8) {
            Destroy(gameObject);
        }
    }
}