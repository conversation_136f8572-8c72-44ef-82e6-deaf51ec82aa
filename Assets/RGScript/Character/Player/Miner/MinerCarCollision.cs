using UnityEngine;

public class MinerCarCollision : RGSwordBuffTrigger {
    private MinerCar _minercar;

    private void Start() {
        _minercar = GetComponentInParent<MinerCar>();
    }

    public override void OnHitEnemy(bool isCritical, int dmg, RGEController enemy, GameObject hitObj) {
        if (_minercar && _minercar.enemyOnCar) {
            if (_minercar.enemyOnCar == enemy) {
                return;
            }
        }
        
        base.OnHitEnemy(isCritical, dmg, enemy, hitObj);
    }
}
