using Activities.BugFeatures.Scripts;
using Activities.Fire.Scripts;
using Activities.Zongzi.Scripts;
using Cicada;
using Com.LuisPedroFonseca.ProCamera2D;
using CustomFactor;
using DG.Tweening;
using GG.Extensions;
using I2.Loc;
using IronTide;
using ModeDefence;
using ModeLoopTravel.Scripts;
using Newtonsoft.Json;
using Papa.Util;
using RGScript.Battle;
using RGScript.Character;
using RGScript.Data;
using RGScript.Item.RoomItem;
using RGScript.Manager.Factory;
using RGScript.Manager.Message;
using RGScript.Manager.SDK;
using RGScript.PlayerBuff;
using Sirenix.OdinInspector;
using SoulKnight.Runtime.Skill;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Random = UnityEngine.Random;
using RGScript.Map;
using RGScript.Net.StateSync.Core;
using RGScript.Net.StateSync.Objects;
using RGScript.Other.InputControl;
using TimerPool = RGScript.Util.TimerUtil.TimerPool;
using HutongGames.PlayMaker.Actions;

// ReSharper disable MemberCanBeProtected.Global

public class RGController : RGBaseController, OffensiveInterface, MasterInterface {
    [NonSerialized] public RGController attributeDelegate = null; // 属性变更代理
    public bool customAI;
    
    public bool disableInteractWeapon { get; set; }

    public RoleAttributeProxy role_attribute => attribute;

    public event Action onAfterMount;
    public event Action onLand;
    public event Action onAfterPickUpItem;
    public event Action onUseSpecialButton;
    public event Action<float> customUpdateEvent;
    public event Action<RGWeapon> onForceDropWeapon;

    [NonSerialized] public float hitInterval = 0.5f; // 最短受伤间隔
    private Color _skillLevelUpColor = new Color(0, 1, 250f / 255);

    protected int _facing = 1;
    //1 右边，-1 左边
    public override int facing {
        get {
            if (transform_img != null) {
                _facing = Quaternion.Angle(RotLeft, transform_img.rotation) <= 0.1f ? -1 : 1;
            }
            return _facing;
        }
        set {
            if (keepFacing) {
                return;
            }

            if (transform_img != null) {
                transform_img.rotation = value == 1 ? RotRight : RotLeft;
            }
            _facing = value;
        }
    }

    /// <summary>
    /// 英雄离开坐骑事件
    /// </summary>
    public event Action onMountOff;

    private Collider2D _bodyCol;
    public Collider2D bodyCol {
        get {
            if (_bodyCol == null) {
                _bodyCol = transform.Find("collider")?.GetComponent<Collider2D>();
            }
            return _bodyCol;
        }
    }

    public Vector2 RigidBodyPosition { get; set; }
    public Vector2 BodyCenter() {
        if (rigibody == null) {
            if (transform != null) {
                return transform.position;
            }

            Debug.LogError("未找到transform或rigibody");
            return Vector2.zero;
        }
        
        if (bodyCol != null) {
            return rigibody.position + bodyCol.offset.Rotate(rigibody.rotation);
        }

        return rigibody.position;
    }

    private static SkinAudioConfig _skinAudioConfig;

    public static SkinAudioConfig SkinAudioConfig {
        get {
            if (_skinAudioConfig == null) {
                _skinAudioConfig = Resources.Load<SkinAudioConfig>("SkinAudioConfig");
            }

            return _skinAudioConfig;
        }
    }

    public ISpecialItem special_item;

    public Jewelry jewelry;

    public RGMountController mount { get; set; }

    public AudioClip clip_skill;
    public AudioClip clip_hit;

    private List<SkillInfo> _skills;
    public virtual List<SkillInfo> skills {
        get {
            if (_skills != null) {
                return _skills;
            }

            _skills = new List<SkillInfo>();
            
            // Get and clone all
            var skillInfoList = SkillConfigLoader.GetSkillInfoList(GetHeroType().ToInt());
            foreach (var info in skillInfoList) {
                _skills.Add(info.Clone());
            }

            return _skills;
        }
    }

    public SkillInfo skill => skills[skillIndex];
    
    [NonSerialized] public bool in_battle = false;

    internal bool move_invers;

    //是否可以捡东西
    public bool in_item {
        get {
            return item_tf != null;
        }
    }

    //处于技能状态
    internal SpriteRenderer shadow_lock;
    protected Transform item_tf;
    public Transform ItemTransform => item_tf;

    /// <summary>
    /// 角色拾取物变更
    /// </summary>
    public event Action OnInteractingItemChange;

    public UsableItemInterface usableItem;

    internal Transform camera_focus;

    //刷新搜索敌人的时间
    float seach_time;
    
    internal bool canMount = true; //可以上坐骑
    

    /// <summary>
    /// 角色攻击事件(如手刀)
    /// </summary>
    public event Action OnRoleAttack;

    /// <summary>
    /// 角色攻击停止
    /// </summary>
    public event Action OnRoleAttackStop;

    /// <summary>
    /// 武器攻击事件
    /// </summary>
    public event Action OnWeaponAttack;

    /// <summary>
    /// 武器攻击停止
    /// </summary>
    public event Action OnWeaponStop;

    /// <summary>
    /// 角色施放技能事件
    /// </summary>
    public event Action OnRoleSkillStart;

    public event Action afterSkillStart;

    /// <summary>
    /// 机甲攻击事件
    /// </summary>
    public event Action OnMountAttack;


    /// <summary>
    /// 角色特殊按键事件
    /// </summary>
    public event Action OnRoleSpecial;

    
    /// <summary>
    /// 角色被控制瞄准方向（手柄）的事件，目前只在赛季模式有用
    /// </summary>
    public event Action<MovementInput> onRoleAim;

    /// <summary>
    /// 角色按下攻击键后的事件，不受awake的影响, 返回值bool 代表是否要继续处理后续的攻击操作
    /// </summary>
    public event Func<bool, bool> onBtnAttackClick;

    /// <summary>
    /// 角色护盾为0时触发
    /// </summary>
    public event Action onArmorBreak;
    
    /// <summary>
    /// 角色从0开始增长护盾时触发
    /// </summary>
    public event Action onRestoreArmorFromZero;
    
    public event Action onAtkCritical;

    public bool IsInvulnerable => attribute.isProtectedFromDamage;
    public bool can_hurt = true; // 仅用作Prefab设置的默认配置， 运行时统一CanHurt

    bool _can_hurt = true;
    public bool CanHurt{
        get => _can_hurt;
        set{
            if(_can_hurt != value){
                _can_hurt = value;
                attribute.SetProtectedFromDamage(!_can_hurt, this);
            }
        }
    }

    /// <summary>
    /// 无视爆炸桶伤害
    /// </summary>
    public virtual bool IgnoreExlodeFireBox {
        get {
            return false;
        }
    }

    // 挂载的免疫冰冻buff
    private bool has_immune_ice_buff {
        get => GetComponentInChildren<BuffIceImmune>();
    }

    public void InvokeRestoreArmorFromZero() {
        onRestoreArmorFromZero?.Invoke();
    }

    /// <summary>
    /// 雕像圈的位置
    /// </summary>
    public Vector3 StatuePosition {
        get {
            var localPosition = shadow_lock.transform.localPosition;
            return shadow_lock != null
                ? new Vector3(localPosition.x, localPosition.y + 0.0625f, 0)
                : Vector3.zero;
        }
    }

    [NonSerialized] public readonly LinkedList<GameObject> Statues = new(); //雕像

    public void AddStatue(int index, bool updateBattleData = true) {
        if (Statues.Count >= thisBattleData.MaxStatue() && Statues.Count > 0) {
            var first = Statues.First.Value;
            Statues.RemoveFirst();
            if (first != null) {
                Destroy(first);
            }
        }
        GameObject tempObj = Instantiate(ResourcesUtil.Load(
            "RGPrefab/Effect/buff_statue_" + index + ".prefab"), transform, false) as GameObject;
        tempObj!.name = "buff_statue";
        tempObj.transform.localPosition = StatuePosition;
        tempObj.GetComponent<SpriteRenderer>().enabled = (mount == null || mount.coverController);
        Statues.AddLast(tempObj);
        if (updateBattleData) {
            thisBattleData.SetStatue(index);
        }
    }

    public void ResetStatue(int index) {
        GameObject toRemove = null;
        foreach (var statueObj in Statues) {
            if (statueObj.GetComponent<BuffStatue>() is { } statue && (int)statue.statue == index) {
                toRemove = statueObj;
                break;
            }
        }
        Statues.Remove(toRemove);
        GameObject.Destroy(toRemove);
        AddStatue(index, false);
    }
    
    /// <summary>
    /// 当前的位置
    /// </summary>
    public Vector3 CurPos {
        get {
            if (mount && mount.runAnim) {
                return mount.transform.position;
            }

            return transform.position;
        }
        set {
            if (mount && mount.runAnim) {
                mount.transform.position = value;
            } else {
                transform.position = value;
            }
        }
    }
    
    protected float scale = 1;

    public float Scale => scale;

    public override bool has_target {
        get {
            return target_obj != null;
        }
    }

    public bool is_target_enemy{
        get {
            if (target_obj == null) {
                return false;
            }
            return target_obj.GetComponent<RGEController>() != null;
        }
    }

    /// <summary>
    /// 选择英雄界面被选中时调用的事件
    /// </summary>
    [NonSerialized] public Action OnHeroSelectAnimEnabled;

    private bool _isExceptionFactorInCd;
    private static string[] randomBuffs = {
        "buff_fire",
        "buff_posion",
        "buff_ice",
        "buff_ele"
    };
    
    [NonSerialized]
    public GameObject servant;

    [NonSerialized]
    public GameObject summonGunServant;
    
    [NonSerialized]
    public StateSyncPlayer StateSyncPlayer;
    public Transform Back { get; private set; }
    
    protected virtual void OnEnable() {
        SimpleEventManager.AddEventListener<PlayerBulletPreHitEnemyEvent>(PreHitEnemy);
        SimpleEventManager.AddEventListener<PlayerBulletHitEnemyEvent>(OnHitEnemy);
        SimpleEventManager.AddEventListener<PlayerBulletPenetrateEvent>(OnBulletPenetrate);
        SimpleEventManager.AddEventListener<GetCommonMessageEvent>(OnGetCommonMessage);
        SimpleEventManager.AddEventListener<BattleRoomStartEvent>(OnStartBattle);
        SimpleEventManager.AddEventListener<EnterGameEvent>(OnStartGame);
        SimpleEventManager.AddEventListener<CharacterLongIdleEvent>(OnCharacterLongIdleEvent);
        SimpleEventManager.AddEventListener<ShowPauseWindow>(OnShowPauseWindow);
        SimpleEventManager.AddEventListener<ClearRoomEvent>(OnClearRoom);
        SimpleEventManager.AddEventListener<EnterDungeonRoomEvent>(OnRoomEnter);
        SimpleEventManager.AddEventListener<EnemyDieEvent>(OnEnemyDieEvent);
    }
    
    protected virtual void OnDisable() {
        SimpleEventManager.RemoveListener<PlayerBulletPreHitEnemyEvent>(PreHitEnemy);
        SimpleEventManager.RemoveListener<PlayerBulletHitEnemyEvent>(OnHitEnemy);
        SimpleEventManager.RemoveListener<PlayerBulletPenetrateEvent>(OnBulletPenetrate);
        SimpleEventManager.RemoveListener<GetCommonMessageEvent>(OnGetCommonMessage);
        SimpleEventManager.RemoveListener<BattleRoomStartEvent>(OnStartBattle);
        SimpleEventManager.RemoveListener<EnterGameEvent>(OnStartGame);
        SimpleEventManager.RemoveListener<CharacterLongIdleEvent>(OnCharacterLongIdleEvent);
        SimpleEventManager.RemoveListener<ShowPauseWindow>(OnShowPauseWindow);
        SimpleEventManager.RemoveListener<ClearRoomEvent>(OnClearRoom);
        SimpleEventManager.RemoveListener<EnterDungeonRoomEvent>(OnRoomEnter);
        SimpleEventManager.RemoveListener<EnemyDieEvent>(OnEnemyDieEvent);
    }

    /// <summary>
    /// 统计联机首杀的玩家
    /// </summary>
    /// <param name="e"></param>
    private void OnEnemyDieEvent(EnemyDieEvent e) {
        if (!GameUtil.IsMultiGame()) return;

        if (BattleData.data.firstKillPlayerIndex == 0 && e.killer != null && e.killer.GetComponent<RGController>() is {} rgCtrl) {
            BattleData.data.firstKillPlayerIndex = rgCtrl.p_index;
            if (rgCtrl == this) {
                BattleData.data.continueFirstKillCount++;
                if (BattleData.data.continueFirstKillCount >= 3) {
                    BattleData.data.continueFirst3KillCount++;
                    BattleData.data.continueFirstKillCount = 0;
                }
            } else {
                // 不是自己的话，清空累计值
                BattleData.data.continueFirstKillCount = 0;
            }
        }
    }

    private string _lastClearRoom;
    private float _lastClearRoomTime;
    private void OnClearRoom(ClearRoomEvent e) {
        if (!IsLocalPlayer()) {
            return;
        }
        
        _lastClearRoom = e.roomName;
        _lastClearRoomTime = Time.time;
    }
    
    public void TriggerBattleRecord() {
        if (GameUtil.IsMultiGame() && BattleData.data.gameMode == emGameMode.Normal) {
            StatisticData.data.AddEventCount(RGGameConst.MULTI_FIRST_ENTER_ROOM, 1, false);
        }
    }
    
    private void OnRoomEnter(EnterDungeonRoomEvent e) {
        if (!IsLocalPlayer()) {
            return;
        }
        
        if (string.IsNullOrEmpty(_lastClearRoom) || _lastClearRoom == e.roomName) {
            return;
        }

        if (Time.time - _lastClearRoomTime >= RGGameConst.AchievementHurryManTime) {
            return;
        }

        if (GameUtil.IsMultiGame()) {
            return;
        }
        
        BattleData.data.AddMark("AchievementHurryManTimes");
        if (BattleData.data.GetMark("AchievementHurryManTimes") >= 5) {
            AchieveInfos.info.CheckUnlock(AchieveInfos.AchievementType.HurryMan, 0, "", 0);
        }
    }
    
    private void OnShowPauseWindow(ShowPauseWindow e) {
        OnMotivated();
    }
    
    private void OnGetCommonMessage(EventBase e) {
        var temp = e as GetCommonMessageEvent;
        if (temp == null)
            return;
        var message = temp.message;
        
        if (message.type == CommonMessageType.TempWeaponEvoluteSkinData) {
            var data = JsonConvert.DeserializeObject<RGWeapon.WeaponEvoluteSkin>(message.paramString);
            BattleData.data.AddTempEvolvedSkinData($"{data.WeaponName}_{data.NetId}", data);
        }
        
        if (message.intArray == null)
            return;
        
        if (message.intArray.Length != 3)
            return;
        
        if (IsLocalPlayer())
            return;
        
        var netPlayerController = NetControllerManager.Inst.GetNetController(this);
        if (netPlayerController == null)
            return;

        if ((int)netPlayerController.netId != message.intArray[0])
            return;
        
        if (message.type == CommonMessageType.ArmorSync) {
            SyncArmor(message.intArray[1], message.intArray[2]);
        }
    }

    public Vector2 skillDir;
    public virtual void SyncSkillDir(Vector2 dir) { }
    
    private void SyncArmor(int current, int max) {
        attribute.armor = current;
        attribute.MaxArmor_Sync = max;
        var netPlayerController = NetControllerManager.Inst.GetNetController(this);
        if (!netPlayerController) {
            return;
        }

        SimpleEventManager.Raise(new UpdateArmorEvent {
            netId = (int)netPlayerController.netId, percentage = current / (float)max
        });
        if(LogUtil.IsShowLog){LogUtil.Log(
            $"<SyncArmor>Get SyncArmor : netId : {(int)netPlayerController.netId}, percentage : {current / (float)max}");}
    }

    public int fixedSkinIndex = -1;
    private int BattleSkinIndex {
        get {
            if (fixedSkinIndex >= 0) {
                return fixedSkinIndex;
            }
            return thisBattleData.skinIndex;
        }

        set {
            thisBattleData.skinIndex = value;
        }
    }

    private void ReplaceHands() {
        if (!BattleData.data.CompareActivityFactor(emActivityFactor.WeaponMaster)) {
            return;
        }
        
        var hero = (emHero)attribute.c_index;
        if (hero == emHero.Taoist) {
            // 道士不需要替换
            return;
        }

        var hands = transform.Find("img").GetComponentsInChildren<RGHand>();
        foreach (var theHand in hands) {
            theHand.gameObject.AddComponent<RGHandTaoistForAllChars>();
            DestroyImmediate(theHand);
        }

        // 设置浮游root
        var funnelRoot = new GameObject();
        funnelRoot.transform.parent = transform;
        funnelRoot.name = "funnel_root";
        funnelRoot.transform.localPosition = new Vector3(-0.95f, 1.65f, 0);
    }

    protected void AwakeController() {
        if (DataMgr.UnlockConditionData.IsHeroSkinLockedByConfig(GetHeroType(), GetSkinIndex())) {
            Debug.LogError($"[RGController] AwakeController: {GetHeroType()} {GetSkinIndex()} is locked by config");
            return;
        }
        
        InitBase();
        CanHurt = can_hurt;
        rigibody = gameObject.GetComponent<Rigidbody2D>();
        anim = transform.GetComponent<Animator>();
        _hitColliderCache.GetCache(gameObject).enabled = true; // 初始化hitColliderCache

        ReplaceHands();
        hand = transform.Find("img/h1").GetComponent<RGHand>();
        hand.SetController(this);
        Back = transform.Find("img/back");
        camera_focus = transform.Find("img/focus");
        shadow_lock = transform.Find("shadow_lock").GetComponent<SpriteRenderer>();
        if (GetHeroType() != emHero.None && GetHeroType() != emHero.Count) {
            if (NetControllerManager.Inst.playerCount == 0 || DataUtil.IsHeroRoom()) {
                bool _skinUnlocked = false;
                bool loadSkin = true;
                if (IsDemonstrationCharacter) {
                    // 这里不会走，因为Instantiate之后就立马awake调用这里了，因此无论是否在技能演示，IsDemonstrationCharacter都是false
                    _skinUnlocked = true;
                } else if (RGScript.UI.Util.SkillDemonstration.IsShowing) {
                    // 单人模式角色awake的时候，如果在技能演示中，则不干活
                    loadSkin = false;
                    _skinUnlocked = true;
                } else if (role_attribute.c_index >= RGSaveManager.Inst.char_list.Length || BattleSkinIndex >=
                           RGSaveManager.Inst.char_list[role_attribute.c_index].skin_list.Length) {
                    _skinUnlocked = false;
                    BattleSkinIndex = 0;
                } else {
                    _skinUnlocked = DataUtil.GetSkinUnlock((emHero)role_attribute.c_index, BattleSkinIndex);
                    //未解锁则使用默认皮肤
                    // 活动因子：允许未解锁使用皮肤
                    if (!_skinUnlocked && !BattleData.data.CompareActivityFactor(emActivityFactor.RandomSkin)) {
                        BattleSkinIndex = 0;
                    }
                }

                if (Activities.Dream.Scripts.ActivityDreamManager.NeedReplaceJokerSkin(this)) {
                    BattleData.data.skinIndex = 0;
                }
                
                if (loadSkin) {
                    LoadSkin(BattleSkinIndex);
                }
            }
        }

        if (GameUtil.InGameScene) {
            //防止不同存档受到客厅选人的影响
            skillIndex = thisBattleData.skillIndex;
        } else {
            skillIndex = RGSaveManager.Inst.GetSkillIndex(GetHeroType());
        }

        rg_random.SetRandomSeed(RGGameInfo.Inst.SampleRandomSeed);

        if (!IsDemonstrationCharacter) {
            AchieveInfos.info.CheckUnlock(
                AchieveInfos.AchievementType.PlayerRebornVal,
                BattleData.data.GetMark(RGGameConst.PLAYER_REBORN_VAL),
                null,
                emHero.None);
        }

        gameObject.SetCharacterZPos();

        var talkNode = transform.Find("talk");
        if (talkNode) {
            talkNode.gameObject.SetActive(false);
        }

        buffMgr.InitBuffShowList(this.transform);
        _hitTriggerTimerPool.Register(nameof(_hitTriggerTimerPool), 1/30f, true, false, CheckCanHit);
    }

    public bool IsSetUp { get; private set; }

    void CheckAndFixSpecialStatue() {
        if (!BattleData.data.NeedRollStatue()) {
            return;
        }
        RGRandom statueRandom = new RGRandom();
        statueRandom.SetRandomSeed(RGGameInfo.Inst.MapRandomSeed);
        while (thisBattleData.NeedRollStatue()) {
            int newStatueIdx = RandomUtil.GetRandomStatue(statueRandom);
            thisBattleData.statueIndex = newStatueIdx;
            if(LogUtil.IsShowLog){LogUtil.Log($"重新随机雕像id: {thisBattleData.statueIndex}");}
        }
    }

    public bool BindToCameraOnStart { get; set; } = true;

    public bool IsDemonstrationCharacter {
        get {
            return ((RoleAttributePlayer)(role_attribute.Client)).IsDemonstrationCharacter;
        }
        set {
            ((RoleAttributePlayer)(role_attribute.Client)).IsDemonstrationCharacter = value;
        }
    }

    public void BindToCamera(float duration = 0) {
        SetCameraFocus();
        ProCamera2D.Instance.AddCameraTarget(
            camera_focus.GetChild(0),
            1,
            1,
            duration,
            new Vector2(0, 0.5f));
        ProCamera2D.Instance.VerticalFollowSmoothness = 0.25f;
        ProCamera2D.Instance.HorizontalFollowSmoothness = 0.25f;
        if (GameUtil.InGameScene || GameUtil.InHeroRoom) {
            GameUtil.SetLimitCamera(true);
        }
    }

    public void UnbindCamera() {
       ProCamera2D.Instance.RemoveCameraTarget(camera_focus.GetChild(0)); 
    }

    private int _loadedSkinIndex = -1;
    public int GetLoadedSkinIndex => _loadedSkinIndex;

    private CreationRequest _creationRequest;
    private ProductSkin _productSkin;

    private string MakeSkinAbName(int index) {
        return DataUtil.GetSkinAbName(GetHeroType(), index);
    }

    protected virtual void OnPreSkinLoaded() {
        bodyRenderer.transform.localPosition = new Vector3(0, 0, 0);
    }
    
    protected virtual void OnPostSkinLoaded() {
    }
    
    protected void CreateSpecialShowEffect() {
        if (DataUtil.IsHeroRoom() || DataUtil.IsMultiRoom()) {
            return;
        }

        if (!awake || IsDemonstrationCharacter) {
            return;
        }

        var config = DataMgr.ConfigData.Tables.TbSkinShowEffectConfig.DataMap;
        if (!config.TryGetValue($"c_{GetHeroType().ToInt()}_s_{GetSkinIndex()}", out var effectConfig)) {
            return;
        }

        var effectPath =
            $"Skin/Character/{GetHeroType()}/Skin_{GetSkinIndex()}/{effectConfig.EffectName}.prefab";
        var effectProto = ResourcesUtil.Load<GameObject>(effectPath);
        Instantiate(effectProto, transform.position, Quaternion.identity);
    }
    
    public bool SkinLoaded { get; private set; }
    
    public bool LoadSkin(int index, Action onCompleted = null) {
        if (GetHeroType().Equals(emHero.Count)) {
            return false;
        }
        
        if (!gameObject.activeSelf) {
            return false;
        }
        
        if (_loadedSkinIndex == index) {
            // Skin in used
            return false;
        }
        
        var skinFactory = FactoryManager.Instance.GetFactory<SkinFactory>();
        SkinLoaded = false;
        
        if (_loadedSkinIndex >= 0) {
            UnLoadSkin();
        }
        _loadedSkinIndex = index;
        
        _creationRequest = skinFactory.Create(MakeSkinAbName(index), productSkin => {
            _productSkin = productSkin;
            
            OnPreSkinLoaded();
            anim.enabled = true;
            anim.runtimeAnimatorController = productSkin.AnimatorController;
            onCompleted?.Invoke();
            OnPostSkinLoaded();
            CreateSpecialShowEffect();
            SkinLoaded = true;
        });

        return true;
    }

    private void UnLoadSkin() {
        _creationRequest?.Cancel();
        _creationRequest = null;
        _productSkin?.DestroyProduct();
        _productSkin = null;
    }

    [NonSerialized] public bool setupWeapon = true;
    
    private RGWeapon[] RegisterInitWeapons(List<BattleData.WeaponInBattle> weapons) {
        RGWeapon[] createdWeapons = null;
        StateSynchronizer.WithRegStaticObjectDisabled(() => {
            createdWeapons = hand.SetUpWeapon(weapons);
        });
        
        if (IsDemonstrationCharacter) {
            return createdWeapons;
        }
        
        // ReSharper disable once ForeachCanBePartlyConvertedToQueryUsingAnotherGetEnumerator
        foreach (var weapon in createdWeapons) {
            var stateSyncRgWeapon = weapon.GetComponent<StateSyncRgWeapon>();
            if (stateSyncRgWeapon) {
                stateSyncRgWeapon.RegisterStaticObjectAsInitWeapon();
            }
        }

        return createdWeapons;
    }

    public override void ResetAttributeProxy() {
        base.ResetAttributeProxy();
        if (GameUtil.IsDefenceMode()) {
            SetLevel(ModeDefenceData.Data.player_level, false);
        }

        if (thisBattleData.HasBuff(emBuff.HealthRegen)) {
            attribute.HealthRegen.AddModifier(1, AttributeModifierType.Flat, emBuff.HealthRegen);
        }
    }
    
    //初始化角色数据
    public virtual void SetUpChar() {
        RGGetPath.RegisterTransform(transform);
        
        attribute.SkillLevel.OnModify += OnSkillLevelChanged;

        role_attribute.SetUpChar();

        ((RoleAttributePlayer)role_attribute.Client).OnSkillReady += UpdateShadowLock;
        ((RoleAttributePlayer)role_attribute.Client).OnSkillReady += ShowSkillCDEffect;

        hand.SetController(this);
        if (thisBattleData.CompareFactor(emBattleFactor.Huge)) {
            float deltaScale = RGGameConst.CHALLENGE_HUGE_SCALE;
            ChangeScale(deltaScale, 0.5f);
        }

        if (thisBattleData.CompareFactor(emBattleFactor.Tiny)) {
            float deltaScale = RGGameConst.CHALLENGE_TINY_SCALE;
            ChangeScale(deltaScale, 0.5f);
        }

        if (IsDemonstrationCharacter) {
            p_index = -1;
        } else if (GameUtil.IsMultiGame() && IsLocalPlayer()) {
            p_index = NetControllerManager.Inst.localNetId;
        }

        if (NetControllerManager.Inst.playerCount == 0 || DataUtil.CurIsTargetScene(RGGameConst.SCENE_HERO_ROOM)) {
            if (BattleData.data.IsLoopTravel && thisBattleData.jewelry_item == JewelryItem.ReplaceWeapon) {
                hand.fixedBackMaxWeapon = 0;
            }
        } else {
            if (BattleData.data.IsLoopTravel && thisBattleData.jewelry_item == JewelryItem.ReplaceWeapon) {
                hand.fixedBackMaxWeapon = 0;
            }
        }

        // 创建武器 皮肤 雕像 饰品
        if (NetControllerManager.Inst.playerCount == 0 || DataUtil.CurIsTargetScene(RGGameConst.SCENE_HERO_ROOM)) {
            if (setupWeapon) {
                var createdWeapons = RegisterInitWeapons(thisBattleData.weapons);
                SimpleEventManager.Raise(new AfterInitCharacterWeaponsEvent {
                    character = this,
                    createdWeapons = createdWeapons
                });
            }
            CheckAndFixSpecialStatue();
            if (thisBattleData.statueIndex != 0) {
                AddStatue(thisBattleData.statueIndex, false);
            }

            for (int i = 0; i < thisBattleData.additionStatues.Count; i++) {
                int statusIndex = thisBattleData.additionStatues[i];
                float scaleStep = thisBattleData.additionStatues.Count > 4
                    ? 0.4f / thisBattleData.additionStatues.Count
                    : 0.1f;
                float statueScale = 1f + scaleStep * i;
                GameObject temp_obj4 = Instantiate(
                    ResourcesUtil.Load(
                        $"RGPrefab/Effect/buff_statue_{statusIndex}.prefab")) as GameObject;
                temp_obj4.transform.localScale = new Vector3(statueScale, statueScale, 1);
                temp_obj4.name = "buff_statue";
                temp_obj4.transform.SetParent(transform, false);
                temp_obj4.transform.localPosition = StatuePosition;
                temp_obj4.GetComponent<SpriteRenderer>().enabled = !mount || mount.coverController;
                Statues.AddLast(temp_obj4);
            }

            if (mount) {
                mount.ResetWeapons(true);
                shadow_lock.enabled = mount.coverController;
            }

            this.WearJewelry(thisBattleData.jewelry_item);
        } else {
            if (setupWeapon) {
                RegisterInitWeapons(thisBattleData.weapons);
                SimpleEventManager.Raise(new AfterInitCharacterWeaponsEvent { character = this });
            }

            LoadSkin(thisBattleData.skinIndex);

            if (thisBattleData.statueIndex != 0) {
                AddStatue(thisBattleData.statueIndex, false);
            }

            for (int i = 0; i < thisBattleData.additionStatues.Count; i++) {
                int statusIndex = thisBattleData.additionStatues[i];
                float scaleStep = thisBattleData.additionStatues.Count > 4
                    ? 0.4f / thisBattleData.additionStatues.Count
                    : 0.1f;
                float statueScale = 1f + scaleStep * i;
                GameObject temp_obj4 = Instantiate(ResourcesUtil.Load(
                    $"RGPrefab/Effect/buff_statue_{statusIndex}.prefab")) as GameObject;
                temp_obj4.transform.localScale = new Vector3(statueScale, statueScale, 1);
                temp_obj4.name = "buff_statue";
                temp_obj4.transform.SetParent(transform, false);
                temp_obj4.transform.localPosition = StatuePosition;
                Statues.AddLast(temp_obj4);
            }

            if (mount) {
                mount.ResetWeapons(true);
                shadow_lock.enabled = mount.coverController;
            }

            this.WearJewelry(thisBattleData.jewelry_item);
        }

        if (UICanvas.Inst) {
            UICanvas.Inst.UpdatePauseInfo();
        }

        //界面调整
        if (!mount) {
            shadow_lock.enabled = true;
        }

        UpdateStateBar();
        AddDelegate();

        if (IsLocalPlayer() && BindToCameraOnStart) {
            BindToCamera();
            UICanvas.GetInstance().UpdateSkillMask(1, 1, skillInfo.hasMultiCount ? skillInfo.maxCount : -1,
                skillInfo.skillType);
        }

        HandleMultiSkillBuff();
        if (BattleData.data.gameMode == emGameMode.Defence) {
            attribute.SkillLevel.AddModifier(
                Mathf.Clamp(ModeDefenceData.Data.skill_level, 0, 15),
                AttributeModifierType.Flat,
                this);
        }

        if (BattleData.data.HasActivityEnabled(ActivityFireManager.TAG)) {
            var skillLevel = DataMgr.ActivityFireData.GetBuySkillLevelValue();
            if (skillLevel > 0) {
                LastSkillLevel = Mathf.Clamp(skillLevel, 0, ActivityFireData.Max_Skill_Level);
                attribute.SkillLevel.AddModifier(
                    LastSkillLevel,
                    AttributeModifierType.Flat,
                    this);
            }
        }

        if (BattleData.data.IsSandboxEditing) {
            SetFlyable(SettingData.data.sandboxFlyable, false);
        }

        if (!DataUtil.IsHeroRoom()) {
            if (GameUtil.IsMultiGame()) {
                var netCtrl = NetControllerManager.Inst.GetNetController(this);
                if (netCtrl && null != netCtrl.battleData) {
                    SetPlayerDatas(netCtrl.battleData.playerDicDatas);
                }
            } else {
                SetPlayerDatas(thisBattleData.playerDicDatas);
            }
        }
        var battleData = GameUtil.GetBattleData(this);
        if (battleData.HasBuff(emBuff.NextLevelRestoreHP)) {
            var restoreHp = Mathf.Max(1, attribute.max_hp * 0.1f);
            attribute.RestoreHealth((int)restoreHp);
        }

        if (battleData.HasBuff(emBuff.NextLevelRestoreEnergy)) {
            var restoreEnergy = Mathf.Max(1, attribute.max_energy * 0.3f);
            attribute.RestoreEnergy((int)restoreEnergy);
        }

        if (battleData.HasBuff(emBuff.SpeedUpWithCritic)) {
            _buff36LastPosition = rigibody.position;
            Invoke(nameof(UpdateSpeedUpWithCritic), 0);
        }

        SetUpBuffController();

        IsSetUp = true;
        SimpleEventManager.Raise(new AfterCharacterSetupEvent {
            controller = this,
        });

        SimpleEventManager.Raise(new FlushCharacterStatsEvent {
            controller = this
        });

        if (!IsDemonstrationCharacter && !GetComponent<StateSyncPlayer>()) {
            StateSyncPlayer = gameObject.AddComponent<StateSyncPlayer>();
        }
    }

    private PlayerBuffController _playerBuffController;
    void SetUpBuffController() {
        _playerBuffController = new PlayerBuffController(this);
        _playerBuffController.Enable(emBuffType.BulletHitPlayer);
    }

    [NonSerialized] public bool disableSaveWeaponData;
    public virtual List<BattleData.WeaponInBattle> GetWeaponsData() {
        List<BattleData.WeaponInBattle> weapons = new();
        if (disableSaveWeaponData) {
            return weapons;
        }
        var list = GetAllWeapons();
        
        foreach(var w in list) {
            var weaponTags = new List<string>();
            var weaponDataTagArray = w.GetComponents<IWeaponDataTags>();
            foreach (var weaponDataTags in weaponDataTagArray) {
                weaponTags.AddRange(weaponDataTags.CustomTags);
            }

            var data = new BattleData.WeaponInBattle {
                weaponName = w.name, weaponItems = w.GetWeaponItems(),
                level = w.level,
                customTags = weaponTags,
                customValues = w.GetCustomValues(),
                hasPickedUp = w.has_picked,
                source = w.weaponSource,
                atkTimeDuration = w.AtkTimeDuration,
                isEvolvedWeapon = w.IsEvolvedWeapon,
                skinIndex = w.WeaponSkinId,
                lockEvolute = w.LockEvolute,
                lockSkin = w.LockSkin,
                weaponAffixSyncDatas = w.GetWeaponAffixSyncData(),
                customStrings = w.GetCustomStrings(),
            };
            weapons.Add(data);
        }

        if (hand is RGHandTaoist funnelHand) {
            weapons.AddRange(funnelHand.GetWeaponDatas());
        }
        
        return weapons;
    }

    public List<RGWeapon> GetAllWeapons() {
        Transform trHand;

        trHand = transform.Find("img/h1");
        
        var list = new List<RGWeapon>();
        if(trHand != null){
            for (var i = 0; i < trHand.transform.childCount; ++i) {
                var w = trHand.transform.GetChild(i).GetComponent<RGWeapon>();
                if(w && w.CanSaveToBattleData && !w.IsInvisibleWeapon)
                    list.Add(w);
            }
            list.AddRange(trHand.GetComponent<RGHand>().GetBackFunnelWeapons());
        }
        
        // ReSharper disable once InvertIf
        if (Back) {
            for (var i = 0; i < Back.childCount; ++i) {
                var weapon = Back.GetChild(i).GetComponent<RGWeapon>();
                if (weapon && weapon.CanSaveToBattleData && !weapon.IsInvisibleWeapon) {
                    list.Add(weapon);   
                }
            }
        }

        return list;
    }
    
    public void DropAllWeapons() {
        Transform trHand;
        trHand = transform.Find("img/h1");
        
        if (trHand != null && trHand.GetComponent<RGHand>() is {} rgHand) {
            rgHand.DropWeapon();
            rgHand.DropAllBackFunnelWeapons();
        }
        
        // ReSharper disable once InvertIf
        if (Back) {
            for (var i = 0; i < Back.childCount; ++i) {
                var weapon = Back.GetChild(i).GetComponent<RGWeapon>();
                if (weapon != null) {
                    DestroyImmediate(weapon.gameObject);
                }
            }
        }
    }

#if UNITY_EDITOR || INGAME_DEBUG
    /// <summary>
    /// 无敌
    /// </summary>
    public static bool isInvincible = false;
#endif

    private void ResetExceptionFactorCd() {
        _isExceptionFactorInCd = false;
    }

    public GameObject AddBuff(string buffName, GameObject sourceObject) {
        GameObject buff = Instantiate(
            ResourcesUtil.Load<GameObject>(
                $"RGPrefab/Bullet/Buff/{buffName}.prefab"),
            transform.position,
            Quaternion.identity);
        buff.name = buffName;
        buff.transform.parent = transform;
        buff.GetComponent<RGBuff>().is_enemy = false;
        buff.GetComponent<OffensiveInterface>().SetSourceObject(sourceObject);
        return buff;
    }

    private bool CheckExceptionFactor() {
        if (role_attribute.hp <= 0 ||
            !thisBattleData.CompareFactor(emBattleFactor.Exception) ||
            _isExceptionFactorInCd) {
            return false;
        }

        const int exceptionFactorCd = 5; 
        Invoke(nameof(ResetExceptionFactorCd), exceptionFactorCd);
        _isExceptionFactorInCd = true;
        
        return true;
    }

    public override void GetForce(Vector2 dir, float force, bool needLimit = true) {
        if(mount && mount.GetForce(dir, force)){
            return;
        }
        
        if (GameUtil.IsDefenceMode() && needLimit) {
            force = Mathf.Clamp(force, -DefenceModeConfig.Config.MAX_FORCE, DefenceModeConfig.Config.MAX_FORCE);
        }

        if (BattleData.data.IsLoopTravel && needLimit) {
            force = Mathf.Clamp(force, -ModeLoopTravelConfig.Config.MAX_FORCE, ModeLoopTravelConfig.Config.MAX_FORCE);
        }
        
        base.GetForce(dir, force, needLimit);
    }

    
    public event Action<int, GameObject> AfterGetHurt;
    public event Action<HurtInfo> OnGetHurt;
    public event Action AfterGetHurtNonParam;
    [Button(Name = "模拟角色受伤")]
    //受到伤害
    public override void GetHurt(HurtInfo hurtInfo) {
        if (!awake || !CanHurt || IsInvulnerable) {
            return;
        }
        
        if (!IsLocalPlayer()) { 
            StartHitTrigger(gameObject, hitInterval);
            return;
        }

        if (!thisBattleData.getHurtInRoom) {
            thisBattleData.getHurtInRoom = true;
        }
        if (!thisBattleData.getHurtInGame) {
            thisBattleData.getHurtInGame = true;
        }
        var source_object = hurtInfo.Source;
        if (source_object != null && source_object.GetComponent<RGEController>() is { } e) {
            var enemyInflictDamageEvent = new EnemyInflictDamageEvent {
                enemy = e,
                target = this, 
                hurtInfo = hurtInfo
            };
            SimpleEventManager.Raise(enemyInflictDamageEvent);
            hurtInfo = enemyInflictDamageEvent.hurtInfo;
        }

        if (BattleData.data.isBadass) {
            hurtInfo.Damage += 1;
        }

        if (thisBattleData.CompareFactor(emBattleFactor.ExtraHurtDamage)) {
            hurtInfo.Damage += thisBattleData.GetFactorTimes(emBattleFactor.ExtraHurtDamage);
        }

        // 受伤事件
        if (!SendBeforeHurtEventCheckDodge(hurtInfo, out hurtInfo)) {
            return;
        }

        if (!hurtInfo.DamageSubtype.HasFlag(DamageSubtype.True) && !hurtInfo.DamageSubtype.HasFlag(DamageSubtype.IgnoreDefence)) {
            hurtInfo.Damage = Mathf.Max(0, hurtInfo.Damage - role_attribute.finalDefence);
        }

        if (mount) {
            hurtInfo.Damage = Mathf.Max(0, hurtInfo.Damage - mount.defence);
        }

        if (thisBattleData.CompareFactor(emBattleFactor.HardShield) && role_attribute.armor > 0 &&
            (mount == null || !mount.hasHp)) {
            hurtInfo.Damage = Mathf.Min(hurtInfo.Damage, 1 + MapManager.LoopCount);
        }

        //受伤统计
        if (IsLocalPlayer()) {
            BattleData.data.BattleStatementsData.Add(emBattleStatementsType.GetHurt, hurtInfo.Damage);
        }
        
        // 受伤事件2
        var hurtEvent2 = BeforePlayerGetHurtEvent2.UseCache(this, hurtInfo.Source, hurtInfo);
        SimpleEventManager.Raise(hurtEvent2);
        hurtInfo = hurtEvent2.hurtInfo;
        if (hurtEvent2.hurtInfo.Damage <= 0) {//处理BeforePlayerGetHurtEvent2触发的免伤效果，例如冰霜之环
            if (!hurtEvent2.dontTriggerHurtInterval) {
                StartHitTrigger(gameObject, hitInterval);
            }
            return;
        }
        
        if (oneshot_shields.Count > 0) {
            hurtInfo.Damage = 0;
        }

        if(!hurtInfo.DamageSubtype.HasFlag(DamageSubtype.True)){
            role_attribute.ReSetArmorReload();
        }

        if (hurtInfo.Damage > 0) {
            SetHit();
            anim.SetTrigger("hit");
        }
        
        if (oneshot_shields.Count > 0) {
            oneshot_shields[0].EndBuff();
            StartHitTrigger(gameObject, hitInterval);
        } else if (mount && mount.hasHp && !mount.IsInvulnerable) {
            hurtInfo.Damage = mount.GetHurt(hurtInfo.Damage);
            if(hurtInfo.Damage > 0){
                StartHitTrigger(gameObject, hitInterval);
            }
        } else {
            if (!hurtInfo.DontPlayHurtAudio) {
                PlayGetHitEffect();
            }
            
            if (role_attribute.HasArmor && !hurtInfo.DamageSubtype.HasFlag(DamageSubtype.True)) {
                HurtArmor(hurtInfo.Damage, hurtInfo);
            } else {
                HurtHp(hurtInfo.Damage, source_object);
            }
            
            if (CheckExceptionFactor()) {
                String buffName = randomBuffs[rg_random.Range(0, randomBuffs.Length)];
                AddBuff(buffName, source_object);
            }
        }

        SimpleEventManager.Raise(PlayerGetHurtEvent.UseCache(this, hurtInfo.Source, hurtInfo));

        if (hurtInfo.Damage <= 0) {
            return;
        }

        UICanvas.GetInstance().ShowTextHurt(transform, hurtInfo, 2, gameObject.name);
        if (IsLocalPlayer()) {
            InputControl.Inst.ReInputControlHandler?.CallVibration(InputManager.VibrationType.hurt);
        }
        AfterGetHurt?.Invoke(hurtInfo.Damage, source_object);
        AfterGetHurtNonParam?.Invoke();
        OnGetHurt?.Invoke(hurtInfo);
        //受到攻击时解除待机动画
        OnMotivated();
    }

    protected bool SendBeforeHurtEventCheckDodge(HurtInfo hurtInfo, out HurtInfo newHurtInfo) {
        var beforeHurtEv = BeforePlayerGetHurtEvent.UseCache(this, hurtInfo.Source, hurtInfo);
        SimpleEventManager.Raise(beforeHurtEv);
        newHurtInfo = beforeHurtEv.hurtInfo;
        if (beforeHurtEv.hurtInfo.Dodged) {
            StartHitTrigger(gameObject, hitInterval * 0.5f);
            SimpleEventManager.Raise(new PlayerDodgeDamageEvent { hurtInfo = newHurtInfo, controller = this });
            UICanvas.GetInstance().ShowTextHurt(transform, newHurtInfo, 2, gameObject.name);
            return false;
        }
        return true;
    }

    /// <summary>
    /// 受到直接扣除血量的伤害
    /// </summary>
    public virtual void GetHurtHp(HurtInfo hurtInfo) {
        PlayGetHitEffect();
        var damage = hurtInfo.Damage;
        var source_object = hurtInfo.Source;
        role_attribute.ReSetArmorReload();
        SetHit();
        anim.SetTrigger("hit");
        if (damage < 2)
            GameUtil.CameraShake(1);
        else if (damage < 3)
            GameUtil.CameraShake(2);
        else
            GameUtil.CameraShake(3);
        HurtHp(damage, source_object);
        StartHitTrigger(gameObject, hitInterval);
        UICanvas.GetInstance().ShowTextHurt(transform, hurtInfo, 2, gameObject.name);
    }
    
    /// <summary>
    /// 受到只扣除护甲的伤害
    /// </summary>
    public void GetHurtOnlyArmor(int damage, HurtInfo hurtInfo) {
        if (damage > role_attribute.armor) {
            if(LogUtil.IsShowLog){LogUtil.LogError("damage more than armor");}
            return;
        }
        
        PlayGetHitEffect();
        role_attribute.ReSetArmorReload();
        SetHit();
        anim.SetTrigger("hit");
        if (damage < 2)
            GameUtil.CameraShake(1);
        else if (damage < 3)
            GameUtil.CameraShake(2);
        else
            GameUtil.CameraShake(3);
        HurtArmor(damage, hurtInfo);
        StartHitTrigger(gameObject, hitInterval);
        UICanvas.GetInstance().ShowTextHurt(transform, new HurtInfo { Damage = damage }, 2, gameObject.name);
    }

    public void PlayGetHitEffect()
    {
        var hitClip = SkinAudioConfig.GetGetHitClip(GetHeroType(), GetSkinIndex());
        if (hitClip != null)
        {
            RGMusicManager.GetInstance().PlayEffect(hitClip);
        }
        else
        {
            RGMusicManager.GetInstance().PlayEffect(clip_hit);
        }
    }

    public void SetHit(){
        bodyRenderer.material.EnableKeyword("HIT_ON");
    }

    public void HitBack() {
        bodyRenderer.material.DisableKeyword("HIT_ON");
    }

    public virtual void GetGray() {
        foreach (var sprite in transform.Find("img").GetComponentsInChildren<SpriteRenderer>(true)) {
            sprite.material.EnableKeyword("GRAY_ON");
        }
    }

    public virtual void GrayBack() {
        foreach (var sprite in transform.Find("img").GetComponentsInChildren<SpriteRenderer>(true)) {
            sprite.material.DisableKeyword("GRAY_ON");
        }
    }

    public virtual void HurtArmor(int damage, HurtInfo hurtInfo) {
        var armorDamage = damage;
        var evData = BeforePlayerHurtArmorEvent.UseCache(this, hurtInfo, armorDamage);
        SimpleEventManager.Raise(evData);
        armorDamage = evData.finalArmorDamage;

        int hurt_hp_value = role_attribute.armor - armorDamage;
        role_attribute.RestoreArmor(-armorDamage);

        HandleBuffShieldBlast();
        HandleBuffArmorHurtRestoreEnergy();
        
        if (role_attribute.armor <= 0) {
            onArmorBreak?.Invoke();
            SimpleEventManager.Raise(new PlayerShieldBreakEvent {rgCtrl = this});
        }
        var battleData = GameUtil.GetBattleData(this);
        if (hurt_hp_value < 0 && !battleData.HasBuff(emBuff.ShieldBlue)) {
            //护甲不够, 如果有护甲buff则不扣血
            HurtHp(-hurt_hp_value, hurtInfo.Source);
        }
        StartHitTrigger(gameObject, hitInterval);
    }

    [NonSerialized] public GameObject shieldBlastProto;

    protected void HandleBuffShieldBlast() {
        var battleData = GameUtil.GetBattleData(this);
        if (battleData.HasBuff(emBuff.ShieldBlast)) {
            var blastEvent = new BuffShieldBlastEvent {
                Controller = this,
                KeepOriginalEffect = true,
            };
            SimpleEventManager.Raise(blastEvent);

            if (blastEvent.KeepOriginalEffect) {
                //触发碎盾buff
                GameObject bulletPrefab;
                if (shieldBlastProto == null) {
                    bulletPrefab = PrefabManager.GetPrefab(PrefabName.explode_blast);
                } else {
                    bulletPrefab = shieldBlastProto;
                }

                BulletFactory.TakeBullet(
                    new BulletInfo().SetUp(
                        bulletPrefab,
                        gameObject,
                        0,
                        transform.position + Vector3.up * 0.5f,
                        0,
                        camp).SetBulletSize(4),
                    new DamageInfo().SetUp(bulletPrefab, 0, 0, 0, camp)
                );
            }
        }
    }

    protected void HandleBuffArmorHurtRestoreEnergy() {
        if (thisBattleData.HasBuff(emBuff.ArmorHurtRestoreEnergy) && IsLocalPlayer()) {
            // int costArmor = Math.Min(oldArmor, Math.Abs(value));
            int rndRestoreEnergy = Random.Range(0, 100);
            int restoreEnergy = 0;
            if (rndRestoreEnergy < 50) {
                restoreEnergy = Random.Range(
                    RGGameConst.BUFF_ARMOR_HURT_RESTORE_ENERGY_MIN, RGGameConst.BUFF_ARMOR_HURT_RESTORE_ENERGY_MAX);
                Timer.Register(0.4f, false, false, () => {
                    if (null != this) {
                        role_attribute.RestoreEnergy(restoreEnergy, false, true);
                    }
                });
            }
        }
    }
    
    public virtual void HurtHp(int damage, GameObject source_object) {
        var oldHP = attribute.hp;
        role_attribute.RestoreHealth(-damage);
        if (damage > 0) {
            SimpleEventManager.Raise(new CharacterHurtHpEvent {
                controller = this,
                oldHP = oldHP,
                damage = damage
            });
        }

        if (role_attribute.hp <= 0) {
            Dead(source_object);
        } else {
            StartHitTrigger(gameObject, hitInterval);
        }
    }

    protected virtual void SetDeadAnim() {
        anim.SetTrigger("dead");
    }

    public event Action onCharacterDead;
    void CharacterDeadCallbacks() {
        onCharacterDead?.Invoke();
    }

    //死亡
    public void Dead(GameObject sourceObject) {
        if (dead) {
            return;
        }

        if (IsLocalPlayer()) {
            //本地玩家
            BattleData.data.player_dead = true;
            BattleData.data.AddMark(RGGameConst.PLAYER_REBORN_VAL);
            BattleData.data.BattleStatementsData.Add(emBattleStatementsType.Dead);
            
            string sourceObjectName = sourceObject == null ? "" : sourceObject.name;
            RecordKilledByBoss(sourceObjectName);
        }
        BattleData.data.AddMark(RGGameConst.PLAYER_DEAD_COUNT);
            
        var canRebornByBlessing = GameUtil.CanRebornByBlessing(p_index);
        bool realDead = !HasRebornWeapon() && !canRebornByBlessing;
        CancelInvoke();
            
        //播死亡音效
        var deadClip = SkinAudioConfig.GetDeadClip(GetHeroType(), GetSkinIndex());
        if (deadClip != null) {
            RGMusicManager.GetInstance().PlayEffect(deadClip);
        }

        dead = true;
        awake = false;
        StopMove();
        rigibody.isKinematic = true;
        hand.DeadWeapon();
        transform.GetComponent<BoxCollider2D>().enabled = false;
        SetDeadAnim();
        buffMgr.ClearAllBuff();
        ResetExceptionFactorCd();

        if (IsLocalPlayer()) {
            InputControl.Inst.ReInputControlHandler?.CallVibration(InputManager.VibrationType.dead);
        }

        bool needAddDeadCount;
        //单人 或 在客厅准备时
        if (GameUtil.IsSingleGame() || DataUtil.IsHeroRoom()) {
            needAddDeadCount = true;
            if (realDead) {
                if (GameUtil.IsMultiGame()) {
                    BattleData.data.canContinue = false;
                }
                if (BattleData.data.IsSandbox) {
                    //沙盒模式总是能继续游戏
                    BattleData.data.canContinue = true;
                }

                Invoke(nameof(Resurrection), 2f);
                    
                SimpleEventManager.Raise(new OnPlayerDeadInSingleGame {
                    Controller = this,
                });
            } else {
                Invoke(nameof(ForceReborn), 2f);
            }
        } else {
            needAddDeadCount = IsLocalPlayer();
            // 多人
            if (realDead) {
                bool allDead = NetControllerManager.Inst.AllPlayerDead();
                if (allDead && !GameUtil.CanRebornTwice()) {
                    MultiGameManager.Inst.CheckSendGameFail();
                }
            } else {
                CancelInvoke(nameof(ForceReborn));
                Invoke(nameof(ForceReborn), 2f);
            }

            SimpleEventManager.Raise(new OnePlayerDeadEvent {
                netId = p_index,
                isDead = true,
            });
        }

        if (needAddDeadCount) {
            StatisticData.data.AddEventCount("dead", 1, true); //统计死亡次数
        }

        BuffDestroy();
        DeadChild();
        DisableShadow();
        CharacterDeadCallbacks();
        
        if (skillCasting) {
            RoleSkillEnd();
        }
    }

    private static void RecordKilledByBoss(string sourceObjName) {
        if (MapManagerLevel.Instance == null) return;
        var room = MapManagerLevel.Instance.TheRoom;
        if (room == null) {
            return;
        }

        if (room.room_type != 2 || room.process == 0) {
            return;
        }

        var bosses = room.GetComponentsInChildren<RGEBossController>(true);
        switch (bosses.Length) {
            case 0:
                return;
            case 1:
                PlayerSaveData.Inst.last_killed_by_boss_name = bosses[0].enemy_id;
                return;
        }

        if (bosses.Any(boss => boss.enemy_id == sourceObjName)) {
            PlayerSaveData.Inst.last_killed_by_boss_name = bosses[0].enemy_id;
            return;
        }

        // 没有对应的就记录第一个 boss
        PlayerSaveData.Inst.last_killed_by_boss_name = bosses[0].enemy_id;
    }

    public bool HasRebornWeapon() {
        return HasWeapon("weapon_244");
    }

    public void SyncHp(int value) {
        //hp + (v - hp)
        role_attribute.RestoreHealth(value - role_attribute.hp, true);
        if (value <= 0 && role_attribute.CanDie()) {
            role_attribute.hp = 0;
        }

        if (role_attribute.hp <= 0) {
            Dead(null);
        } else {
            if(value < 0)
                StartHitTrigger(gameObject, hitInterval);
        }
    }

    public void SyncEnergy(int value) {
        role_attribute.RestoreEnergy(value - role_attribute.energy, true);
    }

    protected virtual void DeadChild() { }

    //*************************************Update FixedUpdate 操作*****************************************

    public override void AddAdditionalVelocity(Vector2 velocity){
        if(attributeDelegate != null){
            attributeDelegate.AddAdditionalVelocity(velocity);
            return;
        }
        additionVelocity += velocity;
    }

    public virtual void SetFixedVelocity(Vector2 velocity){
        fixedVelocity = velocity;
    }

    public float CurrentSpeed { get; private set; }
    

    public float GetCurrentSpeedValue(bool useMovementInput = true) {
        return GetMoveSpeed() * (useMovementInput ? movementInput.weight : 1);
    }
    
    protected virtual void SetVelocity() {
        //移动选项
        if (awake && !attribute.isDisable && attributeDelegate == null) {
            if (inertial_vel > 1 || movementInput.weight > 0) {
                OnMotivated();
            }

            CurrentSpeed = GetCurrentSpeedValue(true);
            var normalVelocity = move_dir.normalized * CurrentSpeed;
            if (inertial_vel > 1f) {
                if(fixedVelocity == Vector2.zero){
                    rigibody.velocity = force_direction.normalized * inertial_vel * forceLerp +
                                        normalVelocity * (1 - forceLerp) + additionVelocity;
                }
                else{
                    rigibody.velocity = fixedVelocity + additionVelocity;
                }
                inertial_vel *= Mathf.Min(1, friction);
            } else {
                if(fixedVelocity == Vector2.zero){
                    rigibody.velocity = normalVelocity + additionVelocity;
                }
                else{
                    rigibody.velocity = fixedVelocity + additionVelocity;
                }
            }
        } else {
            rigibody.velocity = new Vector2(0, 0);
        }
        additionVelocity = Vector2.zero;
        fixedVelocity = Vector2.zero;
    }
    
    //技能回复，护甲回复
    protected void AttributeUpdate() {
        role_attribute.HpReload();
        role_attribute.ArmorReload();
        SkillReload();
        role_attribute.ClampAttribute();
    }

    protected virtual void SkillReload() {
        role_attribute.SkillReload();
    }

    //搜寻敌人
    protected void SeachUpdate() {
        seach_time += Time.deltaTime;
        if (seach_time >= 0.05f) {
            AutoLock();
            seach_time = 0;
        }
    }

    #region CantMove
    private HashSet<object> _cantMoveSetter = new HashSet<object>();
    public bool CantMove => _cantMoveSetter.Count != 0;
    
    public void ForceResetCantMove() {
        _cantMoveSetter = new HashSet<object>();
    }

    public bool IsCantMoveFlagSet(object setterObj) {
        return _cantMoveSetter.Contains(setterObj);
    }
    
    public void SetCantMove(object setterObj, bool isCantMove) {
        if (setterObj == null) {
            Debug.LogError("SetterObj cant be null!");
            return;
        }

        if (isCantMove) {
            if (_cantMoveSetter.Add(setterObj)) {
                if (_cantMoveSetter.Count != 1) {
                    return;
                }

                // 第一次设置不可动处理
                move_dir = Vector2.zero;
                SetCharacterAnimatorRun(false);
                SetMountAnimatorRun(false);
                return;
            }
            Debug.LogWarning($"{gameObject.name}已被{setterObj}设置过不可移动,请勿重复设置!");
        } else {
            if (_cantMoveSetter.Remove(setterObj)) {
                return;
            }
            Debug.LogWarning($"{gameObject.name}已被{setterObj}设置过可移动,请勿重复设置!");
        }
    }
    #endregion

    protected void SetMountAnimatorRun(bool isRunning) {
        if (mount && mount.anim != null) {
            mount.anim.SetBool(AnimatorParameterRun, isRunning);
        }
    }

    public event Action<Vector2> callbackTryToMove;
    public void TryToMove(Vector2 dir){
        callbackTryToMove?.Invoke(dir);
    }

    public Vector2 aimDirAccordingToMove{get ; private set;}

    public void ResetAimDirAccordingToMove(){
        aimDirAccordingToMove = Vector2.zero;
    }

    public void ResetAimDirAccordingToMove(Vector2 dir){
        aimDirAccordingToMove = dir;
    }

    public override sealed void SetMove(MovementInput input) {
        RoleMove(input);
    }

    //移动摇杆
    public override void RoleMove(MovementInput m) {
        if (awake && !attribute.isDisable) {
            preMoveInput = m;
            if (CantMove){
                TryToMove(m.dir);
                return;
            }
            
            if (m.dir != Vector2.zero) {
                if (move_invers) {
                    m.Inverse();
                }

                movementInput = m;
                latestMoveDir = move_dir = m.dir;
                aimDirAccordingToMove = m.dir.normalized;

                if (mount && mount.runAnim) {
                    mount.RoleMove(m);
                } else {
                    if (anim != null && !anim.GetBool(AnimatorParameterRun)) {
                        SetCharacterAnimatorRun(true);
                        CreateSmoke();
                    }
                }
            } else {
                movementInput.weight = 0;
                if (mount && mount.runAnim) {
                    mount.RoleMove(m);
                } else if (anim.GetBool(AnimatorParameterRun)) {
                    SetCharacterAnimatorRun(false);
                }
            }
            OnRoleMove(m);
        }
        OnRoleMoveIgnoreAttrDisable(m);
        SetCameraFocus();
    }

    public virtual void RoleAim(MovementInput m) {
        if (!awake || attribute.isDisable) {
            return;
        }

        if (m.aimDir != Vector2.zero) {
            latestAimDir = m.aimDir;
            if (mount) {
                mount.RoleAim(m);
            }
        } else {
            if (mount) {
                mount.RoleAim(m);
            }
        }
        onRoleAim?.Invoke(m);
    }

    public float cameraFocusOffsetFactor{get; set;} = 1;
    Vector3 _cameraFocusOffset;
    protected virtual void SetCameraFocus() {
        if (null == camera_focus) {
            return;
        }
        camera_focus.GetChild(0).localPosition =
            new Vector3(4 * movementInput.weight * cameraFocusOffsetFactor, 0, 0);
        camera_focus.GetChild(0).localPosition += _cameraFocusOffset;
    }

    public void SetCameraFocusOffset(Vector2 offset) {
        _cameraFocusOffset = offset;
        SetCameraFocus();
    }

    protected Transform FindTargetThroughBox() {
        return FindTarget(findTargetRange, PhysicsUtil.StaticWallMask);
    }

    protected Vector3 temp_lock_dir = Vector3.zero;
    public Vector3 autoAimDir => Quaternion.Euler(facing * temp_lock_dir) * (facing * Vector3.right);

    public void LostTarget() {
        if (target_obj != null && target_obj.GetComponent<ILockableObject>() is { } lockableObject) {
            lockableObject.OffLock();
        }
        target_obj = null;
    }

    public void LockTarget(Transform target) {
        LostTarget();
        target_obj = target;
        if (target != null) {
            target.GetComponent<ILockableObject>().BeLock();
        }
        SimpleEventManager.Raise(PlayerChangeTargetEvent.UseCache(this, target));
    }

    [NonSerialized] public bool stopAutoAiming;
    [NonSerialized] public bool lockFacing;

    //自动瞄准
    public virtual void AutoLock() {
        if (awake) {
            temp_lock_dir = Vector3.zero;
            //范围内最近单位
            var oldTarget = target_obj;
            if (!stopAutoAiming && !customAI) {
                if (IsLocalPlayer()) {
                    if (!oldTarget || (oldTarget.GetComponent<ILockableObject>() is { } lockableObject &&
                                       lockableObject.CanOffLock(gameObject))) {
                        // 当前目标为空 或 当前目标允许解除锁定 则尝试寻找 新的 或 最近的目标
                        Transform min_obj = FindTarget();
                        if (oldTarget != min_obj) {
                            LockTarget(min_obj);
                        }
                    }

                    if (NetControllerManager.Inst.playerCount > 0 && oldTarget != target_obj) {
                        NetControllerManager.Inst.localController.SyncTarget(target_obj);
                    }
                }
            }

            //调整角色角度
            var aim_dir = aimDirAccordingToMove;
            var facing_dir = aim_dir;
            Transform temp_target = (hand != null && hand.handTarget) ? hand.handTarget : target_obj;
            if(temp_target && !stopAutoAiming){
                var aimPosition = temp_target.position.Vec2() + Vector2.up * 0.5f;
                if(temp_target.GetComponent<ILockableObject>() is ILockableObject lockableObj && lockableObj != null)
                    aimPosition = lockableObj.GetColliderCenter();

                Vector2 sourcePosition = hand != null ? hand.transform.position.Vec2() : transform.position.Vec2();
                aim_dir = (aimPosition - sourcePosition).normalized;
                facing_dir = (aimPosition - transform.position.Vec2()).normalized;
            }

            if(facing_dir.x != 0 && !lockFacing){
                facing = (int)Mathf.Sign(facing_dir.x);
            }
            FixedDirection = aim_dir;
            FixedAngle = Vector2.Angle(aim_dir, new Vector2(facing, 0)) * Mathf.Sign(aim_dir.y);
            if (mount) {
                mount.UpdateHpBarTransform();
            }

            if(hand != null){
                if (hand.NeedLock() || hand.handTarget)
                    hand.transform.localEulerAngles = new Vector3(0, 0, FixedAngle);
                else {
                    hand.transform.localEulerAngles = Vector3.zero;
                    hand.LockedWeaponProcess(FixedAngle);
                }
            }

            if(camera_focus != null){
                camera_focus.localEulerAngles = new Vector3(0, 0, FixedAngle);
            }

            temp_lock_dir = new Vector3(0, 0, FixedAngle);
        }
    }

    public override sealed void SetSpecialButton(bool value) {
        BtnSpecialClick(value);
    }

    public virtual void BtnSpecialClick(bool isDown) {
        if (!awake) {
            return;
        }

        SpecialClick(isDown);
        OnMotivated();
    }
    
    public void BtnFusionClick(bool isDown) {
        if (!awake) {
            return;
        }

        FusionClick(isDown);
        OnMotivated();
    }

    private void SpecialClick(bool isDown) {
        if (isDown)
            onUseSpecialButton?.Invoke();
                
        if (special_item != null && special_item.IsSpecial()) {
            special_item.SpecialItemTrigger(isDown);
        } else if (mount) {
            if (isDown) {
                SimpleEventManager.Raise(HeroSpecialAttackEvent.UseCache(this));
            }
            mount.SpecialClick(isDown);
        } else if (CanTriggerSpecialWeapon) {
            if (isDown) {
                SimpleEventManager.Raise(HeroSpecialAttackEvent.UseCache(this));
            }
            TriggerSpecialWeapon(isDown);
        }
    }

    public bool CanTriggerSpecialWeapon => hand.front_weapon && hand.front_weapon.isSpecialWeapon && hand.front_weapon.gameObject.activeSelf;

    public void TriggerSpecialWeapon(bool isDown) {
        hand.front_weapon.weaponSpecial.WeaponSpecial(isDown);
        RoleSpecialEvent(isDown);
    }

    private void FusionClick(bool isDown) {
        if (!isDown) {
            return;
        }

        if (NetControllerManager.Inst.playerCount == 0 || this.IsLocalPlayer()) {
            if (item_tf && item_tf.GetComponent<RGWeapon>()) {
                item_tf.GetComponent<RGWeapon>().FusionTrigger();
            }
        }
    }

    protected bool btnAtkPressed;
    public bool ButtonAtkPressed => btnAtkPressed;
    public System.Func<bool> preAttack = null;

    private bool InterceptCharacterAttack(bool value) {
        if (RGGameProcess.Inst && RGGameProcess.Inst.modeProcess != null) {
            return RGGameProcess.Inst.modeProcess.InterceptCharacterAttack(this, value);
        }

        return false;
    }
    
    public virtual void RoleAtk(bool value) {
        if (!awake) {
            return;
        }

        OnMotivated();
        if (!value) {
            RoleAttackEvent(false);
        }

        if (in_item && value) {
            TriggerItem();
        } else {
            if (TryUseItem(value)) {
                return;
            }

            if (InterceptCharacterAttack(value)) {
                return;
            }
            
            if (!hand.front_weapon && value && can_cut) {
                UseHandAbility(false);
            } else if (value && hand.CanUseHandCut(this, target_obj, hand)) {
                UseHandAbility(true);
            } else {
                HandAtk(value);
            }
        }
    }

    public virtual void BtnAtkClick(bool value) {
        if (preAttack != null && !preAttack()) {
            return;
        }
        
        btnAtkPressed = value;
        var continueProcessAttackClick = onBtnAttackClick?.Invoke(value);
        if (null != continueProcessAttackClick && !(bool) continueProcessAttackClick) {
            return;
        }
        
        OnMotivated();
        if (mount && !mount.useControllerWeapon) {
            if (mount.canTriggerItem && in_item && value) {
                TriggerItem();
            } else {
                if (!mount.useControllerWeapon) {
                    if (InterceptCharacterAttack(value)) {
                        return;
                    }
                    if (value) {
                        SimpleEventManager.Raise(HeroAttackEvent.UseCache(this));
                    }
                    mount.SetAttack(value);
                    MountAttackEvent(value);
                } else {
                    if (value) {
                        SimpleEventManager.Raise(HeroAttackEvent.UseCache(this));
                    }
                    RoleAtk(value);
                }
            }
        } else {
            DoRoleAtk(value);
        }
    }

    public void DoRoleAtk(bool down) {
        if (down) {
            SimpleEventManager.Raise(HeroAttackEvent.UseCache(this));
        }
        RoleAtk(down);
    }

    public sealed override void SetAttack(bool value) {
        base.SetAttack(value);
        if (StateSyncPlayer) {
            StateSyncPlayer.CommandAttack(value);
        } else {
            BtnAtkClick(value);
        }
    }

    public void RoleSpecialEvent(bool value) {
        if (value) {
            if (OnRoleSpecial != null) {
                OnRoleSpecial();
            }
        }
    }

    public void RoleAttackEvent(bool value) {
        if (null == transform) {
            return;
        }

        if (null == this) {
            return;
        }
        
        if (value) {
            OnRoleAttack?.Invoke();
            SimpleEventManager.Raise(new PlayerAttackEvent{ controller = this });
        } else {
            OnRoleAttackStop?.Invoke();
        }
    }

    public void MountAttackEvent(bool value) {
        if (value) {
            OnMountAttack?.Invoke();
        }
    }

    public void WeaponAttackEvent() {
        OnWeaponAttack?.Invoke();
        SimpleEventManager.Raise(new PlayerAttackEvent{ controller = this });
    }

    public void WeaponStopEvent() {
        OnWeaponStop?.Invoke();
    }

    public HandAbilityInfo HandAbility {
        get {
            if (BattleData.data.IsARAM) {
                return ARAMConfig.Config.characterHandAbilities[Mathf.Clamp(attribute.c_index, 0, ARAMConfig.Config.characterHandAbilities.Count - 1)];
            }
            return CommonConfig.Config.heroHandAbilities[Mathf.Clamp(attribute.c_index, 0, CommonConfig.Config.heroHandAbilities.Count - 1)];
        }
    }

    public Func<GameObject> HandAbilityProxy;
    protected virtual GameObject UseHandAbility(bool hasWeapon) {
        if (HandAbilityProxy != null) {
            return HandAbilityProxy();
        }
        
        GameObject o = null;
        if (hasWeapon) {
            if (Time.timeScale != 0) {
                o = hand.AtkCut();
                if (BattleData.data.IsARAM) {
                    can_cut = false;
                    if (HandAbility.cooldown > 0) {
                        Invoke(nameof(TurnCanCut), HandAbility.cooldown);
                    } else {
                        can_cut = true;
                    }
                }
            }
        } else {
            o = hand.AtkCut();
            can_cut = false;
            if (BattleData.data.IsARAM) {
                if (HandAbility.cooldown > 0) {
                    Invoke(nameof(TurnCanCut), HandAbility.cooldown);
                } else {
                    can_cut = true;
                }
            } else {
                Invoke(nameof(TurnCanCut), 1);
            }
        }

        return o;
    }

    protected bool TryUseItem(bool start) {
        return usableItem != null && usableItem.Use(this, start);
    }

    public virtual void HandAtk(bool val) {
        hand.SetAttack(val);
    }

    protected bool can_cut = true;

    protected void TurnCanCut() {
        can_cut = true;
    }

    #region 技能

    private int _skillIndex;
    // ReSharper disable once InconsistentNaming
    public int skillIndex {
        get {
            if (fixedSkillIndex >= 0) {
                return fixedSkillIndex;
            }

            return _skillIndex;
        }

        set {
            _skillIndex = value;
        }
    }
    public int fixedSkillIndex = -1;

    protected bool IsSkillIndex(int index) {
        return skillIndex == index;
    }
    
    /// <summary>
    /// 技能释放额外升级，0表示没升级，1表示升级。（普通关卡导师升级）
    /// </summary>
    public int SkillExtraUpdate => thisBattleData.HasBuff(emBuff.Tutor) ? 1 : 0;
    
    /// <summary>
    /// 拥有导师技能升级天赋
    /// </summary>
    public bool HasSkillExtraUpdate => thisBattleData.HasBuff(emBuff.Tutor);
    /// <summary>
    /// 技能五星升级
    /// </summary>
    public bool HasSkillStrengthen => attribute.skill_strengthen;

    int _skillCastState;
    public int skillCastState => _skillCastState;
    /// <summary>
    /// 多状态技能的当前状态正在蓄力
    /// </summary>
    public bool MultiStatePreparing;
    /// <summary>
    /// 多状态技能的当前状态可以蓄力
    /// </summary>
    public bool MultiStateCanPrepare;

    public virtual bool IsDragSkill => skillInfo.skillType == emSkillType.Drag ||
                                       skillInfo.skillType == emSkillType.DragMultiCount ||
                                       skillInfo.skillType == emSkillType.DragLastingUseSecond;
    public bool skillCasting{
        get{
            return _skillCastState > 0;
        }
        set{
            _skillCastState = value ? 1 : 0;
        }
    } //技能是否正在释放

    public void SetSkillCastState(int state) {
        _skillCastState = state;
    }

    public virtual bool isDoingSkillAction => skillCasting;

    internal bool aiming {
        get {
            return skillCasting && skillIndex == 0;
        }
    } //是否正在瞄准

    internal float skill_time;

    public virtual void UpdateShadowLock() {
        if (role_attribute is { skill_ready: true }) {
            if (null != shadow_lock) {
                shadow_lock.color = RGGameConst.SKILL_READY_COLOR;
            }
        } else {
            if (null != shadow_lock) {
                shadow_lock.color = RGGameConst.SKILL_UNREADY_COLOR;
            }
        }
    }

    public void UpdateShadowLockCustom(bool ready) {
        shadow_lock.color = ready ? RGGameConst.SKILL_READY_COLOR : RGGameConst.SKILL_UNREADY_COLOR;
    }

    public void ShowSkillCDEffect() {
        if (IsLocalPlayer()) {
            GameObject skill_cd_effect = null;
            if (!transform.Find("skill_cd_effect")) {
                skill_cd_effect = Instantiate(ResourcesUtil.Load<GameObject>(
                    "RGPrefab/Effect/skill_cd_effect.prefab"));
                skill_cd_effect.name = "skill_cd_effect";
                skill_cd_effect.transform.SetParent(transform);
                skill_cd_effect.transform.localPosition = Vector3.up * 0.5f;
            } else {
                skill_cd_effect = transform.Find("skill_cd_effect").gameObject;
            }

            if (skill_cd_effect) {
                skill_cd_effect.GetComponent<Animator>().Play("skill_cd_effect", 0, 0f);
            }
        }
    }

    public void ShowArrowEffect() {
        if (!IsLocalPlayer()) {
            return;
        }

        GameObject arrow_effect = PrefabPool.Inst.Take(PrefabManager.GetPrefab(PrefabName.arrow_effect), transform.position, Quaternion.identity);
        if (null != arrow_effect) {
            arrow_effect.transform.parent = transform;
            arrow_effect.transform.localPosition = Vector3.up * 1.9f;
        }
    }

    public virtual void RoleSkillStartEvent() {
        if (skillCasting || !role_attribute.skill_ready) {
            return;
        }
        
        //有坐骑的情况下返回
        if (mount && !mount.canUseSkill) {
            return;
        }

        InvokeOnRoleSkillStart();
        
        SimpleEventManager.Raise(new HeroUseSkillEvent {
            controller = this, 
            skillIndex = skillIndex,
        });
    }

    protected void InvokeOnRoleSkillStart() {
        if (OnRoleSkillStart != null) {
            OnRoleSkillStart();
        }
    }

    public override sealed void SetSkill(bool value) {
        if (value) {
            BtnSkillDown();
        } else {
            BtnSkillUp();
        }
    }

    #region DragSkill 拖拽技能
    public Vector3 DragSkillOffset => DragSkillWorldOffset;
    protected Vector3 DragSkillWorldOffset;  //拖拽相对于角色的位置
    protected MovementInput DragInput;
    protected DragSkillData DragSkillData { set; get; }

    protected virtual void InitDragSkillBtn() {
        if (IsDragSkill && IsLocalPlayer() && UICanvas.Inst.btnSkill != null) {
            UICanvas.Inst.btnSkill.transform
                .GetComponent<RGJoyBtnSkill>()
                .InstallJoyStick(OnSkillCastDirectionChange);
        }
    }
    public void SetDragSkillOffset(Vector3 pos) {
        DragSkillWorldOffset = pos;
    }
    protected virtual void InitDragSkillData(float skillMaxDistance = 6, float uiMaxDistance = 200, float uiDeadZone = 10) {
        if (IsDragSkill) {
            InitDragSkillBtn();
            DragSkillData = new DragSkillData(skillMaxDistance, uiMaxDistance, uiDeadZone);
        }
    }
    public virtual void OnSkillCastDirectionChange(MovementInput input) {
        if (!awake)
            return;
        if(!attribute.skill_ready)
            return;
        if (DragSkillData == null) {
            return;
        }

        DragInput = input;
        Vector2 dir = input.touchCenterDir;
        var dis = Mathf.Min(dir.magnitude, DragSkillData.UIMaxDistance);
        if ((input.touchPos - input.touchStartPos).sqrMagnitude < DragSkillData.UIDeatZone) {
            return;
        }
        DragSkillWorldOffset =  (Vector3)(dis / DragSkillData.UIMaxDistance * DragSkillData.SkillMaxDistance * dir.normalized);
    }

    protected virtual void OnDragSkillEnd() {
        if (IsDragSkill) {
            if (IsLocalPlayer() && UICanvas.Inst != null && UICanvas.Inst.btnSkill != null) {
                UICanvas.Inst.btnSkill.GetComponent<RGJoyBtnSkill>().UninstallJoyStick();
            }
        }
    }
    #endregion
    
    
    public bool SkillEnable { get; set; } = true;
    public virtual void BtnSkillDown() {
#if UNITY_SWITCH
        if (!InputManager.Inst.IsForceObject(gameObject))return;
#endif
        if (!awake) {
            return;
        }
        // local函数
        void FuncUseSkill(){
            if (!SkillEnable) {
                if (mount) {
                    ProcessSkillWithMount();
                }

                return;
            }
            
            RoleSkillStartEvent();
            
            if (mount && !mount.canUseSkill) {
                ProcessSkillWithMount();
            } else {
                switch (skillInfo.skillType) {
                    case emSkillType.Prepare:
                    case emSkillType.Channel:
                    case emSkillType.PrepareToLasting:
                    case emSkillType.Drag:
                    case emSkillType.DragMultiCount:
                    case emSkillType.DragLastingUseSecond:   
                        RoleSkillStart();
                        break;
                    case emSkillType.MultiState:
                        if (MultiStateCanPrepare) {
                            RoleSkillStart();
                        } else {
                            RoleSkill();
                        }
                        break;
                    default:
                        RoleSkill();
                        break;
                }
            }

            afterSkillStart?.Invoke();
        }

        if(RGGameProcess.Inst == null || RGGameProcess.Inst.modeProcess == null || !RGGameProcess.Inst.modeProcess.InterceptCharacterSkill(this, FuncUseSkill)) {
            FuncUseSkill();
        }
    }

    protected void ProcessSkillWithMount() {
        if (DataUtil.IsMultiRoom()) {
            string key = "multi/multi_room_cant_do_this";
            string content = ScriptLocalization.Get(key);
            if (string.IsNullOrEmpty(content)) {
                content = key;
            }

            UICanvas.GetInstance().ShowTextTalk(transform, content, 2.5f, 3f);
        } else {
            mount.RoleSkill();
        }
    }

    protected void ProceesSkillWithRole() {
        switch (skillInfo.skillType) {
            case emSkillType.Prepare:
            case emSkillType.Channel:
            case emSkillType.PrepareToLasting:
                RoleSkillStart();
                break;
            default:
                RoleSkill();
                break;
        }
    }

    public virtual void BtnSkillUp() {
#if UNITY_SWITCH
        if (!InputManager.Inst.IsForceObject(gameObject))return;
#endif
        void FuncButtonUp(){
            switch (skillInfo.skillType) {
                case emSkillType.Prepare:
                case emSkillType.PrepareToLasting:
                case emSkillType.Drag:
                case emSkillType.DragMultiCount:
                    if (skillCasting) {
                        RoleSkill();
                    }
                    break;
                case emSkillType.Channel:
                    if (skillCasting) {
                        RoleSkillEnd();
                    }
                    break;
                case emSkillType.MultiState:
                    if (MultiStateCanPrepare && MultiStatePreparing) {
                        RoleSkill();
                    }
                    break;
                case emSkillType.DragLastingUseSecond:
                    RoleSkill();
                    break;
            }
        }

        if(RGGameProcess.Inst == null || RGGameProcess.Inst.modeProcess == null || !RGGameProcess.Inst.modeProcess.InterceptCharacterSkillButtonUp(this, FuncButtonUp)) {
            FuncButtonUp();
        }
    }

    public virtual void RoleSkillStart() {
        skillCasting = true;
        skillInfo.time = 0;
    }

    public virtual void RoleSkill() { }

    public virtual void RoleSkillEnd() {
        skillCasting = false;
    }

    public virtual void ForceDestroySkillObj() { }

    public virtual SkillInfo skillInfo {
        get {
            return skills[skillIndex];
        }
    }
    
    public int SkillMaxUseCount() {
        return skillInfo.maxCount + attribute.Client.skillExtraUseTimes;
    }

    public void SetSkillIndex(int index) {
        this.skillIndex = index;
        skillInfo.count = skillInfo.maxCount;
        //role_attribute.skill_cd = skillInfo.cd;
    }
    /// <summary>
    /// 触发雕像效果
    /// </summary>
    public void CastStatue() {
        foreach (var statue in Statues) {
            if (statue != null && statue.GetComponent<BuffStatue>() is { } buffStatue) {
                buffStatue.UseSkill();
            }
        }
    }

    #endregion

    public class KillSomeOneReturnData {
        public bool triggerRestoreHealth;
        public bool triggerRestoreEnergy;
    }

    private const int ChanceToTriggerVampireHp = 9;
    private const int ChanceToTriggerVampireEnergy = 28;
    
    private RGCoin DropBall(Vector3 position, PrefabName prefabName) {
        GameObject ball = Instantiate(PrefabManager.GetPrefab(prefabName), position, Quaternion.identity, RGGameSceneManager.Inst.temp_objects_parent);
        float rx = rg_random.Range(-1f, 1f);
        float ry = rg_random.Range(-1f, 1f);
        const int force = 12;
        var coin = ball.GetComponent<RGCoin>();
        coin.SetTarget(transform);
        coin.GetForce(new Vector2(rx, ry), force);
        coin.alwaysInRange = true;
        return ball.GetComponent<RGCoin>();
    }

    public virtual KillSomeOneReturnData KillSomeOne(object other) {
        var triggerRestoreHealth = false;
        var triggerRestoreEnergy = false;
        SimpleEventManager.Raise(new KillEnemyEvent {controller = this, other = other});
        
        // Health buff
        if (thisBattleData.HasBuff(emBuff.VampireHp)) {
            if (Random.Range(0, 100) < ChanceToTriggerVampireHp) {
                if (other is Component component) {
                    DropBall(component.transform.position, PrefabName.health);
                } else {
                    DropBall(transform.position, PrefabName.health);
                }

                triggerRestoreHealth = true;
            }
        }

        // Energy buff
        if (thisBattleData.HasBuff(emBuff.VampireEnergy)) {
            if (Random.Range(0, 100) < ChanceToTriggerVampireEnergy) {
                if (other is Component component) {
                    DropBall(component.transform.position, PrefabName.energy);
                } else {
                    DropBall(transform.position, PrefabName.energy);
                }

                triggerRestoreHealth = true;
            }
        }

        if (mount is IronTideMechaController mecha && other is RGEController enemy) {
            mecha.OnKill(enemy);
        }

        RGGameProcess.Inst.AccumulateKillCount();

        KillEnemyEvent?.Invoke(this, other);

        if (IsLocalPlayer() &&
            DataUtil.CurIsTargetScene(RGGameConst.SCENE_GAME)) {
            BattleData.data.BattleStatementsData.Add(emBattleStatementsType.Kill);
        }

        return new KillSomeOneReturnData {
            triggerRestoreHealth = triggerRestoreHealth, 
            triggerRestoreEnergy = triggerRestoreEnergy
        };
    }

    public virtual void HurtSomeOne(HurtInfo hurtInfo, GameObject target) {
    }

    public virtual void PickUpItem(Transform temp_tf, bool fusion = false, bool showText = true, bool force = false) {
        hand.PickUpItem(temp_tf, fusion, showText, force);
        SetItemtf(null);
        onAfterPickUpItem?.Invoke();
    }


    private readonly Dictionary<int, float> _canHitDic = new();
    private TimerPool _hitTriggerTimerPool = new();
    private readonly List<int> _needDelList = new();

    void CheckCanHit() {
        var nowLevelLoadTime = Time.timeSinceLevelLoad;
        var canHit = true;
        _needDelList.Clear();
        foreach (var canHitKv in _canHitDic) {
            if (canHitKv.Value > nowLevelLoadTime) {
                canHit = false;
            } else {
                _needDelList.Add(canHitKv.Key);
            }
        }

        foreach (var needDelHash in _needDelList) {
            if (_canHitDic.ContainsKey(needDelHash)) {
                _canHitDic.Remove(needDelHash);
            }
        }

        this.CanHurt = canHit;

        if (this.CanHurt && _hitColliderCache != null && gameObject) {
            var cachedCollider = _hitColliderCache.GetCache(gameObject);
            if (!cachedCollider.isActiveAndEnabled && !dead) {
                cachedCollider.enabled = true;
            }
        } else {
            if (RGGameSceneManager.GetInstance() &&
                RGGameSceneManager.GetInstance().game_state == 1 ||
                IsDemonstrationCharacter) {
                if (_hitColliderCache != null && gameObject) {
                    _hitColliderCache.GetCache(gameObject).enabled = false;
                }
            }
        }
    }

    public const float MaxHitTriggerEnableTime = 99f; 
    /// <summary>
    /// 设置无敌
    /// </summary>
    /// <param name="sourceObject">无敌的来源</param>
    /// <param name="durTime">无敌时间（单位秒）</param>
    public virtual void StartHitTrigger(UnityEngine.Object sourceObject, float durTime) {
        int sourceObjectHash = 0;
        if (null != sourceObject) {
            sourceObjectHash = sourceObject.GetHashCode();
        }
        //无敌结束的时间
        var newHitTriggerEndTime = Time.timeSinceLevelLoad + durTime;
        _canHitDic[sourceObjectHash] = newHitTriggerEndTime; 
        
        
        if (in_item && IsLocalPlayer() && PlayerCanSetItemTf()) {
            SetItemtf(null);
            if (UICanvas.GetInstance() != null) {
                UICanvas.GetInstance().HideObjectInfo();
                UICanvas.GetInstance().HideItemInfo();
            }
        }

        if (RGGameSceneManager.GetInstance().game_state == 1 || IsDemonstrationCharacter) {
            _hitColliderCache.GetCache(gameObject).enabled = false;
        }

        CheckCanHit();
    }

    public virtual void NewEndHitTrigger(UnityEngine.Object sourceObject) {
        StartHitTrigger(sourceObject, 0f);
    }

    /// <summary>
    /// 当前交互物是否可以被重置，目前是为了避免联机复活被StartHitTrigger打断使用
    /// </summary>
    /// <returns></returns>
    private bool PlayerCanSetItemTf() {
        if (null == item_tf) {
            return true;
        }

        var netPlayerHpBar = item_tf.GetComponent<NetPlayerHPBar>();
        return null == netPlayerHpBar;
    }

    private readonly GameObjectSingleCache<Collider2D> _hitColliderCache = new();
    //结束隐藏碰撞器
    [Obsolete("老的受击已经被弃用")]
    public virtual void EndHitTrigger() {
        CanHurt = true;
        var collider = _hitColliderCache.GetCache(gameObject);
        if (!collider.isActiveAndEnabled) {
            collider.enabled = true;
        }
    }

    public override sealed void SetSwitchWeapon(bool value) {
        if (value) {
            BtnSwitchWeaponClick();
        }
    }

    //切换武器
    public virtual void BtnSwitchWeaponClick() {
#if UNITY_SWITCH
        if (!InputManager.Inst.IsForceObject(gameObject))return;
#endif
        if (dead) return;
        if (mount && !mount.useControllerWeapon) {
            mount.SwitchWeapon();
        } else {
            if (GameUtil.IsSingleGame() && !CanSwitchWeapon()) {
                return;
            }
            var oldFrontWeapon = hand.front_weapon;
            SwitchWeapon();
            if (oldFrontWeapon != hand.front_weapon) {
                SimpleEventManager.Raise(new PlayerSwitchWeaponEvent { controller = this, frontWeapon = hand.front_weapon });
            }
        }
        OnMotivated();
    }

    public void SetWeaponAttackEvent(RGWeapon weapon, bool isSet) {
        if (!weapon) {
            return;
        }

        if (isSet) {
            weapon.afterAttack += WeaponAttackEvent;
            weapon.afterAttack += WeaponStopEvent;
        } else {
            weapon.afterAttack -= WeaponAttackEvent;
            weapon.afterAttack -= WeaponStopEvent;
        }
    }

    public override void SwitchWeapon() {
        hand.SwitchWeapon();
        GetComponent<BoxCollider2D>().enabled = true;
    }

    public override void SetWeaponBack(RGWeapon w, bool temporarily, bool asFirstSibling) {
        hand.SetWeaponBack(w, temporarily, asFirstSibling);
    }

    public override bool SwitchToLastWeapon() {
        hand.SwitchToLastWeapon(true);
        return true;
    }

    public virtual void GetChose() {
        JewelryInit();
        WeaponInit();
        SetUpChar();
        UpdateStateBar();
        awake = true;
        StatisticInitWeapon();
    }

    public void JewelryInit() {
        foreach (var data in ItemData.data.jewelryPlayerData) {
            if (data.player_index == attribute.c_index) {
                thisBattleData.jewelry_item = data.jewelry_item;
                break;
            }
        }
    }

    public void WeaponInit() {
        thisBattleData.InitWeapons(DataUtil.GetInitWeaponName(GetHeroType(), GetSkinIndex()), emWeaponSource.Init);
    }

    protected virtual void StatisticInitWeapon() {
        if (IsDemonstrationCharacter) {
            return;
        }

        if (hand.front_weapon != null) {
            hand.front_weapon.StatisticCount();
        }
    }

    public virtual void NotChose() {
        anim.enabled = true;
        rigibody.isKinematic = true;
        transform.Find("collider").localPosition = new Vector3(0, 0.5f, 0);

        var talkNode = transform.Find("talk");
        if (talkNode) {
            talkNode.gameObject.SetActive(true);
        }
    }

    protected void UpdateStateBar() {
        if (IsLocalPlayer()) {
            UICanvas.GetInstance().UpdateArmorBar();
            UICanvas.GetInstance().UpdateHpBar();
            UICanvas.GetInstance().UpdateEnergyBar();
            UICanvas.GetInstance().UpdateCoin();
            UICanvas.GetInstance().UpdateTokenCoin();
            UICanvas.GetInstance().UpdateSeasonCoin();
        }
    }

    

    public override float Dizzy(float the_time, bool isFreeze) {
        if(attributeDelegate != null){
            return attributeDelegate.Dizzy(the_time, isFreeze);
        }
        if (NetControllerManager.Inst.playerCount == 0 || IsLocalPlayer()) {
            try{
                hand.StopWeapon();
            }
            catch{}
            Pause();
            the_time = base.Dizzy(the_time, isFreeze);
        }

        if (mount) {
            mount.SetAttack(false);
            if (mount.runAnim && mount.anim.GetBool(AnimatorParameterRun)) {
                SetMountAnimatorRun(false);
            }
        }
        return the_time;
    }

    public override void EndDizzy() {
        base.EndDizzy();
        if (!dead) {
            awake = true;
            hand.StartWeapon();
        }
    }

    protected void CreateSmoke() {
        if (anim.GetBool(AnimatorParameterRun)) {
            GameObject temp_obj = PrefabPool.Inst.Take(PrefabManager.GetPrefab(PrefabName.smoke), transform.position,
                Quaternion.identity);
            temp_obj.transform.localScale = new Vector3(move_dir.x > 0 ? 1 : -1, 1, 1);
            CancelInvoke("CreateSmoke");
            Invoke("CreateSmoke", Random.Range(0.3f, 1f));
        }
    }

    /// <summary>
    /// 显示确认复活界面
    /// </summary>
    public void Resurrection() {
        if(RGGameProcess.Inst != null && RGGameProcess.Inst.modeProcess != null && !RGGameProcess.Inst.modeProcess.CanResurrection()){
            return ;
        }
        
        if (BattleData.data.isGameOver) return;
        CanRebornTwice = BattleData.data.CompareFactor(emBattleFactor.RebornTwice) && !BattleData.data.has_reborn_twice;
        CanAdRebornTwice = AdOptimizeUtil.RebornAdOpen && !BattleData.data.has_ad_reborn_twice && AdsUtil.IsRewardVideoReady(AdsUtil.RebornTwice);
        //因邪王模式结算时的部分广告会强制跳转，导致系统内存不足杀游戏后台，被系统判定为跳过广告，丢失结算奖励，经常有玩家找来要赔偿，所以邪王模式屏蔽广告。
        if (BattleData.data.IsLoopTravel) {
            CanAdRebornTwice = false;
        }
        StaticCustomFactorManager.EventDispatch("ControllerRebornConfirm", this);
#if INGAME_DEBUG
        // 在调试工具中指定无限复活后每次都可以复活
        if (InGameDebug.InGameDebugReborn.CanReborn) {
            BattleData.data.has_reborn = false;
            BattleData.data.has_reborn_twice = false;
        }
#endif
        if (DataUtil.IsHeroRoom() && !Application.isEditor) {//客厅死亡设置为0金币，避免意料之外的情况刷宝石。
            RGGameProcess.Inst.coin_value = 0;
            RGGameProcess.Inst.GameFail();
        }
        
        if (BattleData.data.IsDefenceMode) {
            DefenceMapManager.DefenceMap.OnPlayerDead(this);
        } else if (!BattleData.data.has_reborn) {
            BattleData.data.has_reborn_twice = false;
            UICanvas.ShowUIWindowReborn();
        } else if (CanRebornTwice) {
            UICanvas.ShowUIWindowReborn();
        } else if (CanAdRebornTwice) {
            UICanvas.ShowUIWindowRebornTwice();
        } else {
            RGGameProcess.Inst.GameFail();
        }
    }

    public bool CanRebornTwice { get; set; }
    public bool CanAdRebornTwice { get; set; }

    /// <summary>
    /// 复活十字章或赐福手杖因子强制复活
    /// </summary>
    void ForceReborn() {
        DoReborn(RebornType.GameMachanism);

        
        var markIdBlessingReborn = HiddenLevelConst.RelicMarkIdBlessingReborn;
        if (GameUtil.IsMultiGame()) {
            markIdBlessingReborn = $"{markIdBlessingReborn}_{p_index}";
        }
        var canRebornByBlessing = BattleData.data.CompareFactor(emBattleFactor.Blessing) &&
                                  BattleData.data.GetMark(markIdBlessingReborn) <
                                  HiddenLevelConst.RelicBlessingCount;
        if (canRebornByBlessing) {//先消耗消耗墓穴正面因子
            BattleData.data.AddMark(markIdBlessingReborn);
        } else {//消耗复活武器
            hand.RemoveWeapon("weapon_244");
        }
    }

    // /// <summary>
    // /// 联机复活因子复活一次
    // /// </summary>
    // void RebornTwiceInNet() {
    //     foreach (var ctrl in NetControllerManager.Inst.controllers) {
    //         ctrl.controller.DoReborn(false);
    //     }
    //
    //     RGGameProcess.Inst.SetUpPlayerInfoReBorn();
    // }

    public enum RebornType{
        Default, // 通过默认途径复活，比如复活UI
        GameMachanism, // 通过特殊游戏内容机制
        Redemption, // 联机队友拯救
        AdTwice,// 广告二次复活
        NewPlayerReborn, // 新手关卡的免费复活
        PriestSkillAuto,//牧师一技能法阵内自动复活
        ReturnPlayerReborn, // 回归因子的额外复活次数
    }

    protected virtual void SetRebornAnim() {
        anim.SetTrigger("reborn");
    }

    public event Action onCharacterReborn;
    //复活
    public virtual void DoReborn(RebornType rebornType) {
        try {
            if (dead) {
                if (IsLocalPlayer()) {
                    BattleData.data.AddMark(RGGameConst.PLAYER_REBORN_VAL, -1);
                }
                rigibody.isKinematic = false;
                GameObject rebornEffect =
                    Instantiate(ResourcesUtil.Load<GameObject>(
                        "RGPrefab/Effect/effect_reborn.prefab"), transform);
                GameObject rebornShield =
                    Instantiate(ResourcesUtil.Load<GameObject>(
                        "RGPrefab/Effect/reborn_shield.prefab"), transform);
                rebornShield.GetComponent<BuffShield>().SetController(this);
                move_dir = Vector2.zero;
                inertial_vel = 0;
                awake = true;
                dead = false;
                can_cut = true;
                move_invers = false; //复活后去除反向移动Debuff

                if(rebornType == RebornType.Redemption) // 记录被拯救次数
                    BattleData.data.AddMark("totalRedemption");

                if (GameUtil.IsSingleGame()) {
                    var hp = role_attribute.max_hp;
                    var canRebornByBlessing = BattleData.data.CompareFactor(emBattleFactor.Blessing) &&
                                              BattleData.data.GetMark(HiddenLevelConst.RelicMarkIdBlessingReborn) <
                                              HiddenLevelConst.RelicBlessingCount;
                    if (canRebornByBlessing) {
                        hp = Mathf.Min(hp, HiddenLevelConst.RelicBlessingRebornHp);
                    }
                    role_attribute.RestoreHealth(hp);
                    role_attribute.RestoreEnergy(role_attribute.max_energy);
                } else {
                    if (IsLocalPlayer()) {
                        role_attribute.RestoreHealth(1);
                    }

                    if (transform.Find("reborn_talk") != null) {
                        Destroy(transform.Find("reborn_talk").gameObject);
                    }

                    SimpleEventManager.Raise(new OnePlayerDeadEvent {
                        netId = p_index,
                        isDead = false
                    });
                }

                role_attribute.armor = role_attribute.MaxArmor;
                var hpBar = transform.GetComponentInChildren<NetPlayerHPBar>();
                if (null != hpBar) {
                    hpBar.CurHpBarState = emNetHpBarState.Alive;
                }
                
                transform.GetComponent<BoxCollider2D>().enabled = true;
                SetCharacterAnimatorRun(false);
                SetRebornAnim();
                StartHitTrigger(gameObject, 2f);
                if (IsLocalPlayer()) {
                    UpdateStateBar();
                    if (rebornType == RebornType.Default) {
                        RGGameProcess.Inst.SetUpPlayerInfoReBorn();
                    } else if (rebornType == RebornType.AdTwice) {
                        RGGameProcess.Inst.SetUpPlayerInfoReBornAd();
                    } else if (rebornType == RebornType.NewPlayerReborn) {
                        RGGameProcess.Inst.SetUpPlayerInfoReBornNewPlayer();
                    } else if (rebornType == RebornType.ReturnPlayerReborn) {
                        RGGameProcess.Inst.SetUpPlayerInfoReBornReturnPlayer();
                    }
                }

                var frontWeapon = hand.front_weapon;
                if (frontWeapon != null) {
                    frontWeapon.OnReborn();
                }

                var back_weapons = hand.GetBackWeapons();
                if (back_weapons != null) {
                    foreach (var weapon in back_weapons) {
                        weapon.OnRebornBack();
                    }
                }

                SimpleEventManager.Raise(new CharacterRebornEvent {
                    controller = this,
                });
                
                onCharacterReborn?.Invoke();
            }
        } catch (Exception e) {
            Debug.LogError(e.ToString());
        }
    }

    #region 回调

    // 添加代理，一般start函数调用
    public void AddDelegate() {
        RGGameSceneManager.GetInstance().GSDelegate += OnGameStateChange;
        BattleData.data.OnBuffChanged += OnBuffChanged;
        
        if (BattleData.data.IsARAM) {
            OnGameStateChange(0);
        }
    }

    // 移除代理，需要销毁物体前调用
    public virtual void RemoveDelegate() {
        if (null != RGGameSceneManager.GetInstance()) {
            RGGameSceneManager.GetInstance().GSDelegate -= OnGameStateChange;
        }
        BattleData.data.OnBuffChanged -= OnBuffChanged;
        _playerBuffController?.Disable(emBuffType.BulletHitPlayer);
    }

    // 代理所调用的事件
    public virtual void OnGameStateChange(int game_state) {
#if UNITY_EDITOR
        Debug.Log($"OnGameStateChange {game_state}");
#endif
        if (!GameUtil.IsDefenceMode()) {
            if (game_state != 1) {
                if (BattleData.data.IsARAM) {
                    attribute.SpeedValue.SetFixedValue("ARAM_FixedSpeed", 6.5f);
                }
            }
            
            if (game_state == 1) {
                if (BattleData.data.IsARAM) {
                    SetProcessSpeedFactor(-.25f);
                    attribute.SpeedValue.RemoveValue("ARAM_FixedSpeed");
                } else {
                    SetProcessSpeedFactor(-.5f);
                }
            } else if (game_state == 2) {
                SetProcessSpeedFactor(0);
            }
        }
    }

    #endregion

    #region Item相关

    public virtual void TriggerItem() {
        if (!item_tf || !CanTriggerItem()) {
            return;
        }

        if (disableInteractWeapon && item_tf.GetComponent<RGWeapon>() != null) {
            return;
        }

        SimpleEventManager.Raise(new ControllerPreTriggerItemEvent {
            controller = this,
            item = item_tf.gameObject,
        });
        item_tf.GetComponent<ItemInterface>()?.ItemTrigger(this);
    }

    InteractItems _interactItems;
    public virtual void SetItemtf(Transform value, Sprite sprite = null) {
        void Func(){
            _SetItemtf(value, sprite);
            if(!value && _interactItems != null)
                _interactItems.Clear();
        }

        if(RGGameProcess.Inst != null && RGGameProcess.Inst.modeProcess != null && RGGameProcess.Inst.modeProcess.InterceptEnterOrExitItem(this, value, Func)){
            return;
        }
        Func();
    }

    public void ClearItemTf(){
        item_tf = null;
        OnInteractingItemChange?.Invoke();
        if (IsLocalPlayer()){
            UICanvas.GetInstance().SwitchAtkBtn(true);
        }
    }

    void _SetItemtf(Transform value, Sprite sprite = null) {
        if (IsLocalPlayer()) {
            var canvas = UICanvas.GetInstance();
            if (canvas) {
                canvas.SwitchAtkBtn(!value, sprite);
            }
        }

        item_tf = value;
        OnInteractingItemChange?.Invoke();
        //修复可能导致不触发OnExit武器的Bug
        if (null != hand && null != hand.front_weapon) {
            hand.front_weapon.FlushIcon(true);
        }
    }

    public virtual void SetInteractItem(Transform value, string text, int level, float yOffset, Sprite sprite = null, Action onEnter = null, Action onExit = null){
        SetInteractItem(value, text, level, new Vector3(0, yOffset, 0), sprite, onEnter, onExit);
    }
    
    public virtual void SetInteractItem(Transform value, string text, int level, Vector3 offset, Sprite sprite = null, Action onEnter = null, Action onExit = null){
        if(_interactItems == null){
            _interactItems = new InteractItems();
            _interactItems.onSetItem = (trans, specifiedSprite)=>{
                void Func(){
                    _SetItemtf(trans, specifiedSprite);
                }
                if(RGGameProcess.Inst != null && RGGameProcess.Inst.modeProcess != null && RGGameProcess.Inst.modeProcess.InterceptEnterOrExitItem(this, trans, Func)){
                    return;
                }
                Func();
            };
            _interactItems.onRemoveItem = ()=>{
                void Func(){
                    _SetItemtf(null);
                }
                if(RGGameProcess.Inst != null && RGGameProcess.Inst.modeProcess != null && RGGameProcess.Inst.modeProcess.InterceptEnterOrExitItem(this, null, Func)){
                    return;
                }
                Func();
            };
        }
        _interactItems.Add(value, text, level, offset, sprite, onEnter, onExit);
    }

    public virtual void RemoveInteractItem(Transform value){
        if(_interactItems != null)
            _interactItems.Remove(value);
    }

    public virtual bool CompareItem(Transform value) {
        if (in_item && item_tf == value) {
            return true;
        }

        return false;
    }

    public virtual bool CompareItem(string itemName) {
        if (in_item && item_tf.name == itemName) {
            return true;
        }

        return false;
    }

    #endregion

    public bool IsSinglePlayer() {
        return NetControllerManager.Inst.playerCount == 0;
    }

    public bool IsLocalPlayer() {
        if (IsDemonstrationCharacter) {
            return true;
        }

        if (NetControllerManager.Inst == null || 
            NetControllerManager.Inst.localController == null ||
            DataUtil.IsHeroRoom()) {
            return true;
        }

        return NetControllerManager.Inst.localController.netId == (uint)p_index;
    }
    //使用GetSkinIndex() , 不然只会得到0
    [NonSerialized] public int skinIndex = 0;

    /// <summary>
    /// 初始化时,返回结果为大厅中初始皮肤序号, 而非选中皮肤序号,可使用 GetLoadedSkinIndex代替 
    /// </summary>
    /// <returns></returns>
    public int GetSkinIndex() {
        if (IsDemonstrationCharacter) {
            return skinIndex;
        }
        
        if ((GameUtil.IsMultiGame() || thisBattleData.CompareFactor(emBattleFactor.RandomCharactor)) &&
            !DataUtil.IsHeroRoom()) {

            if (IsLocalPlayer()) {
                return BattleSkinIndex;
            }

            if (NetControllerManager.Inst.id2Controllers.ContainsKey((uint)p_index)) {
                var net_ctrl = NetControllerManager.Inst.id2Controllers[(uint)p_index];
                if (null != net_ctrl.battleData) {
                    return net_ctrl.battleData.skinIndex;
                }
            }

            return skinIndex;
        }

        if (GameUtil.InGameScene) {
            return BattleSkinIndex;
        }

        if (RGSaveManager.Inst == null) {
            return skinIndex;
        }
        
        return RGSaveManager.Inst.GetCurrentSkin((emHero)role_attribute.c_index);
    }

    /// <summary>
    /// 更改持有的所有武器速度
    /// </summary>
    public void ChangeWeaponSpeed(float factor, float duration, bool hasEffect, string buffName = "effect_atk_up") {
        if (hand) {
            if (hasEffect) {
                GameObject temp_obj = PrefabPool.Inst.Take(ResourcesUtil.Load<GameObject>(
                        $"RGPrefab/Effect/{buffName}.prefab"),
                    transform.position, Quaternion.identity, transform);
            }

            if (hand.front_weapon) {
                hand.front_weapon.SetWeaponSpeed(factor, duration);
            }

            foreach (var weapon in hand.GetBackWeapons()) {
                weapon.SetWeaponSpeed(factor, duration);
            }
        }
    }

    /// <summary>
    /// buff更改所有武器的速度
    /// </summary>
    /// <param name="factor"></param>
    /// <param name="duration"></param>
    public void BuffChangeWeaponSpeed(float factor, float duration) {
        if (hand) {
            foreach (var weapon in hand.GetAllWeapons()) {
                if (weapon != null) {
                    weapon.BuffSetWeaponSpeed(factor, duration);
                }
            }
        }
    }

    /// <summary>
    /// buff更改角色蓄力速度因子
    /// </summary>
    /// <param name="factor"></param>
    /// <param name="duration"></param>
    public void BuffChangeWeaponHoldSpeed(float factor, float duration) {
        if (duration > 0) {
            StartCoroutine(BuffChangingHoldSpeed(factor, duration));
        } else {
            holdSpeedFactor += factor;
        }
    }

    protected IEnumerator BuffChangingHoldSpeed(float factor, float duration) {
        holdSpeedFactor += factor;
        yield return new WaitForSeconds(duration);
        holdSpeedFactor -= factor;
    }

    public virtual void AttackSpeedChange() {
        if (hand) {
            if (hand.front_weapon) {
                hand.front_weapon.ResetWeaponSpeed();
            }

            foreach (var weapon in hand.GetBackWeapons()) {
                weapon.ResetWeaponSpeed();
            }
        }

        if (mount) {
            if (mount.hands == null) {
                return;
            }

            foreach (var mountHand in mount.hands) {
                if (mountHand.weapons == null) {
                    continue;
                }

                foreach (var mountHandWeapon in mountHand.weapons) {
                    if (mountHandWeapon != null) {
                        mountHandWeapon.ResetWeaponSpeed();
                    }
                }
            }
        }
    }


    public virtual void MultiplyMoveSpeed(float factor, object source){
        if(attributeDelegate != null){
            attributeDelegate.MultiplyMoveSpeed(factor, source);
        }
    }

    public virtual void CancelMoveSpeedEffect(object source){
        if(attributeDelegate != null){
            attributeDelegate.CancelMoveSpeedEffect(source);
        }
    }

    public virtual Dictionary<string, string> GetPlayerDatas() {
        Dictionary<string, string> extraData = new();
        return extraData;
    }

    public virtual void SetPlayerDatas(Dictionary<string, string> playerDatas) {
    }

    public virtual emHero GetHeroType() {
        return emHero.Count;
    }

    public void SetSourceObject(GameObject value) { }

    public GameObject GetSourceObject() {
        return this.gameObject;
    }

    public virtual RGWeapon DropWeapon(bool autoSwitchWeapon = false) {
        return hand.DropWeapon(autoSwitchWeapon);
    }

    public virtual void DropWeaponOnBack(RGWeapon w) {
        if (hand.GetBackWeapons().Contains(w)) {
            hand.DropWeapon(w);
        }
    }

    public virtual RGWeapon ForceDropWeapon() {
        var dropWeapon = hand.ForceDropWeapon();
        if (!hand.front_weapon) {
            hand.RemoveWeaponFlush();
        }
        onForceDropWeapon?.Invoke(dropWeapon);
        return dropWeapon;
    }

    //下坐骑
    public virtual void Land() {
        foreach (Transform child in transform) {
            if (child.name.Equals("buff_statue")) {
                child.gameObject.SetActive(true);
            }
        }

        if (null != hand && null != hand.front_weapon && IsLocalPlayer() && !IsDemonstrationCharacter) {
            UICanvas.GetInstance().ShowBtnSpecial(
                hand.front_weapon.isSpecialWeapon && hand.front_weapon.weaponSpecial.IsSpecial ?
                    hand.front_weapon.weaponSpecial.SpecialMode + 1 :
                    -1);
        }

        onLand?.Invoke();
    }

    public override bool HoldWeapon(string weaponName) {
        return hand.HoldWeapon(weaponName);
    }

    public override bool HasWeapon(string weaponName) {
        return hand.HasWeapon(weaponName);
    }

    public string GetHoldingWeaponName() {
        return hand.GetWeaponKey();
    }

    public virtual RGWeapon GetPhatomWeapon(RGWeapon phatomStaff) {
        if (hand) {
            return hand.GetBackWeapon(false);
        }

        return null;
    }

    /// <summary>
    /// 更换皮肤回调
    /// </summary>
    /// <param name="skinIndex">Skin index.</param>
    public virtual void OnSkinChanged(int skinIndex) {
        Debug.Log("切换皮肤, skinIdx: " + skinIndex);
        // DoSkinChanged(skinIndex);
    }

    /// <summary>
    /// 执行皮肤回调
    /// </summary>
    /// <param name="skinIndex"></param>
    public virtual void DoSkinChanged(int skinIndex) { }

    public virtual void OnSkillChanged() { }

    /// <summary>
    /// 获取角色实际受击部位的位置
    /// </summary>
    public Vector3 GetHitPosition() {
        if (mount) {
            return mount.transform.position - Vector3.down;
        }

        return transform.position;
    }

    public string GetSkillName(int skillIndex) {
        var skillName =
            ScriptLocalization.Get(string.Format("Character{0}_skill_{1}_name", (int)GetHeroType(), skillIndex));
        return skillName;
    }

    public string GetSkillDesc(int skillIndex) {
        var skillName =
            ScriptLocalization.Get(string.Format("Character{0}_skill_{1}_info", (int)GetHeroType(), skillIndex));
        return skillName;
    }

    /// <summary>
    /// 获取对应技能的实体,部分角色情况不通用，需要重新实现
    /// </summary>
    /// <param name="skill_idx">对应技能索引</param>
    /// <returns></returns>
    public virtual GameObject GetSkillObject(int skill_idx = 1) {
        int skin_idx = GetSkinIndex();
        GameObject skill_obj_proto = null;
        if (skin_idx < skillInfo.SkillObjectData.Length) {
            skill_obj_proto = skillInfo.SkillObjectData[Math.Max(0, skin_idx)].GetData();
        } else {
            skill_obj_proto = skillInfo.SkillObjectData[0].GetData();
        }

        return skill_obj_proto;
    }

    ObjectZPos _objZPos;
    public ObjectZPos ZPos{
        get{
            if(_objZPos == null)
                _objZPos = GetComponent<ObjectZPos>();
            return _objZPos;
        }
    }
    int flyHeight;

    [NonSerialized] public bool isFly;

    /// <summary>
    /// 设置是否可以飞行
    /// </summary>
    public void SetFlyable(bool isFlyable, bool changeSortingOrder = true, bool ignoreLandFromAir = false) {
        transform.Find("collider").gameObject.layer = LayerMask.NameToLayer(isFlyable ? "Fly" : "Body");
        isFly = isFlyable;
        if (isFlyable) {
            //起飞
            if (changeSortingOrder && flyHeight == 0) {
                flyHeight = 2;
                if(ZPos) ZPos.height += flyHeight;
            }
        } else {
            if (!ignoreLandFromAir) {
                LandFromAir();//降落
            }
            if (changeSortingOrder && flyHeight > 0) {
                if (ZPos) ZPos.height -= flyHeight;
                flyHeight = 0;
            }
        }
    }



    public void SetSortingLayer(string targetLayer) {
        var root = bodyRenderer.transform.parent;
        foreach (var renderer in root.GetComponentsInChildren<SpriteRenderer>()) {
            bool changeLayer = !renderer.GetComponent<DamageTrigger>(); //不改变子弹层级
            changeLayer &= renderer.maskInteraction == SpriteMaskInteraction.None; //不改变受Mask影响物体的层级
            if (changeLayer) {
                renderer.sortingLayerName = targetLayer;
            }
        }
    }

    

    public override void TurnTransparent(bool isTransparent) {
        gameObject.layer = isTransparent ? 0 : LayerMask.NameToLayer("Body_P");
        isPassive = isTransparent;
    }

    /// <summary>
    /// 无语
    /// </summary>
    public void NoWord() {
        RGMusicManager.GetInstance().PlayEffect(RGMusicManager.emEffect.Message);
        UICanvas.Inst.ShowTextTalk(transform, "...", 2f, 1f);
    }

    #region 改变尺寸

    Vector3 _handPositionOrigin;

    Vector3 handPositionOrigin {
        get {
            if (_handPositionOrigin == default(Vector3)) {
                var h1 = transform.Find("img/h1");
                _handPositionOrigin = h1.localPosition;
            }

            return _handPositionOrigin;
        }
    }

    Vector3 _hand2PositionOrigin;

    Vector3 hand2PositionOrigin {
        get {
            if (_hand2PositionOrigin == default(Vector3)) {
                var h2 = transform.Find("img/h2");
                _hand2PositionOrigin = h2.localPosition;
            }

            return _hand2PositionOrigin;
        }
    }

    /// <summary>
    /// 改变尺寸
    /// </summary>
    /// <param name="deltaScale">改变量,注意:不是最终值</param>
    /// <param name="animDuration">动画时间</param>
    public void ChangeScale(float deltaScale, float animDuration) {
        scale += deltaScale;
        OnScaleChange(animDuration);
    }

    private Tween bodyScaleTween;
    private Tween shadowScaleTween;
    private Sequence bodyScaleSequence;
    
    public event Action<float> chageScaleEvent;
    void OnScaleChange(float animDuration) {
        var currentScale = new Vector3(scale, scale, 1);
        if (bodyScaleTween != null && bodyScaleTween.IsPlaying()) {
            bodyScaleTween.Kill();
        }

        if (shadowScaleTween != null && shadowScaleTween.IsPlaying()) {
            shadowScaleTween.Kill();
        }

        if(bodyScaleSequence != null)
            bodyScaleSequence.Kill();

        if (animDuration <= 0) {
            transform.Find("img/body").localScale = currentScale;
            shadow_lock.transform.localScale = currentScale;
            chageScaleEvent?.Invoke(scale);
        } else {
            bodyScaleSequence = DOTween.Sequence();
            var bodyTransform = transform.Find("img/body");
            bodyScaleSequence.Append(bodyTransform.DOScale(currentScale, animDuration));
            bodyScaleSequence.Join(shadow_lock.transform.DOScale(currentScale, animDuration));
            var t = 0.0f;
            bodyScaleSequence.Join(DOTween.To(()=>t, v =>{
                t = v;
                chageScaleEvent?.Invoke(bodyTransform.transform.localScale.x);
            }, 1, animDuration));
        }

        var h1 = transform.Find("img/h1");
        var h2 = transform.Find("img/h2");
        h1.localPosition = new Vector3(handPositionOrigin.x * scale, handPositionOrigin.y * scale, 0);
        if (h2) {
            h2.localPosition = new Vector3(hand2PositionOrigin.x * scale, hand2PositionOrigin.y * scale, 0);
        }

        float hitSize = 0.5f * scale;
        GetComponent<BoxCollider2D>().size = new Vector2(hitSize, hitSize);
    }

    #endregion

    #region 坐骑相关

    public event Action onMount;
    public virtual void OnMount(bool isMount) {
        if(isMount)
            onMount?.Invoke();
        if (!isMount && hand.front_weapon) {
            hand.front_weapon.CheckAutoAttackDelay();
        }
    }

    public void OnAfterMount() {
        onAfterMount?.Invoke();
    }

    public void OnMountOff() {
        onMountOff?.Invoke();
    }

    public bool ImmuneIce {
        get {
            return GameUtil.HasShieldIce(GameUtil.GetBattleData(this)) || (mount && mount.name == "mcristal") || has_immune_ice_buff;
        }
    }

    public bool ImmuneFire {
        get {
            return GameUtil.GetBattleData(this).HasShieldFire() || (mount && mount.name == "mspider");
        }
    }

    public bool ImmunSting {
        get {
            var battleData = GameUtil.GetBattleData(this);
            return battleData.HasBuff(emBuff.ShieldSting) ||
                   battleData.CompareFactor(emBattleFactor.Endurance) ||
                   battleData.TempBuffs.Contains((int)emBuff.ShieldSting) ||
                   (mount && (mount.name == "mboar2" || mount is FlyMountController)) ||
                   (hand && hand.front_weapon && hand.front_weapon is GunHarmmerSmith { IsHoldHammerSmith: true });
        }
    }

    public bool ImmuneGas {
        get {
            var hasShieldGas = thisBattleData.HasShieldGas();
            return hasShieldGas || (mount && mount.name == "mvaken");
        }
    }

    public bool ImmunInversDir {
        get {
            return false;
        }
    }
    
    /// <summary>
    /// 要执行某些逻辑时的免疫判断，如果触发了临时增益，就会返回 false，会优先判断免疫
    /// 部分减伤逻辑的判断应该选择 ImmuneIce 方法，因为关卡增益只对全减伤或者冻结buff有效
    /// </summary>
    public bool TryHandleIfImmuneIce(bool isDamage) {
        if (!CanTriggerLevelBuffTempImmune(isDamage)) {
            return false;
        }
        if (ImmuneIce) return true;
        return false;
    }

    /// <summary>
    /// 要执行某些逻辑时的免疫判断，如果触发了临时增益，就会返回 false，会优先判断免疫
    /// 部分减伤逻辑的判断应该选择 ImmuneFire 方法，因为关卡增益只对全减伤或者点燃buff有效
    /// </summary>
    public bool TryHandleIfImmuneFire(bool isDamage) {
        if (!CanTriggerLevelBuffTempImmune(isDamage)) {
            return false;
        }
        if (ImmuneFire) return true;
        return false;
    }

    /// <summary>
    /// 要执行某些逻辑时的免疫判断，如果触发了临时增益，就会返回 false，会优先判断免疫
    /// 部分减伤逻辑的判断应该选择 ImmuneGas 方法，因为关卡增益只对全减伤或者毒buff有效
    /// </summary>
    public bool TryHandleIfImmuneGas(bool isDamage) {
        if (!CanTriggerLevelBuffTempImmune(isDamage)) {
            return false;
        }
        if (ImmuneGas) return true;
        return false;
    }
    
    private bool CanTriggerLevelBuffTempImmune(bool isDamage) {
        if (!awake) return false;
        if (isDamage) {
            if (!CanHurt || IsInvulnerable) {
                return false;
            }
        }

        return true;
    }

    #endregion

    #region 沙盒

    public void SetWeapon(RGWeapon weapon) {
        if (weapon) {
            var dropWeapon = hand.DropWeapon();
            if (dropWeapon != null) {
                Destroy(dropWeapon.gameObject);
            }

            var backWeapon = hand.GetBackWeapon();
            if (backWeapon != null) {
                Destroy(backWeapon.gameObject);
            }
            
            hand.PickUpItem(weapon.transform);
        }
    }

    #endregion

    #region 防守模式

    public int additionLevel {
        get {
            // if (BattleData.data.gameMode == emGameMode.Defence) {
            //     return BattleData.data.defenceMode.heroLevel;
            // }

            return 0;
        }
    }

    public virtual void OnAdditionLevelChanged() {
    }

    protected int _level;

    [ShowInInspector, PropertyRange(0, 10)]
    public int level {
        get => _level;
        set => SetLevel(value);
    }

    public virtual void SetLevel(int level, bool show = true) {
        role_attribute.MaxHpValue.RemoveValue(RoleAttributeValueEnum.OnAdditionLevelChanged);
        role_attribute.MaxHpValue.AddAdditionValue(RoleAttributeValueEnum.OnAdditionLevelChanged,
            level * DefenceModeConfig.Config.PER_LEVEL_HP);
        role_attribute.MaxEnergyValue.RemoveValue(RoleAttributeValueEnum.OnAdditionLevelChanged);
        role_attribute.MaxEnergyValue.AddAdditionValue(RoleAttributeValueEnum.OnAdditionLevelChanged,
            level * DefenceModeConfig.Config.PER_LEVEL_ENERGY);
        role_attribute.MaxArmorValue.RemoveValue(RoleAttributeValueEnum.OnAdditionLevelChanged);
        role_attribute.MaxArmorValue.AddAdditionValue(RoleAttributeValueEnum.OnAdditionLevelChanged,
            level * DefenceModeConfig.Config.PER_LEVEL_ARMOR);
        role_attribute.hp = role_attribute.max_hp;
        role_attribute.energy = role_attribute.max_energy;
        role_attribute.armor = role_attribute.Max_armor;
        if (IsLocalPlayer()) {
            UICanvas.Inst.UpdateHpBar();
            UICanvas.Inst.UpdateArmorBar();
            UICanvas.Inst.UpdateEnergyBar();
        }

        _level = level;
        ModeDefenceData.Data.player_level = level;
        if (show && ModeDefenceData.Data.state == DefenceModeState.IN_BATTLE)
            UICanvas.GetInstance().ShowTextTalk(transform, RGItem.GetStringWithColor(
                $"{ScriptLocalization.Get("defence/ui/char_level")} {_level}",
                DefenceModeConfig.Config.CHAR_LEVELUP_COLOR), 2.5f, 3f);
    }

    [ShowInInspector]
    public int SkillLevel {
        get => attribute.SkillLevel.IntValue;
    }
    

    public override int ProcessSkillDamage(float damage, float deltaDamageFactor = .05f) {
        float value = GetProcessSkillDamageFactor(deltaDamageFactor);
        float defenceModeFactor = GetDefenceModeFactor();
        var finalDmg = DamageToInt(value * defenceModeFactor * damage);
        return finalDmg;
    }

    /// <summary>
    /// 不使用守护神殿加成的伤害计算
    /// </summary>
    public int ProcessSkillDamageNoDefenceMode(float damage, float deltaDamageFactor = .05f) {
        float value = GetProcessSkillDamageFactor(deltaDamageFactor);
        var finalDmg = DamageToInt(value * damage);
        return finalDmg;
    }

    private int DamageToInt(float damage) {
        return Mathf.RoundToInt(damage);
    }

    
    float GetDefenceModeFactor() {
        if (BattleData.data.IsDefenceMode) {
            return Mathf.Pow(DefenceModeConfig.Config.DEFENCE_WEAPON_DAMAGE_BASE, SkillLevel);
        }
        return 1;
    }
    
    private float GetProcessSkillDamageFactor(float deltaDamageFactor) {
        var data = BattleData.GetData(this);
        var value = 1 + data.GetMark(JewelrySkillEnhance.Mark) * .05f;
        if (!BattleData.data.IsDefenceMode) {
            value += SkillLevel * deltaDamageFactor;
        }

        if (BattleData.data.HasActivityEnabled(ActivityFireManager.TAG)) {
            var nutBuffCount = DataMgr.ActivityFireData.GetBuyGoodTime("g_nut");
            value += SkillLevel * nutBuffCount * 0.01f;
        }
        value *= attribute.skillDamageFactor.GetFinalValue();
        return value;
    }

    

    protected int LastSkillLevel;
    public virtual void OnSkillLevelChanged() {
        if (BattleData.data.IsDefenceMode) {
            ModeDefenceData.Data.skill_level = SkillLevel;
            if (ModeDefenceData.Data.state == DefenceModeState.IN_BATTLE && SkillLevel != LastSkillLevel) {
                UICanvas.GetInstance().ShowTextTalk(transform, RGItem.GetStringWithColor(
                    $"{ScriptLocalization.Get("defence/ui/skill_level")} {SkillLevel}",
                    DefenceModeConfig.Config.SKILL_LEVELUP_COLOR), 2.5f, 3f);
            }
        }

        if (BattleData.data.HasActivityEnabled(ActivityFireManager.TAG)) {
            if (GameUtil.InGameScene && SkillLevel != LastSkillLevel) {
                UICanvas.GetInstance().ShowTextTalk(transform, RGItem.GetStringWithColor(
                    $"{ScriptLocalization.Get("defence/ui/skill_level")} {SkillLevel}",
                    _skillLevelUpColor), 2.5f, 2f);
            }
        }

        LastSkillLevel = SkillLevel;
    }

    /// <summary>
    /// 神殿的技能增加升级特效, 类似于发光效果
    /// </summary>
    public virtual void DecorateSkillObject(GameObject skill_object, float custom_intensity = 1f) {
        ModifyBulletHelper.ModifyBullet(skill_object, 0, 0, 0, attribute.AoeRange.Value, ModifyBulletMark);
        if (!GameUtil.IsDefenceMode() || skill_object == null) return;
        if (SkillLevel > 0) DefenceBullet.DecorateSkillObject(skill_object, SkillLevel, custom_intensity);
    }

    public void DecorateAtkCut(GameObject sword, float custom_intensity = 1f) {
        if (!GameUtil.IsDefenceMode() || sword == null) return;
        if (level > 0) DefenceBullet.DecorateSkillObject(sword, level + 5, custom_intensity);
    }

    #endregion

    public override void OnBulletCreate(GameObject bulletGo) {
        base.OnBulletCreate(bulletGo);
        var carrier = bulletGo.GetComponent<DamageCarrier>();
        if (!carrier) {
            return;
        }

        if (!BattleData.data.HasActivityEnabled(ActivityZongziManager.TAG)) {
            return;
        }

        if (BattleData.data.GetMark("zongzi_feel") == 2) {
            var validWeaponTypes = new List<emWeaponType>() {
                emWeaponType.Gun, emWeaponType.Pistol, emWeaponType.Rifle, emWeaponType.Rocket, emWeaponType.ShotGun
            };
            var bulletInfo = carrier.bulletInfo;
            var damageInfo = carrier.damageInfo;
            if (bulletInfo.sourceWeapon && validWeaponTypes.Contains(bulletInfo.sourceWeapon.weapon_type)) {
                bulletInfo.createPosition = transform.position; 
                var bullet = BulletFactory.TakeBullet(bulletInfo, damageInfo);
                bullet.GetComponent<RGBullet>().SetAwakeTrue();
            }
            
        }
        
        if (BattleData.data.GetMark("zongzi_feel") == 3) {
            if (rg_random.Range(0, 100) < 25) {
                foreach (var t in bulletGo.GetComponentsInChildren<DamageTrigger>()) {
                    CreateChaos(t.transform);
                }
            }
        }
    }
    
    private void CreateChaos(Transform parent) {
        var effect = ResourcesUtil.Load<GameObject>("RGPrefab/Bullet/Buff/effect_chaos.prefab");
        if (parent) {
            Instantiate(effect, parent);
        }
    }

    protected void HandleMultiSkillBuff() {
        foreach (var skill in skills) {
            var battleData = thisBattleData;
            if (skill.hasMultiCount && battleData.HasBuff(emBuff.MasterContinuous) && !skill.HasMultiState) {
                var addCount = 1 + DataUtil.CalBuffMasterContinuousExtraCount();
                skill.maxCount += BuffStackCfgMasterContinuous.GetParam(battleData, addCount).addCount;
                skill.count += BuffStackCfgMasterContinuous.GetParam(battleData, addCount).addCount;
            }
        }
    }

    public virtual void OnBuffChanged(emBuff buff, bool isGet) {
        if (buff == emBuff.MasterWeapon && !isGet) {
            //失去第三把武器天赋时,丢掉当前手上的武器
            if (hand && hand.front_weapon) {
                hand.DropWeapon();
                hand.SwitchWeapon();
            }
        }

        if (buff == emBuff.CoinAddArmor) {
            role_attribute.UpdateArmor(RGGameProcess.Inst.coin_value);
        }

        if (buff == emBuff.SpeedUpWithCritic) {
            CancelInvoke(nameof(UpdateSpeedUpWithCritic));
            if (isGet) {
                _buff36LastPosition = rigibody.position;
                Invoke(nameof(UpdateSpeedUpWithCritic), 0);
            }
        }
    }

    private const float Buff36TriggerDistance = 40;
    private float _buff36MoveDistance;
    private Vector2 _buff36LastPosition;
    private void UpdateSpeedUpWithCritic() {
        var position = rigibody.position;
        var distance = Vector2.Distance(_buff36LastPosition, position);
        if (distance < 5) {
            _buff36MoveDistance += distance;
        }

        _buff36LastPosition = position;
        if (_buff36MoveDistance >= Buff36TriggerDistance && attribute.armor < attribute.MaxArmor) {
            _buff36MoveDistance = 0;
            attribute.RestoreArmor(1, true);
        }
        
        Invoke(nameof(UpdateSpeedUpWithCritic), Time.fixedDeltaTime);
    }

    /// <summary>
    /// 选择角色确定时调用
    /// </summary>
    public virtual void OnChosen() {
    }

    /// <summary>
    /// 保存游戏进度时调用
    /// </summary>
    public virtual void OnEnterNextLevel(bool changeScene) {
    }

    /// <summary>
    /// 销毁回调
    /// </summary>
    public Action onDestoryCb;

    protected override void OnDestroy() {
        _hitTriggerTimerPool.DestroyAllTimers();
        base.OnDestroy();
        MasterDestroy();
        RemoveDelegate();
        UnLoadSkin();
        buffMgr.ClearAllBuff();
        OnDragSkillEnd();
    }

    public void AddDestroyCb(Action act) {
        onDestoryCb += act;
    }

    public void MasterDestroy() {
        if (null != onDestoryCb) {
            onDestoryCb();
        }
    }

    protected void SetupPrice() {
#if UNITY_SWITCH
        //switch版二技能花钱的改为10000宝石
        if (skills[1] != null) {
            if (skills[1].price == 0) {
                skills[1].price = 10000;
            }
        }
#endif
    }

    public virtual T GetSkinData<T>(int skinIdx) where T : class {
        if (null == skillInfo.skinSkillCfg) {
            Debug.Log("skillInfo.skinSkillCfg is null");
            return null;
        }

        var skinData = skillInfo.skinSkillCfg.data.GetSkinData<T>(skinIdx);
        if (null == skinData) {
            Debug.Log("skinSkillCfg data is null");
            return null;
        }

        return skinData;
    }


    protected virtual AudioClip GetSkillClip(int skinIdx) {
        if (null == skillInfo.skinSkillCfg) {
            return null;
        }

        return skillInfo.skinSkillCfg.data.GetSkinAudioClip(skinIdx);
    }
    //获取当前皮肤是否有技能音效
    public bool HasSkinClip() {
        return SkinAudioConfig.HasConfig(skillIndex, GetHeroType(), GetLoadedSkinIndex);
    }

    //是否可使用技能
    public virtual bool CanUseSkill() {
        return role_attribute.skill_ready;
    }

    public virtual bool CanSwitchWeapon() {
        return true;
    }

    public virtual bool CanTriggerItem() {
        return true;
    }
    
    public event Action onStopMove;
    public void StopMove() {
        onStopMove?.Invoke();
        SetCharacterAnimatorRun(false);
        rigibody.velocity = Vector2.zero;
        move_dir = Vector2.zero;
        inertial_vel = 0f;
        force_direction = Vector2.zero;
        preMoveInput = new MovementInput();
        SetMountAnimatorRun(false);
    }

    public virtual RGHand GetHand(int idx = 0) {
        if (idx == 0) return hand;
        return null;
    }

    private int spriteMaskInteractionCount;
    /// <summary>
    /// 设置角色躯体SpriteMaskInteraction
    /// </summary>
    /// <param name="interaction"></param>
    public void SetBodyMaskInteraction(SpriteMaskInteraction interaction) {
        if (interaction != SpriteMaskInteraction.None) {
            // 设置
            if (bodyRenderer.maskInteraction == interaction ||
                bodyRenderer.maskInteraction == SpriteMaskInteraction.None) {
                bodyRenderer.maskInteraction = interaction;
                ++spriteMaskInteractionCount;
            }
        } else {
            // 清除
            if (bodyRenderer.maskInteraction != SpriteMaskInteraction.None) {
                --spriteMaskInteractionCount;
                if (spriteMaskInteractionCount == 0)
                    bodyRenderer.maskInteraction = SpriteMaskInteraction.None;
            }
        }
    }

    private Timer shadowTimer;

    public event Action<PlayerBulletHitEnemyEvent> HitEnemyEvent;

    public event Action HitEnemyNonParam;
    public event Action<RGController, object> KillEnemyEvent;
    public event Action<PlayerBulletPenetrateEvent> BulletPenetrateEvent;

    public event Action<PlayerBulletPreHitEnemyEvent> PreHitEnemyEvent;
    void PreHitEnemy(PlayerBulletPreHitEnemyEvent eParam){
        if (null == eParam || eParam.enemy == null || eParam.sourceObj != gameObject) {
            return;
        }
        PreHitEnemyEvent?.Invoke(eParam);
    }

    void OnHitEnemy(PlayerBulletHitEnemyEvent eParam) {
        if (null == eParam || !eParam.enemy || eParam.sourceObj != gameObject) {
            return;
        }
        HitEnemyEvent?.Invoke(eParam);
        HitEnemyNonParam?.Invoke();
        OnAtkCritical(eParam);

        if (rg_random.Range(0f, 1f) < attribute.Lifesteal.Value) {
            attribute.RestoreHealth(1);
        }

        // 联机记录单次最高伤害
        if (GameUtil.IsMultiGame() && IsLocalPlayer()) {
            BattleData.data.multiSingleMaxDamage = Mathf.Max(BattleData.data.multiSingleMaxDamage, eParam.damage);
        }
    }

    void OnBulletPenetrate(PlayerBulletPenetrateEvent eParam){
        if (null == eParam || eParam.hitEvent.sourceObj != gameObject) {
            return;
        }
        BulletPenetrateEvent?.Invoke(eParam);
    }

    void OnAtkCritical(PlayerBulletHitEnemyEvent eParam) {
        if (!eParam.isCritical) {
            return;
        }
        onAtkCritical?.Invoke();
        if (!thisBattleData.HasBuff(emBuff.SpeedUpWithCritic)) {
            return;
        }

        var durationTime = 2f;
        role_attribute.ChangeSpeedNE("crit_speedup", 0.3f, durationTime);
        shadowTimer?.Cancel();
        EnableShadow();
        shadowTimer = Timer.Register(durationTime, false, false, () => {
            DisableShadow();
        });
        if (mount) {
            mount.EnableShadow(durationTime);
        }
    }

    public void DoAttack() {
        BtnAtkClick(true);
    }

    public void DoStop() {
        RoleMove(new MovementInput { dir = Vector2.zero, weight = 0.0f });
    }
    
    [Button]
    public void EnableOutline(Color outlineColor) {
        bodyRenderer.material.EnableKeyword("CHAR_OUTLINE_ON");
        bodyRenderer.material.SetColor("_OutlineColor", outlineColor);
    }

    [Button]
    public void DisableOutline() {
        bodyRenderer.material.DisableKeyword("CHAR_OUTLINE_ON");
    }
#if UNITY_EDITOR
    [Button]
    void LogCharInfo() {
        //cd
        LogUtil.Log($"-- FinalCooldown {skillInfo.FinalCooldown} baseCooldown {skillInfo.baseCooldown}");
        LogUtil.Log($"-- skillHaste {skillInfo.skillHaste}");
        //暴击
        LogUtil.Log($"-- attribute.critical {attribute.critical}");
    }
#endif
    
    public static float TargetDistance(RGController ctrl, Transform target) {
        if (null == ctrl || null == target) {
            return -1;
        }

        return Vector2.Distance(ctrl.transform.position, target.position);
    }


    

    protected override void FixedUpdate() {
        if (awake) {
            base.FixedUpdate();
        }
        customUpdateEvent?.Invoke(Time.fixedDeltaTime);

        if (awake || attribute.isDisable) { // 被眩晕的时候也要更新buff
            if (buffMgr != null) {
                buffMgr.FixedUpdate(Time.fixedDeltaTime);
            }
        }

        SimpleEventManager.Raise(PlayerMoveEvent.UseCache(this));
    }

    public virtual bool HasShield() {
        if (oneshot_shields.Count > 0) return true;
        if (GetComponentInChildren<RGShield>()) return true;
        if (GetComponentInChildren<BuffArmor>()) return true;
        return false;
    }
    
    public string GetMusicTag() {
        if (GameUtil.IsMultiGame()) {
            return p_index.ToString();
        }

        return "default";
    }
    
    public virtual void GetWeaponItemBefore(WeaponItemBase item){}
    public virtual void GetWeaponItemAfter(WeaponItemBase item){}
    
    /// <summary>
    /// 贴图颜色渐变
    /// </summary>
    /// <param name="toColor"></param>
    /// <param name="duration"></param>
    public void ChangeSpriteColor(Color toColor, float duration = 1f) {
        bodyRenderer.material.DOColor(toColor, duration);
    }
    public void ResetSpriteColor(float duration = 1f) {
        bodyRenderer.material.DOColor(Color.white, duration);
    }

    public void SetImageActive(bool active) {
        transform.Find("img").gameObject.SetActive(active);
    }

    public Transform GetImgNode(){
        return transform.Find("img");
    }

    public bool IsHeroChar() {
        return GameUtil.IsHeroChar(this.GetHeroType());
    }

    public bool HasHeroTransformWeapon() {
        return null != hand && hand.CheckTransformHeroWeapon();
    }

#if UNITY_EDITOR
    [Button("测试掉落蓝量")]
    public void TestDropEnergy(int energyCount = 10) {
        EnemyDropManager.DropEnergy(energyCount, transform.position);
    }
#endif

    public override UnitType GetUnitType() {
        return UnitType.Character;
    }

    protected override void OnDisableStateChanged(bool isDisable, object key) {
        base.OnDisableStateChanged(isDisable, key);
        awake = !dead && !isDisable;
    }
    
    private void OnStartBattle(BattleRoomStartEvent e) {
        thisBattleData.getHurtInRoom = false;
    }
    
    private void OnStartGame(EnterGameEvent e) {
        thisBattleData.getHurtInGame = false;
    }

    /// <summary>
    /// 皮肤长待机动作相关
    /// </summary>

    #region long_idle

    [SerializeField] protected int[] longIdleSkinIndexes;

    private bool _isLongIdleDisable;
    
    public bool IsLongIdleDisable {
        set {
            OnMotivated();
            _isLongIdleDisable = value;
        }
        get {
            return _isLongIdleDisable;
        }
    }

    public bool IsLongIdleSkin {
        get { return longIdleSkinIndexes.Any(t => GetSkinIndex() == t); }
    }

    public bool muteLongIdleAudio;
    public bool CanLongIdle => IsLongIdleSkin && !IsLongIdleDisable && !IsDemonstrationCharacter; 

    protected float WaitTime; //等待时间

    /// <summary>
    /// 移动了,重置闲置时间(用于判断LongIdle)
    /// </summary>
    public virtual void OnMotivated() {
        if (!CanLongIdle) {
            return;
        }

        WaitTime = 5f;
        if ((anim.GetCurrentAnimatorStateInfo(0).IsName("idle_long") ||
             anim.GetCurrentAnimatorStateInfo(0).IsName("idle_long_1"))) {
            anim.Play("ide", 0);
        }
    }

    protected void LongIdleCheckUpdate() {
        if (hand.front_weapon && hand.front_weapon.attackPressed) {
            OnMotivated();
        }

        if (WaitTime > 0 && !mount) {
            WaitTime -= Time.deltaTime;
            if (WaitTime <= 0) {
                LongIdle();
            }
        }
    }

    public void TurnLongIdle() {
        LongIdle();
    }
    
    protected virtual void LongIdle() {
        AudioClip clip = GetLongIdleAudioClip(false);
        if (clip != null) {
            if (RGGameSceneManager.GetInstance().game_state != 1 && !muteLongIdleAudio) {
                string musicTag = GetMusicTag();
                RGMusicManager.GetInstance().FadeInSpecialBGM(clip, 0.5f, IsLongIdleAudioLoop, false, musicTag);
            }
        }

        anim.Play("idle_long", 0);
    }

    public virtual bool IsLongIdleAudioLoop {
        get {
            return true;
        }
    }
    
    public virtual AudioClip GetLongIdleAudioClip(bool isDefault){
        return SkinAudioConfig.GetCustomizeClip(GetHeroType(), GetSkinIndex(), 0);
    }

    protected virtual void OnCharacterLongIdleEvent(CharacterLongIdleEvent e) {
        if (!IsLongIdleSkin) {
            return;
        }

        if (!e.isEnter) {
            if (gameObject && e.animator.gameObject && e.animator.gameObject == this.gameObject) {
                string musicTag = GetMusicTag();
                RGMusicManager.GetInstance().StopSpecialBGM(true, musicTag);
            }

            StartCoroutine(ReEnableWeapon());
        }
    }

    IEnumerator ReEnableWeapon() {
        // 隔一帧，防止动画 OnStateExit 之后 RGHand h1 没有 Enable 回去，导致不能 weapon setTrigger
        yield return null;
        var weapon = transform_img.Find("h1").gameObject.GetComponentInChildren<RGWeapon>();
        if (weapon != null) {
            weapon.StartUseWeapon();
        }
    }

    protected void OnLongIdleExitEvent(CharacterLongIdleEvent e) {
        if (!CanLongIdle) {
            return;
        }

        if (!e.isEnter) {
            if (gameObject && e.animator.gameObject && e.animator.gameObject == this.gameObject) {
                string musicTag = GetMusicTag();
                RGMusicManager.GetInstance().StopSpecialBGM(true, musicTag);
            }

            StartCoroutine(ReEnableWeapon());
        }
    }
    
    #endregion

    #region 处理房间边界碰撞检测逻辑
    private bool _needIgnoreVacantWallCheck;
    public bool NeedIgnoreVacantWallCheck => _needIgnoreVacantWallCheck;
    public void SetIgnoreVacantWallCheck(bool needIgnore) {
        _needIgnoreVacantWallCheck = needIgnore;
    }
    public bool GetIgnoreVacantWallCheck() {
        return _needIgnoreVacantWallCheck;
    }
    #endregion
    #region 处理多状态技能的icon
    private Sprite _currentSkillSprite;
    public Sprite GetSpecifySkillSprite() {
        return _currentSkillSprite;
    }
    protected void SetSpecifySkillSprite(Sprite sprite) {
        _currentSkillSprite = sprite;
    }
    #endregion

    /// <summary>
    /// 角色蓄能条刷新
    /// </summary>
    protected void UpdateSkillPassiveBar(float currentValue, float maxValue, RGController controller,
        string heroStyle = null,
        bool usePercentage = false, bool hideText = false, int count = 1) {
        SimpleEventManager.Raise(new UpdatePassiveSkillBar {
            currentValue = currentValue,
            maxValue = maxValue,
            controller = controller,
            heroStyle = heroStyle,
            usePercentage = usePercentage,
            hideText = hideText,
            maxCount = count,
        });
    }

    /// <summary>
    /// 角色蓄能条刷新
    /// </summary>
    protected void UpdateSkillPassiveBar(int currentValue, int maxValue, RGController controller,
        string heroStyle = null,
        bool usePercentage = false, bool hideText = false, int count = 1) {
        SimpleEventManager.Raise(new UpdatePassiveSkillBar {
            currentValue = currentValue,
            maxValue = maxValue,
            controller = controller,
            heroStyle = heroStyle,
            usePercentage = usePercentage,
            hideText = hideText,
            maxCount = count,
        });
    }

    /// <summary>
    /// 获取背上第一把武器
    /// </summary>
    /// <returns></returns>
    public RGWeapon GetBackWeapon() {
        if (!hand) return null;
        return hand.GetBackWeapon(false);
    }

    public virtual string GetSkillSyncExtra() {
        return string.Empty;
    }

    public virtual void SetSkillSyncExtra(string extra) {
    }
}
