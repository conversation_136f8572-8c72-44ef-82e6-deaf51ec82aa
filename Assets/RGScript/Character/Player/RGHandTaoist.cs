using Activities.BugFeatures.Scripts;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public class RGHandTaoist : RGHand {
    public GameObject funnelProto;
    internal List<FunnelWeaponContainer> funnels = new List<FunnelWeaponContainer>();

    internal bool active {
        get {
            return IsActive();
        }
    }

    protected virtual bool IsActive() {
        var battleData = GameUtil.GetBattleData(controller);
        if (BattleData.data.CompareActivityFactor(emActivityFactor.WeaponMaster)) {
            return true;
        }
        return battleData.HasBuff(emBuff.FlySword);
    }

    FunnelWeaponContainer CreateFunnel() {
        var funnel = Instantiate<GameObject>(funnelProto, transform.position, Quaternion.identity, RGGameSceneManager.Inst.temp_objects_parent).GetComponent<FunnelWeaponContainer>();
        funnel.SetSourceObject(controller.gameObject);
        funnel.name = funnelProto.name + "_" + funnels.Count;
        funnel.parent_point = controller.transform.Find("funnel_root");
        funnel.StartRunning(false);
        funnel.isOwnerActive = () => { return controller != null && !controller.dead; };
        funnels.Add(funnel);
        return funnel;
    }

    public List<BattleData.WeaponInBattle> GetWeaponDatas() {
        var datas = new List<BattleData.WeaponInBattle>();
        if (active) {
            foreach (var f in funnels) {
                if (f.weapon) {
                    var funnelWeapon = f.weapon.GetWeaponData();
                    datas.Add(funnelWeapon);
                }
            }
        }
        return datas;
    }

    public void StartFunnels() {
        if (active) {
            foreach (var f in funnels) {
                f.StartRunning();
            }
        }
    }
    public void StopFunnels() {
        if (active) {
            foreach (var f in funnels) {
                f.StopRunning();
            }
        }
    }

    protected FunnelWeaponContainer GetEmptyFunnels(RGWeapon w) {
        ReorderFunnels();
        foreach (var f in funnels) {
            if (!f.weapon || f.weapon == w) {//浮游炮武器为空或正好拿着这把武器
                return f;
            }
        }
        var funnel = CreateFunnel();
        return funnel;
    }
    void ReorderFunnels() {
        for (int i = 0; i < funnels.Count; i++) {
            if (!funnels[i].weapon) {//如果没有武器,则移动到末尾
                var f = funnels[i];
                for (int j = i; j < funnels.Count - 1; j++) {
                    funnels[j] = funnels[j + 1];
                }
                funnels[funnels.Count - 1] = f;
            }
        }
        ResetFunnelOffset();
    }
    void ResetFunnelOffset() {
        int count = 0;
        foreach (var funnel in funnels) {
            if (funnel.weapon) {
                count++;
            }
        }
        float deltaAngle = 360f / count * Mathf.Deg2Rad;
        for (int i = 0; i < count; i++) {
            float radians = deltaAngle * i;
            funnels[i].offset = new Vector3(Mathf.Cos(radians), Mathf.Sin(radians)) * 0.8f;
        }

    }

    public override void SetWeaponFront(RGWeapon w, Vector3 angles, bool isPick) {
        if (active) {
            if (w) {
                foreach (var f in funnels) {
                    if (f.weapon == w) {
                        f.SetWeapon(null);
                        w.SetController(controller);
                        break;
                    }
                }
            }
            ReorderFunnels();
        }
        _backWeaponList.Remove(w);
        base.SetWeaponFront(w, angles, isPick);

    }
    
    public override void SetWeaponBack(RGWeapon w, bool temporarily, bool asFirstSibling) {

        if(w && !_backWeaponList.Contains(w)){
            if(asFirstSibling)
                _backWeaponList.Insert(0, w);
            else
                _backWeaponList.Add(w);
        }

        //  武器已处于浮游状态，直接返回
        if(funnels != null && funnels.Count > 0){
            foreach(var f in funnels){
                if(f.weapon == w)
                    return;
            }
        }
        
        base.SetWeaponBack(w, temporarily, asFirstSibling);
        if (active && GetFunnelsCount() < GetCurrentBackMaxWeaponCount()) {
            if(w && w.controller != null && !w.HasTag(emWeaponTag.NotOccupied) && !w.IsInvisibleWeapon)
                GetEmptyFunnels(w).SetWeapon(w);
            else{
                // 尝试找下一把武器 浮空
                for(var i = 0; i < _backWeaponList.Count; ++i){
                    var nextW = _backWeaponList[i];
                    if(nextW && !nextW.HasTag(emWeaponTag.NotOccupied) && !nextW.IsInvisibleWeapon && !IsWeaponInFunnel(nextW)){
                        GetEmptyFunnels(nextW).SetWeapon(nextW);
                        break;
                    }
                }
            }
        }
    }

    protected bool IsWeaponInFunnel(RGWeapon w){
        return w && funnels.Find(container => container.weapon == w) != null;
    }

    public override RGWeapon DropWeapon(RGWeapon w) {
        if (w) {
            _backWeaponList.Remove(w);
            foreach (var f in funnels) {
                if (f.weapon == w) {
                    f.SetWeapon(null);
                    w.SetController(controller);
                    break;
                }
            }
        }
        ReorderFunnels();
        w = base.DropWeapon(w);
        if (w) {
            var p = controller.transform.position + new Vector3(0, 0.5f, 0);
            if (!w.gameObject.isStatic && Vector2.Distance(w.transform.position, p) > 1){ // 武器掉落位置太远了
                w.transform.position = p;
            }
        }
        return w;
    }

    public int GetFunnelsCount(){
        int count = 0;
        foreach (var f in funnels) {
            if (f.weapon && !f.weapon.IsInvisibleWeapon && !f.weapon.HasTag(emWeaponTag.NotOccupied)) {
                count++;
            }
        }
        return count;
    }

    protected override bool IsBackFull() {
        var currentBackWeaponCount = 0;
        foreach(var w in _backWeaponList){
            if(w && !w.HasTag(emWeaponTag.NotOccupied) && !w.IsInvisibleWeapon)
                ++currentBackWeaponCount;
        }
        return currentBackWeaponCount >= GetCurrentBackMaxWeaponCount();
    }

    protected List<RGWeapon> _backWeaponList = new List<RGWeapon>();
    void _CleanNullWeapon(){
        for(var i = _backWeaponList.Count - 1; i >= 0; --i){
            if(!_backWeaponList[i] || (_backWeaponList[i].transform.parent != back_tf && !IsWeaponInFunnel(_backWeaponList[i])))
                _backWeaponList.RemoveAt(i);
        }
    }
    public override RGWeapon GetBackWeapon(bool includeInvisible = true) {
        _CleanNullWeapon();
        if(_backWeaponList.Count > 0){
            var idx = _backWeaponList.FindIndex(0, w => {
                return w && (includeInvisible || !w.IsInvisibleWeapon);
            });
            if(idx >= 0)
                return _backWeaponList[idx];
        }
        return null;
    }
    public override RGWeapon[] GetBackWeapons() {
        _CleanNullWeapon();
        return _backWeaponList.ToArray();
    }
    
    public override void SwitchWeapon(bool showText = true) {
        base.SwitchWeapon(showText);
        if (active) {
            ReorderFunnels();
        }
    }
    public override bool RemoveWeapon(RGWeapon w, bool switchToNextWeapon = true) {
        bool removed = base.RemoveWeapon(w, switchToNextWeapon);
        if (active) {
            if (!removed) {
                foreach (var f in funnels) {
                    if (w.controller == f) {
                        f.SetWeapon(null);
                        Destroy(w.gameObject);
                        ReorderFunnels();
                        return true;
                    }
                }
            }
        }
        return removed;
    }


}
