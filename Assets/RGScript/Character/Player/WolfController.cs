using Activities.Fire.Scripts;
using ModeSeason.Troop2;
using RGScript.Character;
using System.Linq;
using UnityEngine;

public class WolfController : RGPetController {
    public float atkDistance = 2;
    public bool strengthen = false;
    public bool showSmoke;
    public GameObject bullet1;
    public GameObject bullet2;
    public AudioClip audio_clip, audio_clip_s12;
    RGRandom rg_random = new RGRandom();
    public int index = 1;
    public float swordScale;
    public int[] skinHideAttackSprite;
    public bool isNpcDruidWolf;//德鲁伊Npc专属
    public bool bulletAutoFacing;
    Transform atk_point;
    internal UIHpBar hpBar;

    bool has_buff_druid {
        get {
            if (target_obj != null && target_obj.Find("buff_druid")) {
                return true;
            } else {
                return false;
            }
        }
    }

    protected override void Awake() {
        base.Awake();
        atk_point = transform.Find("img/body");
        hpBar = CreatePetBar();
        hpBar.TurnColor(UIHpBar.blue);
        hpBar.SetWidth(60);
        gameObject.SetNPCZPos();
    }

    protected override void Start() {
        if (showSmoke) {
            Instantiate(PrefabManager.GetPrefab(PrefabName.effect_smoke), transform.position, Quaternion.identity);
        }

        rg_random.SetRandomSeed(RGGameInfo.Inst.SampleRandomSeed + index);
        if (thisBattleData.HasBuff(emBuff.ExpertPet)) {
            damage += 3;
            role_attribute.MaxHpValue.AddAdditionValue(RoleAttributeValueEnum.BuffExpertPet, 10);
        }

        StrengthenPetIfFactorValid();

        role_attribute.hp = role_attribute.max_hp;
        AddDelegate();
        OnInit();
        if (awake)
            Scout();
        attribute.Client.OnHpChanged += OnHpChanged;
    }

    protected override void OnDestroy() {
        base.OnDestroy();
        attribute.Client.OnHpChanged -= OnHpChanged;
    }

    private void OnHpChanged(float value, float maxvalue) {
        if (value >= maxvalue) {
            Recovered();
        }
    }

    protected override void FixedUpdate() {
        if (awake) {
            if (inertial_vel > 1f) {
                rigibody.velocity = force_direction.normalized * inertial_vel;
                inertial_vel *= friction;
            } else {
                rigibody.velocity = move_direction.normalized * role_attribute.speed * (1 + role_attribute.speed_rate);
            }

            if (null != master_tf) {
                if (Vector2.Distance(transform.position, master_tf.position) > 20) {
                    transform.position = master_tf.position;
                } else if (target_obj == master_tf && Vector2.Distance(transform.position, master_tf.position) < 2) {
                    move_direction = Vector2.zero;
                    anim.SetBool("run", false);
                }
            }

            if (null != master_tf && !master_tf.GetComponent<RGBaseController>().dead) {
                ReplyingHP();
            }
        }
    }

    protected override void Recovered() {
        base.Recovered();
        hpBar.TurnColor(UIHpBar.blue);
    }

    public override void Scout() {
        has_target = false;
        target_obj = master_tf;
        if (ShouldSearchTarget) {
            RaycastHit2D[] hit_list = Physics2D.CircleCastAll(transform.position, 12, new Vector2(0, 0), 0f,
                1 << LayerMask.NameToLayer("Body_E"));
            if (hit_list.Length > 0) {
                min_distance = 100;
                for (int i = 0; i < hit_list.Length; i++) {
                    var tmpPos = transform.position.Vec2() + Vector2.up * 0.5f;
                    if (!GetDistanceFromTarget(tmpPos, hit_list[i].transform, ref _targetCollider, out var dist, out var closestPoint)) {
                        continue;
                    }

                    if (dist < min_distance && !Physics2D.Linecast(tmpPos, closestPoint, PhysicsUtil.NotWalkableMask)) {
                        min_distance = dist;
                        target_obj = hit_list[i].transform;
                        has_target = true;
                    }
                }
            }
        }

        FixedRotation();
        RunReflection();
    }

    protected override void RunReflection() {
        if (has_target) {
            var tmpPos = transform.position.Vec2() + Vector2.up * 0.5f;
            var isTargetValid = GetDistanceFromTarget(tmpPos, target_obj, ref _targetCollider, out var dist, out var closestPoint);
            if (!isTargetValid) {
                Invoke("EndCycle", ScoutRate);
                return;
            }
            if (dist > atkDistance) {
                if (rg_random.Range(0, 10) < 8) {
                    move_direction = (closestPoint - (Vector2)transform.position).normalized;
                    anim.SetBool("run", true);
                } else {
                    move_direction = new Vector2(0, 0);
                }
            } else {
                if (rg_random.Range(0, 10) < 8 && can_atk) {
                    anim.SetTrigger("atk");
                    can_atk = false;
                    Invoke("TurnToCanAtk", AtkCD);
                }
            }
        } else {
            int r0 = rg_random.Range(0, 10);
            if (r0 < 8 && target_obj && Vector2.Distance(transform.position, target_obj.position) > 2) {
                if (Physics2D.LinecastNonAlloc(transform.position, target_obj.transform.position, _linecastCache,
                        PhysicsUtil.FallMask) > 0) {
                    SetRun(false);
                    move_direction = Vector2.zero;
                } else {
                    move_direction = ((Vector2)target_obj.position - (Vector2)transform.position).normalized;
                    anim.SetBool("run", true);
                }
            } else {
                move_direction = new Vector2(0, 0);
            }
        }

        Invoke("EndCycle", ScoutRate);
    }

    public override void EndCycle() {
        CancelInvoke("EndCycle");
        move_direction = Vector2.zero;
        if (null != anim) {
            anim.SetBool("run", false);
        }

        Scout();
    }

    protected override void FixedRotation() {
        if (!target_obj) {
            return;
        }

        Vector2 vectorToTarget = (Vector2)target_obj.position - (Vector2)transform.position;
        if (vectorToTarget.x * facing > 0) {
            // Same direction
            return;
        }

        facing *= -1;
    }
    
    public virtual GameObject OnAtk() {
        GameObject bullet = GetBullet();
        var pos = transform.position + new Vector3(0.5f * facing, 0.5f, 0);
        var critic = strengthen ? 40 : 0;
        int finalAtk = AtkDamage;
        var size = swordScale;
        var controller = master_tf.GetComponent<RGController>();
        if (controller) {
            //火力全开模式中熊的伤害每1级增加10%
            if (this is BearController && BattleData.data.HasActivityEnabled(ActivityFireManager.TAG)) {
                finalAtk = controller.ProcessSkillDamage(finalAtk, 0.1f);
            } else {
                finalAtk = controller.ProcessSkillDamage(finalAtk);
                size += 1f * controller.SkillLevel / 15;
            }
        }

        GameObject bulletSourceObject = GetBulletSourceObject();
        float angle = 0;

        var tempObj = BulletFactory.TakeBullet(
            new BulletInfo().SetUp(bullet, bulletSourceObject, 0, pos, 0, camp).SetBulletSize(size),
            new DamageInfo().SetUp(bullet, finalAtk, critic, 3, camp)
        );
        //子弹根据朝向翻转
        if (bulletAutoFacing) {
            Vector3 scale = tempObj.transform.localScale;
            if (controller) {
                scale.x *= controller.facing;
            } else {
                scale.x *= facing;
            }

            tempObj.transform.localScale = scale;
        }
        ModifyBullet(tempObj);
        HideBulletBySkin(tempObj);
        if (!controller) {
            return tempObj;
        }

        controller.DecorateSkillObject(tempObj);
        var skinIndex = controller.GetSkinIndex();
        var skillIndex = controller.skillIndex;
        OnAtkProcessSkillStrengthen(controller);
        var hero = controller.GetHeroType();

        if (isNpcDruidWolf) {
            var changer = GetComponent<FoceSkinChangerIndex>();
            skinIndex = changer.skinIndex;
            skillIndex = changer.skillIndex;
        }
        if ((hero == emHero.Druid && skinIndex == 12 && skillIndex == 0))
            RGMusicManager.GetInstance().PlayEffect(audio_clip_s12);
        else
            RGMusicManager.GetInstance().PlayEffect(audio_clip);
        return tempObj;
    }

    protected virtual GameObject GetBulletSourceObject() {
        if (null != master_tf) {
            return master_tf.gameObject;
        }

        return gameObject;
    }
    
    protected virtual GameObject GetBullet() {
        GameObject bullet = (strengthen && bullet2 != null) ? bullet2 : bullet1;
        return bullet;
    }

    protected virtual void OnAtkProcessSkillStrengthen(RGController controller) {
    }

    public virtual void ModifyBullet(GameObject bullet) {
    }

    private void HideBulletBySkin(GameObject bullet) {
        var skinIndex = 0;
        var skillIndex = 0;
        var hero = emHero.Druid;
        if (isNpcDruidWolf) {
            var changer = GetComponent<FoceSkinChangerIndex>();
            skinIndex = changer.skinIndex;
            skillIndex = changer.skillIndex;
        }
        else if (master_tf.GetComponent<RGController>()) {
            skinIndex = master_tf.GetComponent<RGController>().GetSkinIndex();
            skillIndex = master_tf.GetComponent<RGController>().skillIndex;
            hero = master_tf.GetComponent<RGController>().GetHeroType();
        } else if (master_tf.GetComponent<Troop2Mercenary>()) {
            skinIndex = master_tf.GetComponent<Troop2Mercenary>().SkinIndex;
            hero = master_tf.GetComponent<Troop2Mercenary>().Hero;
        }

        var renderers = bullet.GetComponentsInChildren<SpriteRenderer>();
        foreach (var spriteRenderer in renderers) {
            if (!(hero == emHero.Druid && skillIndex == 0)) {
                continue;
            }

            if (skinHideAttackSprite.Contains(skinIndex)) {
                spriteRenderer.enabled = false;
            }
        }
    }

    public override void Escape() {
        state = 0;
        gameObject.GetComponent<BoxCollider2D>().enabled = false;
        hpBar.TurnColor(UIHpBar.red);
    }
}
