using UnityEngine;

namespace RGScript.Character.Player.SkinSkillEffect {
    public class SkinObjectUsing : MonoBehaviour {
        public GameObject[] list;


#if UNITY_EDITOR
        [Sirenix.OdinInspector.<PERSON><PERSON>("复制prefab并重新赋值")]
        void CopyPrefabs() {
            for (int i = 0; i < list.Length; i++) {
                if (list[i] != null) {
                    string originalPath = UnityEditor.AssetDatabase.GetAssetOrScenePath(list[i]);
                    string targetDirectory = System.IO.Path.GetDirectoryName(UnityEditor.AssetDatabase.GetAssetOrScenePath(gameObject));
                    string fileName = System.IO.Path.GetFileName(originalPath);
                    string newPath = System.IO.Path.Combine(targetDirectory, fileName);
                    LogUtil.Log($"-- new path {newPath}");
                    UnityEditor.AssetDatabase.CopyAsset(originalPath, newPath);
                    list[i] = UnityEditor.AssetDatabase.LoadAssetAtPath<GameObject>(newPath);
                }
            }
            UnityEditor.AssetDatabase.Refresh();
        }
#endif
    }
}