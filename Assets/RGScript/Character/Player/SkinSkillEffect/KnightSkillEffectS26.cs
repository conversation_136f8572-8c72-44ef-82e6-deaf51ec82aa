using UnityEngine;
using DG.Tweening;
using System.Collections.Generic;

namespace RGScript.Character.Player.SkinSkillEffect {
    public class KnightSkillEffectS26 : MonoBehaviour {
        public SpriteRenderer spriteRenderer;
        public Sprite[] sprites;
        public float gapX = 2f;
        public float gapY = 2f;
        public float jumpTime = 0.8f;
        public float keepTime = 0.4f;

        private bool canUse = true;
        private GameObject parentObj;

        private void Start() {
            parentObj = transform.parent.gameObject;
        }

        private void Update() {
            if(canUse) Jump();
        }

        public void Jump() {
            int index = Random.Range(0, sprites.Length);
            canUse = false;

            if(parentObj != null) {
                transform.SetParent(parentObj.transform);
            } else {
                Destroy(gameObject);
            }
            
            Sprite sprite = sprites[index];
            spriteRenderer.sprite = sprite;
            spriteRenderer.color = Color.white;

            Transform curTransform = spriteRenderer.transform;
            curTransform.localPosition = Vector3.zero;
            curTransform.gameObject.SetActive(true);

            Vector3 posTo = Vector3.zero;
            int random = Random.Range(0, 100);
            if (random < 25) {
                posTo = curTransform.position - new Vector3(gapX, 0, 0);
            } else if (random < 50) {
                posTo = curTransform.position + new Vector3(gapX, 0, 0);
            } else if (random < 75) {
                posTo = curTransform.position - new Vector3(0, gapY, 0);
            } else if (random < 100) {
                posTo = curTransform.position + new Vector3(0, gapY, 0);
            }

            Vector3 endv = transform.localEulerAngles + new Vector3(0, 0, 180);
            curTransform.DOLocalRotate(endv, jumpTime);

            curTransform.DOJump(posTo, 1.5f, 1, jumpTime).OnComplete(() => {
                transform.SetParent(null);
                spriteRenderer.DOFade(0, keepTime).OnComplete(() => { 
                    canUse = true;
                });
            });
        }
    }
}
