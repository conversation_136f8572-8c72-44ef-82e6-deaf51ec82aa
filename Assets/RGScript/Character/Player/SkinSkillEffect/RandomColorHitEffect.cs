using UnityEngine;

namespace RGScript.Character.Player.SkinSkillEffect{
    public class RandomColorHitEffect : MonoBehaviour {
        public Color[] colors = new Color[] { Color.red, Color.blue, Color.green, Color.yellow, Color.magenta, Color.cyan };
        private SpriteRenderer _sr;
        private ParticleSystem _ps;

        void Awake() {
            _sr = GetComponent<SpriteRenderer>();
            _ps = GetComponent<ParticleSystem>();
        
            Color randomColor = colors[Random.Range(0, colors.Length)];
        
            _sr.color = randomColor;

            var main = _ps.main;
            main.startColor = randomColor;
        }
    }
}