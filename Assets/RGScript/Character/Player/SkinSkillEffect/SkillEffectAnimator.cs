using UnityEngine;

namespace RGScript.Character.Player.SkinSkillEffect {
    public class SkillEffectAnimator : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ISkillEffect {
        public Animator[] animators;
        public float destroyDelay = 0.2f;
        public static readonly int SkillEndHash = Animator.StringToHash("SkillEnd");

        private bool _isDestroyed;

        public void Init(RGBaseController controller) {
            foreach (var animator in animators) {
                animator.SetBool(SkillEndHash, false);
            }
        }

        public void EndSkill() {
            if (_isDestroyed) {
                return;
            }

            foreach (var animator in animators) {
                animator.SetBool(SkillEndHash, true);
            }
            GameObject.Destroy(gameObject, destroyDelay);
        }

        private void OnDestroy() {
            _isDestroyed = true;
        }
    }
}