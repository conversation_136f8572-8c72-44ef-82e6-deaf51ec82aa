using Papa.Util;
using RGScript.Util.QuickCode;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace RGScript.Character.Player {
    public class C27Skill3ShapeShiftCtrl : MonoBehaviour, IC27ShapeShiftCtrl {
        public C27Controller RoleCtrl {
            get {
                if (_roleCtrl == null) {
                    _roleCtrl = GetComponentInParent<C27Controller>();
                }
                return _roleCtrl;
            }
        }
        private C27Controller _roleCtrl;
        public Transform Tf => transform;
        public bool CanGetForce => false;
        public C27Skill3AttributeConfig config;
        public C27Skill3Prefab prefabs;
        private Transform TempParent => RGGameSceneManager.Inst?.temp_objects_parent;

        #region 生命周期相关
        public bool IsShifted { get; private set; }

        public void InitShiftCtrl() {
            _animator = gameObject.GetComponent<Animator>();
            InitSkillWeapon();
            InitSlider();
        }

        private void OnDisable() {
            if (IsShifted) {
                StopShiftShape();
            }
        }

        private void OnEnable() {
            if (!IsShifted) {
                gameObject.SetActive(false);
            }
        }

        public void StartShiftShape() {
            IsShifted = true;
            RoleCtrl.RemoveAllArrowRoot();
            PlayStartAnimation();
            StoreRoleAttribute();
            StoreRoleWeapon();

            _isMainSkillReady = true;
            _canHit = true;

            SimpleEventManager.AddEventListener<EnemyGetHurt>(OnCreateDamage);
        }

        public void EndShiftShape() {
            SimpleEventManager.RemoveListener<EnemyGetHurt>(OnCreateDamage);

            if (_mainSkillCdTimer != null) {
                _mainSkillCdTimer.Cancel();
                _mainSkillCdTimer = null;
                _isMainSkillReady = true;
            }

            if (_hitInvincibilityTimer != null) {
                _hitInvincibilityTimer.Cancel();
                _hitInvincibilityTimer = null;
                _canHit = true;
            }
            
            _swordMissile.ResetStates();
            _swordRevolve.ResetStates();
            _animator.SetBool(AnimaHasSword, true);
            _currentPunchTime = 0;

            PlayEndAnimation();
            _animator.SetBool(AnimaSkillB, false);
            IsDoingSkill = false;
            RoleCtrl.SetMainSkillBtnColor(false);
        }

        public void StopShiftShape() {
            RoleCtrl.RoleSkillEnd();
            OnShiftShapeEnd();
        }

        public void OnRoleMove(MovementInput m) {
            bool isShapeRunning = _animator.GetBool(AnimaRun);
            bool isRoleRunning = RoleCtrl.anim.GetBool(AnimaRun);
            if (isShapeRunning != isRoleRunning) {
                _animator.SetBool(AnimaRun, isRoleRunning);
            }
        }
        
        private void FixedUpdate() {
            RoleCtrl.rigibody.velocity *= _speed;
        }
        #endregion

        #region 战勇相关
        public int Hp {
            get => _sliderCtrl.CurrentValorValue;
            set {
                if(value == _sliderCtrl.CurrentValorValue || _sliderCtrl.IsDead) return;
                if (((IsHpProtect && value - _sliderCtrl.CurrentValorValue > 0) || !IsHpProtect) && 
                    !debugIsLockHp) {
                    _sliderCtrl.CurrentValorValue = value;
                }
                if (_sliderCtrl.IsDead) {
                    RoleCtrl.RoleSkillEnd();
                    return;
                }

                if (_isValor != _sliderCtrl.IsValor) {
                    DoPassiveSkill();
                }
            }
        }
        private bool _isValor;
        private C27Skill3ValorSliderCtrl _sliderCtrl;

        private bool IsHpProtect {
            get => _isHpProtect;
            set {
                if (value) {
                    _isHpProtect = true;
                    if (_protectHpTimer != null) {
                        _protectHpTimer.Cancel();
                        _protectHpTimer = null;
                    }
                    _protectHpTimer = Timer.Register(
                        config.turnValorHpProtectTime, false, false, ()=> _isHpProtect = false );
                } else {
                    _isHpProtect = false;
                    if (_protectHpTimer != null) {
                        _protectHpTimer.Cancel();
                        _protectHpTimer = null;
                    }
                }
            }
        }
        private bool _isHpProtect;
        private Timer _protectHpTimer;
        
        private static readonly int AnimaIsValor = Animator.StringToHash("isValor");
        private CustomBuffImmure _buffImmure;

        private void InitSlider() {
            _sliderCtrl = transform.Find("img/ValorSlider").GetComponent<C27Skill3ValorSliderCtrl>();
            _sliderCtrl.InitSlider(config.initValorValue, config.initValorTurnOnValue, config.initMaxValorValue);
            _phantom = transform.Find("img/body/phantom/phantom_creator").gameObject;
        }

        public void DoPassiveSkill() {
            _isValor = _sliderCtrl.IsValor;
            _animator.SetBool(AnimaIsValor, _isValor);
            float animaSpeed = _isValor ? config.valorAnimatorSpeed : 1;
            var battleData = GameUtil.GetBattleData(RoleCtrl);
            if (battleData.HasBuff(emBuff.MasterSpeedAtk)) {
                animaSpeed *= 1.1f;
            }
            _animator.speed = animaSpeed;
            if (_buffImmure != null) {
                _buffImmure.immure_all = _isValor;
            }

            if (!_isValor) {
                return;
            }
            RGMusicManager.Inst.PlayEffect(prefabs.turnValorSoundFx);
            _valorStateValorValueReduceTimer = 0;
            IsHpProtect = true;
        }

        private int _energyCountPreLevel;
        private int OnGetDamage(int damage) {
            if (damage > config.notValorGetDamageLimit) {
                damage = config.notValorGetDamageLimit;
            }
            int finalDamage = damage;
            float changeValue = damage * config.getDamageValorConversionRate;
            Hp -= Mathf.CeilToInt(changeValue);
            
            // 导师强化受伤概率掉蓝
            if (!RoleCtrl.IsDemonstrationCharacter && _energyCountPreLevel <= 10 &&
                _roleCtrl.HasSkillExtraUpdate && _isValor && RoleCtrl.rg_random.GetBool(0.1f)) {
                float rx = RoleCtrl.rg_random.Range(-2f, 2f);
                float ry = RoleCtrl.rg_random.Range(-2f, 2f);
                int r3 = RoleCtrl.rg_random.Range(5, 15);
                GameObject tempObj = Instantiate(PrefabManager.GetPrefab(PrefabName.energy), transform.position, Quaternion.identity);
                var rgCoin = tempObj.GetComponent<RGCoin>(); 
                rgCoin.GetForce(new Vector2(rx, ry), r3);
                _energyCountPreLevel++;
            }
            return finalDamage;
        }
        
        private void OnCreateDamage(EnemyGetHurt e) {
            if (!IsShifted || e.hurtInfo.Source != RoleCtrl.gameObject) {
                return;
            }
            int damage = e.hurtInfo.Damage;
            if(damage < 0)return;
            
            int changeValue = Mathf.CeilToInt(damage * config.createDamageValorConversionRate);
            Hp += changeValue;
            _notCreateDamageTimer = 0;
            IsSpeedUp = true;

            // Debug.Log($"C27S3>Create [{damage}] Damage at {e.enemy.name}, add [{changeValue}] Valor");
            if (e.enemy.dead) {
                int killedAdditive = config.killedEnemyValorAdditive;
                var battleData = GameUtil.GetBattleData(RoleCtrl);
                if (battleData.HasBuff(emBuff.VampireHp)) {
                    killedAdditive += 5;
                }
                Hp += killedAdditive;
                // Debug.Log($"C27S3>Enemy {e.enemy.name} Dead, add [{killedAdditive}] Valor");
            }
            
            if (GameUtil.IsMultiGame() && RoleCtrl.IsLocalPlayer()) {
                MessageManager.SendCostumePrinceHolsterGetHurtMessage(NetControllerManager.Inst.localController.netId, Hp);
            }
        }

        #region 计时器相关
        private void Update() {
            if (!RoleCtrl.skillCasting) return;
            if (IsSpecialBtnClick) DoMainSkill();
            CheckNormalAtk();

            float dTime = Time.deltaTime;
            UpdateNotCreateDamageTimer(dTime);
            if (_isValor) {
                UpdateValorStateValorValueReduceTimer(dTime);
            }

            _handRRotateTf.localEulerAngles = new Vector3(0, 0, RoleCtrl.FixedAngle);
            _handLRotateTf.localEulerAngles = new Vector3(0, 0, RoleCtrl.FixedAngle);
        }

        private float _notCreateDamageTimer;
        private float _notCreateDamageValorValueReduceTimer;
        private void UpdateNotCreateDamageTimer(float dTime) {
            _notCreateDamageTimer += dTime;
            if (_notCreateDamageTimer < config.notCreateDamageTimeLimit) {
                return;
            }
            IsSpeedUp = false;
            _notCreateDamageValorValueReduceTimer += dTime;
            while (_notCreateDamageValorValueReduceTimer >= 1) {
                _notCreateDamageValorValueReduceTimer -= 1;
                Hp -= config.notCreateDamageValorValueReducePerSecond;
            }
        }

        private float _valorStateValorValueReduceTimer;
        private void UpdateValorStateValorValueReduceTimer(float dTime) {
            _valorStateValorValueReduceTimer += dTime;
            while (_valorStateValorValueReduceTimer >= 1) {
                _valorStateValorValueReduceTimer -= 1;
                Hp -= config.valorStateValorValueReducePerSecond;
            }
        }

        private void ResetAllTimers() {
            _notCreateDamageTimer = 0;
            _notCreateDamageValorValueReduceTimer = 0;
            _valorStateValorValueReduceTimer = 0;
            IsHpProtect = false;
        }
        #endregion

        private int GetFinalDamage(int damage) {
            int finalDamage = damage;
            if (_isValor) {
                finalDamage += config.valorStateDamageAdditive;
            }
            if (BattleData.data.gameMode == emGameMode.Defence) {
                finalDamage += RoleCtrl.SkillLevel * 2;
            }
            return finalDamage;
        }

        private int GetFinalCritic() => _isValor?  config.initCritic + config.valorCriticAdditive : config.initCritic;
        #endregion

        #region 技能相关
        private static readonly int AnimaSkillB = Animator.StringToHash("skill_b");
        public bool IsDoingSkill { get; private set; }

        private bool _isMainSkillReady;
        private Timer _mainSkillCdTimer;
        public bool IsSpecialBtnClick { get; set; }

        public void DoMainSkill() {
            if (IsDoingSkill || !_isMainSkillReady || Hp <= 0) {
                return;
            }
            _animator.SetBool(AnimaSkillB, true);
            IsDoingSkill = true;
            RoleCtrl.SetMainSkillBtnColor(true);
        }

        private void AnimaOnMainSkillOffGround() {
            SetJumping(true);
        }

        private void AnimaOnMainSkillLand() {
            SetJumping(false);
            CreateLandDamage();
            
            CreateBlade(_handLRotateTf.position, RoleCtrl.oriantationAngle ,true);
            CreateBlade(_handRRotateTf.position, RoleCtrl.oriantationAngle ,false);
            
            var jazzLandingGo = Instantiate(prefabs.jazzLeapLandingPrefab, TempParent);
            var damageCarrier = jazzLandingGo.GetComponent<DamageCarrier>();
            (BulletInfo bInfo, DamageInfo dInfo) = 
                CreateBulletInfo(prefabs.jazzLeapLandingPrefab, config.jazzLeapLandingDamage, RoleCtrl.transform.position);
            bInfo.duration = config.jazzLeapLandingEffectDuration;
            damageCarrier.UpdateInfo(bInfo, dInfo);
            jazzLandingGo.transform.position = RoleCtrl.transform.position;
            
            var buffZone = jazzLandingGo.GetComponentInChildren<BuffZoneCtrl>();
            buffZone.camp = 1;
            buffZone.sourceObject = RoleCtrl.gameObject;
            buffZone.buffDuration = config.jazzLeapLandingDizzyDuration;
            if (!RoleCtrl.attribute.skill_strengthen) {
                buffZone.buffPrefab = null;
            }

            float size = config.jazzLeapLandingRadius;
            if (_isValor) {// 战勇圈随战勇值大小而改变
                size *= (100 + Hp)/100f;
            }
            if (RoleCtrl.attribute.skill_strengthen) {// 技能升级战勇圈再大50%
                size *= 1.5f;
            }
            ((CircleDamageCarrier)damageCarrier).radius = size;
            Transform particleParent = damageCarrier.transform.Find("burn_effect/bullet_flame_spread");
            var particles = particleParent.GetComponentsInChildren<ParticleSystem>();
            foreach (var particle in particles) {
                ParticleSystem.ShapeModule shapeModule = particle.shape;
                shapeModule.radius = 5.0f;
            }
            buffZone.transform.localScale = new Vector3(size, size, 1);
            CreateBuffCircle();
        }

        private void CreateBuffCircle() {
            if (!RoleCtrl.attribute.skill_strengthen) {
                return;
            }
            var targetZonePrefab = RoleCtrl.rg_random.GetBool() ? prefabs.fireZonePrefab : prefabs.poisonZonePrefab;
            Transform buffZone = Instantiate(targetZonePrefab, TempParent).transform;
            buffZone.position = transform.position;
            buffZone.GetComponent<OffensiveInterface>().SetSourceObject(gameObject);
            if (buffZone.GetComponent<BulletFire>() != null) {
                var fireBuffZoneCtrl = buffZone.GetComponent<BulletFire>();
                fireBuffZoneCtrl.camp = 1;
                fireBuffZoneCtrl.hit_enemy = true;
                fireBuffZoneCtrl.hit_player = false;
                return;
            }
            var gasBuffZoneCtrl = buffZone.GetComponent<BulletGas>();
            gasBuffZoneCtrl.camp = 1;
            gasBuffZoneCtrl.hit_enemy = true;
            gasBuffZoneCtrl.hit_player = false;
        }

        private void AnimaOnMainSkillEnd() {
            IsDoingSkill = false;
            RoleCtrl.ResetRoleHandPos();

            if (_mainSkillCdTimer != null) {
                _mainSkillCdTimer.Cancel();
                _mainSkillCdTimer = null;
            }

            _isMainSkillReady = false;
            float mainSkillCd = _isValor? config.initMainSkillCd * config.valorStateCdAdditive : config.initMainSkillCd;
            var battleData = GameUtil.GetBattleData(RoleCtrl);
            if (battleData.HasBuff(emBuff.SkillCd)) {
                mainSkillCd *= 0.8f;// 技能cd减少天赋
            }
            _mainSkillCdTimer = Timer.Register(mainSkillCd, false, false, () => {
                _isMainSkillReady = true;
                RoleCtrl.SetMainSkillBtnColor(false);
            });
            _normalAtkTimesAfterMainSkill = 0;
            RoleCtrl.SetMainSkillBtnColor(true);
            _animator.SetBool(AnimaSkillB, false);
        }

        private void SetJumping(bool isJumping) {
            RoleCtrl.isJumping = isJumping;
            transform.Find("collider").gameObject.layer = LayerMask.NameToLayer(isJumping ? "Fly" : "Body");
            RoleCtrl.SetFlyable(isJumping);
        }
        #endregion

        #region 普攻相关
        private static readonly int AnimaAtkB = Animator.StringToHash("atk_b");
        private static readonly int AnimaHasSword = Animator.StringToHash("hasSword");
        private  static  readonly int AnimaLeft = Animator.StringToHash("isLeft");
        
        private C27Skill3FlySwordCtrl _swordMissile;
        private C27Skill3FlySwordCtrl _swordRevolve;
        private Transform _punchPointL;
        private Transform _punchPointR;
        private int _normalAtkTimesAfterMainSkill;

        private bool _isAtkBtnHold;
        
        public bool DoNormalSkill(bool isStart) {
            _isAtkBtnHold = isStart;
            if (!_isAtkBtnHold) {
                _animator.SetBool(AnimaAtkB, false);
            } else {
                CheckNormalAtk();
            }
            return true;
        }

        private void CheckNormalAtk() {
            if (_isAtkBtnHold && 
                (_animator.GetCurrentAnimatorStateInfo(1).IsName("idle") ||
                 _animator.GetCurrentAnimatorStateInfo(1).IsName("run"))) {
                _animator.SetBool(AnimaAtkB, true);
            }
        }
        
        private void OnCreateSwingSwordBullet() {
            _normalAtkTimesAfterMainSkill++;
            if (_normalAtkTimesAfterMainSkill > config.swingSwordBulletCreateTimesAfterMainSkill &&// 主动技能后普攻加弹幕
                !(_isValor && 
                 RoleCtrl.HasSkillExtraUpdate && 
                 _normalAtkTimesAfterMainSkill % config.swingSwordBulletCreateTimesInValor == 0)) {// 导师增强后每三次普攻加弹幕
                return;
            }
            
            var dir = RoleCtrl.target_obj != null
                ? (RoleCtrl.target_obj.transform.position - _handsParent.position).normalized
                : RoleCtrl.oriantation;
            var angle = Vector3.SignedAngle(Vector3.right, dir, Vector3.forward);
            angle -= 120 / 2f;
            int bNum = config.swingSwordBulletNum;
            var battleData = GameUtil.GetBattleData(RoleCtrl);
            if (battleData.HasBuff(emBuff.MasterShotgun)) {
                bNum += 2;
            }
            float angleInterval = 120 * 1.0f / (bNum - 1);

            float emitDistance = 1f;
            for (int i = 0; i <= bNum; ++i) {
                var pos = _handsParent.position + Quaternion.Euler(0, 0, angle) * (Vector3.right * emitDistance);
                (BulletInfo bInfo, DamageInfo dInfo) = CreateBulletInfo(
                    prefabs.swingSwordBulletPrefab, config.swingSwordBulletDamage, pos);
                bInfo.SetAngle(angle);
                bInfo.SetSpeed(10);
                var b = BulletFactory.TakeBullet(bInfo, dInfo);
                b.GetComponent<RGBullet>().hitOn = true;
                angle += angleInterval;
            }
        }

        private void AnimaOnSwordFlyOut() {
            RGMusicManager.Inst.PlayEffect(prefabs.swordFlyOutSoundFx);
            _animator.SetBool(AnimaHasSword, false);
            Vector3 targetPos;
            bool hasBuff = RoleCtrl.attribute.skill_strengthen;
            float length = 20f;
            if (RoleCtrl.has_target) {
                var dir = (RoleCtrl.TargetObject.position - RoleCtrl.CurPos).normalized;
                dir.z = 0;
                targetPos = RoleCtrl.TargetObject.position + dir * length;
                _swordMissile.damage = config.swordMissileDamage;
                _swordMissile.ThrowOutSword(targetPos, hasBuff);
                targetPos = RoleCtrl.TargetObject.position + dir * 2;
                _swordRevolve.damage = config.swordRevolveDamage;
            } else {
                var dir = RoleCtrl.FixedDirection.normalized;
                targetPos = RoleCtrl.transform.position + dir.Vec3() * length;
                _swordMissile.ThrowOutSword(targetPos, hasBuff);
            }
            _swordRevolve.ThrowOutSword(targetPos, hasBuff);
        }

        private int _currentPunchTime;
        private void AnimaOnPunch() {
            _currentPunchTime++;
            
            RGMusicManager.Inst.PlayEffect(prefabs.punchSoundFx);
            var punchPoint = _currentPunchTime % 2 == 0 ? _punchPointL : _punchPointR;
            Vector2 createPos = punchPoint.position;
            (BulletInfo bInfo, DamageInfo dInfo) = CreateBulletInfo(
                prefabs.punchPrefab, config.initPunchDamage, createPos, config.initPunchSize);
            bInfo.directionAngle = RoleCtrl.oriantationAngle;
            var punchGo = BulletFactory.TakeBullet(bInfo, dInfo);
            var buffTrigger = punchGo.GetComponent<BuffEffectTrigger>();
            buffTrigger.active = RoleCtrl.attribute.skill_strengthen;
            buffTrigger.SetSourceObject(RoleCtrl.gameObject, 1);
            var battleData = GameUtil.GetBattleData(RoleCtrl);
            if (battleData.HasBuff(emBuff.QigongWave)) {
                GameObject qiGong = CommonAssets.Assets.GetAirbenderQigongWave(0)[1];
                var pos = transform.position + (Vector3)RoleCtrl.FixedDirection.normalized * 1;
                bInfo.damage = 2;
                dInfo.damage = 2;
                bInfo.bulletProto = qiGong;
                bInfo.createPosition = pos;
                float curAngle = RoleCtrl.facing > 0 ? RoleCtrl.FixedAngle : -RoleCtrl.FixedAngle;
                var b =BulletFactory.TakeBullet(bInfo, dInfo);
                b.GetComponent<QigongWave>().SetAngle(RoleCtrl.facing == 1 ? curAngle : 180 + curAngle, punchPoint);
                RoleCtrl.attribute.RestoreEnergy(-2);
            }
            
            if (_currentPunchTime == 4) {
                _currentPunchTime = 0;
            }
        }
        
        private void OnSwordBack(C27Skill3FlySwordCtrl swordCtrl) {
            RGMusicManager.Inst.PlayEffect(prefabs.swordFlyBackSoundFx);
            if (_swordMissile.IsFlying || _swordRevolve.IsFlying) {
                return;
            }
            _animator.SetBool(AnimaHasSword, true);
            _currentPunchTime = 0;
        }

        private void AnimaOnCreateBladeR() {
            CreateBlade(_handRRotateTf.position, RoleCtrl.oriantationAngle,false);
            OnCreateSwingSwordBullet();
        }

        private void AnimaOnCreateBladeL() {
            CreateBlade(_handLRotateTf.position, RoleCtrl.oriantationAngle,true);
            OnCreateSwingSwordBullet();
        }

        private void AnimaOnCreateBlade360() {
            CreateBlade(_handsParent.position, 0 , true);
            CreateBlade(_handsParent.position, 180 , false);
            OnCreateSwingSwordBullet();
        }

        private void CreateBlade(Vector2 pos, float angle,bool isLeft) {
            RGMusicManager.GetInstance().PlayEffect(prefabs.bladeSoundFx);
            prefabs.bladePrefab.GetComponent<RGSword>().revert = RoleCtrl.facing != -1;
            prefabs.bladePrefabValor.GetComponent<RGSword>().revert = RoleCtrl.facing != -1;
            float bladeSize = _isValor ? config.valorBladeSize : config.initBladeSize;
            if (RoleCtrl.thisBattleData.HasBuff(emBuff.MasterSwordRange)) {
                bladeSize *= 1.3f;// 刀光增大天赋
            }
            (BulletInfo bInfo, DamageInfo dInfo) = CreateBulletInfo(_isValor? prefabs.bladePrefabValor : prefabs.bladePrefab, config.initBladeDamage, pos, bladeSize);
            bInfo.directionAngle = angle;
            GameObject bullet = BulletFactory.TakeBullet(bInfo, dInfo);
            Animator anim = bullet.GetComponent<Animator>();
            if (anim.HasParam(AnimaLeft)) {
                anim.SetBool(AnimaLeft, isLeft);
            }
        }
        
        private void AnimaOnCreateSpear() {
            RGMusicManager.GetInstance().PlayEffect(prefabs.spearSoundFx);
            prefabs.spearPrefab.GetComponent<RGSword>().revert = RoleCtrl.facing != -1;
            prefabs.spearPrefabValor.GetComponent<RGSword>().revert = RoleCtrl.facing != -1;
            (BulletInfo bInfo, DamageInfo dInfo) = CreateBulletInfo(
                _isValor ? prefabs.spearPrefabValor : prefabs.spearPrefab, config.initSpearDamage,
                _handsParent.position, config.initSpearSize);
            bInfo.directionAngle = RoleCtrl.oriantationAngle;
            var spear = BulletFactory.TakeBullet(bInfo, dInfo).GetComponent<RGSword>();
            spear.revert = RoleCtrl.facing != -1;
            OnCreateSwingSwordBullet();
        }
        #endregion

        #region 角色状态保存相关
        private string PropertyModifyKey => "C27Skill3";
        private GameObject _phantom;
        private float _speed;
        private bool IsSpeedUp {
            set {
                _phantom.SetActive(value);
                _speed = value ? config.attackMoveSpeedAdditive : config.notAttackMoveSpeedAdditive;
            }
        }

        private void StoreRoleAttribute() {
            RoleCtrl.onRoleMoveIgnoreAttrDisable += OnRoleMove;

            var gsm = RGGameSceneManager.GetInstance();
            gsm.GSDelegate += RoleCtrl.SetRoleTriggerSize;
            if (gsm.inBattle || DataUtil.IsHeroRoom()) {
                RoleCtrl.SetRoleTriggerSize(1);
            }
            
            _buffImmure = RoleCtrl.gameObject.AddComponent<CustomBuffImmure>();
            
            _normalAtkTimesAfterMainSkill = 100;

            int maxValorValue = config.initMaxValorValue;
            if (_roleCtrl.HasSkillExtraUpdate) {
                maxValorValue += 20;
            }
            var battleData = GameUtil.GetBattleData(RoleCtrl);
            if (battleData.HasBuff(emBuff.MaxHp)) {
                maxValorValue += 20;
            }
            if (BattleData.data.gameMode == emGameMode.Defence) {
                maxValorValue += RoleCtrl.SkillLevel / 3;
            }
            int initValorValue = config.initValorValue;
            if (RoleCtrl.attribute.skill_strengthen) {
                initValorValue += 10;
            }
            _sliderCtrl.InitSlider(initValorValue, config.initValorTurnOnValue, maxValorValue);
            
            ResetAllTimers();
            DoPassiveSkill();
            IsHpProtect = true;
            IsSpeedUp = false;
        }

        private void RestoreRoleAttribute() {
            RoleCtrl.UnModifyExtraSpeedFactor(PropertyModifyKey);
            RoleCtrl.onRoleMoveIgnoreAttrDisable -= OnRoleMove;
            if (RGGameSceneManager.GetInstance() != null) {
                RGGameSceneManager.GetInstance().GSDelegate -= RoleCtrl.SetRoleTriggerSize;
            }

            RoleCtrl.SetRoleTriggerSize(-1);
            if (_buffImmure != null) {
                Destroy(_buffImmure);
            }
            ResetAllTimers();
            SetJumping(false);

            // 技能cd减少天赋
            float shiftCd = config.shiftCd;
            var battleData = GameUtil.GetBattleData(RoleCtrl);
            if (battleData.HasBuff(emBuff.SkillCd)) {
                shiftCd *= 0.7f;
            }
            RoleCtrl.skillInfo.baseCooldown = shiftCd;
        }

        #endregion

        #region 动画相关

        public GameObject shiftEffectPrefab;
        public AudioClip shiftFx;
        public AudioClip shiftBackFx;

        private Animator _animator;
        private static readonly int AnimaRun = Animator.StringToHash("run");
        private static readonly int AnimaStop = Animator.StringToHash("stop");

        private void PlayStartAnimation() {
            RoleCtrl.ChangeSpriteColor(new Color(0, 0, 0, 0), 0);
            gameObject.SetActive(true);
            _animator.Play("start", -1, 0f);
            _animator.Update(0f);
            OnRoleMove(default);
            RGMusicManager.GetInstance().PlayEffect(shiftFx);

            RoleCtrl.transform.Find("shadow_lock").gameObject.SetActive(false);
            RoleCtrl.transform.Find("shadow").gameObject.SetActive(false);
            SetJumping(true);
        }

        private void PlayEndAnimation() {
            IsSpeedUp = false;
            _animator.SetTrigger(AnimaStop);
            RGMusicManager.GetInstance()?.PlayEffect(prefabs.shiftBackStartSoundFx);
        }

        private void AnimaOnStartLand() {
            var effectTf = Instantiate(shiftEffectPrefab, transform).transform;
            effectTf.localPosition = Vector3.down;
            CreateLandDamage();
            SetJumping(false);
        }

        private void OnShiftShapeEnd() {
            RestoreRoleAttribute();
            RestoreRoleWeapon();
            
            var effectTf = Instantiate(shiftEffectPrefab, TempParent).transform;
            effectTf.position = transform.position + Vector3.down;
            RGMusicManager.GetInstance()?.PlayEffect(shiftBackFx);
            
            RoleCtrl.RemoveAllArrowRoot();
            RoleCtrl.ResetRoleHandPos();
            RoleCtrl.ResetSpriteColor();
            
            transform.Find("img").LocalRest();
            Transform shadowTf = transform.Find("shadow");
            shadowTf.LocalRest();
            shadowTf.localPosition = new Vector3(0, -0.4f, 0);
            shadowTf.localScale = new Vector3(2.3f, 2.3f, 1);

            RoleCtrl.transform.Find("shadow_lock").gameObject.SetActive(true);
            RoleCtrl.transform.Find("shadow").gameObject.SetActive(true);
            IsShifted = false;
            gameObject.SetActive(false);
        }
        #endregion

        #region 武器处理相关
        private RGWeapon _skillWeapon;
        private RGHand _roleHand;
        private RGWeapon _roleWeapon;
        private Transform _storeWeaponParent;
        private Transform _handsParent;
        private Transform _handRRotateTf;
        private Transform _handLRotateTf;

        private void InitSkillWeapon() {
            _roleHand = RoleCtrl.hand;
            _storeWeaponParent = transform.Find("weapons");

            _skillWeapon = RGWeapon.CreateSkillWeapon(prefabs.weaponPrefab);
            _skillWeapon.gameObject.SetActive(false);
            _skillWeapon.transform.SetParent(_storeWeaponParent);
            
            _handsParent = transform.Find("img/body/hands");
            _handRRotateTf = _handsParent.Find("right/hand");
            _handLRotateTf = _handsParent.Find("left/hand");
            _punchPointL = _handsParent.Find("left/hand/punch_point");
            _punchPointR = _handsParent.Find("right/hand/punch_point");
            
            _swordMissile = Instantiate(prefabs.swordMissilePrefab, _handsParent).GetComponent<C27Skill3FlySwordCtrl>();
            _swordMissile.Init(RoleCtrl, _handsParent, RoleCtrl.gameObject, OnSwordBack);
            _swordMissile.gameObject.SetActive(false);
            _swordRevolve = Instantiate(prefabs.swordRevolvePrefab, _handsParent).GetComponent<C27Skill3FlySwordCtrl>();
            _swordRevolve.Init(RoleCtrl, _handsParent, RoleCtrl.gameObject, OnSwordBack);
            _swordRevolve.gameObject.SetActive(false);
        }

        private void StoreRoleWeapon() {
            RoleCtrl.RecordRoleHandPos();
            _roleWeapon = _roleHand.front_weapon;
            _roleHand.PickUpItem(_skillWeapon.transform, false, false, true);
            _skillWeapon.gameObject.SetActive(false);
            RGWeapon[] weapons = _roleHand.GetBackWeapons();
            foreach (var w in weapons) {
                w.HideWeapon();
            }
            
            var fishRod = _roleCtrl.Back.GetComponentInChildren<FishRod>(true);
            if (fishRod) {
                fishRod.gameObject.SetActive(false);
            }
            
        }

        private void RestoreRoleWeapon() {
            List<RGWeapon> storedWeapons = _storeWeaponParent.GetComponentsInChildren<RGWeapon>(true).ToList();
            foreach (var w in storedWeapons) {
                w.ShowWeapon();
                RoleCtrl.hand.SetWeaponBack(w, false, false);
            }
            
            var fishRod = _roleCtrl.Back.GetComponentInChildren<FishRod>(true);
            if (fishRod) {
                fishRod.gameObject.SetActive(true);
            }

            if (_roleWeapon != null) {
                _roleHand.SwitchWeaponFromBack(_roleWeapon);
                _roleHand.ShowWeapon();
            }

            if (_skillWeapon == null) {
                return;
            }

            _skillWeapon.StopWeapon();
            RoleCtrl.hand.DropWeapon(_skillWeapon);
            _skillWeapon.gameObject.SetActive(false);
            _skillWeapon.transform.SetParent(_storeWeaponParent);
        }
        #endregion

        #region 受击处理
        public AudioClip sufferFx;
        private float HitInvincibilityFrameDuration => config.hitInvincibilityFrameDuration;
        private Timer _hitInvincibilityTimer;
        private bool _canHit;

        public void OnGetHurt(HurtInfo hurtInfo) {
            if (!_canHit) {
                return;
            }

            _canHit = false;
            if (_hitInvincibilityTimer != null) {
                _hitInvincibilityTimer.Cancel();
                _hitInvincibilityTimer = null;
            }

            _hitInvincibilityTimer = Timer.Register(HitInvincibilityFrameDuration, false, false, () => _canHit = true);
            RGMusicManager.GetInstance().PlayEffect(sufferFx);

            int finalDamage = OnGetDamage(hurtInfo.Damage);
            hurtInfo.Damage = finalDamage;
            UICanvas.GetInstance().ShowTextHurt(transform, hurtInfo, 2, RoleCtrl.name);
            _canHit = false;
            if (GameUtil.IsMultiGame() && RoleCtrl.IsLocalPlayer()) {
                MessageManager.SendCostumePrinceHolsterGetHurtMessage(NetControllerManager.Inst.localController.netId, Hp);
            }

            if (_hitInvincibilityTimer != null) {
                _hitInvincibilityTimer.Cancel();
                _hitInvincibilityTimer = null;
            }

            _hitInvincibilityTimer = Timer.Register(HitInvincibilityFrameDuration, false, false, () => _canHit = true);
        }
        #endregion
        
        public (BulletInfo bInfo, DamageInfo dInfo) CreateBulletInfo(GameObject bulletProto, int damage, Vector2 pos, float size = 1) {
            int finalDamage = GetFinalDamage(damage);
            int finalCritic = GetFinalCritic();
            var bInfo = new BulletInfo {
                bulletProto = bulletProto,
                sourceObject = RoleCtrl.gameObject,
                createPosition = pos,
                size = size,
                camp = 1,
                damage = finalDamage,
            };
            var dInfo = new DamageInfo {
                damage = finalDamage,
                camp = 1,
                critic = finalCritic
            };
            return (bInfo, dInfo);
        }
        
        private void CreateLandDamage() {
            GameUtil.CameraShake(2);
            GameObject landDamageGo = Instantiate(prefabs.landDamagePrefab, TempParent);
            landDamageGo.transform.position = transform.position;
            var landDamageScript = landDamageGo.GetComponent<ExplodeHammer>();
            landDamageScript.damage = GetFinalDamage(config.landDamage);
            landDamageScript.critic = GetFinalCritic();
            landDamageScript.SetSourceObject(RoleCtrl.gameObject);
            landDamageScript.OnTaken();
        }
#if UNITY_EDITOR
        [Sirenix.OdinInspector.Button]
#endif
        private void DebugSetHp(int hp) {
            var isLock = _isHpProtect;
            var isDebugLock = debugIsLockHp;
            _isHpProtect = false;
            debugIsLockHp = false;
            Hp = hp;
            _isHpProtect =  isLock;
            debugIsLockHp = isDebugLock;
        }

        public bool debugIsLockHp;
        
#if UNITY_EDITOR
        [Sirenix.OdinInspector.Button("复制皮肤prefabs")]
        private void CopyPrefabs() {
            prefabs.weaponPrefab = CopyNewPrefab(prefabs.weaponPrefab);
            prefabs.bladePrefab = CopyNewPrefab(prefabs.bladePrefab);
            prefabs.bladePrefabValor = CopyNewPrefab(prefabs.bladePrefabValor);
            prefabs.spearPrefab = CopyNewPrefab(prefabs.spearPrefab);
            prefabs.spearPrefabValor = CopyNewPrefab(prefabs.spearPrefabValor);
            prefabs.swordMissilePrefab = CopyNewPrefab(prefabs.swordMissilePrefab);
            prefabs.swordRevolvePrefab = CopyNewPrefab(prefabs.swordRevolvePrefab);
            prefabs.swingSwordBulletPrefab = CopyNewPrefab(prefabs.swingSwordBulletPrefab);
            prefabs.jazzLeapLandingPrefab = CopyNewPrefab(prefabs.jazzLeapLandingPrefab);
            UnityEditor.EditorUtility.SetDirty(this);
            UnityEditor.AssetDatabase.SaveAssetIfDirty(this);
            UnityEditor.AssetDatabase.Refresh();
        }

        private GameObject CopyNewPrefab(GameObject prefab) {
            string originalPath = UnityEditor.AssetDatabase.GetAssetOrScenePath(prefab);
            string targetDirectory =
                System.IO.Path.GetDirectoryName(UnityEditor.AssetDatabase.GetAssetOrScenePath(gameObject));
            string fileName = System.IO.Path.GetFileName(originalPath);
            string newPath = System.IO.Path.Combine(targetDirectory, fileName);
            LogUtil.Log($"-- new path {newPath}");
            UnityEditor.AssetDatabase.CopyAsset(originalPath, newPath);
            return UnityEditor.AssetDatabase.LoadAssetAtPath<GameObject>(newPath);
        }
#endif
    }
}