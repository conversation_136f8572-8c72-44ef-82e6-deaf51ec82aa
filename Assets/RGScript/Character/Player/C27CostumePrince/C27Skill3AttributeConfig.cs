using System;
using UnityEngine;

namespace RGScript.Character.Player {
    [Serializable]
    public class C27Skill3Prefab {
        public GameObject weaponPrefab;
        public GameObject landDamagePrefab;
        public GameObject punchPrefab;
        public AudioClip punchSoundFx;
        public GameObject bladePrefab;
        public GameObject bladePrefabValor;
        public AudioClip bladeSoundFx;
        public GameObject spearPrefab;
        public GameObject spearPrefabValor;
        public AudioClip spearSoundFx;
        public GameObject swordMissilePrefab;
        public GameObject swordRevolvePrefab;
        public AudioClip swordFlyOutSoundFx;
        public AudioClip swordFlyBackSoundFx;
        public GameObject swingSwordBulletPrefab;
        public GameObject jazzLeapLandingPrefab;
        public AudioClip shiftBackStartSoundFx;
        public AudioClip turnValorSoundFx;

        public GameObject fireZonePrefab;
        public GameObject poisonZonePrefab;
    }
    
    [Serializable]
    public class C27Skill3AttributeConfig {
        public float shiftCd;
        public float hitInvincibilityFrameDuration = 0.4f;
        public int landDamage = 8;

        [Header("普攻参数")]
        public int initPunchDamage = 5;
        public float initPunchSize = 1.5f;
        
        public int initBladeDamage = 4;
        public float initBladeSize = 2.5f;
        public float valorBladeSize = 3f;
        public int initSpearDamage = 4;
        public float initSpearSize = 2.5f;
        
        public int swordMissileDamage = 5;
        public int swordRevolveDamage = 5;
        
        public int swingSwordBulletDamage = 5;
        public int swingSwordBulletCreateTimesAfterMainSkill = 3;
        public int swingSwordBulletCreateTimesInValor = 3;
        public int swingSwordBulletNum = 5;
        
        [Header("主动技能参数")]
        public float initMainSkillCd = 2f;
        public int jazzLeapLandingDamage = 1;
        public float jazzLeapLandingRadius = 4f;
        public float jazzLeapLandingEffectDuration = 5;
        public float jazzLeapLandingDizzyDuration = 1f;

        [Header("被动技能(战勇)参数")]
        public int initValorValue = 20;
        public int initValorTurnOnValue = 50;
        public int initMaxValorValue = 100;

        public float turnValorHpProtectTime = 1f;
        
        public float createDamageValorConversionRate = 0.5f;
        public int killedEnemyValorAdditive = 5;
        public float getDamageValorConversionRate = 1f;
        
        public int valorStateValorValueReducePerSecond = 5;
        public float notCreateDamageTimeLimit = 2f;
        public int notCreateDamageValorValueReducePerSecond = 5;

        public int notValorGetDamageLimit = 5;
        public int valorStateDamageAdditive = 2;
        public float valorStateCdAdditive = 0.8f;
        public int initCritic = 5;
        public int valorCriticAdditive = 30;
        public float valorAnimatorSpeed = 1.2f;
        public float attackMoveSpeedAdditive = 1.2f;
        public float notAttackMoveSpeedAdditive = 0.8f;
    }
}