using RGScript.Character;
using RGScript.UI.ChoosePet;
using System;
using UnityEngine;
using System.Collections;
using UnityEngine.Events;
using Random = UnityEngine.Random;
using Sirenix.OdinInspector;
using RGScript.UI.MVC;
using Papa.Util;
using RGScript.Manager.Factory;
using System.Collections.Generic;
using RGScript.Character;
using System.Globalization;
using RGScript.Data;
using RGScript.Util.MathTool;

public class RGPetController : RGBaseController, OffensiveInterface, MasterInterface, IMinion, IProduct {
    public bool stayInAir { get; set; }
    public RoleAttributeProxy role_attribute => attribute;
    public Action OnFly2Master;

    //0 跟随 1 攻击状态
    public int state = 0;

    public int damage = 2;
    public float criticalFactor = 2; // 暴击倍数

    public int AtkDamage => Mathf.RoundToInt(damage * attribute.atk_damage_factor);

    [NonSerialized]
    public Vector2 move_direction = new Vector2(0, 0);

    [NonSerialized]
    public float min_distance = 100;

    //范围内最小单位距离
    protected float reply_time1 = 4f;
    protected float reply_time2 = 2f;
    protected float this_reply_time;

    public float scout_rate = 1f;
    public float ScoutRate => scout_rate / attribute.atk_speed;

    //思考间隔
    public float atk_cd = 2;

    public float AtkCD => atk_cd / attribute.atk_speed;

    public int atk_rate = 8;

    public int AtkRate => atk_rate;

    public bool summon;

    [NonSerialized]
    public bool can_atk = true;

    [NonSerialized]
    public Transform master_tf;
    public Transform masterTransform => master_tf;

    [HideInInspector][NonSerialized] public int specifiedIdx = -1;

    public float min_follow_distance = 2f;
    public float max_follow_distance = 20f;
    public UnityEvent<bool> OnAttackStart = new();
    public UnityEvent<bool> AttackStartEvent => OnAttackStart;

    public virtual bool SetCharacterZPos => true;

    public bool shouldFlyToMaster = true;
    protected Vector3 FacingLeftScale = new Vector3(-1, 1, 1);
    protected Vector3 FacingRightScale = new Vector3(1, 1, 1);

    protected readonly RaycastHit2D[] _linecastCache = new RaycastHit2D[16];

    public List<System.Action<RGPetController, Transform>> attackActionReplacement = new List<System.Action<RGPetController, Transform>>();

    [HideInInspector]public bool isEasterTempPet;
    
    public bool IsRun => anim!=null && anim.GetBool("run");
    protected Transform fixTarget;
    
    public virtual void SetRun(bool value) {
        anim.SetBool("run", value);
    }

    protected virtual void Awake() {
        InitBase();
        buffMgr.InitWithoutShowList(transform);
    }

    protected virtual UIHpBar CreatePetBar() {
        var bar = UIHpBar.CreatePetBar(role_attribute.Client);
        var ectrl = gameObject.GetComponent<RGEController>();
        if(ectrl != null && ectrl.hpBarOffset != default(Vector3) && bar.GetComponent<UIFollowWorldObject>() is var uiFollow && uiFollow != null){
            uiFollow.offset = ectrl.HpBarOffsetFactor * ectrl.hpBarOffset + ectrl.GetHpBarOffset();
        }
        return bar;
    }

    protected void SetPetScale(float scale) {
        transform.localScale = Vector3.one * scale;
    }

    protected int _facing = 1;
    public override int facing {
        get {
            if (transform_img != null) {
                _facing = Quaternion.Angle(RotLeft, transform_img.rotation) <= 0.1f ? -1 : 1;
            }
            return _facing;
        }
        set {
            if (keepFacing) {
                return;
            }

            if (transform_img != null) {
                transform_img.rotation = value == 1 ? RotRight : RotLeft;
            }
            _facing = value;
        }
    }

    protected virtual void StrengthenPetIfFactorValid() {
        if (!BattleData.data.CompareFactor(emBattleFactor.HugePet)) {
            return;
        }

        SetPetScale(2.0f);
        var isDruidPet = this is WolfOfDruidController || this is BearController;

        if (!isDruidPet) {
            atk_cd = 0.5f;
        }

        if (this is WolfController wolfController) {
            var uiFollowWorldObject = wolfController.hpBar.GetComponent<UIFollowWorldObject>();
            uiFollowWorldObject.offset.y *= 2;
        }

        if (this is PetWeaponController petWeaponController) {
            petWeaponController.hand.front_weapon.ChangeAtk(6, emChangeValueType.Add);
        } else {
            if (!isDruidPet) {
                damage *= 4;
            } else {
                damage = (int)(damage * 1.25f);
            }
        }

        if (this is Pet41Controller pet41Controller) {
            pet41Controller.extraDamage += 3;
        }
    }

    protected virtual void Start() {
        var battleData = GameUtil.GetBattleData(master_tf);
        if (battleData.HasBuff(emBuff.ExpertPet)) {
            reply_time1 = 2f;
            reply_time2 = 1f;
            damage += 3;
            role_attribute.MaxHpValue.AddAdditionValue(RoleAttributeValueEnum.BuffExpertPet, 10);
            if (this is Pet41Controller pet41Controller) {
                pet41Controller.extraDamage += 3;
            }
        }

        int pet_food_level = PlayerSaveData.Inst.furniture_pet_food_level;
        role_attribute.MaxHpValue.AddAdditionValue(
            RoleAttributeValueEnum.PetFood, (pet_food_level > 3 ? 3 : pet_food_level) * 3);
        damage += (pet_food_level - 3) > 0 ? (pet_food_level - 3) : 0;

        // Huge pet factor
        StrengthenPetIfFactorValid();

        role_attribute.hp = role_attribute.max_hp;
        if (!summon && (!GameUtil.IsMultiGame() || NetControllerManager.Inst.isServer || DataUtil.IsHeroRoom())) {
            if (!isEasterTempPet) {
                var idx = 0;
                if (GameUtil.InGameScene) {
                    idx = GetPetIndex();
                } else {
                    (emPartner partner, string idxOrName) = RGSaveManager.Inst.GetLastPartner();
                    if (partner == emPartner.Pet) {
                        idx = idxOrName.ToInt();
                    }
                }
                if (!DataUtil.GetPetUnlock((emPet)idx)) {
                    idx = 0;
                }

                if (string.IsNullOrEmpty(battleData.customPetSkin)) {
                    RuntimeAnimatorController animCtrl = null;
                    if (specifiedIdx >= 0) {
                        idx = specifiedIdx;
                    }

                    anim.runtimeAnimatorController = ResourcesUtil.LoadPetSkin(idx, 0);
                } else {
                    anim.runtimeAnimatorController = ResourcesUtil.Load(
                            "RGAnim/pet_anim/" + battleData.customPetSkin + ".controller") as
                        RuntimeAnimatorController;
                }

                if (specifiedIdx < 0) {
                    BroadcastMessage("OnSkinChanged", idx, SendMessageOptions.DontRequireReceiver);
                }
            }
        }

        AddDelegate();
        if ((GameUtil.InGameScene && (BattleData.data.gameMode == emGameMode.Normal || BattleData.data.gameMode == emGameMode.BossRush)) || DataUtil.IsTutorialRoom())
            StartCoroutine(CheckPositionValid());

        OnInit();
        
        if (awake)
            Scout();


        if (SetCharacterZPos) {
            gameObject.SetCharacterZPos();
        }
    }

    protected int GetPetIndex() {
        var roomPet = BattleData.data.extraData.GetDataByKey(RGGameConst.ExtraDataKey.ROOM_PET_ID);
        if (int.TryParse(roomPet, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out int pIndex)) {
            return pIndex;
        }

        return 0;
    } 

    protected virtual void OnInit(){
        SimpleEventManager.Raise(new PetStartEvent { petObject = gameObject, petController = this } );
    }

    public void ChangeSkin(emPet skin) {
        anim.runtimeAnimatorController = ResourcesUtil.LoadPetSkin((int)skin, 0);
        CancelInvoke();
        Scout();
        OnSkinChanged((int)skin);
    }

    public void ChangeSkinWithAnimatorController(string animatorCtrlName) {
        var animCtrl = ResourcesUtil.LoadPetAnimatorController(animatorCtrlName);
        anim.runtimeAnimatorController = animCtrl;
        CancelInvoke();
        Scout();
    }

    public override float Dizzy(float the_time, bool isFreeze) {
        var ret = base.Dizzy(the_time, isFreeze);
        move_direction = Vector2.zero;
        awake = false;
        if (null != rigibody) {
            rigibody.velocity = new Vector2(0, 0);
        }

        SetRun(false);
        return ret;
    }

    public override void EndDizzy() {
        base.EndDizzy();
    }
    
    [HideInInspector]public int petFixedIndex;
    
    protected override void FixedUpdate() {
        if (!awake) return;
        base.FixedUpdate();

        if (inertial_vel > 1f) {
            rigibody.velocity = force_direction.normalized * inertial_vel;
            inertial_vel *= Mathf.Min(1f, friction);
        } else {
            rigibody.velocity = move_direction.normalized * (role_attribute.speed * (1 + role_attribute.speed_rate) * extraSpeedFactor.GetFinalValue());
        }

        if (master_tf) {
            var tempPetFixedIndex = 0;
            if (ItemData.data.easterEggTempPets.Count > 0 && isEasterTempPet) {
                tempPetFixedIndex =  RGGameSceneManager.CurrentPetFixedIndex % ItemData.data.easterEggTempPets.Count;
            }
            
            var dist = Vector2.Distance(transform.position, master_tf.position);
            if (dist > max_follow_distance) {
                FlyToMaster();
                if (tempPetFixedIndex == petFixedIndex) {
                    if (HitWallEdgeIgnoreTrigger(transform.position, 0.4f)) {
                        // 当前位置在墙里面
                        LandFromAir();
                    }
                }
            } else if (dist < min_follow_distance && !has_target) {
                if (IsRun) {
                    StopMove();
                }
            }
        }

        ReplyingHP();
    }

    public virtual void ReplyingHP() {
        if (role_attribute.hp < role_attribute.max_hp) {
            this_reply_time += Time.deltaTime;
            if (this_reply_time >= reply_time2 + reply_time1) {
                role_attribute.hp += (role_attribute.max_hp / 5);
                this_reply_time = reply_time1;
                if (role_attribute.hp >= role_attribute.max_hp) {
                    Recovered();
                }

                role_attribute.HpChanged();
            }
        }
    }

    /// <summary>
    /// 完全恢复了
    /// </summary>
    protected virtual void Recovered() {
        role_attribute.hp = role_attribute.max_hp;
        GetComponent<Collider2D>().enabled = true;
        state = 1;
        if (anim.HasParam("dead"))
            anim.SetBool("dead", false);
    }

    public virtual void Reborn() {
        Recovered();
    }

    public virtual List<Transform> FindTargets(float range, int wallMask = 0) {
        List<Transform> tmpTargets = new List<Transform>();
        float minDistance = float.MaxValue;
        int hitCount = Physics2D.CircleCastNonAlloc(
            transform.position,
            range,
            new Vector2(0, 0),
            hitCache,
            0.0f,
            PhysicsUtil.MonsterMask);
        for (int i = 0; i < hitCount; i++) {
            var position = transform.position;
            var lockableObj = hitCache[i].transform.GetComponent<ILockableObject>();
            var hitPosition = hitCache[i].transform.position.Vec2();
            if (lockableObj != null) {
                hitPosition = lockableObj.GetColliderCenter();
            }
            float distance = Vector2.Distance(hitPosition, position);
            Vector2 end = position + Vector3.up * 0.5f;
            Vector2 begin = hitPosition;
            if (lockableObj == null || !lockableObj.Lockable() || distance >= minDistance ||
                Physics2D.LinecastNonAlloc(begin, end, _linecastCache, wallMask | PhysicsUtil.FallMask) > 0) {
                continue;
            }

            if (hitCache[i].transform.name == RGGameConst.stake_name) {
                if (!(distance < 8.5)) {
                    continue;
                }
            } else if (DataUtil.IsMultiRoom()) {
                if (hitCache[i].transform.CompareTag("Body_E")) {
                    if (!(distance < 7.5)) {
                        continue;
                    }
                }
            }

            // 宠物不攻击没有攻击性的敌人
            var enemyTag = hitCache[i].transform.GetComponent<RGEController>().enemyTag;
            if ((enemyTag & emEnemyTag.NonAtkPlayer) == emEnemyTag.NonAtkPlayer) {
                continue;
            }
            
            tmpTargets.Add(hitCache[i].transform);
        }

        return tmpTargets;
    }

    protected virtual Transform SearchTarget() {
        if (fixTarget && fixTarget != null) {
            return fixTarget;
        }
        
        return FindTarget(12, PhysicsUtil.AllObstacleMask | PhysicsUtil.FallMask);
    }
    
    public override Transform FindTarget(float range, int wallMask = 0) {
        Transform minObject = null;
        float minDistance = float.MaxValue;
        var tmpTargets = FindTargets(range, wallMask);
        
        // 优先查找优先攻击对象
        foreach (var tmpTarget in tmpTargets) {
            if (tmpTarget.GetComponent<RGEController>().HasTag(emEnemyTag.PriorityTarget)) {
                return tmpTarget;
            }
        }

        foreach (var tmpTarget in tmpTargets) {
            var distance = Vector3.Distance(tmpTarget.position, transform.position);
            if (minDistance > distance) {
                minDistance = distance;
                minObject = tmpTarget;
            }
        }

        return minObject;
    }

    public void SetFixTarget(Transform target) {
        if (target) {
            fixTarget = target;
            target_obj = target;
            has_target = true;
        } 
    }

    public void RemoveFixTarget() {
        fixTarget = null;
        has_target = false;
    }

    public virtual bool ShouldSearchTarget => state == 1 && !isPassive;

    public virtual void Scout() {
        has_target = false;
        target_obj = master_tf;
        if (ShouldSearchTarget) {
            var newTarget = SearchTarget();
            if (newTarget) {
                var oldTarget = target_obj;
                target_obj = newTarget;
                if(oldTarget != target_obj){
                    SimpleEventManager.Raise(new PetChangeTargetEvent{ petController = this });
                }
                has_target = true;
            }
        }

        // 兼容某些场景OnGameStateChange回调滞后一直跟随的情况
        if (GameUtil.InGameScene && role_attribute.hp >= role_attribute.max_hp) {
            state = 1;
        }

        FixedRotation();
        RunReflection();
    }

    protected virtual float atk_distance => 1.5f;
    protected RoleAttributeValue<float> replaceAtkDistance = null;

    public void SetAtkDistance(string key, float value){
        if(replaceAtkDistance == null){
            replaceAtkDistance = new RoleAttributeValue<float>(atk_distance, 0, 999);
        }
        replaceAtkDistance.SetFixedValue(key, value);
    }

    public void RemoveAtkDistanceValue(string key){
        if(replaceAtkDistance != null){
            replaceAtkDistance.RemoveValue(key);
        }
    }

    protected virtual void RunReflection() {
        if (has_target) {
            var isTargetValid = GetDistanceFromTarget(transform.position.Vec2() + Vector2.up * 0.5f, target_obj, ref _targetCollider, out var dist, out var closestPoint);
            if (!isTargetValid) {
                Invoke(nameof(EndCycle), ScoutRate);
                return;
            }

            if (dist > (replaceAtkDistance != null ? replaceAtkDistance.GetFinalValue() : atk_distance)) {
                if (Random.Range(0, 10) < 8) {
                    move_direction = (closestPoint - (Vector2)transform.position).normalized;
                    if (dist < 2 && has_target && Random.Range(0, 10) < 3) {
                        move_direction = -move_direction;
                    }

                    SetRun(true);
                } else {
                    move_direction = new Vector2(0, 0);
                }
            } else {
                DoActionWhenClosed();
            }
        } else {
            if (!target_obj || Vector2.Distance(transform.position, target_obj.position) < min_follow_distance) {
                SetRun(false);
                move_direction = new Vector2(0, 0);
            } else if (Random.Range(0, 10) < 8) {
                if (Physics2D.LinecastNonAlloc(transform.position, target_obj.transform.position, _linecastCache, PhysicsUtil.FallMask) > 0) {
                    SetRun(false);
                    move_direction = Vector2.zero;
                    
                } else {
                    move_direction = ((Vector2)target_obj.position - (Vector2)transform.position).normalized;
                    SetRun(true);    
                }
            }
        }

        Invoke(nameof(EndCycle), ScoutRate);
    }

    protected virtual void DoActionWhenClosed() {
        if (Random.Range(0, 10) < atk_rate) {
            // 角色死亡后宠物不再攻击敌人，避免白嫖
            if (can_atk && master_tf && !master_tf.GetComponent<RGController>().dead) {
                AtkProcess();
            }
        }
    }

    protected virtual void AtkProcess() {
        anim.SetTrigger("atk");
        can_atk = false;
        Invoke("TurnToCanAtk", AtkCD);
        DoAtkProcess(target_obj);
    }

    protected virtual void DoAtkProcess(Transform theTarget) {
        if (theTarget == null) {
            return;
        }
        
        SimpleEventManager.Raise(new PetAttackEvent{ petController = this });
        if(attackActionReplacement != null && attackActionReplacement.Count > 0){
            RaiseAttackEvent(false);
            for(var i = attackActionReplacement.Count - 1; i >= 0; --i){
                attackActionReplacement[i].Invoke(this, theTarget);
            }
            attackActionReplacement.Clear();
        }
        else{
            RGEController targetCtrl = theTarget.GetComponent<RGEController>();
            if (targetCtrl == null) {
                return;
            }
            targetCtrl.GetForce(rigibody.velocity, RGGameProcess.ProcessRepelForce(damage * 3));
            
            var isCritical = rg_random.Range(0, 100) <= attribute.critical;
            SimpleEventManager.Raise(PlayerBulletPreHitEnemyEvent.UseCache(targetCtrl, null, null, gameObject, null, AtkDamage, isCritical));
            var finalDmg = (int)(PlayerBulletPreHitEnemyEvent.finalDamage * (isCritical ? criticalFactor : 1));
            isCritical = PlayerBulletPreHitEnemyEvent.finalCritical;
            DamageCarrierHelper.ProcessEnemyGetHurtDamage(targetCtrl, new HurtInfo{
                Source = gameObject,
                FingerPrint = DamageCarrier.DefaultFingerPrint,
                Damage = finalDmg,
                DamageType = DamageType.Melee,
                Critic = isCritical, 
            });
            Instantiate(
                ResourcesUtil.Load("RGPrefab/Effect/pet_atk_effect.prefab"),
                theTarget.position,
                Quaternion.identity);
            RaiseAttackEvent(isCritical);
        }
    }

    protected void RaiseAttackEvent(bool isCritical) {
        OnAttackStart?.Invoke(isCritical);
    }

    public virtual void EndCycle() {
        CancelInvoke("EndCycle");
        move_direction = Vector2.zero;
        SetRun(false);
        Scout();
    }

    protected virtual void FixedRotation() {
        if (!target_obj) {
            return;
        }

        var targetPosition = (Vector2)target_obj.position;
        Vector2 vectorToTarget = targetPosition - (Vector2)transform.position;
        FixedAngle = Vector2.Angle(vectorToTarget, new Vector2(facing, 0)) * Mathf.Sign(vectorToTarget.y);
        if (vectorToTarget.x * facing > 0) {
            // Same direction
            return;
        }

        facing *= -1;
        //transform.localScale = facing > 0 ? FacingRightScale : FacingLeftScale;
    }

    public void SetFacing(int f){
        facing = f;
        //transform.localScale = facing > 0 ? FacingRightScale : FacingLeftScale;
    }

    public virtual void TurnToCanAtk() {
        can_atk = true;
    }

    [Button(ButtonSizes.Medium)]
    public override void GetHurt(HurtInfo hurtInfo) {
        if (state == 0 || attribute.isProtectedFromDamage) return;

        var damage = hurtInfo.Damage;
        UICanvas.GetInstance().ShowTextHurt(transform, hurtInfo, 2, gameObject.name);
        this_reply_time = 0;
        role_attribute.hp -= damage;
        if (bodyRenderer != null) {
            bodyRenderer.material.EnableKeyword("HIT_ON"); 
        }
        Invoke("HitBack", 0.05f);
        //anim.SetTrigger ("hit");
        if (role_attribute.hp <= 1) {
            role_attribute.hp = 1;
            Escape();
        } else {
            StartHitTrigger(0.5f);
        }

        role_attribute.HpChanged();
    }

    private Timer _hitTriggerTimer;
    //隐藏碰撞器
    public virtual void StartHitTrigger(float time) {
        _hitTriggerTimer?.Cancel();
        transform.GetComponent<Collider2D>().enabled = false;
        _hitTriggerTimer = Timer.Register(time,false,false,EndHitTrigger);
    }

    //结束隐藏碰撞器
    public virtual void EndHitTrigger() {
        transform.GetComponent<Collider2D>().enabled = true;
    }

    public virtual void Escape() {
        state = 0;
        gameObject.GetComponent<Collider2D>().enabled = false;
        anim.SetBool("dead", true);
    }

    /// <summary>
    /// 添加代理，一般start函数调用
    /// </summary>
    public virtual void AddDelegate() {
        if (null != RGGameSceneManager.GetInstance()) {
            RGGameSceneManager.GetInstance().GSDelegate += OnGameStateChange;
        }
    }

    // 移除代理，需要销毁物体前调用
    public virtual void RemoveDelegate() {
        if (null != RGGameSceneManager.GetInstance()) {
            RGGameSceneManager.GetInstance().GSDelegate -= OnGameStateChange;
        }
    }

    // 代理所调用的事件
    public virtual void OnGameStateChange(int game_state) {
        if (game_state == 1) {
            state = 1;
        } else if (game_state == 2) {
            state = 0;
        }
    }

    public void TurnTo(Vector2 normal) {
        move_direction = Vector2.Reflect(move_direction, normal);
    }

    public void HitBack() {
        //坑，如果这里bodyRenderer是null，在墓穴关卡掉落的时候，会导致dotweenCallback走一半，并且不会报错,子类写的时候记得给bodyRenderer赋值
        if(bodyRenderer != null && bodyRenderer.material != null){
            bodyRenderer.material.DisableKeyword("HIT_ON");
        }
    }

    public void GetGray() {
        if(bodyRenderer != null && bodyRenderer.material != null){
            bodyRenderer.material.EnableKeyword("GRAY_ON");
        }
    }

    public void GrayBack() {
        if(bodyRenderer != null && bodyRenderer.material != null){
            bodyRenderer.material.DisableKeyword("GRAY_ON");
        }
    }

    [Title("不受独行侠因子的影响")]
    public bool ignoreAllAlone = false;

    public virtual void SetMaster(Transform masterTransform) {
        if (!ignoreAllAlone && BattleData.data.CompareFactor(emBattleFactor.AllAlone)) {
            return;
        }

        master_tf = masterTransform;
        if (masterTransform) {
            var masterInterface = master_tf.GetComponent<MasterInterface>();
            if (null != masterInterface) {
                masterInterface.AddDestroyCb(this.OnMasterDestory);
            }
        }

        if (master_tf.GetComponent<RGController>() is { } c) {
            damage += c.attribute.PetPower.IntValue;
            attribute.MaxHpValue.RemoveValue("PetPower");
            attribute.MaxHpValue.AddAdditionValue("PetPower", c.attribute.PetPower.IntValue);
            attribute.hp += c.attribute.PetPower.IntValue;

            //成就检测
            CheckAchievement(c);
        }
    }

    void CheckAchievement(RGController controller) {
        var localController = RGGameSceneManager.Inst.controller;
        if (localController == null || localController.transform != master_tf) {
            return;
        }

        BattleData.data.petMap.Add(this.GetInstanceID());
        int petCount = BattleData.data.petMap.Count;
        if (petCount >= 6) {
            AchieveInfos.info.CheckUnlock(AchieveInfos.AchievementType.StrengthInNumbers, 0, "", 0);
        }
    }

    public virtual void OnSkinChanged(int index) {
        if (index == (int)emPet.TapTap) {
            var name = "pet_tap_handler";
            if (!transform.Find(name)) {
                var go = Instantiate<GameObject>(ResourcesUtil.Load<GameObject>(
                    "RGPrefab/Pet/pet_tap_handler.prefab"));
                go.transform.parent = transform;
                go.gameObject.name = name;
                go.transform.localPosition = Vector3.zero;
            }
        } else if (index == (int)emPet.Serenade) {
            // 夜歌
            if (DataUtil.IsHeroRoom() && RGGameSceneManager.Inst.controller == null) {
                // 大厅 未选择角色
                anim.SetBool("exhibit", true);
            }

            // 夜歌会飞
            stayInAir = true;
        } else if (index == (int)emPet.Seal) {
            anim.runtimeAnimatorController = ResourcesUtil.LoadPetSkin(index, BattleData.data.petSealSkinIndex);
        } else if (index == (int)emPet.Cat6) {
            if (DataUtil.IsHeroRoom() && RGGameSceneManager.Inst.controller == null) {
                // 大厅 未选择角色
                anim.runtimeAnimatorController = CommonAssets.Assets.pet35Animator;
            }
        }
        
        if (index == (int)emPet.Bug) {
            StartCoroutine("PetBugHandler");
        } else {
            StopCoroutine("PetBugHandler");
        }
    }

    IEnumerator PetBugHandler() {
        var wait = new WaitForSeconds(0.1f);
        float errDuration = 0;
        while (true) {
            if (errDuration <= 0) {
                if (Random.Range(0, 1000) < 10) {
                    errDuration = Mathf.Min(Random.Range(0.25f, 2.5f), Random.Range(0.25f, 2.5f));
                }
            } else {
                errDuration -= 0.1f;
            }

            if (anim.GetBool("err") != (errDuration > 0)) {
                anim.SetBool("err", errDuration > 0);
            }

            yield return wait;
        }
    }

    public virtual void SetSourceObject(GameObject value) {
        master_tf = value ? value.transform : null;
        if (master_tf.GetComponent<RGController>()) {
            attribute.camp = master_tf.GetComponent<RGController>().camp;
        }
    }

    public GameObject GetSourceObject() {
        if (null == master_tf) {
            return null;
        }

        return master_tf.gameObject;
    }

    /// <summary>
    /// 主人销毁回调
    /// </summary>
    public virtual void OnMasterDestory() {
        MasterDestroy();
        if (null != this) {
            Destroy(gameObject);
        }
    }
    
    public ulong InstanceID { get; set; }
    
    public void DestroyProduct() {
        var petFactory = FactoryManager.Instance.GetFactory<PetFactory>();
        petFactory.Destroy(InstanceID);
    }

    public event System.Action onDestroyCb;

    protected virtual void OnDestroy() {
        MasterDestroy();
        DestroyProduct();
        _hitTriggerTimer?.Cancel();
        BattleData.data.petMap.Remove(this.GetInstanceID());
    }

    public void AddDestroyCb(Action act) {
        onDestroyCb += act;
    }

    public void MasterDestroy() {
        if (null != onDestroyCb) {
            onDestroyCb();
        }
    }

    public void DestroyCallback() {
        onDestroyCb?.Invoke();
    }

    public void StopMove() {
        SetRun(false);
        move_direction = Vector2.zero;
        rigibody.velocity = Vector2.zero;
        inertial_vel = 0f;
    }


    RaycastHit2D[] checkPosValidHit = new RaycastHit2D[4];

    protected IEnumerator CheckPositionValid() {
        var wait = new WaitForSeconds(2);
        // 判断是否在可行走区域之外
        while (!dead) {
            yield return wait;
            if (master_tf && GameUtil.InGameScene) {
                if (RGGameProcess.Inst.modeProcess is NormalGameModeProcess) { // 仅在普通模式及其衍生模式检测房间碰撞体
                    int checkPosValidLayer = LayerMask.GetMask("Default");
                    var hitCount = Physics2D.CircleCastNonAlloc(transform.position.Vec2(), 0.01f, Vector2.zero, checkPosValidHit, 0, checkPosValidLayer);
                    bool inRoom = false;
                    for (var i = 0; i < hitCount; ++i) {
                        if (checkPosValidHit[i].collider.gameObject.CompareTag("Room")) {
                            inRoom = true;
                            break;
                        }
                    }

                    if (!inRoom) {
                        FlyToMaster();
                    }
                }
                else {
                    if (Vector2.Distance(master_tf.position, transform.position) > 40) { // 40 通常已经超出视野范围很多
                        FlyToMaster();
                    }
                }
            }
        }
    }

    protected virtual void FlyToMaster() {
        if (shouldFlyToMaster) {
            transform.position = master_tf.position;
            OnFly2Master?.Invoke();
        }
    }

    public virtual void OnTapDown() {
        if (gameObject.name == "pet_" + emPet.TapTap) {
            var choosePetController = UICanvas.GetInstance().GetController<ChoosePetController>();
            if (!choosePetController.IsShowing) return;

            // 宠物选择界面点击TapTap，增加亲密度
            choosePetController.DispatchMessage(new Message {
                Command = ChoosePetCommand.ClickTapTapRandomFeed,
            });
        }
    }

    public virtual void OnTapUp() { }

    public override UnitType GetUnitType() {
        return UnitType.Character;
    }

    public bool IsMasterDead() {
        if (master_tf != null) {
            var controller = master_tf.GetComponent<RGController>();
            if (controller != null) {
                return controller.dead;
            }
        }

        return false;
    }
}