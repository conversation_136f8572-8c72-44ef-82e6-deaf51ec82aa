using System;
using System.Collections;
using System.Collections.Generic;
using Com.LuisPedroFonseca.ProCamera2D;
using DG.Tweening;
using ModeSeason.ComboGun;
using RGScript.Character;
using RGScript.Data;
using Sirenix.OdinInspector;
using Sirenix.Utilities;
using System.Linq;
using RGScript.Manager.Factory;
using RGScript.Net.StateSync.Core;
using RGScript.Util;
using UnityEngine;
using UnityEngine.Events;
using Random = UnityEngine.Random;

/// <summary>
/// 坐骑
/// </summary>
[RequireComponent(typeof(RGNetBehaviour))]
public class RGMountController :
    StateSyncObject, ItemInterface, RGNetTransformInterface, IConsumeRecord, IProduct {
    public string unlock_key; //用于判断是否解锁的key
    public int itemLevel;
    public int itemValue;
    public int defence;
    public bool canTriggerItem = true;
    public bool canUseSkill;
    public GameObject deadObject;
    public AudioClip mountClip;
    public AudioClip deadClip;
    public AudioClip hitClip;
    internal Animator anim;

    // 可骑乘
    protected bool canMount = true;
    
    // 是否技能形态
    public bool isSkillForm { get; set; }

    public string mechFxResName;
    
    [ShowInInspector]
    internal RoleAttributeProxy attribute { get; private set; }
    
    internal RGController driver;
    
    [NonSerialized]
    public Transform driverTransform;
    
    public float speedRate;
    
    [HideInInspector]
    public DrivePos drivePosition;

    private Vector3 initDrivePos;
    
    protected Collider2D col;
    Transform shadowLock;
    
    public bool dead{ get; protected set; }

    public void SetDead(bool isDead) {
        this.dead = isDead;
    }

    [HideInInspector] public bool awake;

    public MultiHand[] hands;
    public MountHp HpBar => hpBar;
    protected MountHp hpBar;
    public MultiHand hand { get; protected set; }
    int handIndex;
    bool justMount;

    private List<SpriteRenderer> CachedWeaponSpr { get; set; }

    public bool IsMech {
        get {
            return IsMechName(gameObject.name);
        }
    }
    public static bool IsMechName(string mountName) {
        return mountName.Contains("mech");
    }

    internal bool unlocked { //是否已经解锁
        get {
            if (itemLevel == 6) {
                return DataMgr.CGMountData.IsUnlocked(unlock_key);
            }
            return ItemData.data.GetBluePrintStatus(unlock_key) == emBluePrintStatus.Researched;
        }
    }

    /// <summary>
    /// 坐骑产生位移的事件
    /// </summary>
    public event Action<MovementInput> onMountMove;
    public event Action<MovementInput> onMountAim;
    
    protected virtual float justMountDelay => 0.1f;

    //是否在建造中
    internal bool inBuild;
    //是否隐藏不使用的武器
    public bool hideInactiveWeapon;
    public bool offsetRiderCollider;
    
    [LabelText("是否使用角色的武器")]
    public bool useControllerWeapon;
    //控制偏移,渲染层级以及雕像显示
    [LabelText("是否覆盖角色")]
    public bool coverController;
    [LabelText("是否取代角色移动动画")]
    public bool runAnim;
    [LabelText("是否有idle动画")]
    public bool idleAnim;
    [LabelText("是否为生物")]
    public bool isCreature;
    //是否有血量. 无血量则不会产生抵挡作用
    [LabelText("拥有生命值")]
    public bool hasHp = true;
    [LabelText("跟随到下一关")]
    public bool keep = true;
    [LabelText("是否隐藏角色")]
    public bool hideCharacter;
    [LabelText("攻击动画是否和机甲本身相关")]
    public bool attackAnimationOnBody;
    
    //是否可以脱下.通常技能不可脱下
    internal bool canTakeoff = true;
    public bool isLockBulletAngle;
    public Action onDead;
    public event Action<GameObject> OnDeadExplode;
    // 是否有ui特效
    public bool hasFx = true;
    public int SortingOrderOffset;
    public bool playDeadClip = true;
    
    [NonSerialized]
    public bool canUpdateDriver = true;
    [NonSerialized]
    public int ignoreForce;
    
    private bool _moving;
    private Renderer[] _subRenderers;
    private bool _hasLandState;
    private static readonly int AnimationLand = Animator.StringToHash("land");
    private static readonly int AnimationIsLand = Animator.StringToHash("is_land");
    private static readonly int AnimationRun = Animator.StringToHash("run");
    private static readonly int AnimationAttack = Animator.StringToHash("attack");
    private static readonly int AnimationDead = Animator.StringToHash("dead");
    private static readonly int BDead = Animator.StringToHash("b_dead");

    public bool IsInvulnerable { get; protected set; }
    public bool NeedRecord { get; set; }
    public virtual bool CanKeepToNextLevel => canTakeoff && keep;

    private MountZPos _zPos;
    public MountZPos ZPos{
        get{
            // ReSharper disable once InvertIf
            if (!_zPos) {
                _zPos = gameObject.GetComponent<MountZPos>();
                if (_zPos && drivePosition) {
                    _zPos.SetHeightWithDriverPos(drivePosition.transform.localPosition.y);
                }
            }
            return _zPos;
        }
    }

    private Material _originMat;
    public void SetBodyMaterial(Material mat) {
        var img = transform.Find("img");
        if (!img) {
            return;
        }

        var body = img.Find("body").GetComponent<SpriteRenderer>();
        if (!body) {
            return;
        }

        if (!_originMat) {
            _originMat = body.material;
        }
        body.material = mat;
    }

    public void ResetBodyMaterial() {
        var img = transform.Find("img");
        if (!img) {
            return;
        }

        var body = img.Find("body").GetComponent<SpriteRenderer>();
        if (!body) {
            return;
        }

        if (_originMat) {
            body.material = _originMat;
        }
    }
    
    public ulong InstanceID { get; set; }

    IMountSpecialAttack _specialAttack;
    protected virtual IMountSpecialAttack MountSpecialAttack {
        get { return _specialAttack ??= GetComponent<IMountSpecialAttack>(); }
    }

    public int landRestoreShield;
    private bool _landSkillReady = true;
    private const float LandSkillCooldown = 5;
    private float _landSkillTimer;
    private Transform _imgTransform;
    private Vector3 _originalScale = Vector3.one;
    
    protected override void Awake() {
        base.Awake();
        
        attribute = new RoleAttributeProxy(GetComponent<RoleAttributePlayer>());
        drivePosition = transform.Find("img/drive_container/drive_position").GetComponent<DrivePos>();
        initDrivePos = drivePosition.transform.position - transform.position;
        col = transform.Find("collider").GetComponent<Collider2D>();
        anim = GetComponent<Animator>();
        if (hasHp) {
            hpBar = transform.Find("mount_hp").GetComponent<MountHp>();
        }

        shadowLock = transform.Find("shadow_lock");
        if (hands.Length > 0) {
            hand = hands[0];
        }

        //屌炸天模式机甲+1防御
        if (BattleData.data.isBadass && !isCreature) {
            defence += 1;
        }

        RefreshWeaponOutlook();
        if (BattleData.data.HasBuff(emBuff.ExpertPet)) {
            if (this is MountBearController) {
                attribute.MaxHpValue.AddAdditionValue(RoleAttributeValueEnum.BuffExpertPet, 10);
            } else {
                attribute.MaxHpValue.AddAdditionValue(RoleAttributeValueEnum.BuffExpertPet, attribute.max_hp / 2);    
            }
            
            attribute.hp = attribute.max_hp;
        }

        if (MountSpecialAttack is { ShouldHideWeapon: true }) {
            MountSpecialAttack.OnAttackStart += OnNormalWeaponHide;
            MountSpecialAttack.OnAttackEnd += OnNormalWeaponShow;
        }

        _hasLandState = anim.HasState(0, AnimationLand);
        if (_hasLandState) {
            anim.SetBool(AnimationIsLand, true);
        }
    }

    protected virtual void OnEnable() {
        CachedWeaponSpr = transform.GetComponentsInChildren<SpriteRenderer>().ToList();
    }

    public void OnReady() { }

    private void OnNormalWeaponShow() {
        foreach (var h in hands) {
            h.SetSpriteRenderer(!h.autoUse && hand == h);
        }
    }

    private void OnNormalWeaponHide() {
        foreach (var h in hands) {
            h.SetSpriteRenderer(h.autoUse);
        }
    }
    
    protected virtual void Start() {
        _imgTransform = transform.Find("img");
        if (_imgTransform) {
            _originalScale = _imgTransform.localScale;   
        }
    }

    protected virtual void Update() {
        if (!_landSkillReady) {
            _landSkillTimer += Time.deltaTime;
            if (_landSkillTimer >= LandSkillCooldown) {
                _landSkillReady = true;
                _landSkillTimer = 0;
            }
        }
        
        if (driver) {
            if (MountSpecialAttack is { IsSpecialAttacking: true }) {
                foreach (var multiHand in hands) {
                    if (multiHand.autoUse) {
                        multiHand.SetAngle(driver.FixedAngle);
                    }
                }
            } else if (hand) {
                hand.SetAngle(driver.FixedAngle);
            }
        }

        var moving = IsMoving();
        if (_moving == moving) {
            return;
        }

        if (moving) {
            startMoving?.Invoke();
        } else {
            stopMoving.Invoke();   
        }
        _moving = moving;
    }
    
    public void RestoreHealth(int health, bool showText = false) {
        var oldHp = attribute.hp;
        attribute.hp = Mathf.Min(attribute.hp + health, attribute.max_hp);
        if (hasHp) {
            hpBar.UpdateHpBar(attribute.hp, attribute.max_hp);
        }

        if (showText && health > 0 && attribute.hp - oldHp > 0) {
            UICanvas.GetInstance().ShowTextMountHp(
                transform.position, attribute.hp - oldHp, 2, gameObject.name, true);
        }
    }

    public void AddMaxHp(int value, string key) {
        attribute.MaxHpValue.AddAdditionValue(key, value);
    }

    public void RemoveMaxHp(string key) {
        attribute.MaxHpValue.RemoveValue(key);
    }

    public void SetMaxHp(int value) {
        attribute.MaxHpValue.SetOriginalValue(value);
    }

    public void SetHp(int value) {
        attribute.hp = Mathf.Min(value, attribute.max_hp);
        if (hasHp) {
            hpBar.UpdateHpBar(attribute.hp, attribute.max_hp);
        }
    }

    protected virtual void LateUpdate() {
        if (driver) {
            UpdateDriverPosition();
        }
    }

    protected virtual void UpdateDriverPosition() {
        if (!canUpdateDriver) {
            return;
        }
        
        var driverTrans = driver.transform;
        var driverPosition = driverTrans.position;
        var offset = driverPosition - drivePosition.transform.position;
        offset.z = 0;
        if (offsetRiderCollider) {
            driverPosition -= offset;
            driverTrans.position = driverPosition;
        }
        
        transform.position += offset;
        driver.SetCameraFocusOffset(transform.TransformPoint(initDrivePos) - driverPosition);
    }

    private void RestoreHealthSmooth(int health, float duration, float delay) {
        var sequence = DOTween.Sequence();
        sequence.AppendInterval(delay);
        var interval = duration / health;
        for (int i = 0; i < health; i++) {
            sequence.AppendCallback(() => {
                RestoreHealth(1, true);
            });
            sequence.AppendInterval(interval);
        }
    }

    /// <summary>
    /// 初始化. 只在伴随角色创建时调用
    /// </summary>
    /// <param name="controller"></param>
    public virtual void SetUp(RGController controller) {
        if (!isCreature && controller.GetHeroType() == emHero.Engineer) { //工程师每层恢复1点装甲值
            RestoreHealthSmooth(attribute.max_hp, 1.5f, 1);
        } else {
            RestoreHealthSmooth(attribute.max_hp / 2, 1.5f, 1);
        }

        this.driver = controller;
        driverTransform = controller.transform;
        controller.mount = this;
        controller.isLockBulletAngle = isLockBulletAngle;

        if(controller.ZPos && ZPos){ // 设置骑乘高度
            controller.ZPos.height += ZPos.mountHeight;
        }
        
        awake = true;
        ResetCommon(true);
        transform.SetParent(GetParent(controller));
        if (drivePosition) {
            drivePosition.ResetPosition(
                controller.GetHeroType(), controller.GetSkinIndex(), transform.Find("img").localScale);
        }
        
        UpdateDriverPosition();
        transform.localEulerAngles = Vector3.zero;
        if (!isCreature && controller && controller.IsLocalPlayer() && hasFx) {
            UIMech.LoadFx(name, controller, mechFxResName);
        }

        if (controller.IsLocalPlayer()) {
            UpdateSpecialButton(driver);
        }

        if (null != CachedWeaponSpr && null != controller) {
            controller.AddExclusiveChangeColorRenderers(CachedWeaponSpr);
        }

        ResetWeapons(true);
        
        if (_hasLandState) {
            anim.SetBool(AnimationIsLand, false);
        }

        UpdateSortingLayer(true);
        UpdateScaleAccordingToDriver();

        //乘坐事件
        SimpleEventManager.Raise(new MountEvent {
            mount_name = transform.name,
            MountController = this,
            mount = true,
            isSetup = true
        });
    }
    
    /// <summary>
    /// 结算页面骑乘展示用
    /// </summary>
    /// <param name="controller"></param>
    public virtual void SetUpForDisplay(RGController controller) {
        this.driver = controller;
        driverTransform = controller.transform;
        controller.mount = this;
        controller.isLockBulletAngle = isLockBulletAngle;

        if (controller.ZPos && ZPos) { // 设置骑乘高度
            controller.ZPos.height += ZPos.mountHeight;
        }
        
        awake = true;
        anim.enabled = true;
        transform.Find("shadow").gameObject.SetActive(false);
        transform.SetParent(GetParent(controller));
        if (drivePosition) {
            drivePosition.ResetPosition(
                controller.GetHeroType(), controller.GetSkinIndex(), transform.Find("img").localScale);
        }
        
        UpdateDriverPosition();
        transform.localEulerAngles = Vector3.zero;
        
        if (_hasLandState) {
            anim.SetBool(AnimationIsLand, false);
        }

        UpdateSortingLayer(true);
    }

    void UnJustMount() {
        justMount = false;
    }

    protected virtual void UpdateSpecialButton(RGController controller) {
        if (!(controller != null && controller.IsLocalPlayer())) {
            return;
        }
        if (MountSpecialAttack != null) {
            UICanvas.GetInstance().ShowBtnSpecial(0);
        } else if (controller != null && controller.hand != null && controller.hand.front_weapon != null) {
            controller.hand.front_weapon.FlushIcon();
        }
    }

    protected virtual void DoMount(RGController controller) {
        UpdateSortingLayer(true);

        canUpdateDriver = true;
        if (null != CachedWeaponSpr && null != controller) {
            controller.AddExclusiveChangeColorRenderers(CachedWeaponSpr);
        }
        if (!dead) {
            if (controller.IsLocalPlayer()) {
                UpdateSpecialButton(driver);
            }
            
            this.driver = controller;
            driverTransform = controller.transform;
            awake = true;
            controller.mount = this;
            controller.isLockBulletAngle = isLockBulletAngle;

            if(controller.IsLocalPlayer() && mountClip != null){
                RGMusicManager.Inst.PlayEffect(mountClip);
            }

            if(controller.ZPos && ZPos){ // 设置骑乘高度
                controller.ZPos.height += ZPos.mountHeight;
            }
            
            ResetCommon(true);
            justMount = true;
            Invoke(nameof(UnJustMount), justMountDelay);
            GetComponent<Collider2D>().enabled = false;
            ChangeParentWhenMount(controller);
            if (drivePosition) {
                drivePosition.ResetPosition(
                    controller.GetHeroType(), controller.GetSkinIndex(), transform.Find("img").localScale);
                var localPosition =
                    controller.facing >= 0 ? -drivePosition.transform.localPosition : Vector3.zero;
                controller.camera_focus.localPosition = localPosition;
            }
            transform.localEulerAngles = Vector3.zero;

            UpdateDriverPosition();
            
            if (runAnim) {
                controller.anim.SetBool(AnimationRun, false);
            }

            if (hideCharacter) {
                controller.BodyRenderer.enabled = false;
            }
            
            ResetWeapons(true);
            controller.mount = this;
            if (!isCreature && controller && controller.IsLocalPlayer() && hasFx) {
                UIMech.LoadFx(name, controller, mechFxResName);
            }

            //乘坐事件
            SimpleEventManager.Raise(new MountEvent {
                mount_name = transform.name,
                MountController = this,
                mount = true
            });
            controller.OnAfterMount();
            
            if (_hasLandState) {
                anim.SetBool(AnimationIsLand, false);
            }
        }
        UpdateScaleAccordingToDriver();
    }

    public void Mount(RGController controller) {
        var controllersMount = controller.mount;
        if (controllersMount) {
            if (controllersMount.canTakeoff && controllersMount != this) {
                controllersMount.Land();
            } else {
                return;
            }
        }

        if (IsCloseStrategy) {
            this.ChangeState(StateEnum.Mount, controller.p_index, true.ToString());
        } else {
            this.ChangeState(StateEnum.Mount, controller.p_index);
        }
    }

    protected virtual void ChangeParentWhenMount(RGController controller) {
        transform.SetParent(GetParent(controller));
    }

    void RefreshScale(float scale) {
        if (_imgTransform) {
            _imgTransform.localScale = _originalScale * scale;   
        }
    }

    /// <summary>
    /// 根据驾驶者大小缩放
    /// </summary>
    void UpdateScaleAccordingToDriver() {
        if (!driver) {
            return;
        }
        RefreshScale(driver.Scale);
        driver.chageScaleEvent += RefreshScale;
    }

    void ResetScale() {
        if (_imgTransform) {
            _imgTransform.localScale = _originalScale;   
        }

        if (driver) {
            driver.chageScaleEvent -= RefreshScale;   
        }
    }

    public void ShowUp() {
        if (itemLevel == 6) {
            return;
        }

        foreach (var spriteRenderer in GetComponentsInChildren<SpriteRenderer>()) {
            spriteRenderer.color = new Color(1, 1, 1, 0);
            spriteRenderer.DOFade(1, 0.75f);
        }
    }

    protected virtual void DoLand() {
        if (isSpecialDown) {
            SpecialClick(false);
        }

        SimpleEventManager.Raise(new MountEvent {
            mount_name = transform.name,
            MountController = this,
            mount = false
        });

        UpdateSortingLayer(false);

        ResetCommon(false);
        if (!dead) {
            GetComponent<Collider2D>().enabled = !dead;
            if (_hasLandState) {
                anim.SetBool(AnimationIsLand, true);
            }
        }
            
        if (driver) {
            driver.SetCameraFocusOffset(Vector2.zero);
            driver.OnMountOff();

            // 回退骑乘高度
            if(driver.ZPos && ZPos){
                driver.ZPos.height -= ZPos.mountHeight;
            }

            ResetScale();
            transform.SetParent(RGGameSceneManager.GetInstance().temp_objects_parent, true);
            if (offsetRiderCollider) {
                driver.transform.position += Vector3.down * 0.5f;
            }

            var isLocalPlayer = driver.IsLocalPlayer();
                
            driver.mount = null;
            driver.isLockBulletAngle = false;
                
            ResetWeapons(false);
            driver.Land();
            driver.camera_focus.localPosition = Vector3.zero;
            driver.AddExclusiveChangeColorRenderers(CachedWeaponSpr);
            driver = null;
            driverTransform = null;
            awake = false;
            if (!isCreature && isLocalPlayer && hasFx) {
                UIMech.unloadFx();
            }
        }

        transform.SetParent(RGGameSceneManager.GetInstance().temp_objects_parent, true);

        if (!_landSkillReady) {
            return;
        }

        _landSkillReady = false;
        if (!driver) {
            return;
        }

        if (landRestoreShield > 0) {
            driver.attribute.RestoreArmor(landRestoreShield, true);
        }
    }

    public void Land() {
        if (!canTakeoff) {
            return;
        }

        if (!driver) {
            return;
        }

        this.ChangeState(StateEnum.Dismount, driver.p_index);
    }

    void ResetCommon(bool isMount) {
        if (null == driver) {
            return;
        }
        driver.OnMount(isMount);
        
        // 本地玩家调整摄像机
        if (driver.IsLocalPlayer()) {
            if (isMount) {
                foreach (var cameraTarget in ProCamera2D.Instance.CameraTargets) {
                    if (cameraTarget.TargetTransform != driver.transform) {
                        continue;
                    }

                    ProCamera2D.Instance.RemoveAllCameraTargets();
                    ProCamera2D.Instance.AddCameraTarget(
                        transform, 1, 1, 0, new Vector2(0, 1.0f));
                    break;
                }
            } else {
                // ReSharper disable once ForeachCanBeConvertedToQueryUsingAnotherGetEnumerator
                foreach (var cameraTarget in ProCamera2D.Instance.CameraTargets) {
                    if (cameraTarget.TargetTransform != transform) {
                        continue;
                    }

                    ProCamera2D.Instance.RemoveAllCameraTargets();
                    ProCamera2D.Instance.AddCameraTarget(
                        driver.transform, 1, 1, 0, new Vector2(0, 1.0f));
                    break;
                }
            }
        }
        
        if (offsetRiderCollider) {
            OnSetBodyColliderOffset(isMount);
            if (isMount) {
                driver.AddTriggerColliderOffset(new Vector2(0, -1), $"mount_{GetInstanceID()}");  
            } else {
                driver.RemoveTriggerColliderOffset($"mount_{GetInstanceID()}");
            }
        }

        // 调整角色阴影、碰撞器
        if (!coverController) {
            driver.shadow_lock.enabled = !isMount;
            driver.transform.Find("shadow").GetComponent<SpriteRenderer>().enabled = !isMount;
            
            // 没有巨大化挑战时才缩放. 防止碰撞体过大
            if (!BattleData.data.CompareFactor(emBattleFactor.Huge)) {
                driver.transform.GetComponent<BoxCollider2D>().size *= isMount ? 1.6f : 0.625f;
            }
        }

        if (shadowLock) {
            shadowLock.gameObject.SetActive(isMount);
        }

        foreach (var buffStatue in driver.GetComponentsInChildren<BuffStatue>(true)) {
            buffStatue.OnMount(isMount);
        }

        if (isMount) {
            anim.enabled = true;
            if (hasHp && !BattleData.data.InComboGunGameScene) {
                hpBar.ShowBar();
                hpBar.UpdateHpBar(attribute.hp, attribute.max_hp);
            }

            driver.role_attribute.speed_rate += speedRate;
            GetComponent<Collider2D>().enabled = false;
            col.enabled = false;
        } else {
            if (runAnim && anim.GetBool(AnimationRun)) {
                anim.SetBool(AnimationRun, false);
            }

            if (hideCharacter) {
                driver.BodyRenderer.enabled = true;
            }
            
            driver.role_attribute.speed_rate -= speedRate;
            anim.enabled = dead || idleAnim;
            col.enabled = false;

            if (hasHp) {
                hpBar.HideBar();
            }
        }

        OnDriveStatusChanged(isMount);
    }

    protected virtual void OnSetBodyColliderOffset(bool isMount) {
        driver.transform.Find("collider").GetComponent<Collider2D>().offset +=
            new Vector2(0, 1) * (isMount ? -1 : 1);
    }

    protected virtual void OnDriveStatusChanged(bool isMount) { }

    private bool IsNormalMultiHand(MultiHand multiHand) {
        return multiHand is not MultiHandPassive &&
               (MountSpecialAttack == null || multiHand != MountSpecialAttack.SpecialHand);
    }

    public void ResetWeapons(bool isMount) {
        if (isMount) {
            foreach (var multiHand in hands) {
                multiHand.SetController(driver);
            }

            if (useControllerWeapon) {
                foreach (var multiHand in hands) {
                    if (!IsNormalMultiHand(multiHand) || !multiHand.front_weapon) {
                        continue;
                    }

                    var mountWeapon = Instantiate(ResourcesUtil.Load<GameObject>(
                        "RGPrefab/Weapon_Library/weapon_mount.prefab"));
                    mountWeapon.GetComponent<GunMount>().SetLinkWeapon(multiHand.front_weapon);
                    driver.hand.PushWeapon(mountWeapon.GetComponent<GunMount>());
                }
            } else {
                if (driver.hand.front_weapon) {
                    driver.hand.front_weapon.HideWeapon();
                }

                foreach (var w in driver.hand.GetBackWeapons()) {
                    w.HideWeapon();
                }
            }

            ResetWeaponInfo(false);
            if (driver.IsLocalPlayer() && UICanvas.Inst) {
                UpdateSpecialButton(driver);
            }
            foreach (var h in hands) {
                h.Activate();
            }
        } else {
            if (MountSpecialAttack != null && MountSpecialAttack.SpecialHand) {
                foreach (var weapon in MountSpecialAttack.SpecialHand.weapons) {
                    weapon.StopWeapon();
                }
            }

            if (!useControllerWeapon) {
                if (hand) {
                    hand.SetAttack(false);
                }

                if (driver.hand.front_weapon != null) {
                    driver.hand.front_weapon.ShowWeapon(true);
                }

                foreach (var w in driver.hand.GetBackWeapons()) {
                    w.ShowWeapon();
                    driver.hand.SetWeaponBack(w, false, false);
                }

                if (driver.IsLocalPlayer() && !driver.IsDemonstrationCharacter) {
                    if (driver.hand.front_weapon != null) {
                        UICanvas.GetInstance()
                            .ChangeWeaponDisplayObj(
                                driver.hand.front_weapon,
                                driver.hand.front_weapon.realConsume);
                        UICanvas.GetInstance().FlushWeaponSlot(driver.hand.front_weapon);
                        UICanvas.Inst.ShowWeaponBtnIconInfo(driver.hand.front_weapon);
                    } else {
                        UICanvas.GetInstance().ChangeWeaponIcon(null, 0);
                        UICanvas.GetInstance().FlushWeaponSlot(null);
                    }
                }
            } else {
                foreach (var multiHand in hands) {
                    if (IsNormalMultiHand(multiHand)) {
                        driver.hand.RemoveMountWeapon(multiHand.front_weapon);
                    }
                }
            }

            foreach (var h in hands) {
                h.InActivate();
            }
        }
    }

    public static Transform GetParent(RGController controller) {
        return controller.transform.Find("img");
    }
    
    void FuncSwitchWeapon() {
        if (hand) {
            hand.SetAngle(0);
        }

        DoSwitchWeapon();
        RefreshWeaponOutlook();
    }

    protected virtual void DoSwitchWeapon() {
        if (hands.Length > 1) {
            handIndex++;
            if (handIndex >= hands.Length) {
                handIndex = 0;
            }

            hand.SetAttack(false);
            hand = hands[handIndex];

            if (hand.autoSwitch || hand.autoUse) {
                SwitchWeapon();
            } else {
                RGMusicManager.GetInstance().PlayEffect(6);
            }

            ResetWeaponInfo(true);
        }
    }

    public void SwitchWeapon() {
        if (RGGameProcess.Inst == null ||
           RGGameProcess.Inst.modeProcess == null ||
           !RGGameProcess.Inst.modeProcess.InterceptMountSwitchWeapon(this, FuncSwitchWeapon)) {
            FuncSwitchWeapon();
        }
    }

    public void SwitchWeapon(RGWeapon weapon, int idx) {
        hand.SetWeapon(weapon, idx);
    }

    public void AddWeapon(RGWeapon weapon, bool playSound) {
        if (hand.WeaponCount() == hand.MaxWeaponCount()) {
            hand.ExpandWeaponSlots();
        }
        hand.PickupWeapon(weapon, playSound);
    }

    public void DestroyAllWeapons() {
        var weapons = new List<RGWeapon>(hand.weapons);
        foreach (var w in weapons.Where(w => w != null)) {
            hand.DropWeapon(w);
            Destroy(w.gameObject);
        }
    }

    public virtual void SetAttack(bool isAttack) {
        if (driver) {
            if (isAttack && !driver.awake) {
                return;
            }

            if (MountSpecialAttack is { IsSpecialAttacking: true }) {
                return;
            }

            if (!hand) {
                return;
            }

            hand.SetAttack(isAttack);
            if (attackAnimationOnBody) {
                anim.SetBool(AnimationAttack,isAttack);
            }
        } else {
            if (hand) {
                hand.SetAttack(false);
            }
        }
    }

    public void RoleSkill() {
        if (!justMount && driver && driver.awake) {
            Land();
        }
    }

    public virtual void RoleMove(MovementInput m) {
        if (driverTransform != null) {
            if (m.dir != Vector2.zero) {
                if (runAnim && !anim.GetBool(AnimationRun)) {
                    anim.SetBool(AnimationRun, true);
                    CreateSmoke();
                }
            } else {
                if (runAnim && anim.GetBool(AnimationRun)) {
                    anim.SetBool(AnimationRun, false);
                }
            }
        }
        onMountMove?.Invoke(m);
    }

    public void RoleAim(MovementInput m) {
        onMountAim?.Invoke(m);
    }

    void CreateSmoke() {
        if (!anim.GetBool(AnimationRun)) {
            return;
        }

        GameObject tempObj = PrefabPool.Inst.Take(
            PrefabManager.GetPrefab(PrefabName.smoke),
            transform.position,
            Quaternion.identity);
        tempObj.transform.localScale = new Vector3((driver && driver.move_dir.x > 0) ? 1 : -1, 1, 1);
        CancelInvoke(nameof(CreateSmoke));
        Invoke(nameof(CreateSmoke), Random.Range(0.3f, 1f));
    }
    
    [Button(Name = "模拟受伤")]
    public virtual int GetHurt(int damage) {
        if (hitClip != null) {
            RGMusicManager.Inst.PlayEffect(hitClip);
        }

        if (BattleData.data.InComboGunGameScene) {
            return damage;
        }
        
        var armor = attribute.armor;
        attribute.armor -= damage;
        
        var realDamage = damage - armor;
        if (realDamage > 0) {
            attribute.hp -= realDamage;
            hpBar.UpdateHpBar(attribute.hp, attribute.max_hp);
            if (attribute.hp < attribute.max_hp / 2f && !isCreature && driver && driver.IsLocalPlayer() && hasFx) {
                UIMech.Dangerous();
            }

            if (attribute.hp <= 0) {
                Dead();
            }
        }
        
        if (NetControllerManager.Inst.playerCount > 0 && driver && driver.IsLocalPlayer()) {
            NetControllerManager.Inst.localController.SetMountHp(attribute.hp);
        }

        if (driver) {
            driver.StartHitTrigger(driver, 0.5f);
        }

        return realDamage;
    }
    
    public virtual bool GetForce(Vector2 dir, float force) {
        return ignoreForce > 0;
    }

    public void SyncHp(int hp) {
        attribute.hp = hp;
        hpBar.UpdateHpBar(attribute.hp, attribute.max_hp);
    }

    public void Dead() {
        var sourcePlayerIndex = 0;
        if (driver) {
            sourcePlayerIndex = driver.p_index;
        } else {
            var controller = RGGameSceneManager.Inst.controller;
            if (controller) {
                sourcePlayerIndex = controller.p_index;
            }
        }
        
        this.ChangeState(StateEnum.Dead, sourcePlayerIndex);
    }

    protected virtual void DoDead() {
        if (dead) {
            return;
        }

        dead = true;
        canTakeoff = true;
        if (driver) {
            DoLand();
        }

        CancelInvoke();
        
        if (MountSpecialAttack is { IsSpecialAttacking: true }) {
            SpecialClick(false);
        }

        if (hasHp) {
            anim.SetBool(BDead, true);
            anim.SetTrigger(AnimationDead);
            if (playDeadClip && deadClip) {
                RGMusicManager.GetInstance().PlayEffect(deadClip);
            }

            if (deadObject) {
                Invoke(nameof(DeadExplode), deadExplosionDelay);
            }
        } else {
            Destroy(gameObject);
        }

        onDead?.Invoke();
        if (BattleData.data.remainsDestroy) {
            Destroy(gameObject, RGGameConst.REMAINS_TIME);
        }
        DisableShadow();
    }
    
    void DeadExplode() {
        if (!deadObject) {
            return;
        }

        var obj = Instantiate(
            deadObject, RGGameSceneManager.GetInstance().temp_objects_parent, true);
        obj.transform.position = transform.position;
        if (obj.GetComponent<Explode>()) {
            obj.GetComponent<Explode>().damage = attribute.max_hp;
        }

        OnDeadExplode?.Invoke(obj);
    }
    
    // ReSharper disable once UnusedMember.Global
    // Called by MessageManager.HandleDead
    protected void SyncDead(DeadSyncData data) {
        Dead();
    }

    private void ResetWeaponInfo(bool showText) {
        if (driver == null ||
            !driver.IsLocalPlayer() ||
            useControllerWeapon ||
            hand == null ||
            hand.front_weapon == null ||
            driver.IsDemonstrationCharacter) {
            return;
        }

        UICanvas.GetInstance()
            .ChangeWeaponDisplayObj(hand.front_weapon,
                hand.GetConsume());
        if (UICanvas.Inst == null) {
            return;
        }

        UICanvas.GetInstance().FlushWeaponSlot(hand.front_weapon);
        UICanvas.GetInstance().ShowWeaponBtnIconInfo(hand.front_weapon);
        if (showText) {
            UICanvas.GetInstance().ShowTextTalk(
                transform, hand.GetWeaponI2Name(), 2.5f, 1f, hand.GetWeaponLevel());
        }
    }

    private void RefreshWeaponOutlook() {
        if (!hideInactiveWeapon) return;
        foreach (var h in hands) {
            if (h.autoUse) continue;
            h.SetSpriteRenderer(h == hand);
        }
    }

    public void CheckGray() {
        if (itemLevel == 6) {
            return;
        }
        bool isGray = ItemData.data.GetBluePrintStatus(unlock_key) != emBluePrintStatus.Researched;
        foreach (var spriteRenderer in GetComponentsInChildren<SpriteRenderer>()) {
            if (isGray) {
                spriteRenderer.material.EnableKeyword("GRAY_ON");
            } else {
                spriteRenderer.material.DisableKeyword("GRAY_ON");
            }
        }
    }

   private bool _inConsumingHp;
   
    public void StartConsumingHp() {
        if (_inConsumingHp) {
            return;
        }
        
        StartCoroutine(ConsumingHp());
    }

    IEnumerator ConsumingHp() {
        var wait = new WaitForSeconds(1f);
        _inConsumingHp = true;
        while (!dead && !canTakeoff) {
            yield return wait;
            if (!canTakeoff) {
                GetHurt(1);
            }
        }

        _inConsumingHp = false;
    }

    protected virtual void OnDisable() {
        _inConsumingHp = false;
    }

    protected virtual void DisplayInfoOnApproach(RGController controller){
        Vector3 tempv3 = transform.position;
        tempv3.y += 3f;
        UICanvas.GetInstance().ShowObjectInfo(tempv3, GetItemName(), GetItemLevel());
        UICanvas.GetInstance().ShowItemInfo(
            new[] {1, 9, 8},
            new[] {attribute.hp.ToString(), defence.ToString(), ((int)(speedRate * 100)) + "%"});
    }

    protected virtual void HideInfoOnLeave(RGController controller) {
        UICanvas.GetInstance().HideObjectInfo();
        UICanvas.GetInstance().HideItemInfo();
    }

    #region ItemInterface
    public void OnTriggerEnter2D(Collider2D other) {
        if (driver || !other.gameObject.CompareTag("Body_P") ||
            (RGGameSceneManager.GetInstance().game_state == 1 && !GameUtil.IsDefenceMode()) ||
            !canMount) {
            return;
        }

        var rgController = other.GetComponent<RGController>();
        if (!rgController.IsLocalPlayer()) {
            return;
        }

        rgController.SetInteractItem(transform, GetItemName(), GetItemLevel(), 3, onEnter: () => {
            DisplayInfoOnApproach(rgController);
        }, onExit: ()=>{
            HideInfoOnLeave(rgController);
        });
        if (!rgController.mount && rgController.IsLocalPlayer()) {
            UICanvas.GetInstance().ShowBtnSpecial(-1);
        }
    }

    public void OnTriggerExit2D(Collider2D other) {
        if (driver || !other.gameObject.CompareTag("Body_P")) {
            return;
        }

        var rgController = other.GetComponent<RGController>();
        if (rgController.IsLocalPlayer()) {
            rgController.RemoveInteractItem(transform);
        }
        if (rgController.hand.front_weapon) {
            rgController.hand.front_weapon.FlushIcon();
        }
    }

    public void ItemTrigger(RGController controller) {
        Transform bossInfoTrans = UICanvas.GetInstance().transform.Find("boss_info");
        if (controller.canMount && (!bossInfoTrans || !bossInfoTrans.gameObject.activeSelf ||
                                    (bossInfoTrans.GetComponent<BossInfo>() is { IsAnimEnd: true }))) {
            this.ChangeStateLocalPlayer(StateEnum.Interact);
        } else if (controller.IsLocalPlayer()) {
            controller.SetItemtf(null);
            HideInfoOnLeave(controller);
        }
    }

    public void SyncItemTrigger(RGController controller, string extraInfo) {
    }

    public void UpdateHpBarTransform() {
        if (hasHp) {
            hpBar.UpdateTransform();
        }
    }

    public int GetItemValue() {
        return itemValue;
    }

    public string GetItemName() {
        return NameUtil.GetMechName(name);
    }

    public int GetItemLevel() {
        return itemLevel;
    }

    public bool CanUse() {
        return null == driver;
    }

    #endregion

    internal Vector3 GetSourcePosition() {
        if (!driver) {
            return transform.position;
        }

        if (useControllerWeapon && driver.hand.front_weapon != null &&
            driver.hand.front_weapon.weapon_type == emWeaponType.Token) {
            //角色使用坐骑武器时返回坐骑的位置
            return transform.position;
        } else { //否则返回角色位置
            return driver.transform.position;
        }
    }

    public void ChangeAtk(float value, emChangeValueType changeType) {
        foreach (var h in hands) {
            var frontWeapon = h.front_weapon;
            if (frontWeapon != null) {
                frontWeapon.ChangeAtk(value, changeType);
            }
        }
    }
    
    public void ChangeLevel(int value) {
        if (value <= 0) {
            return;
        }
        
        foreach (var h in hands) {
            if (h.weapons is { Length: > 0 })
                h.weapons.ForEach(w => {
                    if (w != null) {
                        w.level = value;
                    }
                });
        }
    }
    
    public void ChangeWeaponSpeed(float value) {
        foreach (var h in hands) {
            if (h.weapons is { Length: > 0 })
                // dont use SetWeaponSpeed()
                h.weapons.ForEach(w => {
                    if (w != null) {
                        w.weapon_speed *= value;
                    }
                });
        }
    }

    public bool isSpecialDown { get; protected set; }
    [NonSerialized]
    public float deadExplosionDelay = 1f;
    
    private bool HasSpecialAttack => MountSpecialAttack != null;

    private bool UseControllerWeaponSpecialAttack =>
        MountSpecialAttack == null && driver != null && driver.CanTriggerSpecialWeapon && useControllerWeapon;

    /// <summary>
    /// 在机甲状态下按下特殊按键后触发
    /// </summary>
    /// <param name="isDown">按下、抬起状态</param>
    public virtual void SpecialClick(bool isDown) {
        isSpecialDown = isDown;
        if (HasSpecialAttack) {
            if (isDown) {
                if (hand.IsAttacking) {
                    hand.SetAttack(false);
                }

                MountSpecialAttack.SpecialDown();
            } else {
                MountSpecialAttack.SpecialUp();
            }
        }

        if (UseControllerWeaponSpecialAttack) {
            driver.TriggerSpecialWeapon(isDown);
        }
    }
    
    public virtual void FlushWeaponIcon(RGWeapon rgWeapon) {
        if (rgWeapon == null || driver == null) {
            return;
        }

        if (!driver.IsLocalPlayer()) {
            return;
        }

        UpdateSpecialButtonWithWeapon(rgWeapon);
        if (!useControllerWeapon || driver.IsDemonstrationCharacter) return;
        UICanvas.GetInstance()
            .ChangeWeaponDisplayObj(rgWeapon, rgWeapon.realConsume);
        UICanvas.GetInstance().FlushWeaponSlot(rgWeapon);
        UICanvas.Inst.ShowWeaponBtnIconInfo(rgWeapon);
    }

    protected void UpdateSpecialButtonWithWeapon(RGWeapon rgWeapon) {
        var specialMode = -1;
        if (MountSpecialAttack != null) {
            specialMode = 0;
        } else if (useControllerWeapon && rgWeapon.isSpecialWeapon) {
            specialMode = rgWeapon.weaponSpecial.IsSpecial ? rgWeapon.weaponSpecial.SpecialMode + 1 : -1;
        }
        var canvas = UICanvas.GetInstance();
        if (canvas != null) {
            canvas.ShowBtnSpecial(specialMode);
        }
    }

    public void UIFlushNullWeapon() {
        if (!driver.IsLocalPlayer()) {
            return;
        }

        if (!useControllerWeapon) return;
        UICanvas.GetInstance().ChangeWeaponIcon(null, 0);
        UICanvas.GetInstance().FlushWeaponSlot(null);
        UpdateSpecialButton(driver);
    }

    public void SetNewWeaponActive(GameObject newWeaponGo, RGWeapon frontWeaponGo) {
        newWeaponGo.SetActive(useControllerWeapon && (!frontWeaponGo || frontWeaponGo.gameObject.activeSelf));
    }
    
    public void AddRecord() {
        if (!NeedRecord || !DataUtil.IsHeroRoom()) {
            return;
        }

        BattleData.data.AddConsumeInfo(new ConsumeInfo {
            consumeType = emConsumeType.Mech,
            name = name,
        });
        NeedRecord = false;
    }
    
    public virtual void EnableShadow(float durTime) {
        CancelInvoke(nameof(DisableShadow));
        var phantom = transform.Find("img/body").GetComponent<PhantomCreator>();
        if (null == phantom) {
            phantom = transform.Find("img/body").gameObject.AddComponent<PhantomCreator>();
        }
        phantom.sprite_color = new Color(113 / 255f, 1f, 1f);
        phantom.interval = 0.05f;
        phantom.endAlpha = 0.05f;
        phantom.fadeSpeed = 2f;
        phantom.enabled = true;
        phantom.mode = PhantomCreator.emTargetMode.SpriteRender;
        if (durTime > 0) {
            Invoke(nameof(DisableShadow), durTime);
        }
    }

    public virtual void DisableShadow() {
        var phantom = transform.Find("img/body").GetComponent<PhantomCreator>();
        if (null != phantom) {
            phantom.enabled = false;
        }
    }

    public bool IsMoving() {
        return driver && driver.rigibody.velocity.magnitude > 0;
    }
    
    [Serializable]
    public class StartMovingEvent : UnityEvent { }

    public StartMovingEvent startMoving;
    
    [Serializable]
    public class StopMovingEvent : UnityEvent { }

    public StopMovingEvent stopMoving;

    protected void UpdateSortingLayer(bool isMount){
        if (isMount) {
            _subRenderers ??= transform.GetComponentsInChildren<Renderer>(true);
            foreach(var r in _subRenderers){
                if(r.sortingLayerName == "Character"){
                    r.sortingOrder += SortingOrderOffset;
                }
            }
        }
        else {
            if (_subRenderers == null) {
                return;
            }

            foreach(var r in _subRenderers){
                if (r == null) {
                    continue;
                }
                if(r.sortingLayerName == "Character"){
                    r.sortingOrder -= SortingOrderOffset;
                }
            }
        }
    }

    public void DestroyProduct() {
        var mountFactory = FactoryManager.Instance.GetFactory<MountFactory>();
        mountFactory.Destroy(InstanceID);
    }
    
    protected override void OnDestroy() {
        base.OnDestroy();
        DestroyProduct();
    }
    
    public override Dictionary<StateEnum, StateSyncStrategy> StateSyncStrategyConfig { get; set; } = new() {
        { StateEnum.Interact, StateSyncStrategy.DefaultLocal },
        { StateEnum.Mount, StateSyncStrategy.DefaultRemote },
        { StateEnum.Dismount, StateSyncStrategy.DefaultLocal },
        { StateEnum.Dead, StateSyncStrategy.DefaultLocal }
    };
    
    public override void PreServerProcessData(StateEnum state, int sourcePlayerIndex, ref string data) {
        // ReSharper disable once InvertIf
        if (state == StateEnum.Mount) {
            data = driver ? "False" : "True";  
        }
    }

    private void OnInteract(RGController sourcePlayer) {
        if (!sourcePlayer) {
            return;
        }
        
        if (sourcePlayer.IsLocalPlayer()) {
            if (GameUtil.IsSingleGameOrHeroRoomMultiGame()) {
                HideInfoOnLeave(sourcePlayer);
                AddRecord();
            }
                        
            if (GameUtil.MultiGameInHeroRoom()) {
                BattleData.data.mountName = this.name;
                BattleData.data.hp = this.attribute.hp;
            }
        }

        RGMusicManager.GetInstance().PlayEffect(RGMusicManager.emEffect.Message);
        Mount(sourcePlayer);
    }

    private void OnMount(RGController sourcePlayer) {
        if (!sourcePlayer) {
            return;
        }
        
        var cgPlayer = sourcePlayer.GetComponent<CGPlayer>();
        if (cgPlayer) {
            cgPlayer.DoMount(this);
            DoMount(sourcePlayer);
        } else {
            var controller = RGGameSceneManager.GetInstance().controller;
            if (controller && controller.CompareItem(transform)) {
                HideInfoOnLeave(sourcePlayer);
                controller.SetItemtf(null);
            }
            sourcePlayer.SetItemtf(null);
            DoMount(sourcePlayer);
        }
    }

    private void OnDismount(Component sourcePlayer) {
        if (!sourcePlayer) {
            return;
        }
        
        var cgPlayer = sourcePlayer.GetComponent<CGPlayer>();
        if (cgPlayer) {
            DoLand();
            cgPlayer.DoDismount();
        } else {
            DoLand();
        }
    }
    
    public override void OnStateChanged(int sourcePlayerIndex, string data) {
        var sourcePlayer = StateSynchronizer.GetPlayerController(sourcePlayerIndex);
        // ReSharper disable once SwitchStatementMissingSomeEnumCasesNoDefault
        switch (SyncState) {
            case StateEnum.Interact: {
                OnInteract(sourcePlayer);
                break;
            }
            
            case StateEnum.Mount: {
                if (bool.TryParse(data, out var ret) && ret) {
                    OnMount(sourcePlayer);
                }
                break;
            }
            
            case StateEnum.Dismount: {
                OnDismount(sourcePlayer);
                break;
            }
            
            case StateEnum.Dead: {
                DoDead();
                break;
            }
        }
    }

    public override bool CanChangeState(StateEnum state) {
        return SyncState != state || state != StateEnum.Mount;
    }
}
