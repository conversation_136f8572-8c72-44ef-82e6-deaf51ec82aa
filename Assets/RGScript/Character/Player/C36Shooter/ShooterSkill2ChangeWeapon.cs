using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;
using Sirenix.OdinInspector;
using UnityEngine.EventSystems;

namespace RGScript.Character.Player.C36Shooter {
    [RequireComponent(typeof(Button))]
    public class ShooterSkill2ChangeWeapon : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerDownHandler, IPointerUpHandler {
        [Title("图标资源")] [SerializeField] private Image iconImage;
        [SerializeField] private Sprite spriteOn;
        [SerializeField] private Sprite spriteOff;

        [Title("配置")] [SerializeField] private emWeaponType weaponType;
        [SerializeField] private ShooterSkill2ChangeWeapon[] siblings;

        private C37Controller _controller;
        private bool _isSelected;

#if UNITY_EDITOR
        [Title("Editor 调试按键")] [SerializeField]
        private KeyCode debugKey = KeyCode.None;

        private void Update() {
            if (debugKey == KeyCode.None) return;

            if (Input.GetKeyDown(debugKey)) {
                OnPointerDown(new PointerEventData(EventSystem.current));
            } else if (Input.GetKeyUp(debugKey)) {
                OnPointerUp(new PointerEventData(EventSystem.current));
            }
        }
#endif

        private void Awake() {
            _controller = RGGameSceneManager.Inst.controller as C37Controller;
            SetSelected(false);
            SimpleEventManager.AddEventListener<ShooterSkill2SyncWeaponUIEvent>(OnSyncFromController);
            GetComponent<Image>().alphaHitTestMinimumThreshold = 0.1f;
        }

        private void OnDestroy() {
            SimpleEventManager.RemoveEventListener<ShooterSkill2SyncWeaponUIEvent>(OnSyncFromController);
        }

        public void OnPointerDown(PointerEventData eventData) {
            PlayClickAnim(); 
            if (!_isSelected)
                ChangeWeapon();

            _controller?.RoleAtk(true);
        }

        public void OnPointerUp(PointerEventData eventData) {
            _controller?.RoleAtk(false);
        }

        private void OnSyncFromController(ShooterSkill2SyncWeaponUIEvent e) {
            bool selected = e.WeaponType == weaponType;
            SetSelected(selected);
        }

        private void ChangeWeapon() {
            foreach (var s in siblings) s.SetSelected(false);
            SetSelected(true);

            SimpleEventManager.Raise(new ShooterSkill2ChangeWeaponEvent(weaponType));
        }

        private void SetSelected(bool selected) {
            if (_isSelected == selected)
                return;

            _isSelected = selected;
            iconImage.sprite = selected ? spriteOn : spriteOff;
        }

        private void PlayClickAnim() {
            iconImage.transform.DOKill();

            iconImage.transform
                .DOScale(0.63f, 0.08f)
                .SetEase(Ease.InQuad)
                .OnComplete(() => {
                    iconImage.transform
                        .DOScale(0.73f, 0.15f)
                        .SetEase(Ease.OutBack, 1f);
                });
        }
    }
}