using DG.Tweening;
using RGScript.Character.Player.C36Shooter.DamageModifier;
using UnityEngine;
using Random = UnityEngine.Random;

namespace RGScript.Character.Player.C36Shooter {
    public class AimAreaDamage : MonoBehaviour {
        private GameObject _source;

        [Header("范围与检测")] public float radius = 8f;
        public LayerMask targetMask;

        [Header("减速效果")] public float slowRatio = -0.3f;
        public float slowTime = 2f;

        [SerializeField] private AimReticleFeedback[] aimReticles;

        [Header("伤害")] public C37Controller.DamageProfile profile;
        public DamageType damageType = DamageType.Aoe;
        public ElementalType element = ElementalType.None;

        [Header("命中特效")] public GameObject hitEffect;
        public GameObject bulletEffect;
        public GameObject fireCircle;
        public GameObject tagetBuff;
        
        [Tooltip("每触发 N 次伤害才生成一次 bulletEffect (≤1 表示每次都生成)")]
        public int bulletEffectInterval = 1;

        private int _bulletEffectCounter;

        [Header("连续命中加成")] public ConsecutiveHitBonus consecutiveBonus = new();

        private readonly Collider2D[] _hits = new Collider2D[32];
        private ContactFilter2D _filter;
        private SpriteRenderer _circleSr;

        private bool _expanded;
        private Tween _hitTween;

        private EnemyHitRegistry _registry;
        private FireEnchantEffect _fireEnchant;

        public AudioClip atkClip;

        private static readonly Vector2[] Offsets = {
            new(0.2f, 0),
            new(-0.2f, 0),
            new(0, 0.2f),
            new(0, -0.2f)
        };

        private void Awake() {
            if (targetMask == 0) targetMask = 1 << LayerMask.NameToLayer("Body_E");
            _filter = new ContactFilter2D { useLayerMask = true, layerMask = targetMask, useTriggers = true };

            if (aimReticles == null || aimReticles.Length == 0)
                aimReticles = GetComponentsInChildren<AimReticleFeedback>(includeInactive: true);

            foreach (var r in aimReticles) {
                r.SetDetectConfig(radius, targetMask);
            }

            _registry = GetComponent<EnemyHitRegistry>();
            _fireEnchant = GetComponent<FireEnchantEffect>();

            if (hitEffect && hitEffect.TryGetComponent<IPrefabPoolObject>(out _)) {
                StartCoroutine(PrefabPool.Inst.Cache(hitEffect, 5));
            }

            if (bulletEffect && bulletEffect.TryGetComponent<IPrefabPoolObject>(out _)) {
                StartCoroutine(PrefabPool.Inst.Cache(bulletEffect, 3));
            }
        }
        
        private void OnEnable() {
            SimpleEventManager.AddEventListener<ShooterCompletedSkillEvent>(OnSkillComplete);
        }

        private void OnSkillComplete(ShooterCompletedSkillEvent e) {
            consecutiveBonus.Reset();
            SimpleEventManager.RemoveEventListener<ShooterCompletedSkillEvent>(OnSkillComplete);
        }

        public void ApplyDamage(GameObject source) {
            RGMusicManager.GetInstance().PlayEffect(atkClip);
            
            _source = source;
            foreach (var r in aimReticles) r.PlayFireFeedback();

            int count = Physics2D.OverlapCircle(transform.position, radius, _filter, _hits);

            if (hitEffect && PrefabPool.Inst) {
                PrefabPool.Inst.Take(
                    hitEffect,
                    transform.position,
                    Quaternion.identity
                );
            }
            
            if (fireCircle)
                Instantiate(fireCircle, transform.position, Quaternion.identity)
                    .GetComponent<BulletFire>().SetSourceObject(_source);

            int baseDmg = profile.damage;

            //暴击天赋增益翻倍
            float crit = profile.critChance;
            crit += source.GetComponent<C37Controller>().role_attribute.critical * 0.01f; //枪手本身暴击会叠加到准心武器上
            if (BattleData.data.HasBuff(emBuff.MasterAccurate))
                crit = Mathf.Clamp01(crit * 2f);
            bool isCrit = Random.value < crit;

            RGEController mainEnemy = null;
            for (int i = 0; i < count; i++) {
                mainEnemy = _hits[i].GetComponent<RGEController>();
                if (mainEnemy) break; // 第一个合法敌人即为主目标
            }

            float bonusMult = consecutiveBonus.GetMultiplierForAttack(mainEnemy, _source);

            for (int i = 0; i < count; i++) {
                var box = _hits[i].GetComponent<RGBox>();
                if (box) {
                    box.BoxDestroy(source);
                    continue;
                }
                
                var enemy = _hits[i].GetComponent<RGEController>();
                if (!enemy) continue;

                float mult = enemy == mainEnemy ? bonusMult : 1f;
                int damage = isCrit
                    ? Mathf.RoundToInt(baseDmg * 2f * mult)
                    : Mathf.RoundToInt(baseDmg * mult);

                //根据技能等级提高1.25倍伤害
                damage = _source.GetComponent<RGController>().ProcessSkillDamage(damage, 0.25f);
                var hurtInfo = new HurtInfo {
                    Damage = damage,
                    Critic = isCrit,
                    DamageType = damageType,
                    ElementalType = element,
                    Source = source,
                    FingerPrint = DamageCarrier.DefaultFingerPrint
                };
                
                _fireEnchant?.Apply(enemy, ref hurtInfo);

                enemy.attribute.ChangeSpeed("sniper_slow", slowRatio, slowTime);
                if (damage != 0) {
                    DamageCarrierHelper.ProcessEnemyGetHurtDamage(enemy, hurtInfo);
                    SimpleEventManager.Raise(new PlayerBulletHitEnemyEvent {
                        enemy = enemy,
                        sourceObj = _source,
                        damage = damage,
                        isCritical = isCrit
                    });
                }

                if (tagetBuff) RGBuff.AddBuff(tagetBuff, source, enemy.transform, true);

                if (bulletEffect
                    && (bulletEffectInterval <= 1
                        || (++_bulletEffectCounter % bulletEffectInterval == 0))) {
                    Vector2 off = Offsets[Random.Range(0, Offsets.Length)];
                    PrefabPool.Inst.Take(
                        bulletEffect,
                        enemy.transform.position + (Vector3)off,
                        Quaternion.identity,
                        enemy.transform
                    );
                }

                _registry?.RegisterEnemy(enemy);
            }
        }

#if UNITY_EDITOR
        private void OnDrawGizmosSelected() {
            Gizmos.color = new Color(1f, 0, 0, .35f);
            Gizmos.DrawWireSphere(transform.position, radius);
        }
#endif
    }
}