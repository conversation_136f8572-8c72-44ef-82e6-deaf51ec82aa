// ReSharper disable InconsistentNaming

using Cicada;
using Sirenix.OdinInspector;
using System;
using System.Collections;
using UnityEngine;
using System.Collections.Generic;
using HutongGames.PlayMaker.Actions;
using Rewired;
using RGScript.Data.YearReview;

namespace RGScript.Character {
    [Serializable]
    public class RoleAttributeProxy {
        public RoleAttribute Client { get; }

        // Proxy fields
        // RoleAttribute start
        public int camp {
            get => Client.camp;
            set => Client.camp = value;
        }

        static public string GetAttributeValueText(emRoleAttribute attr, float value) {
            var percent = false;
            switch (attr) {
                case emRoleAttribute.Critic:
                    percent = true;
                    break;
                case emRoleAttribute.AttackSpeed:
                    value *= 100;
                    percent = true;
                    break;
                case emRoleAttribute.WeaponDamageFactor:
                    value *= 100;
                    percent = true;
                    break;
                case emRoleAttribute.AoeRange:
                    value *= 100;
                    percent = true;
                    break;
                case emRoleAttribute.Lifesteal:
                    percent = true;
                    break;
            }

            var color = Color.white;
            if (value > 0) {
                color = Color.green;
            } else if (value < 0) {
                color = Color.red;
            }

            var valueText = $"{value:F1}";
            if (valueText.EndsWith(".0")) {
                valueText = valueText.Replace(".0", "");
            }

            if (percent) {
                valueText += "%";
            }

            return valueText;
        }

        static public (string, Color) GetAttributeValueTextWithColor(emRoleAttribute attr, float value) {
            var color = Color.white;
            if (value > 0) {
                color = Color.green;
            } else if (value < 0) {
                color = Color.red;
            }
            return (string.Format(color.ToRichText(), GetAttributeValueText(attr, value)), color);
        }

        public (string, Color) GetAttributeValueText(emRoleAttribute attr) {
            return GetAttributeValueTextWithColor(attr, GetAttributeDescValue(attr));
        }

        public float GetAttributeDescValue(emRoleAttribute attr) {
            float value = 0;
            switch (attr) {
                case emRoleAttribute.MaxHp:
                    value = MaxHpValue.GetFinalValue();
                    break;
                case emRoleAttribute.MaxEnergy:
                    value = MaxEnergyValue.GetFinalValue();
                    break;
                case emRoleAttribute.MaxArmor:
                    value = MaxArmorValue.GetFinalValue();
                    break;
                case emRoleAttribute.Critic:
                    value = critical;
                    break;
                case emRoleAttribute.Defence:
                    value = defence;
                    break;
                case emRoleAttribute.MoveSpeed:
                    value = Client.baseController.GetMoveSpeed();
                    break;
                case emRoleAttribute.AttackSpeed:
                    value = atk_speed - 1;
                    break;
                case emRoleAttribute.Deviation:
                    value = deviation;
                    break;
                case emRoleAttribute.SkillHaste:
                    value = skillHaste;
                    break;
                case emRoleAttribute.BulletSpeedRatio:
                    value = bulletSpeedRatio;
                    break;
                case emRoleAttribute.MeleeDamage:
                    value = MeleeDamage.IntValue;
                    break;
                case emRoleAttribute.RangedDamage:
                    value = RangedDamage.IntValue;
                    break;
                case emRoleAttribute.WeaponDamageFactor:
                    value = WeaponDamageFactor.Value - 1;
                    break;
                case emRoleAttribute.SkillLevel:
                    value = SkillLevel.IntValue;
                    break;
                case emRoleAttribute.ElementalDamage:
                    value = ElementalDamage.IntValue;
                    break;
                case emRoleAttribute.AoeRange:
                    value = AoeRange.Value;
                    break;
                case emRoleAttribute.Lifesteal:
                    value = Lifesteal.Value;
                    break;
                case emRoleAttribute.EnergyRegen:
                    value = EnergyRegen.Value;
                    break;
                case emRoleAttribute.Salary:
                    value = Salary.IntValue;
                    break;
                case emRoleAttribute.Luck:
                    value = Luck.IntValue;
                    break;
                case emRoleAttribute.PetPower:
                    value = PetPower.IntValue;
                    break;
                case emRoleAttribute.HealthRegen:
                    value = HealthRegen.Value;
                    break;
            }
            return value;
        }

        [ShowInInspector]
        private RoleAttributeValue<float> _speedValue;

        public RoleAttributeValue<float> SpeedValue {
            get {
                _speedValue =
                    _speedValue ?? new RoleAttributeValue<float>(Client.speed, float.MinValue, float.MaxValue);
                return _speedValue;
            }
        }

        public float speed {
            get => SpeedValue.GetFinalValue();
        }

        public float speed_rate {
            get => Client.SpeedRate;
            set => Client.SpeedRate = value;
        }

        public float FinalSpeedRate => Client.FinalSpeedRate;

        HashSet<object> _immuneMoveSpeedSlowDownTags = new();
        public bool immuneMoveSpeedSlowDown => _immuneMoveSpeedSlowDownTags.Count > 0;

        public void SetImmuneMoveSpeedSlowDown(bool val, object tag) {
            if (val) {
                if (_immuneMoveSpeedSlowDownTags.Count == 0) {
                    SpeedValue.SetFilters((in1) => Mathf.Max(in1, 0), (in2) => Mathf.Max(in2, 0), (in3) => Mathf.Max(in3, 1));
                }
                _immuneMoveSpeedSlowDownTags.Add(tag);
                
            } else {
                _immuneMoveSpeedSlowDownTags.Remove(tag);
                if (_immuneMoveSpeedSlowDownTags.Count == 0) {
                    SpeedValue.SetFilters(null, null, null);
                }
            }
        }

        public RoleAttributeValue<float> addSpeedRate => Client.addSpeedRate;
        public RoleAttributeValue<float> addAtkSpeed => Client.addAtkSpeed;

        public int add_energy_ball_recover { get => Client.add_energy_ball_recover; set => Client.add_energy_ball_recover = value; }

        [ShowInInspector]
        private RoleAttributeValue<int> _maxHpValue;

        public RoleAttributeValue<int> MaxHpValue {
            get {
                _maxHpValue =
                    _maxHpValue ?? new RoleAttributeValue<int>(Client.max_hp, 1, Int32.MaxValue);
                return _maxHpValue;
            }
        }

        public float HpPercent => (float)hp / max_hp;
        public int max_hp {
            get => MaxHpValue != null ? MaxHpValue.GetFinalValue() : 0;
            set => MaxHpValue.SetOriginalValue(Client.max_hp = value);
        }

        public int max_hp_changed => max_hp - MaxHpValue.GetBaseValue();

        public int hp {
            get => Client.hp;
            set {
                Client.hp = Mathf.Clamp(value, 0, max_hp);
            }
        }

        public float dynamicAddSpeedFactor {
            get => Client.dynamicAddSpeedFactor;
            set => Client.dynamicAddSpeedFactor = value;
        }

        public int critical {
            get => Client.critical;
            set => Client.critical = value;
        }

        HashSet<object> _criticalChance100 = new();
        // 100%暴击开关
        public bool criticalChance100 => _criticalChance100.Count > 0;
        public void SetCriticalChance100(bool val, object tag) {
            if (val) {
                _criticalChance100.Add(tag);
            } else {
                _criticalChance100.Remove(tag);
            }
        }

        public float deviation {
            get => Client.deviation;
            set => Client.deviation = value;
        }

        // 最终伤害
        public AttributeValue finalDamage {
            get => Client.FinalDamage;
        }

        public AttributeValue criticDamage {
            get => Client.CriticDamage;

        }
        // RoleAttribute end

        // RoleAttributePlayer start
        [ShowInInspector]
        private RoleAttributeValue<int> _maxArmorValue;

        public RoleAttributeValue<int> MaxArmorValue {
            get {
                var roleAttributePlayer = Client as RoleAttributePlayer;
                if (!roleAttributePlayer) {
                    return null;
                }

                _maxArmorValue =
                    _maxArmorValue ?? new RoleAttributeValue<int>(
                        roleAttributePlayer._max_armor, 1, Int32.MaxValue);
                return _maxArmorValue;
            }
        }

        public int Max_armor => MaxArmor;
        public int MaxArmor { get => MaxArmorValue != null ? MaxArmorValue.GetFinalValue() : 0; }
        public int MaxArmorWithoutTempValue { get => MaxArmorValue != null ? MaxArmorValue.GetFinalValue(true) : 0; }

        public int max_armor_changed => MaxArmor - MaxArmorValue.GetBaseValue();

        //仅用于联机同步护盾显示时使用，初始值设为1，避免出现除数为0，以及可以确保所有情况下护盾是满的。
        public int MaxArmor_Sync = 1;

        public int armor {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.armor;
                }

                return 0;
            }
            set {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    roleAttributePlayer.armor = Mathf.Clamp(value, 0, MaxArmor);
                }
            }
        }

        public bool HasArmor {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.HasArmor;
                }

                return false;
            }
        }

        [ShowInInspector]
        private RoleAttributeValue<int> _maxEnergyValue;

        public RoleAttributeValue<int> MaxEnergyValue {
            get {
                var roleAttributePlayer = Client as RoleAttributePlayer;
                if (!roleAttributePlayer) {
                    return null;
                }

                _maxEnergyValue =
                    _maxEnergyValue ?? new RoleAttributeValue<int>(
                        roleAttributePlayer.max_energy, 1, Int32.MaxValue);
                return _maxEnergyValue;
            }
        }

        public int max_energy { get => MaxEnergyValue != null ? MaxEnergyValue.GetFinalValue() : 0; }

        public int energy {
            get => Client.energy;
            set {
                var deltaEnergy = value - Client.energy;
                Client.energy = Mathf.Clamp(value, 0, max_energy);
                RecordEnergyCostNormalMode(deltaEnergy);
            }
        }

        private void RecordEnergyCostNormalMode(int deltaEnergy) {
            if (Client.baseController is RGController rgController && rgController.IsLocalPlayer() && deltaEnergy < 0 &&
                BattleData.data.IsNormalMode && !BattleData.data.IsLoopTravel) {
                BattleData.data.AddMark(YearReviewManager.GamePlayEnergyCostCount, -deltaEnergy);
            }
        }

        public int max_energy_changed => max_energy - MaxEnergyValue.GetBaseValue(); // 因buff等效果导致max_energy变化，1表示增加，-1表示减少

        public int atk {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.Atk;
                }

                if (Client is RoleAttributePet roleAttributePet) {
                    return roleAttributePet.atk;
                }

                return 0;
            }
            set {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    roleAttributePlayer.Atk = value;
                } else if (Client is RoleAttributePet roleAttributePet) {
                    roleAttributePet.atk = value;
                }
            }
        }

        public float atk_damage_factor = 1;

        public RoleAttributeValue<float> skillDamageFactor => Client.SkillDamageFactor;
        public RoleAttributeValue<float> potionEffectFactor = new RoleAttributeValue<float>(1, 0.01f, 9999);
        public RoleAttributeValue<float> chargeTimeFactor = new RoleAttributeValue<float>(1, 0, 9999);
        public RoleAttributeValue<float> defenceLevel = new RoleAttributeValue<float>(0, float.MinValue, float.MaxValue);

        public float takeDamageFactor = 1; // 受伤倍率
        public float inflictDamagFactor = 1; // 伤害倍率

        public int c_index {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.c_index;
                }

                return 0;
            }
        }

        public int c_level {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.c_level;
                }

                return 0;
            }
        }

        public emBuff default_buff {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.default_buff;
                }

                return 0;
            }
        }

        public float atk_speed_final => atk_speed + addAtkSpeed.GetFinalValue();

        public float atk_speed {
            get {
                return Client.atk_speed;
            }
            set {
                Client.atk_speed = value;
            }
        }

        public event System.Action onAtkSpeedChanged;
        public void OnAtkSpeedChanged() {
            onAtkSpeedChanged?.Invoke();
        }

        public int defence {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.defence;
                }

                return 0;
            }
            set {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    roleAttributePlayer.defence = value;
                }
            }
        }

        public int finalDefence {
            get {
                var N = 0.5f * Mathf.Abs(defence) + 0.972f;
                var value = Mathf.Log10(N) * 6.74f * Mathf.Sign(defence);
                return Mathf.RoundToInt(value);
            }
        }

        public float this_skill_time {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.this_skill_time;
                }

                if (Client is RoleAttributePet roleAttributePet) {
                    return roleAttributePet.this_skill_time;
                }

                return 0.0f;
            }
            set {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    roleAttributePlayer.this_skill_time = value;
                } else if (Client is RoleAttributePet roleAttributePet) {
                    roleAttributePet.this_skill_time = value;
                }
            }
        }

        public RoleAttributeValue<float> armorRestoreSpeed {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.armorRestoreSpeed;
                }

                return null;
            }
        }

        public bool skill_ready {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.skill_ready;
                }

                if (Client is RoleAttributePet roleAttributePet) {
                    return roleAttributePet.skill_ready;
                }

                return false;
            }
        }

        public bool skill_strengthen {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.skill_strengthen;
                }

                return false;
            }
        }

        public float role_skill_time {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.role_skill_time;
                }

                if (Client is RoleAttributePet roleAttributePet) {
                    return roleAttributePet.role_skill_time;
                }

                return 0.0f;
            }
        }

        public SkillInfo skillInfo {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.skillInfo;
                }

                return null;
            }
        }

        public float skillReloadSpeedFactor {
            get => Client.skillReloadSpeedFactor;
            set => Client.skillReloadSpeedFactor = value;
        }

        public float addSkillMaxTime {
            get => Client.addSkillMaxTime;
            set => Client.addSkillMaxTime = value;
        }

        public PetSkillInfo petSkillInfo {
            get {
                if (Client is RoleAttributePet roleAttributePet) {
                    return roleAttributePet.skillInfo;
                }

                return null;
            }
        }

        public uint skill_release_count {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.skill_release_count;
                }

                if (Client is RoleAttributePet roleAttributePet) {
                    return roleAttributePet.skill_release_count;
                }

                return 0;
            }
            set {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    roleAttributePlayer.skill_release_count = value;
                } else if (Client is RoleAttributePet roleAttributePet) {
                    roleAttributePet.skill_release_count = value;
                }
            }
        }

        public float skillCd {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.skillCd;
                }

                if (Client is RoleAttributePet roleAttributePet) {
                    return roleAttributePet.skillCd;
                }

                return 0;
            }
        }

        public float skillHaste {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.skillHaste;
                }

                return 0;
            }

            set {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    roleAttributePlayer.skillHaste = value;
                }
            }
        }

        public float bulletSpeedRatio {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.bullet_speed_ratio;
                }

                return 0;
            }
        }

        private static AttributeValue defaultValue = new AttributeValue(0, readOnly: true);
        private static AttributeValue defaultFactor = new AttributeValue(1, readOnly: true);

        public AttributeValue MeleeDamage {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.MeleeDamage;
                }

                return defaultValue;
            }
        }

        public AttributeValue RangedDamage {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.RangedDamage;
                }

                return defaultValue;
            }
        }

        public AttributeValue WeaponDamageFactor {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.WeaponDamageFactor;
                }

                return defaultFactor;
            }
        }

        public AttributeValue AddWeaponDamage {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.AddWeaponDamage;
                }
                return defaultValue;
            }
        }

        public AttributeValue SkillLevel {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.SkillLevel;
                }

                return defaultValue;
            }
        }

        public AttributeValue ElementalDamage {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.ElementalDamage;
                }

                return defaultValue;
            }
        }

        public AttributeValue ElementalDamageFactor {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.ElementalDamageFactor;
                }

                return defaultFactor;
            }
        }
        public RoleAttributeValue<float> AddFireDamageFactor {
            get {
                return Client.AddFireDamageFactor;
            }
        }

        public AttributeValue AoeRange {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.AoeRange;
                }

                return defaultValue;
            }
        }

        public AttributeValue Lifesteal {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.Lifesteal;
                }

                return defaultValue;
            }
        }

        public AttributeValue EnergyRegen {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.EnergyRengen;
                }

                return defaultValue;
            }
        }

        public AttributeValue Salary {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.Salary;
                }

                return defaultValue;
            }
        }

        public AttributeValue Luck {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.Luck;
                }

                return defaultValue;
            }
        }

        public AttributeValue PetPower {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.PetPower;
                }

                return defaultValue;
            }
        }

        public AttributeValue HealthRegen {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.HealthRegen;
                }

                return defaultValue;
            }
        }

        public RoleAttributeValue<int> ShieldRegen {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.ShieldRegen;
                }

                return null;
            }
        }

        public RoleAttributeValue<float> AbilityDamageFactor {
            get {
                if (Client is RoleAttributePlayer roleAttributePlayer) {
                    return roleAttributePlayer.AbilityDamageFactor;
                }

                return null;
            }
        }


        private float _hpRengenTimer;
        private float _enRegenTimer;
        private float _healthRegen;
        private float _energyRegen;
        [HideInInspector] public RoleAttributeValue<float> hpRegenSpeedFactor = new(1f, 0, float.MaxValue);
        [HideInInspector] public RoleAttributeValue<float> enRegenSpeedFactor = new(1f, 0, float.MaxValue);

        // RoleAttributePlayer end

        public RoleAttributeProxy(RoleAttribute roleAttribute) {
            this.Client = roleAttribute;
        }

        // Proxy functions
        public void ChangeSpeed(string id, float value, float time,
            string effect_up_path = "RGPrefab/Effect/effect_up.prefab",
            string effect_down_path = "RGPrefab/Effect/effect_down.prefab") {
            if (immuneMoveSpeedSlowDown && value < 0) {
                return;
            }
            Client.ChangeSpeed(id, value, time, effect_up_path, effect_down_path);
        }

        public void ChangeSpeedNE(string id, float value, float time) {
            if (immuneMoveSpeedSlowDown && value < 0) {
                return;
            }
            Client.ChangeSpeedNE(id, value, time);
        }

        public void SpeedBack() {
            Client.SpeedBack();
        }

        public void HpChanged() {
            Client.HpChanged();
        }

        public void RestoreHealth(int value, bool sync = false, GameObject sourceObject = null) {
            Client.RestoreHealth(value, sourceObject, sync);
        }

        public void RestoreEnergy(
            int value,
            bool sync = false,
            bool playEffect = false,
            bool fromEnergyBall = false,
            bool breakEnergy = false,
            bool singleText = false,
            RoleAttributePlayer.RestoreEnergySource source = RoleAttributePlayer.RestoreEnergySource.Normal) {
            Client.RestoreEnergy(value, sync, playEffect, fromEnergyBall, breakEnergy, singleText, source);
        }

        public void SetUpChar() {
            if (Client is RoleAttributePlayer roleAttributePlayer) {
                roleAttributePlayer.SetUpChar();
            } else if (Client is RoleAttributePet roleAttributePet) {
                roleAttributePet.SetUpChar();
            }
        }

        public void ChangeAttribute(emRoleAttribute attribute, float value) {
            if (Client is RoleAttributePlayer roleAttributePlayer) {
                roleAttributePlayer.ChangeAttribute(attribute, value);
            } else if (Client is RoleAttributePet roleAttributePet) {
                roleAttributePet.ChangeAttribute(attribute, value);
            }
        }

        public void ReSetArmorReload() {
            if (Client is RoleAttributePlayer roleAttributePlayer) {
                roleAttributePlayer.ReSetArmorReload();
            }
        }

        public bool CanDie() {
            if (Client is RoleAttributePlayer roleAttributePlayer) {
                roleAttributePlayer.CanDie();
            }

            return true;
        }

        public void HpReload() {
            Client.HpReload();
        }

        public void ArmorReload() {
            if (Client is RoleAttributePlayer roleAttributePlayer) {
                roleAttributePlayer.ArmorReload();
            }
        }

        public void SkillReload() {
            if (Client is RoleAttributePlayer roleAttributePlayer) {
                roleAttributePlayer.SkillReload();
            } else if (Client is RoleAttributePet roleAttributePet) {
                roleAttributePet.SkillReload();
            }
        }

        public void SkillReload(float deltaTime) {
            if (Client is RoleAttributePlayer roleAttributePlayer) {
                roleAttributePlayer.SkillReload(deltaTime);
            } else if (Client is RoleAttributePet roleAttributePet) {
                roleAttributePet.SkillReload(deltaTime);
            }
        }

        public void ClampAttribute() {
            if (Client is RoleAttributePlayer roleAttributePlayer) {
                roleAttributePlayer.ClampAttribute();
            }
        }

        public void RefreshSkillCd() {
            if (Client is RoleAttributePlayer roleAttributePlayer) {
                roleAttributePlayer.RefreshSkillCd();
            } else if (Client is RoleAttributePet roleAttributePet) {
                roleAttributePet.RefreshSkillCd();
            }
        }

        public void RestoreArmor(int restore, bool showText = false) {
            if (Client is RoleAttributePlayer roleAttributePlayer) {
                roleAttributePlayer.RestoreArmor(restore, showText);
            }
        }

        public void UpdateArmor(int coinVal) {
            if (Client is RoleAttributePlayer roleAttributePlayer) {
                roleAttributePlayer.UpdateArmor(coinVal);
            }
        }

        public void UpdateArmor() {
            if (Client is RoleAttributePlayer roleAttributePlayer) {
                roleAttributePlayer.UpdateArmor();
            }
        }

        public void ChangeDefenceTemp(int def, float duration) {
            if (Client is RoleAttributePlayer roleAttributePlayer) {
                roleAttributePlayer.ChangeDefenceTemp(def, duration);
            }
        }

        public void ChangeAtkSpeedTemp(int change_val, float duration) {
            if (Client is RoleAttributePlayer roleAttributePlayer) {
                roleAttributePlayer.ChangeAtkSpeedTemp(change_val, duration);
            } else if (Client is RoleAttributePet roleAttributePet) {
                roleAttributePet.ChangeAtkSpeedTemp(change_val, duration);
            }
        }

        public void RestoreHealthAndEnergy(int hpValue, int mpValue, bool sync = false) {
            if (Client is RoleAttributePlayer roleAttributePlayer) {
                roleAttributePlayer.RestoreHealthAndEnergy(hpValue, mpValue, sync);
            }
        }

        public void ReSetSkillReload() {
            if (Client is RoleAttributePlayer roleAttributePlayer) {
                roleAttributePlayer.ReSetSkillReload();
            } else if (Client is RoleAttributePet roleAttributePet) {
                roleAttributePet.ReSetSkillReload();
            }
        }

        public void ConsumeSkillCount(int count) {
            if (Client is RoleAttributePlayer roleAttributePlayer) {
                roleAttributePlayer.ConsumeSkillCount(count);
            } else if (Client is RoleAttributePet roleAttributePet) {
                roleAttributePet.ConsumeSkillCount(count);
            }
        }

        public void ResetClampTime() {
            if (Client is RoleAttributePlayer roleAttributePlayer) {
                roleAttributePlayer.ResetClampTime();
            }
        }

        public IEnumerator RestoringEnergy(bool stopWhenFull = false, int delta = 1) {
            if (Client is RoleAttributePlayer roleAttributePlayer) {
                return roleAttributePlayer.RestoringEnergy(stopWhenFull, delta);
            }

            return null;
        }

        public bool CanChangeMaxHP(int deltaHp) {
            if (Client is RoleAttributePlayer roleAttributePlayer) {
                return roleAttributePlayer.CanChangeMaxHP(deltaHp);
            }

            if (Client is RoleAttributePet roleAttributePet) {
                return roleAttributePet.CanChangeMaxHP(deltaHp);
            }

            return false;
        }

        public void ChangeChargeTimeFactorTemp(string key, float value, float duration) {
            if (Client is RoleAttributePlayer roleAttributePlayer) {
                roleAttributePlayer.ChangeChargeTimeFactorTemp(key, value, duration);
            }
        }

        // 无敌状态
        public event System.Action<bool> OnInvincibleStateChanged;
        public bool isProtectedFromDamage => _protectedFromDamageCounter.Count > 0;
        HashSet<object> _protectedFromDamageCounter = new HashSet<object>();
        public void SetProtectedFromDamage(bool value, object key, float duration = 0) {
            var old = isProtectedFromDamage;
            if (value) {
                _protectedFromDamageCounter.Add(key);
            } else {
                _protectedFromDamageCounter.Remove(key);
            }
            if (old != isProtectedFromDamage) {
                OnInvincibleStateChanged?.Invoke(isProtectedFromDamage);
            }

            if (duration > 0 && value) {
                Client.StartCoroutine(new FuncObj(this, nameof(SetProtectedFromDamage), false, key, 0).ExeuteAsCoroutine(duration));
            }
        }

        public System.Func<bool> immuneDizzyChecker;
        public bool IsImmuneDizzy() {
            if (immuneDizzyChecker != null) {
                return immuneDizzyChecker();
            }
            return false;
        }

        // 被控制状态，无法行动（眩晕、冰冻等）
        public event System.Action<bool, object> OnDisableStateChanged;
        public bool isDisable => _disableCounter.Count > 0;
        HashSet<object> _disableCounter = new HashSet<object>();
        public void SetDisable(bool value, object key) {
            var old = isDisable;
            if (value) {
                _disableCounter.Add(key);
            } else {
                _disableCounter.Remove(key);
            }
            if (old != isDisable) {
                OnDisableStateChanged?.Invoke(isDisable, key);
            }
        }
        public void ClearDisable() {
            _disableCounter.Clear();
        }

        // 运动控制
        public float inertial_vel = 0; //惯性速度
        public Vector2 force_direction;
        public void ClearForce() {
            force_direction = Vector2.zero;
            inertial_vel = 0;
        }

        public int SkillExtraUseTimes {
            get => Client.skillExtraUseTimes;
            set => Client.skillExtraUseTimes = value;
        }

        public void ResetAttributes() {
            _speedValue = null;
            _maxHpValue = null;
            _maxArmorValue = null;
            _maxEnergyValue = null;
            potionEffectFactor = new RoleAttributeValue<float>(1, 0.01f, 9999);
            chargeTimeFactor = new RoleAttributeValue<float>(1, 0, 9999);
            defenceLevel = new RoleAttributeValue<float>(0, float.MinValue, float.MaxValue);
        }

        public void AttributeRegen(float deltaTime) {
            if ((_hpRengenTimer += deltaTime * (HealthRegen.Value > 0 ? hpRegenSpeedFactor.GetFinalValue() : 1)) >= 1) {
                _hpRengenTimer -= 1;
                _healthRegen += HealthRegen.Value;
                if (Mathf.Abs(_healthRegen) >= 1) {
                    var value = (int)_healthRegen;
                    _healthRegen -= value;
                    if (value < 0 && Mathf.Abs(value) >= hp) {
                        value = 1 - hp;
                    }
                    RestoreHealth(value);
                }
            }

            if ((_enRegenTimer += deltaTime * (EnergyRegen.Value > 0 ? enRegenSpeedFactor.GetFinalValue() : 1)) >= 1) {
                _enRegenTimer -= 1;
                _energyRegen += EnergyRegen.Value;
                if (Mathf.Abs(_energyRegen) >= 1) {
                    var value = (int)_energyRegen;
                    _energyRegen -= value;
                    RestoreEnergy(value);
                }
            }
        }
    }
}
