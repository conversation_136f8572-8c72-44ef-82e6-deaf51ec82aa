using ModeSeason.Troop2;
using RGScript.Manager.Factory;
using UnityEngine;

namespace RGScript.Character {
    public class AutoChangeSkin : MonoBehaviour {
        [SerializeField]
        private GameObject rootObject;
        [SerializeField]
        private emHero master;
        [SerializeField]
        private bool isUseDefaultSkinInMultiPlay = true;
        [SerializeField]
        private string[] skins;
        [SerializeField]
        private Color[] colors;

        private CreationRequest _creationRequest;
        private ProductSkin _productSkin;
        
        private void ChangeSkin(int index) {
            if (GameUtil.IsSingleGame()) {
                if (BattleData.data.CompareFactor(emBattleFactor.RandomCharactor)) {
                    index = BattleData.data.skinIndex;
                }
            } else {
                if (rootObject) {
                    if (rootObject.GetComponent<RGPetController>()) {
                        var masterTransform = rootObject.GetComponent<RGPetController>().master_tf;
                        if (masterTransform) {
                            index = masterTransform.GetComponent<RGController>().GetSkinIndex();
                        }
                    } else if (rootObject.GetComponent<OffensiveInterface>() != null) {
                        var sourceObject = rootObject.GetComponent<OffensiveInterface>().GetSourceObject();
                        if (sourceObject && sourceObject.GetComponent<RGController>()) {
                            index = sourceObject.GetComponent<RGController>().GetSkinIndex();
                        }
                    }
                }

                if (isUseDefaultSkinInMultiPlay) {
                    index = 0;
                }
            }

            if (skins != null && skins.Length > index) {
                var skinName = $"skin/{skins[index]}";
                var skinFactory = FactoryManager.Instance.GetFactory<SkinFactory>();
                _creationRequest = skinFactory.Create(skinName, productSkin => {
                    _productSkin = productSkin;
                    GetComponent<Animator>().runtimeAnimatorController = productSkin.AnimatorController;
                });
            }

            if (colors != null && colors.Length > index) {
                GetComponent<SpriteRenderer>().color = colors[index];
            }
        }

        private void Start() {
            if (rootObject) {
                var rgController = rootObject.GetComponent<RGController>();
                if (rgController && rgController.IsDemonstrationCharacter) {
                    ChangeSkin(rgController.GetSkinIndex());
                    return;
                }
            }

            var initWithMaster = false;
            var index = 0;
            if (rootObject) {
                var forceSkinChangerIndex = rootObject.GetComponent<FoceSkinChangerIndex>();
                if (forceSkinChangerIndex) {
                    index = forceSkinChangerIndex.skinIndex;
                    initWithMaster = true;
                } else {
                    var petController = rootObject.GetComponent<RGPetController>();
                    if (petController && petController.master_tf) {
                        var masterTransform = petController.master_tf;
                        if (masterTransform.GetComponent<RGController>()) {
                            index = masterTransform.GetComponent<RGController>().GetSkinIndex();
                        } else if (masterTransform.GetComponent<Troop2Mercenary>()) {
                            index = masterTransform.GetComponent<Troop2Mercenary>().SkinIndex;
                        }
                        initWithMaster = true;
                    }
                }
            }

            if (!initWithMaster) {
                index = RGSaveManager.Inst.GetCurrentSkin(master);
            }

            ChangeSkin(index);
        }

        private void OnDestroy() {
            _creationRequest?.Cancel();
            _productSkin?.DestroyProduct();
        }
    }
}