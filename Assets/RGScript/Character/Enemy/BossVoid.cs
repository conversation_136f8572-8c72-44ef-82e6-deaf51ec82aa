using BattleSystem;
using RGScript.DifficultyLevel;
using System.Collections;
using UnityEngine;
using UnityEngine.Serialization;

public class BossVoid : RGEBossController {
    private static readonly int DeadHash = Animator.StringToHash("dead");
    private BossVoidBrain Brain => AIController.Brain as BossVoidBrain;
    private const int EscapeDropVoidCoinCount = 100;
    [HideInInspector] public bool escaping = false;

    public override void Dead(GameObject source_object, bool sync) {
        if (!Brain.IsFinalForm()) {
            Escape();
            return;
        }

        base.Dead(source_object, sync);
    }

    public void Escape() {
        escaping = true;
        AIController.SkillManager.StopSkill();
        AIController.GotoIdleState();

        HpBar?.gameObject.SetActive(false);
        if (anim) {
            anim.SetTrigger(DeadHash);
        }

        StartCoroutine(EscapeCoroutine());
    }

    private IEnumerator EscapeCoroutine() {
        yield return new WaitForSeconds(3f);
        var manager = DifficultyLevelManager.Inst;
        if (!manager) {
            yield break;
        }

        const int value = 5;
        const float loopCount = EscapeDropVoidCoinCount / 5f;
        for (int i = 0; i < loopCount; i++) {
            manager.DropVoidCoin(value, transform.position);
        }

        Destroy(gameObject);
    }

    public override Vector3 GetHpBarOffset() {
        return new Vector3(0, 4.6f, 0);
    }
}