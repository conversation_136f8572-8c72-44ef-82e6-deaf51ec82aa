using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class AutoFacing : MonoBehaviour, ISandboxSerializable {
    Rigidbody2D rigid;
    static string key = "facing";
    private void Awake() {
        rigid = GetComponent<Rigidbody2D>();
    }
    private void Update() {
        if (rigid.velocity.x != 0 && Mathf.Sign(rigid.velocity.x) != Mathf.Sign(transform.localScale.x)) {
            transform.localScale = new Vector3(Mathf.Sign(rigid.velocity.x) * Mathf.Abs(transform.localScale.x), transform.localScale.y, transform.localScale.z);
        }
    }

    public Dictionary<string, string> GetSerializedData() {
        int direction = transform.localScale.x >= 0 ? 1 : -1;
        return new Dictionary<string, string>() {
            {key , direction.ToString()},
        };
    }

    public bool SetSerializedData(Dictionary<string, string> dic) {
        if (dic.ContainsKey(key)) {
            int direction = int.Parse(dic[key]);
            transform.localScale = new Vector3(direction * transform.localScale.x, transform.localScale.y, transform.localScale.z);
            return true;
        }
        return false;
    }

}
