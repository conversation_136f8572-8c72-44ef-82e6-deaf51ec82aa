using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 青蛙
/// </summary>
public class EnemyAIFrog : RGEController {
    public int damage = 3;
    public GameObject bullet01;
    public GameObject dead_obj;
    bool inAttack;

    protected override void Awake() {
        base.Awake();
        rigibody = gameObject.GetComponent<Rigidbody2D>();
        anim = transform.GetComponent<Animator>();
        role_attribute = gameObject.GetComponent<RoleAttribute>();
        shadow_lock = transform.Find("shadow_lock").GetComponent<SpriteRenderer>();
    }

    protected override void FixedUpdate() {
        InnerFixedUpdate();
        if (awake) {
            if (inAttack) {
                rigibody.velocity = GetVelocity();
            } else if (inertial_vel > 1f && !kinematic) {
                rigibody.velocity = force_direction.normalized * inertial_vel;
                inertial_vel *= friction;
            } else {
                if (dead) {
                    awake = false;
                    rigibody.velocity = new Vector2(0, 0);
                    transform.Find("collider").GetComponent<CircleCollider2D>().enabled = false;
                    this.enabled = false;
                } else {
                    rigibody.velocity = force_direction.normalized * inertial_vel;
                    inertial_vel *= friction;
                }
            }
        }
    }

    public override void RunReflection() {
        if (target_obj != null) {
            target_point = target_obj.position;
            //anim.SetBool("run", true);
            if (rg_random.Range(0, 10) < (intensive ? 10 : 7) && can_shoot) {
                move_direction = ((Vector2)target_point - (Vector2)transform.position).normalized;
                Jump();
            } else {
                Invoke("EndCycle", scout_rate);
            }
        } else {
            float r1 = rg_random.Range(-1f, 1f);
            float r2 = rg_random.Range(-1f, 1f);
            target_point = transform.position + new Vector3(r1, r2).normalized;
            //anim.SetBool("run", true);
            if (rg_random.Range(0, 10) < 3 && can_shoot) {
                Jump();
            } else {
                Invoke("EndCycle", scout_rate);
            }
            //Invoke("EndCycle", scout_rate);
        }
    }

    public override void ShootReflection() {
        if (!dead && !dizzy) {
            CreateSmoke();
            can_shoot = false;
            inAttack = true;
            Invoke("TurnCanShoot", shoot_cd);
            anim.SetTrigger("atk_1");
        } else {
            EndCycle();
        }
    }
    void Jump() {
        move_direction = ((Vector2)target_point - (Vector2)transform.position).normalized;
        anim.SetTrigger("atk");
        Invoke("ShootReflection", 0.25f);
    }
    void OnAtk() {
        inAttack = false;
        move_direction = Vector2.zero;
        rigibody.velocity = Vector2.zero;
        if (target_obj != null) {
            target_point = target_obj.transform.position;
            Invoke("Jump", rg_random.Range(0.25f, 1.0f));
        } else {
            EndCycle();
        }
        if (bullet01 != null) {
            BulletInfo bulletInfo = new BulletInfo().SetUp(bullet01, gameObject, 0, transform.position, 0, camp);
            DamageInfo damageInfo = new DamageInfo().SetUp(bullet01, damage * 2, 0, 3, camp);
            BulletFactory.TakeBullet(bulletInfo,damageInfo);
            //GameObject temp_obj1 = Instantiate(bullet01) as GameObject;
            //temp_obj1.transform.position = transform.position;
            //temp_obj1.GetComponent<OffensiveInterface>().SetSourceObject(gameObject);
        }
        //if (bullet02 != null) {
        //	for (int i = -6; i < 6; i++) {
        //		GameObject temp_obj = Instantiate(bullet02) as GameObject;
        //		//更新武器属性到子弹里
        //		temp_obj.GetComponent<RGBullet>().UpdateAttribute(gameObject, 2, 6, false, 3, camp);
        //		temp_obj.transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;
        //		temp_obj.transform.position = transform.position;
        //		temp_obj.transform.eulerAngles = new Vector3(0, 0, i * 30);
        //	}
        //}
    }
    //*******************调整角色角度***************************
    public override void FixedRotation() {
        Vector2 tempv2 = new Vector2(0, 0);
        if (target_obj != null)
            tempv2 = (Vector2)target_obj.position - (Vector2)transform.position;
        else
            tempv2 = target_point - (Vector2)transform.position;
        if ((tempv2.x > 0 && facing == -1) || (tempv2.x < 0 && facing == 1)) {
            facing *= -1;
            Vector3 temp_v3 = transform.eulerAngles;
            if (facing < 0)
                temp_v3.y = 180;
            else
                temp_v3.y = 0;
            transform.eulerAngles = temp_v3;
        }
        fixed_angle = Vector2.Angle((tempv2), new Vector2(facing, 0));
        if (tempv2.y < 0)
            fixed_angle = -fixed_angle;
    }

    //********************************重写父类函数**********************************
    public override void ChildDead() {
        inAttack = false;
        friction = 0.92f;
    }

    public void DeadEvent() {
        if (!dead_obj) 
            return;
        GameObject temp_obj1 = Instantiate(dead_obj) as GameObject;
        temp_obj1.transform.position = transform.position;
        temp_obj1.GetComponent<OffensiveInterface>().SetSourceObject(gameObject);
    }

    void OnTriggerEnter2D(Collider2D other) {
        HitUnit(other, damage, 1.5f);
    }

}
