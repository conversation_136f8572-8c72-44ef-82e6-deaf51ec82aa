using UnityEngine;

namespace BossSkill {
    public class Boss26HurtProcessor : Mono<PERSON><PERSON><PERSON><PERSON>, IHurtProcessor {
        public int hpRestoreSkillIndex;
        public int hpRestoreAmount;
        public RoleAttribute roleAttribute;
        public AudioClip healClip;
        public GameObject healthEffectPrefab;
        public bool CanProcessHurt(BossAI25 controller) {
            return controller.currentSkillIndex == hpRestoreSkillIndex;
        }

        public void HurtProcess(BossAI25 controller, HurtInfo hurtInfo) {
            roleAttribute.hp += hpRestoreAmount;
            if (roleAttribute.hp > roleAttribute.max_hp) {
                roleAttribute.hp = roleAttribute.max_hp;
            }
            UICanvas.GetInstance().ShowTextHp(transform.position, hpRestoreAmount, 2, gameObject.name);
            var healthEffectInstance = PrefabPool.Inst.Take(healthEffectPrefab);
            healthEffectInstance.transform.SetParent(transform, false);
            if (healClip != null) {
                RGMusicManager.GetInstance().PlayEffect(healClip);
            }
        }
    }
}