using UnityEngine;
using System.Collections;

public class BossAI30Child : MonoBehaviour {

    Rigidbody2D rigibody2d;
    Transform gun_point;
    Transform parent_point;
    Transform target;
    Vector3 target_point;
    bool free_move = false;         //是否在自由活动状态
    bool get_target = true;         //是否到达目标位置
    bool has_target = false;        //自由活动状态是否有目标
    bool lock_target = true;        //瞄准敌人
    float speed = 10;
    public GameObject aim;
    public GameObject bullet01;
    public GameObject bullet02;
    public GameObject bullet03;
    GameObject the_aim;
    RGEController controller;
    bool in_atk1;
    bool in_atk3;
    public AudioClip atk_clip1;
    public AudioClip atk_clip2;
    internal int camp;
    // Use this for initialization
    void Awake() {
        rigibody2d = transform.GetComponent<Rigidbody2D>();
        gun_point = transform.Find("point");
    }

    void FixedUpdate() {
        Vector3 temp_v3;
        if (free_move) {
            temp_v3 = target_point;
        } else {
            temp_v3 = parent_point.position;
        }
        if (get_target) {
            transform.position = temp_v3;
        } else {
            rigibody2d.velocity = (temp_v3 - transform.position).normalized * speed;
            if (Vector2.Distance(transform.position, temp_v3) < 1) {
                GetToTarget();
            }
        }
        if (free_move && lock_target && has_target) {
            FixedRotation();
        }
    }

    public void SetAngrySprite(Sprite value) {
        transform.Find("img").GetComponent<SpriteRenderer>().sprite = value;
    }
    public void SetParentPoint(Transform value, RGEController value2) {
        parent_point = value;
        controller = value2;
    }

    public void SetFreeMove() {
        //transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;
        transform.SetParent(RGGameSceneManager.GetInstance().temp_objects_parent, true);
        speed = 10;
        free_move = true;
        FindTarget();
        if (has_target) {
            GetNextPoint();
        }
    }
    void GetNextPoint() {
        if (free_move && target != null) {
            target_point = target.position;
            float rx = Random.Range(-4, 4);
            float ry = Random.Range(-4, 4);
            target_point.x += rx;
            target_point.y += ry;
            get_target = false;
        }
    }
    public void BackToParent() {
        speed = 20;
        free_move = false;
        //target_point = parent_point.position;
        get_target = false;
        if (the_aim != null) {
            Destroy(the_aim);
        }
    }
    void GetToTarget() {
        if (free_move) {
            Attack(Random.Range(0, 2));
            transform.position = target_point;
        } else {
            transform.parent = parent_point;
            transform.localPosition = Vector3.zero;
            transform.localEulerAngles = new Vector3(0, 0, 0);
        }
        get_target = true;
    }
    protected RaycastHit2D[] detect_list = new RaycastHit2D[8];
    void FindTarget() {
        has_target = false;
        float min_distance = 100;
        int detectCount = Physics2D.CircleCastNonAlloc(transform.position, 10, new Vector2(0, 0), detect_list, 0f, 1 << LayerMask.NameToLayer("Body_P"));

        if (detectCount > 0) {
            min_distance = 100;
            for (int i = 0; i < detectCount; i++) {
                float distance = Vector2.Distance(detect_list[i].transform.position, transform.position);
                if (distance < min_distance) {
                    min_distance = distance;
                    target = detect_list[i].transform;
                    has_target = true;
                }
            }
        }
    }
    //*******************调整角色角度***************************
    void FixedRotation() {
        if (!in_atk1) {
            Vector2 tempv2 = new Vector2(0, 0);
            if (target != null)
                tempv2 = (Vector2)target.position - (Vector2)transform.position;
            float fixed_angle = Vector2.Angle((tempv2), new Vector2(1, 0));
            if (tempv2.y < 0) {
                fixed_angle = -fixed_angle;
            }
            transform.localEulerAngles = new Vector3(0, 0, fixed_angle);
        }
    }
    public void Attack(int value) {
        if (value == 0)
            Atk1();
        else if (value == 1) {
            Atk02();
        } else if (value == 2) {
            Atk03();
        }
    }
    int laser_facing;
    /// <summary>
    /// 短激光
    /// </summary>
    void Atk1() {
        lock_target = false;
        if (the_aim != null) {
            Destroy(the_aim);
        }
        the_aim = PrefabPool.Inst.Take(aim, gun_point.position, Quaternion.identity) as GameObject;
        the_aim.transform.parent = gun_point;
        the_aim.transform.localEulerAngles = new Vector3(0, 0, 0);
        //Invoke ("EndAtk1", 0.6f);
        int facing = controller.GetFacing();
        StartCoroutine(EndAtk1(facing, 0.6f));
    }
    IEnumerator EndAtk1(int facing, float delay) {
        yield return new WaitForSeconds(delay);
        if (the_aim != null) {
            float angle = free_move || facing >= 0 ? the_aim.transform.eulerAngles.z :
                (180 - the_aim.transform.eulerAngles.z);
            var temp_obj = BulletFactory.TakeBullet(
                new BulletInfo().SetUp(bullet01, controller.gameObject, 0, gun_point.position, angle, controller.camp),
                new DamageInfo().SetUp(bullet01, 2, 0, 3, controller.camp)
                );
            lock_target = true;
            Invoke(nameof(GetNextPoint), Random.Range(0.5f, 1.5f));
            RGMusicManager.GetInstance().PlayEffect(atk_clip1);
            Destroy(the_aim);
        }
    }
    /// <summary>
    /// 散弹
    /// </summary>
    void Atk02() {
        for (int i = -2; i <= 2; i++) {
            GameObject temp_obj = PrefabPool.Inst.Take(bullet02);
            if (!temp_obj) continue;
            //更新武器属性到子弹里
            temp_obj.GetComponent<RGBullet>().UpdateAttribute(controller ? controller.gameObject : null, 2, 6, false, 3, camp);
            temp_obj.transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;
            if (gun_point) temp_obj.transform.position = gun_point.position;
            if (controller && !free_move && controller.GetFacing() == -1)
                temp_obj.transform.eulerAngles = new Vector3(0, 0, 180 - transform.eulerAngles.z + i * 20);
            else
                temp_obj.transform.eulerAngles = new Vector3(0, 0, transform.eulerAngles.z + i * 20);
            temp_obj.GetComponent<IPrefabPoolObject>().OnTaken();
        }
        Invoke(nameof(GetNextPoint), Random.Range(0.5f, 1.5f));
        RGMusicManager.GetInstance().PlayEffect(atk_clip2);
    }
    /// <summary>
    /// 大回环
    /// </summary>
    void Atk03() {
        GameObject temp_obj = Instantiate(bullet03) as GameObject;
        //更新武器属性到子弹里
        temp_obj.GetComponentInChildren<RGBulletTrigger>().SetInfo(1, 1, false, 3, 0);
        temp_obj.transform.parent = transform;
        temp_obj.transform.localPosition = Vector3.zero;
        temp_obj.name = "energy_ball";
        in_atk3 = true;
        //InAtk03 ();
        Invoke("EndAtk03", 3.6f);
        StartCoroutine(InAtk03());
    }
    IEnumerator InAtk03() {
        int facing = controller.GetFacing();
        float interval = 0.38f;
        var wait = new WaitForSeconds(interval);
        while (in_atk3) {
            GameObject temp_obj = Instantiate(bullet02) as GameObject;
            //更新武器属性到子弹里
            temp_obj.GetComponent<RGBullet>().UpdateAttribute(controller.gameObject, 2, 6, false, 3, camp);
            temp_obj.transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;
            temp_obj.transform.position = gun_point.position;
            temp_obj.transform.eulerAngles = new Vector3(0, 0, controller.transform.rotation.y == 0 ? transform.eulerAngles.z : 180 - transform.eulerAngles.z);
            yield return wait;
        }
        EndAtk03();
    }
    void EndAtk03() {
        in_atk3 = false;
        if (transform.Find("energy_ball") != null)
            Destroy(transform.Find("energy_ball").gameObject);
    }
}
