using DG.Tweening;
using Sirenix.OdinInspector;
using UnityEngine;

/// <summary>
///  远古巨像
/// </summary>
public class BossAI22 : RGEBossController {
    [InlineEditor(InlineEditorModes.LargePreview)]
    public GameObject stone1;
    [InlineEditor(InlineEditorModes.LargePreview)]
    public GameObject stone2;
    [InlineEditor(InlineEditorModes.LargePreview)]
    public GameObject safe_area;
    [InlineEditor(InlineEditorModes.LargePreview)]
    public GameObject vine_obj;
    [InlineEditor(InlineEditorModes.LargePreview)]
    public GameObject enemy_obj01;
    [InlineEditor(InlineEditorModes.LargePreview)]
    public GameObject enemy_obj02;
    [InlineEditor(InlineEditorModes.LargePreview)]
    public GameObject invers_buff;
    [InlineEditor(InlineEditorModes.LargePreview)]
    public GameObject shake_obj;

    public GameObject move_shake_obj;
    GameObject the_safe_area;
    Sprite<PERSON>enderer danger_sr;
    Sprite<PERSON>enderer light_sr;
    Transform hand_l_tf;
    Transform hand_r_tf;

    public EnemyAI18 child_ctrl01; //boss房内的石像人
    public EnemyAI18 child_ctrl02;
    public GameObject[] face_points;
    public GameObject dead_face_point;
    SoulFace follow_face;

    protected override void Awake(){
        base.Awake();
        rigibody = gameObject.GetComponent<Rigidbody2D>();
        anim = transform.GetComponent<Animator>();
        role_attribute = gameObject.GetComponent<RoleAttribute>();
        shadow_lock = transform.Find("shadow_lock").GetComponent<SpriteRenderer>();
        light_sr = transform.Find("img/head/light").GetComponent<SpriteRenderer>();
        hand_l_tf = transform.Find("img/arm_l/root_arm_l/forearm_l/hand_l/fire_point");
        hand_r_tf = transform.Find("img/arm_r/root_arm_r/forearm_r/hand_r/fire_point");
        follow_face = transform.Find("follow_face").GetComponent<SoulFace>();
        danger_sr = transform.Find("danger_area").GetComponent<SpriteRenderer>();
    }

    public override void SetUpAttribution() {
        base.SetUpAttribution();
        rigibody.bodyType = RigidbodyType2D.Static;
        var child_tf = the_maker.transform.Find("wall_group/e_malphite03_temp");
        if (null != child_tf) {
            child_ctrl01 = child_tf.GetComponent<EnemyAI18>();
        }
        child_tf = the_maker.transform.Find("wall_group/e_malphite03_temp2");
        if (null != child_tf) {
            child_ctrl02 = child_tf.GetComponent<EnemyAI18>();
        }
        SafeArea.SafeAreaList.Clear();
    }

    public override void OnGameStateChange(int game_state) {
        base.OnGameStateChange(game_state);
        if (null != child_ctrl01 && null != child_ctrl02) {
            child_ctrl01.OnGameStateChange(game_state);
            child_ctrl02.OnGameStateChange(game_state);
        }
        if (transform.Find("img")) {
            transform.Find("img").GetComponent<UnityEngine.Rendering.SortingGroup>().sortingOrder = -1;
        }
    }

    public override void ChildDead() {
        base.ChildDead();
        if (null != child_ctrl01 && null != child_ctrl02) {
            child_ctrl01.ChildDead();
            child_ctrl02.ChildDead();
        }
        if (null != dead_face_point) {
            follow_face.track_mode = TrackMode.Follow;
            follow_face.ShowDeadFace(dead_face_point);
            follow_face.lerp_val = 3f;
        }
    }

    public override void ShootReflection() {
        if (can_shoot && !dead && !dizzy) {
            int r1 = rg_random.Range(0, 100);
            can_shoot = false;
            if (SafeArea.SafeAreaList.Count <= 0) {
                if (r1 < 33) {
                    AttackCommand(1);
                } else if (r1 < 66) {
                    AttackCommand(2);
                } else {
                    AttackCommand(3);
                }
            } else {
                if (r1 < 50) {
                    AttackCommand(1);
                } else {
                    AttackCommand(3);
                }
            }
        } else {
            EndCycle();
        }
    }

    public override void AttackCommand(int index) {
        base.AttackCommand(index);
        MoveFaceToTarget(index);
    }

    void MoveFaceToTarget(int index) {
        follow_face.track_mode = TrackMode.Bezier;
        follow_face.ChangeFace();
        if ((index - 1) < face_points.Length) {
            follow_face.MoveToTarget(face_points[index - 1]);
        }
    }

    public override void StartAttack(int index) {
        base.StartAttack(index);
        anim.SetTrigger("atk_" + index);
        switch (index) {
            case 1:
            case 2:
            case 3:
                anim.SetTrigger("atk_" + index);
                break;
        }
        Invoke("TurnCanShoot", shoot_cd);
    }

    int stone1_rate = 50;
    GameObject GetRandomStone() {
        if (stone1_rate < rg_random.Range(0, 100)) {
            return stone1;
        } else {
            return stone2;
        }
    }

    void HandDoShock(bool left, bool right, bool move_shock = false) {
        if (left) {
            if (move_shock) {
                GameObject tmp_obj = Instantiate(move_shake_obj, hand_l_tf.position, Quaternion.identity);
                tmp_obj.GetComponent<RGBullet>().UpdateAttribute(gameObject, 3, angry ? 9 : 7, false, 2, camp);
                tmp_obj.transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;

                Vector2 tempv2 = new Vector2(0, 0);
                if (null != target_obj) {
                    tempv2 = (Vector2)target_obj.position - (Vector2)hand_l_tf.position;
                } else {
                    tempv2 = target_point - (Vector2)transform.position;
                }
                float angle = Vector2.Angle((tempv2), new Vector2(facing, 0));
                if (tempv2.y < 0) {
                    angle = -angle;
                }

                tmp_obj.transform.eulerAngles = new Vector3(0, 0, angle);
            } else {
                GameObject tmp_obj = Instantiate(shake_obj, hand_l_tf.position, Quaternion.identity);
                tmp_obj.transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;
            }
        }
        if (right) {
            if (move_shock) {
                GameObject tmp_obj = Instantiate(move_shake_obj, hand_r_tf.position, Quaternion.identity);
                tmp_obj.GetComponent<RGBullet>().UpdateAttribute(gameObject, 3, angry ? 9 : 7, false, 2, camp);
                tmp_obj.transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;

                Vector2 tempv2 = new Vector2(0, 0);
                if (null != target_obj) {
                    tempv2 = (Vector2)target_obj.position - (Vector2)hand_r_tf.position;
                } else {
                    tempv2 = target_point - (Vector2)transform.position;
                }
                float angle = Vector2.Angle((tempv2), new Vector2(facing, 0));
                if (tempv2.y < 0) {
                    angle = -angle;
                }
                tmp_obj.transform.eulerAngles = new Vector3(0, 0, angle);
            } else {
                GameObject tmp_obj = Instantiate(shake_obj, hand_r_tf.position, Quaternion.identity);
                tmp_obj.transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;
            }
        }
    }

    void InAtk01() {
        if (transform.parent.childCount < 10) {
            GameObject enemyProto = GetRandomTempEnemy();
            GameObject tempObj = Instantiate(PrefabManager.GetPrefab(PrefabName.target), transform.parent, true);
            tempObj.GetComponent<TargetEffect>().SetTarget(enemyProto, gameObject, camp);
            tempObj.transform.parent = transform.parent;
            Vector2 targetPos = GetRoomRandomPos();
            tempObj.transform.position = targetPos;
            HandDoShock(true, false, angry);
        } else {
            HandDoShock(true, false, true);
        }
        RGMusicManager.GetInstance().PlayEffect(boss_clip[0]);
    }

    void EndAtk01() {
        can_shoot = true;
        EndCycle();
    }

    void BeginAtk02() {
        GodMode();
    }

    void InAtk02_1() {
        HandDoShock(false, true, true);
        RGMusicManager.GetInstance().PlayEffect(boss_clip[0]);
    }

    void InAtk02() {
        float rangeVal = 2f;
        int count = angry ? 11 : 9;
        for (int i = 0; i < count; i++) {
            float x = rg_random.Range(-10 + rangeVal, 10 - rangeVal);
            float y = rg_random.Range(-10 + rangeVal, 10 - rangeVal);
            Vector2 tmpPos = new Vector2(x, y);
            tmpPos.x += transform.parent.position.x;
            tmpPos.y += transform.parent.position.y;
            the_safe_area = Instantiate(safe_area, RGGameSceneManager.GetInstance().temp_objects_parent, true);
            the_safe_area.GetComponent<SafeArea>().delay_end_time = angry ? 2f : 3f;
            the_safe_area.transform.position = tmpPos;
            the_safe_area.GetComponent<SafeArea>().SafeAreaEndCallback += OnSafeAreaEndCB;
        }
        danger_sr.enabled = true;
        danger_sr.DOFade(0.14f, 0.4f);
    }

    void OnSafeAreaEndCB(RGController rgctrl) {
        if (dead) {
            return;
        }
        var buffObj = Instantiate(invers_buff, rgctrl.gameObject.transform, true);
        buffObj.GetComponent<RGBuff>().is_enemy = false;
        buffObj.name = invers_buff.name;
        buffObj.GetComponent<OffensiveInterface>().SetSourceObject(gameObject);
        buffObj.transform.localPosition = Vector3.zero;
        danger_sr.DOFade(0f, 0.2f);
    }

    void EndAtk02() {
        can_shoot = true;
        NormalMode();
        EndCycle();
    }

    bool _isGod;
    void GodMode() {
        _isGod = true;
        DOTween.Kill(light_sr);
        light_sr.DOFade(0.8f, 0.2f);
    }

    void NormalMode() {
        _isGod = false;
        DOTween.Kill(light_sr);
        light_sr.DOFade(0f, 0.2f);
    }

    public override void GetHurt(HurtInfo hurtInfo) {
        if (!_isGod) {
            base.GetHurt(hurtInfo);
        }
    }

    private const int EnemyObj01Rate = 50;

    GameObject GetRandomTempEnemy() {
        return EnemyObj01Rate < rg_random.Range(0, 100) ? enemy_obj01 : enemy_obj02;
    }

    void InAtk03() {
        // Debug.Log("召唤落石");
        float range_val = 2f;
        int tmp_count = angry ? 4 : 3;
        for (int i = 0; i < tmp_count; i++) {
            GameObject stone_proto = GetRandomStone();
            var temp_bullet_info = new BulletInfo().SetUp(stone_proto, gameObject, 0, transform.position, 0, camp);
            var temp_damage_info = new DamageInfo().SetUp(stone_proto, 10, 0, 2, camp);
            GameObject tmp_obj = BulletFactory.TakeBullet(temp_bullet_info, temp_damage_info);
            float x = rg_random.Range(-10 + range_val, 10 - range_val);
            float y = rg_random.Range(-10 + range_val, 10 - range_val);
            Vector2 target_pos = new Vector2(x, y);
            tmp_obj.transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;
            target_pos.x += transform.parent.position.x;
            target_pos.y += transform.parent.position.y;
            tmp_obj.transform.position = target_pos;
        }

    }

    void InAtk03_1() {
        RGMusicManager.GetInstance().PlayEffect(boss_clip[0]);
        HandDoShock(true, false);
    }

    void InAtk03_2() {
        RGMusicManager.GetInstance().PlayEffect(boss_clip[0]);
        HandDoShock(false, true);
    }

    void EndAtk03() {
        can_shoot = true;
        EndCycle();
    }

    /// <summary>
    /// 获取房间的随机位置
    /// </summary>
    /// <returns></returns>
    Vector2 GetRoomRandomPos() {
        Vector2 targetPos = transform.position;
        bool isPlaceable = false;
        if (the_maker) {
            var grid = the_maker.theRoom.Grid;
            (Vector2Int pos, bool isPlaceableT) = grid.GetRandomPlaceableGridPosition(rg_random);
            if (isPlaceableT) {
                targetPos = grid.GridPos2WorldPos(pos);
                isPlaceable = true;
            }
        }
        if (!isPlaceable) {
            targetPos = transform.position + new Vector3(rg_random.Range(-5f, 5f), rg_random.Range(-5f, 5f), 0);
        }
        return targetPos;
    }

    public override void RunReflection() {
        if (target_obj != null) {
            float t = rg_random.Range(0.5f * scout_rate, scout_rate);
            Invoke(nameof(ShootReflection), t);
        } else {
            float t = rg_random.Range(0.5f * scout_rate, scout_rate);
            Invoke(nameof(EndCycle), t);
        }
    }

    public override void FixedRotation() {
        Vector2 tempv2 = new Vector2(0, 0);
        if (target_obj != null) {
            if (target_obj.CompareTag("Body_P")) {
                tempv2 = (Vector2)target_obj.GetComponent<RGController>().GetHitPosition() - (Vector2)transform.position;
            } else {
                tempv2 = (Vector2)target_obj.position - (Vector2)transform.position;
            }
        } else {
            tempv2 = target_point - (Vector2)transform.position;
        }
        fixed_angle = Vector2.Angle((tempv2), new Vector2(facing, 0));
        if (tempv2.y < 0) {
            fixed_angle = -fixed_angle;
        }
    }

}