using cfg.Aram;
using UnityEngine;
using System.Collections;

/// <summary>
/// 站立不动会攻击的敌人(触手怪)
/// </summary>
public class EnemyAI06 : RGEController {
    public GameObject dead_obj;

    protected override void Awake() {
        base.Awake();
        anim = transform.GetComponent<Animator>();
        role_attribute = gameObject.GetComponent<RoleAttribute>();
        hand_tf = transform.Find("img/h1");
        hand = hand_tf.GetComponent<RGEHand>();
        shadow_lock = transform.Find("shadow_lock").GetComponent<SpriteRenderer>();
    }

    protected override void FixedUpdate() {
        InnerFixedUpdate();
    }

    public override void Scout() {
        if (CheckScout()) {
            if (dizzy) {
                StartCoroutine(DizzyScout());
            } else {
                if (TargetingType == EnemyTargetingType.Default) {
                    target_obj = null;
                    int detectCount = Physics2D.CircleCastNonAlloc(transform.position, 20, new Vector2(0, 0), detect_list, 0f, 1 << LayerMask.NameToLayer("Body_P"));
                    if (detectCount > 0) {
                        min_distance = 100;
                        for (int i = 0; i < detectCount; i++) {
                            float distance = Vector2.Distance(detect_list[i].transform.position, transform.position);
                            Vector2 temp_v2 = transform.position;
                            Vector2 temp_v3 = detect_list[i].transform.position;
                            temp_v2.y += 0.5f;
                            temp_v3.y += 0.5f;
                            if (distance < min_distance && !Physics2D.Linecast(temp_v3, temp_v2, 1 << LayerMask.NameToLayer("Wall"))) {
                                min_distance = distance;
                                target_obj = detect_list[i].transform;
                            }
                        }
                    }
                }
                
                ShootReflection();
                float t = rg_random.Range(0.5f * scout_rate, scout_rate);
                Invoke("EndCycle", t);
            }
        }
    }


    public override void ShootReflection() {
        if (!dead && !dizzy) {
            int r0 = rg_random.Range(0, 10);
            if (r0 < 6 && can_shoot) {
                can_shoot = false;
                anim.SetTrigger("atk");
                Invoke("TurnCanShoot", shoot_cd);
            }
        }
    }

    void Atk() {
        if (!dead)
            hand.SetAttackTrigger();
    }

    //*******************调整角色角度***************************
    public override void FixedRotation() {
        Vector2 tempv2 = new Vector2(0, 0);
        if (target_obj != null)
            tempv2 = (Vector2)target_obj.position - (Vector2)transform.position;
        else
            tempv2 = target_point - (Vector2)transform.position;
        if ((tempv2.x > 0 && facing == -1) || (tempv2.x < 0 && facing == 1)) {
            facing *= -1;
            Vector3 temp_v3 = transform.eulerAngles;
            if (facing < 0)
                temp_v3.y = 180;
            else
                temp_v3.y = 0;
            transform.eulerAngles = temp_v3;
        }
    }

    //********************************重写父类函数**********************************
    public override void ChildDead() {
        awake = false;
        transform.Find("collider").GetComponent<CircleCollider2D>().enabled = false;
        if (dead_obj != null) {
            Invoke("DeadEvent", 1.2f);
        }
    }

    void DeadEvent() {
        GameObject temp_obj1 = Instantiate(dead_obj) as GameObject;
        temp_obj1.GetComponent<OffensiveInterface>().SetSourceObject(gameObject);
        temp_obj1.transform.position = transform.position;
    }

    public override void EndCycle() {
        CancelInvoke("EndCycle");
        move_direction = Vector2.zero;
        if (awake && !dead) {
            Scout();
        }
    }


    public override void Dizzy(float duration, bool isFreeze) {
        if (!dead) {
            dizzy = true;
            Invoke("EndDizzy", duration);
        }
    }
}
