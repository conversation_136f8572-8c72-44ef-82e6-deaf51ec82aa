using System.Collections;
using EnemyGenerator;
using RGScript.Data;
using UnityEngine;

public class EnemyAIChestBattery : R<PERSON>Controller {
	public bool need_tap = true;
	internal int total_chest;
	RGEWeapon smg;
	RGEWeapon se;

	protected override void Awake() {
        base.Awake();
		rigibody = gameObject.GetComponent<Rigidbody2D>();
		anim = transform.GetComponent<Animator>();
		role_attribute = gameObject.GetComponent<RoleAttribute>();
		hand_tf = transform.Find("img/head");
		hand = hand_tf.GetComponent<RGEHand>();
		smg = hand_tf.Find("smg").GetComponent<RGEWeapon>();
		smg.GetComponent<Animator>().speed = 0.666f;
		se = hand_tf.Find("se").GetComponent<RGEWeapon>();
        shadow_lock = transform.Find("shadow_lock").GetComponent<SpriteRenderer>();
	}

	protected override void Start() {
		int levelIndex = (RGGameProcess.Inst.this_index - 1) / 5;
		attribute.max_hp += levelIndex * 15;
		attribute.hp = attribute.max_hp;
		base.Start();
	}

	// Update is called once per frame
	void FixedUpdate() {
		if (awake) {
			if (inertial_vel > 1f && !kinematic) {
				rigibody.velocity = GetVelocity() + force_direction.normalized * inertial_vel;
				inertial_vel *= friction;
			} else {
				if (dead) {
					awake = false;
					rigibody.velocity = new Vector2(0, 0);
					transform.Find("collider").GetComponent<CircleCollider2D>().enabled = false;
					this.enabled = false;
				} else {
					rigibody.velocity = GetVelocity();
				}
			}
		}
	}

	public void Activate(int index) {
		awake = true;
		shadow_lock.gameObject.SetActive(true);
		Scout();
		StartCoroutine(ShottingSMG(index));
	}

	IEnumerator ShottingSMG(int index) {
		var attackDuration = new WaitForSeconds(1.0f);
		int totalCount = GetComponentInParent<EnemyMaker>().SurvivingEnemiesCount;
		var wait = new WaitForSeconds(1.0f + totalCount * 0.25f);
		var speedFactor = 0.8f - totalCount * 0.05f;
		if (PlayerSaveData.Inst.index_difficalty < 3) {
			speedFactor -= (3 - PlayerSaveData.Inst.index_difficalty) * 0.05f;
		}
		smg.GetComponent<Animator>().speed = speedFactor;
		shoot_cd /= (speedFactor + 0.25f);
		yield return new WaitForSeconds(0.5f);
		while (awake && !dead) {
			if (!dizzy) {
				smg.SetAttack(true);
				yield return attackDuration;
				smg.SetAttack(false);
			}
			yield return wait;
		}
	}

	public override void Dead(GameObject sourceObject, bool sync) {
        if (dead) {
            return;
        }

        if (NetControllerManager.Inst.playerCount > 0 && !temp_enemy && !sync) {
            MessageManager.SendDeadMessage(RGGetPath.GetNetId(transform), RGGetPath.GetNetId(sourceObject ? sourceObject.transform : null), transform.position);
        } else {
            dead = true;
            transform.GetComponent<BoxCollider2D>().enabled = false;
            awake = false;
            GetComponentInParent<ItemChestBattery>().GetComponent<Animator>().SetTrigger("dead");
            hand.transform.localRotation = Quaternion.identity;
            ReSetAtk();
            CancelInvoke();
            OnDeadBeforeMakerTriggerHandler();
            if (intensive && transform.Find("intensive")) {
                Destroy(transform.Find("intensive").gameObject);
            }
            if (!string.IsNullOrEmpty(enemy_id)) {
                TaskDatas.data.OnEnemyDead(enemy_id);
                RGGameProcess.Inst.StatisticObtainObject(enemy_id, 1);
                the_maker.GuardDead(this);
            }
            if (destory_on_dead || enemyTagRunTime.HasFlag(emEnemyTagRunTime.ChangeShape))
                Destroy(gameObject, 4f);
            BuffDestroy();
            body_render.material = ResourcesUtil.Load<Material>("RGMaterial/character_sample.mat");
            if (sourceObject != null && sourceObject.GetComponent<RGController>() != null)
                sourceObject.GetComponent<RGController>().KillSomeOne(this);
            OnDeadEvent();
            if (!reborned) {
                DeadBoom(sourceObject);
            }
            GetReward();
            RGMusicManager.GetInstance().PlayEffect(clip_dead);
            ChildDead();
            if (NetControllerManager.Inst.playerCount > 0 && !temp_enemy) {
                MessageManager.SendDeadMessage(RGGetPath.GetNetId(transform), 0, transform.position);
            }
        }
    }

	public override void ChildDead() {
		base.ChildDead();
		smg.SetAttack(false);
	}

	public override void GetHurt(HurtInfo hurtInfo) {
		if (awake && !dead) {
            var source_object = hurtInfo.Source;
			if (source_object == null || source_object.GetComponent<RGController>() == null) {
				if (NetControllerManager.Inst.isClient)
					return;
			} else {
				source_object.GetComponent<RGController>().HurtSomeOne(hurtInfo, gameObject);
				if (!source_object.GetComponent<RGController>().IsLocalPlayer())
					return;
			}
			GetHurtEvent(hurtInfo);
            hurtInfo.Damage = Mathf.Max(BattleData.data.CompareFactor(emBattleFactor.EnemyDefence) ? hurtInfo.Damage - 1 : hurtInfo.Damage, 0);
			role_attribute.hp -= hurtInfo.Damage;
			AfterHurtEvent(source_object);
			body_render.material = RGBaseController.hit_material;
			UICanvas.GetInstance().ShowTextHurt(transform, hurtInfo, 2, transform.parent.parent.name);
			if (role_attribute.hp <= 0) {
				role_attribute.hp = 0;
				friction = 0.95f;
				Dead(source_object, false);
			}
			Invoke("HitBack", 0.05f);
			if (NetControllerManager.Inst.playerCount > 0) {
				MessageManager.SendHurtEnemyMessage(hurtInfo, transform);
			}
		}
	}

	public override void RunReflection() {
		if (target_obj != null) {
			target_point = target_obj.position;
			FixedDirection(5, 8, 5, 15);
			anim.SetBool("run", true);
			CreateSmoke();
			if (rg_random.Range(0, 10) < 7 && can_shoot) {
				if (need_tap)
					anim.SetTrigger("atk");
				Invoke("ShootReflection", 0.5f * scout_rate);
			} else {
				Invoke("EndCycle", scout_rate);
			}
		} else {
			move_direction = new Vector2(rg_random.Range(-1f, 1f), rg_random.Range(-1f, 1f)).normalized;
			anim.SetBool("run", true);
			CreateSmoke();
			Invoke("EndCycle", rg_random.Range(0.5f * scout_rate, scout_rate));
		}
	}

	public override void ShootReflection() {
		if (!dead && !dizzy) {
			can_shoot = false;
			hand.SetAttackTrigger();
			Invoke("TurnCanShoot", shoot_cd);
		}
		EndCycle();
	}

	//*******************调整角色角度***************************
	public override void FixedRotation() {
		Vector2 tempv2 = new Vector2(0, 0);
		if (target_obj != null)
			tempv2 = (Vector2)target_obj.position - (Vector2)transform.position;
		else
			tempv2 = target_point - (Vector2)transform.position;
		if ((tempv2.x > 0 && facing == -1) || (tempv2.x < 0 && facing == 1)) {
			facing *= -1;
			hand.SetWeaponFacing(facing);
			smg.facing = facing;
			Vector3 temp_v3 = transform.eulerAngles;
			if (facing < 0)
				temp_v3.y = 180;
			else
				temp_v3.y = 0;
			transform.eulerAngles = temp_v3;
		}
		fixed_angle = Vector2.Angle((tempv2), new Vector2(facing, 0));
		if (tempv2.y < 0)
			fixed_angle = -fixed_angle;
		if (weapon_lock_target)
			hand_tf.localEulerAngles = new Vector3(0, 0, fixed_angle);
	}

}