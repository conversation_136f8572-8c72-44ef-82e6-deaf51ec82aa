using BattleSystem;
using MapSystem;
using RGScript.Data;
using RGScript.Util.QuickCode;
using System.Collections;
using UnityEngine;

public class BossCtrlStoneDragon : BossController {
    public StoneDragonCtrl dragonCtrl;
    public StoneDragonPriestCtrl priestCtrl;
    
    private RGWeapon _lastHitWeapon;

    protected override void Awake() {
        InitBuffMgr();
        buffMgr.onAddBuffSuccess += OnAddBuffHandler;
        rigibody = gameObject.GetComponent<Rigidbody2D>();
        anim = transform.GetComponent<Animator>();
        role_attribute = gameObject.GetComponent<RoleAttribute>();
        enemyName = name.Replace("(Clone)", "");
        autoTeleportIntoRoom = false;
        canGetForce = false;
        needShowDamage = false;
    }
    
    public override void StartEnemyAI() {
        base.StartEnemyAI();
        anim = priestCtrl.PriestAnimator;
        SkillManager = priestCtrl.SMgr;
    }

    public override void OnGameStateChange(int gameState) {
        if (gameState != 1) {
            return;
        }
    
        if (the_maker == null || the_maker.theRoom.process != ERoomProcess.Exploring) {
            return;
        }
        awake = true;
        if (bodyCol != null) {
            bodyCol.transform.localScale = Vector3.one;
        }
        FixedRotation();
        ResetScoutMask();
        Scout();
        dragonCtrl.StartCruiseMove(emDirection.Up);
    }

    public override void FixedRotation() {
        if (onFixedRotation != null && !onFixedRotation()) return;
        if (!needFixedRotation) {
            return;
        }
        if (angry) {// 祭祀独立行动时需要控制转向
            priestCtrl.FixedRotation(target_point);
        }
    }

    public override void RunReflection() {
        if (!angry || !IsTurnAngry) {// 骑龙飞行不需要RunReflection
            move_direction = Vector2.zero;
            return;
        }
        // 未使用技能或技能不包含跑动逻辑
        if (SkillManager.CurrentSkill == null || !SkillManager.CurrentSkill.Data.NeedMoveCtrl) {
            RunReflection(7, 9, false);
        } else {
            move_direction = SkillManager.CurrentSkill.GetMoveDirection();
        }
        if (rigibody.velocity != Vector2.zero && needCreateSmokeWhenRun) {
            Transform smoke = CreateSmoke();
            smoke.position = priestCtrl.transform.position;
        }

        anim.SetBool(Run, move_direction != Vector2.zero);
    }

    public int DragonTargetSkillID { get; private set; }
    public int PriestTargetSkillID { get; private set; }
    public override void ShootReflection() {
        if (!can_shoot || dead || dizzy) {
            EndCycle();
            return;
        }

        if (!IsTurnAngry && angry) {
            anim.SetBool(Run, false);
            return;
        }

        if (dragonCtrl.SMgr.IsUsingSkill) {
            dragonCtrl.SMgr.StopSkill();
        }
        DragonTargetSkillID = dragonCtrl.SelectSkill();// 决定龙的技能
        PriestTargetSkillID = priestCtrl.SelectSkill();// 决定祭祀的技能
        
        int targetSkillID = DragonTargetSkillID * 10 + PriestTargetSkillID;
        if (targetSkillID == 0) {
            EndCycle();
            return;
        }
        
        can_shoot = false;
        SendPosSyncMessage();
        AttackCommand(targetSkillID);
    }

    public override void StartAttack(int index) {
        DragonTargetSkillID = index / 10;
        PriestTargetSkillID = index % 10;

        needFixedRotation = false;
        dragonCtrl.DoSkill(DragonTargetSkillID);// 执行龙的技能
        priestCtrl.DoSkill(PriestTargetSkillID);// 执行祭祀的技能
        
        needFixedRotation = (dragonCtrl.SMgr.IsUsingSkill && dragonCtrl.SMgr.CurrentSkill.Data.NeedFixedRotation) || 
                            (priestCtrl.SMgr.IsUsingSkill && priestCtrl.SMgr.CurrentSkill.Data.NeedFixedRotation);

        if (index != 0) {
            EndCycle();
        }

        DragonTargetSkillID = 0;
        PriestTargetSkillID = 0;
    }

    protected override IEnumerator SendingPosition() {
        if (temp_enemy || !GameUtil.IsMultiGame() || !NetControllerManager.Inst.isServer || !(attribute.speed > 0)) {
            yield break;
        }

        var wait = new WaitForSeconds(0.05f);
        while (awake || !dead) {
            if (awake) {
                SendPosSyncMessage();
            }
            yield return wait;
        }
    }

    public void SendPosSyncMessage() {
        if (!GameUtil.IsMultiGame() || !NetControllerManager.Inst.isServer) {
            return;
        }
        var tf = angry ? priestCtrl.transform : dragonCtrl.transform;
        Vector2 pos = tf.position;
        Vector2 moveDir = angry
            ? move_direction
            : new Vector2(tf.localScale.x, tf.rotation.eulerAngles.z);
        MessageManager.Inst.SendEnemyPositionMessage(
            RGGetPath.GetNetId(transform), pos, moveDir, force_direction, inertial_vel);
    }

    public override void SyncEnemy(Vector2 position, Vector2 moveDir) {
        if (!awake || dead) {
            return;
        }

        Transform tf;
        if (!angry) {
            tf = dragonCtrl.transform;
            tf.localScale = new Vector3(moveDir.x, Mathf.Abs(moveDir.x));
            tf.localRotation = Quaternion.Euler(new Vector3(0, 0, moveDir.y));
        } else {
            _move_direction = moveDir;
            tf = priestCtrl.transform;
        }
        var oldPos = tf.position;
        var distance = oldPos.x - position.x + oldPos.y - position.y;
        tf.position = distance is > 0.01f and < 20 ? 
            Vector2.Lerp(tf.position, position, 0.2f) : position;
    }

    protected override void BossAngry() {
        if (angry || isDemonstrationCharacter) {
            return;
        }
        base.BossAngry();
        IsTurnAngry = false;
        EndCycle();
        needFixedRotation = false;
        dragonCtrl.Dead();
        buffMgr.ClearAllBuff();
        if (_lastHitWeapon != null && _lastHitWeapon is GunStoneDragonSlayer) {// 石龙炮成就触发
            AchieveInfos.info.CheckUnlock(AchieveInfos.AchievementType.GunStoneDragonSlayer, 0, "", 0);
        }
    }

    public override void GetHurt(HurtInfo hurtInfo) {
        base.GetHurt(hurtInfo);
        _lastHitWeapon = hurtInfo.SourceWeapon;
        if (_lastHitWeapon != null && _lastHitWeapon is GunStoneDragonSlayer) {
            CanGetHurt = true;
        }
    }

    public void OnBossAngryAnimaEndHandler() {
        if (dead) {
            return;
        }
        IsTurnAngry = true;
        needFixedRotation = true;
        SkillManager = priestCtrl.SMgr;
        anim = priestCtrl.PriestAnimator;
        SkillManager = priestCtrl.SMgr;
        rigibody = priestCtrl.PriestRb;
        canGetForce = true;
        autoTeleportIntoRoom = true;
        hurtCd = -1;
        can_shoot = true;
        var roomCtrl = gameObject.GetComponentInAllParents<StoneDragonBossRoomCtrl>();
        if (roomCtrl != null) {
            roomCtrl.SetCameraViewSize(StoneDragonBossRoomCtrl.CameraSize.Reset);
        }
        
        EndCycle();
    }

    public void OnAddBuffHandler(RGBuff buff) {
        if (angry) {
            return;
        }
        buff.transform.localScale = Vector3.one * 0.25f;
        int sameBuffCount = buffMgr.GetSameNameBuffNum(buff.name);
        if (sameBuffCount >= 2) {
            buffMgr.RemoveBuff(buff);
            buff.BuffEnd();
        }
    }

    public override void Dead(GameObject sourceObject, bool sync) {
        if (!dragonCtrl.IsDead) {
            dragonCtrl.Dead();
        }
        priestCtrl.Dead();
        base.Dead(sourceObject, sync);
    }

    protected override void InitSkills() { }
}
