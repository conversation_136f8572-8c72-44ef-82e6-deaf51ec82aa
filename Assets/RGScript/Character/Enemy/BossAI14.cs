using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 圣诞树人
/// </summary>
public class BossAI14 : RGEBossController {

    public GameObject bullet01;
    public GameObject bullet02;
    public GameObject bullet03;
    public GameObject bullet04;
    public GameObject bullet05;
    public GameObject bullet06;

    Transform h1;
    Transform h2;
    Transform h3;
    Transform energy_ball;
    Transform star;
    Transform body;

    bool root = true;

    protected override void Awake(){
        base.Awake();
        rigibody = gameObject.GetComponent<Rigidbody2D>();
        anim = transform.GetComponent<Animator>();
        role_attribute = gameObject.GetComponent<RoleAttribute>();
        shadow_lock = transform.Find("shadow_lock").GetComponent<SpriteRenderer>();
        h1 = transform.Find("img/h1");
        h2 = transform.Find("img/h2");
        h3 = transform.Find("img/h3");
        energy_ball = transform.Find("img/energy_ball");
        star = transform.Find("img/star");
        body = transform.Find("img/body");
    }

    protected override void FixedUpdate() {
        InnerFixedUpdate();
        if (awake) {
            if (!shooting) {
                if (dead) {
                    awake = false;
                    rigibody.velocity = new Vector2(0, 0);
                    transform.Find("collider").GetComponent<CircleCollider2D>().enabled = false;
                } else {
                    if (inertial_vel > 1f) {
                        rigibody.velocity = GetVelocity() + force_direction.normalized * inertial_vel;
                        inertial_vel *= friction;
                    } else {
                        Vector2 tempv2 = GetVelocity();
                        //tempv2.x = tempv2.x * 3;
                        rigibody.velocity = tempv2;
                    }
                }
            }
            if (!dead) {
                FixedRotation();
            }
        }
    }
    public override void RunReflection() {
        if (isDemonstrationCharacter) {
            return;
        }
        
        if (root) {
            move_direction = Vector2.zero;
            float t = rg_random.Range(0.5f * scout_rate, scout_rate);
            Invoke("ShootReflection", t);
        } else {
            RunReflection(7, 9);
        }
    }

    public override void ShootReflection() {
        if (can_shoot && !dead && !dizzy) {
            int r1 = rg_random.Range(0, 100);
            if (angry) {
                if (r1 < 33) {
                    AttackCommand(1);
                } else if (r1 < 66) {
                    AttackCommand(2);
                } else {
                    AttackCommand(3);
                }
            } else {
                if (r1 < 33) {
                    AttackCommand(4);
                } else if (r1 < 66) {
                    AttackCommand(5);
                } else {
                    AttackCommand(6);
                }
            }
        } else {
            EndCycle();
        }
    }
    public override void StartAttack(int index) {
        base.StartAttack(index);
        if (index == 1) {
            StartAtk01();
        } else if (index == 2) {
            StartAtk02();
        } else if (index == 3) {
            StartAtk03();
        } else if (index == 4) {
            StartAtk04();
        } else if (index == 5) {
            StartAtk05();
        } else if (index == 6) {
            StartAtk06();
        }
        can_shoot = false;
        Invoke("TurnCanShoot", shoot_cd);
    }

    //*******************调整角色角度***************************
    public override void FixedRotation() {
        Vector2 tempv2 = new Vector2(0, 0);
        if (target_obj != null)
            tempv2 = (Vector2)target_obj.position - (Vector2)transform.position;
        else
            tempv2 = target_point - (Vector2)transform.position;
        if ((tempv2.x > 0 && facing == -1) || (tempv2.x < 0 && facing == 1)) {
            if (!shooting) {
                facing *= -1;
                Vector3 temp_v3 = transform.eulerAngles;
                if (facing < 0)
                    temp_v3.y = 180;
                else
                    temp_v3.y = 0;
                transform.eulerAngles = temp_v3;
            }
        }
        fixed_angle = Vector2.Angle((tempv2), new Vector2(facing, 0));
        if (tempv2.y < 0)
            fixed_angle = -fixed_angle;
    }

    //********************************重写父类函数**********************************
    public override void GetHurt(HurtInfo hurtInfo) {
        if (awake && !dead && !inAtk03) {
            base.GetHurt(hurtInfo);
            if (1.0f * role_attribute.hp / role_attribute.max_hp < 0.5f && !angry) {
                BossAngry();
            }
            if (boss_info) {
                if (boss_info) {
                    boss_info.UpdateBossHp();
                }
            }
        }
    }
    public override void Dead(GameObject source_object, bool sync) {
        base.Dead(source_object, sync);
        if (dead) {
            anim.SetBool("dead_b", true);
        }
    }

    public override void Reborn() {
        base.Reborn();
        angry = true;
        anim.SetBool("angry", true);
    }

    public override void TurnPlayer() {
        base.TurnPlayer();
        transform.Find("img/dash_big").GetComponent<RGDash>().ResetCamp();
    }

    void StartAtk01() {
        anim.SetTrigger("atk_1");
        Vector2 temp_v2 = ((Vector2)target_point - (Vector2)transform.position).normalized;
        rigibody.velocity = temp_v2 * 10f;
    }
    public virtual void InAtk01() {
        InAtk04(1);
        for (int i = -8; i < 8; i++) {
            GameObject temp_obj = Instantiate(bullet01) as GameObject;
            //更新武器属性到子弹里
            temp_obj.GetComponent<RGBullet>().UpdateAttribute(gameObject, 3, 10, false, 3, camp);
            temp_obj.transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;
            temp_obj.transform.position = h2.position;
            temp_obj.transform.eulerAngles = new Vector3(0, 0, i * 22.5f);
        }
        RGMusicManager.GetInstance().PlayEffect(boss_clip[0]);
        EndCycle();
    }
    /// <summary>
    /// 星星攻击
    /// </summary>
    void StartAtk02() {
        move_direction = Vector2.zero;
        RGMusicManager.GetInstance().PlayEffect(boss_clip[1]);
        anim.SetTrigger("atk_2");
    }
    bool bulletStay;
    public virtual void InAtk02() {
        GameObject temp_obj = Instantiate(bullet02) as GameObject;
        temp_obj.transform.SetParent(RGGameSceneManager.GetInstance().temp_objects_parent);
        //更新武器属性到子弹里
        temp_obj.transform.position = star.position;
        bulletStay = true;
        StartCoroutine(Atk02BulletTimer());
        StartCoroutine(Atk02BulletMove(temp_obj.transform));
    }
    IEnumerator Atk02BulletTimer() {
        yield return new WaitForSeconds(0.5f);
        bulletStay = false;
    }
    IEnumerator Atk02BulletMove(Transform the_bullet) {
        while (bulletStay && the_bullet) {
            the_bullet.position = star.position;
            yield return null;
        }
        if (the_bullet) {
            for (int i = 0; i < the_bullet.childCount; i++) {
                Transform temp_obj = the_bullet.GetChild(i);
                temp_obj.GetComponent<RGBullet>().UpdateAttribute(gameObject, 3, 6.8f, false, 3, camp);
                temp_obj.GetComponent<RGBullet>().SetAwakeTrue();
            }
        }
        EndCycle();
    }
    bool inAtk03;
    void StartAtk03() {
        move_direction = Vector2.zero;
        anim.SetBool("atk_3", true);
    }
    public virtual void InAtk03() {
        RGMusicManager.GetInstance().PlayEffect(boss_clip[2]);
        Vector2 temp_v2 = ((Vector2)target_point - (Vector2)transform.position).normalized;
        rigibody.velocity = Vector2.zero;
        GetForce(temp_v2, 17 * Mathf.Min(1, (1 + attribute.FinalSpeedRate)));
        friction = 1f;
        inAtk03 = true;
    }
    void EndAtk03() {
        anim.SetBool("atk_3", false);
        inAtk03 = false;
        move_direction = Vector3.zero;
        friction = 0f;
        EndCycle();
    }
    void StartAtk04() {
        anim.SetTrigger("atk_4");
    }
    public virtual void InAtk04(int count) {
        if (count > 0) {
            var angle = 360 / count;
            for (int i = 0; i < count; i++) {
                GameObject temp_obj = Instantiate(bullet04) as GameObject;
                //更新武器属性到子弹里
                temp_obj.GetComponent<RGBullet>().UpdateAttribute(gameObject, 6, 12, true, 3, camp);
                temp_obj.transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;
                temp_obj.transform.position = transform.position;
                if (facing > 0)
                    temp_obj.transform.eulerAngles = new Vector3(0, 0, fixed_angle + angle * i);
                else
                    temp_obj.transform.eulerAngles = new Vector3(0, 0, 180 - fixed_angle + angle * i);
            }
            RGMusicManager.GetInstance().PlayEffect(boss_clip[3]);
        }
        EndCycle();
    }

    void StartAtk05() {
        anim.SetTrigger("atk_5");
        RGMusicManager.GetInstance().PlayEffect(boss_clip[4]);
    }
    public virtual void InAtk05() {
        RGMusicManager.GetInstance().PlayEffect(boss_clip[5]);

        GameObject temp_obj = Instantiate(bullet05) as GameObject;
        //更新武器属性到子弹里
        temp_obj.GetComponent<RGBullet>().UpdateAttribute(gameObject, 5, 8, true, 3, camp);
        temp_obj.transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;
        temp_obj.transform.position = energy_ball.position;

        var angle = fixed_angle;
        if (target_obj != null) {
            var tempv2 = (Vector2)target_obj.position - (Vector2)energy_ball.position;
            angle = Vector2.Angle((tempv2), new Vector2(facing, 0));
            if (tempv2.y < 0)
                angle = -angle;
        }
        if (facing > 0)
            temp_obj.transform.eulerAngles = new Vector3(0, 0, angle);
        else
            temp_obj.transform.eulerAngles = new Vector3(0, 0, 180 - angle);

        EndCycle();
    }
    /// <summary>
    /// 持续放波
    /// </summary>
    void StartAtk06() {
        anim.SetTrigger("atk_6");
        RGMusicManager.GetInstance().PlayEffect(boss_clip[4]);
    }
    public virtual void InAtk06() {
        StartCoroutine(Attacking06());
    }
    IEnumerator Attacking06() {
        int offset = 0;
        int offset_change = rg_random.Range(0, 100) < 50 ? 11 : -11;
        for (int i = 0; i < 5; i++) {
            offset += offset_change;
            if (!dizzy) {
                for (int j = 0; j < 12; j++) {
                    GameObject temp_obj = Instantiate(bullet06) as GameObject;
                    //更新武器属性到子弹里
                    temp_obj.GetComponent<RGBullet>().UpdateAttribute(gameObject, 3, 8f, false, 4, camp);
                    temp_obj.transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;
                    temp_obj.transform.position = transform.position + new Vector3(0, 1f, 0);
                    if (facing > 0)
                        temp_obj.transform.eulerAngles = new Vector3(0, 0, offset + j * 30);
                    else
                        temp_obj.transform.eulerAngles = new Vector3(0, 0, 180 - offset + j * 30);
                }
                RGMusicManager.GetInstance().PlayEffect(boss_clip[5]);
            }
            yield return new WaitForSeconds(0.225f);
        }
        EndCycle();
    }

    public override void Dizzy(float value1, bool isFreeze) {
        if (!dead) {
            dizzy = true;
            anim.SetBool("run", false);
            move_direction = Vector2.zero;
            inertial_vel = 0;
            rigibody.velocity = Vector2.zero;
            Invoke("EndDizzy", value1);
        }
    }

    void ShowMiddleFoot() {
        h2.GetComponent<SpriteRenderer>().sortingOrder = 5;
        rigibody.isKinematic = false;
        root = false;
        EndCycle();
    }

    public override bool GetForce(Vector2 value1, float value2, float limit = 30, bool ignoreDizzy = false) {
        if (!inAtk03 && !root) {
            return base.GetForce(value1, value2, limit, ignoreDizzy);
        }

        return false;
    }

    private float GetAngle(Transform trans) {
        var angle = fixed_angle;
        if (target_obj != null) {
            var tempv2 = (Vector2)target_obj.position - (Vector2)trans.position;
            angle = Vector2.Angle((tempv2), new Vector2(facing, 0));
            if (tempv2.y < 0)
                angle = -angle;
        }
        return angle;
    }

    public override void CreateHpBar(Vector3 offset = default) {
        base.CreateHpBar(offset);
        BossAngry();
    }

}
