using UnityEngine;

/// <summary>
/// 炮台
/// </summary>
public class EnemyAI01 : RGEController {
    //是否需要攻击前叹号提示
    public bool need_tap = true;
    public float hand2AtkInterval = 0.1f;
    RGEHand hand2;
    Transform hand2_tf;

    protected static readonly int Atk = Animator.StringToHash("atk");

    protected override void Awake() {
        base.Awake();
        rigibody = gameObject.GetComponent<Rigidbody2D>();
        anim = transform.GetComponent<Animator>();
        role_attribute = gameObject.GetComponent<RoleAttribute>();
        hand_tf = transform.Find("img/h1");
        hand = hand_tf.GetComponent<RGEHand>();
        hand2_tf = transform.Find("img/h2");
        if (null != hand2_tf) {
            hand2 = hand2_tf.GetComponent<RGEHand>();
        }
        shadow_lock = transform.Find("shadow_lock").GetComponent<SpriteRenderer>();
    }

    protected override void FixedUpdate() {
        InnerFixedUpdate();
        if (awake) {
            if (inertial_vel > 1f && !kinematic) {
                var v = GetVelocity();
                rigibody.velocity = v + force_direction.normalized * inertial_vel;
                inertial_vel *= friction;
            } else {
                if (dead) {
                    rigibody.velocity = Vector2.zero;
                    if (!bodyCol.enabled) {
                        awake = false;
                        enabled = false;
                    } else {
                        bodyCol.enabled = false;
                    }
                } else {
                    var v = GetVelocity();
                    rigibody.velocity = v;
                }
            }
        }
    }

    protected virtual void DoAttack() {
        if (need_tap) {
            anim.SetTrigger(Atk);
        }

        Invoke(nameof(ShootReflection), 0.5f * scout_rate);
    }

    [Range(0, 10)] public int attackProbability = 7;
    public override void RunReflection() {
        if (target_obj != null) {
            target_point = target_obj.position;
            if (can_move) {
                FixedDirection(5, 8, (!defaultTarget ? 5 : 0), (!defaultTarget ? 5 : 0) + 10);
                anim.SetBool("run", true);
                CreateSmoke();
            }

            if (rg_random.Range(0, 10) < attackProbability && can_shoot) {
                DoAttack();
            } else {
                Invoke("EndCycle", scout_rate);
            }
        } else {
            move_direction = new Vector2(rg_random.Range(-1f, 1f), rg_random.Range(-1f, 1f)).normalized;
            anim.SetBool("run", true);
            CreateSmoke();
            Invoke("EndCycle", rg_random.Range(0.5f * scout_rate, scout_rate));
        }
    }

    public override void ShootReflection() {
        if (!dead && !dizzy && !shooting && can_shoot) {
            can_shoot = false;
            hand.SetAttackTrigger();
            // Hand2Atk();
            Invoke("Hand2Atk", hand2AtkInterval);
            Invoke("TurnCanShoot", shoot_cd);
        }
        EndCycle();
    }
    void Hand2Atk() {
        if (null != hand2) {
            hand2.SetAttackTrigger();
        }
    }

    //*******************调整角色角度***************************
    public override void FixedRotation() {
        Vector2 tempv2 = new Vector2(0, 0);
        if (target_obj != null) {
            tempv2 = (Vector2)target_obj.position - (Vector2)transform.position;
        } else {
            tempv2 = target_point - (Vector2)transform.position;
        }
        if (!shooting) {
            if ((tempv2.x > 0 && facing == -1) || (tempv2.x < 0 && facing == 1)) {
                facing *= -1;
                hand.SetWeaponFacing(facing);
                if (null != hand2) {
                    hand2.SetWeaponFacing(facing);
                }
                Vector3 temp_v3 = transform.eulerAngles;
                if (facing < 0) {
                    temp_v3.y = 180;
                } else {
                    temp_v3.y = 0;
                }
                transform.eulerAngles = temp_v3;
            }
        }
        fixed_angle = Vector2.Angle((tempv2), new Vector2(facing, 0));
        if (tempv2.y < 0) {
            fixed_angle = -fixed_angle;
        }
        if (weapon_lock_target) {
            hand_tf.localEulerAngles = new Vector3(0, 0, fixed_angle);
            if (null != hand2_tf) {
                hand2_tf.localEulerAngles = new Vector3(0, 0, fixed_angle);
            }
        }
    }
}