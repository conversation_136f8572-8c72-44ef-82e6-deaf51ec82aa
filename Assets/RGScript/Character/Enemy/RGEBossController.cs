using Activities.BugFeatures.Scripts;
using System;
using System.Collections;
using CustomFactor;
using DG.Tweening;
using EnemyGenerator;
using GameStatisticSystem;
using MapSystem;
using ModeLoopTravel.Scripts;
using RGScript.Data;
using RGScript.Item;
using RGScript.Map;
using RGScript.Util.QuickCode;
using Sirenix.OdinInspector;
using SoulKnight.Runtime.Config2Code.Config;
using UnityEngine;

public class RGEBossController : RGEController {
    [FoldoutGroup("Base")] public BulletInfoInspector[] bullets;
    [FoldoutGroup("Audio")] public AudioClip[] boss_clip;
    [FoldoutGroup("Audio")] public AudioClip angry_clip;
    [FoldoutGroup("Base")] public bool createChestAnyway;
    [FoldoutGroup("Base")] public Vector2 doubleboss_offset = new Vector2(2f, 0f);
    [FoldoutGroup("Stats")] public int powerupProbability = 5;
    internal bool angry;
    internal BossInfo boss_info;
    internal int boss_index; //只有单个boss为0. 有两个boss时主boss为1, 副boss为2
    protected GameObject Gate;
    public GameObject GateObject => Gate;
    protected GameObject Chest;
    protected GameObject WeaponEvolutionChest;
    protected const string GatePathStr = "RGPrefab/LevelObject/Item/transfer_gate.prefab";
    public const float BossChestOffsetY = -4;

    public override bool isBoss => true;

    public event Action<GameObject> onInstantiateGate;
    public event Action onShowGate;
    public event Action onAngry;
    public Action<Action> angryHook;
    internal bool NeedGate => boss_index <= 1 && !isDemonstrationCharacter;

    public Action onInitFinish;

    private static GameStatisticManager StatisticManager =>GameStatisticManager.Instance;
    public string weaponEvolutionFragmentName;
    private bool _weaponEvolutionEnhanced;

    protected override void Start() {
        SimpleEventManager.Raise(new BeforeEnemyStart {
            enemy = this
        });
        
        _Init();
        
        SetUpAttribution();
        ShowBossInfo();
        CreatePortalGate();
        onInitFinish?.Invoke();
        
        SimpleEventManager.Raise(new AfterEnemyStart {
            enemy = this
        });
    }

    protected override void FixedUpdate() {
        if (useAIController && AIController != null) {
            AIController.AIControllerFixedUpdate();
            AIController.GetForce(additionVelocity);
            additionVelocity = Vector2.zero;
            return;
        }

        InnerFixedUpdate();
        if (!awake) {
            return;
        }

        if (!shooting) {
            if (rigibody) {
                if (dead) { //是否死亡
                    rigibody.velocity = Vector2.zero;
                    if (bodyCol) {
                        if (!bodyCol.enabled) {
                            awake = false;
                        } else {
                            bodyCol.enabled = false;
                        }
                    }
                } else {
                    if (rigibody.bodyType != RigidbodyType2D.Static) {
                        UpdateVelocity();
                    }
                }
            }
        }
        if (!dead) {
            FixedRotation();
        }
    }

    protected void UpdateVelocity(){
        rigibody.velocity = GetVelocity();
    }

    public override void SetUpAttribution() {
        InitBuffMgr();
        
        onEnemyDead += _ => { // 死亡后修改渲染层级
            if (!transform.Find("img")) {
                return;
            }

            var sortingGroup = transform.Find("img").GetComponent<UnityEngine.Rendering.SortingGroup>();
            if (!sortingGroup) {
                return;
            }

            sortingGroup.sortingLayerName = "Wall";
            sortingGroup.sortingOrder = -1;
        };

        if (!temp_enemy) {
            the_maker = GetComponentInParent<EnemyMaker>();
            AddDelegate();
        }

        onEnemyDead += controller => {
            var bossDeadEvent = new BossDeadEvent {
                enemeyId = controller.enemy_id,
                rgeController = controller
            };
            SimpleEventManager.Raise(bossDeadEvent);
            if (BattleData.data != null && BattleData.data.killedBossSet != null) {
                BattleData.data.killedBossSet.Add(controller.enemy_id);
            }
        };
        GenNetId();

        StaticCustomFactorManager.EventDispatch("EnemySetUpAttribution", this);

        if (BattleData.data.CompareFactor(emBattleFactor.EnemyDoubleHp)) {
            role_attribute.max_hp *= 2;
            if (reward_value.Length > 3) {
                reward_value[3] *= 2;
            }
        }
        if (BattleData.data.CompareFactor(emBattleFactor.DoubleBoss)) {
            role_attribute.max_hp = (int)(role_attribute.max_hp * 0.75f);
            shoot_cd *= 1.33f;
        }
        if (BattleData.data.CompareFactor(emBattleFactor.FastEnemy)) {
            role_attribute.speed *= 1.5f;
            shoot_cd *= 0.75f; 
        }
        if (BattleData.data.CompareFactor(emBattleFactor.AggressiveEnemy)) {
            ai_level = 0;
        }
        if (BattleData.data.CompareFactor(emBattleFactor.Loop)) {//因子:无尽
            role_attribute.max_hp = (int)FormulaUtil.CalcMaxHp(role_attribute.max_hp, MapManager.Instance.PressureLv);
        }
        if (BattleData.data.CompareActivityFactor(emActivityFactor.BossAngry)) {
            // 活动特性因子：领主暴君
            shoot_cd *= 0.75f; 
            role_attribute.max_hp = (int)(role_attribute.max_hp * 2f);
            role_attribute.SpeedRate += 0.5f;
            transform.localScale *= 2f;
        }
        if (BattleData.data.IsTroop) {//因子:佣兵团
            ai_level = 0;
            role_attribute.max_hp = (int)(role_attribute.max_hp * BattleData.data.troopMode.hpFactor);
            buff_immune *= 0.5f;
        }
        if (BattleData.data.IsBossRushMode || BattleData.data.isBadass
            || BattleData.data.CompareFactor(emBattleFactor.ExEnemy)
            //|| BattleData.data.CompareFactor(emBattleFactor.Troop)
            || rg_random.Range(0, 100) < powerupProbability && !isDemonstrationCharacter) { //精英Boss
            GrowIntensive(powerupProbability > 0);
        } else if (NetControllerManager.Inst.playerCount == 0) {
            shoot_cd = shoot_cd * (2 - PlayerSaveData.Inst.difficulty / 5.0f);
            role_attribute.max_hp = (int)(role_attribute.max_hp * (1f + 0.2f * PlayerSaveData.Inst.index_difficalty / 10.0f)); //累积难度影响
            role_attribute.max_hp = (int)(role_attribute.max_hp * (0.6f + 0.4f * PlayerSaveData.Inst.difficulty / 5.0f)); //动态难度影响
            role_attribute.hp = role_attribute.max_hp;
        }
        if (NetControllerManager.Inst.playerCount > 0) {
            role_attribute.max_hp = (int)(role_attribute.max_hp * NetControllerManager.Inst.GetBossHpFactor());
            role_attribute.hp = role_attribute.max_hp;
            shoot_cd *= NetControllerManager.Inst.GetShootSpeedFactor();
        }
        attributeProxy.max_hp = role_attribute.max_hp;

        OnSetupAttribute();
        if (autoStartAI) {
            StartEnemyAI();
        }
        StartCoroutine(CheckPositionValid());
    }

    /// <summary>
    /// 精英化
    /// </summary>
    bool _alreadyIntensive;
    public virtual void GrowIntensive(bool resetScale) {
        if (_alreadyIntensive) {
            return;
        }

        if (resetScale) {
            /*
                    玩家报告Bug说吊炸天巨型史莱姆（boss20)作为第二个BOSS出现时，体型特别大。
                    原因是巨型史莱姆的动画状态机在GrowIntensive之前激活，其死亡动作会锁定img的scale(prefab中的值是0.9)，于是巨型史莱姆作为主BOSS不管是否精英化都不会变大；
                    但是作为第二BOSS出现时，GrowIntensive会在动画状态机激活之前执行，这时修改scale会生效，导致（精英化）巨型史莱姆比作为主BOSS时更大。
                    为了维持史莱姆不变大（玩家一直以来的印象），这里做了一个特殊处理，如果是巨型史莱姆，不修改scale。
                */
            if (transform.name != "boss20" && transform.Find("img") != null) { 
                transform.Find("img").localScale = new Vector3(1.5f, 1.5f, 1);
            }
            //transform.Find("shadow_lock").localScale = new Vector3(2f, 2f, 1);
        }
        if (BattleData.data.IsBossRushMode && BattleData.data.isBadass) {
            role_attribute.max_hp = (int)(role_attribute.max_hp * 1.8f);
        } else {
            role_attribute.max_hp = (int)(role_attribute.max_hp * 1.5f);
        }
        if (BattleData.data.IsBossRushMode && BattleData.data.isBadass) {
            shoot_cd *= 0.4f;
        } else {
            shoot_cd *= 0.5f;
        }
        role_attribute.hp = role_attribute.max_hp;
        if (boss_info) {
            boss_info.UpdateBossMaxHp();
        }
        intensive = true;
        _alreadyIntensive = true;
    }

    public void BossEvolutionWeaponEnhance() {
        if (_weaponEvolutionEnhanced) {
            return;
        }

        shoot_cd *= 0.77f; 
        role_attribute.max_hp = (int)(role_attribute.max_hp * 1.3f);
        role_attribute.hp = role_attribute.max_hp;
        attributeProxy.max_hp = role_attribute.max_hp;
        role_attribute.SpeedRate += 0.3f;
        transform.localScale *= 1.3f;
        _weaponEvolutionEnhanced = true;
            
        if (boss_info) {
            boss_info.UpdateBossMaxHp();
        }
    }
    
    public virtual void ShowBossInfo() {
        if (isDemonstrationCharacter || AutoShowInfo || !bossInfoEnable) {
            return;
        }

        if (boss_info) {
            boss_info.RegisterBoss(this);
            return;
        }
        if (boss_index <= 1) {
            CreateBossInfo();
        } else {
            StartCoroutine(DetectingBossInfo());
        }
    }

    protected virtual void CreateBossInfo() {
        GameObject tempObj = BossInfo.InstantiateBossInfo(enemyName);
        if (tempObj == null) {
            return;
        }
        tempObj!.name = "boss_info";
        tempObj.GetComponent<RectTransform>().localPosition = new Vector3(0, 0, 0);
        tempObj.GetComponent<RectTransform>().localScale = new Vector3(1, 1, 1);
        boss_info = tempObj.GetComponent<BossInfo>();
        boss_info.RegisterBoss(this);
        tempObj.SetActive(false); // 防止出场动画实例化后自动开始
    }

    /// <summary>
    /// 绑定已存在的BossInfo
    /// </summary>
    /// <returns></returns>
    protected IEnumerator DetectingBossInfo() {
        while (!boss_info) {
            var trInfo = UICanvas.GetInstance().transform.Find("boss_info");
            if (null != trInfo) {
                boss_info = trInfo.GetComponent<BossInfo>();
                if (boss_info) {
                    boss_info.RegisterBoss(this);
                    boss_info.NextInfoName = enemy_id + "_info";
                }
            }
            yield return null;
        }
    }

    [FoldoutGroup("Base")] public bool bossInfoEnable = true;
    [NonSerialized] public bool AutoShowInfo;
    [NonSerialized] public bool PlayFightBGM = true;

    public Action onShowBossInfo;

    public override void StartEnemyAI() {
        if (useAIController) {
            InitAIController();
        }

        if (bossInfoEnable && AutoShowInfo) {
            AutoShowInfo = false;
            CreateBossInfo();
            if (boss_info != null) {
                boss_info.gameObject.SetActive(true);
                boss_info.ShowBossInfo(PlayFightBGM);
                onShowBossInfo?.Invoke();
            }
        }

        detect_list = new RaycastHit2D[8];
        ResetScoutMask();
        if (!temp_enemy) {
            if ((the_maker && the_maker.theRoom.process == ERoomProcess.Exploring) || start_awake) {
                awake = true;
            }
        }
        if (awake) {
            Scout();
        }
        if (!rigibody) {
            rigibody = GetComponent<Rigidbody2D>();
        }
        if (!GameUtil.IsDefenceMode() && rigibody && !rigibody.isKinematic && !awake) {
            rigibody.isKinematic = true;
        }

        if (useAIController) {
            return;
        }
        
        StopCoroutine(nameof(SendingPosition));
        StartCoroutine(nameof(SendingPosition));
    }

    public override void UpdateHpBar(float hp, float maxHp) {
        base.UpdateHpBar(hp, maxHp);
        if (boss_info) {
            boss_info.UpdateBossHp();
        }
    }

    public override void GetHurt(HurtInfo hurtInfo) {
        if (!awake || dead) {
            return;
        }

        if (hurtInfo.Damage == role_attribute.hp && enemy_id == "boss29") {
            hurtInfo.Damage += 1;
        }

        base.GetHurt(hurtInfo);

        UpdateHpBar();

        // 不要用dead判断，从机的话，dead不为true
        if (attribute.hp <= 0) {
            if (BattleData.data.gameMode == emGameMode.Normal) {
                var killerW = hurtInfo.SourceWeapon;
                var killerSource = hurtInfo.Source;
                if (killerW != null && killerSource != null && killerSource.GetComponent<RGController>() is { } rgCtrl) {
                    var initW = DataUtil.GetInitWeaponName((emHero)rgCtrl.attribute.c_index, rgCtrl.GetSkinIndex());
                    if (killerW.name == initW) {
                        StatisticData.data.AddEventCount(RGGameConst.INIT_WEAPON_KILL_BOSS, 1, false);
                    }
                }

                if (hurtInfo.FingerPrint != null && hurtInfo.FingerPrint.damageCarrier &&
                    hurtInfo.FingerPrint.damageCarrier.HasDamageType(emDamageType.HandCut)) {
                    StatisticData.data.AddEventCount(RGGameConst.HAND_CUT_KILL_BOSS, 1, false);
                }
            }

            return;
        }

        if (!angry && role_attribute.hp / (float)role_attribute.max_hp < 0.5f) {
            BossAngry();
        }
    }

    public override void SyncGetHurt(HurtInfo hurtInfo) {
        base.SyncGetHurt(hurtInfo);
        if (!angry && role_attribute.hp / (float)role_attribute.max_hp < 0.5f) {
            BossAngry();
        }
        if (boss_info) {
            boss_info.UpdateBossHp();
        }
    }

#if UNITY_EDITOR
    [FoldoutGroup("Debug")]
    [Button("测试狂暴", ButtonSizes.Medium)]
    public void DebugAngry() {
        angry = false;
        BossAngry();
    }
#endif

    protected virtual void BossAngry() {
        if (isDemonstrationCharacter) {
            return;
        }

        if (angry) {
            return;
        }

        angry = true;
        shoot_cd = shoot_cd / 2;
        buff_immune = 0.2f * NetControllerManager.Inst.GetBuffImmune()
                           * (1f / (MapManager.LoopCount + 1));
        if (BattleData.data.CompareFactor(emBattleFactor.EnemyBuffImmune)) {
            buff_immune *= 0.5f;
        }
        ai_level = 0;

        Action angryTodo = () => {
            if (anim) {
                anim.speed = 1.2f;
                anim.SetBool("angry", true);
                anim.SetTrigger("angry_t");
            }

            RGMusicManager.GetInstance().PlayEffect(angry_clip);
            if (!isPlayer) {
                DropEnergy(GetDropCoinNum());
            }
            onAngry?.Invoke();
        };

        if (angryHook != null) {
            angryHook(angryTodo);
        } else {
            angryTodo();
        }
    }

    public void DropEnergy(int count) {
        for (int i = 0; i < count; i++) {
            GameObject temp_obj = Instantiate(PrefabManager.GetPrefab(PrefabName.energy), transform.position,
                Quaternion.identity);
            float rx = rg_random.Range(-1f, 1f);
            float ry = rg_random.Range(-1f, 1f);
            int r3 = rg_random.Range(5, 15);
            temp_obj.GetComponent<RGCoin>().GetForce(new Vector2(rx, ry), r3);
            temp_obj.transform.position = transform.position;
            temp_obj.transform.position += (Vector3)dropOffset;
        }
    }

    public override void EatenBy(RGBaseController eater) {
        GetHurt(new HurtInfo {
            Damage = 15,
            DamageType = DamageType.Melee,
            Source = eater.gameObject,
            FingerPrint = DamageCarrier.DefaultFingerPrint
        });
    }

    public override void ChildDead() {
        base.ChildDead();
        if (!BattleData.data.IsTroop || reborned) {
            return;
        }  
        //掉落猫币
        for (int i = 0; i < 3; i++) {
            float rx = rg_random.Range(-1f, 1f);
            float ry = rg_random.Range(-1f, 1f);
            int r3 = rg_random.Range(5, 15);
            GameObject tempObj = Instantiate(PrefabManager.GetPrefab(PrefabName.coin_cat), transform.position, Quaternion.identity);
            tempObj.GetComponent<RGCoin>().GetForce(new Vector2(rx, ry), r3);
        }
    }

    /// <summary>
    /// boss掉落的金币数
    /// </summary>
    /// <returns></returns>
    public virtual int GetDropCoinNum() {
        if (reward_value == null || reward_value.Length <= 3) {
            return 0;
        }

        if (BattleData.data.IsBossRushMode) {
            return reward_value[3] * 2;
        }
        return reward_value[3];
    }

    public virtual void OnBossRoomClear() {
        // Invoke("ShowTransferGate", 2f);
        Timer.Register(2f, false, true, ShowTransferGate);
        if (boss_index <= 1) {
            RGMusicManager.GetInstance().StopBgm();
            foreach (var enemy in transform.parent.GetComponentsInChildren<RGEController>()) {
                if (!(enemy is RGEBossController) && !enemy.dead) {
                    enemy.Dead(null, false);
                }
            }
        }

        if (GameUtil.IsMultiGame()) {
            AchieveInfos.info.CheckUnlock(AchieveInfos.AchievementType.MultiGameBossKiller, 0, null,
                emHero.None,
                StatisticManager.GetStatisticData<EnemyGameStatisticData>().GetBossKilledOnMultiGameTimes("Any#Any") >=
                20);
        }
    }

    protected virtual void CreatePortalGate() {
        if (BattleData.data.IsARAM || isDemonstrationCharacter) {
            return;
        }
        
        CreateBossChest();
        CreateBossEvolutionWeaponChest();
        if (!NeedGate) {
            return;
        }

        if (GameUtil.InFirstTutorial()) {
            return;
        }

        if (GameUtil.InReturnPlayerIntro()) {
            return;
        }

        if (enemy_id == "boss_void") {
            return;
        }

        if (gameObject.name.Contains("boss_bossrush")) {
            Gate = Instantiate(ResourcesUtil.Load<GameObject>(GatePathStr),
                the_maker ? the_maker.theRoom.transform : transform.parent);
        } else {
            Gate = Instantiate(ResourcesUtil.Load<GameObject>(GatePathStr), transform.parent);
        }

        Gate.transform.localPosition = new Vector3(0, 0, 0);
        Gate.SetActive(false);
        var gateCtrl = Gate.GetComponent<RGTransferGate>();
        gateCtrl.SetCreator(enemy_id);
        onInstantiateGate?.Invoke(Gate);
    }

    protected virtual void CreateBossChest() {
        if (isDemonstrationCharacter) {
            return;
        }

        if (GameUtil.InFirstTutorial()) {
            return;
        }

        if (GameUtil.InReturnPlayerIntro()) {
            return;
        }

        if (enemy_id.Contains("boss_void")) {
            return;
        }

        var bossConfig = EnemyTable.GetEnemyData(enemy_id);
        GameObject bossWeapon = null;
        if (bossConfig != null) {
            bossWeapon = string.IsNullOrEmpty(bossConfig.BossWeapon) ?
                null : ResourcesUtil.LoadWeapon(bossConfig.BossWeapon);
        } else {
            Debug.LogError($"enemy_id:{enemy_id}的BossConfig不存在！");
        }

        var bd = BattleData.data;
        int posX;
        string chestPrefabPath;
        GameObject chestPrefab;
        if (the_maker != null && bd.IsNormalMode) {
            chestPrefabPath = "RGPrefab/LevelObject/Item/chest_boss.prefab";
            chestPrefabPath = RGGameProcess.Inst.modeProcess.OnPreCreatBossChest(chestPrefabPath);
            if (!string.IsNullOrEmpty(chestPrefabPath)) {
                chestPrefab = ResourcesUtil.Load<GameObject>(chestPrefabPath);
                if (chestPrefab == null) {
                    Debug.LogError($"路径:{chestPrefabPath}错误，boss:{name}宝箱加载失败！");
                    return;
                }
                Chest = Instantiate(chestPrefab, the_maker.theRoom.floor_group, true);
                posX = boss_index switch {
                    2 => 2,
                    1 => -2,
                    _ => 0
                };
                Chest.transform.localPosition = new Vector3(posX, BossChestOffsetY, 0);
                if (Chest.TryGetComponent<RGChestBoss>(out var chestBoss)) {
                    if (bossWeapon != null) {
                        chestBoss.object_list = new[] { bossWeapon };
                    } else if (createChestAnyway) {
                        chestBoss.object_list = new GameObject[] { };
                    }
                }

                Chest.SetActive(false);
            }
        }

        if (!bd.IsBossRushMode) {
            return;
        }

        chestPrefabPath = "RGPrefab/LevelObject/Item/chest_gold_single_big.prefab";
        chestPrefab = ResourcesUtil.Load<GameObject>(chestPrefabPath);
        if (chestPrefab == null) {
            Debug.LogError($"路径:{chestPrefabPath}错误，boss:{name}宝箱加载失败！");
            return;
        }
        Chest = Instantiate(chestPrefab, the_maker.theRoom.floor_group, true);
        Chest.name = $"chest_gold_single_big_{boss_index}";
        posX = boss_index switch {
            2 => 3,
            1 => -3,
            _ => 0
        };
        Chest.transform.localPosition = new Vector3(posX, -4, 0);
        float tmpRg = rg_random.Range(0, 100);

        string potPrefabPath = tmpRg switch {
            < 30 => "RGPrefab/LevelObject/Item/health_pot.prefab",
            < 50 => "RGPrefab/LevelObject/Item/restore_pot.prefab",
            _ => "RGPrefab/LevelObject/Item/energy_pot.prefab"
        };
        GameObject potPrefab = ResourcesUtil.Load<GameObject>(potPrefabPath);
        if (potPrefab == null) {
            Debug.LogError($"路径:{chestPrefabPath}错误，boss:{name}宝箱中的pot加载失败！");
            return;
        }
        Chest.GetComponent<ItemChestBig>().boss_objs = new [] { bossWeapon, potPrefab };
        
        Chest.SetActive(false);
    }

    protected void CreateBossEvolutionWeaponChest() {
        if (!GameUtil.IsSingleGame() || GameUtil.InFirstTutorial() || GameUtil.InReturnPlayerIntro()) {
            return;
        }

        if (isDemonstrationCharacter || weaponEvolutionFragmentName.IsNullOrEmpty()) {
            return;
        }

        if (enemy_id.Contains("boss_void")) {
            return;
        }
        
        if (the_maker != null) {
            var  chestWeaponEvolutionPrefabPath = "RGPrefab/LevelObject/Item/chest_boss_weapon_evolution.prefab";
            var chestPrefab = ResourcesUtil.Load<GameObject>(chestWeaponEvolutionPrefabPath);
            if (chestPrefab == null) {
                Debug.LogError($"路径:{chestWeaponEvolutionPrefabPath}错误，boss:{name}武器进化宝箱加载失败！");
                return;
            }
            
            WeaponEvolutionChest = Instantiate(chestPrefab, the_maker.theRoom.floor_group, true);
            WeaponEvolutionChest.transform.localPosition = new Vector3(0, -6, 0);
            if (WeaponEvolutionChest.TryGetComponent<RGChestBossWeaponEvolution>(out var chestBoss)) {
                chestBoss.weaponEvolutionFragmentName = weaponEvolutionFragmentName;
            }
            
            WeaponEvolutionChest.SetActive(false);
        }
    }

    public void ShowTransferGate() {
        var bd = BattleData.data;
        if (bd.IsARAM) {
            return;
        }
        
        if (null != boss_info) {
            boss_info.HideHpBar();
        }
        if (Gate) {
            Gate.SetActive(true);
            ExtraditionNpc.CreateNewExtraditionNpc(transform.parent);// 去第四大关摆渡NPC焊接点
        }
        
        if (Chest) {
            Chest.SetActive(true);
            PrefabPool.Inst.Take(PrefabManager.GetPrefab(PrefabName.effect_show_up), Chest.transform.position, Quaternion.identity);
        }
        
        if (WeaponEvolutionChest && _weaponEvolutionEnhanced) {
            WeaponEvolutionChest.SetActive(true);
            PrefabPool.Inst.Take(PrefabManager.GetPrefab(PrefabName.effect_show_up), Chest.transform.position, Quaternion.identity);
        }
        
        if (Gate || Chest) {
            BattleData.data.boss_end_time = Time.time;
            float costTime = BattleData.data.boss_end_time - BattleData.data.boss_begin_time;
            BattleData.data.AddBossPassTime(costTime);
        }
        onShowGate?.Invoke();
    }

    public void PowerUp() {
        GrowIntensive(false);
        attribute.max_hp = (int)(attribute.max_hp * 2f);
        attribute.hp = attribute.max_hp;
        buff_immune *= 0.5f;
        boss_info.UpdateBossMaxHp();

        transform.Find("img").DOScale(new Vector3(1.75f, 1.75f, 1), 1f);
        if (shadow_lock)
            shadow_lock.transform.localScale = new Vector3(2.5f, 2.5f, 1);
    }

    /// <summary>
    /// Boss通用RunReflection方法
    /// </summary>
    /// <param name="diagonalPossibility">把移动方向改为对角的概率（0~10）</param>
    /// <param name="inverseDiagonalPossibility">移动方向为对角后反向对角的概率（0~10）</param>
    /// <param name="canCreateSmoke">是否创建走路的烟尘特效</param>
    protected void RunReflection(int diagonalPossibility, int inverseDiagonalPossibility, bool canCreateSmoke = true) {
        if (target_obj != null) {
            target_point = target_obj.position;
            FixedDirection(diagonalPossibility, inverseDiagonalPossibility, 5, 10);
            float t = rg_random.Range(0.5f * scout_rate, scout_rate);
            Invoke(nameof(ShootReflection), t);
        } else {
            float r1 = rg_random.Range(-1f, 1f);
            float r2 = rg_random.Range(-1f, 1f);
            move_direction = new Vector2(r1, r2).normalized;
            float t = rg_random.Range(0.5f * scout_rate, scout_rate);
            Invoke(nameof(EndCycle), t);
        }
        SetAnimation("run", true);
        if (canCreateSmoke) {
            CreateSmoke();
        }
    }

    [FoldoutGroup("Debug")]
    [Button("检测boss相关信息", ButtonSizes.Medium)]
    public void CheckBossInfo() {
        var boss = this;
        bool hasProblem = false;
        if (boss.reward_rate < 100) {
            hasProblem = true;
            Debug.LogError($"boss reward_rate 应该为 100");
        }
        if (boss.name != boss.enemy_id) {
            hasProblem = true;
            Debug.LogError($"boss名不一致 name:{boss.name}, enemyid:{boss.enemy_id}");
        }
        if (!hasProblem) {
            Debug.Log("掉率，enemyid没问题");
        }
    }
}