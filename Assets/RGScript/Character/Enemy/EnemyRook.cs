using DG.Tweening;
using UnityEngine;

namespace RGScript.Character.Enemy {
    public class EnemyRook : EnemyAI01 {
        private static readonly int InAtk = Animator.StringToHash("in_atk");
        
        [SerializeField]
        private BulletInfoInspector slashBullet;
        
        protected override void DoAttack() {
            if (need_tap) { 
                anim.SetTrigger(Atk);
            }

            var position = transform.position;
            var distance = Vector2.Distance(target_point, position);
            if (distance < 2.0f) {
                var skillMovement = 5.0f;
                
                var dir = (target_point - position.Vec2()).normalized;
                facing = dir.x > 0 ? 1 : dir.x < 0 ? -1 : facing;
                var angle = Vector2.Angle(dir, new Vector2(facing, 0)) * Mathf.Sign(dir.y);
                
                var forceDir = new Vector2(Mathf.Cos(angle * Mathf.Deg2Rad) * (facing > 0 ? 1 : -1),
                    Mathf.Sin(angle * Mathf.Deg2Rad));
                var hit = Physics2D.BoxCast(
                    transform.position + new Vector3(0, 0.5f, 0),
                    new Vector2(0.2f, 0.2f),
                    0,
                    forceDir,
                    skillMovement,
                    PhysicsUtil.StaticWallMask);
                if (hit) {
                    skillMovement = hit.distance;
                }
                
                var moveTo = rigibody.MoveTo(
                    (Vector2)transform.position + forceDir * skillMovement, 0.2f);
                moveTo.OnComplete(() => {
                    can_move = true;
                    anim.SetBool(InAtk, false);
                    Invoke(nameof(TurnCanShoot), shoot_cd);
                    Invoke(nameof(EndCycle), scout_rate);
                });

                var bulletInfo = new BulletInfo()
                    .SetUp(slashBullet.bulletProto,
                        gameObject,
                        0,
                        position + new Vector3(0, 0.5f, 0),
                        angle * (facing > 0 ? 1 : -1), camp);
                var damageInfo = new DamageInfo().SetUp(
                    slashBullet.bulletProto, slashBullet.damage, attribute.critical, 2, camp);
                var bullet = BulletFactory.TakeBullet(
                    bulletInfo,
                    damageInfo,
                    false,
                    transform
                );
                bullet.transform.localScale = new Vector3(facing, 1, 1);
                
                anim.SetBool(InAtk, true);
                
                can_move = false;
                can_shoot = false;
            } else {
                Invoke(nameof(ShootReflection), 0.5f * scout_rate);
            }
        }
    }
}
