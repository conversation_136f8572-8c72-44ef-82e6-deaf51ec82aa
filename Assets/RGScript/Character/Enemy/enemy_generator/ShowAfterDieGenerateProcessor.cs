using EnemySystem;
using System.Collections.Generic;
using System.Text;
using Sirenix.OdinInspector;
using UnityEngine;

namespace EnemyGenerator {
    /// <summary>
    /// 在其他怪物死掉后再生成指定怪物
    /// </summary>
    public class ShowAfterDieGenerateProcessor : MonoBehaviour, IGenerateProcessor {
        public string enemyId;
        public int hideEnemyCountMin;
        public int hideEnemyCountMax = 2;
        public float multiEnemyRadius = 2.5f;
        private readonly Dictionary<RGEController, EnemyTag> _enemyTags = new Dictionary<RGEController, EnemyTag>();
        private int _hideEnemyCount;
        private readonly List<(List<RGEController>, List<RGEController>)> _targetHideEnemies = new List<(List<RGEController>, List<RGEController>)>();
        private int _currentIndex = -1;
        public void ProcessEnemyShow(EnemyMaker em, RGEController enemyController, RGRandom random) {
            var targetEnemies = _targetHideEnemies[_currentIndex].Item1;
            var hideEnemies = _targetHideEnemies[_currentIndex].Item2;
            if (enemyController.enemy_id == enemyId && hideEnemies.Count < _hideEnemyCount) {
                hideEnemies.Add(enemyController);
                _enemyTags[enemyController] = EnemyTag.Hide;
                enemyController.gameObject.SetActive(false);
            } else if (enemyController.enemy_id != enemyId && targetEnemies.Count < _hideEnemyCount) {
                targetEnemies.Add(enemyController);
                _enemyTags[enemyController] = EnemyTag.Target;
                var currentIndex = _currentIndex;
                enemyController.onEnemyDead += controller =>  OnTargetEnemyDead(controller, currentIndex);
            } else {
                _enemyTags[enemyController] = EnemyTag.None;
            }
        }

#if UNITY_EDITOR
        [Button]
        public void OutputTargets() {
            Debug.Log("========Targets========");
            for (var index = 0; index < _targetHideEnemies.Count; index++) {
                var targetHideEnemy = _targetHideEnemies[index];
                var builder = new StringBuilder($"Index {index}:\n");
                builder.Append("targets:\n\t");
                for (var i = 0; i < targetHideEnemy.Item1.Count; i++) {
                    var controller = targetHideEnemy.Item1[i];
                    builder.Append(controller.name);
                    if (i < targetHideEnemy.Item1.Count - 1) {
                        builder.Append(", ");
                    }
                }
                builder.Append("\nhides:\n\t");
                for (int i = 0; i < targetHideEnemy.Item2.Count; i++) {
                    var controller = targetHideEnemy.Item2[i];
                    builder.Append(controller.name);
                    if (i < targetHideEnemy.Item2.Count - 1) {
                        builder.Append(", ");
                    }
                }
                Debug.Log(builder.ToString());
            }
            Debug.Log("========Ends========");
        }
#endif

        /// <summary>
        /// 目标怪物死亡时的回调
        /// </summary>
        private void OnTargetEnemyDead(RGEController obj, int index) {
            var targetEnemies = _targetHideEnemies[index].Item1;
            var hideEnemies = _targetHideEnemies[index].Item2;
            if (hideEnemies.Count == 0) {
                return;
            }
            
            targetEnemies.Remove(obj);
            var hideEnemy = hideEnemies[0];
            hideEnemies.Remove(hideEnemy);
            var hideEnemyCount = hideEnemies.Count;
            hideEnemy.gameObject.SetActive(true);
            var deadPosition = obj.transform.position;
            var enemyRoom = obj.the_maker ? obj.the_maker.theRoom : null;
            if (targetEnemies.Count == 0 && hideEnemyCount > 0) {
                var angleInBetween = 360 / hideEnemyCount + 1;
                var hideEnemyPosition = deadPosition + Vector3.right * multiEnemyRadius;
                hideEnemy.transform.position = hideEnemyPosition;
                CreateShowUpEffect(hideEnemyPosition);
                for (var i = 0; i < hideEnemyCount; i++) {
                    var enemy = hideEnemies[i];
                    enemy.gameObject.SetActive(true);
                    var enemyPosition = deadPosition +
                                                Quaternion.Euler(0, 0, (i + 1) * angleInBetween) *
                                                Vector3.right *
                                                multiEnemyRadius;
                    CreateShowUpEffect(enemyPosition);
                    enemy.transform.position = EnemyPosition(enemyRoom, enemyPosition);
                }
            } else {
                hideEnemy.transform.position = EnemyPosition(enemyRoom, deadPosition);
                CreateShowUpEffect(deadPosition);
            }
        }

        /// <summary>
        /// 处理房间中怪物的位置避免怪物生成到房间外
        /// </summary>
        private static Vector3 EnemyPosition(RGRoomX room, Vector3 wordPosition) {
            if (!room) {
                return wordPosition;
            }

            var roomLocalPosition = room.transform.InverseTransformPoint(wordPosition);
            var halfWidth = room.rect.width / 2f;
            var halfHeight = room.rect.height / 2f;
            if (roomLocalPosition.x >= halfWidth - .5f) {
                roomLocalPosition.x = halfWidth - 1f;
            }

            if (roomLocalPosition.x <= -halfWidth + .5f) {
                roomLocalPosition.x = -halfWidth + 1f;
            }

            if (roomLocalPosition.y >= halfHeight - .5f) {
                roomLocalPosition.y = halfHeight - 1f;
            }

            if (roomLocalPosition.y <= -halfHeight + .5f) {
                roomLocalPosition.y = -halfHeight + 1f;
            }

            return room.transform.TransformPoint(roomLocalPosition);
        }

        public bool IsSetEnemyHide(RGEController enemyController) {
            return _enemyTags.ContainsKey(enemyController) && _enemyTags[enemyController] == EnemyTag.Hide;
        }

        public void BeforeCreatePart(EnemyMaker em, RGRandom random, int part) {
            var targetEnemies = new List<RGEController>();
            var hideEnemies = new List<RGEController>();
            _currentIndex += 1;
            _targetHideEnemies.Add((targetEnemies, hideEnemies));
            _enemyTags.Clear();
            _hideEnemyCount = random.Range(hideEnemyCountMin, hideEnemyCountMax);
        }

        public void AfterCreatePart(EnemyMaker enemyMaker, RGRandom random, int part) {
            (List<RGEController> targetEnemies, List<RGEController> hideEnemies) = _targetHideEnemies[_currentIndex];
            while (hideEnemies.Count > 0 && hideEnemies.Count > targetEnemies.Count) {
                var hideEnemy = hideEnemies[0];
                hideEnemies.RemoveAt(0);
                if (Debug.isDebugBuild) {
                    Debug.Log("Set Enemy To Show", hideEnemy.gameObject);
                }
                _enemyTags[hideEnemy] = EnemyTag.None;
                EnemyFactory.CreateEnemyShowUpEffect(hideEnemy.gameObject);
            }
        }

        public void BeforeRoomCreated(RGRoomX room) {
            room.EMaker.generateProcessor = this;
        }

        private void CreateShowUpEffect(Vector3 position) {
            var showUpEffect = Instantiate(
                PrefabManager.GetPrefab(PrefabName.effect_show_up), 
                position,
                Quaternion.identity,
                RGGameSceneManager.Inst.temp_objects_parent
            );
            var effectSpriteRenderer = showUpEffect.transform.Find("img").GetComponent<SpriteRenderer>();
            effectSpriteRenderer.color = new Color(0.9f, 0.1f, 0.1f);
        }

        private enum EnemyTag {
            None, // 不做任何处理
            Hide, // 隐藏
            Target // 被击败后显示隐藏怪物
        }
    }
}