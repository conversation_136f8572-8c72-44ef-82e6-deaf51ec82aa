using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class TransitionPortSummonEffect : EnemySummonEffect {
    public Vector3 effectPosition;
    public GameObject effectPrefab;
    public float delayTime = .5f;
    public float effectScale = 1;
    public override void EffectStop() {
    }

    public override void EffectInit() {
    }

    public override void IdleEffectStart() {
    }

    public override void EffectOnSummon(Transform summonTransform) {
        StartCoroutine(ShowEffect(transform.position + effectPosition));
    }

    private IEnumerator ShowEffect(Vector3 position) {
        yield return new WaitForSeconds(delayTime);
        var effectGO = PrefabPool.Inst.Take(effectPrefab, 
            position,
            Quaternion.identity,
            RGGameSceneManager.GetInstance().temp_objects_parent
        );

        effectGO.transform.localScale = Vector3.one * effectScale;
    }
}
