using System;
using DG.Tweening;
using UnityEngine;

[RequireComponent(typeof(RGEController))]
public class Boss25Dead : MonoBehaviour {
    public float animationTime;
    public float initDelay = 2f;
    public Vector3 swampEntrancePosition;
    public AnimationCurve scaleAnimCurve;
    public float rotationAmount;
    private void Awake() {
        var controller = GetComponent<RGEController>();
        controller.onEnemyDead += OnEnemyDead;
    }

    private void OnEnemyDead(RGEController obj) {
        obj.enabled = false;
        var sequence = DOTween.Sequence();

        sequence.SetDelay(initDelay);
        sequence.OnStart(() => {
            var colliders = GetComponentsInChildren<Collider2D>();
            foreach (var c in colliders) {
                c.enabled = false;
            }
            var collider2d = GetComponent<Collider2D>();
            if (collider2d) {
                collider2d.enabled = false;
            }
        });
        sequence.Append(transform.DOLocalMove(swampEntrancePosition, animationTime));
        sequence.Join(transform.DOScale(Vector3.zero, animationTime / 3 * 2).SetDelay(animationTime / 3).SetEase(scaleAnimCurve));
        sequence.Join(transform.DOLocalRotateQuaternion(transform.rotation * Quaternion.Euler(0, 0, rotationAmount), animationTime));
        sequence.Play().OnComplete(() => {
            if (gameObject) {
                gameObject.SetActive(false);
            }
        });
    }
}