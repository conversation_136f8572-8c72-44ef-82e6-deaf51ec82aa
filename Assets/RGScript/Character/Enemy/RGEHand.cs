using RGScript.Character.Player;
using UnityEngine;
using static RGEWeapon;

public class RGEHand : MonoBehaviour, IHand {
    [HideInInspector]
	public RGEWeapon weapon;
	void Awake () {
		UpdateWeapon();
	}

	public void DoStartAtk() {
        if (weapon == null) return;
        switch (weapon.atkMode) {
            case AtkMode.useBool:
                weapon.SetAttack(true);
                break;
            case AtkMode.useTrigger:
                weapon.SetAttackTrigger();
                break;
            case AtkMode.both:
                if (weapon.Rg_random.GetBool())
                    weapon.SetAttackTrigger();
                else
                    weapon.SetAttack(true);
                break;
        }
    }

    public void DoEndAtck() {
        if (weapon == null) {
            return;
        }

        if (weapon.atkMode==AtkMode.useBool || weapon.atkMode == AtkMode.both) {
            weapon.SetAttack(false);
        }
    }

	public void UpdateWeapon(){
		if(transform.childCount!=0){
			weapon=transform.GetChild(0).GetComponent<RGEWeapon>();
		}
	}
	public void SetWeaponFacing(int facing){
		if(weapon)
			weapon.facing=facing;
	}

    public Transform GetTransform() {
        return transform;
    }
    public void SetAttack(bool value1){
		if(weapon)
			weapon.SetAttack(value1);
	}

    public bool NeedLock() {
        return weapon != null && weapon.need_lock;
    }

    public void LockedWeaponProcess(float fixedAngle) {
        
    }

    public void SetAttackTrigger(){
		if(weapon)
			weapon.SetAttackTrigger();
	}

    public RGEWeapon GetRGEWeapon() {
        if (weapon == null) {
            UpdateWeapon();
        }
        return weapon;
    }

    public RGWeapon GetRGWeapon() {
        throw new System.NotImplementedException();
    }

    public void DestroyWeapon(){
        if (weapon) {
            Destroy (weapon.gameObject);
        }
	}

    public void ShowWeapon() {
        if (weapon) {
            weapon.gameObject.SetActive(true);
        }
    }
}