/// <summary>
/// pet 招财
/// 技能：随机标记一个敌人，限时击败该敌人则随机掉落金币
/// </summary>
public class Pet39Controller : RGPetWithSkillController {
    /// <summary>
    /// 限制地牢里面每层关卡金币上限
    /// </summary>
    public int limitCoinOfLevel = 20;
    private int _totalCoinCount = 0;
    
    // boss不受限制
    public override bool CanUseSkill() {
        var isBoss = has_target && target_obj.GetComponent<RGEController>().isBoss;
        return IsSkillReady && (isBoss || _totalCoinCount < limitCoinOfLevel);
    }
    
    protected override void Start() {
        base.Start();
        _totalCoinCount = 0;
        SimpleEventManager.AddEventListener<Pet39SkillCoinEvent>(OnSkillAddCoin);
    }

    private void OnDisable() {
        SimpleEventManager.RemoveListener<Pet39SkillCoinEvent>(OnSkillAddCoin);
    }

    private void OnSkillAddCoin(Pet39SkillCoinEvent e) {
        if (!GameUtil.IsSingleGame() && !GameUtil.IsMultiGame()) return;
        if (_totalCoinCount >= limitCoinOfLevel) return;

        _totalCoinCount += e.count;
    }
}