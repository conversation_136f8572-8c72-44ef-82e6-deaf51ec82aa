using UnityEngine;

/// <summary>
/// pet 波奇
/// </summary>
public class Pet31Controller : RGPetWithSkillController {
    public int skillDamage = 1;

    // skill1； 圆形范围，造成伤害1，击退2。CD 8s
    public void OnDoSkill1() {
        if (ShouldDoSkill1 && IsSkillReady && skillInfo.skillObject && rg_random.Range(0, 10) < 5) {
            RoleSkillStart();
            ShowSkillEfect();
            var obj = Instantiate(skillInfo.skillObject, transform.Find("img").position, Quaternion.identity, RGGameSceneManager.Inst.temp_objects_parent);
            if(obj.GetComponent<ExplodeHammer>() is {} damageCarrier){
                damageCarrier.damage = Mathf.RoundToInt(skillDamage * attribute.atk_damage_factor);
                damageCarrier.SetSourceObject(gameObject);
            }
            RoleSkillEnd();
        }
    }

    public override bool CanUseSkill() {
        return false;
    }
}