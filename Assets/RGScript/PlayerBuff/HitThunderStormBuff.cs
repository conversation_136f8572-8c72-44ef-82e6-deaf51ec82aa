using RGScript.Character.Player;
using UnityEngine;

namespace RGScript.PlayerBuff {
    /// <summary>
    /// 雷暴天赋 & 闪电链子伤害
    /// </summary>
    public class HitThunderStormBuff : EnemyHitBuff {

        public const string ThunderPath = "RGPrefab/Bullet/Explosion/explode_thunder.prefab"; //蓝色
        public const string ThunderPurplePath = "RGPrefab/Bullet/Explosion/explode_thunder_purple.prefab"; //紫色
        public const string ThunderYellowPath = "RGPrefab/Bullet/Explosion/explode_thunder_yellow.prefab"; //黄色
        public const string ThunderOrangePath = "RGPrefab/Bullet/Explosion/explode_thunder_orange.prefab"; //橙色
        public const string ThunderGreenPath = "RGPrefab/Bullet/Explosion/explode_thunder_green.prefab"; //绿色
        
        public const string ThunderS6Path = "RGPrefab/Bullet/Explosion/explode_thunder_s6.prefab";  //博士s6

        public const string ThunderLightPath = 
            "RGPrefab/Bullet/Explosion/explode_thunder_light.prefab"; //蓝色+光柱

        public const string ThunderLightPurplePath =
            "RGPrefab/Bullet/Explosion/explode_thunder_light_purple.prefab"; //紫色+光柱

        public const string ThunderLightYellowPath =
            "RGPrefab/Bullet/Explosion/explode_thunder_light_yellow.prefab"; //黄色+光柱
        
        public const string ThunderLightOrangePath =
            "RGPrefab/Bullet/Explosion/explode_thunder_light_orange.prefab"; //橙色+光柱

        public const string ThunderLightGreenPath =
            "RGPrefab/Bullet/Explosion/explode_thunder_light_green.prefab"; //绿色+光柱
        
       public const string ThunderLightS6Path =
            "RGPrefab/Bullet/Explosion/explode_thunder_light_s6.prefab"; //博士s6

        public override emBuff Buff { get { return emBuff.HitThunderStormBuff; } }

        public const int AddCritic = 5;
        public const int DefaultThunderDamage = 3;
        public const int BuffThunderDamage = 4; //雷暴天赋

        public HitThunderStormBuff(float cd, float randomWeight) : base(cd, randomWeight) {
            triggerByCritical = true;
        }

        public override void OnProcessEnemyHit(PlayerBulletHitEnemyEvent eParam, RGController ctrl) {
            Vector3 pos = eParam.enemy.transform.position;
            var thunderCount = 1;
            thunderCount += DataUtil.CalBuffHitThunderStormExtraCount();
            thunderCount = Mathf.Min(thunderCount, 3);
            for (var i = 0; i < thunderCount; i++) {
                GenThunderByPlayer(ctrl.gameObject, pos, ctrl.camp, GetThunderType(ctrl), true, BuffThunderDamage);
            }
        }

        private ThunderType GetThunderType(RGController ctrl) {
            if (ctrl.GetHeroType() == emHero.Doctor) {
                return (ctrl as C28Controller).GetSkinThunderType();
            }

            return ThunderType.Purple;
        }

        #region static Fun

        public static void GenThunderByPlayer(GameObject source, Vector3 pos, int camp, ThunderType type,
            bool withLight = false,
            int damage = DefaultThunderDamage, int count = 2, float distance = 15f) {
            string path = GetThunderPath(type, withLight);
            var prefab = ResourcesUtil.Load<GameObject>(path);
            GameObject temp_obj = PrefabPool.Inst.Take(prefab);
            temp_obj.transform.position = pos;
            LightningEffector trigger = SetLightningData(source, camp, count, distance, temp_obj);
            if (withLight) {
                //产生光柱爆炸
                Explode.DoExplode(temp_obj, source, damage);
            } else {
                //主动触发闪电链
                trigger.TriggerByCustom(temp_obj.transform, damage);
            }
        }
        
        private static LightningEffector SetLightningData(GameObject source, int camp, int count, float distance,
            GameObject tempObj) {
            var trigger = tempObj.GetComponent<LightningEffector>();
            trigger.distance = distance;
            trigger.count = count;
            trigger.SetSourceObject(source, camp);
            return trigger;
        }

        public static string GetThunderPath(ThunderType type, bool withLight) {
            string path = withLight ? ThunderLightPath : ThunderPath;
            switch (type) {
                case ThunderType.Blue:
                    path = withLight ? ThunderLightPath : ThunderPath;
                    break;
                case ThunderType.Purple:
                    path = withLight ? ThunderLightPurplePath : ThunderPurplePath;
                    break;
                case ThunderType.Yellow:
                    path = withLight ? ThunderLightYellowPath : ThunderYellowPath;
                    break;
                case ThunderType.Orange:
                    path = withLight ? ThunderLightOrangePath : ThunderOrangePath;
                    break;                
                case ThunderType.Green:
                    path = withLight ? ThunderLightGreenPath : ThunderGreenPath;
                    break;
                case ThunderType.Skin6:
                    path = withLight ? ThunderLightS6Path : ThunderS6Path;
                    break;
                default:
                    return path;
            }

            return path;
        }

        #endregion
    }

    public enum ThunderType {
        Blue,
        Purple,
        Yellow,
        Orange,
        Green,
        Skin6
    }
}