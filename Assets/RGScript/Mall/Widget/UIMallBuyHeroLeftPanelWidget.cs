using DG.Tweening;
using I2.Loc;
using RGScript.UI.ChooseHero;
using RGScript.Util;
using RGScript.Weapon;
using System.Collections.Generic;
using UI.Base;
using UnityEngine;
using UnityEngine.UI;

namespace RGScript.Mall {
    public class UIMallBuyHeroLeftPanelWidget : BaseUIWidget {
        private ChooseHeroData _data;
        private RectTransform _heroAttributes;

        public Sprite buttonDownSprite;
        public Sprite buttonUpSprite;

        private Image _heroIcon;
        private Transform _starsNode;
        private RectTransform _buff;
        private Image _buffIcon;
        private RectTransform _weapon;
        private RectTransform _jewelry;

        private RectTransform _panelBg;
        private RectTransform _detailArrow;
        private RectTransform _detailBg;

        private static readonly Color GrayColor = new Color(0.7f, 0.7f, 0.7f, 0.7f);

        private static readonly float[] AttributesMaxNum = {
            12, 10, 320, 10
        };

        private const float UpgradeAnimationDuration = 2.0f;
        private const float StarEffectDuration = 0.8f;

        private readonly List<Transform> _values = new List<Transform>();

        protected override void OnInit() {
            base.OnInit();


            InitUI();
        }

        private void InitUI() {
            _heroAttributes = transform.Find("panel/hero_attributes").GetComponent<RectTransform>();
            var levelPanel = _heroAttributes.Find("level_panel");
            // Left
            _heroIcon = levelPanel.Find("hero_icon").GetComponent<Image>();
            _starsNode = levelPanel.Find("stars");

            const int heroAttributesNum = 4;
            string[] attributesString = {
                "tips/hp", "tips/armor", "tips/energy", "tips/critical"
            };
            for (var i = 0; i < heroAttributesNum; ++i) {
                var value = _heroAttributes.Find($"value{i + 1}");
                value.Find("Name").GetComponent<Text>().text = ScriptLocalization.Get(attributesString[i]);
                _values.Add(value);
            }

            _buff = _heroAttributes.Find("buff").GetComponent<RectTransform>();
            _buff.Find("name").GetComponent<Text>().text = ScriptLocalization.Get("tips/passive_skill");
            _buffIcon = _buff.Find("icon").GetComponent<Image>();

            _weapon = _heroAttributes.Find("weapon").GetComponent<RectTransform>();
            _weapon.Find("name").GetComponent<Text>().text = ScriptLocalization.Get("tips/init_weapon");

            _jewelry = _heroAttributes.Find("jewelry").GetComponent<RectTransform>();
            _jewelry.Find("name").GetComponent<Text>().text = ScriptLocalization.Get("jewelry/name");


            _buff.GetComponent<Button>().interactable = false;
            _weapon.GetComponent<Button>().interactable = false;
            _jewelry.GetComponent<Button>().interactable = false;
            // _buff.GetComponent<Button>().onClick.AddListener(OnBuffButtonClicked);
            // _weapon.GetComponent<Button>().onClick.AddListener(OnWeaponButtonClicked);
            // _jewelry.GetComponent<Button>().onClick.AddListener(OnJewelryButtonClicked);
            _panelBg = transform.Find("panel/bg").GetComponent<RectTransform>();
            _detailArrow = _heroAttributes.Find("detail_arrow").GetComponent<RectTransform>();
            _detailBg = _heroAttributes.Find("detail_bg").GetComponent<RectTransform>();
            
            _detailArrow.gameObject.SetActive(false);
            _detailBg.gameObject.SetActive(false);
        }


        private void OnClickButton() {
            Debug.Log($"Click button: {_data}");
        }


        protected override void OnStartUp(params object[] args) {
            base.OnStartUp(args);
            _data = (ChooseHeroData)args[0];

            AddListeners();

            Refresh(_data);
        }

        private void AddListeners() {
        }

        private void RemoveListeners() {
        }

        private void Refresh(ChooseHeroData data) {
            if (data.isSuperHero) {
                _buff.anchoredPosition = new Vector2(-28, -78);
                _weapon.anchoredPosition = new Vector2(92, -78);
                _jewelry.gameObject.SetActive(false);
            } else {
                _buff.anchoredPosition = new Vector2(-78, -78);
                _weapon.anchoredPosition = new Vector2(32, -78);
                _jewelry.gameObject.SetActive(true);
            }

            RefreshHeroIcon(data);
            RefreshHeroStars(data);
            RefreshHeroAttributes(data);
            RefreshPassiveSkill(data);
            RefreshWeaponData(data);
        }


        private void RefreshHeroIcon(ChooseHeroData data) {
            _heroIcon.sprite = RGSaveManager.Inst.GetHeroUISprite(data.id);
            _heroIcon.SetNativeSize();
        }

        private void RefreshHeroStars(ChooseHeroData data, bool showUpgradeAnimation = false) {
            var superStar = _starsNode.parent.Find("super_star");
            if (!data.isSuperHero) {
                _starsNode.gameObject.SetActive(true);
                superStar.gameObject.SetActive(false);

                var bg = _starsNode.Find("bg");
                var layout = _starsNode.Find("layout");
                for (var i = 0; i < layout.childCount; ++i) {
                    var bgImage = bg.GetChild(i).GetComponent<Image>();
                    var starImage = layout.GetChild(i).GetComponent<Image>();
                    bgImage.gameObject.SetActive(i < data.maxLevel);
                    starImage.gameObject.SetActive(i < data.maxLevel);
                    starImage.color = i < data.level ? Color.white : new Color(1.0f, 1.0f, 1.0f, 0.0f);

                    if (!showUpgradeAnimation || i != data.level - 1) {
                        continue;
                    }

                    // Do upgrade animation
                    starImage.color = new Color(1.0f, 1.0f, 1.0f, 0.0f);
                    starImage.transform.localScale = new Vector3(1.8f, 1.8f, 1.0f);

                    var sequence = DOTween.Sequence();
                    sequence.Join(starImage.DOFade(1.0f, UpgradeAnimationDuration / 3));
                    sequence.Join(starImage.transform.DOScale(1.0f, UpgradeAnimationDuration / 3));
                }

                bg.localPosition = new Vector3(12 * (bg.childCount - data.maxLevel), 0, 0);
                layout.localPosition = new Vector3(12 * (layout.childCount - data.maxLevel), 0, 0);
            } else {
                _starsNode.gameObject.SetActive(false);
                superStar.gameObject.SetActive(true);
            }
        }

        private void RefreshHeroAttributes(ChooseHeroData data) {
            const int progressBarHeight = 28;
            const int progressBarWidth = 248;
            for (var i = 0; i < _values.Count; ++i) {
                var valueText = _values[i].Find("Text").GetComponent<Text>();
                valueText.text = data.attributes[i].ToString();

                var width = data.attributes[i] / AttributesMaxNum[i] * progressBarWidth;
                width = Mathf.Min(width, progressBarWidth);

                var imageTransform = _values[i].Find("Image").GetComponent<RectTransform>();
                var imageAdditionTransform = _values[i].Find("ImageAddition").GetComponent<RectTransform>();
                imageTransform.sizeDelta = new Vector2(width, progressBarHeight);
                imageAdditionTransform.sizeDelta = new Vector2(width, progressBarHeight);
            }
        }

        private void RefreshBuffAndWeaponButtonState(ChooseHeroData data) {
            var buffBackground = _buff.GetComponent<Image>();
            var weaponBackground = _weapon.GetComponent<Image>();
            switch (data.buffAndWeaponButtonState) {
                case "":
                    buffBackground.sprite = buttonUpSprite;
                    weaponBackground.sprite = buttonUpSprite;
                    break;
                case "buff":
                    buffBackground.sprite = buttonDownSprite;
                    weaponBackground.sprite = buttonUpSprite;

                    break;
                case "weapon":
                    buffBackground.sprite = buttonUpSprite;
                    weaponBackground.sprite = buttonDownSprite;

                    break;
            }
        }

        private void RefreshPassiveSkill(ChooseHeroData data) {
            var hero = MapManagerHall.Inst.Controllers[data.id];

            var lockImage = _buffIcon.transform.parent.Find("lock").GetComponent<Image>();
            _buffIcon.sprite = BuffInfoTable.info.GetInfo(hero.role_attribute.default_buff).icon;
            if (data.level <= 5) {
                _buffIcon.color = GrayColor;
                lockImage.color = Color.white;
            } else {
                _buffIcon.color = Color.white;
                lockImage.color = new Color(1, 1, 1, 0);
            }

            _detailBg.Find("text").GetComponent<Text>().text =
                NameUtil.GetBuffDesc(hero.role_attribute.default_buff, 1);
        }

        private void RefreshWeaponData(ChooseHeroData data) {
            var weaponName = DataUtil.GetInitWeaponName((emHero)data.id, data.skinIndex);
            var rgWeapon = WeaponFactory.CreateWeapon(weaponName, emWeaponSource.Init);
            var texts = rgWeapon.GetComponent<RGWeapon>().texts;
            var weaponInfo = _detailBg.Find("weapon_info");
            weaponInfo.Find("atk/text").GetComponent<Text>().text = texts[0];
            weaponInfo.Find("consume/text").GetComponent<Text>().text = texts[1];
            weaponInfo.Find("critic/text").GetComponent<Text>().text = texts[2];
            weaponInfo.Find("accurate/text").GetComponent<Text>().text = texts[3];
            Destroy(rgWeapon.gameObject);

            _weapon.Find("icon").GetComponent<Image>().sprite = SpriteMap.weaponSprite.GetSprite(weaponName);
        }

        protected override void OnClear() {
            base.OnClear();
            RemoveListeners();
        }
    }
}