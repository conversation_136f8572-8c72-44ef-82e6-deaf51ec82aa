using cfg.Store;
using System.Collections.Generic;
using I2.Loc;
#if UNITY_SWITCH
using nn.hid;
#endif
using RGScript.Data;
using RGScript.Data.GameItemData;
using RGScript.Data.Mall;
using RGScript.Util;
using RGScript.Util.AssetBundle;
using RGScript.WeaponEvolution;
using Sirenix.OdinInspector;
using SoulKnight.Runtime.Item;
using System;
using System.Linq;
using System.Reflection;
using System.Text;
using UIFramework;
using UnityCommon.UI;
using UnityEngine;
using UnityEngine.UI;

namespace RGScript.Mall {
    public class ConfirmWindowBundleView {
        public GameObject parent;
        public GameObject imageContainer;
        public Text bundleNameText;
        public Image bundleIcon;
        public RectTransform bundleDescParentTransform;
        public GameObject bundleDescItem;
        public ScrollRect descScrollRect;
    }

    public class ConfirmWindowView {
        public GameObject confirmWindowObject;
        public CanvasGroup canvasGroup;
        public ConfirmWindowBundleView bundleView;
        public Text priceText;
    }

    /// <summary>
    /// 商城购买礼包时的二次确认弹窗
    /// </summary>
    public class UIMallConfirmPackageWindow : BaseUIView {
        private ConfirmWindowView _confirmWindowView;
        private Button _buyButton;
        private Button _closeButton;
        private Image _currencyImage;
        private Image _buttonImage;
        public float fadeInTime = 0.1f;
        public float fadeOutTime = 0.1f;
        public float sizePerPixel = 4.166667f;
        public float showUpComposedScale = 2f;
        private TimeCounter _counter;
        private State _state;
        private List<BundleDescItemView> _descItemViews = new List<BundleDescItemView>();

        private MallItem _mallItem;
        private Button _introButton;

        // 有能转换成宝石的商品
        private bool _hasOwnedItem;

        private bool _isFromSubPage;

        private Action<MallData.MallBuyBundleResult> onBuyCallback2;
        private ulong _bundleId;

        public override void InitView() {
            base.InitView();
            pauseWhenShow = true;
            handleEsc = true;

            InitGetters();
            _counter = new TimeCounter(true);
            _state = State.None;
            _descItemViews.Add(new BundleDescItemView(_confirmWindowView.bundleView.bundleDescItem));
        }

        private void InitGetters() {
            _confirmWindowView = new ConfirmWindowView();
            _confirmWindowView.canvasGroup = GetComponent<CanvasGroup>();
            var panelTf = transform.Find("panel");
            var contentTf = panelTf.Find("content");

            _confirmWindowView.bundleView = new ConfirmWindowBundleView();
            _confirmWindowView.bundleView.parent = contentTf.Find("bundle").gameObject;
            _confirmWindowView.bundleView.imageContainer = _confirmWindowView.bundleView.parent.transform
                .Find("left/image_container").gameObject;
            _confirmWindowView.bundleView.bundleNameText = _confirmWindowView.bundleView.parent.transform
                .Find("left/GameObject").GetComponent<Text>();
            _confirmWindowView.bundleView.bundleIcon =
                _confirmWindowView.bundleView.imageContainer.GetComponent<Image>();
            _confirmWindowView.bundleView.bundleDescParentTransform = _confirmWindowView.bundleView.parent.transform
                .Find("desc_text/Viewport/Content").GetComponent<RectTransform>();
            _confirmWindowView.bundleView.bundleDescItem =
                _confirmWindowView.bundleView.bundleDescParentTransform.Find("item").gameObject;
            _confirmWindowView.bundleView.descScrollRect = _confirmWindowView.bundleView.parent.transform
                .Find("desc_text").GetComponent<ScrollRect>();

            _buyButton = panelTf.Find("btns/buyBtn").GetComponent<Button>();
            _buttonImage = _buyButton.GetComponent<Image>();
            _confirmWindowView.priceText = _buyButton.transform.Find("Text").GetComponent<Text>();
            _currencyImage = _buyButton.transform.Find("Image").GetComponent<Image>();
            _closeButton = panelTf.Find("cancelBtn").GetComponent<Button>();
            _introButton = panelTf.Find("intro_button").GetComponent<Button>();
            _introButton.onClick.AddListener(OnClickIntro);
        }

        private void OnClickIntro() {
            if (!_mallItem.isMallItem) {
                return;
            }

            var content = GetBlindBoxText();
            if (string.IsNullOrEmpty(content)) {
                return;
            }

            UIWindowTextIntro.ShowWindow(content);
        }

        public void Update() {
            switch (_state) {
                case State.FadeIn:
                    FadeInUpdate();
                    break;
                case State.FadeOut:
                    FadeOutUpdate();
                    break;
            }
        }

        private void FadeOutUpdate() {
            var progress = _counter.TimePass / _counter.TimeLength;
            if (progress > 1) {
                _state = State.None;
                _confirmWindowView.canvasGroup.alpha = 0;
                OnClick_Close();
            } else {
                _confirmWindowView.canvasGroup.alpha = 1 - progress * progress;
            }
        }

        private void FadeInUpdate() {
            var progress = _counter.TimePass / _counter.TimeLength;
            if (progress > 1) {
                _state = State.Idle;
                _confirmWindowView.canvasGroup.alpha = 1;
                _confirmWindowView.canvasGroup.interactable = true;
            } else {
                _confirmWindowView.canvasGroup.alpha = progress * progress;
            }
        }

        public override void ShowView(params object[] args) {
            base.ShowView(args);

            if (args.Length == 0) {
                Debug.LogError("UIMallConfirmWindow.ShowView args.Length == 0");
                return;
            }

            ClearValue();

            _buyButton.onClick.AddListener(ConfirmClick);
            _closeButton.onClick.AddListener(CancelClick);

            if (args[0] is MallItem mallItem) {
                _mallItem = mallItem;
                // 变为fadeIn状态
                _state = State.FadeIn;
                _confirmWindowView.canvasGroup.alpha = 0;
                _confirmWindowView.canvasGroup.interactable = false;
                _counter.ResetTime(fadeInTime);
                if (mallItem is MallItem_Bundle bundleMallItem) {
                    bundleMallItem.UpdateFinalPrice();
                }

                InitMallStoreItem(_mallItem);

                _introButton.gameObject.SetActive(mallItem.isMallItem &&
                                                  MallUtility.IsBlinkBoxPackage(mallItem.Config));
            } else {
                _introButton.gameObject.SetActive(false);
                Debug.LogError("Failed to startup confirm window, args[0] is not MallItem or StoreItem");
                OnClick_Close();
                return;
            }

            if (args.Length > 1) {
                if (args[1] is bool) {
                    _isFromSubPage = (bool)args[1];
                } else if (args[1] is Action<MallData.MallBuyBundleResult>) {
                    onBuyCallback2 = (Action<MallData.MallBuyBundleResult>)args[1];
                }
            }

            SimpleEventManager.AddEventListener<StartLoadSceneEvent>(OnStartLoadSceneEvent);
            // SimpleEventManager.AddEventListener<MallItemsUpdateEvent>(OnMallItemsUpdateEvent);
        }

        private void LoadBundle() {
            _bundleId = AssetBundleRefCountLoader.Instance.LoadAssetBundle("mall_bundle", () => {
                _confirmWindowView.bundleView.bundleIcon.sprite =
                    SpriteUtility.GetBundleSprite(_mallItem.Config.GoodID);
            });
        }

        private void ClearValue() {
            _mallItem = null;
        }

        private void InitMallStoreItem(MallItem mallItem) {
            _confirmWindowView.bundleView.bundleNameText.text = mallItem.GetShowItemTitle();
            _confirmWindowView.bundleView.bundleIcon.sprite = null;
            _confirmWindowView.priceText.text = mallItem.GetShowItemPrice();
            _confirmWindowView.priceText.rectTransform.sizeDelta =
                new Vector2(mallItem.currencyType == CurrencyType.Cash ? 245 : 74, 45);
            MallWindowUtil.UpdateCurrencyComponent(_currencyImage, mallItem.currencyType);
            MallWindowUtil.UpdateBuyButtonComponent(_buttonImage, mallItem.currencyType);
            if (mallItem.finalPrice == 0) {
                _currencyImage.gameObject.SetActive(false);
            }

            // 刷新界面
            switch (mallItem.MallItemType) {
                case MallItemType.Bundle:
                    FlushWindowStoreBundleType(mallItem);
                    break;
                case MallItemType.Unsupported:
                    Debug.LogError("Unsupported MallItemType");
                    break;
                default:
                    Debug.LogError($"Unhandled branch MallItemType {mallItem.MallItemType}");
                    break;
            }

            LoadBundle();
        }

        public override void HideView(params object[] args) {
            base.HideView(args);
            _buyButton.onClick.RemoveListener(ConfirmClick);
            _closeButton.onClick.RemoveListener(CancelClick);
            _state = State.FadeOut;
            _counter.ResetTime(fadeOutTime);

            SimpleEventManager.RemoveListener<StartLoadSceneEvent>(OnStartLoadSceneEvent);
            AssetBundleRefCountLoader.Instance.UnloadAssetBundle("mall_bundle", _bundleId);
        }

        private void OnStartLoadSceneEvent(StartLoadSceneEvent e) {
            OnClick_Close();
        }

        private void FlushWindowStoreBundleType(MallItem storeItem) {
            var mallItem = (MallItem_Bundle)storeItem;
            _confirmWindowView.bundleView.parent.SetActive(true);
            _confirmWindowView.bundleView.descScrollRect.normalizedPosition = Vector2.up;
            var imageContainerTransform = _confirmWindowView.bundleView.imageContainer.transform;
            if (imageContainerTransform.childCount > 0) {
                Destroy(imageContainerTransform.GetChild(0).gameObject);
            }

            _confirmWindowView.bundleView.bundleNameText.text =
                storeItem.GetShowItemTitle();
            if (MallUtility.IsBlinkBoxPackage(mallItem.Config) && mallItem.Config.GoodID == 7010030) {
                // 皮肤盲盒
                RefreshDetailPanel_7010030(mallItem);
            } else {
                RefreshDetailPanel(mallItem);
            }
        }

        private void RefreshDetailPanel_7010030(MallItem_Bundle mallItem) {
            var newObject = Instantiate(_descItemViews[0].itemObject,
                _confirmWindowView.bundleView.bundleDescParentTransform);
            var viewItem = new BundleDescItemView(newObject);
            foreach (var itemView in _descItemViews) {
                itemView.itemObject.SetActive(false);
            }

            _descItemViews.Add(viewItem);
            var itemName = ScriptLocalization.Get("mall/item_randomSkin", "#随机皮肤");
            viewItem.SetShowView(null, itemName, 1, "");
            viewItem.itemObject.SetActive(true);
        }

        private void RefreshDetailPanel(MallItem_Bundle mallItem) {
            var bundleItems = mallItem.items.Select(x => x).ToList();
            if (_descItemViews.Count < bundleItems.Count) {
                var needAmount = bundleItems.Count - _descItemViews.Count;
                for (int i = 0; i < needAmount; i++) {
                    var newObject = Instantiate(_descItemViews[0].itemObject,
                        _confirmWindowView.bundleView.bundleDescParentTransform);
                    _descItemViews.Add(new BundleDescItemView(newObject));
                }
            }

            _hasOwnedItem = false;

            // 武器碎片盲盒才排序
            bundleItems.Sort((a, b) => {
                if (!DataMgr.MallData.weaponFragmentDict.TryGetValue(a.config.key, out WeaponFragment aValue)) {
                    return 0;
                }

                if (!DataMgr.MallData.weaponFragmentDict.TryGetValue(b.config.key, out WeaponFragment bValue)) {
                    return 0;
                }

                var aOrder = aValue.GoodOrder;
                var bOrder = bValue.GoodOrder;
                return bOrder - aOrder;
            });
            bundleItems.Sort((a, b) => {
                var aHasOwned = ItemUtility.HasOwnItem(a.id);
                var bHasOwned = ItemUtility.HasOwnItem(b.id);
                if (aHasOwned && !bHasOwned) {
                    return 1;
                }

                if (!aHasOwned && bHasOwned) {
                    return -1;
                }

                return 0;
            });

            for (var i = 0; i < _descItemViews.Count; i++) {
                var viewItem = _descItemViews[i];
                if (i < bundleItems.Count) {
                    var bundleItem = bundleItems[i];
                    var id = bundleItem.id;
                    GameItem gameItem = new GameItem(id);
                    var key = gameItem.key;
                    var ty = gameItem.ty;

                    Sprite sprite = null;
                    var itemType = ItemUtility.GetItemType(key);
                    if (itemType != ItemType.Seed) {
                        sprite = MallUtility.GetSprite(key);
                    }

                    string itemName = ItemUtility.GetColoredItemTitle(key);
                    string itemTypeName = ItemUtility.GetTypeNameKey(itemType);
                    itemTypeName = ScriptLocalization.Get(itemTypeName);
                    int count = bundleItem.count;
                    if (ty == ItemType.HeroSkin) {
                        var success = ItemUtility.GetHeroSkinInfo(key, out emHero hero, out _);
                        if (!success) {
                            Debug.LogError($"HasOwnItem Failed to GetHeroSkinInfo {key}");
                            viewItem.itemObject.SetActive(false);
                            return;
                        }

                        itemName = $"{itemName} - {NameUtil.GetHeroName(hero)}";
                    } else if (ty == ItemType.HeroSkill) {
                        var success = ItemUtility.GetHeroSkillInfo(key, out emHero hero, out _);
                        if (!success) {
                            Debug.LogError($"HasOwnItem Failed to GetHeroSkinInfo {key}");
                            viewItem.itemObject.SetActive(false);
                            return;
                        }

                        itemName = $"{itemName} - {NameUtil.GetHeroName(hero)}";
                    } else if (ty == ItemType.MultiRoomSkin) {
                        itemName = $"{itemName} - {ScriptLocalization.Get("mall/itemtype_skin_multiroom")}";
                    } else if (ty == ItemType.WeaponBlueprintFragment) {
                        count = WeaponEvolutionModule.GetWeaponFragmentMaterialLeftCount(key);
                        itemName = NameUtil.GetWeaponFragmentNameWithoutType(id);
                        // color
                        var weaponId = id.Replace(WeaponEvolutionModule.WeaponBlueprintFragmentPrefix, "");
                        int level = UIForge.GetWeaponLevel(weaponId);
                        if (!ItemUtility.HasOwnItem(key, itemType)) {
                            itemName = itemName.ToColoredText((ItemLevel)level);
                        }

                        itemName = string.IsNullOrEmpty(itemName) ? $"#{key}" : itemName;
                    }

                    if (!string.IsNullOrEmpty(itemTypeName)) {
                        itemName = $"{itemTypeName}: {itemName}";
                    }

                    viewItem.SetShowView(sprite, itemName, count, key);

                    viewItem.itemObject.SetActive(true);
                } else {
                    viewItem.itemObject.SetActive(false);
                }
            }
        }

        public void ConfirmClick() {
            _state = State.None;
            RGMusicManager.GetInstance().PlayEffect(RGMusicManager.emEffect.Button);
            if (_hasOwnedItem) {
                var context = ScriptLocalization.Get("mall/ui_ask_package_gem_convert", "?部分已拥有商品将更换成宝石\n是否确认购买？");
                var title = ScriptLocalization.Get("I_tip");
                UIWindowDialog.ShowDialog(UIManager.FindCanvasRoot(), title, context,
                    () => { BuyItem(); }, null, 0, false, useAb: false, showMask: true);
            } else {
                BuyItem();
            }
        }

        private void BuyItem() {
            var checkResult = DataMgr.MallData.BuyItemPreCheck(_mallItem);
            if (checkResult == MallPayResult.CurrencyNotEnough) {
                MallWindowUtil.HandleCurrencyNotEnough(_mallItem);
                onBuyCallback2?.Invoke(MallData.MallBuyBundleResult.NotEnoughCurrency);
                return;
            }

            if (checkResult == MallPayResult.Timeout) {
                MallWindowUtil.ShowTimeOutTip(_mallItem);
                onBuyCallback2?.Invoke(MallData.MallBuyBundleResult.OtherError);
                OnClick_Close();
                return;
            }

            if (checkResult == MallPayResult.NotNetTime) {
                NetTime.UpdateNetTime();
                UICanvas.GetInstance().ShowTempMessage("force_patch/error_network", 3.0f);
                onBuyCallback2?.Invoke(MallData.MallBuyBundleResult.OtherError);
                return;
            }

            if (checkResult != MallPayResult.Success) {
                if (checkResult == MallPayResult.HasOwnItem) {
                    UICanvas.GetInstance().ShowTempMessage("multi_room_skin_ui_owned", 3.0f);
                    onBuyCallback2?.Invoke(MallData.MallBuyBundleResult.HasOwnItem);
                } else if (_mallItem.isMallItem && MallUtility.IsBlinkBoxPackage(_mallItem.Config) &&
                           checkResult == MallPayResult.MeetMaxPurchaseTime) {
                    var text = ScriptLocalization.Get("mall/reminder_soldout_limitedGood", "#已达限购上限");
                    UICanvas.GetInstance().ShowTempMessage(text, 3.0f);
                    onBuyCallback2?.Invoke(MallData.MallBuyBundleResult.OtherError);
                } else {
                    Debug.LogError($"Failed to buy Items, preCheck not pass reason: {checkResult}");
                    onBuyCallback2?.Invoke(MallData.MallBuyBundleResult.OtherError);
                }

                OnClick_Close();
                return;
            }

            // SimpleEventManager.RemoveListener<MallItemsUpdateEvent>(OnMallItemsUpdateEvent);

            onBuyCallback2?.Invoke(MallData.MallBuyBundleResult.PreBuy);
            MallPayResult mallPayResult = _mallItem.BuyItem(_isFromSubPage, (success) => {
                if (success) {
                    onBuyCallback2?.Invoke(MallData.MallBuyBundleResult.AfterBuySuccess);
                } else {
                    onBuyCallback2?.Invoke(MallData.MallBuyBundleResult.OtherError);
                }
            });
            if (mallPayResult != MallPayResult.Success && mallPayResult != MallPayResult.WaitingPayResult) {
                Debug.LogError($"Failed to buy Items, reason: {mallPayResult}");
            }

            OnClick_Close();
        }

        public void CancelClick() {
            RGMusicManager.GetInstance().PlayEffect(RGMusicManager.emEffect.Button);
            _state = State.FadeOut;
            _counter.ResetTime(fadeOutTime);
            _mallItem.CancelBuyItem(_isFromSubPage);
        }

        private string GetBlindBoxText() {
            var hasSkin = false;
            var isAllWeaponFragment = true;
            foreach (var item in _mallItem.itemList) {
                var key = item.id;
                var itemType = ItemUtility.GetItemType(key);
                if (itemType == ItemType.HeroSkin) {
                    hasSkin = true;
                    break;
                }

                if (itemType != ItemType.WeaponBlueprintFragment) {
                    isAllWeaponFragment = false;
                }
            }

            if (hasSkin) {
                return GetSkinBlindBoxText();
            }

            if (isAllWeaponFragment) {
                return GetWeaponFragmentBlindBoxText();
            }

            return null;
        }

        private string GetWeaponFragmentBlindBoxText() {
            var content2 = ScriptLocalization.Get("weapon_blueprint_fragment_blindbox_rule_2",
                "#武器碎片可通过解锁该武器锻造资格后，在普通关卡击败首领获取，请谨慎购买。");
            var content3 = ScriptLocalization.Get("weapon_blueprint_fragment_blindbox_rule_4",
                "#本期新增可进化武器的碎片会优先掉落，概率为{0}%");
            content3 = string.Format(content3, 50);
            var content0 = ScriptLocalization.Get("weapon_blueprint_fragment_blindbox_rule_0", @"#宝箱规则：
1. 购买本商品会随机获取一个武器的武器碎片；
2. 武器碎片将仅从本商品的武器列表中随机获取，已达到上限的武器碎片不会重复掉落；
3.武器碎片达到上限后，将解锁该武器的锻造资格；
4. 除了购买本商品，当武器已解锁锻造资格时，在单人关卡模式中击败首领也有概率获取武器碎片；
5. 当一个品阶的全部武器的所有碎片都被抽取完毕，其概率会按照权重比例分配给其他品阶的武器。

概率公示：
红色武器碎片 - {0}%
橙色武器碎片 - {1}%
紫色武器碎片 - {2}%
蓝色武器碎片 - {3}%
绿色武器碎片 - {4}%
白色武器碎片 - {5}%");
            content0 = string.Format(content0, 5, 10, 15, 15, 25, 30);
            var content1 = ScriptLocalization.Get("weapon_blueprint_fragment_blindbox_rule_1", "#武器列表\n{0}");
            content1 = content1.Replace("{0}", "");

            var content = content2 + "\n\n" + content3 + "\n\n" + content0 + "\n\n" + content1;
            string fragmentFormatStr = ScriptLocalization.Get("weapon_blueprint_fragment_2", "#武器碎片【{0}】");
            foreach (var item in _mallItem.itemList) {
                var fragmentKey = item.id;
                string itemName = ItemUtility.GetColoredItemTitle(fragmentKey);
                var itemType = ItemUtility.GetItemType(fragmentKey);
                if (itemType == ItemType.WeaponBlueprintFragment) {
                    var weaponKey = fragmentKey.Replace(WeaponEvolutionModule.WeaponBlueprintFragmentPrefix, "");
                    itemName = string.Format(fragmentFormatStr, NameUtil.GetWeaponName(weaponKey));
                    itemName = itemName.ToColoredText((ItemLevel)UIForge.GetWeaponLevel(weaponKey));
                    itemName += $" \u00d7 {WeaponEvolutionModule.GetFragmentNeedCount(weaponKey)}";
                    var hasOwnItem = ItemUtility.HasOwnItem(fragmentKey, itemType);
                    if (hasOwnItem) {
                        itemName = itemName.RemoveColorText();
                        itemName =
                            $"<color=#808080>{itemName}</color>({ScriptLocalization.Get("multi_room_skin_ui_owned")})";
                    }
                }

                content += $"{itemName}\n";
            }

            return content;
        }

        private string GetSkinBlindBoxText() {
            var content0 = ScriptLocalization.Get("mall/item_pkg_blind_rule_1", "#盲盒规则");
            var content1 = "\n1." +
                           ScriptLocalization.Get("mall/item_pkg_blind_rule_2", "#购买本盲盒会随机抽到以下皮肤列表中的任意一款玩家未拥有的皮肤");
            var content2 = "\n2." +
                           ScriptLocalization.Get("mall/item_pkg_blind_rule_3", "#不会重复抽到一样皮肤，也不会抽到玩家本已拥有的皮肤");
            var content3 = "\n3." +
                           ScriptLocalization.Get("mall/item_pkg_blind_rule_4", "#所有皮肤概率一致");
            var content4 = "\n4." +
                           ScriptLocalization.Get("mall/item_pkg_blind_rule_5", "#本商品第一抽享超半价购买享优惠，之后恢复原价。此优惠为假日限时专属");
            var content5 = "\n5." +
                           ScriptLocalization.Get("mall/item_pkg_blind_rule_6", "#已抽取的结果产生后，不会因为存档回退而改变");

            var content6 = ScriptLocalization.Get("mall/item_pkg_blind_skinList",
                "#\n皮肤列表");

            var content = content0 + content1 + content2 + content3 + content4 + content5 + "\n\n" + content6 + "\n";
            foreach (var item in _mallItem.itemList) {
                var key = item.id;
                string itemName = ItemUtility.GetColoredItemTitle(key);
                var itemType = ItemUtility.GetItemType(key);
                if (ItemUtility.IsLimitedOwnedItemType(itemType)) {
                    var hasOwnItem = ItemUtility.HasOwnItem(key, itemType);
                    var success = ItemUtility.GetHeroSkinInfo(key, out emHero hero, out int skinIndex);
                    if (!success) {
                        Debug.LogError($"HasOwnItem Failed to GetHeroSkinInfo {key}");
                        return null;
                    }

                    if (itemType == ItemType.HeroSkin) {
                        itemName = $"{itemName} - {NameUtil.GetHeroName(hero)}";
                    } else if (itemType == ItemType.WeaponSkin) {
                    }

                    if (hasOwnItem) {
                        itemName = itemName.RemoveColorText();
                        itemName =
                            $"<color=#808080>{itemName}</color>({ScriptLocalization.Get("multi_room_skin_ui_owned")})";
                    }
                }

                content += $"{itemName}\n";
            }

            return content;
        }

        // private void OnMallItemsUpdateEvent(MallItemsUpdateEvent e) {
        //     if (!DataMgr.MallData.CheckMallItemAvailable(_mallItem)) {
        //         OnClick_Close();
        //     }
        // }

        private enum State {
            None,
            FadeIn,
            Idle,
            FadeOut,
        }

        private class BundleDescItemView {
            public GameObject itemObject;
            public Transform groupTf1;
            public Transform groupTf2;
            public Transform nameRootTf;
            public Transform arrowRootTf;
            public Transform iconCoverTf;

            public Text nameText;

            // 角色未解锁
            public Text heroUnlockText;
            public Text countText;
            public Text currencyCount;
            public Text arrowText; // 兑换成
            private Transform _iconRoot;
            private UI.Widget.Item _item;
            public Image icon;
            public Image subIcon;
            public Image arrowImg;
            public Image currencyIcon;
            public Image lineImg;

            public BundleDescItemView(GameObject item) {
                itemObject = item;
                _iconRoot = itemObject.transform.Find("iconRoot");
                _item = _iconRoot.Find("item").GetComponent<UI.Widget.Item>();
                icon = _iconRoot.Find("icon").GetComponent<Image>();
                subIcon = _iconRoot.Find("subIcon").GetComponent<Image>();
                iconCoverTf = _iconRoot.Find("iconCover");

                groupTf1 = itemObject.transform.Find("group1");
                groupTf2 = itemObject.transform.Find("group2");

                nameRootTf = groupTf1.Find("nameRoot");
                nameText = nameRootTf.Find("name").GetComponent<Text>();
                heroUnlockText = nameRootTf.Find("heroUnlock").GetComponent<Text>();
                countText = groupTf1.Find("count").GetComponent<Text>();

                arrowRootTf = groupTf2.Find("ArrowRoot");
                arrowText = arrowRootTf.Find("ui_text").GetComponent<Text>();
                arrowText.text = ScriptLocalization.Get("mall/ui_convert_to", "#兑换成");
                currencyIcon = groupTf2.Find("currency_icon").GetComponent<Image>();
                currencyCount = groupTf2.Find("currency_count").GetComponent<Text>();
                lineImg = nameText.transform.Find("line").GetComponent<Image>();

                ShowHeroUnlockSwitch();
            }

            public void ShowHeroUnlockSwitch() {
                heroUnlockText.gameObject.SetActive(false);
            }

            public void SetConvertView(Sprite itemIcon, CurrencyType currencyType, int count) {
                groupTf1.gameObject.SetActive(false);
                groupTf2.gameObject.SetActive(true);
                iconCoverTf.gameObject.SetActive(true);

                icon.sprite = itemIcon;
                subIcon.enabled = false;
                currencyIcon.sprite = SpriteUtility.GetCurrencySprite(currencyType);
                currencyCount.text = count.ToString();
            }

            public void SetShowView(Sprite itemIcon, string itemName, int count, string key) {
                groupTf1.gameObject.SetActive(true);
                groupTf2.gameObject.SetActive(false);
                iconCoverTf.gameObject.SetActive(false);
                lineImg.gameObject.SetActive(false);
                _item.gameObject.SetActive(false);
                icon.gameObject.SetActive(true);
                subIcon.gameObject.SetActive(true);
                var disableColor = new Color(0.6f, 0.6f, 0.6f, 1);
                nameText.color = Color.white;
                countText.color = Color.white;
                icon.color = Color.white;

                bool isValidItem = !string.IsNullOrEmpty(key);
                string nameTextStr = itemName;
                bool handled = false;
                if (isValidItem) {
                    var itemType = ItemUtility.GetItemType(key);
                    if (itemType == ItemType.Seed) {
                        var itemConfig = ItemConfigLoader.GetItemConfig(key);
                        subIcon.enabled = true;
                        icon.sprite = itemConfig?.Icon.GetSprite();
                        subIcon.sprite = itemConfig?.Background.GetSprite();
                        handled = true;
                    } else if (itemType == ItemType.WeaponBlueprintFragment) {
                        // var itemConfig = ItemConfigLoader.GetItemConfig(key);
                        icon.gameObject.SetActive(false);
                        subIcon.gameObject.SetActive(false);
                        _item.gameObject.SetActive(true);
                        _item.SetItem(key);
                        handled = true;
                    }


                    var own = ItemUtility.HasOwnItem(key, itemType);
                    lineImg.gameObject.SetActive(own);
                    if (own) {
                        nameText.color = disableColor;
                        countText.color = disableColor;
                        icon.color = itemIcon == null ? Color.clear : disableColor;
                    }
                }

                if (!handled) {
                    icon.sprite = itemIcon;
                    icon.color = itemIcon == null ? Color.clear : Color.white;
                    subIcon.enabled = false;
                }

                nameText.text = nameTextStr;
                countText.text = $"\u00d7 {count}";
            }
        }

#if UNITY_EDITOR

        [Title("模拟购买工具 (Editor Only)")] [LabelText("模拟次数"), MinValue(1)]
        public int simulateCount = 10;

        [Button("模拟购买一次", ButtonSizes.Large)]
        private void SimulateBuyOnce() {
            Debug.Log(SimulatePurchaseAndStat(1));
        }

        [Button("模拟购买 N 次", ButtonSizes.Large)]
        private void SimulateBuyMany() {
            Debug.Log(SimulatePurchaseAndStat(simulateCount));
        }

        [Button("实际购买 N 次 (真实逻辑)", ButtonSizes.Large)]
        private void RealBuyMany() {
            Debug.Log(ExecuteRealPurchase(simulateCount));
        }

        private string SimulatePurchaseAndStat(int times) {
            if (_mallItem == null) {
                return "⚠ 未绑定 MallItem，无法模拟。";
            }

            var sb = new StringBuilder();
            var stat = new Dictionary<string, int>();

            sb.AppendLine($"==== 购买模拟（{times} 次）====");

            for (int i = 0; i < times; i++) {
                var pre = DataMgr.MallData.BuyItemPreCheck(_mallItem);
                if (pre != MallPayResult.Success) {
                    sb.AppendLine($"[第 {i + 1} 次] 预检失败: {pre}");
                    continue;
                }

                string dropKey = GetRandomDropKeyByGameRule(_mallItem);
                if (string.IsNullOrEmpty(dropKey)) {
                    sb.AppendLine($"[第 {i + 1} 次] 未抽取到有效物品");
                    continue;
                }

                string dropName = ItemUtility.GetColoredItemTitle(dropKey).RemoveColorText();
                stat.TryAdd(dropName, 0);
                stat[dropName]++;
            }

            sb.AppendLine("---- 结果分布 ----");
            foreach (var kv in stat) {
                float p = kv.Value * 100f / times;
                sb.AppendLine($"{kv.Key}: {kv.Value} 次 ({p:F2}%)");
            }

            sb.AppendLine("==== 结束 ====");
            return sb.ToString();
        }

        private string ExecuteRealPurchase(int times) {
            if (_mallItem == null) {
                return "⚠ 未绑定 MallItem，无法执行购买。";
            }

            int success = 0, fail = 0;
            var sb = new StringBuilder();
            var stat = new Dictionary<string, int>();
            sb.AppendLine($"==== 批量真实购买（{times} 次）====");

            for (int i = 0; i < times; i++) {
                var pre = DataMgr.MallData.BuyItemPreCheck(_mallItem);
                if (pre != MallPayResult.Success) {
                    sb.AppendLine($"[第 {i + 1} 次] 预检失败: {pre}");
                    fail++;
                    continue;
                }

                // 调用正式购买流程（会真实扣费 / 写存档，请谨慎）
                MallPayResult payResult = _mallItem.BuyItem(_isFromSubPage, null);
                if (payResult == MallPayResult.Success || payResult == MallPayResult.WaitingPayResult) {
                    success++;
                    // 按线上算法推算本次掉落（方便统计概率）
                    string dropKey = GetRandomDropKeyByGameRule(_mallItem);
                    string dropName = ItemUtility.GetColoredItemTitle(dropKey).RemoveColorText();
                    stat.TryAdd(dropName, 0);
                    stat[dropName]++;
                } else {
                    sb.AppendLine($"[第 {i + 1} 次] 购买失败: {payResult}");
                    fail++;
                }
            }

            sb.AppendLine($"成功: {success} 次, 失败: {fail} 次");
            if (stat.Count > 0) {
                sb.AppendLine("---- 掉落统计 ----");
                foreach (var kv in stat) {
                    float p = kv.Value * 100f / Mathf.Max(1, success);
                    sb.AppendLine($"{kv.Key}: {kv.Value} 次 ({p:F2}%)");
                }
            }

            sb.AppendLine("==== 结束 ====");
            return sb.ToString();
        }

        private static string GetItemKey(object obj) {
            if (obj == null) return null;

            var type = obj.GetType();

            if (obj is MallGameItem mg) return mg.id;

            const BindingFlags flags = BindingFlags.Public | BindingFlags.NonPublic |
                                       BindingFlags.Instance | BindingFlags.IgnoreCase;

            var fi = type.GetField("id", flags) ?? type.GetField("Item", flags);
            if (fi != null) return fi.GetValue(obj) as string;

            var pi = type.GetProperty("id", flags) ?? type.GetProperty("Item", flags);
            if (pi != null) return pi.GetValue(obj, null) as string;

            Debug.LogError($"GetItemKey: 未找到字段/属性 id/Item，类型={type.FullName}");
            return null;
        }


        /// <summary>
        /// 根据游戏正式服的抽取规则返回掉落 key：
        /// 1. 按权重随机；
        /// 2. 若为盲盒/碎片宝箱等去重道具，则跳过已拥有条目；
        /// 3. 若去重后无可抽取条目，则退回完整池再次按权重抽取（与线上保持一致）。
        /// </summary>
        private string GetRandomDropKeyByGameRule(MallItem mallItem) {
            if (mallItem?.itemList == null || mallItem.itemList.Count == 0) {
                return null;
            }

            bool isBlindBox = MallUtility.IsBlinkBoxPackage(mallItem.Config);

            // —— Step1: 过滤池 ——
            var pool = new List<object>();
            foreach (var item in mallItem.itemList) {
                var key = item.id;
                if (isBlindBox) {
                    var t = ItemUtility.GetItemType(key);
                    bool own = ItemUtility.HasOwnItem(key, t);
                    if (own) {
                        continue; // 盲盒跳过已拥有
                    }
                }

                pool.Add(item);
            }

            if (pool.Count == 0) {
                pool.AddRange(mallItem.itemList);
            }

            float totalWeight = pool.Sum(GetWeight);
            if (totalWeight <= 0) {
                return null;
            }

            float rand = UnityEngine.Random.value * totalWeight;
            foreach (var item in pool) {
                rand -= GetWeight(item);
                if (rand <= 0) {
                    return GetItemKey(item);
                }
            }

            return GetItemKey(pool[^1]);
        }

        private static float GetWeight(object item) {
            const BindingFlags flags = BindingFlags.Public | BindingFlags.NonPublic |
                                       BindingFlags.Instance | BindingFlags.IgnoreCase;

            var fi = item.GetType().GetField("Weight", flags) ??
                     item.GetType().GetField("weight", flags);
            if (fi != null) return Convert.ToSingle(fi.GetValue(item));

            var pi = item.GetType().GetProperty("Weight", flags) ??
                     item.GetType().GetProperty("weight", flags);
            if (pi != null) return Convert.ToSingle(pi.GetValue(item));

            return 1f;
        }
#endif
    }
}