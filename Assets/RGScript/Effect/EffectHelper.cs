using ModeDefence;
using Sirenix.OdinInspector;
using UnityEngine;

namespace RGScript.Effect {
    public class EffectHelper : MonoBehaviour {
        private void Start() {
            Destroy(gameObject, 5);
        }

        // [Button(ButtonSizes.Medium)]
        // public void ChangeColor(int gradient_index) {
        //     var colt = GetComponent<ParticleSystem>().colorOverLifetime;
        //     colt.color = new ParticleSystem.MinMaxGradient(aura_gradients[gradient_index]);
        //     colt = transform.GetChild(0).GetComponent<ParticleSystem>().colorOverLifetime;
        //     colt.color = new ParticleSystem.MinMaxGradient(particle_gradients[gradient_index]);
        //     GetComponent<ParticleSystemRenderer>().material = aura_mats[gradient_index];
        //     transform.GetChild(0).GetComponent<ParticleSystemRenderer>().material = particle_mats[gradient_index];
        // }

        public static ParticleSystem CreateBeamUpEffect(Transform parent) {
            GameObject effectGo = ResourcesUtil.Load<GameObject>("RGTexture/iron_tide/effect/BeamUpYellow.prefab");
            var particleSys = Instantiate(effectGo, parent).GetComponent<ParticleSystem>();
            var sysRenderer = particleSys.GetComponent<Renderer>();
            return particleSys;
        }

        public static ParticleSystem CreateHealGreenEffect(Transform parent) {
            GameObject effectGo = ResourcesUtil.Load<GameObject>("RGTexture/iron_tide/effect/HealGreen.prefab");
            var particleSys = Instantiate(effectGo, parent).GetComponent<ParticleSystem>();
            var sysRenderer = particleSys.GetComponent<Renderer>();
            return particleSys;
        }

        public static ParticleSystem CreateRestoreEnergyEffect(Transform parent) {
            GameObject effectGo = ResourcesUtil.Load<GameObject>("RGTexture/iron_tide/effect/RestoreEnergy.prefab");
            var particleSys = Instantiate(effectGo, parent).GetComponent<ParticleSystem>();
            var sysRenderer = particleSys.GetComponent<Renderer>();
            return particleSys;
        }

        public static ParticleSystem CreateExploitFragmentEffect(Transform parent) {
            GameObject effectGo = ResourcesUtil.Load<GameObject>("RGTexture/iron_tide/effect/ExploitFragment.prefab");
            var particleSys = Instantiate(effectGo, parent).GetComponent<ParticleSystem>();
            var sysRenderer = particleSys.GetComponent<Renderer>();
            return particleSys;
        }
        public static Transform CreateThunderEffect(Transform parent,bool autoFitsize) {
            GameObject effectGo = ResourcesUtil.Load<GameObject>("RGPrefab/Effect/effect_thunder.prefab");
            var effectSpriteRender = effectGo.GetComponent<SpriteRenderer>();
            var originSize= effectSpriteRender.sprite.bounds.size;
            var originScale = effectGo.transform.localScale;
            var trans = Instantiate(effectGo, parent).transform;
            if (parent && autoFitsize) {
                var parentSpriteRender = parent.GetComponent<SpriteRenderer>();
                if (parentSpriteRender != null) {
                    if (parentSpriteRender.sprite != null) {
                        var sprite1 = parentSpriteRender.sprite;
                        var rect = sprite1.rect;
                        var size = sprite1.bounds.size;
                        trans.localScale = new Vector3(originScale.x*size.x/originSize.x,originScale.y*size.y/originSize.y,originScale.z);
                        // Debug.Log($"originSize {originSize}, originScale {originScale}, parentSpriteRenderSize {size}, sprite.pivot:{sprite1.pivot}");
                        trans.localPosition +=
                            new Vector3(0,(0.5f-sprite1.pivot.y/rect.y) * size.y,0);
                    } else {
                        trans.localScale = new Vector3(0, 1.3f, 0);
                        trans.localPosition = new Vector3(0.3523f, 0.2708f, 1);
                    }
                }
            }
            return trans;
        }
    }
}