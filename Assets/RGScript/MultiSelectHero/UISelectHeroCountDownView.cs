using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class UISelectHeroCountDownView : MonoBehaviour {
    public Text cdText; //倒计时文本
    public static int commonCd;
    private bool IsHost => RGNetWorkManager.GetInstance().IsServer();
    // private void Start() {
    //     commonCd = IsHost ? SelectHeroCountDownCtrl.HOST_COUNT_DOWN_TIME : SelectHeroCountDownCtrl.CLIENT_COUNT_DOWN_TIME;
    //     if (null == cdText) {
    //         cdText = GetComponent<Text>();
    //     }
    //     if (null != cdText) {
    //         cdText.text = commonCd.ToString();
    //     }
    // }

    private void OnEnable() {
        SimpleEventManager.AddEventListener<UpdateSelectHeroCDTimeEvent>(OnUpdateCDTime);
        // if (null != cdText) {
        //     cdText.text = commonCd.ToString();
        // }
        int restTime = 0;
        if (null != SelectHeroCountDownCtrl.Inst) {
            restTime = SelectHeroCountDownCtrl.Inst.restTime;
        }
        UpdateCDTime(restTime);
    }

    private void OnDisable() {
        SimpleEventManager.RemoveListener<UpdateSelectHeroCDTimeEvent>(OnUpdateCDTime);
    }

    private void OnUpdateCDTime(EventBase e) {
        var eParam = e as UpdateSelectHeroCDTimeEvent;
        UpdateCDTime(eParam.restTime);
        // LogUtil.Log($"倒计时{(int)restTime}秒");
    }

    void UpdateCDTime(int restTime) {
        if (restTime < 0) {
            return;
        }
        commonCd = restTime;
        cdText.text = restTime.ToString();
    }
}