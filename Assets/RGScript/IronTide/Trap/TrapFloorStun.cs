using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 地上的XX，周期造成移动障碍
/// </summary>
public class TrapFloorStun : TrapRegularEffect
{
    protected override void TodoEffect() {
        
        base.TodoEffect();
        RaycastHit2D[] result = null;
        var hitCount = PhysicsUtil.CheckBoxHit(transform.position, size * 2, 0, _seekEnemyMask, ref result);
        var rect = new Rect(transform.position - size.Vec3() * 0.5f, size);
        for(var i = 0; i < hitCount; i++){
            var objPos = result[i].transform.position;
            if(result[i].transform.gameObject.GetComponent<RGController>() is {} c){
                if(c.attributeDelegate != null){
                    objPos = c.attributeDelegate.transform.position;
                }
            }
            if(rect.Contains(objPos)){
                if(result[i].transform.GetComponent<RGController>() is {} rgController){
                    rgController.Dizzy(0.5f, false);
                }
                if(hitFx != null){
                    PrefabPool.Inst.Take(hitFx, result[i].point, Quaternion.identity, 1);
                }
            }
        }
    }
}
