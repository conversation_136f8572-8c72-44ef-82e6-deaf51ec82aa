using Cicada;
using DG.Tweening;
using UnityEngine;

namespace IronTide {
    /// <summary>
    /// 以自身为圆心，在 {0} 半径的圆范围内，将进入该范围的所有敌方子弹拉向机甲，
    /// 此时机甲附带特效随吸收子弹伤害增加而变化（光盾或其他发生颜色 / 形状等变化），
    /// 期间机甲不受到伤害，机甲的移动速度下降 {1}。吸收子弹阶段持续 {2} 秒，在此阶段结束后，以机甲位置为圆心，
    /// 向周围 360° 释放冲击波，冲击波对敌人造成的伤害为 {2}%*{持续时间吸收敌方子弹总伤害}
    /// </summary>
    public class IronTideSkillAirBender02 : IronTideSkillBase {
        private int totalAbsordDamage; // 吸收的总伤害

        // 配置
        public int maxAbsordDamage = 3; // 最大吸收的伤害
        public float speedSlowDown = 1; // 移动速度下降
        public float during = 3; // 吸收子弹阶段持续多少秒
        public float damagePercentage = 0.5f; // 总吸收伤害乘以的系数
        public float absordRadius = 7; // 拉子弹的圈半径

        public float damageFactor = 1;
        public float explodeRadius = 7; // 半径
        public int force = -100; // 吸引力
        public float immuneTime = 0.5f; // 盾结束后的无敌帧时间

        public GameObject prefab;
        public GameObject prefabDust;
        public PointEffector2D pointEffector;
        public CircleCollider2D circleCollider2D;
        public GameObject shield;
        private ShieldControl _shieldControl;
        public SpriteRenderer shieldSr;
        public override float timeToFinish => Duration;

        /// <summary>
        /// state:1 技能开始
        /// state:2 技能结束，恢复属性，释放冲击波
        /// </summary>
        public override bool IsFinished => _state == 3;

        int _state = 0;
        int _chargeCount = 0;

        private void Awake() {
            _shieldControl = new ShieldControl();
        }

        public override void ButtonDown(IronTideMechaController mc) {
            base.ButtonDown(mc);
            circleCollider2D.radius = absordRadius;
            mechaController = mc;
            if (_state == 0) {
                _state = 1;
                SimpleEventManager.AddEventListener<IronTideMechaControllerGetHurt>(OnGetHurt);
                SkillStart();
            }
        }

        private Tweener tweener;

        private void SkillStart() {
            totalAbsordDamage = 0;
            _passedTime = 0;
            _shieldControl.Init(shield.transform, maxAbsordDamage);
            mechaController.moveSpeed.AddModifier(new AttributeModifier(-speedSlowDown, AttributeModifierType.Flat,
                this));
            ChangeProtect(1);
#if UNITY_EDITOR
            Debug.Log($"SkillAirBender02 skillstart ++ {mechaController.temporarilyProtect}");
#endif
            pointEffector.gameObject.SetActive(true);
            if (tweener != null) {
                tweener.Kill();
                tweener = null;
            }

            tweener = shieldSr.DOFade(0, 1).SetLoops(-1, LoopType.Yoyo);
        }

        private void SkillEnd() {
            _state = 2;
            _shieldControl.Enable(false);
            if (tweener != null) {
                tweener.Kill();
                tweener = null;
            }

            if (immuneTime <= 0) {
                ChangeProtect(-1);
            } else {
                DOVirtual.DelayedCall(immuneTime, () => {
                    ChangeProtect(-1);
                });
            }
#if UNITY_EDITOR
            Debug.Log($"SkillAirBender02 SkillEnd -{1}= {mechaController.temporarilyProtect}");
#endif
            mechaController.moveSpeed.RemoveModifiers(this);
            pointEffector.gameObject.SetActive(false);
        }

        private void OnGetHurt(IronTideMechaControllerGetHurt e) {
            RGController driver = e.driver;
            if (mechaController.driver != driver) {
                return;
            }

            // int realDamage = e.realDamage;
            int damageBeforeConsumeShield = e.damageBeforeConsumeShield;

            AbsordDamage(damageBeforeConsumeShield);
        }

        void AbsordDamage(int damage) {
            totalAbsordDamage += damage;
            _shieldControl.ProcessDamage(damage);
#if UNITY_EDITOR
            Debug.Log($"SkillAirBender02 AbsordDamage +{damage}={totalAbsordDamage}");
#endif

            if (totalAbsordDamage >= maxAbsordDamage) {
                SkillEnd();
                return;
            }

            // RGMusicManager.Inst.PlayEffect(skillInfo.audioClip);
        }

        private void ChangeProtect(int change) {
            mechaController.temporarilyProtect += change;
            Debug.Log($"SkillAirBender02 ChangeProtect change: {change} => {mechaController.temporarilyProtect}");
            mechaController.UpdateShieldFX();
        }

        public override void ButtonUp() {
        }

        void UpdateSkillCount() {
            try {
                (RGGameProcess.Inst.modeProcess as IronTideGameModeProcess)?.DisplaySkillCount(_chargeCount);
            } catch { }
        }

        void HideSkillCount() {
            try {
                (RGGameProcess.Inst.modeProcess as IronTideGameModeProcess)?.DisplaySkillCount(0);
            } catch { }
        }

        float _passedTime = 0;

        public override void FixedUpdateSkill(float dt) {
            if (_state == 1) {
                _passedTime += dt;
                if (_passedTime >= during) {
                    SkillEnd();
                    return;
                }
            } else if (_state == 2) {
                RGMusicManager.Inst.PlayEffect(castSound);
                var bulletInfo = new BulletInfo();
                bulletInfo.SetCamp(camp);
                bulletInfo.SetBullet(prefab);
                bulletInfo.SetBulletSize(explodeRadius * mechaController.skillRangeFactor.Value);
                bulletInfo.SetSourceObject(mechaController.driver.gameObject);
                bulletInfo.SetDuration(2);
                bulletInfo.SetPosition(transform.position);
                var damageInfo = new DamageInfo();
                damageInfo.SetCamp(camp);
                damageInfo.SetDamage(Mathf.RoundToInt(totalAbsordDamage * damagePercentage));
                BulletFactory.TakeBullet(bulletInfo, damageInfo);
                _state = 3;
            }
        }


        public override void DoCleanUp() {
            base.DoCleanUp();
            SimpleEventManager.RemoveListener<IronTideMechaControllerGetHurt>(OnGetHurt);
        }

        class ShieldControl {
            SpriteRenderer shield;
            private Vector3 shieldScale;
            private int fullDamage;
            private int curDamage;
            bool enabled = true;
            private Color _color;
            private Tween tween;

            public void Enable(bool b) {
                enabled = b;
                if (!enabled)
                    shield.gameObject.SetActive(false);
                else {
                    shield.transform.localScale = Vector3.zero;
                    shield.transform.DOScale(shieldScale, 0.25f);
                    shield.gameObject.SetActive(true);
                }
            }

            public void Init(Transform shieldTf, int fullDamage) {
                this.fullDamage = fullDamage;
                this.curDamage = fullDamage;
                if (shield == null) {
                    shield = shieldTf.gameObject.GetComponent<SpriteRenderer>();
                    _color = shield.color;
                    shieldScale = shield.transform.localScale;
                }

                if (shield != null) {
                    shield.gameObject.SetActive(true);
                }
            }

            public void ProcessDamage(int damage) {
                curDamage -= damage;
                curDamage = Mathf.Max(0, fullDamage);
                if (curDamage == 0) {
                    Enable(false);
                    return;
                } else {
                    if (tween != null) {
                        tween.Kill();
                    }

                    float alpha = curDamage / (float)fullDamage;
                    tween = shield.DOColor(new Color(1, 0, 0, alpha), 0.2f);
                    tween.onComplete = () => {
                        SetColor(_color);
                    };
                }
            }

            private void SetColor(Color color) {
                float alpha = curDamage / (float)fullDamage;
                color.a = alpha;
                shield.color = color;
            }
        }
    }
}