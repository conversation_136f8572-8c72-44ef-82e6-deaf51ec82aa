using Cicada;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace IronTide {
    /// <summary>
    /// 机械狂潮 刺客技能
    /// </summary>
    public class IronTideSkillAssassin01 : IronTideSkillBase {
        public float addSpeed = 0.35f;
        public int addCrit = 100;
        Transform _fxSpeed;

        public override void ButtonDown(IronTideMechaController mc) {
            base.ButtonDown(mc);
            _fxSpeed = transform.Find("ps_speed");
            if (mechaController != null) {
                mechaController.ignoreDamage = true;
                mechaController.moveSpeed.AddModifier(new AttributeModifier(addSpeed, AttributeModifierType.Flat,
                    this));
                mechaController.criticRate.AddModifier(new AttributeModifier(addCrit / 100f, AttributeModifierType.Flat,
                    this));
            }
        }

        public override void FixedUpdateSkill(float dt) {
            base.FixedUpdateSkill(dt);
            if (mechaController != null) {
                _fxSpeed.transform.right = mechaController.ironTideRoleController.latestMoveDir;
            }
        }

        public override void DoCleanUp() {
            base.DoCleanUp();
            if (mechaController != null) {
                mechaController.ignoreDamage = false;
                mechaController.moveSpeed.RemoveModifiers(this);
                mechaController.criticRate.RemoveModifiers(this);
            }
        }
    }
}