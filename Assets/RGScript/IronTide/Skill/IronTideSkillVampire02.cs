using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace IronTide {
    /// <summary>
    /// 机械狂潮 吸血鬼 技能(2)
    /// </summary>
    public class IronTideSkillVampire02 : IronTideSkillBase {
        public GameObject bulletPrefab;
        public float damageFactor = 0.3f;
        public float size = 1.5f;
        bool _isFinished = false;
        public override bool IsFinished => _createdHole && _holeEnd;

        bool _createdHole = false;
        bool _holeEnd = false;

        public override void ButtonDown(IronTideMechaController mc) {
            base.ButtonDown(mc);
            var controller = mechaController.driver;
            GameObject skill_obj_proto = bulletPrefab;
            int damage = Mathf.RoundToInt(GetAverageWeaponDamage() * damageFactor);
            BulletInfo bulletInfo = new BulletInfo().SetUp(skill_obj_proto, gameObject, 38,
                    transform.position + new Vector3(0, 0.5f, 0),
                    controller.facing < 0 ? 180 - controller.FixedAngle : controller.FixedAngle, camp)
                .SetBulletSize(size * mechaController.skillRangeFactor.Value).SetSourceObject(mechaController.mechaDriver.gameObject);
            DamageInfo damageInfo = new DamageInfo().SetUp(skill_obj_proto, damage, 0, 0, camp);
            var temp_obj = BulletFactory.TakeBullet(bulletInfo, damageInfo, false);

            var subBulletCreator = temp_obj.GetComponentInChildren<RGBTCreate>();
            subBulletCreator.creationTime = Duration;

            foreach (var item in temp_obj.GetComponentsInChildren<IPrefabPoolObject>()) {
                item.OnTaken();
            }

            subBulletCreator.OnChildCreate -= Skill1ExtraBulletProcess;
            subBulletCreator.OnChildCreate += Skill1ExtraBulletProcess;
        }

        private void Skill1ExtraBulletProcess(RGBTCreate rgbtCreate, GameObject createdObject) {
            if(mechaController != null){
                createdObject.transform.localScale = Vector3.one * (size * mechaController.skillRangeFactor.Value);
                rgbtCreate.OnChildCreate -= Skill1ExtraBulletProcess;
                createdObject.transform.localScale *= 1.3f;
                var blackHole = createdObject.GetComponentInChildren<BloodHoleTrigger>();
                if (blackHole == null) {
                    return;
                }

                _createdHole = true;
                blackHole.damage = Mathf.RoundToInt(GetAverageWeaponDamage() * damageFactor);
                createdObject.GetComponent<RGBullet>().destroyCallback += (bulletObj) => {
                    _holeEnd = true;
                };
            }
        }
    }
}