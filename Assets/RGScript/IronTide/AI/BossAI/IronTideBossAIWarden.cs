using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using CommandUtil;

namespace IronTide{
    public class IronTideBossWardenParam{
        public float initWait = 4; // 初始等待时间
        
        public int machGunDmg = 12; // 机枪伤害
        public float machGunDmgIntv = 0.25f; // 机枪伤害间隔
        public float machGunPrepareWait = 1f; // 机枪扫射开始前等待 
        public float machGunStartWait = 0.75f; // 机枪扫射开始停留时间
        public float machGunSpeed = 10; // 机枪扫射平移速度
        public float machGunDmgSize = 1; // 机枪扫射伤害范围

        public int splitterDamage = 10;
        public float splitterChargeTime = 1.5f;
        public int splitterCount1 = 6;
        public float splitterAngleInterval1 = 60;
        public int splitterCount2 = 6;
        public float splitterAngleInterval2 = 9;
        public int splitterEmitTimes = 2;


        public float beamPrepareDuration = 1;
        public float beamSweepDuration = 4f;
        public float beamEndDuration = 2;
        public int beamDmg = 20;


        public int missileDmg = 5; // 导弹伤害
        public int missleCount = 20;
        public int dropBoxCount = 4;
        public int boxHp = 250;
        public float missileCooldown = 10;



        public float bondSpeed = 1.8f; // 纽带速度
        public float bondSpeedUpInterval = 10;
        public float bondSpeedUpDuration = 8;
        public float bondSpeedUpAmount = 0.15f;

        public float commonActionInterval = 1; // 通用动作间隔

        public float supplymentInterval = 4; // 增援间隔
    }


    public class IronTideBossAIWarden : IronTideAIBoss<IronTideBossWardenParam>{

        public enum State{
            Random,
            Idle,
            MachGun,
            Splitter,
            Beam,
            WalkAndMissile,
        }

        public class IronTideBossAIWardenData{
            public RGRandom rg_random;
            public float nextActionWait = 0; // 下次行动时间
            public State nextActionState = State.Random;
            public System.Func<IronTideBossWardenFight> GetBossFightInterfaceFunc;

            public List<Transform> weaponTransform = new List<Transform>();
            public List<ObjectFloat.Info> weaponFloatingInfo = new List<ObjectFloat.Info>();
            public List<bool> weaponAttacking = new List<bool>();
            public List<GameObject> boxOnBond = null;
            public float supplymentCountdown;
            public float latestEmitMissilesTime; // 最近一次发生导弹(掉箱子)的时间

            public IronTideBossWardenFight GetBossWardenFight(){
                return GetBossFightInterfaceFunc?.Invoke();
            }
        }

        public IronTideBossAIWardenData bossData;

        public IronTideBossWardenFight bossFightInterface;

        public override void Init(CharacterAIData aiData) {
            base.Init(aiData);

            bossData = new IronTideBossAIWardenData(){ 
                rg_random = ai.character.rg_random,
                supplymentCountdown = bossAIParam.supplymentInterval,
                GetBossFightInterfaceFunc = ()=> bossFightInterface
            };

            var weaponRoot = ai.character.transform.Find("img/img/body");
            bossData.weaponTransform.Add(weaponRoot.transform.Find("w0"));
            bossData.weaponFloatingInfo.Add(ObjectFloat.CreateFloatInfo(bossData.weaponTransform[0].gameObject, 0.5f, 3.5f));
            bossData.weaponAttacking.Add(false);
            bossData.weaponTransform.Add(weaponRoot.transform.Find("w1"));
            bossData.weaponFloatingInfo.Add(ObjectFloat.CreateFloatInfo(bossData.weaponTransform[1].gameObject, 0.5f, 3.5f));
            bossData.weaponAttacking.Add(false);
            bossData.weaponTransform.Add(weaponRoot.transform.Find("w2"));
            bossData.weaponFloatingInfo.Add(ObjectFloat.CreateFloatInfo(bossData.weaponTransform[2].gameObject, 0.5f, 3.5f));
            bossData.weaponAttacking.Add(false);
            bossData.weaponTransform.Add(weaponRoot.transform.Find("w3"));
            bossData.weaponFloatingInfo.Add(ObjectFloat.CreateFloatInfo(bossData.weaponTransform[3].gameObject, 0.5f, 3.5f));
            bossData.weaponAttacking.Add(false);

            var commonUpdateMono = ai.character.gameObject.AddComponent<CommonUpdateMono>();
            commonUpdateMono.cmdTag = "update_arms";
            commonUpdateMono.onUpdate = (dt)=>{
                // 更新机械臂浮动位置
                for(var i = 0; i < bossData.weaponAttacking.Count; ++i){
                    if(!bossData.weaponAttacking[i]){
                        bossData.weaponFloatingInfo[i].Udpate(dt);
                    }
                }
            };

            
            AddState((int)State.Idle, (new Idle()).Init(bossData), aiData);
            AddState((int)State.MachGun, (new MachGun()).Init(bossData), aiData);
            AddState((int)State.Splitter, (new Splitter()).Init(bossData), aiData);
            AddState((int)State.Beam, (new Beam()).Init(bossData), aiData);
            AddState((int)State.WalkAndMissile, (new WalkAndMissile()).Init(bossData), aiData);
        }

        /// <summary>
        /// 定时小怪增援
        /// </summary>
        /// <param name="dt"></param>
        void UpdateSupplyment(float dt){
            bossData.supplymentCountdown -= dt;
            if(bossData.supplymentCountdown <= 0){
                bossData.supplymentCountdown = bossAIParam.supplymentInterval;
                bossData.GetBossFightInterfaceFunc()?.CallSupplyment(6);
            }
        }

        public override bool Execute(float dt){
            UpdateSupplyment(dt);
            return base.Execute(dt);
        }

        public class IronTideWardenAIState : IronTideAIBossStateBase<IronTideBossWardenParam>{
            protected IronTideBossAIWardenData bossData;
            public IronTideWardenAIState Init(IronTideBossAIWardenData d){
                bossData = d;
                return this;
            }
        }

        /// <summary>
        /// 空闲状态
        /// </summary>
        public class Idle : IronTideWardenAIState{
            bool hasInitWait = false;

            public override void Start() {
                base.Start();
                if(!hasInitWait){
                    hasInitWait = true;
                    cmdList.Add(CommandUtil.CommandDuration.Delay(bossAIParam.initWait, ()=>{ NextAction(bossData.rg_random.Range(0, 2) == 0 ? State.WalkAndMissile : State.WalkAndMissile); }));
                }
                else if(Time.timeSinceLevelLoad - bossData.latestEmitMissilesTime > bossAIParam.missileCooldown * 2f){
                    NextAction(State.WalkAndMissile);
                }
                else if(bossData.nextActionState != State.Random){
                    cmdList.Add(CommandUtil.CommandDuration.Delay(bossData.nextActionWait, ()=>{ NextAction(bossData.nextActionState); }));
                    bossData.nextActionState = State.Random;
                    bossData.nextActionWait = 0;
                }
                else{
                    if(bossData.nextActionWait > 0){
                        cmdList.Add(CommandUtil.CommandDuration.Delay(bossData.nextActionWait, ()=>{ NextAction(bossData.nextActionState); }));
                    }
                    else{
                        cmdList.Add(CommandUtil.CommandDuration.Delay(bossAIParam.commonActionInterval, ()=>{ NextAction(State.Random); }));
                    }
                    bossData.nextActionWait = 0;
                }
            }

            void NextAction(State actionState){
                if(actionState == State.Random){
                    var states = new State[]{ 
                        State.MachGun,
                        State.Splitter,
                        State.Beam,
                        State.WalkAndMissile,
                    };
                    var list = new List<State>();
                    foreach(var s in states){
                        if(bossAIInterface.IsStatePrepared((int)s)){
                            list.Add(s);
                        }
                    }
                    if(list.Count > 0){
                        actionState = list.GetRandomObject(bossData.rg_random);
                    }
                }

                if(actionState != State.Random){
                    SetNextState((int)actionState);
                }
                else{
                    RGGameProcess.StartTimer(0.5f, ()=>{ NextAction(State.Random); });
                }
            }
        }

        #region MachGun
        /// <summary>
        /// 机枪扫射
        /// </summary>
        public class MachGun : IronTideWardenAIState{
            int _phase = 0;


            class Info{
                public CustomAreaDamageCarrier damageCarrier;
                public Vector2 startPos;
                public Vector2 endPos;
            }
            List<Info> damageCarriers = null;
            GameObject gunObj = null;
            GameObject gun_point = null;
            ObjectTremble.TrembleData gunTremble;
            

            public override void Start() {
                base.Start();
                _phase = 0;
                gunObj = bossData.weaponTransform[3].gameObject;
                bossData.weaponAttacking[3] = true;
                var gunImg = gunObj.transform.GetChild(0);
                damageCarriers = new List<Info>();
                damageCarriers.Add(new Info{ damageCarrier = ai.character.transform.Find("machGunDamageArea0").gameObject.GetComponent<CustomAreaDamageCarrier>()});
                damageCarriers.Add(new Info{ damageCarrier = ai.character.transform.Find("machGunDamageArea1").gameObject.GetComponent<CustomAreaDamageCarrier>()});
                damageCarriers.Add(new Info{ damageCarrier = ai.character.transform.Find("machGunDamageArea2").gameObject.GetComponent<CustomAreaDamageCarrier>()});
                gun_point = gunObj.transform.Find("gun_point").gameObject;

                var machGunAtkRangeList = bossData.GetBossWardenFight().GetMachGunAttackRange();

                var list = new List<Transform>(machGunAtkRangeList);
                RandomUtil.ShuffleList(list, bossData.rg_random);
                var count = Mathf.Min(damageCarriers.Count, list.Count);

                

                cmdList.Clear();

                gunTremble = ObjectTremble.Tremble(gunObj, 0.05f, 0.04f, 0.04f);
                cmdList.Add(new Command{
                    todoWithTime = (dt)=>{
                        gunTremble.Update(dt);
                        return true;
                    }
                });

                var seq = new CommandList(false);
                cmdList.Add(seq);

                var parallel = new CommandList(true);
                seq.Add(parallel);

                for(int i = 0; i < count; i++){
                    var info = damageCarriers[i];
                    info.startPos = list[i].transform.Find("start").position;
                    info.endPos = list[i].transform.Find("end").position;

                    var cmdSeq = new CommandList(false);
                    parallel.Add(cmdSeq);
                    
                    FireMove(info, 0, 0); // 初始化机枪朝向
                    cmdSeq.Add(CommandDuration.Delay(bossAIParam.machGunPrepareWait));
                    cmdSeq.Add(()=> { StartFire(info); });
                    cmdSeq.Add(CommandDuration.Delay(bossAIParam.machGunStartWait));
                    var moveTime = Vector2.Distance(info.endPos, info.startPos) / bossAIParam.machGunSpeed;
                    cmdSeq.Add(new CommandDuration{
                        duration = moveTime,
                        todoWithProgress = (dt, pg)=>{
                            FireMove(info, dt, pg);
                            return true;
                        }
                    });
                }

                seq.Add(()=>{ 
                    SetNextState((int)State.Idle);
                    var r = bossData.rg_random.Range(0, 3);
                    if(r == 0){
                        bossData.nextActionState = State.Splitter;
                        bossData.nextActionWait = 0;
                    }
                    else if(r == 1){
                        bossData.nextActionState = State.Beam;
                        bossData.nextActionWait = 0.5f;
                    }
                });
                
            }

            public override void End(){
                base.End();
                CleanUp();
            }

            RGMusicManager.LoopPlayer sfxPlayer = null;
            void StartFire(Info info){
                // 缺音效
                try{
                    if(sfxPlayer == null){ // 只播放一次
                        sfxPlayer = RGMusicManager.Inst.PlayLoopEffect(ai.character.audioClips[0]);
                        sfxPlayer.volume = 0.6f;
                    }
                }
                catch{}
                gun_point.SetActive(true);
                var bulletInfo = new BulletInfo();
                var damageInfo = new DamageInfo();
                bulletInfo.size = bossAIParam.machGunDmgSize;
                bulletInfo.camp = damageInfo.camp = ai.character.camp;
                damageInfo.damage = bossAIParam.machGunDmg;
                info.damageCarrier.checkTargetInterval = 0.1f;
                info.damageCarrier.damageInterval = bossAIParam.machGunDmgIntv;
                info.damageCarrier.UpdateInfo(bulletInfo, damageInfo);

                int CheckHit(Collider2D[] result){
                    // 以机甲脚底为中心做伤害判定
                    var idx = 0;
                    for(var i = 0; i < ai.modeProcess.processData.mechaControllers.Count; ++i){
                        var mecha =  ai.modeProcess.processData.mechaControllers[i];
                        if(mecha != null && mecha.ironTideRoleController != null){
                            if(Vector2.Distance(info.damageCarrier.transform.position, mecha.ironTideRoleController.transform.position) <= bulletInfo.size + (mecha.driverScale > 0 ? 1 / mecha.driverScale : 1)){
                                result[idx++] = mecha.driver.GetComponent<Collider2D>();
                            }
                        }
                    }

                    // 可以打烂箱子
                    if(bossData.boxOnBond != null){
                        for(var i = 0; i < bossData.boxOnBond.Count; ++i){
                            var boxObj = bossData.boxOnBond[i];
                            if(boxObj != null && boxObj.GetComponent<BoxCollider2D>() is {} col && 
                                Vector2.Distance(info.damageCarrier.transform.position, boxObj.transform.position) <= bulletInfo.size + (col.size.x + col.size.y) * 0.25f){
                                result[idx++] = col;
                            }
                        }
                    }

                    return idx;
                }
                info.damageCarrier.checkHit = CheckHit;
                info.damageCarrier.gameObject.SetActive(true);
            }

            bool FireMove(Info info, float dt, float pg){
                var pos = Vector3.Lerp(info.startPos, info.endPos, pg).Vec2();
                var dir = (pos - gunObj.transform.position.Vec2()).normalized;
                gunObj.transform.localEulerAngles = new Vector3(0, 0, IronTideUtil.WeaponAimLocalAngle(dir, gunObj.transform.parent.right));
                info.damageCarrier.transform.position = pos;
                return true;
            }
            
            void CleanUp(){
                if(sfxPlayer != null){
                    RGMusicManager.Inst.StopLoopPlayer(sfxPlayer);
                    sfxPlayer = null;
                }
                bossData.weaponAttacking[3] = false;
                foreach(var info in damageCarriers){
                    info.damageCarrier.gameObject.SetActive(false);
                }
                if(gun_point != null){
                    gun_point.SetActive(false);
                }
                if(gunTremble != null){
                    gunTremble.Stop();
                }
            }
        }
        #endregion

        #region Splitter
        public class Splitter : IronTideWardenAIState{
            Vector3 aimPos;
            IronTideBossWardenSplitter weapon;
            public override void Start(){
                base.Start();
                var p = bossData.rg_random.Range(0, 1f);
                aimPos = bossData.GetBossFightInterfaceFunc().GetBossAttackAimPoint("aimPoint_splitter_1") * p + bossData.GetBossFightInterfaceFunc().GetBossAttackAimPoint("aimPoint_splitter_2") * (1 - p);
                var gunObj = bossData.weaponTransform[1].gameObject;
                weapon = gunObj.GetComponent<IronTideBossWardenSplitter>();
                weapon.splitterParam.splitterDamage = bossAIParam.splitterDamage;
                weapon.splitterParam.splitterChargeTime = bossAIParam.splitterChargeTime;
                weapon.splitterParam.splitterCount1 = bossAIParam.splitterCount1;
                weapon.splitterParam.splitterAngleInterval1 = bossAIParam.splitterAngleInterval1;
                weapon.splitterParam.splitterCount2 = bossAIParam.splitterCount2;
                weapon.splitterParam.splitterAngleInterval2 = bossAIParam.splitterAngleInterval2;
                weapon.simpleShootParam.times = bossAIParam.splitterEmitTimes;
                
                bossData.weaponAttacking[1] = true;
            }

            public override bool Execute(float dt){
                base.Execute(dt);
                var angleDiff = IronTideWeaponBase.AutoAiming(weapon.transform.right, (aimPos - weapon.transform.position).normalized, weapon.transform, 200, dt);
                if(weapon.IsCoolingDown()){
                    SetNextState((int)State.Idle);
                }
                else if(angleDiff < 1 && !weapon.IsAiming()){
                    weapon.AttackKeyDown(true);
                    weapon.AttackKeyUp(true);
                }
                return true;
            }

            public override void End(){
                base.End();
                bossData.weaponAttacking[1] = false;
                if(weapon){
                    weapon.DoCleanUp();
                }
            }
        }
        #endregion
    
        #region Beam
        public class Beam : IronTideWardenAIState{
            Vector3 aimPos;
            IronTideBossWardenSweepBeam weapon;
            Vector3[] sweepPosStartEnd;

            RGMusicManager.LoopPlayer sfxPlayer = null;

            CommandUtil.CommandList attackCmd = new CommandList(false);
            public override void Start(){
                base.Start();
                var startPos0 = bossData.GetBossFightInterfaceFunc().GetBossAttackAimPoint("aimPoint_beam_start1");
                var startPos1 = bossData.GetBossFightInterfaceFunc().GetBossAttackAimPoint("aimPoint_beam_start2");
                var endPos0 = bossData.GetBossFightInterfaceFunc().GetBossAttackAimPoint("aimPoint_beam_end1");
                var endPos1 = bossData.GetBossFightInterfaceFunc().GetBossAttackAimPoint("aimPoint_beam_end2");
                var sweepPos = new Vector3[][]{
                    new Vector3[]{startPos0, endPos0 },
                    new Vector3[]{startPos1, endPos1 },
                };

                // 从离玩家最近的位置扫射
                sweepPosStartEnd = GameUtil.FindMost(sweepPos, (array)=> 1 / (Vector2.Distance(array[0], ai.modeProcess.playerController.transform.position) + 0.01f));
                
                var gunObj = bossData.weaponTransform[2].gameObject;
                weapon = gunObj.GetComponent<IronTideBossWardenSweepBeam>();
                weapon.beamParam.beamDmg = bossAIParam.beamDmg;
                weapon.beamParam.beamPrepareDuration = bossAIParam.beamPrepareDuration;
                weapon.beamParam.beamSweepDuration = bossAIParam.beamSweepDuration;                        
                weapon.beamParam.beamEndDuration = bossAIParam.beamEndDuration;

                bossData.weaponAttacking[2] = true;

                if(sweepPosStartEnd == sweepPos[1]){
                    weapon.sweepTimeFactor = 1.3f;
                }
                else{
                    weapon.sweepTimeFactor = 1f;
                }

                attackCmd.Clear();
                var start_dir = (sweepPosStartEnd[0] - gunObj.transform.position).normalized;
                var end_dir = (sweepPosStartEnd[1] - gunObj.transform.position).normalized;

                attackCmd.Add(new CommandUtil.Command{ // 对准开始位置
                    todoWithTime = (dt)=>{
                        return IronTideWeaponBase.AutoAiming(gunObj.transform.right, (sweepPosStartEnd[0] - gunObj.transform.position).normalized, gunObj.transform, 200, dt) >= 1;
                    }
                });
                attackCmd.Add(()=>{ // 发射
                    weapon.AttackKeyDown(true);
                    weapon.AttackKeyUp(true);
                });
                attackCmd.Add(CommandUtil.CommandDuration.Delay(weapon.beamParam.beamPrepareDuration));
                attackCmd.Add(()=>{
                    try{
                        sfxPlayer = RGMusicManager.Inst.PlayLoopEffect(ai.character.audioClips[1]);
                        sfxPlayer.volume = 0.6f;
                    }catch{}
                });
                attackCmd.Add(new CommandUtil.CommandDuration{ // 向最终位置旋转
                    duration = weapon.beamParam.beamSweepDuration * weapon.sweepTimeFactor,
                    todoWithProgress = (dt, pg)=>{
                        pg = weapon.sweepCurve.Evaluate(pg);
                        var dir = end_dir * pg + start_dir * (1 - pg);
                        dir.Normalize();
                        weapon.transform.SetAngleWithDirection(dir, weapon.transform.parent.right);
                        return true;
                    }
                });
                attackCmd.Add(CommandUtil.CommandDuration.Delay(weapon.beamParam.beamEndDuration));
                attackCmd.Add(()=>{
                    if(sfxPlayer != null){
                        RGMusicManager.Inst.StopLoopPlayer(sfxPlayer);
                        sfxPlayer = null;
                    }
                });
                attackCmd.Add(new CommandUtil.Command{ // 重新摆正角度
                    todoWithTime = (dt)=>{
                        return IronTideWeaponBase.AutoAiming(gunObj.transform.right, ai.character.transform.right, gunObj.transform, 500, dt) >= 1;
                    }
                });
                attackCmd.Add(()=>{
                    bossData.nextActionWait = 1;
                    SetNextState((int)State.Idle);
                });

                cmdList.Add(attackCmd);
            }

            public override void End() {
                base.End();
                bossData.weaponAttacking[2] = false;
                if(weapon){
                    weapon.DoCleanUp();
                }
                if(sfxPlayer != null){
                    RGMusicManager.Inst.StopLoopPlayer(sfxPlayer);
                    sfxPlayer = null;
                }
            }
        }
        #endregion

        #region
        public class WalkAndMissile : IronTideWardenAIState{
            IronTideWeaponSimpleShoot weapon;
            public int batchIdx = 0;
            public override void Start() {
                base.Start();
                bossData.latestEmitMissilesTime = Time.timeSinceLevelLoad;
                cooldownRemain = bossAIParam.missileCooldown; // 冷却
                var gunObj = bossData.weaponTransform[0].gameObject;
                weapon = gunObj.GetComponent<IronTideWeaponSimpleShoot>();
                bossData.weaponAttacking[0] = true;
                weapon.simpleShootParam.times = bossAIParam.missleCount;
                weapon.shootInterval = 0.35f;
                weapon.stdCooldown = weapon.shootInterval * weapon.simpleShootParam.times + 1;
                weapon.bulletInfo.damage = bossAIParam.missileDmg;

                // 所有爆炸中随机挑选dropBoxCount次触发箱子掉落
                var dropBoxList = new List<bool>();
                for(var i = 0; i < bossAIParam.dropBoxCount; ++i){
                    dropBoxList.Add(true);
                }
                for(var i = 0; i < Mathf.Max(0, bossAIParam.missleCount - bossAIParam.dropBoxCount); ++i){
                    dropBoxList.Add(false);
                }
                RandomUtil.ShuffleList(dropBoxList, bossData.rg_random);

                ++batchIdx;
                weapon.onAfterCreateSubEffect = (emitInfo)=>{ // 导弹产生爆炸
                    GameUtil.CameraShake(1);
                    if(dropBoxList.Count > 0){
                        if(dropBoxList[0]){
                            bossData.boxOnBond = bossData.GetBossFightInterfaceFunc().DropBox(emitInfo.position, bossAIParam.boxHp, batchIdx);
                        }
                        dropBoxList.RemoveAt(0);
                    }
                };
                
                var aimPos0 = bossData.GetBossFightInterfaceFunc().GetBossAttackAimPoint("aimPoint_missile0");
                var aimPos1 = bossData.GetBossFightInterfaceFunc().GetBossAttackAimPoint("aimPoint_missile1");

                var dir0 = (aimPos0 - weapon.transform.position.Vec2()).normalized;
                var dir1 = (aimPos1 - weapon.transform.position.Vec2()).normalized;
                var seq = new CommandUtil.CommandList(false);
                cmdList.Add(seq);
                seq.Add(new CommandUtil.Command{
                    todoWithTime = (dt)=>{
                        return IronTideWeaponBase.AutoAiming(weapon.transform.right, dir0, weapon.transform, 100, dt) >= 1;
                    }
                });
                seq.Add(()=>{ weapon.PullTrigger(); });
                seq.Add(()=>{
                    cmdList.Add(new Command{
                        todoWithTime = (dt)=>{
                            if(weapon.IsCoolingDown()){
                                bossData.nextActionWait = 1f;
                                bossData.nextActionState = bossData.rg_random.Range(0, 2) == 0 ? State.MachGun : State.Splitter;
                                SetNextState((int)State.Idle);
                            }
                            return !weapon.IsCoolingDown();
                        }
                    });

                    var cmdAim = new CommandUtil.CommandList(false);
                    var rotateDuration = weapon.shootInterval * weapon.simpleShootParam.times * 0.5f;
                    var angleSpeed = Vector3.Angle(dir1, dir0) / rotateDuration;
                    cmdAim.Add(CommandUtil.CommandDuration.Progress(rotateDuration, (dt, pg)=>{
                        return IronTideWeaponBase.AutoAiming(weapon.transform.right, dir1, weapon.transform, angleSpeed, dt) >= 1;
                    }));
                    cmdAim.Add(CommandUtil.CommandDuration.Progress(rotateDuration, (dt, pg)=>{
                        return IronTideWeaponBase.AutoAiming(weapon.transform.right, dir0, weapon.transform, angleSpeed, dt) >= 1;
                    }));
                    cmdList.Add(cmdAim);
                });
            }

            public override void End(){
                base.End();
                bossData.weaponAttacking[0] = false;
                if(weapon){
                    weapon.DoCleanUp();
                }
            }
        }
        #endregion
    }
}