using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace IronTide{



    /// <summary>
    /// 机械狂潮AI默认状态（驻守）
    /// </summary>
    public class IronTideAIGarrison : IronTideAIStateBase{
        class GarrisonGroup{
            public event System.Action<Transform> eventAlert;
            public void OnAlert(Transform target){
                eventAlert?.Invoke(target);
            }
        }

        static Dictionary<int, GarrisonGroup> GarrisonGroups = new Dictionary<int, GarrisonGroup>();

        public static void InitCommonData(){
            GarrisonGroups = new Dictionary<int, GarrisonGroup>();
        }


        static public float AlertDistance = 10;
        public Vector2 _latestTargetPos;
        float _updateRemain = 0;
        float _cancelStokeRemain = 5;
        Transform stokeTarget;
        Vector3 _garrisonPosition;
        GarrisonGroup _garrisonGroup;
        int _state;

        public override void Init(CharacterAIData aiData){
            base.Init(aiData);
            _garrisonPosition = ai.character.transform.position;
            if(!GarrisonGroups.TryGetValue(ai.groupID, out _garrisonGroup)){
                _garrisonGroup = new GarrisonGroup();
                _garrisonGroup.eventAlert += OnAlert;
                GarrisonGroups[ai.groupID] = _garrisonGroup;
            }
            else{
                _garrisonGroup.eventAlert += OnAlert;
            }
        }

        public override void Destroy() {
            if(_garrisonGroup != null){
                _garrisonGroup.eventAlert -= OnAlert;
            }
            base.Destroy();
        }

        void OnAlert(Transform target){
            stokeTarget = target;
            _state = 1;
            _cancelStokeRemain = 5;
        }

        public override bool Execute(float deltaTime){
            if(!base.Execute(deltaTime)){
                return false;
            }

            if(ai.modeProcess != null){
                if(_state == 0){ // 最初驻守状态
                    if(stokeTarget == null){
                        stokeTarget = ai.modeProcess.aiUtil.GetTarget(ai, AlertDistance, true);
                    }
                    if(stokeTarget != null){
                        _garrisonGroup.OnAlert(stokeTarget);
                    }
                }
                else if(_state == 1){ // 发现敌人，持续追踪
                    var distanceToGarrisonPosition = Vector2.Distance(ai.transform.position, _garrisonPosition);
                    if(stokeTarget != null){
                        var currentTargetPos = stokeTarget.position.Vec2();
                        _updateRemain -= deltaTime;
                        if(_updateRemain <= 0 || Vector2.Distance(_latestTargetPos, currentTargetPos) > 0.25f){
                            _updateRemain = 0.5f;
                            _latestTargetPos = currentTargetPos;
                            ai.character.MoveTo(_latestTargetPos, true);
                        }

                        if(distanceToGarrisonPosition > 2 * AlertDistance && Vector2.Distance(ai.transform.position, stokeTarget.position) > 3){
                            _cancelStokeRemain -= deltaTime; // 离开驻守区域且追不上目标，继续追踪一段时间
                        }
                        else{
                            _cancelStokeRemain = 5;
                        }
                    }
                    
                    bool stopStoke = stokeTarget == null || _cancelStokeRemain <= 0 || distanceToGarrisonPosition > AlertDistance * 4;
                    if(stopStoke){
                        // 返回驻守位置
                        _state = 2;
                        stokeTarget = null;
                        ai.character.MoveTo(_garrisonPosition, true);
                    }
                }
                else if(_state == 2){
                    // 返回过程中，比较近的范围内有敌人，继续追踪
                    var t = ai.modeProcess.aiUtil.GetTarget(ai, AlertDistance * 0.35f, true);
                    if(t != null){
                        stokeTarget = t;
                        _state = 1;
                        _cancelStokeRemain = 5;
                    }
                    else if(!ai.character.hasMoveDestination){
                        _state = 0;
                    }
                }
            }
            return true;
        }
        
        int _nextState;
        public override bool TryChangeState(){
            if(ai.GetState((int)IronTideAIStateType.Attack) is IronTideAIStateAttack attackState && attackState.CanAttackTarget(stokeTarget)){
                _nextState = (int)IronTideAIStateType.Attack;
                return true;
            }
            if(ai.GetState((int)IronTideAIStateType.Alert) is IronTideAIStateAlert alertState && alertState.CanAlert()){
                _nextState = (int)IronTideAIStateType.Alert;
                return true;
            }
            return false;
        }

        public override int NextState(){
            return _nextState;
        }
    }

    /// <summary>
    /// 机械狂潮AI(直线追逐)
    /// </summary>
    public class IronTideAIStoke : IronTideAIStateBase
    {
        public Transform stokeTarget;
        public bool hasDefaultDestination;
        public Vector3 defaultDestination;

        float _updateRemain = 0;

        public override void Init(CharacterAIData aiData) {
            base.Init(aiData);
            if(ai.modeProcess.processData.playerStayedCampsite != null){
                var distance = Mathf.Max(Mathf.Min(ai.modeProcess.modeConfig.mapBlockSize.x, 60), 50) + ai.modeProcess.rg_random.Range(-3f, 3f);
                var displacement = ai.modeProcess.processData.playerStayedCampsiteEntranceDir * distance;
                ai.transform.position = ai.modeProcess.processData.playerStayedCampsite.position + Quaternion.Euler(0, 0, ai.modeProcess.rg_random.Range(-15f, 15f)) * displacement;
            }
        }

        public override bool Execute(float deltaTime){
            if(!base.Execute(deltaTime)){
                return false;
            }

            if(ai.modeProcess != null){
                
                if(stokeTarget == null){
                    stokeTarget = ai.modeProcess.aiUtil.GetDefaultTarget(ai, ai.modeProcess.modeConfig.mapBlockSize.x, ai.modeProcess.processData.playerStayedCampsite == null);
                }

                if(stokeTarget != null){
                    _updateRemain -= deltaTime;
                    if(_updateRemain <= 0){
                        _updateRemain = 0.5f;
                        _MoveTo(stokeTarget.position.Vec2());
                    }
                }
                else if(ai.modeProcess.processData.playerStayedCampsite != null){
                    hasDefaultDestination = true;
                    defaultDestination = ai.modeProcess.processData.playerStayedCampsite.transform.position;
                }

                                
                if(stokeTarget == null && hasDefaultDestination){
                    _updateRemain -= deltaTime;
                    if(_updateRemain <= 0){
                        _updateRemain = 0.5f;
                        _MoveTo(defaultDestination);
                    }
                }
            }
            return true;
        }

        void _MoveTo(Vector2 position){
            var diff = (position - ai.character.transform.position.Vec2());
            if(Mathf.Abs(diff.x) + Mathf.Abs(diff.y) <= 1.5f){
                ai.character.MoveTo(position, true);
            }
            else{
                var moveDir = diff.normalized;
                var pos = ai.character.transform.position;
                var bodyRadius = 0f;
                if(ai.character.bodyCollider != null){ 
                    pos = ai.character.bodyCollider.transform.position;
                    bodyRadius = ai.character.bodyCollider.radius;
                }
                if(IronTideAIUtil.IsWayBlockedByObstacle(pos, moveDir, bodyRadius + 1.2f, out Vector2 newDir)){
                    moveDir = newDir;
                }
                ai.character.MoveTowards(moveDir);
            }
        }

        int _nextState;
        public override bool TryChangeState(){
            if(ai.GetState((int)IronTideAIStateType.Attack) is IronTideAIStateAttack attackState && attackState.CanAttackTarget(stokeTarget)){
                _nextState = (int)IronTideAIStateType.Attack;
                return true;
            }
            if(ai.GetState((int)IronTideAIStateType.Alert) is IronTideAIStateAlert alertState && alertState.CanAlert()){
                _nextState = (int)IronTideAIStateType.Alert;
                return true;
            }
            return false;
        }

        public override int NextState(){
            return _nextState;
        }
    }

    /// <summary>
    /// 机械狂潮敌人AI(成群地来回巡逻)
    /// </summary>
    public class IronTideAIGroupPatrol : IronTideAIStateBase
    {
        static public float GroupRadius = 10;

        class GroupCenter{
            public static int nextGroupID;
            public static Dictionary<int, GroupCenter> GroupCenters;

            public int groupID{get; private set;}
            public IronTideGameModeProcess modeProcess;
            public Vector2 initDisplacementFromPlayer;
            public float speed;
            public long updateVersion = 0;
            public Vector2 position{get; private set;}
            public Vector2 latestDisplacement{get; private set;}
            Vector2 _latestPlayerPosition;
            public bool moving{get; private set;} = true;
            public bool allFinished = false;

            public event System.Action<Vector2> onRestart;

            int state = 0; // 0 接近玩家  1 远离玩家

            public static GroupCenter JoinGroup(Vector2 position, float speed){
                int groupID = -1;
                // 自动加入附近的集群
                foreach(var kv in GroupCenters){
                    if(Vector2.Distance(kv.Value.position, position) <= GroupRadius){
                        groupID = kv.Key;
                        break;
                    }
                }
                // 没找到附近的集群，新建一个
                if(groupID < 0){
                    groupID = nextGroupID++;
                    var modeProcess = RGGameProcess.Inst.modeProcess as IronTideGameModeProcess;
                    GroupCenters[groupID] = new GroupCenter{ 
                        groupID = groupID, 
                        position = position, 
                        speed = speed, 
                        modeProcess = modeProcess,
                        initDisplacementFromPlayer = position - modeProcess.playerController.transform.position.Vec2(),
                        _latestPlayerPosition = modeProcess.playerController.transform.position.Vec2()
                    };
                }
                return GroupCenters[groupID];
            }

            HashSet<IronTideActor> _aliveMembers = new HashSet<IronTideActor>();
            public void MemberAliveConfirm(IronTideActor c){
                _aliveMembers.Add(c);
            }

            public void Update(float deltaTime){
                if(modeProcess.playerController == null){
                    return ;
                }

                if(!allFinished && moving){
                    if(state == 0){
                        IronTideUtil.Move(position, _latestPlayerPosition, speed, deltaTime, out Vector2 delta, out bool arrived);
                        if(delta.magnitude > 0){
                            latestDisplacement = delta;
                        }
                        position += latestDisplacement;
                        state = arrived ? 1 : 0;
                        var d = modeProcess.playerController.transform.position.Vec2() - position;
                        if(d.magnitude > 10){
                            // 离玩家太近就不更新latestPosition，避免接近玩家时大幅度转向
                            _latestPlayerPosition = modeProcess.playerController.transform.position.Vec2();
                        }
                    }
                    else{
                        IronTideUtil.Move(position, position + latestDisplacement, speed, deltaTime, out Vector2 delta, out bool arrived);
                        latestDisplacement = delta;
                        position += latestDisplacement;
                        var d = modeProcess.playerController.transform.position.Vec2() - position;
                        moving = d.magnitude < 30;
                    }
                }
                else if(allFinished){
                    // 随机起始位置，继续
                    var displacement = position - modeProcess.playerController.transform.position.Vec2();
                    position = modeProcess.playerController.transform.position.Vec2() + initDisplacementFromPlayer * (Quaternion.Euler(0, 0, modeProcess.rg_random.Range(-45f, 45f)) * displacement.normalized);
                    onRestart?.Invoke(position);
                    onRestart = null;
                    if(_aliveMembers.Count > 0){
                        moving = true;
                        state = 0;
                    }
                    else{
                        moving = false;
                        GroupCenters.Remove(groupID);
                    }
                }
                allFinished = true; // 先标记未结束，未结束移动的单位会设置为false
            }
        }

        /// <summary>
        /// 初始化集群数据
        /// </summary>
        public static void InitCommonData(){
            GroupCenter.nextGroupID = 0;
            GroupCenter.GroupCenters = new Dictionary<int, GroupCenter>();
        }

        public bool repeat{get; protected set;} // 是否往复移动

        public IronTideAIGroupPatrol SetRepeat(bool b){
            repeat = b;
            return this;
        }
        
        private long _updateVersion = 0;
        GroupCenter _groupCenter;
        Vector2 _initPosInGroup;
        
        public override void Init(CharacterAIData aiData){
            base.Init(aiData);
            JoinGroup();
        }

        void JoinGroup(){
            _updateVersion = 0;
            
            _groupCenter = GroupCenter.JoinGroup(ai.transform.position.Vec2(), ai.character.attribute.speed);
            _initPosInGroup = ai.transform.position.Vec2() - _groupCenter.position;
        }

        public override bool Execute(float deltaTime){
            if(!base.Execute(deltaTime)){
                return false;
            }

            // 通过updateVersion控制同一个group中，只有一个实体在驱动GroupCenter更新
            ++_updateVersion;
            if(_groupCenter.updateVersion < _updateVersion){
                _groupCenter.updateVersion = _updateVersion;
                _groupCenter.Update(deltaTime);
            }
            _updateVersion = _groupCenter.updateVersion;

            ai.character.MoveTowards(_groupCenter.latestDisplacement.normalized);
            if(!_groupCenter.moving){
                var d = ai.transform.position.Vec2() - _groupCenter.position;
                if(d.magnitude < GroupRadius || Vector2.Dot(d, _groupCenter.latestDisplacement) < 0){
                    _groupCenter.allFinished = false; // 标记：集群移动行为还未全部结束
                }
                else{
                    if(repeat){
                        _groupCenter.onRestart += OnGroupPatrolRestart;
                    }
                    else{
                        // 删除自己
                        ai.modeProcess.processData.mapBlock.DestroyEnemyWithoutReward(ai.character);
                    }
                }
            }
            else{
                _groupCenter.allFinished = false; // 标记：集群移动行为还未全部结束
            }

            return true;
        }

        void OnGroupPatrolRestart(Vector2 newCenterPos){
            ai.transform.position = newCenterPos + _initPosInGroup;
            _groupCenter.MemberAliveConfirm(ai.character);
        }

        public override void Destroy(){
            if(_groupCenter != null){
                _groupCenter.onRestart -= OnGroupPatrolRestart;
            }
            base.Destroy();
        }

        int _nextState;
        public override bool TryChangeState(){
            if(ai.GetState((int)IronTideAIStateType.Attack) is IronTideAIStateAttack attackState){
                var attack_target = ai.modeProcess.aiUtil.GetDefaultTarget(ai, ai.modeProcess.modeConfig.mapBlockSize.x, true);
                if(attack_target != null && attackState.CanAttackTarget(attack_target)){
                    _nextState = (int)IronTideAIStateType.Attack;
                    return true;
                }
            }
            if(ai.GetState((int)IronTideAIStateType.Alert) is IronTideAIStateAlert alertState && alertState.CanAlert()){
                _nextState = (int)IronTideAIStateType.Alert;
                return true;
            }
            return false;
        }

        public override int NextState(){
            return _nextState;
        }
    }
}
