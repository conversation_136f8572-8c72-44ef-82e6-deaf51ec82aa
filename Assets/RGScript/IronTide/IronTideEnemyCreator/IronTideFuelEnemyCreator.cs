using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace IronTide{
    public class IronTideFuelEnemyCreator : IronTideEnemyCreator
    {
        public RGEController campsiteBoss;
        public bool isRecoverFromData = false;
        public override void Init(RGRandom rg_random) {
            if(objectInfo == null){
                return;
            }
            
            var campsiteData = IronTideUtil.GetCampsiteDataWithFuel(modeProcess, objectInfo);
            var campsitePosition = campsiteData.position;
            customSpawnBasePositionGetter = (inputPos)=>{
                return campsitePosition; // 以据点为中心位置为基准生成敌人
            };
            ignoreDefaultCollisionLayer = true; // 生成位置避免碰撞时忽略default层(据点用default层防止其他敌人生成在里面)
            spawnPositionRestrictedAreaRadius = config.campsiteSize / 2 - 1; // 防止敌人生成在据点外面

            if(!modeProcess.modeData.isBadass){
                modeProcess.processData.mapBlock.EraseAllEnemies();
            }

            // 生成一个小BOSS
            if(isRecoverFromData && modeProcess.modeData.currentCampsiteBossHp == 0){
                return;
            }

            var bossIndex = modeProcess.modeData.bossIndexList[modeProcess.modeData.currentEncounterCampsiteBossTimes];
            var bossParams = modeProcess.modeConfig.bossParameters[bossIndex];
            var prefab = ResourcesUtil.Load<GameObject>(bossParams.assetPath);
            if(prefab != null){
                var obj = GameObject.Instantiate(prefab);
                obj.name = prefab.name;
                obj.transform.position = isRecoverFromData ? modeProcess.modeData.currentCampsiteBossPosition : (objectInfo.position + Vector2.right * modeProcess.modeConfig.campsiteSize * 0.25f).Vec3();
                TargetEffect.DisplayFlashLight(obj.transform.position);
                var ectrl = obj.GetComponent<RGEController>();
                var attr = obj.GetComponent<RoleAttribute>();
                ectrl.findTargetRange = 999; // 保证覆盖据点
                ectrl.lowPriorityForPetTarget = true;
                modeProcess.processData.currentCampsiteBoss = ectrl;
                var attributeFactor = IronTideEnemyCreator.GetAttributeFactor(modeProcess, modeProcess.modeData.totalTimeElapsed);
                var bossHpBar = GameUtil.CreateBossHpBar(ectrl.attribute.max_hp, true);

                if(ectrl is RGEBossController bossCtrl){
                    bossCtrl.onInitFinish = ()=>{
                        var maxHp = Mathf.RoundToInt(Mathf.RoundToInt(bossParams.hp * bossParams.encounterTimesAttributeFactor * (modeProcess.modeData.encounterFuelTimes + 1) * attributeFactor));
                        bossCtrl.attribute.hp = bossCtrl.attribute.max_hp = maxHp;
                        if(isRecoverFromData){
                            attr.hp = modeProcess.modeData.currentCampsiteBossHp;
                        }
                        bossHpBar.Refresh(ectrl.attribute.hp, ectrl.attribute.max_hp);
                    };
                }

                ectrl.autoStartAI = false;
                ectrl.awake = true;
                //ectrl.StartEnemyAI();
                if(!isRecoverFromData){
                    RGGameProcess.StartTimer(3, ()=>{
                        ectrl.awake = true;
                        ectrl.StartEnemyAI();
                    });
                }
                else{
                    ectrl.awake = true;
                    ectrl.StartEnemyAI();
                }
                ectrl.reward_value = new int[0];
                ectrl.CanDropItem = false;
                ectrl.gameObject.transform.localScale = Vector3.one * bossParams.scale;
                var bossInterface = ectrl.gameObject.GetComponent<IIronTideBoss>();
                if(bossInterface != null){
                    bossInterface.ParseConfig(bossParams.param);
                }
                
                campsiteBoss = ectrl;
                obj.SetNPCZPos();

                // 创建BOSS血条
                
                if(modeProcess.uiMechaStatus != null){
                    modeProcess.uiMechaStatus.DisplayTimeCounter(false);
                }
                ectrl.afterEnemyGetHurt += ()=>{
                    if(ectrl != null){
                        bossHpBar.Refresh(ectrl.attribute.hp, ectrl.attribute.max_hp);
                        modeProcess.processData.bossWardenProgress = Mathf.RoundToInt(ectrl.attribute.hp * 1f /  ectrl.attribute.max_hp * 100);
                    }
                };

                ectrl.onEnemyDead += (e)=>{
                    modeProcess.processData.currentCampsiteBoss = null;
                    modeProcess.OnGetExtraRewardScore(ModeIronTideConfig.Configs.EnemyScore.Type.Boss);
                    bossHpBar.Show(false, ()=>{
                        GameObject.Destroy(bossHpBar.gameObject);
                        if(modeProcess.uiMechaStatus != null){
                            modeProcess.uiMechaStatus.DisplayTimeCounter(true);
                        }
                    });

                    try{
                        if(bossParams.loot != null){
                            var dropMaterial = IronTideUtil.RandomDropMaterial(modeProcess, bossParams.loot);
                            if(dropMaterial != null){
                                IronTideLootItems.CreateMaterial(modeProcess, dropMaterial.name, dropMaterial.count, e.transform.position, true, IronTideLootItems.GetBatchId());
                            }
                        }
                    }
                    catch(System.Exception exceptoin){ Debug.LogError(exceptoin.StackTrace);}

                    GameObject.Destroy(e.gameObject, 1);
                };
            }

            base.Init(rg_random);
        }

        protected override bool IsRunning(){
            return base.IsRunning() || (campsiteBoss != null && !campsiteBoss.dead);
        }
    }
}