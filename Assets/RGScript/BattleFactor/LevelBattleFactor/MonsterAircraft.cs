using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MonsterAircraft : MonoBehaviour
{
    public BulletInfoInspector bombInfo;
    public RGRoomX room{get; set;}

    float _passedTime = 0;
    // Update is called once per frame
    void FixedUpdate()
    {
        float bombingInterval = 1.5f;
        _passedTime += Time.fixedDeltaTime;
        if(_passedTime >= bombingInterval){
            _passedTime -= bombingInterval;
            bool valid = true;
            if(room){
                var rect = new Rect(room.transform.position - new Vector3(room.room_width / 2, room.room_height / 2, 0), new Vector2(room.room_width, room.room_height));
                valid = rect.Contains(transform.position);
            }

            if(valid){
                var bulletInfo = bombInfo.GetBulletInfo().SetCamp(0).SetPosition(transform.position).SetSourceObject(gameObject);
                var damageInfo = bombInfo.GetDamageInfo();
                BulletFactory.TakeBullet(bulletInfo, damageInfo);
            }
        }
    }
}
