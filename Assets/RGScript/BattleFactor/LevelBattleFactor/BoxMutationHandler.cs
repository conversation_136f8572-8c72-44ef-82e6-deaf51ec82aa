using System.Collections.Generic;
using UnityEngine;
using BattleFactor;
using RGScript.Map;
using Object = UnityEngine.Object;


namespace RGScript.BattleFactor.LevelBattleFactor {
    public class BoxMutationHandler : AbstractBattleFactorHandler<Transform> {
        private GameObject _prefab;

        private UnKnowBoxDropData[] configData = new[] {
            new UnKnowBoxDropData(UnknownBoxType.Explosion, 10),
            new UnKnowBoxDropData(UnknownBoxType.Poison, 8),
            new UnKnowBoxDropData(UnknownBoxType.IceSting, 12),
            new UnKnowBoxDropData(UnknownBoxType.Monster, 8),
            new UnKnowBoxDropData(UnknownBoxType.Energy, 8),
            new UnKnowBoxDropData(UnknownBoxType.Coin, 2, 5),
            new UnKnowBoxDropData(UnknownBoxType.HpPot, 1, 1),
            //天赋最大掉落 UnknownBox.MaxBuffDrop
            new UnKnowBoxDropData(UnknownBoxType.Trait, 5,5),
        };

        private BoxMutationModel _model = new BoxMutationModel();


        public BoxMutationHandler() {
            SimpleEventManager.AddEventListener<MapCreateCompleteEvent>(OnRoomCreatEnd);
            _prefab = ResourcesUtil.Load<GameObject>("RGPrefab/LevelObject/Box/cask_unknown.prefab");
        }

        private void OnRoomCreatEnd(MapCreateCompleteEvent e) {
            if(!BattleData.data.CompareFactor(Factor))
                return;
            
            //ResetTimes
            foreach (var dropData in configData) {
                dropData.curTime = 0;
            }
            _model.Reset();

            RGRandom seedRandom = new RGRandom();
            seedRandom.SetRandomSeed(RGGameInfo.Inst.MapRandomSeed);
            float offsetY = BattleData.data.sceneIndex == 4 ? -0.6f : 0;
            foreach (var room in MapManager.Instance.AllRoom) {
                int seed = seedRandom.Range(int.MinValue, int.MaxValue);
                RGRandom roomRandom = new RGRandom();
                roomRandom.SetRandomSeed(seed);
                LogUtil.LogFormat("{1} {0}",seed,room.name);
                var roomBoxs = room.GetComponentsInChildren<RGBox>();
                var len = roomBoxs.Length;
                for (int i = 0; i < len; i++) {
                    if (IsReplace(len, i) && roomBoxs[i].CanReplace) {
                        var newBox = GetNewUnknownBox(roomRandom);
                        ReplaceBox(roomBoxs[i].transform, newBox, offsetY);
                    }
                }
            }
        }

        private bool IsReplace(int count, int curIndex) {
            int replaceCount = GetReplaceCount(count);
            if (replaceCount == 0) return false;
            int step = count / replaceCount;
            return curIndex % step == 0;
        }

        private int GetReplaceCount(int count) {
            if (count > 40) {
                return 15;
            } else if (count > 20) {
                return 10;
            } else {
                return count / 2;
            }
        }

        private RGBox GetNewUnknownBox(RGRandom rg_random) {
            UnknownBoxType type = GetRandomType(rg_random);
            var newBox = GameObject.Instantiate(_prefab).GetComponent<UnknownBox>();
            newBox.type = type;
            newBox.RGRandom = rg_random;
            newBox.Model = _model;
            //提前设定天赋球内容
            if (newBox.type == UnknownBoxType.Trait) {
                newBox.SetRandomBuff(rg_random);
            }
            return newBox;
        }

        private void ReplaceBox(Transform oldBox, RGBox newBox, float offsetY = 0) {
            newBox.transform.SetParent(oldBox.parent);
            newBox.transform.position = oldBox.position + Vector3.up * offsetY;
            Object.Destroy(oldBox.gameObject);
        }

        private UnknownBoxType GetRandomType(RGRandom rg_random) {
            int weightSum = 0;
            foreach (var dropRow in configData) {
                if (!dropRow.IsMax) {
                    weightSum += dropRow.correctiveWeight;
                }
            }

            var randomNum = rg_random.Range(0, weightSum);
            var count = 0;
            UnknownBoxType res = UnknownBoxType.Energy;
            foreach (var dropRow in configData) {
                if (!dropRow.IsMax) {
                    count += dropRow.correctiveWeight;
                    if (randomNum < count) {
                        res = dropRow.type;
                        dropRow.curTime++;
                        return res;
                    }
                }
            }

            return res;
        }

        public override void MakeEffect(string eventName, Transform target) { }
        public override emBattleFactor Factor => emBattleFactor.BoxMutation;
        public override IEnumerable<string> EventNames { get => new string[] { }; }
    }
}

public enum UnknownBoxType {
    Explosion, //爆炸
    Poison, //毒
    IceSting, //冰刺
    Trait, //天赋
    Monster, //怪物
    HpPot, //血瓶
    Energy, //能量
    Coin, //金币
    //盾
}

public class BoxMutationModel {
    public HashSet<emBuff> emBuffMap = new HashSet<emBuff>();
    public int dropCount = 0;
    public void Reset() {
        dropCount = 0;
        emBuffMap.Clear();
    }
}

public class UnKnowBoxDropData {
    public UnKnowBoxDropData() { }

    public UnKnowBoxDropData(UnknownBoxType type, int correctiveWeight, int maxTime = 9999) {
        this.type = type;
        this.correctiveWeight = correctiveWeight;
        this.maxTime = maxTime;
    }

    public UnknownBoxType type;
    public int correctiveWeight;
    public int maxTime;
    public int curTime;
    public bool IsMax => curTime >= maxTime;
}