Shader "Image Effect/CameraFreeze"
{
    Properties
    {
        [PerRendererData]_MainTex ("Texture", 2D) = "white" {}
        _Radius ("Radius", Float) = 1
        _Soft ("Soft", Float) = 1
        _NoiseTex ("Noise Texture", 2D) = "white" {}
        _Color ("Noise Color", Color) = (1, 1, 1, 1)
        _Saturate ("Noise Saturate", Float) = 1
        _Dist ("Distortion Amount", Float) = 0.1
    }
    SubShader
    {
        // No culling or depth
        Cull Off ZWrite Off ZTest Always

        Pass
        {
            CGPROGRAM
            #pragma vertex vert_img
            #pragma fragment frag
            #include "UnityCG.cginc" // required for v2f_img

            // Properties
            sampler2D _MainTex;
            float _Radius;
            float _Soft;
            fixed4 _Color;
            sampler2D _NoiseTex;
            float4 _NoiseTex_ST;
            float _Saturate;
            float _Dist;
            
            float4 frag(v2f_img input) : COLOR {
                // sample texture for color
                float2 noise_uv = TRANSFORM_TEX(input.uv, _NoiseTex);
                float4 noise = tex2D(_NoiseTex, noise_uv) * _Color * _Saturate;
                float2 uv = input.uv;
                float2 dist = noise.rg * 2 - 1;
                dist.x *= _SinTime.z;
                dist.y *= _CosTime.z;
                uv += dist * _Dist;
                float4 base = tex2D(_MainTex, uv);
                float distFromCenter = distance(input.uv.xy, float2(0.5, 0.5));
				float vignette = smoothstep(_Radius, _Radius - _Soft, distFromCenter);
				base = lerp(noise, base, vignette);
                return base;
            }
            ENDCG
        }
    }
}
