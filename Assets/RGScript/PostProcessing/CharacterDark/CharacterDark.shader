Shader "Image Effect/CharacterDark"
{
    Properties
    {
        [PerRendererData]_MainTex ("Texture", 2D) = "white" {}
    }
    SubShader
    {
        // No culling or depth
        Cull Off ZWrite Off ZTest Always

        Pass
        {
            CGPROGRAM
            #pragma vertex vert_img
            #pragma fragment frag
            #include "UnityCG.cginc" // required for v2f_img

            // Properties
            sampler2D _MainTex;
            float4 _MainTex_TexelSize;
            float _Radius;
            float _Soft;
            float _BrightRadius;
            float _BrightSoft;
            float _CenterX;
            float _CenterY;
            
            float3 shift_col(float3 RGB, float3 shift) {
                float3 RESULT = float3(RGB);
                float VSU = shift.z*shift.y*cos(shift.x*3.14159265/180);
                    float VSW = shift.z*shift.y*sin(shift.x*3.14159265/180);
                
                    RESULT.x = (.299*shift.z+.701*VSU+.168*VSW)*RGB.x
                            + (.587*shift.z-.587*VSU+.330*VSW)*RGB.y
                            + (.114*shift.z-.114*VSU-.497*VSW)*RGB.z;
                
                    RESULT.y = (.299*shift.z-.299*VSU-.328*VSW)*RGB.x
                            + (.587*shift.z+.413*VSU+.035*VSW)*RGB.y
                            + (.114*shift.z-.114*VSU+.292*VSW)*RGB.z;
                
                    RESULT.z = (.299*shift.z-.3*VSU+1.25*VSW)*RGB.x
                            + (.587*shift.z-.588*VSU-1.05*VSW)*RGB.y
                            + (.114*shift.z+.886*VSU-.203*VSW)*RGB.z;
                
                return (RESULT);
            }
            
            float4 frag(v2f_img input) : COLOR {
                // sample texture for color
                float4 base = tex2D(_MainTex, input.uv);
                float2 position = input.uv.xy;
                position.x *= _MainTex_TexelSize.z;
                position.y *= _MainTex_TexelSize.w;
                float distFromCenter = distance(position, float2(_CenterX, _CenterY)) * _MainTex_TexelSize.x;
				float vignette = smoothstep(_Radius, _Radius - _Soft, distFromCenter);
				float birghtVignette = smoothstep(_BrightRadius, _BrightRadius - _BrightSoft, distFromCenter);
				base.rgb = shift_col(base.rgb, float3(0, birghtVignette, vignette));
                base.rgb += shift_col(float3(.1, .1, .1), float3(0, 1 - birghtVignette, 1 - vignette));
                return base;
            }
            ENDCG
        }
    }
}
