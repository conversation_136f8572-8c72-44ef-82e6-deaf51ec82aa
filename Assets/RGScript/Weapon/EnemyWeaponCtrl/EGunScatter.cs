using UnityEngine;

public class EGunScatter : R<PERSON><PERSON><PERSON>pon, WeaponInterface {
    //敌人间歇散弹
    public float speed_correction = 0;//速度补正
    public int count;               //左右偏移子弹数
    public float size = 1; // 子弹大小
    public float angleBetween;

    protected override void Awake() {
        base.Awake();
        gun_point = transform.Find("w/gun_point");
    }

    void Start() {
        rg_random.SetRandomSeed(RGGameInfo.Inst.SampleRandomSeed);
    }

    public void Attack() {
        var startAngle = - angleBetween * (count - 1) / 2;
        for (int i = 0; i < count; i++) {
            float r2 = Random.Range(-deviation, deviation);
            //GameObject temp_obj = Instantiate(bullet) as GameObject;
            GameObject temp_obj = PrefabPool.Inst.Take(bullet, gun_point.position, Quaternion.identity);
            float temp_speed = bullet_speed + Random.Range(-speed_correction, speed_correction);
            //更新武器属性到子弹里
            temp_obj.GetComponent<RGBullet>().UpdateAttribute(controller.gameObject, finalAtk, temp_speed, can_through, repel, camp);
            temp_obj.transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;
            var angleOffset = startAngle + i * angleBetween;
            var bulletAngle = (transform.eulerAngles.z + r2 + angleOffset);
            if (controller.facing < 0)
                bulletAngle = 180 - bulletAngle;
            temp_obj.transform.eulerAngles = new Vector3(0, 0, bulletAngle);
            temp_obj.GetComponent<IPrefabPoolObject>().OnTaken();
            temp_obj.transform.localScale = size * Vector3.one;
        }
        if (fx_play_alone)
            RGMusicManager.GetInstance().PlayEffect(audio_clip);
    }
}