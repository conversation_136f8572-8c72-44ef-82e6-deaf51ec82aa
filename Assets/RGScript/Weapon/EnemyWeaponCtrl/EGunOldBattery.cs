using UnityEngine;
using System.Collections;
using System.Collections.Generic;

public class EGunOldBattery : RGEWeapon, WeaponInterface {
    //battery
    public GameObject atk2_bullet;
    public int atk2_damage;
    public GameObject aim;
    private List<GameObject> the_aims = new List<GameObject>(2);
    int atk_type = 0;
    public List<Transform> gunPoints = new List<Transform>(4);

    private void Start() {
        rg_random.SetRandomSeed(RGGameInfo.Inst.SampleRandomSeed);
    }

    List<float> laserAngles = new List<float>(4);
    private bool _isUpDown;

    public override void SetAttackTrigger() {
        if (activate) {
            atk_type = rg_random.Range(0, 2);
            controller.SetTapAtk();
            controller.weapon_lock_target = false;
            controller.shooting = true;


            if (controller.target_obj != null) {
                Vector3 targetPosition = controller.target_obj.transform.position;
                Vector3 position = controller.transform.position;
                Vector3 minus = targetPosition - position;
                var isUpDown = Mathf.Abs(minus.y) > Mathf.Abs(minus.x);
                SetLaserAngle(isUpDown);
            } else {
                SetLaserAngle(atk_type == 0);
            }

            Invoke(nameof(Shoot), 0.6f);
            controller.attribute.SpeedRate -= 0.3f;
        }
    }

    // 上下 或者 左右发激光
    private void SetLaserAngle(bool isUpDown) {
        _isUpDown = isUpDown;
        laserAngles.Clear();
        var gunPoint1 = isUpDown ? gunPoints[1] : gunPoints[0];
        var gunPoint2 = isUpDown ? gunPoints[3] : gunPoints[2];
        var theAim1 = PrefabPool.Inst.Take(aim, gunPoint1.position, Quaternion.identity) as GameObject;
        theAim1.transform.parent = gunPoint1;
        theAim1.transform.localEulerAngles = new Vector3(0, 0, 0);
        var theAim2 = PrefabPool.Inst.Take(aim, gunPoint2.position, Quaternion.identity) as GameObject;
        theAim2.transform.parent = gunPoint2;
        theAim2.transform.localEulerAngles = new Vector3(0, 0, 0);
        the_aims.Clear();
        the_aims.Add(theAim1);
        the_aims.Add(theAim2);

        var eulerAngles = theAim1.transform.eulerAngles;
        var rightDir = eulerAngles.z;

        if (isUpDown) {
            // 上下发激光
            laserAngles.Add(rightDir + 90);
            laserAngles.Add(270 - rightDir);
            theAim1.transform.eulerAngles = new Vector3(0, 90, 0);
            theAim2.transform.eulerAngles = new Vector3(0, 270, 0);
        } else {
            // 左右发激光
            laserAngles.Add(rightDir);
            laserAngles.Add(180 - rightDir);
            theAim1.transform.eulerAngles = new Vector3(0, 0, 0);
            theAim2.transform.eulerAngles = new Vector3(0, 180, 0);
        }
    }

    void Shoot() {
        if (the_aims != null) {
            foreach (GameObject theAim in the_aims) {
                Destroy(theAim);
            }
        }

        anim.SetTrigger("atk_t");
        //Invoke ("EndShoot", 0.5f);
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
    }

    void EndShoot() {
        controller.weapon_lock_target = true;
        controller.shooting = false;
        controller.attribute.SpeedRate += 0.3f;
    }

    public void Attack() {
        //激光
        // Debug.Log(laserAngles);
        var gunPoint1 = _isUpDown ? gunPoints[1] : gunPoints[0];
        var gunPoint2 = _isUpDown ? gunPoints[3] : gunPoints[2];
        for (var i = 0; i < laserAngles.Count; i++) {
            var gunPoint = i == 0 ? gunPoint1 : gunPoint2;
            float laserAngle = laserAngles[i];
            var temp_obj = BulletFactory.TakeBullet(
                GetBulletInfo().SetAngle(laserAngle).SetPosition(gunPoint.position),
                GetDamageInfo()
            );
        }

        EndShoot();
    }
}