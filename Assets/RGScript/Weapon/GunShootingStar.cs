using cfg.WeaponUpgrade;
using DG.Tweening;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace RGScript.Weapon {
    public sealed class GunShootingStar : GunWave, IWeaponSpecial {

        private const string BonusStarFallingObjS1Path = "Skin/Weapon/weapon_277/Skin_1/bonus_star_falling.prefab";
        private const string BonusStarObjS1Path = "Skin/Weapon/weapon_277/Skin_1/bonus_star.prefab";
        private const string BonusGetVfxS1Path = "Skin/Weapon/weapon_277/Skin_1/bonus_star_get.prefab";

        protected override void WeaponSkinCustomReplaceResources(WeaponSkin weaponSkinConfig) {
            base.WeaponSkinCustomReplaceResources(weaponSkinConfig);
            if (WeaponSkinId == 1) {
                _bonusStarFallingObj = ResourcesUtil.Load<GameObject>(BonusStarFallingObjS1Path);
                _bonusStarObj = ResourcesUtil.Load<GameObject>(BonusStarObjS1Path);
                _bonusGetVfx = ResourcesUtil.Load<GameObject>(BonusGetVfxS1Path);
            }
        }

        #region evolution

        private float _lastBulletHitTimestamp;
        private const float BattleCheckInterval = 2f;
        private float _bonusStarTimer;
        private float _bonusStarInterval;
        private const float BonusStarIntervalOffsetByHit = 0.05f;
        private const float BonusStarMinInterval = 3f;
        private const float BonusStarMaxInterval = 4f;
        private const float BonusStarMinRange = 6.5f;
        private const float BonusStarMaxRange = 8.5f;
        private const float BonusStarRangeScaleOnY = 0.7f;
        private GameObject _bonusStarFallingObj;
        private const string BonusStarFallingObjPath = "RGPrefab/Weapon/weapon_277/bonus_star_falling.prefab";
        private GameObject _bonusStarObj;
        private const string BonusStarObjPath = "RGPrefab/Weapon/weapon_277/bonus_star.prefab";
        private AudioClip _bonusFallingAudio;
        private const string BonusFallingAudioPath = "RGPrefab/Weapon/weapon_277/bonus_star_falling.wav";
        private AudioClip _bonusGetAudio;
        private const string BonusGetAudioPath = "RGPrefab/Weapon/weapon_277/bonus_star_get.wav";
        private GameObject _bonusGetVfx;
        private const string BonusGetVfxPath = "RGPrefab/Weapon/weapon_277/bonus_star_get.prefab";
        private const float BonusStarDuration = 3f;
        private const int BonusStarEnergyRecover = 3;
        private static readonly int AnimPropertyX = Animator.StringToHash("x");
        private static readonly int AnimPropertyY = Animator.StringToHash("y");

        private float _specialAtkEnergy;
        private const string SpecialAtkEnergyCustomValueKey = "SpecialAtkEnergy";
        private const float SpecialAtkEnergyAddByBonus = 10f;
        private const float MaxSpecialAtkEnergy = 70f;
        private readonly Collider2D[] _specialAtkCastResults = new Collider2D[20];
        private static readonly int FlashAmount = Shader.PropertyToID("_FlashAmount");
        private Material _shiningMat;
        private Coroutine _checkBonusStar;
        private GameObject _fallingBonusStar;
        private int _specialAtkCounter;
        public bool IsSpecial {
            get => IsEvolvedWeapon;
        }

        public int SpecialMode => -1;

        public void WeaponSpecial(bool isDown) {
            if (!isDown || _specialAtkEnergy < MaxSpecialAtkEnergy) {
                return;
            }
            StartCoroutine(SpecialAtk());
            _specialAtkEnergy = 0f;
            UpdateEnergyBar();
            FlushIcon();
        }

        protected override void Start() {
            base.Start();
            if (!IsEvolvedWeapon) {
                return;
            }
            onBulletCreate += OnBulletCreate;
            _bonusStarFallingObj = ResourcesUtil.Load<GameObject>(BonusStarFallingObjPath);
            _bonusStarObj = ResourcesUtil.Load<GameObject>(BonusStarObjPath);
            _bonusFallingAudio = ResourcesUtil.Load<AudioClip>(BonusFallingAudioPath);
            _bonusGetAudio = ResourcesUtil.Load<AudioClip>(BonusGetAudioPath);
            _bonusGetVfx = ResourcesUtil.Load<GameObject>(BonusGetVfxPath);
            _shiningMat = new Material(Shader.Find("Sprites/Sprite Flash"));
            if (transform.Find("w").TryGetComponent(out SpriteRenderer spriteRdr)) {
                spriteRdr.material = _shiningMat;
            }
        }

        protected override void OnEnable() {
            base.OnEnable();
            if (GameUtil.IsMultiGame() && controller is RGController rgCtrl && !rgCtrl.IsLocalPlayer()) {
                return;
            }
            if (_checkBonusStar != null) {
                StopCoroutine(_checkBonusStar);
            }
            _checkBonusStar = StartCoroutine(CheckCanBonusStarSpawn());
        }

        protected override void OnDisable() {
            base.OnDisable();
            if (_checkBonusStar != null) {
                StopCoroutine(_checkBonusStar);
            }
            if (_fallingBonusStar) {
                Destroy(_fallingBonusStar);
            }
        }

        public override void SetController(RGBaseController ctrl) {
            //remove from old controller
            if (!ctrl && controller is RGController) {
                if (_checkBonusStar != null) {
                    StopCoroutine(_checkBonusStar);
                }
                if (_fallingBonusStar) {
                    Destroy(_fallingBonusStar);
                }
            }
            base.SetController(ctrl);
            //pick by player
            if (ctrl is RGController) {
                if (GameUtil.IsMultiGame() && controller is RGController rgCtrl && !rgCtrl.IsLocalPlayer()) {
                    return;
                }
                if (_checkBonusStar != null) {
                    StopCoroutine(_checkBonusStar);
                }
                _checkBonusStar = StartCoroutine(CheckCanBonusStarSpawn());
            }
        }

        private void OnBulletCreate(GameObject obj) {
            if (!obj.TryGetComponent(out DamageCarrier dmgCarrier)) {
                return;
            }
            dmgCarrier.AddHitCallback(EnemyHitCheck);
        }

        private void EnemyHitCheck(DamageCarrier dmgCarrier, GameObject tgt, BulletHitData hitData) {
            if (tgt.TryGetComponent(out RGEController rgECtrl) && rgECtrl.Alive()) {
                _lastBulletHitTimestamp = Time.time;
                _bonusStarInterval -= BonusStarIntervalOffsetByHit;
                _bonusStarInterval = Mathf.Max(1f, _bonusStarInterval);
            }
        }

        private IEnumerator CheckCanBonusStarSpawn() {
            var checkInterval = new WaitForSeconds(0.05f);
            while (gameObject && controller) {
                //check if in the battle
                if (_lastBulletHitTimestamp == 0f || Time.time - _lastBulletHitTimestamp > BattleCheckInterval) {
                    yield return checkInterval;
                    continue;
                }

                _bonusStarTimer += Time.deltaTime;
                if (_bonusStarTimer > _bonusStarInterval) {
                    _bonusStarInterval = rg_random.Range(BonusStarMinInterval, BonusStarMaxInterval);
                    _bonusStarTimer = 0f;
                    //if failed, try again immediately;
                    while (controller && !TrySpawnBonusStar()) {
                        yield return checkInterval;
                    }
                }
                yield return null;
            }
        }

        private bool TrySpawnBonusStar() {
            var randomRad = rg_random.Range(0f, Mathf.PI * 2);
            var randomPos = new Vector3(Mathf.Cos(randomRad), Mathf.Sin(randomRad));
            randomPos = randomPos * rg_random.Range(BonusStarMinRange, BonusStarMaxRange);
            randomPos.y *= BonusStarRangeScaleOnY;
            if (controller && controller.target_obj) {
                randomPos += (controller.target_obj.position + transform.position) * 0.5f;
            } else {
                randomPos += transform.position;
            }

            var room = RGRoomX.FindNearestRoom(transform.position);
            if (!room) {
                controller.StartCoroutine(SpawnBonusStar(randomPos));
                return true;
            }
            if (!room.IsPositionOutOfRoom(randomPos, -0.5f)) {
                controller.StartCoroutine(SpawnBonusStar(randomPos));
                return true;
            }
            randomPos += (transform.position - randomPos) * 2f;
            if (!room.IsPositionOutOfRoom(randomPos, -0.5f)) {
                controller.StartCoroutine(SpawnBonusStar(randomPos));
                return true;
            }

            return false;
        }

        private IEnumerator SpawnBonusStar(Vector3 pickPos) {
            if (_fallingBonusStar) {
                Destroy(_fallingBonusStar);
            }
            var dropPos = pickPos - (Vector3)rg_random.RandomInCircle(1f, 1f);
            _fallingBonusStar = Instantiate(_bonusStarFallingObj, dropPos, Quaternion.identity, GameUtil.TempRoot);
            if (_fallingBonusStar.TryGetComponent(out Animator fallingObjAnim)) {
                fallingObjAnim.SetFloat(AnimPropertyX, Random.value - 0.5f);
            }
            RGMusicManager.GetInstance().PlayEffect(_bonusFallingAudio);
            var fallingDuration = new WaitForSeconds(1f);
            yield return fallingDuration;
            Destroy(_fallingBonusStar);
            var pickableBonusStar = Instantiate(_bonusStarObj, dropPos, Quaternion.identity, GameUtil.TempRoot);
            var posOffset = pickPos - dropPos;
            if (pickableBonusStar.TryGetComponent(out Animator bonusObjAnim)) {
                bonusObjAnim.SetFloat(AnimPropertyX, posOffset.x);
                bonusObjAnim.SetFloat(AnimPropertyY, posOffset.y);
            }
            var timestamp = Time.time;
            var spawningDuration = new WaitForSeconds(0.5f);
            var bonusPickRange = 1.5f;
            yield return spawningDuration;
            var bonusObjPos = pickableBonusStar.transform.GetChild(0).position;
            var checkInterval = new WaitForSeconds(0.1f);
            while (Time.time - timestamp < BonusStarDuration && gameObject && gameObject.activeInHierarchy) {
                if ((bonusObjPos - transform.position).sqrMagnitude < bonusPickRange * bonusPickRange) {
                    if (_specialAtkEnergy >= MaxSpecialAtkEnergy) {
                        controller.Attribute.RestoreEnergy(BonusStarEnergyRecover, singleText: true, playEffect: true);
                    }
                    _specialAtkEnergy += SpecialAtkEnergyAddByBonus;
                    UpdateEnergyBar();
                    FlushIcon();
                    RGMusicManager.GetInstance().PlayEffect(_bonusGetAudio);
                    Instantiate(_bonusGetVfx, bonusObjPos, Quaternion.identity, GameUtil.TempRoot);
                    WeaponFlash();
                    break;
                }
                yield return checkInterval;
            }
            Destroy(pickableBonusStar);
        }

        private void WeaponFlash() {
            var stgFlashAmount = 0.8f;
            DOTween.To(() => stgFlashAmount, x => stgFlashAmount = x, 0f, 0.8f)
                .SetEase(Ease.InOutQuad)
                .OnUpdate(() => {
                    _shiningMat.SetFloat(FlashAmount, stgFlashAmount);
                });
        }

        private IEnumerator SpecialAtk() {
            const int maxStarTimes = 5;
            var interval = new WaitForSeconds(0.8f);
            _specialAtkCounter = _specialAtkCastResults.Length;
            for (int i = 0; i < maxStarTimes; i++) {
                var pos = transform.position;
                var layer = LayerMask.GetMask("Body_E");
                var hitCount = Physics2D.OverlapCircleNonAlloc(pos, 20f, _specialAtkCastResults, layer);
                hitCount = Mathf.Min(hitCount, _specialAtkCastResults.Length);
                for (int j = 0; j < hitCount; j++) {
                    _specialAtkCounter--;
                    StartCoroutine(SpawnSpecialAtkStar(_specialAtkCastResults[j].gameObject));
                    if (_specialAtkCounter <= 0) {
                        yield break;
                    }
                }
                yield return interval;
            }
        }

        private IEnumerator SpawnSpecialAtkStar(GameObject atkStarTarget) {
            var atkStarBulletInfo = GetBulletInfo(1).SetAngle(Random.Range(-15f, 15f));
            var atkStar = BulletFactory.TakeBullet(atkStarBulletInfo, GetDamageInfo(1));
            var pos = Vector3.zero;
            while (atkStar) {
                if (atkStarTarget) {
                    pos = atkStarTarget.transform.position;
                }
                atkStar.transform.position = pos;
                yield return null;
            }
        }

        public override Dictionary<string, int> GetCustomValues() {
            var dict = base.GetCustomValues();
            dict[SpecialAtkEnergyCustomValueKey] = Mathf.RoundToInt(_specialAtkEnergy);
            return dict;
        }

        public override void SetUpCustomValues(Dictionary<string, int> customValues) {
            base.SetUpCustomValues(customValues);
            if (customValues != null && customValues.TryGetValue(SpecialAtkEnergyCustomValueKey, out var energyValue)) {
                _specialAtkEnergy = energyValue;
            }
        }

        private void UpdateEnergyBar(ShowSpecialButtonEvent e = null) {
            if (controller is not RGController rgCtrl
                || rgCtrl.hand.front_weapon != this || !rgCtrl.IsLocalPlayer()) {
                return;
            }
            var progress = Mathf.Clamp01(_specialAtkEnergy / MaxSpecialAtkEnergy);
            SimpleEventManager.Raise(new SpecialBtnProgressEvent(progress));
        }

        private IEnumerator DelayUpdateEnergy() {
            //ensure order when multiple weapons have progress bar on specialBtn
            yield return new WaitForEndOfFrame();
            UpdateEnergyBar();
        }

        public override void StartUseWeapon() {
            base.StartUseWeapon();
            AddSpecialAtkEnergyHandler();
        }

        public override void StopWeapon() {
            base.StopWeapon();
            RemoveSpecialAtkEnergyHandler();
        }

        private void AddSpecialAtkEnergyHandler() {
            SimpleEventManager.AddEventListener<ShowSpecialButtonEvent>(UpdateEnergyBar);
            if (gameObject.activeInHierarchy) {
                StartCoroutine(DelayUpdateEnergy());
            }
        }

        private void RemoveSpecialAtkEnergyHandler() {
            SimpleEventManager.RemoveEventListener<ShowSpecialButtonEvent>(UpdateEnergyBar);
            if (controller is not RGController rgCtrl || !rgCtrl.IsLocalPlayer()) {
                return;
            }
            SimpleEventManager.Raise(new SpecialBtnProgressEvent(0f));
        }

        #endregion

    }
}
