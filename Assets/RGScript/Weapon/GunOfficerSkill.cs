
using DG.Tweening;
using UnityEngine;
using System.Collections;
using I2.Loc;
using UnityEngine.Animations;
using UnityEngine.Events;

/// <summary>
/// 警官技能枪
/// </summary>
public class GunOfficerSkill : RGWeapon, WeaponInterface {
    public string weaponNameId;
    internal int bulletCount = 0;
    public float delay = 0.1f;
    public int maxCount = 6;
    public UnityEvent onReloadGun;
    public UnityEvent onAttack;
    internal bool upgraded;
    bool isShooting;
    C16Controller officer;
    private SpriteRenderer _renderer;
    public System.Action onFinished{get; set;}

    protected override void Awake() {
        base.Awake();
        _renderer = transform.Find($"w").gameObject.GetComponent<SpriteRenderer>();
        if (weapon_type == emWeaponType.Default) {
            weapon_type = emWeaponType.Gun;
        }
    }
    
    public override void FlushIcon(bool leave = false) {
        base.FlushIcon(leave);
        if (ShowSelfIcon) {
            UICanvas.Inst.ChangeWeaponConsume(bulletCount, Color.white);
        }
    }

    public override void SetAttack(bool value1, bool manual = true) {
        if (value1 && !isShooting) {
            StartCoroutine(Shooting());
        }
    }

    /// <summary>
    /// 给武器装填子弹
    /// </summary>
    /// <param name="officer">警官</param>
    /// <param name="count">子弹装填数量</param>
    /// <returns>实际装填数量</returns>
    public int ReloadGun(C16Controller officer, int count) {
        if (isShooting) {
            return 0;
        }
        this.officer = officer;
        int oldCount = bulletCount;
        bulletCount = Mathf.Min(maxCount, bulletCount + count);
        WeaponBulletVisualize(officer, bulletCount);
        
        int deltaCount = bulletCount - oldCount;
        onReloadGun.Invoke();
        FlushIcon();
        return deltaCount;
    }

    IEnumerator Shooting() {
        if (!isShooting) {
            bulletCount = Mathf.Min(bulletCount, controller.attribute.energy);
            if (bulletCount > 0) {
                var finalDelay = delay / _speedFactor;
                var wait = new WaitForSeconds(finalDelay);
                while (bulletCount > 0) {
                    isShooting = true;
                    bulletCount--;
                    anim.Play("weapon_pistol", 0, 0);
                    WeaponBulletVisualize(officer, bulletCount);
                    yield return wait;
                }
                AfterAttackEvent();
            } else {
                if (controller is RGController c) {
                    c.NoWord();
                }
            }
            onFinished?.Invoke();
            onFinished = null;
            DestroySelf();
        }
    }

    public void Attack() {
        AddAttackCount();
        int bulletIndex = upgraded ? 1 : 0;
        var actionInfo = new ActionInfo();
        actionInfo.onHit += OnHitEnemy;
        var damageInfo = GetDamageInfo(bulletIndex);
        damageInfo.damage = officer.GetSkill0DamageFector(damageInfo.damage);
        var bulletInfo = GetBulletInfo(bulletIndex);
        bulletInfo.speed *= 1 + officer.duelBulletSpeed;
        bulletInfo.size *= 1 + officer.duelBulletSize;
        var bullet = BulletFactory.TakeBullet(
            bulletInfo,
            damageInfo,
            actionInfo
            );
        BulletCreateEvent(bullet);
        AfterBulletCreateEvent();
        MakeConsume();
        onAttack.Invoke();
        FlushIcon();
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
    }

    private void OnHitEnemy(DamageCarrier damageCarrier, GameObject target, BulletHitData bulletBitData) {
        officer.OnSkill0HitEnemy(target);
    }

    public override void StopWeapon() {
        base.StopWeapon();
        if (!destroyed) {
            onFinished?.Invoke();
            onFinished = null;
            DestroySelf();
        }
    }

    bool destroyed = false;
    void DestroySelf() {
        if (!destroyed) {
            destroyed = true;
            DropWeapon(2);
            Destroy(gameObject);
            officer.OnSkill0ShootEnd();
        }
    }

    public override string GetItemName() {
        string local_name = ScriptLocalization.Get(weaponNameId);
        if (GameUtil.IsDefenceMode() && level > 0) {
            local_name += RGItem.GetStringWithColor("+" + level, (ItemLevel)item_level);
        }
        if (weapon_item) {
            local_name += RGItem.GetStringWithColor("★", (ItemLevel)weapon_item.GetItemLevel());
        }
        return local_name;
    }

    private void WeaponBulletVisualize(C16Controller officer, int bulletCount) {
        Skill0CustomWeaponCharge();
        Skill0CustomWeaponSkin(officer.GetSkinIndex());
    }

    private void Skill0CustomWeaponCharge() {
        if (!officer.IsUseChargeSkin) {
            return;
        }

        Transform chargeHead = transform.Find("w/charge_head");
        Transform charge = transform.Find("w/charge");
        bool hasCharge = null != charge;

        var positionConstraint = chargeHead.GetComponent<PositionConstraint>();
        if (positionConstraint != null && positionConstraint.sourceCount == 0) {
            positionConstraint.AddSource(new ConstraintSource() {
                sourceTransform = officer.transform,
                weight = 1
            });
        }

        positionConstraint.constraintActive = true;

        var rotationConstraint = chargeHead.GetComponent<RotationConstraint>();
        if (rotationConstraint != null && rotationConstraint.sourceCount == 0) {
            rotationConstraint.AddSource(new ConstraintSource() {
                sourceTransform = officer.transform,
                weight = 1
            });
        }

        rotationConstraint.constraintActive = true;

        for (int i = 0; i < 7; i++) {
            bool isShow = bulletCount > i;
            if (hasCharge) {
                charge.Find(i.ToString()).gameObject.SetActive(isShow);
            }
            chargeHead.Find($"root/{i}").gameObject.SetActive(isShow);
        }
    }

    private void Skill0CustomWeaponSkin(int skinIndex) {
        if (skinIndex == 8) {
            transform.Find($"w/charge/0").gameObject.SetActive(bulletCount > 0);
            _renderer.enabled = bulletCount <= 0;
        }else if (skinIndex == 11) {
            var count = bulletCount;
            var bullets = transform.Find("w/Bullets");
            if (!bullets) {
                return;
            }

            for (int i = 0; i < bullets.childCount; i++) {
                bullets.GetChild(i).gameObject.SetActive(count > 0);
                count--;
            }
        }else if (skinIndex == 14) {
            var positionConstraint = transform.Find($"Bullets").GetComponent<PositionConstraint>();
            if (positionConstraint != null && positionConstraint.sourceCount == 0) {
                positionConstraint.AddSource(new ConstraintSource() {
                    sourceTransform = officer.transform,
                    weight = 1
                });
            }

            positionConstraint.constraintActive = true;

            var rotationConstraint = transform.Find($"Bullets").GetComponent<RotationConstraint>();
            if (rotationConstraint != null && rotationConstraint.sourceCount == 0) {
                rotationConstraint.AddSource(new ConstraintSource() {
                    sourceTransform = officer.transform,
                    weight = 1
                });
            }

            rotationConstraint.constraintActive = true;

            for (int i = 1; i < 7; i++) {
                var spear = transform.Find($"Bullets/{i}").gameObject;
                if (bulletCount > i) {
                    spear.SetActive(true);
                } else {
                    spear.SetActive(false);
                }
            }

            if (bulletCount > 7) {
                return;
            }
            
            for (int i = 0; i < bulletCount; i++) {
                if (i == 0) {
                    continue;
                }

                var spear = transform.Find($"Bullets/{i}");
                if (!spear) {
                    break;
                }
                
                if ((bulletCount - 1) % 2 != 0) {
                    if (i % 2 != 0) {
                        spear.transform.eulerAngles = new Vector3(0, 0, 60 - 15 * i);
                    } else {
                        spear.transform.eulerAngles = new Vector3(0, 0, 45 + 15 * i);
                    }
                } else {
                    if (i % 2 != 0) {
                        spear.transform.eulerAngles = new Vector3(0, 0, 45 - 15 * i);
                    } else {
                        spear.transform.eulerAngles = new Vector3(0, 0, 30 + 15 * i);
                    }
                }
            }

            var bulletSprites = transform.Find("Bullets").GetComponentsInChildren<SpriteRenderer>(true);
            foreach (var sprite in bulletSprites) {
                sprite.sortingOrder = 0;
            }
        }
        
    }
}