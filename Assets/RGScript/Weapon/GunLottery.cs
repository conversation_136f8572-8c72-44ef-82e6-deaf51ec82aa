using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 彩票
/// 平均收益24.26金币
/// </summary>
public class GunLottery : RGWeapon {
    public AudioClip audioNothing;
    public Sprite spriteNothing;
    public Sprite spriteReward;
    bool canReward;
    int ran;
    RGController tossController;

    protected override void Start() {
        base.Start();
        limitUseInMultiRoom = true;
    }

    public override void SetAttack(bool value1, bool manual = true) {
        if ((controller is RGController) && value1 && !anim.GetCurrentAnimatorStateInfo(0).IsName("use")) {
            if (LimitUseInMultiRoom()) {
                return;
            }
            RGMusicManager.Inst.PlayEffect(audio_clip);
            anim.SetTrigger("atk_t");
            Toss();
        }
    }
    void Toss() {
        //float adverage = 666 * 0.01f + 100 * 0.05f + 50 * 0.15f + 15 * 0.29f;
        ran = transform.Find("ran").GetComponent<SerializedRandomBahaviour>().rg_random.Range(0, 100);
        ran /= BattleData.data.CompareFactor(emBattleFactor.GoodLuck) ? 2 : 1;
        ran *= BattleData.data.CompareFactor(emBattleFactor.BadLuck) ? 2 : 1;
        canReward = controller is RGController && (controller as RGController).hand.front_weapon == this;
        if (canReward) {
            tossController = controller as RGController;
        }
    }
    void CreateCoin(int coinIndex, int count) {
        if (BattleData.data.IsDefenceMode) {
            count *= level;
        }
        
        if (tossController && tossController.IsLocalPlayer()) {
            for (int i = 0; i < count; i++) {
                var coin = Instantiate<GameObject>(PrefabManager.GetCoin(coinIndex), transform.position, Quaternion.identity, RGGameSceneManager.Inst.temp_objects_parent);
                coin.GetComponent<RGCoin>().GetForce(new Vector2(Random.Range(-1f, 1f), Random.Range(-1f, 1f)), Random.Range(5, 15));
            }
        }
    }
    public void OnSpecialAnimFinish() {
        bool hasReward = ran < 50;
        transform.Find("w").GetComponent<SpriteRenderer>().sprite = hasReward ? spriteReward : spriteNothing;
        if (canReward) {//防止幻影权杖/骑士技能复制
            tossController.StartCoroutine(GetReward());
        }
    }
    IEnumerator GetReward() {
        yield return new WaitForSeconds(0.7f);
        int rewardIndex = 4;
        if (ran < 1) {//特等奖,666G
            rewardIndex = 0;
            CreateCoin(0, 130);
            CreateCoin(1, 4);
            CreateCoin(2, 4);
        } else if (ran < 6) {//一等奖100G
            rewardIndex = 1;
            CreateCoin(0, 20);
        } else if (ran < 21) {//二等奖50G
            rewardIndex = 2;
            CreateCoin(0, 10);
        } else if (ran < 50) {//三等奖15G
            rewardIndex = 3;
            CreateCoin(1, 4);
            CreateCoin(2, 3);
        } else {//没中奖
            RGMusicManager.Inst.PlayEffect(audioNothing);
        }
        UICanvas.Inst.ShowTextTalk(tossController.transform, I2.Loc.ScriptLocalization.Get($"lottery_{rewardIndex}"), 2f, 2f, 4 - rewardIndex);
        if (tossController && tossController.hand.front_weapon == this) {
            tossController?.DropWeapon();
        }
        AddRecord(); // drop weapon 会删除消耗记录，这里补一下
        Destroy(gameObject);
    }

    public override string[] texts {
        get {
            int p = 50;
            if (BattleData.data.CompareFactor(emBattleFactor.GoodLuck)) {
                p *= 2;
            }
            if (BattleData.data.CompareFactor(emBattleFactor.BadLuck)) {
                p /= 2;
            }
            var t = base.texts;
            t[2] = p.ToString();
            return t;
        }
    }

    public override bool IsHold() {
        return true;
    }
}

