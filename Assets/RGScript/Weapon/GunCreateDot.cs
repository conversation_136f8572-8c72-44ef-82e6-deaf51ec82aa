using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 定期产生能量球/血球的武器
/// </summary>
public class GunCreateDot : GunCreate {

    public override void OnCreate(GameObject go) {
        base.OnCreate(go);
        go.GetComponent<RGEnergy>().GetForce(new Vector2(rg_random.Range(-1, 1), rg_random.Range(0.25f, 1)), rg_random.Range(5, 15));
        Destroy(go, 20f);
    }

}
