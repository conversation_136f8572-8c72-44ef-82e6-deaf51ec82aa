using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class GunInitRogue : R<PERSON><PERSON><PERSON>pon, IDoubleWeapon, WeaponInterface, IWeaponSpecial {
    public List<string> startTriggers;
    public GameObject bullet2;
    public GameObject right_hand_bullet;
    public GameObject left_hand_bullet;
    public GameObject sting_bullet;
    public GameObject left_sting_bullet;
    public float melee_distance = 4;
    public float melee_right_scale;
    public float melee_left_scale;
    public int atk_force = 0;
    public float throw_offset = 2.5f;
    public int sting_atk = 8;
    public int sword_atk = 8;
    public bool rotateBullet = true;
    private int StingAtk => DecorateAdditionLevel(bulletsInfo[1].GetDamageInfo()).damage;
    public int SwordAtk => DecorateAdditionLevel(bulletsInfo[2].GetDamageInfo()).damage;
#if UNITY_EDITOR
    internal bool can_throw = true;
#endif

    Transform left;
    Transform right;
    Transform gun_point_sting;
    Transform gun_point_throw_left; // 左手武器投掷位置
    SpriteRenderer right_renderer;
    Sprite<PERSON>enderer left_renderer;
    SpriteRenderer right_renderer_1; // 霓光武器
    SpriteRenderer left_renderer_1;
    int previous_melee_mode = 0;
    int throw_consume;

    int atk_count {
        set {
            anim.SetInteger("atk_count", value);
        }
        get {
            return anim.GetInteger("atk_count");
        }
    }

    int atk_count_throw {
        set {
            anim.SetInteger("atk_count_throw", value);
        }
        get {
            return anim.GetInteger("atk_count_throw");
        }
    }

    /// <summary>
    /// 目标距离近或蓝不够时使用近战
    /// </summary>
    bool should_melee {
        get {
            return controller &&
                   ((controller.target_obj != null &&
                     Vector2.Distance(controller.target_obj.position,
                         controller.transform.position) < melee_distance
                       ) || controller.attribute.energy < throw_consume);
        }
    }

    public bool IsSpecial {
        get {
            return false;
        }
    }

    public int SpecialMode => forceMelle ? 1 : 0;

    protected override void Awake() {
        WeaponItemConfigInit();
        anim = GetComponent<Animator>();
        right = transform.GetChild(1);
        left = transform.GetChild(2);
        gun_point = right.GetChild(0).GetChild(0);
        gun_point_sting = left.GetChild(0).GetChild(0).GetChild(0);
        gun_point_throw_left = left.GetChild(0).GetChild(0);
        right_renderer = right.GetChild(0).GetChild(0).GetComponent<SpriteRenderer>();
        left_renderer = left.GetChild(0).GetChild(0).GetComponent<SpriteRenderer>();
        if (!right_renderer && !left_renderer) {
            right_renderer_1 = right.GetChild(0).GetChild(0).GetChild(1).GetComponent<SpriteRenderer>();
            left_renderer_1 = left.GetChild(0).GetChild(0).GetChild(1).GetComponent<SpriteRenderer>();
        }

        if (weapon_type == emWeaponType.Default)
            weapon_type = emWeaponType.Throw;
        throw_consume = consume;
        gameObject.SetWeaponZPos();
        
        Init();
    }

    public override void AttackStop() {
        base.AttackStop();
        atk_count = 0;
    }

    /// <summary>
    /// 0：近战右手 1：远程 2：近战左手
    /// </summary>
    public void CheckMode() {
#if UNITY_EDITOR
        if (should_melee || !can_throw)
#else
            if (should_melee)
#endif
        {
            anim.SetInteger("mode", 0);
            consume = 0;
        } else {
            anim.SetInteger("mode", 1);
            consume = throw_consume;
        }

        if (forceMelle) {
            anim.SetInteger("mode", 0);
            consume = 0;
        }

        FlushIcon();
    }

    public override void AttackKeyDown(bool manual) {
        base.AttackKeyDown(manual);

        
        //宠物模仿
        if (controller is RGPetController) {
            if (atk_b) {
                SetPetAttackTrigger(true);
            }
        }
    }

    public void Attack() {
        //SwordAttack (right_hand_bullet, gun_point.position, audio_clip, false, SwordAtk, 0, melee_right_scale);
        Vector3 offset = right.right.normalized * 0.45f * melee_left_scale;
        var tfPos = transform.position;
        SwordAttack(left_hand_bullet, tfPos + offset, audio_clip, true, SwordAtk, 0, melee_right_scale);
        SwordAttack(right_hand_bullet, tfPos - offset, audio_clip, false, SwordAtk, 0, melee_left_scale);
        atk_count = 1;
    }

    void Attack2() {
        //SwordAttack (left_hand_bullet, gun_point_sting.position, audio_clip, true, SwordAtk, 0, melee_left_scale);
        Vector3 offset = right.right.normalized * 0.45f * melee_left_scale;
        var tfPos = transform.position;
        SwordAttack(right_hand_bullet, tfPos + offset, audio_clip, true, SwordAtk, 0, melee_left_scale);
        SwordAttack(left_hand_bullet, tfPos - offset, audio_clip, false, SwordAtk, 0, melee_right_scale);
        atk_count = 2;
    }

    void Attack3() {
        //SwordAttack(sting_bullet, gun_point_sting.position, audio_clip, false, StingAtk, atk_force, melee_right_scale);
        SwordAttack(sting_bullet, gun_point.position, audio_clip, false, StingAtk, atk_force, melee_right_scale);
        SwordAttack(left_sting_bullet, gun_point_throw_left.position, audio_clip, false, StingAtk, atk_force, melee_right_scale);
        atk_count = 0;
    }

    void ThrowAttack1() {
        atk_count_throw = 1;
        ThrowAttack(bullet, gun_point.position, throw_offset);
    }

    void ThrowAttack2() {
        atk_count_throw = 0;
        ThrowAttack(bullet2, gun_point.position, throw_offset);
    }

    /// <param name="bullet">
    /// 近战的子弹
    /// </param>
    /// <param name="position">
    /// 近战子弹生成的位置
    /// </para>
    /// <param name="audio_clip">
    /// 攻击时需要播放的音效
    /// </para>
    /// <param name="is_reverse">
    /// 是否需要反转特效
    /// </param>
    /// <param name="force">
    /// 攻击时角色受力（产生位移的力）
    /// </param>
    void SwordAttack(GameObject bullet, Vector3 position, AudioClip audio_clip, bool is_reverse, int atk, int force, float scale) {
        GameObject temp_obj = BulletFactory.TakeBullet(
            GetBulletInfo().SetBullet(bullet).SetPosition(position).SetBulletSize(scale),
            GetDamageInfo().SetDamage(atk));
        temp_obj.transform.localScale = new Vector3(controller.facing * (is_reverse ? -1 : 1), is_reverse ? -1 : 1, 1);
        controller.GetForce(temp_obj.transform.right, controller.facing > 0 ? force : -force, false);
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        //temp_obj.GetComponent<Animator>().speed = weapon_speed * _speedFactor;

        BulletCreateEvent(temp_obj);
        AfterBulletCreateEvent();
        AfterAttackEvent();
    }

    void ThrowAttack(GameObject bulletProto, Vector3 position, float throw_offset) {
        var finalDeviation = GameUtil.GetFinalDeviation(this);
        var displacement = position - transform.position;
        var emitPos = (transform.position + displacement.magnitude * controller.oriantation);
        float angle = (controller.facing < 0 ? 180 - GetThrowAngle(throw_offset) : GetThrowAngle(throw_offset)) + finalDeviation;
        GameObject temp_obj = BulletFactory.TakeBullet(
            GetBulletInfo().SetBullet(bulletProto).SetAngle(angle).SetPosition(emitPos),
            GetDamageInfo());
        if (rotateBullet && controller.facing < 0) {
            temp_obj.transform.localScale = new Vector3(1, -1, 1);
            var bullet = temp_obj.GetComponent<RGBullet>();
            bullet.rotate_angle = Mathf.Abs(bullet.rotate_angle) * -controller.facing;
        }

        BulletCreateEvent(temp_obj);
        AfterBulletCreateEvent();
        MakeConsume();
        AfterAttackEvent();
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
    }

    /// <summary>
    /// 修正旋转角
    /// </summary>
    float GetThrowAngle(float offset) {
        return controller.FixedAngle;
    }

    public override void StartUseWeapon() {
        base.StartUseWeapon();
        foreach (var triggerName in startTriggers) {
            anim.SetTrigger(triggerName);
        }
    }

    public override void StopWeapon() {
        if (left_renderer && !left_renderer.enabled) {
            left_renderer.enabled = true;
        }

        if (right_renderer && !right_renderer.enabled) {
            right_renderer.enabled = true;
        }

        base.StopWeapon();
        PositionInit();
    }

    public override void LockedProcess(float targetFixedAngle) {
        left.eulerAngles = new Vector3(0, controller.facing > 0 ? 0 : 180, targetFixedAngle);
        right.eulerAngles = new Vector3(0, controller.facing > 0 ? 0 : 180, targetFixedAngle);
    }

    public override void OnReborn() {
        base.OnReborn();
        SpriteLayerSetting(7);
    }

    /// <summary>
    /// 两把武器跟随角色旋转
    /// </summary>
    public override void SpriteLayerSetting(int target_layer, bool is_pet = false) {
        if (left_renderer&&right_renderer) {
            if (is_pet) {
                right_renderer.sortingOrder = target_layer;
                left_renderer.sortingOrder = target_layer - 2;
            } else {
                if (target_layer == 7 || target_layer == 5) {
                    right_renderer.enabled = true;
                    left_renderer.enabled = true;
                    right_renderer.sortingOrder = target_layer;
                    left_renderer.sortingOrder = target_layer - 3;
                } else {
                    base.SpriteLayerSetting(target_layer);
                }
            }
        } else if (left_renderer_1 && right_renderer_1) {
            if (is_pet) {
                right_renderer_1.sortingOrder = target_layer;
                left_renderer_1.sortingOrder = target_layer - 2;
            } else {
                if (target_layer == 7 || target_layer == 5) {
                    right_renderer_1.enabled = true;
                    left_renderer_1.enabled = true;
                    right_renderer_1.sortingOrder = target_layer;
                    left_renderer_1.sortingOrder = target_layer - 3;
                } else {
                    base.SpriteLayerSetting(target_layer);
                }
            }
        }
    }

    public override void SetAttack(bool value1, bool manual = true) {
        if (controller) {
            if (activate) {
                if (value1) {
                    AttackKeyDown(manual);
                } else if (!value1) {
                    if (!_autoAttack) {
                        AttackKeyUp(manual);
                    }
                }
            } else {
                UICanvas.GetInstance().ShowTextTalk(controller.transform, "...", 2.5f, 1f, 0);
            }
        }
    }

    /// <summary>
    /// 骑士使用双持武器时施放技能后增强武器属性
    /// </summary>
    public RGWeapon DoubleWeaponBoostStart(out bool doubleWeapon) {
        _speedFactor *= 2;
        ResetWeaponSpeed();
        doubleWeapon = false;
        return null;
    }

    /// <summary>
    /// 骑士技能结束后调用
    /// </summary>
    public void DoubleWeaponBoostEnd() {
        _speedFactor /= 2;
        ResetWeaponSpeed();
    }

    public override bool IsHold() {
        return true;
    }

    void PositionInit() {
        right.localRotation = Quaternion.identity;
        left.localRotation = Quaternion.identity;
    }

    bool forceMelle;

    public void WeaponSpecial(bool isDown) {
        forceMelle = isDown;
        SetAttack(isDown);
    }

    public override string[] texts {
        get {
            var min_atk = Mathf.Min(damageFinal, StingAtk, SwordAtk);
            var max_atk = Mathf.Max(damageFinal, StingAtk, SwordAtk);
            if (min_atk == max_atk) {
                _texts[0] = "" + max_atk;
            } else {
                _texts[0] = string.Format("{0}~{1}", min_atk, max_atk);
            }

            _texts[1] = "" + consume;
            _texts[2] = "" + criticalFinal;
            _texts[3] = "" + clampedRuntimeDeviation;
            return _texts;
        }
    }
}