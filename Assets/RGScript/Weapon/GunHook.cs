using System.Collections;
using UnityEngine;

public class GunHook : RGWeapon {
    private bool hook = true;
    private bool attackable;
    public SpriteRenderer hook_sprite;

    protected override void Start() {
        base.Start();
        hook_sprite = transform.Find("hook").GetComponent<SpriteRenderer>();
        Attackable();
    }

    public void ReleaseHook() {
        hook = false;
        hook_sprite.enabled = false;
    }

    public void StoreHook() {
        hook = true;
        hook_sprite.enabled = true;
    }

    public void AttackEvent() {
        if (gameObject.activeSelf && hook && attackable) {
            Invoke(nameof(AttackWaitFrame), Time.deltaTime);
            ReleaseHook();
            Disattackable();
            Invoke("Attackable", 1f / weapon_speed);
        }
    }

    void AttackWaitFrame() {
        Attack();
    }

    public void Attackable() {
        attackable = true;
    }

    public void Disattackable() {
        attackable = false;
    }

    public virtual void Attack() {
        AddAttackCount();
        float _angle = 0f;
        if (controller) {
            _angle = controller.FixedAngle;
        }

        CreateBullet(bullet_speed, _angle);
        AfterBulletCreateEvent();
        MakeConsume();
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        AfterAttackEvent();
    }

    public void CreateBullet(float speed, float angle) {
        var finalDeviation = GameUtil.GetFinalDeviation(this);
        var temp_obj = BulletFactory.TakeBullet(
            GetBulletInfo().SetAngle(controller.facing < 0 ? 180 - finalDeviation - angle : angle + finalDeviation).SetSpeed(speed).SetSourceWeapon(this),
            GetDamageInfo()
        );
        BulletCreateEvent(temp_obj);
    }
}