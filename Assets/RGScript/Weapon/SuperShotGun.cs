using UnityEngine;

public class SuperShotgun : Gun015 {
    public override void Attack() {
        var tempObj = BulletFactory.TakeBullet(
            GetBulletInfo()
                .SetPosition(createBulletAtGunpoint ? gun_point.transform.position : transform.parent.position),
            GetDamageInfo()
        );
        if (createBulletAtGunpoint)
            tempObj.transform.SetParent(gun_point.transform);
        tempObj.transform.localScale = new Vector3(controller.facing, 1, 1);
        controller.GetForce(tempObj.transform.right * (controller.facing > 0 ? 1 : -1), force, false);
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        BulletCreateEvent(tempObj);
        AfterBulletCreateEvent();
        MakeConsume();
        AfterAttackEvent();
        if (controller.attribute.energy < consume) {
            SetAttack(false);
        }
    }

    private void ProcessWeaponDevelopBullet() {
        if (!IsEvolvedWeapon) {
            return;
        }

        GetComponent<SuperShotGunExtra>().ProcessWeaponDevelopBullet();
    }

    public override void OnWeaponProcessEvolutionEvent(WeaponProcessEvolutionEvent e) {
        base.OnWeaponProcessEvolutionEvent(e);
        if (!e.isEvolved || e.Weapon != this) {
            return;
        }

        GetComponent<SuperShotGunExtra>().ProcessWeaponDevelopBullet();
    }
}