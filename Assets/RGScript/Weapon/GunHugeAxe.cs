using System;
using UnityEngine;

public class GunHugeAxe : RG<PERSON>eapon, WeaponInterface, ChargeWeaponInterface, IDelayReleaseWeapon {
    public float maxTime = 1f;
    public AudioClip holdClip;
    public GameObject reloadPrefab;
    private GameObject _reloadGo;
    private SpriteRenderer _weaponSpriteRenderer;
    private Transform _weaponTransform;
    private float _holdTime = float.NaN;
    private GameObject _bulletGo;
    private readonly GameObjectSingleCache<RGBullet> _bulletCache = new GameObjectSingleCache<RGBullet>();
    
    public override bool IsHold() => true;
    protected override emWeaponType default_type => emWeaponType.Sword;

    private static readonly int Back = Animator.StringToHash("back");
    private static readonly int Atk = Animator.StringToHash("atk");
    private static readonly int HoldAtk1 = Animator.StringToHash("hold_atk");

    private bool BackState { set => anim.SetBool(Back, value); }
    
    private bool ShouldShowReloadGo => _reloadGo == null && _holdTime > maxTime * .2f;
    private bool IsHolding => _holdTime > maxTime * .2f && _holdTime <= maxTime;
    public float GetMaxChargeTime() => maxTime;

    public WeaponChargeState GetWeaponChargeState() {
        if (GetChargeAmount() == 0 || float.IsNaN(_holdTime)) {
            return WeaponChargeState.Normal;
        }
        
        if (GetChargeAmount() < 1) {
            return WeaponChargeState.Charging;
        }

        return WeaponChargeState.FullCharged;
    }

	public float GetChargeAmount() {
        return float.IsNaN(_holdTime) ? 0 : (_holdTime / GetMaxChargeTime());
    }

    public void SetChargeAmount(float amount) {
        if (_reloadGo != null) {
            _reloadGo.GetComponent<ReloadClip>().SetReloadProgress(amount, false);
        }
        _holdTime = amount * GetMaxChargeTime();
    }

    public Action<GameObject> onDelayedBulletRelease { get; set; }

    private Vector3 _initLocalPos;
    private Quaternion _initLocalRot;
    protected override void Start() {
        _weaponTransform = transform.GetChild(0);
        _initLocalPos = _weaponTransform.localPosition;
        _initLocalRot = _weaponTransform.localRotation;
        _weaponSpriteRenderer = _weaponTransform.GetComponent<SpriteRenderer>();
        base.Start();
    }

    public virtual void Attack() {
        CreateBullet();
        MakeConsume();
    }

    public virtual void Attack2() {
        CreateBullet(true);
        MakeConsume();
    }

    private void Update() {
        if (!float.IsNaN(_holdTime) && _holdTime < maxTime) {
            HoldUpdate();
        }

        if (_holdTime >= maxTime && _autoAttack && !_triggerHoldAtk) {
            HoldAtk();
        }
    }

    private void HoldUpdate() {
        _holdTime += Time.deltaTime * _speedFactor * holdSpeed;
        if (ShouldShowReloadGo) {
            ShowReloadGo();
            EnLargeStart();
        }

        if (IsHolding) {
            WeaponSizeUpdate();
        }
    }

    private void ShowReloadGo() {
        _reloadGo = PrefabPool.Inst.Take(reloadPrefab);
        _reloadGo.transform.SetParent(controller.transform, false);
        _reloadGo.GetComponent<ReloadClip>().PlayReloadAnim((maxTime - _holdTime) / holdSpeed / _speedFactor);
        RGMusicManager.GetInstance().PlayEffect(holdClip);
    }

    private void EnLargeStart() {
        _weaponSpriteRenderer.enabled = false;
        var wPos = transform.position + _initLocalPos;
        _bulletGo = Instantiate(bulletsInfo[1].bulletProto, wPos, _initLocalRot, _weaponTransform);
        var b = _bulletCache.GetCache(_bulletGo);
        b.UpdateInfo(
            bulletsInfo[1].GetBulletInfo().SetSourceObject(this.controller.gameObject).SetSourceWeapon(this).SetSpeed(bulletsInfo[1].speed).SetCamp(controller.camp),
            bulletsInfo[1].GetDamageInfo().SetDamage(bulletsInfo[1].damage).SetCritic(bulletsInfo[1].critic + this.controller.attribute.critical).SetCamp(this.controller.camp)
        );
        b.rigid2d.bodyType = RigidbodyType2D.Kinematic;
        b.manual_destroy = true;
        b.transform.GetChild(0).GetComponent<Collider2D>().enabled = false;
        _bulletGo.transform.localScale = new Vector3(1, 1, 1);
        _bulletGo.transform.localEulerAngles = new Vector3(0, 0, 0);
        BulletCreateEvent(_bulletGo);
        AfterBulletCreateEvent();
        _bulletGo = latestCreatedBulletObject;
    }

    private void WeaponSizeUpdate() {
        var progress = GetProgress();
        _bulletGo.transform.localScale = Vector3.one * Mathf.Lerp(bulletsInfo[1].size, bulletsInfo[2].size, progress);
    }

    public override void AttackKeyDown(bool manual) {
        base.AttackStart();
        anim.ResetTrigger("hold_atk");
    }

    public override void AttackStop() {
        if (!_autoAttack) {
            if (float.IsNaN(_holdTime) || _holdTime < maxTime * .2f) {
                NormalAtk();
            } else if (atk_b) {
                HoldAtk();
            }
        }

        base.AttackStop();
    }

    private float GetProgress() {
        float tempHoldTime = float.IsNaN(_holdTime) ? 0 : _holdTime;
        return Mathf.Clamp01((tempHoldTime - .2f) / (maxTime - .2f));
    }

    private void NormalAtk() {
        anim.SetTrigger(Atk);
        _holdTime = float.NaN;
    }

    private bool _triggerHoldAtk;

    private void HoldAtk() {
        anim.SetTrigger(HoldAtk1);
        _triggerHoldAtk = true;
    }

    public void HoldStart() {
        _holdTime = 0f;
    }

    public void HoldAtkStart() {
        if (_reloadGo == null || _bulletGo == null) return;
        _triggerHoldAtk = false;
        var progress = GetProgress();
        var b = _bulletCache.GetCache(_bulletGo);
        var baseDamage = b.bulletInfo.damage;
        var baseCriticRate = b.damageInfo.critic;
        var dmgTrigger = b.GetComponentInChildren<DamageTrigger>();
        if (dmgTrigger != null) {
            baseDamage = dmgTrigger.Damage;
            baseCriticRate = dmgTrigger.critical;
        }
        var a = (int)Mathf.Lerp(0, bulletsInfo[2].damage - bulletsInfo[1].damage, progress) + baseDamage;
        var c = (int)Mathf.Lerp(0, bulletsInfo[2].critic - bulletsInfo[1].critic, progress) + baseCriticRate;
        var effectDeltaSize = b.bulletInfo.effectDeltaSize;
        var speed = Mathf.Lerp(bulletsInfo[1].speed, bulletsInfo[2].speed, progress);
        var finalDeviation = GameUtil.GetFinalDeviation(this);
        var angle = controller.facing < 0 ? 180 - GetThrowAngle() + finalDeviation : GetThrowAngle() + finalDeviation;
        var tc = progress > .99f ? bulletsInfo[2].throughCount : bulletsInfo[1].throughCount;
        b.transform.GetChild(0).GetComponent<Collider2D>().enabled = false;
        b.UpdateInfo(
            bulletsInfo[1].GetBulletInfo().SetSourceObject(this.controller.gameObject).SetSourceWeapon(this).SetSpeed(speed).SetCamp(controller.camp),
            bulletsInfo[1].GetDamageInfo().SetDamage(a).SetCritic(c + this.controller.attribute.critical).SetCamp(this.controller.camp).SetThroughCount(tc)
        );
        b.GetComponentInChildren<DamageTrigger>().Enlarge(0, 0, 0, effectDeltaSize, nameof(GunHugeAxe));
        b.transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;
        b.rotate_angle = Mathf.Abs(b.rotate_angle) * -controller.facing;
        b.transform.rotation = Quaternion.Euler(0, 0, angle);
        if (controller.facing == -1) {
            var localScale = b.transform.localScale;
            localScale.y = -localScale.y;
            b.transform.localScale = localScale;
        }

        b.SetAwakeTrue();
        b.AutoUpdateDirection();
        onDelayedBulletRelease?.Invoke(_bulletGo);

        MakeConsume();
        AfterAttackEvent();
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        _holdTime = float.NaN;
        _bulletGo = null;
        Destroy(_reloadGo);
    }

    private float GetThrowAngle() {
        if (!controller.target_obj) {
            return controller.FixedAngle;
        }

        Transform tempTarget = controller.target_obj;
        Vector2 aimDir = ((Vector2)(tempTarget.position - transform.position)).normalized;
        int facing = aimDir.x > 0 ? 1 : aimDir.x < 0 ? -1 : 1;
        return Vector2.Angle(aimDir, new Vector2(facing, 0)) * Mathf.Sign(aimDir.y);
    }

    public void HoldAtkFinish() {
        if (_weaponSpriteRenderer) {
            _weaponSpriteRenderer.enabled = true;
        }
    }

    void CreateBullet(bool reverse = false) {
        SimpleEventManager.Raise(ChargeAttackStartEvent.UseCache(controller, this, Mathf.Clamp01(_holdTime / maxTime)));
        var tempObj = BulletFactory.TakeBullet(
            GetBulletInfo().SetPosition(transform.parent.position),
            GetDamageInfo()
        );
        tempObj.transform.localScale = new Vector3(controller.facing, reverse ? -1 : 1, 1);
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        BulletCreateEvent(tempObj);
        AfterBulletCreateEvent();
        AfterAttackEvent();
        onDelayedBulletRelease?.Invoke(latestCreatedBulletObject);
        SimpleEventManager.Raise(ChargeAttackEndEvent.UseCache(controller, this));
    }

    public override void StopWeapon() {
        base.StopWeapon();
        if (_reloadGo != null) {
            anim.ResetTrigger("hold_atk");
            HoldAtkStart();
        }

        HoldAtkFinish();
    }

    public override void SetWeaponBack(Transform parent) {
        base.SetWeaponBack(parent);
        BackState = true;
    }

    public override void SetWeaponFront(Transform parent, int targetLayer) {
        base.SetWeaponFront(parent, targetLayer);
        BackState = false;
    }

    public override string[] texts {
        get {
            _texts[0] = damageFinal + "~" + bulletsInfo[2].damage;
            _texts[1] = consume.ToString();
            _texts[2] = criticalFinal + "~" + bulletsInfo[2].critic;
            _texts[3] = clampedRuntimeDeviation.ToString();
            return _texts;
        }
    }
}