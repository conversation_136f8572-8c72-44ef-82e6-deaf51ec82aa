using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class GunInitMiner : RG<PERSON><PERSON>pon, WeaponInterface, ChargeWeaponInterface {
    public override bool HandCutWhenTargetNear { get => false; }
    protected override emWeaponType default_type => emWeaponType.Axe;
    private readonly float _maxHoldTime = 0.5f;
    
    public float force;
    private float _atkHoldTime;
    private float _finalMaxHoldTime = 0.5f;
    private bool _isHolding;
    public GameObject holdGo;
    private GameObject _holdGo;
    public List<AudioClip> audioClips;
    public bool upgrade;
    private void Update() {
        if (!controller) {
            return;
        }

        if (_isHolding) {
            _atkHoldTime += Time.deltaTime;
        }
    }

    public void AutoAttackItem() {
        if (_autoAttack) {
            anim.SetTrigger("holdAtk");
            _isHolding = false;
            _atkHoldTime = 0;
            if (_holdGo) {
                Destroy(_holdGo);
            }

            _holdGo = null;
        }
    }

    public override void AttackKeyDown(bool manual) {
        _isHolding = true;
        anim.ResetTrigger("normalAtk");
        anim.ResetTrigger("holdAtk");
        if (!_holdGo) {
            _holdGo = Instantiate(holdGo, transform.position, Quaternion.identity) as GameObject;
            _holdGo.transform.SetParent(controller.transform, false);
            _holdGo.transform.localPosition = Vector3.zero;
            var reloadClip = _holdGo.GetComponent<ReloadClip>();
            
            var maxTimeFactor = 1f;
            maxTimeFactor *= DataUtil.GetChargeTimeValue();

            _finalMaxHoldTime = _maxHoldTime * maxTimeFactor;
            var holdTime = _finalMaxHoldTime / _speedFactor;
            reloadClip.PlayReloadAnim(holdTime);
        }

        base.AttackKeyDown(manual);
    }

    public override void AttackKeyUp(bool manual) {
        if (!atk_b) return;
        Attack();
        base.AttackKeyUp(manual);
    }

    public void Attack() {
        var maxTimeFactor = 1f;
        maxTimeFactor *= DataUtil.GetChargeTimeValue();

        _finalMaxHoldTime = _maxHoldTime * maxTimeFactor;
        var holdTime = _finalMaxHoldTime / _speedFactor;
        if (_atkHoldTime >= holdTime) {
            anim.SetTrigger("holdAtk");
        } else {
            anim.SetTrigger("normalAtk");
        }

        _isHolding = false;
        _atkHoldTime = 0;
        if (_holdGo) {
            Destroy(_holdGo);
        }

        _holdGo = null;
        atk_b = false;
    }

    public void AttackNormal() {
        var tempObj = BulletFactory.TakeBullet(
            GetBulletInfo().SetBullet(bullet).SetPosition(transform.parent.position).SetBulletSize(bulletInfo.size),
            DecorateAdditionLevel(GetDamageInfo().SetBullet(bullet).SetDamage(bulletInfo.damage)
                .SetCritic(base.critical + controller.attribute.critical))
        );
        tempObj.GetComponent<Animator>().speed = weapon_speed * _speedFactor;
        tempObj.transform.localScale = new Vector3(controller.facing, 1, 1);
        controller.GetForce(transform.right, 0, false);
        BulletCreateEvent(tempObj);
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        AfterBulletCreateEvent();
        AfterAttackEvent();
    }

    public void AttackHold() {
        var temp_obj = BulletFactory.TakeBullet(
            GetBulletInfo().SetBullet(bullet).SetPosition(transform.parent.position)
                .SetBulletSize(bulletInfo.size * 1.5f),
            DecorateAdditionLevel(GetDamageInfo().SetBullet(bullet).SetDamage(bulletInfo.damage * 2)
                .SetCritic(base.critical + controller.attribute.critical))
        );
        temp_obj.GetComponent<Animator>().speed = weapon_speed * _speedFactor;
        temp_obj.transform.localScale = new Vector3(controller.facing, 1, 1);
        controller.GetForce(transform.right, force, false);
        if (upgrade) {
            Invoke(nameof(CreateBoom), 0.3f);
        }

        BulletCreateEvent(temp_obj);
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        AfterBulletCreateEvent();
        AfterAttackEvent();
    }

    public void CreateBoom() {
        var temp_obj = BulletFactory.TakeBullet(
            GetBulletInfo(1).SetPosition(transform.parent.position).SetBulletSize(bulletInfo2.size).SetAngle(0),
            DecorateAdditionLevel(GetDamageInfo().SetBullet(bullet).SetDamage(bulletInfo2.damage)
                .SetCritic(base.critical + controller.attribute.critical))
        );
        temp_obj.GetComponent<Animator>().speed = weapon_speed * _speedFactor;
        GameUtil.CameraShake(2);
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        AfterBulletCreateEvent();
        AfterAttackEvent();
    }

    public float GetMaxChargeTime() {
        return _finalMaxHoldTime;
    }

	public WeaponChargeState GetWeaponChargeState() {
        var chargeAmount = GetChargeAmount();
        if (chargeAmount <= 0) {
            return WeaponChargeState.Normal;
        } else if (chargeAmount >= 1) {
            return WeaponChargeState.FullCharged;
        }
        return WeaponChargeState.Charging;
    }

	public float GetChargeAmount() {
        return _atkHoldTime / _finalMaxHoldTime;
    }

    public void SetChargeAmount(float amount) {
        if (_holdGo != null) {
            _holdGo.GetComponent<ReloadClip>().SetReloadProgress(amount, false);
        }
        _atkHoldTime = amount * GetMaxChargeTime();
    }
}
