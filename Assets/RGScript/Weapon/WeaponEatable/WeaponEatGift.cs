using Activities.FusionWeapon.Scripts;
using RGScript.BattleFactor.LevelBattleFactor;
using RGScript.Data;
using RGScript.Weapon;
using Sirenix.OdinInspector;
using System;
using System.Linq;
using System.Collections.Generic;
using UnityEngine;

[RequireComponent(typeof(RGNetBehaviour))]
public class WeaponEatGift : SerializedRandomBahaviour, IWeaponEatEffect {
    public List<WeaponWeight> weaponWeights;
    [Range(0, 100)]
    public int getSelfRate = 1;
    public int maxGetSelfTime = 9;
    public string finalSelfWeaponName;
    private bool _isGetSelf;
    private int _getSelfCount;
    private RGWeapon _weapon;

    private void Start() {
        _weapon = transform.parent.GetComponent<RGWeapon>();
    }

    public void MakeEffect(RGController controller) {
        string weaponName = GetWeaponName();
        if (weaponName == "new_year") {
            weaponName = GetForNewYear();
        }
        
        Instantiate(PrefabManager.GetPrefab(PrefabName.fusion_effect), controller.transform.position, Quaternion.identity);
        var rgWeapon = WeaponFactory.CreateWeapon(weaponName, this._weapon.weaponSource);
        rgWeapon.name = weaponName;
        if (weaponName == _weapon.name) {
            var weaponEatGift = rgWeapon.GetComponentInChildren<WeaponEatGift>();
            weaponEatGift._isGetSelf = true;
            weaponEatGift._getSelfCount = _getSelfCount + 1;
            CheckAchieve();
        }

        if (BattleData.data.IsDefenceMode) {
            rgWeapon.level = _weapon.level;
        }
        
        controller.hand.PickUpItem(rgWeapon.transform);
    }

    //成就
    private void CheckAchieve() {
        //新年礼物 连续4次开出自己
        if (_getSelfCount+1 == 4 && _weapon.name == "weapon_293") {
            AchieveInfos.info.CheckUnlock(AchieveInfos.AchievementType.HappyNewYear, 0, "", emHero.None);
        }
    }
    
    private string GetForNewYear() {
        string[] newYearWeapons = {"weapon_900","weapon_901","weapon_902"};
        int index = rg_random.Range(0,newYearWeapons.Length);
        if (BattleData.data.HasActivityEnabled(ActivityFusionManager.TAG)) {
            var fusionSourceWeapons = ActivityFusionManager.GetFusionSourceWeapons();
            var randomIndex = rg_random.Range(0, fusionSourceWeapons.Count);
            return fusionSourceWeapons[randomIndex];
        }
        
        return  newYearWeapons[index];
    }
    
    private string GetWeaponName() {
        if (BattleData.data.HasActivityEnabled(ActivityFusionManager.TAG)) {
            var fusionSourceWeapons = ActivityFusionManager.GetFusionSourceWeapons();
            var randomIndex = rg_random.Range(0, fusionSourceWeapons.Count);
            return fusionSourceWeapons[randomIndex];
        }
        
        if (_isGetSelf) {
            return _getSelfCount <= maxGetSelfTime ? _weapon.name : finalSelfWeaponName;
        }
        if (rg_random.Range(0, 100) < getSelfRate) {
            return _weapon.name;
        }
        
        var weightOverall = weaponWeights.Select(w => w.weight).Aggregate(0, (all, temp) => all + temp);
        var tempResult = rg_random.Range(0, weightOverall);
        var tempWeight = 0;
        List<int> levels = null;
        for (int i = 0; i < weaponWeights.Count; i++) {
            tempWeight += weaponWeights[i].weight;
            if (tempResult < tempWeight) {
                levels = weaponWeights[i].levels;
                break;
            }
        }

        if (levels == null) {
            throw new ApplicationException("无法生成武器");
        }

        var weapons = levels.Select(level => WeaponDropInfo.Instance.Data[level]).SelectMany(w => w).ToList();
        if (BattleData.data.CompareFactor(emBattleFactor.MelleOnly)) {
            weapons.RemoveAll(w => !RGWeapon.IsMelle(w.Name));
        }
        var index = rg_random.Range(0, weapons.Count);
        var weaponName = weapons[index].Name;
        return weaponName;
    }

#if UNITY_EDITOR
    [Button]
    private void TestGetSelf() {
        _isGetSelf = true;
        LogUtil.Log("yns isGetSelf");
    }
#endif
    
    [Serializable]
    public struct WeaponWeight {
        public List<int> levels;
        public int weight;
    }
}
