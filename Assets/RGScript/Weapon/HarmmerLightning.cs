using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[RequireComponent(typeof(RGWeapon))]
public class HarmmerLightning : MonoBehaviour
{
    public float triggerInterval;
    public float lifeTime;
    public float triggerDistance;
    public int triggerCount;
    public emBuff extraCountBuff;
    public int extraCount;
    private RGWeapon weapon;
    public bool useAfterBulletCreateCallBack = true;
    private void Start()
    {
        weapon = GetComponent<RGWeapon>();
        if (useAfterBulletCreateCallBack) {
            weapon.afterBulletCreate += AfterBulletCreate;
        }
    }

    public void AfterBulletCreate() {
        var position = weapon.gun_point.transform.position;
        var controller = weapon.controller;
        if (controller == null) {
            return;
        }
        if (controller.attribute.energy >= weapon.consume) {
            var arcGO = Instantiate(weapon.bulletsInfo[1].bulletProto, position, Quaternion.identity, RGGameSceneManager.Inst.temp_objects_parent);
            var arc = arcGO.GetComponent<HarmmerLightningArc>();
            arc.UpdateInfo(weapon.GetBulletInfo(1).SetSourceObject(weapon.controller.gameObject).SetSourceWeapon(weapon), weapon.GetDamageInfo(1));
            var count = controller.thisBattleData.HasBuff(extraCountBuff) ? extraCount + triggerCount : triggerCount;
            arc.Init(lifeTime, triggerInterval, count, triggerDistance);
            weapon.BulletCreateEvent(arcGO);
        } else {
            weapon.EnergyWarn();
        }
    }

#if UNITY_EDITOR
    private void OnDrawGizmos() {
        Gizmos.color = Color.blue;
        Gizmos.DrawWireSphere(transform.position, triggerDistance);
    }
#endif
}
