using AudioPlayer;
using DG.Tweening;
using System.Collections;
using UnityEngine;

//尚方宝剑 weapon_161
public class GunShangFang : Gun006 {
    [Header("击杀几个敌人之后之后可以变为宝剑模式")] public int evoNum = 5;
    [Header("宝剑模式的攻击次数")] public int swordModeNum = 10;
    private int _evoCounter;
    private int _swordModeCounter;
    private bool _isNormalMode;
    private SpriteRenderer _evoIcon;
    private ParticleSystem _swordModeParticle;
    private DOTweenAnimation[] _doTweenAnimations;

    private GameObject _lastCreateBullet;
    private bool _canAddSwordModeCounter;
    private bool isNormalMode {
        get => _isNormalMode;
        set {
            if (_isNormalMode != value) {
                _isNormalMode = value;
                SwitchMode();
            }
        }
    }
    
    public override string[] texts {
        get {
            var weaponCustomText = GetComponent<IWeaponCustomText>();
            if (weaponCustomText != null) {
                _texts[0] = weaponCustomText.damage;
                _texts[1] = weaponCustomText.consume;
                _texts[2] = weaponCustomText.critical;
                _texts[3] = weaponCustomText.deviation;
            } else {
                _texts[0] = "" + (isNormalMode ? damageFinal : damageFinal2);
                _texts[1] = "" + (consume == -1 ? "?" : consume.ToString());
                _texts[2] = "" + (isNormalMode ? criticalFinal : criticalFinal2);
                _texts[3] = "" + clampedRuntimeDeviation;
            }

            return _texts;
        }
    }
    
    protected override void Awake() {
        base.Awake();
        _evoIcon = transform.Find("w/evo_icon").GetComponent<SpriteRenderer>();
        _swordModeParticle = GetComponentInChildren<ParticleSystem>();
        _doTweenAnimations = new DOTweenAnimation[2];
        _doTweenAnimations[0] = CachedWeaponSpr[0].GetComponent<DOTweenAnimation>();
        _doTweenAnimations[1] = _evoIcon.GetComponent<DOTweenAnimation>();
    }
    
    protected override void OnEnable() {
        base.OnEnable();
        isNormalMode = true;
    }

    private void SwitchMode() {
        CachedWeaponSpr[0].enabled = isNormalMode;  
        _evoIcon.enabled = !isNormalMode;

        _swordModeCounter = 0;
        _evoCounter = 0;
        CachedWeaponSpr[0].color = Color.white;
        _evoIcon.color = Color.white;
        _doTweenAnimations[0].DOPause();
        _doTweenAnimations[1].DOPause();
        if (controller != null && !isNormalMode) {
            _swordModeParticle.Play();
            _swordModeParticle.GetComponent<AnimAudioPlayer>().PlayAudio();
            EmoticonTips.ShowEmoticonTips(controller, 33, GameUtil.GenerateRandomSeedNumber(), 1.8f);
        }
    }

    public override void CreateBullet(bool reverse = false) {
        if (!IsEvolvedWeapon) {
            base.CreateBullet(reverse);
            return; 
        }

        ActionInfo actionInfo = new ActionInfo();
        actionInfo.onHit += OnBulletHit;
        _canAddSwordModeCounter = true;
        var index = isNormalMode ? 0 : 1;
        _lastCreateBullet = BulletFactory.TakeBullet(
            GetBulletInfo(index).SetPosition(transform.parent.position).SetSourceWeapon(this).SetSourceObject(controller != null ? controller.gameObject : null),
            GetDamageInfo(index), actionInfo
        );
        _lastCreateBullet.transform.localScale = new Vector3(controller.facing, reverse ? -1 : 1, 1);
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        BulletCreateEvent(_lastCreateBullet);
        AfterBulletCreateEvent();
        AfterAttackEvent();
    }
    
    public void OnBulletHit(DamageCarrier attacker, GameObject target, BulletHitData hitData) {
        if (!isNormalMode && _canAddSwordModeCounter && attacker.gameObject == _lastCreateBullet && target != null) {
            _canAddSwordModeCounter = false;
            _swordModeCounter++;
            if (_swordModeCounter == swordModeNum - 1) {
                _doTweenAnimations[1].DOPlay();
            }
            else if (_swordModeCounter >= swordModeNum) {
                isNormalMode = true;
            }
        } else if (target.TryGetComponent<RGEController>(out var enemy)) {
            if(!enemy.IsCopy && enemy.attribute.hp <= 0) {
                _evoCounter++;
                if (_evoCounter == evoNum - 1) {
                    _doTweenAnimations[0].DOPlay();
                }
                else if (_evoCounter >= evoNum) {
                    isNormalMode = false;
                }
            }
        }
    }
}
