public class WModBulletSizeLaser : WModBulletSize {

    [Sirenix.OdinInspector.Title("激光天赋的子弹大小变化量")]
    public float buffScale = 2;

    private float FinalScale {
        get {
            return GameUtil.GetBattleData(weapon).ProcessAoeRange(0, new AoeRangeInfo {
                RangeType = AoeRangeType.Laser,
                CalculationType = CalculationType.Add,
                Value = buffScale
            });
        }
    }

    private float _modifyValue;

    /// <summary>
    /// 天赋带来的修正值
    /// </summary>
    public override void BuffModify() {
        var scale = FinalScale;
        WeaponScaleModify(-_modifyValue, emDamageType.Laser | emDamageType.Energy);
        WeaponScaleModify(scale, emDamageType.Laser | emDamageType.Energy);
        _modifyValue = scale;
    }
    
    public override void LevelModify(int current_level, int target_level) {
        if (buffScale == 0) return;
        var value = 0.2f;
        var level = 5;
        var current_value = value * (current_level / level);
        var target_value = value * (target_level / level);
        var delta = target_value - current_value;
        WeaponScaleModify(delta, emDamageType.Laser | emDamageType.Energy);
    }

    public override void WeaponScaleModify(float value, emDamageType filter) {
        weapon.ChangeBulletSize(value, emChangeValueType.Add, filter);
    }
}
