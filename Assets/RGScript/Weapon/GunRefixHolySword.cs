using System.Collections;
using UnityEngine;

/// <summary>
/// 重铸的圣剑
/// </summary>
public class GunRefixHolySword : RG<PERSON><PERSON>pon, WeaponInterface {
    int index { get => anim.GetInteger("index"); set => anim.SetInteger("index", value); }
    bool back_state {
        set {
            anim.SetBool("back", value);
        }
    }

    public override void SetAttack(bool value1, bool manual = true) {
        if (controller) {
            if (activate && anim) {
                if (value1 && !anim.GetBool("atk_b")) {
                    AttackKeyDown(manual);
                } else if (!value1 && anim.GetBool("atk_b")) {
                    AttackKeyUp(manual);
                }
            }
        }
    }

    public void Attack() {
        bool isHolyAttack = index == 2;
        if (isHolyAttack) {
            HolyAttack();
        } else {
            SwordAttack();
        }
        if (controller.attribute.energy >= consume) {
            MakeConsume();
        }
        
        index = (index + 1) % 3;
        anim.SetInteger("index", index);
        
        AfterBulletCreateEvent();
        AfterAttackEvent();
    }

    void SwordAttack() {
        var position = transform.parent.position;
        var temp_obj = BulletFactory.TakeBullet(
            GetBulletInfo(0).SetPosition(position),
            GetDamageInfo(0)
            );
        temp_obj.transform.localScale = new Vector3(controller.facing, index == 1 ? -1 : 1, 1);
        RGMusicManager.GetInstance().PlayEffect(3);
        BulletCreateEvent(temp_obj);
    }

    void HolyAttack() {
        var position =  controller.target_obj ? controller.target_obj.position : transform.parent.position;
        var temp_obj = BulletFactory.TakeBullet(
            GetBulletInfo(1).SetPosition(position).SetAngle(controller.facing < 0 ? 180 - controller.FixedAngle : controller.FixedAngle)/*.SetAngle(0)*/,
            GetDamageInfo(1)
            );
        // if (!controller.target_obj) {
        //     temp_obj.transform.position += temp_obj.transform.right * 3;
        // }
        RGMusicManager.GetInstance().PlayEffect(3);
        BulletCreateEvent(temp_obj);
    }


    public override void MakeConsume() {
        controller.attribute.RestoreEnergy(-consume);
    }

    public override void SetWeaponBack(Transform parent) {
        base.SetWeaponBack(parent);
        anim.SetInteger("index", 0);
        back_state = true;
    }

    public override void SetWeaponFront(Transform parent, int target_layer) {
        base.SetWeaponFront(parent, target_layer);
        anim.SetInteger("index", 0);
        back_state = false;
    }
    public override void StopWeapon() {
        base.StopWeapon();
        anim.SetInteger("index", 0);
    }
}