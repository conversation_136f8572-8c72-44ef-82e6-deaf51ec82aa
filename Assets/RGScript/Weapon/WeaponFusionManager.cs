using System.Collections.Generic;

namespace RGScript.Weapon {
    public static class WeaponFusionManager {
        private static List<FusionWeaponPath> _fusionWeaponPaths = new();
        private static readonly List<string> FusionWeaponChildren = new();
        private static readonly List<string> FusionWeaponParent = new();

        private static bool _isInit;
        public static void InitFusionConfig() {
            if (_isInit) {
                return;
            }
            var config = ResourcesUtil.Load<FusionWeaponPathConfig>("Resources/FusionWeaponPathConfig.asset");
            if (!config) {
                return;
            }

            _fusionWeaponPaths = config.Paths;
            foreach (var path in _fusionWeaponPaths) {
                foreach (var weapon in path.SourceWeapons) {
                    if (!FusionWeaponChildren.Contains(weapon)) {
                        FusionWeaponChildren.Add(weapon);
                    }
                }

                if (!FusionWeaponParent.Contains(path.TargetWeapon)) {
                    FusionWeaponParent.Add(path.TargetWeapon);
                }
            }
            _isInit = true;
        }

        public static bool HasCompareWeaponInList(List<string> weapons, out List<string> compareWeapons) {
            if (weapons.Count < 1) {
                compareWeapons = new List<string>();
                return false;
            }

            foreach (var weapon in weapons) {
                if (HasCompareWeapon(weapon)) {
                    compareWeapons = GetFusionCompareSourceWeapons(weapon);
                    return true;
                }
            }

            compareWeapons = new List<string>();
            return false;
        }

        public static bool HasCompareWeapon(string weaponName) {
            if (string.IsNullOrWhiteSpace(weaponName)) {
                return false;
            }

            var compareSourceWeapons = new List<string>();
            foreach (var path in _fusionWeaponPaths) {
                if (!path.SourceWeapons.Contains(weaponName)) {
                    continue;
                }

                var sameNameCount = 0;
                foreach (var weapon in path.SourceWeapons) {
                    if (compareSourceWeapons.Contains(weapon)) {
                        continue;
                    }

                    if (weapon.Equals(weaponName) && sameNameCount < 1) {
                        sameNameCount++;
                        continue;
                    }

                    if (!weapon.Equals(weaponName)) {
                        compareSourceWeapons.Add(weapon);
                    } else if (weapon.Equals(weaponName) && sameNameCount > 0) {
                        compareSourceWeapons.Add(weapon);
                    }
                }
            }

            return compareSourceWeapons.Count >= 1;
        }

        public static List<string> GetFusionCompareSourceWeapons(string weaponName) {
            var compareSourceWeapons = new List<string>();
            foreach (var path in _fusionWeaponPaths) {
                if (!path.SourceWeapons.Contains(weaponName)) {
                    continue;
                }

                var sameNameCount = 0;
                foreach (var weapon in path.SourceWeapons) {
                    if (compareSourceWeapons.Contains(weapon)) {
                        continue;
                    }

                    if (weapon.Equals(weaponName) && sameNameCount < 1) {
                        sameNameCount++;
                        continue;
                    }

                    if (!weapon.Equals(weaponName)) {
                        compareSourceWeapons.Add(weapon);
                    } else if (weapon.Equals(weaponName) && sameNameCount > 0) {
                        compareSourceWeapons.Add(weapon);
                    }
                }
            }

            if (compareSourceWeapons.Count < 1) {
                compareSourceWeapons = GetFusionSourceWeapons();
            }

            return compareSourceWeapons;
        }

        public static List<string> GetFusionSourceWeapons() {
            var sourceWeapons = new List<string>();
            foreach (var path in _fusionWeaponPaths) {
                foreach (var sourceWeapon in path.SourceWeapons) {
                    if (!sourceWeapons.Contains(sourceWeapon)) {
                        sourceWeapons.Add(sourceWeapon);
                    }
                }
            }

            return sourceWeapons;
        }
    }
}