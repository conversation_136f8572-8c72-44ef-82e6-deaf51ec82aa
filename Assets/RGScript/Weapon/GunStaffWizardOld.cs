using Sirenix.OdinInspector;
using UnityEngine;

///大巫师的旧法杖
public class GunStaffWizardOld : GunStaffWizard {
    [Header("攻击几次后产生光球")] public int createImmuneTimes = 3;
    private int _currentAttackTimes;
    [Header("光球的最大数量")] public int maxBalls = 10;
    [Header("光球的持续时间")] public int ballImmuneDuration = 30;

    private string ImmuneName = "GunStaffWizardOldImmune";
    public GameObject ImmunePrefab;
    public override void Attack() {
        base.Attack();
        if (IsEvolvedWeapon) {
            _currentAttackTimes++;
            if (_currentAttackTimes >= createImmuneTimes) {
                _currentAttackTimes = 0;
                CreateImmune();
            }
        }
    }

    private void CreateImmune() {
        if (controller == null || controller.hand == null || controller.hand.front_weapon != this) {
            return;
        }

        var immune = RGBuff.AddBuff(ImmunePrefab, controller.gameObject, controller.transform, true) as StaffWizardOldImmune;
        if (immune == null)
            return;
        if (immune.immuneFx.fxCount >= maxBalls)
            return;
        immune.camp = GetDamageInfo().camp;
        immune.immuneFx.fxCount += 1;
    }
}