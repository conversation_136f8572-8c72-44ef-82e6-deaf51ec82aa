using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class GunInitRobotChild : Gun017Child {
    public bool hasAtkAnim;
    private Animator animator;
    private static int AtkHash = Animator.StringToHash("atk_b");

    void Awake() {
        rigibody2d = transform.GetComponent<Rigidbody2D>();
        gun_point = transform.GetChild(0).GetChild(0);
        sprite_renderer = transform.GetChild(0).GetComponent<SpriteRenderer>();
        parent_point = transform.parent;
        if (hasAtkAnim) {
            onAttack += DoAttack;
            animator = transform.GetComponent<Animator>();
        }
    }

    private void DoAttack() {
        animator.SetTrigger(AtkHash);    
    }

    public override void FixedRotation() {
        Vector2 tempv2 = new Vector2(0, 0);
        if (has_target) {
            tempv2 = (Vector2)target.position - (Vector2)transform.position;
        } else {
            if (parent_point) {
                target_point = parent_point.position;
                tempv2 = (Vector2)target_point - (Vector2)transform.position;
            } else {
                GameObject.Destroy(gameObject);
            }
        }

        float fixed_angle = Vector2.Angle((tempv2), new Vector2(1, 0));

        if (tempv2.y < 0)
            fixed_angle = -fixed_angle;
        sprite_renderer.flipY = fixed_angle < -90 || fixed_angle > 90;

        transform.localEulerAngles = new Vector3(0, 0, fixed_angle);
    }
}