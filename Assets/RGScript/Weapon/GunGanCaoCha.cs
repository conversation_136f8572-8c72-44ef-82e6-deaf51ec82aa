using UnityEngine;

namespace RGScript.Weapon {
    public class GunGanCaoCha : Gun015 {
        private const string ChaKey = "gun_gan_cao_cha";
        public GameObject chaPrefab;

        public override void OnWeaponProcessEvolutionEvent(WeaponProcessEvolutionEvent e) {
            if (e.Weapon == this && e.isEvolved) {
                SimpleEventManager.AddEventListener<BulletKillWitHurtInfoEvent>(CheckWeaponIsHit);
                SimpleEventManager.AddEventListener<WeaponCreateGameObjectEvent>(OnWeaponCreateGameObject);
            }
        }

        protected override void OnDisable() {
            base.OnDisable();
            SimpleEventManager.RemoveEventListener<BulletKillWitHurtInfoEvent>(CheckWeaponIsHit);
            SimpleEventManager.RemoveEventListener<WeaponCreateGameObjectEvent>(OnWeaponCreateGameObject);
        }

        public override void DropWeapon(int targetLayer) {
            base.DropWeapon(targetLayer);
            if (IsEvolvedWeapon) {
                SimpleEventManager.RemoveEventListener<BulletKillWitHurtInfoEvent>(CheckWeaponIsHit);
                SimpleEventManager.RemoveEventListener<WeaponCreateGameObjectEvent>(OnWeaponCreateGameObject);
            }
        }

        private void OnWeaponCreateGameObject(WeaponCreateGameObjectEvent e) {
            if (e.key != ChaKey) return;
            SpawnCha(e.pos);
        }

        private void SpawnCha(Vector2 worldPos) {
            var cha = Instantiate(chaPrefab, worldPos, Quaternion.identity)
                .GetComponent<Cha>();
            cha.weaponLevel = level;
        }

        private void CheckWeaponIsHit(BulletKillWitHurtInfoEvent e) {
            if (e.hurtInfo.SourceWeapon == null || e.hurtInfo.SourceWeapon != this) {
                return;
            }

            if (NetControllerManager.Inst.isServer) {
                MessageManager.SendWeaponCreateObjectPosMessage(
                    ChaKey, e.enmey.transform.position);
            } else if (!GameUtil.IsMultiGame()) {
                SpawnCha(e.enmey.transform.position);
            }
        }
    }
}