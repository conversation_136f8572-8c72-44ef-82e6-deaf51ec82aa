using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[RequireComponent(typeof(RGWeapon))]
public class AttackTalk : MonoBehaviour {
    public string talkKey;
    private RGWeapon weapon;

    private void Awake() {
        weapon = GetComponent<RGWeapon>();
    }

    private void OnEnable() {
        weapon.afterAttack += Talk;
    }

    private void OnDisable() {
        weapon.afterAttack -= Talk;
    }

    private void Talk() {
        string talk = I2.Loc.ScriptLocalization.Get(talkKey);
        UICanvas.Inst.ShowTextTalk(weapon.controller.transform, talk, 2.5f, 0.5f);
    }
}
