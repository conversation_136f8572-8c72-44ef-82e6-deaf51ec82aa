using RGScript.Net.StateSync.Core;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

//激光鱼 weapon_094
public class GunLaserFish : Gun003 {
    public GameObject laserFishItem; //= "RGPrefab/Weapon/weapon_laser_fish_item.prefab";
    public GameObject funnelLaserFish;// = "RGPrefab/Pet/funnel_laser_fish.prefab";
    Transform _funnelRoot;
    private Vector3 _funnelRootInitPos;
    Dictionary<FunnelWeaponContainer, float> _funnels = new();
    private List<FunnelWeaponContainer> _cacheFunnels;
    [Header("最多飞鱼数量")]
    public int maxFunnelCount = 4;

    private int _currentMaxFunnelCount;
    [Header("飞鱼攻击间隔")]
    public float funnelAttackInterval = 1f;
    [Header("飞鱼攻击伤害")]
    public int funnelDamage = 6;
    [Header("飞鱼持续时间")]
    public float funnelLifeTime = 30;
    [Header("创建飞鱼所需要的攻击次数")]
    public int createFunnelHitCount = 12;
    
    public float maxLaserDamageFactor = 1.5f;
    private int _currentHitCount;
    private float _hitTime = 4;  //维持攻击次数的cd
    private float _stopHitTime;
    private float _updateTimer = 0f;        
    private const float UpdateInterval = 1f;
    private bool _isAtttacking;
    private GameObject _createFunnelAnim;
    private static readonly int CreateFunnelAnim = Animator.StringToHash("create");
    
    protected override void Awake() {
        base.Awake();
        _funnelRoot = transform.Find("funnel_root");
        _createFunnelAnim = transform.Find("w/create_funnel_anim").gameObject;
        _funnelRootInitPos = _funnelRoot.localPosition;
        _funnelRoot.localPosition = _funnelRootInitPos;
        StartCoroutine(FunnelRotating());
    }
    
    protected override void OnEnable() {
        base.OnEnable();
        SimpleEventManager.AddEventListener<PlayerBulletHitEnemyEvent>(OnPlayerBulletHitEnemy);
    }

    protected override void OnDisable() {
        base.OnDisable();
        SimpleEventManager.RemoveListener<PlayerBulletHitEnemyEvent>(OnPlayerBulletHitEnemy);
    }

    private void Update() {
        if (IsEvolvedWeapon && InHeroHand()) {
            _updateTimer += Time.deltaTime;
            if (_updateTimer >= UpdateInterval) {
                _cacheFunnels = _funnels.Keys.ToList();
                foreach (var funnel in _cacheFunnels) {
                    if (funnel == null) {
                        _funnels.Remove(funnel);
                    } else {
                        _funnels[funnel] -= _updateTimer;
                        if (_funnels[funnel] <= 0) {
                            StoreFunnel(funnel);
                        }
                    }
                }
                _updateTimer = 0f; 
                _cacheFunnels.Clear();
            }
        }

        if (IsEvolvedWeapon) {
            if (_isAtttacking) {
                _stopHitTime = Time.time;
            }
            if (Time.time - _stopHitTime > _hitTime) {
                _currentHitCount = 0;
            }
        }
        
    }
    public override void StopWeapon() {
        base.StopWeapon();
        ClearAllFunnel();
    }

    public override void OnSwitchWeapon(bool toFront, bool isPick) {
        base.OnSwitchWeapon(toFront, isPick);
        if (!toFront) {
            ClearAllFunnel();
        }
    }

    public override void AttackStart() {
        base.AttackStart();
        if( latestCreatedBulletObject.TryGetComponent<RGLaserBuff>(out var buff))
            buff.buff_probability = 5;
        latestCreatedBulletObject.GetComponent<RGLaser>().SetMaxAddDamageFactor(maxLaserDamageFactor);
        _isAtttacking = true;
    }

    public override void AttackStop() {
        base.AttackStop();
        _isAtttacking = false;
    }

    private void OnPlayerBulletHitEnemy(PlayerBulletHitEnemyEvent e) {
        if (IsEvolvedWeapon && e.sourceWeapon == this && InHeroHand()) {
            _currentHitCount++;
            int exFunnelCount = BattleData.data.HasBuff(emBuff.MasterContinuous) ? 1 : 0;
            exFunnelCount += BattleData.data.HasBuff(emBuff.MasterShotgun) ? 1 : 0;
            _currentMaxFunnelCount = maxFunnelCount + exFunnelCount;
            if (_currentHitCount >= createFunnelHitCount && _funnels.Count < _currentMaxFunnelCount) {
                CreatingFunnel();
                _currentHitCount = 0;
            }
        }
    }

    private void CreatingFunnel() {
        GetFunnelRootPos();
        var index = GetEmptyRoot();
        _createFunnelAnim.gameObject.SetActive(true);
        anim.SetBool(CreateFunnelAnim, true);
        CancelInvoke(nameof(HidenCreateFunnelAnim));
        Invoke(nameof(HidenCreateFunnelAnim), 1.5f);
        if(index == -1)
            return;
        FunnelWeaponContainer funnel = TakeFunnel(index);
        if (!funnel) {
            return;
        }
        _funnels.Add(funnel, funnelLifeTime);
            
        var weapon = funnel.GetComponentInChildren<RGWeapon>();
        funnel.atk_cd = funnelAttackInterval;
        weapon.ChangeAtk(funnelDamage, 0, emChangeValueType.Set);
        funnel.StartRunning(true);
        if (BattleData.data.HasBuff(emBuff.ExpertPet)) {
            funnel.attribute.atk_speed += 0.5f;
        }
        funnel.isOwnerActive = () => InHeroHand();
    }

    private void HidenCreateFunnelAnim() {
        anim.SetBool(CreateFunnelAnim, false);
        _createFunnelAnim.gameObject.SetActive(false);
    }
    
    private FunnelWeaponContainer TakeFunnel(int j) {
        FunnelWeaponContainer funnel = null;
        StateSynchronizer.WithRegStaticObjectDisabled(() => {
            funnel = Instantiate(funnelLaserFish)
                .GetComponent<FunnelWeaponContainer>();
            funnel.transform.SetParent(_funnelRoot.GetChild(j));
        });
        if (funnel.effect == null) {
            funnel.effect = ResourcesUtil.Load<GameObject>("RGPrefab/Bullet/Effect/hit_white.prefab");
        }
        funnel.awake = false;
        funnel.SetSourceObject(gameObject);
        funnel.weapon.level = level;
        if (BattleData.data.HasBuff(emBuff.PetCharge)) {
            var buffPrefab = ResourcesUtil.Load<GameObject>("RGPrefab/Bullet/Buff/buff_charge.prefab");
            var buff = RGBuff.AddBuff(buffPrefab, controller.gameObject, funnel.transform, true);
            if (buff is BuffCharge buffCharge) {
                buffCharge.addAtkFactor = 0.25f;
                buffCharge.addAtkSpeedFactor = 0.25f;
                buffCharge.addMoveSpeedFactor = 0;
                buffCharge.buff_time = 12;
            }
            buff.RefreshTime(12, true);
        }
        return funnel;
    }
    private int GetEmptyRoot() {
        for (int i = 0; i < _funnelRoot.childCount; i++) {
            bool isFind = false;
            foreach (var funnel in _funnels) {
                if (funnel.Key.parent_point == _funnelRoot.GetChild(i)) {
                    isFind = true;
                    break;
                }
            }
            if (!isFind) {
                return i;
            }
        }
        return -1;
    }
    
    private void GetFunnelRootPos(float exRadius = 1) {
        int ffpCount = _funnels.Count + 1;
        if (_funnelRoot.childCount == ffpCount)
            return;

        var currentCount = _funnelRoot.childCount;
        _funnelRoot.rotation = Quaternion.identity;
        var angle = 360 / ffpCount;
        for (int i = 0; i < ffpCount; i++) {
            GameObject ffp;
            if (i < currentCount) {
                ffp = _funnelRoot.GetChild(i).gameObject;
            } else {
                ffp = new GameObject("ffp_" + i);
                ffp.transform.parent = _funnelRoot;
            }
            ffp.transform.position = _funnelRoot.position + Vector3.up * ((1.5f + 1.5f  / 15) * exRadius);
            _funnelRoot.Rotate(new Vector3(0, 0, angle));
        }
    }
    
    private void StoreFunnel(FunnelWeaponContainer funnel) {
        if (funnel) {
            if (funnel.effect) {
                PrefabPool.Inst.Take(funnel.effect).transform.position = funnel.transform.position;
            }
            Destroy(funnel.gameObject);
        }
        _funnels.Remove(funnel);

        if (!((RGController)controller).IsLocalPlayer()) {
            return;
        }
        var itemProto = laserFishItem;
        var position = funnel.transform.position;
        var note = GameObject.Instantiate(itemProto, position,
            Quaternion.identity);
        int dir = controller.rg_random.Range(0, 2) == 1 ? 1 : -1;
        Quaternion rotation = Quaternion.AngleAxis(dir * controller.rg_random.Range(3, 15), Vector3.back);
        var finallDir = rotation * (controller.transform.position - position);
        float force = controller.rg_random.Range(10, 20);
        note.GetComponent<LaserFishItem>().GetForce(finallDir, force);
    }

    private void ClearAllFunnel() {
        _cacheFunnels = new List<FunnelWeaponContainer>(_funnels.Keys);
        foreach (var funnel in _cacheFunnels) {
            StoreFunnel(funnel);
        }
        _funnels.Clear();
        _cacheFunnels.Clear();
        HidenCreateFunnelAnim();
        _currentHitCount = 0;
    }
    
    IEnumerator FunnelRotating() {
        while (true) {
            _funnelRoot.Rotate(new Vector3(0, 0, 2));
            yield return null;
        }
    }
}
