using Cicada;
using ModeDefence;
using RGScript.Character.Player;
using RGScript.Manager.Factory;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace RGScript.Weapon {
    public class StaffOfNecromancy : GunSummon01 {
        public GameObject boneProto;
        private readonly List<RGPetController> _pets = new();
        private int _summonCount;
        private bool _canSummonLeader;
        private NpcSkeleton3 _npcLeader;
        public int spawnEliteAfterSummonCount = 3;
        public int spawnLeaderAfterSummonCount = 6;
        private readonly List<Vector3> _bonePosList = new();
        private readonly List<GameObject> _waitToDestroy = new();
        public float collectBoneRadius = 7.5f;
        private float _collectBoneRadiusPow2;

        protected override void Awake() {
            base.Awake();
            _collectBoneRadiusPow2 = Mathf.Pow(collectBoneRadius, 2);
        }

        protected override GameObject Summon() {
            var position = transform.position;
            var petFactory = FactoryManager.Instance.GetFactory<PetFactory>();
            
            if (_canSummonLeader) {
                _summonCount = 0;

                petFactory.Create("npc_skeleton_03", petController => {
                    petController.transform.position = position;
                    _npcLeader = petController.GetComponent<NpcSkeleton3>();
                    
                    foreach (Vector3 pos in _bonePosList) {
                        CreateBoneEffect(pos);
                    }

                    foreach (var deadPet in _waitToDestroy) {
                        Destroy(deadPet.gameObject);
                    }

                    _waitToDestroy.Clear();
                    AfterSummonPet(petController.gameObject, position, true);
                });
                
                _canSummonLeader = false;
                return null;
            }

            _summonCount++;
            string petId;
            if (_summonCount >= spawnEliteAfterSummonCount) {
                petId = thisBattleData.HasMasterStaff() && rg_random.Range(0, 100) < 50 ?
                    "ex_npc_skeleton_02" : "ex_npc_skeleton_01";
                _summonCount = 0;
            } else {
                petId = thisBattleData.HasMasterStaff() && rg_random.Range(0, 100) < 50 ?
                    "npc_skeleton_02" : "npc_skeleton_01";
            }
            
            petFactory.Create(petId, petController => {
                petController.transform.position = position;
                
                if (IsSplitting) {
                    var summon01 = petController.GetComponent<NpcSummon01>();
                    if (summon01) {
                        summon01.hand.front_weapon.BulletSizeFactor.AddModifier(new AttributeModifier(SplittingScale,
                            AttributeModifierType.Flat, this));
                    }
                }
                
                AfterSummonPet(petController.gameObject, position, false);
            });
            
            return null;
        }

        private void AfterSummonPet(GameObject tempObj, Vector3 position, bool isLeader) {
            var pet = tempObj.GetComponent<RGPetController>();
            pet.SetMaster(controller.transform);
            if (!isLeader) {
                pet.onDestroyCb += RefreshSummonLeaderCondition;
                AddPet(pet);
            }

            if (pet is NpcSummon01 summon01) {
                if (!isLeader) {
                    summon01.autoDestroyAfterDead = false;
                }

                if (BattleData.data.IsDefenceMode) {
                    if (summon01.hand && summon01.hand.front_weapon) {
                        summon01.hand.front_weapon.level = level;
                        if (BattleData.data.IsDefenceMode) {
                            var scale = Mathf.Clamp(
                                1 + (float)level / DefenceModeConfig.Config.DEFENCE_WEAPON_MAX_LEVEL,
                                1,
                                2);
                            summon01.transform.localScale *= scale;
                            summon01.hand.front_weapon.BulletSizeFactor.AddModifier(
                                new AttributeModifier(scale, AttributeModifierType.Flat, this));
                        }
                    }
                }
            }

            Instantiate(smoke_effect, position, Quaternion.identity);
            BulletCreateEvent(tempObj);
            AfterBulletCreateEvent();
            MakeConsume();
            AfterAttackEvent();
        }

        private void AddPet(RGPetController petController) {
            _pets.Add(petController);
            RefreshSummonLeaderCondition();
        }

        private void RefreshSummonLeaderCondition() {
            List<RGPetController> deadPets = _pets.Where(pet => pet.dead).ToList();
            if (!controller || deadPets.Count < spawnLeaderAfterSummonCount) {
                return;
            }

            var controllerPos = controller.transform.position;
            _bonePosList.Clear();
            for (int i = deadPets.Count - 1; i >= 0; i--) {
                RGPetController deadPet = deadPets[i];
                if (!(Vector3.Magnitude(deadPet.transform.position - controllerPos) < _collectBoneRadiusPow2)) {
                    continue;
                }

                _pets.Remove(deadPet);
                _bonePosList.Add(deadPet.transform.position);
                deadPets.RemoveAt(i);
                _waitToDestroy.Add(deadPet.gameObject);
            }

            _canSummonLeader = true;
        }

        private void CreateBoneEffect(Vector3 worldPos) {
            if (!_npcLeader) {
                return;
            }
            
            worldPos.y += 0.5f;
            var bone = Instantiate(boneProto, worldPos, Quaternion.identity);
            FlyingBone flyingBone = bone.GetComponent<FlyingBone>();
            flyingBone.StartFlying(_npcLeader.gameObject);
        }
    }
}