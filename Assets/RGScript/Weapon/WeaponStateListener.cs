using System;
using UnityEngine;

/// <summary>
/// 监听武器攻击事件，便于外部统一监听的类
/// 和武器的脚本挂在同一个GameObject下，在武器脚本中调用触发函数
/// </summary>
public class WeaponStateListener : MonoBehaviour {
    /// <summary>
    /// 武器攻击开始
    /// 需要监听事件处添加回调函数
    /// </summary>
    public event Action onWeaponAttackStart;

    /// <summary>
    /// 武器攻击结束
    /// 需要监听事件处添加回调函数
    /// </summary>
    public event Action onWeaponAttackFinished;

    /// <summary>
    /// 武器冷却开始
    /// 需要监听事件处添加回调函数
    /// </summary>
    public event Action onWeaponColdDownStart;
    
    /// <summary>
    /// 武器冷却结束
    /// 需要监听事件处添加回调函数
    /// </summary>
    public event Action onWeaponColdDownFinished;

    /// <summary>
    /// 武器攻击开始
    /// 在同一GameObject上的武器脚本中调用
    /// </summary>
    public void OnWeaponAttackStart() {
        if (onWeaponAttackStart != null) {
            onWeaponAttackStart();
        }
    }

    /// <summary>
    /// 武器攻击结束 触发函数
    /// 在同一GameObject上的武器脚本中调用
    /// </summary>
    public void OnWeaponAttackFinished() {
        if (onWeaponAttackFinished != null) {
            onWeaponAttackFinished();
        }
    }
    
    /// <summary>
    /// 武器冷却攻击的开始 触发函数
    /// 在同一GameObject上的武器脚本中调用
    /// </summary>
    public void OnWeaponColdDownStart() {
        if (onWeaponColdDownStart != null) {
            onWeaponColdDownStart();
        }
    }

    /// <summary>
    /// 武器冷却结束 触发函数
    /// 在同一GameObject上的武器脚本中调用
    /// </summary>
    public void OnWeaponColdDownFinished() {
        if (onWeaponColdDownFinished != null) {
            onWeaponColdDownFinished();
        }
    }
}
