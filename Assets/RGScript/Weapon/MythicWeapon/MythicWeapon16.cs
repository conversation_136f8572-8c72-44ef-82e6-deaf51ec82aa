using System.Collections.Generic;
using UnityEngine;
using ModeSeason.Artifacts;
using I2.Loc;
using SoulKnight.Runtime.Weapon;

/// <summary>
/// 古大陆的神器 转 常规武器
/// </summary>
public class MythicWeapon16 : ArtifactMythicWeaponBase
{
    public int crystalLimit = 4;
    public int crystalLineDamage = 4;
    public float crystalBurstSizeAddFactor = 0.3f;
    public int crystalAddDuration = 10;
    public int crystalLimitAdd = 1;

    protected override bool showSpecialButton => false;

    protected override string MythicAbility => string.Format(ScriptLocalization.Get($"atf2_staff09_desc1", ""), crystalLimit, crystalLineDamage);
    protected override string MythicAttribute1 => string.Format(base.MythicAttribute1, (crystalBurstSizeAddFactor * 100).ToSimpleString());
    protected override string MythicAttribute2 => string.Format(base.MythicAttribute2, crystalAddDuration, crystalLimitAdd);

    Transform displayW;
    Transform animationW;

    PartEffect partEffect;
    WeaponComSimpleBulletEmitter bulletEmitter;

    protected override void Awake() {
        base.Awake();
        displayW = transform.Find("w");
        animationW = actionComponent.transform.Find("w/img");
        displayW.gameObject.SetActive(true);
        animationW.gameObject.SetActive(false);
        actionComponent.onAnimationStart = new FuncObj(this, nameof(AttackAnimationStart));
        actionComponent.onAnimationEnd = new FuncObj(this, nameof(AttackAnimationEnd));
        _cachedPartArray = GetComponentsInChildren<ArtifactComponentPart>();
        addAtk = bulletsInfo[0].damage;
        addCrit = bulletsInfo[0].critic;
        var part = GetComponentInChildren<ArtifactComponentPart>();
        part.addEnergyConsume = consume;
        bulletEmitter = GetComponentInChildren<WeaponComSimpleBulletEmitter>();
    }

    public override void OnSwitchWeapon(bool toFront, bool pick) {
        displayW.gameObject.SetActive(!toFront);
        animationW.gameObject.SetActive(toFront);
        base.OnSwitchWeapon(toFront, pick);
    }

    protected override void OnInitPartEffect() {
        if (partEffect == null) {
            partEffect = new PartEffect();
            partEffect.weapon = this;
            partEffect.enabled = true;
            partEffect.tier = 1;
            partEffect.level = MythicLevelInBattle;
            _partEffectsDict = new();
            _partEffectsDict.Add(partEffect.GetType().Name, partEffect);
            partEffect.OnEffectStart();
            partEffect.OnEffectChanged(1, 1, partEffect.level, partEffect.level);
        }
    }

    protected override void CancelPartEffectsAndInscriptions() {
        base.CancelPartEffectsAndInscriptions();
        partEffect = null;
    }

    public override void DropWeapon(int targetLayer) {
        if (partEffect != null) {
            partEffect.OnEffectEnd();
            _partEffectsDict.Remove(partEffect.GetType().Name);
            partEffect = null;
        }
        displayW.gameObject.SetActive(true);
        animationW.gameObject.SetActive(false);
        base.DropWeapon(targetLayer);
    }

    void AttackAnimationStart() {
        bulletEmitter.onEmitBulletOverrideOnce = OverrideMainBullet;
    }

    void AttackAnimationEnd() {
        actionComponent.overrideEnergyConsume = 0;
        actionComponent.SetDisplayConsume();
    }

    void ActionComponentUpdate(float dt) {
        if (actionComponent.modifiedChargeAmount >= 0.45f) {
            var extraEmitTimes = (int)(actionComponent.modifiedChargeAmount / 0.5f);
            actionComponent.overrideEnergyConsume = consume + 2 * extraEmitTimes;
            if (actionComponent.overrideEnergyConsume > 0) {
                actionComponent.SetDisplayConsume();
            }
        }
    }

    void OverrideMainBullet(WeaponComponentBulletEmitter emitter, RGWeaponArtifactBody.AttackType attackType, int atkSequence, int attackTypeParam) {
        var extraEmitTimes = (int)(actionComponent.modifiedChargeAmount / 0.5f);
        if (thisBattleData.HasBuff(emBuff.MasterContinuous)) {
            var factor = Config.GetTalentCustomData<float>(emBuff.MasterContinuous);
            extraEmitTimes = Mathf.FloorToInt(extraEmitTimes * factor);
        }
        emitter.tempExtraEmitTimes += extraEmitTimes;
        emitter.onEmitBullet -= OnEmitMainBullet;
        emitter.onEmitBullet += OnEmitMainBullet;
        emitter.OnEmit(atkSequence, attackTypeParam);
    }

    void OnEmitMainBullet(GameObject bulletObj, float chargeAmount, int atkTypeParam) {
        if (partEffect != null && partEffect.level >= 3) {
            partEffect.CrystalBurst();
        }
    }

    public class PartEffect : ArtifactPartEffect {

        public float crystalBurstDelayRemain;

        private List<ModeSeason.Artifacts.Staff09RGCrystal> staff09RgCrystals;

        public override void OnEffectStart() {
            ArtifactsLevelData.AddEventListener<HeroUseSkillEvent>(this, nameof(OnUseSkill));
            staff09RgCrystals = new List<Staff09RGCrystal>();
        }

        public override void OnUpdate(float dt) {
            for (var i = staff09RgCrystals.Count - 1; i >= 0; --i) {
                if (staff09RgCrystals[i] == null || !staff09RgCrystals[i].gameObject.activeSelf) {
                    if (staff09RgCrystals[i] != null) {
                        GameObject.Destroy(staff09RgCrystals[i].gameObject);
                    }
                    staff09RgCrystals.RemoveAt(i);
                }
            }

            crystalBurstDelayRemain -= dt;
            if (crystalBurstDelayRemain <= 0) {
                crystalBurstDelayRemain = 1;
                CrystalBurst();
            }
        }

        public void CrystalBurst() {
            var bulletEmitter = (weapon as MythicWeapon16).bulletEmitter;
            var sizeFactor = 1f;
            if (weapon.thisBattleData.HasBuff(emBuff.MasterLaser)) {
                sizeFactor = 1.3f;
            }
            for (var i = staff09RgCrystals.Count - 1; i >= 0; --i) {
                if (staff09RgCrystals[i] != null && staff09RgCrystals[i].gameObject.activeSelf && staff09RgCrystals[i].CanLink(weapon.controller.transform)) {
                    var pos = staff09RgCrystals[i].transform.position;
                    var bulletCfg = bulletEmitter.GetBulletConfig(0, 0);
                    var bulletInfo = weapon.CreateBulletInfo(bulletCfg.bulletProto, pos, bulletCfg.size * 0.65f * sizeFactor, 0, 0, true);
                    var damageInfo = weapon.CreateDamageInfo(bulletCfg.bulletProto, GetDamage());
                    BulletFactory.TakeBullet(bulletInfo, damageInfo);
                }
            }
        }

        private void CreateCrystal(GameObject sGameObject) {
            var position = sGameObject.transform.position;
            var temp_obj = GameObject.Instantiate(
                ResourcesUtil.Load<GameObject>("RGPrefab/Bullet/bullet_mythic_w16_crystal.prefab"),
                position, Quaternion.identity, RGGameSceneManager.Inst.temp_objects_parent);
            var staff09RgCrystal = temp_obj.GetComponent<Staff09RGCrystal>();
            var sizeFactor = 1f;
            if (weapon.thisBattleData.HasBuff(emBuff.MasterLaser)) {
                sizeFactor = 1.6f;
            }
            staff09RgCrystal.UpdateInfo(
                new BulletInfo() {
                    speed = 0,
                    createPosition = position + new Vector3(0, 0.5f, 0),
                    duration = -1,
                }.SetSourceWeapon(weapon).SetSourceObject(sGameObject).SetBulletSize(1 * sizeFactor).SetCamp(weapon.controller.camp),
                new DamageInfo() {
                    damage = GetDamage(),
                    critic = 0,
                    repel = 3,
                }.SetCamp(weapon.controller.camp),
                tier >= 3,
                GetDisableTime()
            );

            staff09RgCrystals.Add(staff09RgCrystal);
        }

        public override void OnEffectEnd() {
            ArtifactsLevelData.RemoveEventListener<HeroUseSkillEvent>(this, nameof(OnUseSkill));
        }

        void OnUseSkill(HeroUseSkillEvent e) {
            if (e.controller == weapon.controller) {
                CreateCrystal(weapon.controller.gameObject);
                while (staff09RgCrystals.Count > CrystalCountLimit()) {
                    GameObject.Destroy(staff09RgCrystals[0].gameObject);
                    staff09RgCrystals.RemoveAt(0);
                }
            }
        }

        public int GetDamage() {
            var addDamage = 0;
            if (weapon.thisBattleData.HasBuff(emBuff.MasterLaser)) {
                addDamage = 1;
            }
            var dmg = (weapon as MythicWeapon16).crystalLineDamage + addDamage;
            dmg = Mathf.Max(0, Mathf.RoundToInt(dmg * (1 + (weapon.GetDamageFactor() - 1) * 0.3f)));
            return dmg;
        }

        private float GetDisableTime() {
            return 10f + (level > 1 ? (weapon as MythicWeapon16).crystalAddDuration : 0);
        }

        public int CrystalCountLimit() {
            return (weapon as MythicWeapon16).crystalLimit + (level > 1 ? (weapon as MythicWeapon16).crystalLimitAdd : 0);
        }

    }
}
