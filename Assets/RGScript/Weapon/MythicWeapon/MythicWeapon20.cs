using System.Collections.Generic;
using UnityEngine;
using System;

public class MythicWeapon20 : MythicWeapon, WeaponInterface, ChargeWeaponInterface, IWeaponBulletSpliter {
    protected override void SetupMythicWeapon() { }
    protected override void EnableMythicAttribute1() { }
    protected override void EnableMythicAttribute2() { }
    protected override void EnableMythicAttribute3() { }
    protected override void RemoveMythicWeapon() { }
    protected override void DisableMythicAttribute1() { }
    protected override void DisableMythicAttribute2() { }
    protected override void DisableMythicAttribute3() { }

    protected override string MythicAttribute1 => string.Format(I2.Loc.ScriptLocalization.Get($"weapon/weapon_mythic_{id:00}/attr/0"),
        GetAirFlowDuration().ToSimpleString());

    protected override string MythicAttribute2 => string.Format(I2.Loc.ScriptLocalization.Get($"weapon/weapon_mythic_{id:00}/attr/1"),
        GetAdditionalPenetrationTime());

    protected override string MythicAttribute3 => string.Format(I2.Loc.ScriptLocalization.Get($"weapon/weapon_mythic_{id:00}/attr/2"),
        (GetAdditionalDamageFrequency() * 100).ToSimpleString());

    public override int realConsume { get => Mathf.Max((chargeLevel > 0 ? consume : 0) + consumeChange, 0); }

    public AudioClip clip_hold;
    public AudioClip clip_bullet;
    public float max_time = 4;
    public float addAirFlowTime = 2;
    public int addPenetrateTimes = 2;
    public float addDamageFrequency = 0.3f;
    float a_time = 0;
    bool _hasAttack;
    bool _animating;
    public GameObject reload_obj;
    GameObject the_reload_obj;
    int chargeLevel = 0;

    float ChargeThreshold() {
        return 0.05f;
    }

    float GetAddAirFlowTime() {
        return addAirFlowTime;
    }

    int GetAdditionalPenetrationTime() {
        return addPenetrateTimes;
    }

    float GetAdditionalDamageFrequency() {
        return addDamageFrequency;
    }

    float GetAirFlowDuration() {
        return 2 + (MythicLevelInBattle > 0 ? GetAddAirFlowTime() : 0) + (chargeLevel > 0 ? 3 : 0);
    }

    void Update() {
        if (!_animating && attackPressed) {
            var oldLevel = chargeLevel;
            if (controller != null && controller.attribute.energy >= consume) {
                a_time += Time.deltaTime * holdSpeed * _speedFactor;
                chargeLevel = (a_time + 0.0001f > GetMaxChargeTime()) ? 1 : 0;
                if (chargeLevel != oldLevel) {
                    FlushIcon();
                }
                if (a_time >= GetMaxChargeTime()) {
                    a_time = GetMaxChargeTime();
                    if (_autoAttack) {
                        SetAttack(false, false);
                    }
                }
                if (a_time / GetMaxChargeTime() >= ChargeThreshold() && the_reload_obj == null) {
                    the_reload_obj = Instantiate(reload_obj) as GameObject;
                    the_reload_obj.transform.SetParent(controller.transform, false);
                    RGMusicManager.GetInstance().PlayEffect(clip_hold);
                }

                if (the_reload_obj != null) {
                    var p = a_time / GetMaxChargeTime();
                    the_reload_obj.GetComponent<ReloadClip>().SetReloadProgress(p, false);
                }
            } else {
                a_time = 0;
                chargeLevel = 0;
                if (the_reload_obj != null) {
                    GameObject.Destroy(the_reload_obj);
                    the_reload_obj = null;
                }
                if (controller is RGController rgctrl) {
                    rgctrl.BtnAtkClick(false);
                }
                FlushIcon();
            }
        }
    }

    public override void AttackKeyDown(bool manual) {
        attackPressed = true;
        a_time = 0;
        _hasAttack = true;
        SendAttackKeyDownEvent();
    }

    public override void AttackKeyUp(bool manual) {
        base.AttackKeyUp(manual);
        attackPressed = false;
        if (_hasAttack) {
            _hasAttack = false;
            if (chargeLevel < 1) {
                anim.SetTrigger("atk_t");
            } else {
                anim.SetTrigger("charge_atk_t");
            }
            _animating = true;
            a_time = 0;

            if (the_reload_obj != null) {
                Destroy(the_reload_obj);
            }

            if (chargeLevel > 0) {
                SimpleEventManager.Raise(ChargeAttackStartEvent.UseCache(controller, this, 1));
            }
        }
    }

    public override void AttackStop() {
        base.AttackStop();
        if (!_hasAttack) {
            Attack();
        }
    }

    public override void OnSwitchWeapon(bool toFront, bool isPick) {
        base.OnSwitchWeapon(toFront, isPick);
        if (controller is RGController rgctrl && rgctrl.ButtonAtkPressed && !isPick) {
            if (chargeLevel > 0 && !toFront) {
                EmitAirFlow();
                rgctrl.BtnAtkClick(true);
            } else if (toFront) {
                rgctrl.BtnAtkClick(true);
            }
        }
    }

    public override void SetController(RGBaseController ctrl) {
        base.SetController(ctrl);
        var chain = transform.Find("w/chain");
        if (chain != null) {
            chain.gameObject.SetActive(ctrl != null);
        }
    }

    public override void StopWeapon() {
        if (_animating && chargeLevel > 0) {
            EmitAirFlow();
        }
        base.StopWeapon();
        if (the_reload_obj != null) {
            Destroy(the_reload_obj);
        }
        _animating = false;
    }

    public override void DropWeapon(int target_layer) {
        base.DropWeapon(target_layer);
        bulletSplitActive = false;
    }


    public void Attack() {
        PlayAttackAudio();
        var meleeBullet = BulletFactory.TakeBullet(
            GetBulletInfo().SetPosition(transform.parent.position + Vector3.up * 0.6f + controller.oriantation * 0.5f),
            GetDamageInfo()
        );
        meleeBullet.transform.localScale = new Vector3(controller.facing, 1, 1);
        BulletCreateEvent(meleeBullet);
        if (bulletSplitActive) {
            DefaultWeaponBulletSpliter.DefaultSplitBullet(this, meleeBullet, splitCfg);
        }
        EmitAirFlow();
    }

    void EmitAirFlow() {
        if (chargeLevel > 0 && controller != null && controller.attribute.energy >= realConsume) {
            var bulletInfo = GetBulletInfo(1);
            var damageInfo = GetDamageInfo(1);
            var count = 1;
            var angleInterval = 0f;
            var size = 1f;
            if (bulletSplitActive) {
                damageInfo.SetDamage(Mathf.RoundToInt(damageInfo.damage * splitCfg.damageFactor));
                count = splitCfg.count;
                angleInterval = 30;
                size = 0.7f;
            }

            for (var i = 0; i < count; ++i) {
                var pos = GameUtil.CalculateAngleSpanPosition(transform.position, controller.oriantation, angleInterval * (count - 1), 1.5f, count, i);
                bulletInfo.SetPosition(pos);
                bulletInfo.SetAngle((pos - transform.position.Vec2()).ToAngle());
                var bulletObj = EmitBullet(bulletInfo, damageInfo, GetAirFlowDuration());
                bulletObj.transform.localScale = new Vector3(bulletObj.transform.localScale.x, bulletObj.transform.localScale.y * size, 1);
                BulletCreateEvent(bulletObj);
            }
        }

        MakeConsume();
        AfterBulletCreateEvent();
        AfterAttackEvent();
        a_time = 0;
        chargeLevel = 0;
    }

    GameObject EmitBullet(BulletInfo bulletInfo, DamageInfo damageInfo, float duration) {
        var temp_obj = BulletFactory.TakeBullet(
            bulletInfo, damageInfo
        );
        if (temp_obj.TryGetComponent<AreaDamageCarrier>(out var adc)) {
            adc.duration = duration;
            if (MythicLevelInBattle > 2) {
                adc.SetDamageInterval(bulletInfo.bulletProto.GetComponent<AreaDamageCarrier>().damageInterval / (1 + GetAdditionalDamageFrequency()));
            }

            var enhanceHandler = new BulletEnhanceHandler();
            var extraBulletInfo = GetBulletInfo(2);
            var extraDamageInfo = GetDamageInfo(2);
            enhanceHandler.Start(adc, extraBulletInfo, extraDamageInfo, MythicLevelInBattle > 1 ? GetAdditionalPenetrationTime() : 0);
        }
        return temp_obj;
    }

    public void AttackEnd() {
        _animating = false;
        FlushIcon();
    }

    public override bool IsHold() {
        return true;
    }

    public float GetMaxChargeTime() {
        return max_time;
    }

    public WeaponChargeState GetWeaponChargeState() {
        if (a_time / GetMaxChargeTime() >= ChargeThreshold()) {
            if (Mathf.Abs(GetChargeAmount() - 1) < Mathf.Epsilon) {
                return WeaponChargeState.FullCharged;
            }
            return WeaponChargeState.Charging;
        }
        return WeaponChargeState.Normal;
    }

    public float GetChargeAmount() {
        return a_time / GetMaxChargeTime();
    }

    public void SetChargeAmount(float amount) {
        if (the_reload_obj != null) {
            the_reload_obj.GetComponent<ReloadClip>().SetReloadProgress(amount, false);
        }
        a_time = amount * GetMaxChargeTime();
    }

    class BulletEnhanceHandler {
        AreaDamageCarrier adc;
        BulletInfo bulletInfo;
        DamageInfo damageInfo;
        int addPenetrate;

        float EnhanceBulletDamageFactor() {
            return 0.8f;
        }

        public void Start(AreaDamageCarrier adc, BulletInfo bulletInfo, DamageInfo damageInfo, int addPenetrate) {
            this.adc = adc;
            this.bulletInfo = bulletInfo;
            this.damageInfo = damageInfo;
            this.addPenetrate = addPenetrate;
            RGGameSceneManager.Inst.StartCoroutine(Update());
        }

        class ExtraBulletData {
            public Collider2D col;
            public DamageCarrier bullet;
            public Vector2 offset;

            public Vector3 GetPosition() {
                if (col != null) {
                    return col.transform.TransformPoint(col.offset);
                }
                return Vector3.zero;
            }

            public void UpdateTransform() {
                if (col != null && bullet != null) {
                    bullet.transform.position = col.transform.TransformPoint(col.offset);
                    bullet.transform.eulerAngles = col.transform.eulerAngles;
                }
            }
        }

        System.Collections.IEnumerator Update() {
            var triggers = new List<Collider2D>();
            for (var i = 0; i < adc.areaTriggers.Count; ++i) {
                var trigger = GameObject.Instantiate(adc.areaTriggers[i].gameObject).GetComponent<Collider2D>();
                trigger.transform.position = adc.areaTriggers[i].transform.position;
                trigger.transform.localScale = adc.areaTriggers[i].transform.localScale;
                trigger.transform.rotation = adc.areaTriggers[i].transform.rotation;
                triggers.Add(trigger);
            }

            LookUpRecordList<DamageCarrier, ExtraBulletData> list = new();
            var bulletLayer = LayerMask.GetMask("Bullet");
            var checkIntervalRemain = 0f;
            var wait = new WaitForEndOfFrame();
            while (true) {
                for (var i = list.Count - 1; i >= 0; --i) {
                    var col = list.GetKeyAt(i);
                    if (col == null || !col.gameObject.activeInHierarchy) {
                        if (list[i] != null && list[i].bullet != null) {
                            list[i].bullet.DeleteResourceObject();
                        }
                        list.RemoveAt(i);
                    }
                }

                if (adc != null && !adc.IsEnded() && (checkIntervalRemain -= Time.deltaTime) <= 0) {
                    checkIntervalRemain = 0.03f;
                    for (var i = 0; i < triggers.Count; ++i) {
                        var area = triggers[i];
                        if (!area.gameObject.activeInHierarchy || !area.enabled) {
                            continue;
                        }
                        var scale = area.transform.lossyScale;
                        if (area is BoxCollider2D boxCol) {
                            RaycastHit2D[] hits = null;
                            var center = area.transform.TransformPoint(area.offset);
                            var size = new Vector2(boxCol.size.x * scale.x, boxCol.size.y * scale.y);
                            var hitCount = PhysicsUtil.CheckBoxHit(center, size, boxCol.transform.eulerAngles.z, bulletLayer, ref hits);
                            for (var j = 0; j < hitCount; ++j) {
                                if (hits[j].rigidbody != null && hits[j].rigidbody.velocity.magnitude > 0 &&
                                    hits[j].rigidbody.TryGetComponent<DamageCarrier>(out var bullet) && bullet != adc &&
                                    !list.ContainsKey(bullet) && !bullet.bulletInfo.isFromEffect && bullet.damageInfo.camp == adc.damageInfo.camp) {
                                    list.Add(bullet, null);
                                }
                            }
                        } else if (area is CircleCollider2D circleCol) {
                            var hits = PhysicsUtil.CircleCastTargets(area.transform.TransformPoint(area.offset), circleCol.radius * scale.x, bulletLayer, out var hitCount);
                            for (var j = 0; j < hitCount; ++j) {
                                if (hits[j].rigidbody != null && hits[j].rigidbody.velocity.magnitude > 0 &&
                                    hits[j].rigidbody.TryGetComponent<DamageCarrier>(out var bullet) && bullet != adc &&
                                    !list.ContainsKey(bullet) && !bullet.bulletInfo.isFromEffect && bullet.damageInfo.camp == adc.damageInfo.camp) {
                                    list.Add(bullet, null);
                                }
                            }
                        }
                    }
                }

                for (var i = 0; i < list.Count; ++i) {
                    var bullet0 = list.GetKeyAt(i);
                    if (list[i] == null) {
                        var col = bullet0.GetComponentInChildren<Collider2D>();
                        var extraBulletData = new ExtraBulletData {
                            col = col,
                            offset = col.offset
                        };
                        bulletInfo.SetPosition(extraBulletData.GetPosition());
                        bulletInfo.SetAngle(adc.transform.eulerAngles.z);
                        bulletInfo.SetSourceObject(bullet0.bulletInfo.sourceObject);
                        bulletInfo.SetSourceWeapon(bullet0.bulletInfo.sourceWeapon);
                        bulletInfo.IsFromEffect(true);
                        damageInfo.SetDamage(Mathf.Max(1, (int)(bullet0.damageInfo.damage * EnhanceBulletDamageFactor())));

                        if (col is BoxCollider2D boxCol) {
                            bulletInfo.SetBulletSize(Mathf.Max(boxCol.size.x, boxCol.size.y) * 0.5f + 0.5f);
                        } else if (col is CircleCollider2D circleCol) {
                            bulletInfo.SetBulletSize(circleCol.radius + 0.5f);
                        }
                        var addBullet = BulletFactory.TakeBullet(bulletInfo, damageInfo);
                        extraBulletData.bullet = addBullet.GetComponent<DamageCarrier>();
                        list[i] = extraBulletData;

                        if (addPenetrate > 0) {
                            var dmgTrigger = bullet0.GetComponentInChildren<RGBulletTrigger>();
                            if (dmgTrigger != null) {
                                dmgTrigger.through_count += addPenetrate;
                            }
                        }
                    }

                    if (list[i] != null) {
                        list[i].UpdateTransform();
                    }
                }

                if (adc == null || adc.IsEnded()) {
                    for (var i = 0; i < triggers.Count; ++i) {
                        GameObject.Destroy(triggers[i].gameObject);
                    }
                    triggers.Clear();
                }

                if (triggers.Count == 0 && list.Count == 0) {
                    yield break;
                }

                yield return wait;
            }
        }
    }

    BulletSplitConfig splitCfg;
    bool bulletSplitActive;
    public void StartSplit(Func<BulletSplitConfig> splitConfigGetter, Action<BulletSplitConfig> onSplitStart) {
        splitCfg = splitConfigGetter();
        onSplitStart?.Invoke(splitCfg);
        bulletSplitActive = true;
    }

    public void EndSplit(Action onSplitEnd) {
        bulletSplitActive = false;
        onSplitEnd?.Invoke();
    }
}
