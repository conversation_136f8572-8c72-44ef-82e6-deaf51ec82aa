using System.Collections;
using UnityEngine;

/// <summary>
/// 盾牌
/// </summary>
public class GunShield : RGWeapon, WeaponInterface {
    protected bool _attackStartWhenSwitchWeapon = true;

    public override void AttackStart() {
        if (controller.attribute.energy < consume)return;
        var root = controller.transform;
        if (!root.Find("shield_w")) {
            anim.SetTrigger("atk_t");
            var temp_obj = BulletFactory.TakeBullet(
                GetBulletInfo().SetAngle(0),
                GetDamageInfo(),
                true,
                root);
            temp_obj.transform.localPosition = Vector3.zero;
            temp_obj.gameObject.name = "shield_w";
            RGMusicManager.GetInstance().PlayEffect(audio_clip);
            BulletCreateEvent(temp_obj);
            AfterBulletCreateEvent();
        } else {
            var shield = root.Find("shield_w").GetComponent<RGShield>();
            if (shield != null) {
                shield.ReSetDestoryTime();
            }
        }
        MakeConsume();
        AfterAttackEvent();
    }

    public void Attack() { }

    public override void OnSwitchWeapon(bool toFront, bool isPick) {
        base.OnSwitchWeapon(toFront, isPick);
        if (toFront && _attackStartWhenSwitchWeapon) {
            AttackStart();
        }
    }

}