using System.Collections;
using UnityEngine;
using ValueProperty;

/// <summary>
/// 圣诞树
/// </summary>
public class GunDrill : RGWeapon {
    public float force = 1;
    public int maxForce = 15;
    public bool lockRotation = false;
    GameObject the_bullet;
    Transform direct;
    private WeaponStateListener _weaponStateListener;
    protected override emWeaponType default_type {
        get {
            return emWeaponType.Spear;
        }
    }

    protected override void Awake() {
        base.Awake();
        direct = gun_point.Find("direct");
        _weaponStateListener = GetComponent<WeaponStateListener>();
    }

    protected override void Start() {
        base.Start();
    }

    public override void SetAttack(bool value1, bool manual = true) {
        if (controller) {
            if (value1) {
                if (activate && controller.attribute.energy >= consume) {
                    anim.SetBool("atk_b", value1);
                    if (_weaponStateListener != null) {
                        _weaponStateListener.OnWeaponAttackStart();
                    }
                } else {
                    UICanvas.GetInstance().ShowTextTalk(controller.transform, "...", 2.5f, 1f, 0);
                }
            } else if (!value1 && !_autoAttack) {
                EndAttack();
            }
        }
    }
    void Attack() {
        if (anim.GetBool("atk_b")) {
            StartAttack();
        }
    }
    void StartAttack() {
        if (the_bullet) {
            GameObject.Destroy(the_bullet);
        }
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        the_bullet = BulletFactory.TakeBullet(
            GetBulletInfo(),
            GetDamageInfo(),
            true, gun_point);
        if (lockRotation) {
            the_bullet.transform.localRotation = Quaternion.identity;
        }
        StopAllCoroutines();
        StartCoroutine(Dashing());
        BulletCreateEvent(the_bullet);
        the_bullet = latestCreatedBulletObject;
        AfterBulletCreateEvent();
        AfterAttackEvent();
    }

    void EndAttack() {
        if (the_bullet) {
            EndDrills();
        }

        if (anim) {
            anim.SetBool("atk_b", false);
        }
        
        if (_weaponStateListener != null) {
            _weaponStateListener.OnWeaponAttackFinished();
        }
    }
    float frictionOffset;
    IEnumerator Dashing() {
        if (force > 0) {
            yield return new WaitForSeconds(0.1f);
            activate = false;
            // frictionOffset = 1 - controller.GetFriction();
            // controller.SetFriction(1);
            var extraValue = 1 - controller.GetOriginalFriction();
            controller.ModifyFriction(AdditionalType.Add, extraValue, "gunDrill");
            while (this && the_bullet) {
                if (need_lock) {
                    controller.AddForce(transform.right, force, maxForce);
                } else if (direct) {
                    if (controller.facing < 0)
                        direct.transform.eulerAngles = new Vector3(0, 0, 180 - controller.FixedAngle);
                    else
                        direct.transform.eulerAngles = new Vector3(0, 0, controller.FixedAngle);
                    controller.AddForce(direct.right, force, maxForce);
                    //controller.AddForce((controller as RGController).move_dir, force, maxForce);
                }
                yield return null;
            }
            yield return new WaitForSeconds(0.1f);
            extraValue /= 2f;
            controller.UnModifyFriction("gunDrill");
            controller.ModifyFriction(AdditionalType.Add, extraValue, "gunDrill");
            // controller.SetFriction(controller.GetFriction() - frictionOffset / 2f);
            // frictionOffset = frictionOffset / 2f;
            yield return new WaitForSeconds(0.1f);
            controller.UnModifyFriction("gunDrill");
            // controller.SetFriction(controller.GetFriction() - frictionOffset);
            // frictionOffset = 0;
            activate = true;
        }
    }

    public override void MakeConsume() {
        if (!controller) return;
        controller.attribute.RestoreEnergy(-consume);
        if (controller.attribute.energy < consume) {
            UICanvas.GetInstance().ShowTextTalk(controller.transform, "...", 2.5f, 1f, 0);
            EndAttack();
        }
    }

    public override void StartUseWeapon() {
        base.StartUseWeapon();
        for (int i = 0; i < gun_point.childCount; i++) {
            Destroy(gun_point.GetChild(i).gameObject);
        }
    }
    public override void StopWeapon() {
        base.StopWeapon();
        EndAttack();
        StopAllCoroutines();
        // controller.SetFriction(controller.GetFriction() - frictionOffset);
        // frictionOffset = 0;
        if (controller != null) {
            controller.UnModifyFriction("gunDrill");
        }
        activate = true;
    }
    void EndDrills() {
        foreach (var drill in gun_point.GetComponentsInChildren<RGDrill>(true)) {
            drill.EndDrill();
        }
        the_bullet = null;
    }
    public override bool IsHold() {
        return true;
    }

    public override void OnReborn() {
        base.OnReborn();
        // controller.SetFriction(controller.GetFriction() - frictionOffset);
        // frictionOffset = 0;
        controller.UnModifyFriction("gunDrill");
    }
    
    

}
