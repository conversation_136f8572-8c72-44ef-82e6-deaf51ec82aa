using System;
using System.Collections.Generic;
using Sirenix.OdinInspector;
using UnityEngine;

[RequireComponent(typeof(RGWeapon))]
public class GeneralWeaponFusionTarget : SerializedMonoBehaviour, IWeaponFusionTarget, IWeaponDataTags {
    [DictionaryDrawerSettings(KeyLabel = "武器名")]
    public Dictionary<string, FusionModel> fusionModels;
    public HSVShader[] sprites;
    public Shader hsvShader;
    public string shaderHueShiftField;
    public string weaponDataPrefix;
    private RGWeapon _weapon;
    private int _shaderHueShiftFieldId;
    private string _sourceWeaponName;
    private void Awake() {
        _weapon = GetComponent<RGWeapon>();
        _shaderHueShiftFieldId = Shader.PropertyToID(shaderHueShiftField);
        _weapon.OnFlushIcon += FlushUIMaterial;
        _weapon.OnCopyFrom += CopyFromWeapon;
    }

    private void CopyFromWeapon(RGWeapon obj) {
        var sourceChimotoFusion = obj.GetComponent<GeneralWeaponFusionTarget>();
        if (sourceChimotoFusion == null) {
            return;
        } 
        FusionFrom(sourceChimotoFusion._sourceWeaponName);
    }

    private void FlushUIMaterial() {
        if (_sourceWeaponName == null) {
            return;
        }
        var material = new Material(hsvShader);
        material.SetFloat(_shaderHueShiftFieldId, fusionModels[_sourceWeaponName].hueShift);
        UICanvas.GetInstance().ChangeWeaponIconImageMaterial(material);
    }

    public void FusionFrom(params string[] sourceWeaponNames) {
        _sourceWeaponName = null;
        foreach (var sourceWeaponName in sourceWeaponNames) {
            if (fusionModels.NotNullAndContainsKey(sourceWeaponName)) {
                _sourceWeaponName = sourceWeaponName;
                break;
            }
        }
        if (_sourceWeaponName == null) {
            return;
        }
        var model = fusionModels[_sourceWeaponName];
        for (var i = 0; i < _weapon.bulletsInfo.Count; i++) {
            _weapon.bulletsInfo[i].bulletProto = model.bullets[i];
        }
        foreach (var modelSprite in sprites) {
            modelSprite.hueShift = model.hueShift;
        }
    }

    [Serializable]
    public class FusionModel {
        public GameObject[] bullets;
        public float hueShift;
    }

    public List<string> CustomTags => string.IsNullOrEmpty(_sourceWeaponName) ? new List<string>() : new List<string>() {$"{weaponDataPrefix}_{_sourceWeaponName}"};
    public void HandleTags(List<string> customTags) {
        string weaponName = null;
        if(customTags == null) return;
        foreach (var customTag in customTags) {
            if (customTag.StartsWith($"{weaponDataPrefix}_")) {
                weaponName = customTag.Substring(weaponDataPrefix.Length + 1);
                break;
            }
        }
        if (weaponName == null) {
            return;
        }
        FusionFrom(weaponName);
    }
}
