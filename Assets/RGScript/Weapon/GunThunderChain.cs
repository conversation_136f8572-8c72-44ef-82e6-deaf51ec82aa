using System;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 闪电链武器
/// </summary>
public class GunThunderChain : RGWeapon, WeaponInterface {
    public int maxTarget = 3;

    public void Attack() {
        if (!controller.has_target) {
            UICanvas.GetInstance().ShowTextTalk(controller.transform, "...", 3f, 1f, 0);
            return;
        }
        
        CreateBullet();
        MakeConsume();
        AfterBulletCreateEvent();
        AfterAttackEvent();
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
    }

    public virtual void CreateBullet() {
        var theBulletInfo = GetBulletInfo().SetSourceWeapon(this);
        var damgeInfo = GetDamageInfo();
        if (ExternalSourceObject != null) {
            theBulletInfo.SetSourceObject(ExternalSourceObject);
        }

        var temp_obj = BulletFactory.TakeBullet(theBulletInfo,damgeInfo);
        var bulletThunder = temp_obj.GetComponent<BulletThunder>();
        bulletThunder.source_object = controller.gameObject;
        bulletThunder.max_count = maxTarget;
        bulletThunder.StartThunder(false, controller.target_obj.transform, damgeInfo.damage);
        BulletCreateEvent(temp_obj);
        GameUtil.CameraShake(1);
    }
}