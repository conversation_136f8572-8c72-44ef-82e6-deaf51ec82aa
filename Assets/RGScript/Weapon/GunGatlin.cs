using System.Collections;
using UnityEngine;

namespace RGScript.Weapon {
    public class GunGatlin : RGWeapon, WeaponInterface, IWeaponSpecial {
        public int multiCount = 3; //散弹子弹数
        public float speed_correction;

        [SerializeField] private SpriteRenderer heatIndicator; // 需要在Inspector中赋值

        public bool IsSpecial => IsEvolvedWeapon;
        private Coroutine _powerAttackCoroutine; // 添加协程引用用于取消

        public int SpecialMode => -1;
        private bool _isAttacking;

        // Heat system variables
        private float _heatValue;
        public float heatIncreasePerShot = 5f;
        private bool _isOverheated;
        public float maxHeat = 100f;
        public float delay = 0.1f;
        public float duration = 1;

        protected override emWeaponType default_type => emWeaponType.ShotGun;

        public void WeaponSpecial(bool isDown) {
            if (!IsEvolvedWeapon) return;
            if (!isDown) return;
            if (!_isOverheated) return;

            _powerAttackCoroutine = StartCoroutine(PowerAttackSequence());
            _isOverheated = false;
            SetHeatValue(0);
        }

        public override void SetWeaponFront(Transform parent, int targetLayer) {
            base.SetWeaponFront(parent, targetLayer);
            SimpleEventManager.AddEventListener<ShowSpecialButtonEvent>(UpdateHeatValueHandler);
            UpdateHeatValueHandler(new ShowSpecialButtonEvent());
        }

        public override void SetWeaponBack(Transform parent, bool temporarily, bool asFirstSibling) {
            SimpleEventManager.Raise(new SpecialBtnGrayEvent(false));
            SimpleEventManager.Raise(new SpecialBtnProgressEvent(0f));
            SimpleEventManager.RemoveEventListener<ShowSpecialButtonEvent>(UpdateHeatValueHandler);
            if (_powerAttackCoroutine != null) {
                StopCoroutine(_powerAttackCoroutine);
                _powerAttackCoroutine = null;
                _isAttacking = false;
            }
            base.SetWeaponBack(parent, temporarily, asFirstSibling);
        }

        public override void DropWeapon(int targetLayer) {
            base.DropWeapon(targetLayer);
            SimpleEventManager.RemoveEventListener<ShowSpecialButtonEvent>(UpdateHeatValueHandler);
        }

        protected override void OnDisable() {
            base.OnDisable();
            SimpleEventManager.Raise(new SpecialBtnGrayEvent(false));
            SimpleEventManager.Raise(new SpecialBtnProgressEvent(0f));
            SimpleEventManager.RemoveEventListener<ShowSpecialButtonEvent>(UpdateHeatValueHandler);
            StopAllCoroutines();
            _powerAttackCoroutine = null;
            _isAttacking = false;
        }

        private void UpdateHeatValueHandler(ShowSpecialButtonEvent e) {
            StartCoroutine(DelayUpdateHeatValue());
        }

        private IEnumerator DelayUpdateHeatValue() {
            //ensure order when multiple weapons have progress bar on specialBtn
            yield return new WaitForEndOfFrame();
            SetHeatValue(_heatValue);
        }

        private IEnumerator PowerAttackSequence() {
            _isAttacking = true;
            int totalShots = Mathf.FloorToInt(duration / delay);

            for (int shot = 0; shot < totalShots; shot++) {
                for (int i = 0; i < multiCount; i++) {
                    CreateBullet(1);
                    GameUtil.CameraShake(2);
                }

                RGMusicManager.GetInstance().PlayEffect(audio_clip);
                AfterBulletCreateEvent();
                AfterAttackEvent();
                yield return new WaitForSeconds(delay);
            }

            _isAttacking = false;
            _powerAttackCoroutine = null;
        }

        public void Attack() {
            if (_isAttacking) {
                return;
            }

            _isAttacking = true;

            for (int i = 0; i < multiCount; i++) {
                CreateBullet();
            }

            MakeConsume();
            RGMusicManager.GetInstance().PlayEffect(audio_clip);
            AfterBulletCreateEvent();
            AfterAttackEvent();

            SetHeatValue(_heatValue + heatIncreasePerShot);

            if (_heatValue >= maxHeat) {
                TriggerOverheatEffect();
            }

            _isAttacking = false;
        }

        public override void AttackStop() {
            base.AttackStop();
            if (_powerAttackCoroutine == null) {
                _isAttacking = false;
            }
        }

        private void CreateBullet(int bulletIndex = 0) {
            float tempSpeed = bullet_speed + rg_random.Range(-speed_correction, speed_correction);
            var tempObj = BulletFactory.TakeBullet(
                GetBulletInfo(bulletIndex).SetSpeed(tempSpeed),
                GetDamageInfo(bulletIndex)
            );
            BulletCreateEvent(tempObj);
        }

        private void TriggerOverheatEffect() {
            if (!IsEvolvedWeapon) return;
            _isOverheated = true;
        }

        private void SetHeatValue(float newValue) {
            if (!IsEvolvedWeapon) return;
            if (!controller.TryGetComponent(out RGController _)) return;//宠物或者分身不许蓄力
            if (!transform.parent.name.Equals("h1")) return;//骑士三只手
            _heatValue = Mathf.Clamp(newValue, 0, maxHeat);
            SimpleEventManager.Raise(new SpecialBtnProgressEvent(_heatValue / maxHeat));
            SimpleEventManager.Raise(new SpecialBtnGrayEvent(_heatValue < maxHeat));
        }
    }
}