using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 斧头
/// </summary>
public class GunAxe : GunHoldSword {
    public GameObject swordBullet;
    public int a_atk_times = 1;
    public int continuousCount = 3;
    protected int atk_times;
    bool back_state {
        set {
            anim.SetBool("back", value);
        }
    }

    protected int attackLastBulletFacing;
    protected int attack1LastBulletFacing;

    protected override void Start() {
        base.Start();
        if (weapon_type == emWeaponType.Default) {
            weapon_type = emWeaponType.Axe;
        }
        a_atk_times += RGScript.Battle.BuffStackCfgMasterContinuous.GetParam(GameUtil.GetBattleData(controller), 1).addCount;
    }

    public override void SetAttack(bool value1, bool manual = true) {
        if (controller) {
            if (activate) {
                if (value1) {
                    AttackKeyDown(manual);
                } else if (!value1) {
                    AttackKeyUp(manual);
                }
            } else {
                //UICanvas.GetInstance().ShowTextTalk(controller.transform, "...", 2.5f, 1f, 0);
            }
            if (manual) {
                attackPressed = value1;
            }
        }
    }

    public override void AttackStop() {
        base.AttackStop();
        OnAtk();
        if (hold) {
            Destroy(the_reload_obj);
            hold = false;
        }
        activate = false;
    }

    protected override void OnAtk() {
        SimpleEventManager.Raise(ChargeAttackStartEvent.UseCache(controller, this, Mathf.Clamp01(a_time / max_time)));
        atk_times = Mathf.FloorToInt(a_atk_times * (a_time / max_time));
        if (atk_times > 0) {
            need_lock = false;
            transform.localRotation = Quaternion.identity;
        }
        anim.SetInteger("atk_times", atk_times);
        anim.SetTrigger("atk_t");
        a_time = 0;
    }
    /// <summary>
    /// 创建剑刀光
    /// </summary>
    public new void Attack() {
        var temp_obj = BulletFactory.TakeBullet(
            GetBulletInfo(1).SetPosition(transform.parent.position),
            GetDamageInfo(1)
            );
        if (controller != null) {
            attackLastBulletFacing = controller.facing;
        }
        temp_obj.transform.localScale = new Vector3(attackLastBulletFacing > 0 ? 1 : -1, 1, 1);
        RGMusicManager.GetInstance().PlayEffect(audio_clip);
        TurnActivate();
        BulletCreateEvent(temp_obj);
        AfterBulletCreateEvent();
        AfterAttackEvent();
    }
    /// <summary>
    /// 创建斧头刀光
    /// </summary>
    public virtual void Attack2() {
        if (atk_times > 0) {
            atk_times--;
            anim.SetInteger("atk_times", atk_times);
            var temp_obj = BulletFactory.TakeBullet(
                GetBulletInfo().SetPosition(controller.transform.position),
                GetDamageInfo(),
                true,
                controller ? controller.transform : null
                );
            if (controller != null) {
                attack1LastBulletFacing = controller.facing;
            }
            temp_obj.transform.localScale = new Vector3(attack1LastBulletFacing > 0 ? 1 : -1, 1, 1);
            //temp_obj.GetComponent<RGSword>().GetComponent<Animator>().speed = anim.speed < 1 ? anim.speed : 1;
            RGMusicManager.GetInstance().PlayEffect(audio_clip);
            BulletCreateEvent(temp_obj);
            if (atk_times == 0) {
                AttackEnd();
            }
        } else {
            AttackEnd();
        }
    }

    public virtual void AttackEnd() {
        AfterBulletCreateEvent();
        AfterAttackEvent();
        need_lock = true;
        Invoke("TurnActivate", 0.1f);
        SimpleEventManager.Raise(ChargeAttackEndEvent.UseCache(controller, this));
    }

    public override void StopWeapon() {
        if (atk_times > 0) {
            SimpleEventManager.Raise(ChargeAttackEndEvent.UseCache(controller, this));
        }
        need_lock = true;
        atk_times = 0;
        anim.SetInteger("atk_times", atk_times);
        base.StopWeapon();
    }

    public override void SetWeaponBack(Transform parent) {
        base.SetWeaponBack(parent);
        back_state = true;
    }

    public override void SetWeaponFront(Transform parent, int target_layer) {
        base.SetWeaponFront(parent, target_layer);
        back_state = false;
    }
}