using cfg.task;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace TaskSystem {
    public class BeginnerTask : TaskBase {
        private TaskBeginnerConfig _config;
        public TaskBeginnerConfig Config => _config;
        public string LocalizationKey => _config.LocalizationKey;
        public List<int> NextTaskIDList { get; private set; }
        public emBeginnerTaskType BeginnerTaskType  { get; private set; }
        public override bool CanGetReward {
            get {
                return !HasGotAllReward && 
                       HasGotRewardDict.Any(kv => kv.Value == false && Progress.x >= kv.Key);
            }
        }

        public override void InitByData(TaskData data) {
            Data = data;
            _config = TaskFactory.Instance.GetBeginnerTaskConfig(ID);
            
            InitBeginnerTask();
        }

        public override void InitByConfig(object config) {
            _config = (TaskBeginnerConfig)config;
            
            int progressMax = _config.Checkers.Sum(
                checkerInfos => checkerInfos.Value.Sum(
                    checkerInfo => checkerInfo.Value));

            Dictionary<int, bool> hasGotReward = _config.FinishRewards.ToDictionary(
                rewardsInfo => rewardsInfo.Key, rewardsInfo => false);
            
            Data = new TaskData {
                ID = _config.Id,
                Status = TaskStatus.Ready.ToString(),
                TaskType = TaskTypes.Beginner.ToString(),
                CurrentProgress = 0,
                TargetProgress = progressMax,
                HasGotReward = hasGotReward,
                ProgressCacheInfos = new Dictionary<string, Dictionary<string, int>>(),
            };
            
            InitBeginnerTask();
            StartTask();
        }

        private void InitBeginnerTask() {
            CheckInfos = _config.Checkers;
            Rewards = _config.FinishRewards;
            
            NextTaskIDList = new List<int>();
            BeginnerTaskType = (emBeginnerTaskType)_config.MissionType;
            foreach (var id in TaskFactory.Instance.BeginnerTaskDict[BeginnerTaskType]) {
                var taskConfig = TaskFactory.Instance.GetBeginnerTaskConfig(id);
                if (taskConfig.PreTaskID.Contains(ID)) {
                    NextTaskIDList.Add(taskConfig.Id);
                }
            }
        }

        public override void OnTaskCompleteHandler() {
            SimpleEventManager.Raise(new BeginnerTaskCompleteEvent() {
                taskID = ID,
            });
            
            if (NextTaskIDList == null || NextTaskIDList.Count == 0) {
                return;
            }
            //
            // foreach (var nextTaskID in NextTaskIDList) {
            //     TaskBase nextTask = TaskFactory.Instance.InitTask<BeginnerTask>(nextTaskID);
            //     TaskManager.Instance.AddNewTask(nextTask);
            // }
        }
        
        public override void OnGetRewardHandler(List<IRewardable> rewardList) {
            if (Data.HasGotReward.All((x) => x.Value) && CurrentProgress == TargetProgress) {
                Data.Status = TaskStatus.End.ToString();
            }
            //目前只有一种奖励，上传字符串
            TAUtil.Track("beginner_task_get_reward", new Dictionary<string, object> {
                { "beginner_task_reward_id", rewardList[0].GetName()},
                { "beginner_task_type",TaskManager.Instance.GetBeginnerTaskTypeName(_config) },
            });
        }
        
        public override void Check() {
            CantUpdate = BeginnerTaskType == emBeginnerTaskType.InGame && !DataUtil.IsGameScene();
            base.Check();
        }
    }
}