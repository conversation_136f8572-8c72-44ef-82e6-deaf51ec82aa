using cfg.task;
using RGScript.Data;
using System;
using UnityEngine;

namespace TaskSystem {
    public partial class TaskFactory {
        public TbTaskMissionH5Config ReturnPlayerH5TaskConfigTb =>
            DataMgr.ConfigData.Tables.TbTaskMissionH5Config;

        public TaskMissionH5Config GetReturnPlayerH5TaskConfig(int taskID) {
            TaskMissionH5Config config = ReturnPlayerH5TaskConfigTb.GetOrDefault(taskID);
            if (config != null) {
                return config;
            }

            Debug.LogError($"TaskSystem>TaskFactory>Error>任务:<{taskID}>在表格<ReturnPlayerH5TaskConfigTb>中不存在!");
            return null;
        }

        public ReturnPlayerH5Task InitReturnPlayerH5Task(Type taskType, TaskData data) {
            var output = (ReturnPlayerH5Task)Activator.CreateInstance(taskType);
            output.InitByData(data);
            return output;
        }
    }
}