using cfg.talk;
using I2.Loc;
using RGScript.Data;
using RGScript.Util.MathTool;
using System.Collections.Generic;
using TalkSystem;
using UnityEngine;
using Random = UnityEngine.Random;


public class CharacterTalkTrigger : MonoBehaviour, ItemInterface {
    [SerializeField] private string npcName = "npc";
    [SerializeField] private float height = 1.6f;
    [HideInInspector] public bool isMahJongTable;
    
    private TbCharacterTalkConfig _characterTalkConfigTb; 
    private RGController _other;
    private RGController _self;
    private emHero _selfHero;
    
    private readonly List<int> _skinTalks = new ();
    private const int PriestMonkSkinIndex = 15;
    private const int SkinMaxTalkNumber = 999;
    private int _selfSkinIndex;
    private int _talkIndex;
    

    private void Start() {
        GetSkinAllTalk();
        _talkIndex = 0;
    }

    private void GetSkinAllTalk() {
        _self = GetComponentInParent<RGController>();
        _selfHero = _self.GetHeroType();
        _selfSkinIndex = _self.GetSkinIndex();
        var otherHeroId = (int)_self.GetHeroType() + 1;
        var otherSkinId = _self.GetSkinIndex() + 1;
        var talkStartId = otherHeroId * 100000 + otherSkinId * 1000;
        var talkDefaultId = otherHeroId * 100000;

        _characterTalkConfigTb = DataMgr.ConfigData.Tables.TbCharacterTalkConfig;
        int talkId = _characterTalkConfigTb.DataMap.ContainsKey(talkStartId) ? talkStartId : talkDefaultId;
        int talksLength = talkId + SkinMaxTalkNumber;

        for (int i = talkId; i < talksLength + 1; i++) {
            if (!_characterTalkConfigTb.DataMap.TryGetValue(talkId, out CharacterTalkConfig config)) {
                break;
            }

            if (config is { IsSkinTalksEnd: false }) {
                _skinTalks.Add(talkId);
                talkId++;
            } else {
                _skinTalks.Add(talkId);
                break;
            }
        }
    }

    private void FilterTalksByCondition() {
        if (_skinTalks.Count == 0) {
            return;
        }

        for (int i = _skinTalks.Count - 1; i >= 0; i--) {
            string filter = "";
            if (_characterTalkConfigTb.DataMap.TryGetValue(_skinTalks[i], out CharacterTalkConfig config)) {
                filter = config.Checkers;
            }

            if (filter.StartsWith("Special")) {
                var splitChecker = filter.Split("_");
                switch (splitChecker.Length)
                {
                    case 3:
                    {
                        var characterIndex = filter.Split("_")[1].Replace("C", "").ToInt();
                        var skinIndex = filter.Split("_")[2].Replace("S", "").ToInt();
                        if (_other.GetHeroType().ToInt().Equals(characterIndex) && _other.GetSkinIndex().Equals(skinIndex)) {
                            continue;
                        }

                        _skinTalks.Remove(_skinTalks[i]);
                        continue;
                    }
                    case 2:
                    {
                        var characterIndex = filter.Split("_")[1].Replace("C", "").ToInt();
                        if (_other.GetHeroType().ToInt().Equals(characterIndex)) {
                            continue;
                        }

                        _skinTalks.Remove(_skinTalks[i]);
                        continue;
                    }
                    default:
                        _skinTalks.Remove(_skinTalks[i]);
                        continue;
                }
            }


            switch (filter) {
                case "TalkWithoutTangMonk": {
                    if (DataUtil.GetSkinUnlock((emHero.Priest), PriestMonkSkinIndex)) {
                        _skinTalks.Remove(_skinTalks[i]);
                    }

                    break;
                }
                case "TalkOnMahJong": {
                    if (!isMahJongTable) {
                        _skinTalks.Remove(_skinTalks[i]);
                    }

                    break;
                }
                case "TalkMeetAssassin": {
                    if (!_other || _other.GetHeroType() != emHero.Assassin) {
                        _skinTalks.Remove(_skinTalks[i]);
                    }

                    break;
                }
            }
        }
    }

    private void ShowTalkOnTrigger() {
        FilterTalksByCondition();
        _characterTalkConfigTb.DataMap.TryGetValue(_skinTalks[0], out CharacterTalkConfig config);
        var currentIndex = _talkIndex % _skinTalks.Count;
        var randomIndex = Random.Range(0, _skinTalks.Count);

        var talkId = _skinTalks[config is { IsLoopTalk: true } ? currentIndex : randomIndex];
        var key = CharacterTalkManager.Instance.GetTextKey(talkId);

        if (CheckEmoticonTalk(key)) {
            return;
        }

        var finalKey = key.Replace("\n", "").Replace("\r", "").Replace("\t", "");
        var talkTime = CharacterTalkManager.Instance.GetTalkTime(talkId);
        string talkContent = ScriptLocalization.Get(finalKey, $"本地化key{finalKey}");
        UICanvas.GetInstance().ShowTextTalk(transform.parent, talkContent, height + 0.4f, talkTime);
        _talkIndex++;
        CheckOtherTalk();
        SimpleEventManager.Raise(new HallHeroTalkEvent(_selfHero));
    }

    //只发送表情, 没有对话
    private bool CheckEmoticonTalk(string key) {
        if (_selfHero == emHero.Taoist && _selfSkinIndex == 14 && key == "EmoticonTip") {
            EmoticonTips.ShowEmoticonTips(
                _self.transform, 35, _self.facing, new Vector3(1, 2, 0));
            return true;
        }

        return false;
    }

    //道士-波子特殊对话
    private void CheckOtherTalk() {
        if (BattleData.data.playerIndex == (int)emHero.Taoist && BattleData.data.skinIndex == 14) {
            var playerController = RGGameSceneManager.Inst.controller;
            EmoticonTips.ShowEmoticonTips(
                playerController.transform, 35, playerController.facing, new Vector3(1.5f, 1.5f, 0));
        }
    }

    void OnTriggerEnter2D(Collider2D other) {
        if (!other.gameObject.CompareTag("Body_P")) {
            return;
        }

        var rgController = other.GetComponent<RGController>();
        if (!rgController.IsLocalPlayer()) {
            return;
        }

        _other = rgController;
        rgController.SetItemtf(transform);
        Vector3 temp3 = transform.position;
        temp3.y += height;
        UICanvas.GetInstance().ShowObjectInfo(temp3, ScriptLocalization.Get(npcName), 0);
    }

    void OnTriggerExit2D(Collider2D other) {
        if (!other.gameObject.CompareTag("Body_P")) {
            return;
        }

        var rgController = other.GetComponent<RGController>();
        if (!rgController.IsLocalPlayer()) {
            return;
        }

        if (!rgController.CompareItem(transform)) {
            return;
        }

        rgController.SetItemtf(null);
        if (UICanvas.GetInstance() != null) {
            UICanvas.GetInstance().HideObjectInfo();
        }
    }

    public void ItemTrigger(RGController controller) {
        _other = controller;
        if (controller.IsLocalPlayer()) {
            ShowTalkOnTrigger();
        }

        RGMusicManager.GetInstance().PlayEffect(10);
        UICanvas.GetInstance().HideObjectInfo();
        controller.SetItemtf(null);
    }

    public void SyncItemTrigger(RGController controller, string extraInfo = "") { }

    public bool CanUse() {
        return true;
    }

    public int GetItemValue() {
        return 0;
    }

    public int GetItemLevel() {
        return 0;
    }

    public string GetItemName() {
        return "name";
    }
}