using System;
using UnityEngine;

namespace BattleSystem.Strategy {
    public class NearestEnemyIgnoreObstacleTargetingNoOptimizeStrategy : ITargetingStrategy {
        private AIController _aiController;
        private RaycastHit2D[] _detectList;
        private readonly RaycastHit2D[] _linecastCache = new RaycastHit2D[8];
        private readonly Func<Transform, bool> filter = _ => true;
        private int Masks => _aiController.Camp == 0 ? LayerMask.GetMask("Body_P") : LayerMask.GetMask("Body_E");

        public void Init(AIController aiController) {
            _aiController = aiController;
        }

        public bool CanExecute() {
            return true;
        }

        public Transform Execute() {
            return Targeting.FindNearestTarget(_aiController.BodyTransform, _aiController.AIAttributeConfig.FindTargetRange, Masks, filter, false, _detectList, _linecastCache, false);
        }
    }
}