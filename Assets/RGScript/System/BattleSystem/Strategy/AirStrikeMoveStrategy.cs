using UnityEngine;

namespace BattleSystem.Strategy {
    public class AirStrikeMoveStrategy : IMoveStrategy {
        private AIController _aiController;
        private Vector2 SelfPosition => _aiController.BodyWorldPosition;
        private const float TargetYOffset = 5f;
        private const float TargetYRange = 7f;
        private const float TargetXOffset = 20f;
        private bool _above;
        private Vector2 MoveIntention { get; set; }
        private Vector2 TargetPosition { get; set; }
        public void Init(AIController aiController) {
            _aiController = aiController;
            _above = _aiController.RGRandom.Range(0f, 1f) > 0.5f;
        }

        public bool CanExecute() {
            return true;
        }

        public bool IsAchieved() {
            var xDistance = TargetPosition.x - SelfPosition.x;
            var away = _aiController.Facing * xDistance < 0;
            return away && Mathf.Abs(xDistance) > TargetXOffset;
        }

        public void Finish() {
            _above = _aiController.RGRandom.Range(0f, 1f) > 0.5f;
        }

        public Vector2 Execute(Vector2 targetPosition) {
            TargetPosition = targetPosition;
            MoveIntention = (_aiController.Facing > 0 ? Vector2.right : Vector2.left) + SelfPosition;
            var yOffset = (_above ? 1 : -1) * TargetYOffset;
            var ySync = ((Vector2)TargetPosition - new Vector2(SelfPosition.x, SelfPosition.y + yOffset)).normalized.y;
            ySync = Mathf.Clamp(ySync, -TargetYRange, TargetYRange);
            MoveIntention += new Vector2(0, ySync);
            return MoveIntention;
        }
    }
}