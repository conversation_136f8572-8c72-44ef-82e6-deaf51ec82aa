using System.Collections.Generic;
using UnityEngine;

namespace BattleSystem.Strategy {
    public enum MovementType { Static, Random, CloseToTarget, CloseToTargetRandom, AwayFromTarget, KeepDistanceFromTarget, KeepDistanceFromTargetRandom}
    public static class MovementStrategies {
        private delegate Vector2 MovementStrategy(Vector3 selfPos, Vector3 targetPos, RGRandom random);
        private static Dictionary<MovementType, MovementStrategy> _movementStrategyDict =
            new Dictionary<MovementType, MovementStrategy> {
                {MovementType.Static, Static},
                {MovementType.Random, Random},
                {MovementType.CloseToTarget, CloseToTarget},
                {MovementType.CloseToTargetRandom, CloseToTargetRandom},
                {MovementType.AwayFromTarget,AwayFromTarget},
                {MovementType.KeepDistanceFromTarget, KeepDistanceFromTarget},
                {MovementType.KeepDistanceFromTargetRandom, KeepDistanceFromTargetRandom}
            };
        
        public static Vector2 GetMoveDirection(MovementType strategyType, Vector3 selfPos, Vector3 targetPos, RGRandom random) {
            if (_movementStrategyDict.TryGetValue(strategyType, out MovementStrategy theStrategy)) {
                return theStrategy(selfPos, targetPos, random);
            }

            Debug.LogError($"BattleSystem>MovementStrategies>不存在此移动策略:{strategyType}");
            return Vector2.zero;
        }
        
        private static Vector2 Static(Vector3 selfPos, Vector3 targetPos, RGRandom random) 
            =>Vector2.zero;

        private static Vector2 Random(Vector3 selfPos, Vector3 targetPos, RGRandom random) 
            => new Vector2(random.Range(-1f, 1f), random.Range(-1f, 1f)).normalized;
        

        private static Vector2 CloseToTarget(Vector3 selfPos, Vector3 targetPos, RGRandom random) {
            Vector2 output = ((Vector2)targetPos - (Vector2)selfPos).normalized;
            return output;
        }

        private static Vector2 CloseToTargetRandom(Vector3 selfPos, Vector3 targetPos, RGRandom random) {
            int randomInt = random.Range(0, 10);
            Vector2 output = ((Vector2)targetPos - (Vector2)selfPos).normalized;
            if (randomInt >= 2) return output;
            output = randomInt > 0 ? new Vector2(-output.y, output.x) : new Vector2(output.y, -output.x);
            return output;
        }

        private static Vector2 AwayFromTarget(Vector3 selfPos, Vector3 targetPos, RGRandom random) {
            Vector2 output = ((Vector2)targetPos - (Vector2)selfPos).normalized;
            return -output;
        }

        private const float DefaultDistance = 5f;
        public static float Distance;
        private static Vector2 KeepDistanceFromTarget(Vector3 selfPos, Vector3 targetPos, RGRandom random) {
            float distance = Vector2.Distance(selfPos, targetPos);
            Vector2 output = distance > Distance
                ? CloseToTarget(selfPos, targetPos, random)
                : AwayFromTarget(selfPos, targetPos, random);
            Distance = DefaultDistance;
            return output;
        }

        private static Vector2 KeepDistanceFromTargetRandom(Vector3 selfPos, Vector3 targetPos, RGRandom random) {
            float distance = Vector2.Distance(selfPos, targetPos);
            int randomInt = random.Range(0, 10);
            Vector2 output = ((Vector2)targetPos - (Vector2)selfPos).normalized;
            if (randomInt > 5) {
                output = randomInt < 8 ? new Vector2(-output.y, output.x) : new Vector2(output.y, -output.x);
            } else if (randomInt + 5 > distance) {
                output = -output;
            }
            Distance = DefaultDistance;
            return output;
        }
    }
}
