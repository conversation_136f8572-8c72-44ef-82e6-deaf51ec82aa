using DG.Tweening;
using EnemyGenerator;
using RGScript.DifficultyLevel;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

// ReSharper disable CheckNamespace
namespace BattleSystem {
    public class BossVoidBrain : AIBrain {
        private Vector3 _gatePos = new Vector3(3, 1, 0);
        private Transform _gate;
        private int _form; //boss形态
        private BossVoid _bossVoid;
        protected int EscapeDropVoidCoinCount = 100;
        protected int DeadDropVoidCoinCount = 200;
        private EnemyMaker _enemyMaker;
        private void RegisterEvents() {
            SimpleEventManager.AddEventListener<ClearRoomEvent>(OnRoomClear);
            AIController.OnDead += OnDead;
        }

        private void UnregisterEvents() {
            SimpleEventManager.RemoveEventListener<ClearRoomEvent>(OnRoomClear);
            AIController.OnDead -= OnDead;
        }

        public bool IsFinalForm() {
            return _form >= 2;
        }

        public override void Init(AIController aiController) {
            base.Init(aiController);
            RegisterEvents();
            _gate = transform.Find("img/gate_slot/boss_void_gate");
            _bossVoid = aiController.BodyTransform.GetComponent<BossVoid>();
            _enemyMaker = _bossVoid.the_maker;
        }

        public int GetForm() {
            return _form;
        }

        public void SetForm(int form) {
            _form = form;
            if (!IsFinalForm()) {
                _bossVoid.CreateHpBar();
            }
        }

        private void OnRoomClear(ClearRoomEvent e) {
            if (!_enemyMaker) {
                return;
            }

            if (e.room != _enemyMaker.theRoom) {
                return;
            }

            if (!IsFinalForm()) {
                _bossVoid.Escape();
            }
        }

        private void OnDead() {
            StartCoroutine(HandleGate());
        }

        private IEnumerator HandleGate() {
            yield return new WaitForSeconds(0.8f);
            _gate.SetParent(transform.parent);
            var distance = Vector2.Distance(_gatePos, transform.localPosition);
            var speed = 3f;
            _gate.localEulerAngles = Vector3.zero;
            _gate.DOLocalMove(_gatePos, distance / speed).onComplete += () => {
                _gate.GetComponent<Collider2D>().enabled = true;
            };
        }

        private void OnDestroy() {
            UnregisterEvents();
        }

        public override AIDecision GetDecision() {
            return _bossVoid.escaping ? AIDecision.Idle : base.GetDecision();
        }
    }
}