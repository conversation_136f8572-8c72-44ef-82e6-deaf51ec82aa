using cfg.ComboGun;
using ModeSeason.ComboGun;
using RGScript.Character.Follower;
using RGScript.Data;
using RGScript.Util.TimerUtil;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using CGWeapon = ModeSeason.ComboGun.CGWeapon;

namespace BattleSystem.FollowerBrain {
    public class FollowerAIBrain : AIBrain {
        enum FollowerAIState {
            FollowMaster,
            AttackTarget,
        }

        public RGFollowerController FollowerController;
        private FollowerAIState _followerAIState;
        protected FollowerCreateInfo _createInfo;
        public FollowerCreateInfo CreateInfo => _createInfo;
        public CGFollowerAttribute FollowerAttribute { get; private set; }
        public int FollowerLevel => _createInfo.FollowerInfo.FollowerSaveData.level;
        public List<CGEffectData> effectDatas;


        public AudioClip SkillAudioClip;


        private TimerPool _talkTimerPool;
        private float _talkInterval = 10;
        private List<string> talkKeyList;
        private int talkIndex = -1;
        private float _talkHeight = 3.3f;
        private const float TalkRate = 30f;


        public override void Init(AIController aiController) {
            aiController.SelfAlwaysHostAI = true;
            base.Init(aiController);
            _followerAIState = FollowerAIState.FollowMaster;
            FollowerController = GetComponent<RGFollowerController>();
            SetParameter(AIBrainParameters.ApproachingTargetDeviation, 1f);


            SimpleEventManager.AddEventListener<ProjectileHitEvent>(OnProjectileHitEnemy);
            SimpleEventManager.AddEventListener<AmmoChangedEvent>(OnAmmoChanged);
            SimpleEventManager.AddEventListener<MobTakeDamageEvent>(OnMobTakeDamage);
            SimpleEventManager.AddEventListener<AddBuffEvent>(OnAddBuff);
            SimpleEventManager.AddEventListener<SkillBaseDoSkillEvent>(OnDoSkill);
            SimpleEventManager.AddEventListener<ReloadAmmoEvent>(OnReloadAmmoEvent);
            SimpleEventManager.AddEventListener<PlayerMoveEvent>(OnMoveEvent);
            SimpleEventManager.AddEventListener<BlockAttackStartupEvent>(OnBlockAttackStartupEvent);
            SimpleEventManager.AddEventListener<AfterBulletCreatedEvent>(OnAfterBulletCreatedEvent);
        }


        protected virtual void OnDestroy() {
            _talkTimerPool?.DestroyAllTimers();
            SimpleEventManager.RemoveListener<ProjectileHitEvent>(OnProjectileHitEnemy);
            SimpleEventManager.RemoveListener<AmmoChangedEvent>(OnAmmoChanged);
            SimpleEventManager.RemoveListener<MobTakeDamageEvent>(OnMobTakeDamage);
            SimpleEventManager.RemoveListener<AddBuffEvent>(OnAddBuff);
            SimpleEventManager.RemoveListener<SkillBaseDoSkillEvent>(OnDoSkill);
            SimpleEventManager.RemoveListener<ReloadAmmoEvent>(OnReloadAmmoEvent);
            SimpleEventManager.RemoveListener<PlayerMoveEvent>(OnMoveEvent);
            SimpleEventManager.RemoveListener<BlockAttackStartupEvent>(OnBlockAttackStartupEvent);
            SimpleEventManager.RemoveListener<AfterBulletCreatedEvent>(OnAfterBulletCreatedEvent);
        }


        public virtual void SetUpInfo(FollowerCreateInfo createInfo) {
            _createInfo = createInfo;
            this.FollowerAttribute =
                DataMgr.ConfigData.Tables.TbFollowerAttribute.Get(_createInfo.FollowerInfo.FollowerSaveData
                    .followerConfigId);
            ParserConfigParam(this.FollowerAttribute);
            effectDatas = FollowerAttribute.FollowerEntry.Select(CGFactory.GetEffectData).ToList();
            SetUpTalkData();
        }

        void SetUpTalkData() {
            var masterController = _createInfo.MasterTf.GetComponent<RGController>();
            if (null == masterController) {
                return;
            }

            if (!masterController.IsLocalPlayer()) {
                return;
            }
            
            this.talkKeyList = this.FollowerAttribute.Talk;
            var masterSkinIndex = masterController.GetSkinIndex();
            var masterHeroIndex = masterController.GetHeroType().ToInt();

            if (masterHeroIndex == this.FollowerAttribute.HeroIndex &&
                masterSkinIndex == this.FollowerAttribute.SkinIndex) {
                this.talkKeyList = this.FollowerAttribute.SpeTalk;
            }

            if (null == this.talkKeyList) {
                if(LogUtil.IsShowLog){LogUtil.LogError("this.talkKeyList is null");}
                return;
            }

            talkIndex = Random.Range(0, this.talkKeyList.Count);

            _talkTimerPool = new TimerPool();
            _talkTimerPool.Register(nameof(_talkTimerPool), _talkInterval, true, false, ShowNextTalk);
        }

        void ShowNextTalk() {
            if (null == this.talkKeyList) {
                return;
            }

            if (Random.Range(0, 100) > TalkRate) {
                return;
            }

            talkIndex = (talkIndex + 1) % talkKeyList.Count;
            ShowTalk(talkKeyList[talkIndex]);
        }

        void ShowTalk(string talkKey) {
            var talkContent = I2.Loc.ScriptLocalization.Get(talkKey);
            if (string.IsNullOrEmpty(talkContent)) {
                return;
            }

            UICanvas.GetInstance().ShowTextTalk(transform, talkContent, _talkHeight, 3f);
        }

        protected virtual void ParserConfigParam(CGFollowerAttribute cgFollowerAttribute) {
        }


        protected virtual void OnDoSkill(SkillBaseDoSkillEvent e) {
        }

        protected virtual void OnAddBuff(AddBuffEvent e) {
        }

        protected virtual void OnMobTakeDamage(MobTakeDamageEvent e) {
        }

        void OnProjectileHitEnemy(ProjectileHitEvent e) {
            if (null == e || null == e.proj || null == e.proj.context || null == e.proj.context.player ||
                null == e.target || !FollowerController || FollowerController.CreateInfo == null ||
                !FollowerController.CreateInfo.MasterTf || !FollowerController.CreateInfo.MasterTf.gameObject) {
                return;
            }

            var masterTfGameObject = FollowerController.CreateInfo.MasterTf.gameObject;
            if (e.proj.context.player.gameObject != masterTfGameObject) {
                return;
            }

            if (e.target is not CGMob mob) {
                return;
            }

            if (mob.Dead) {
                return;
            }

            if (null == FollowerController) {
                return;
            }

            var curPos = transform.position;
            var newTargetDistance = Vector2.Distance(mob.transform.position, curPos);

            if (newTargetDistance > RGFollowerController.MaxTargetRange) {
                return;
            }

            if (null != AIController.Target && AIController.Target.gameObject != masterTfGameObject) {
                var curTargetDistance =
                    Vector2.Distance(FollowerController.target_obj.transform.position, curPos);

                if (curTargetDistance < newTargetDistance) {
                    return;
                }
            }


            SetParameter(AIBrainParameters.MasterTargetCgMob, mob);
            SetTarget(mob.transform);
        }

        protected virtual void OnAmmoChanged(AmmoChangedEvent e) {
            if (!e.player || !FollowerController || FollowerController.CreateInfo == null ||
                !FollowerController.CreateInfo.MasterTf) {
                return;
            }

            if (e.player.gameObject != FollowerController.CreateInfo.MasterTf.gameObject) {
                return;
            }

            if (e.consumeCount <= 0) {
                return;
            }

            OnMasterConsumeAmmo(e.consumeCount, e.player);
        }

        protected virtual void OnReloadAmmoEvent(ReloadAmmoEvent e) {
            if (!e.player || !FollowerController || FollowerController.CreateInfo == null ||
                !FollowerController.CreateInfo.MasterTf) {
                return;
            }

            if (e.player.gameObject != FollowerController.CreateInfo.MasterTf.gameObject) {
                return;
            }

            OnMasterReloadAmmoEvent();
        }

        protected virtual void OnMoveEvent(PlayerMoveEvent e) {
            if (!e.controller || !FollowerController || FollowerController.CreateInfo == null ||
                !FollowerController.CreateInfo.MasterTf) {
                return;
            }

            if (e.controller.gameObject != FollowerController.CreateInfo.MasterTf.gameObject) {
                return;
            }

            OnPlayerMoveEvent();
        }

        protected virtual void OnMasterReloadAmmoEvent() {
        }
        
        protected virtual void OnPlayerMoveEvent() {
        }

        protected virtual void OnMasterConsumeAmmo(int consumeAmmoCount, RGController rgController) {
        }


        private void OnBlockAttackStartupEvent(BlockAttackStartupEvent e) {
            if (!e.RgController || !FollowerController || FollowerController.CreateInfo == null ||
                !FollowerController.CreateInfo.MasterTf) {
                return;
            }

            if (e.RgController.gameObject != FollowerController.CreateInfo.MasterTf.gameObject) {
                return;
            }

            this.OnMasterBlockAttackStartupEvent(e);
        }

        protected virtual void OnMasterBlockAttackStartupEvent(BlockAttackStartupEvent e) {
        }


        private void OnAfterBulletCreatedEvent(AfterBulletCreatedEvent e) {
            if (BattleData.data.IsComboGun) {
                return;
            }

            if (null == e.weapon || null == e.weapon.controller) {
                return;
            }

            if (e.weapon.controller.gameObject != FollowerController.CreateInfo.MasterTf.gameObject) {
                return;
            }

            OnAfterMasterBulletCreatedEvent(e);
        }

        protected virtual void OnAfterMasterBulletCreatedEvent(AfterBulletCreatedEvent e) {
        }


        public override AIDecision GetDecision() {
            if (BattleData.data.InComboGunGameScene) {
                TryGetParameter(AIBrainParameters.MasterTargetCgMob, out CGMob cgMob);
                if (null == cgMob || cgMob.Dead) {
                    _followerAIState = FollowerAIState.FollowMaster;
                }
            } else {
                TryGetParameter(AIBrainParameters.TargetNormalEnemy, out RGEController target);
                if (null == target || target.dead) {
                    _followerAIState = FollowerAIState.FollowMaster;
                }
            }

            if (FollowerController.followerState == RGFollowerController.FollowerState.Follow) {
                _followerAIState = FollowerAIState.FollowMaster;
            }

            AIDecision result = AIDecision.Idle;
            switch (_followerAIState) {
                case FollowerAIState.AttackTarget:
                    result = AIController.SkillManager.CanUseSkill() ? AIDecision.Skill : AIDecision.Idle;
                    break;
                case FollowerAIState.FollowMaster:
                    result = AIDecision.Move;
                    break;
            }

            return result;
        }


        private void Update() {
            if (null == AIController) {
                return;
            }

            if (!AIController.IsInit) {
                return;
            }

            CheckFollowDistanceAndTeleportToMasterPosition();
        }

        void CheckFollowDistanceAndTeleportToMasterPosition() {
            var createInfo = FollowerController.CreateInfo;
            if (!createInfo.MasterTf) {
                return;
            }
            
            var distance = Vector2.Distance(transform.position, createInfo.MasterTf.position);
            if (distance > RGFollowerController.FollowMasterMaxDistance) {
                transform.position = createInfo.MasterTf.position;
                FollowerController.target_obj = null;
                this.AIController.Target = _createInfo.MasterTf;
                SetParameter(AIBrainParameters.TargetNormalEnemy, null);
                SetParameter(AIBrainParameters.MasterTargetCgMob, null);
            }
        }

        protected void CopySelfWeaponBullet(int count = 1) {
            //亲卫自己的武器
            if (FollowerController is not {followerHand : {front_weapon: CGWeapon cgFollowerWeapon}}) {
                return;
            }

            for (int i = 0; i < count; i++) {
                foreach (var block in cgFollowerWeapon.Blocks) {
                    block.Attack(cgFollowerWeapon.NewContext());
                }
            }
        }

        public void SetTarget(Transform target) {
            if (target != null) {
                _followerAIState = FollowerAIState.AttackTarget;
            } else {
                _followerAIState = FollowerAIState.FollowMaster;
                if (FollowerController != null) {
                    target = FollowerController.CreateInfo.MasterTf;
                }
            }

            if (null != FollowerController) {
                FollowerController.target_obj = target;
                AIController.Target = target;
            }
        }
    }
}