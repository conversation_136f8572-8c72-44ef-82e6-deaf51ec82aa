using BattleSystem.Skill;
using HutongGames.PlayMaker;
using UnityEngine;

namespace BattleSystem.AIFsmAction {
    public class SkillFsmAction : FsmStateAction {
        private AIController _aiController;
        private bool NeedFixedRotation => _skill.Data.NeedFixedRotation;
        private SkillBase _skill;
        private Timer _skillStartTimer;
        private bool _skillStart;
        private GameObject _startUpProto;
        public override void OnPreprocess() {
            Fsm.HandleFixedUpdate = true;
        }

        public override void Awake() {
            _startUpProto = ResourcesUtil.Load<GameObject>("RGPrefab/AI/ai_skill_start_up.prefab");
        }
        /// <summary>
        /// 在这里决策要使用的技能并使用
        /// </summary>
        public override void OnEnter() {
            _aiController = Owner.GetComponent<AIController>();
            int skillId;
            if (_aiController.IsHost) {
                if (_aiController.ManualSkillId >= 0) {
                    _skill = _aiController.SkillManager.GetSkill(_aiController.ManualSkillId);
                    _aiController.ResetManualSkill();
                } else {
                    _skill = _aiController.SkillManager.ChooseSkill(_aiController.RGRandom);
                }

                var randomSeed = RandomUtil.GetRandomSeedCurTime();
                _skill.Data.RandomSeed = randomSeed;
                _aiController.SendSyncSkill(_skill.Data.SkillID, randomSeed);
                skillId = _skill.Data.SkillID;
            } else {
                _skill = _aiController.SkillManager.GetSkill(_aiController.SyncSkillId);
                skillId = _aiController.SyncSkillId;
            }

            if (_skill == null) {
                Debug.LogError($"[SkillFsmAction]未找到Skill : {skillId}");
                Finish();
                return;
            }

            if (LogUtil.IsShowLog) {
                LogUtil.Log($"[SkillFsmAction]{_aiController.BodyTransform.name} choose skill : {_skill.Data.SkillName}");
            }

            _skill.onSkillExpectedEnd += Finish;
            _skill.onSkillUnexpectedEnd += Finish;

            var startUpTime = _skill.Data.SkillStartUp;
            if (startUpTime <= 0) {
                _aiController.SkillManager.DoSkill(_skill.Data.SkillID);
                _skillStart = true;
            } else {
                StartUpIdle();
            }

            if (NeedFixedRotation) {
                _aiController.UpdateFacingAndAiming = false;
            }
        }

        private void StartUpIdle() {
            var go = PrefabPool.Inst.Take(_startUpProto);
            go.name = "start_up";
            go.transform.SetParent(_aiController.BodyTransform);
            go.transform.localPosition = new Vector3(0.8f, 1.2f, 0);
            go.transform.eulerAngles = Vector3.zero;
            _skillStartTimer = Timer.Register(_skill.Data.SkillStartUp, false, false, () => {
                CancelStartUpIdle(go);
                _aiController.SkillManager.DoSkill(_skill.Data.SkillID);
                _skillStart = true;
            });
        }

        private void CancelStartUpIdle(GameObject startUpGo) {
            PrefabPool.Inst.Store(startUpGo);
        }

        public override void OnFixedUpdate() {
            if (!_skillStart) {
                return;
            }

            if (_skill.Data.NeedMoveCtrl) {
                _aiController.MoveUpdate(_skill.GetMoveDirection());
            }
        }

        public override void OnExit() {
            _skillStartTimer?.Cancel();
            _skillStartTimer = null;
            _skillStart = false;
            _aiController.Animator.SetInteger(AIController.AnimatorHashSkill, 0);
            if (_skill != null) {
                _skill.onSkillExpectedEnd -= Finish;
                _skill.onSkillUnexpectedEnd -= Finish;
            }
            _aiController.UpdateFacingAndAiming = true;
            _aiController.StopMove();
            // 如果意外离开，触发技能意外停止
            _aiController.SkillManager.StopSkill();
            _aiController.Think();
        }
    }
}