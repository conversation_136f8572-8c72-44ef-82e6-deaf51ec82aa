using BattleSystem.Skill;
using ModeSeason.ComboGun;
using System.Collections;
using UnityEngine;

namespace BattleSystem.SkillSystem {
    public class SkillCGBoss0JumpAttack : AISkillBaseCGBoss {
        #region 参数

        private const float ForceFactor = 40f;
        private const int RepelFactor = 5;
        private const int AnimatorSkillIndex = 2;
        private const float BulletSize = 1.5f;
        private const float MinUseDistance = 15;
        private const float XOffset = 2.7f;
        private const string HammerId = "proj1101";
        private const string BulletId = "proj1104";
        private const int BulletCount = 18;
        private const int AngleDelta = 360 / BulletCount;
        private const float ProjectileSize = 4;

        #endregion

        private readonly GameObject _hammerProto;
        private readonly GameObject _dustProto;
        private Vector2 _jumpIntention;
        private int _effectiveStartCounter;
        private WarnArea _warnArea;
        private readonly int _skillDamage;


        #region 关卡模式

        private readonly GameObject _circleProto;
        private readonly GameObject _bulletProto;
        private const string BulletSizeInLevel = "BulletSizeInLevel";
        private bool Angry {
            get {
                var hpPercent = AIController.GetHpPercent();
                return hpPercent < 0.5f;
            }
        }

        private float Size {
            get {
                return Angry ? ProjectileSize * AngryFactor : ProjectileSize;
            }
        }

        private const float AngryFactor = 1.2f;

        #endregion

        public SkillCGBoss0JumpAttack(AIController aiController, SkillData data) : base(aiController, data) {
            _hammerProto = ResourcesUtil.Load<GameObject>(SkillConfig.Prefabs[0]);
            _dustProto = ResourcesUtil.Load<GameObject>(SkillConfig.Prefabs[1]);
            if (!InCG) {
                _circleProto = ResourcesUtil.Load<GameObject>(SkillConfig.Prefabs[2]);
                _bulletProto = ResourcesUtil.Load<GameObject>(SkillConfig.Prefabs[3]);
            }

            _skillDamage = DamageSystem.GetSkillDamage(AIController.AIAttributeConfig.PhysicalAttack, SkillConfig.SkillDamageFactor[0]);
        }

        protected override void InitParameters() {
            AddParameter(UseSkillCondition.MinUseDistance, MinUseDistance);
            AddParameter(BulletSizeInLevel, 7f);
        }

        public override bool DoSkill(bool isEnforce = false) {
            if (!base.DoSkill(isEnforce)) {
                return false;
            }

            _effectiveStartCounter = 0;
            AIController.Animator.SetInteger(AIController.AnimatorHashSkill, AnimatorSkillIndex);
            _jumpIntention = (Vector2)Target.position - AIController.BodyWorldPosition;
            AIController.Facing = _jumpIntention.x > 0 ? 1 : -1;

            var sign = AIController.Facing;
            var offset = GetBulletPositionOffset(sign);
            _warnArea = WarnArea.Circle(AIController.BodyWorldPosition + offset + _jumpIntention.normalized * (ForceFactor * 0.15f), InCG ? ProjectileSize : Size, 1);
            return true;
        }

        public override void OnEffectiveStartHandler() {
            if (!Target) {
                return;
            }

            switch (_effectiveStartCounter) {
                case 0:
                    // 视觉
                    var visual = Object.Instantiate(_dustProto, AIController.BodyWorldPosition, Quaternion.identity);
                    visual.transform.localScale = new Vector3(BulletSize * BodySize, BulletSize * BodySize, 1.5f);
                    break;
                case 1:
                    AIController.GetForceActive(_jumpIntention.normalized * ForceFactor);
                    break;
            }

            _effectiveStartCounter++;
        }

        public override void OnEffectiveEndHandler() {
            if (_warnArea) {
                Object.Destroy(_warnArea.gameObject);
            }

            var sign = AIController.Facing;

            // 逻辑
            var position = AIController.BodyWorldPosition + GetBulletPositionOffset(sign);

            if (InCG) {
                var projectile = CGFactory.CreateProjectile(HammerId);
                var context = new CGBlockAttackContext(AIController.BodyTransform.GetComponent<CGMob>());
                projectile.Launch(position, Vector2.zero, null, context, true);
                projectile.transform.localScale = GetFinalBulletScale();
                projectile.damage.SetOriginalValue(_skillDamage);

                for (int i = 0; i < BulletCount; i++) {
                    var angle = i * AngleDelta;
                    var proj = CGFactory.CreateProjectile(BulletId);
                    proj.Launch(position, AngleToVector(angle), null, context, true);
                }
            } else {
                var bulletSize = GetParameter<float>(BulletSizeInLevel);
                if (Angry) {
                    bulletSize *= AngryFactor;
                }
                var bullet = BulletFactory.TakeBullet(
                    new BulletInfo().SetUp(_circleProto, AIController.BodyTransform.gameObject, 0, position, 0, AIController.Camp).SetBulletSize(bulletSize),
                    new DamageInfo().SetUp(_circleProto, _skillDamage, 0, 3, AIController.Camp));
                AIController.StartCoroutine(CreateBullets(position));
                bullet.GetComponent<ExplodeHammer>().destroyTime = 0.1f;
            }

            // 视觉
            var visual = Object.Instantiate(_hammerProto, position, Quaternion.identity);
            visual.transform.localScale = GetFinalBulletScale();

            AIController.Animator.SetInteger(AIController.AnimatorHashSkill, 0);
        }

        private IEnumerator CreateBullets(Vector2 position) {
            CreateBulletCircle(position);
            if (!Angry) {
                yield break;
            }

            yield return new WaitForSeconds(0.25f);
            CreateBulletCircle(position);
            yield return new WaitForSeconds(0.25f);
            CreateBulletCircle(position);
        }

        private void CreateBulletCircle(Vector2 position) {
            for (int i = 0; i < BulletCount; i++) {
                var angle = i * AngleDelta;
                BulletFactory.TakeBullet(
                    new BulletInfo().SetUp(_bulletProto, AIController.BodyTransform.gameObject, 12, position, angle, AIController.Camp).SetBulletSize(GetFinalBulletScale().x),
                    new DamageInfo().SetUp(_bulletProto, 4, 0, 3, AIController.Camp));
            }
        }

        protected override void OnExpectedEndHandler() {
            AIController.ClearInertialVelocity();
            base.OnExpectedEndHandler();
        }

        private static Vector2 AngleToVector(float angleInDegrees) {
            float angleInRadians = angleInDegrees * Mathf.Deg2Rad;
            float x = Mathf.Cos(angleInRadians);
            float y = Mathf.Sin(angleInRadians);
            return new Vector2(x, y);
        }

        private Vector3 GetFinalBulletScale() {
            return new Vector3(BulletSize * BodySize, BulletSize * BodySize, 1);
        }

        private Vector2 GetBulletPositionOffset(int sign) {
            return Vector2.right * (sign * XOffset * BulletSize);
        }
    }
}