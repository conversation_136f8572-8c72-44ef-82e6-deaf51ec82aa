using BattleSystem.Skill;
using BattleSystem.Strategy;
using ModeSeason.ComboGun;
using RGScript.Character.Player;
using UnityEngine;

namespace BattleSystem.SkillSystem {
    public class SkillFollowerRemoteAttack : AISkillBase {
        protected readonly IHand _hand;
        protected readonly IMoveStrategy _moveStrategy;
        protected CGMob _targetMob;
        protected RGEController _targetNormal;
        protected const float MaxAttackDistance = 8f;

        public SkillFollowerRemoteAttack(AIController aiController, SkillData data) : base(aiController, data) {
            _hand = aiController.Hand;
            _moveStrategy = StrategyManager.GetStrategy<IMoveStrategy>(<PERSON><PERSON>ontroller, SkillConfig.MoveStrategy);
        }
        
        public override bool CanUseSkill() {
            if (_hand == null) {
                return false;
            }

            if (BattleData.data.InComboGunGameScene) {
                AIController.Brain.TryGetParameter(AIBrainParameters.MasterTargetCgMob, out CGMob target);
                if (null == target) {
                    return false;
                }

                _targetMob = target;

                if (target.Dead) {
                    return false;
                }

                return null != _targetMob;
            } 
            
            AIController.Brain.TryGetParameter(AIBrainParameters.TargetNormalEnemy, out RGEController target2);
            if (null == target2) {
                return false;
            }

            _targetNormal = target2;

            if (target2.dead) {
                return false;
            }

            return null != _targetNormal;
        }

        public override bool DoSkill(bool isEnforce = false) {
            if (!base.DoSkill(isEnforce)) {
                return false;
            }
            AIController.Brain.SetParameter(AIBrainParameters.ApproachingTargetDeviation, MaxAttackDistance);
            AIController.Target = BattleData.data.InComboGunGameScene ? _targetMob.transform : _targetNormal.transform;
            return true;
        }

        public override Vector2 GetMoveDirection() {
            Transform targetTR;
            if (BattleData.data.InComboGunGameScene) {
                AIController.Brain.TryGetParameter(AIBrainParameters.MasterTargetCgMob, out CGMob target);
                _targetMob = target;

                if (null == _targetMob) {
                    UnexpectedEnd();
                    return AIController.BodyWorldPosition;
                }

                if (_targetMob.Dead) {
                    UnexpectedEnd();
                    return AIController.BodyWorldPosition;
                }
                targetTR = _targetMob.transform;
            } else {
                AIController.Brain.TryGetParameter(AIBrainParameters.TargetNormalEnemy, out RGEController target);
                _targetNormal = target;

                if (null == _targetNormal) {
                    UnexpectedEnd();
                    return AIController.BodyWorldPosition;
                }

                if (_targetNormal.dead) {
                    UnexpectedEnd();
                    return AIController.BodyWorldPosition;
                }
                targetTR = _targetNormal.transform;
            }

            var targetPos = _moveStrategy.Execute(targetTR.position);
            if (_moveStrategy.IsAchieved()) {
                OnArrived();
            }

            return targetPos;
        }


        protected override void OnExpectedEndHandler() {
            base.OnExpectedEndHandler();
            GenericEndProcess();
        }

        protected override void OnUnexpectedEndHandler() {
            base.OnUnexpectedEndHandler();
            GenericEndProcess();
        }

        private void GenericEndProcess() {
            _moveStrategy?.Finish();
        }

        protected void OnArrived() {
            Attack();
        }

        protected virtual void Attack() {
            _hand.SetAttackTrigger();
            ExpectedEnd();
        }
    }
}