using BattleSystem.Skill;
using BattleSystem.FollowerBrain;
using BattleSystem.Strategy;
using ChillyRoom;
using ModeSeason.ComboGun;
using RGScript.Character.Follower;
using System.Collections;
using UnityEngine;

namespace BattleSystem.SkillSystem {
    public class TaiShiCiMuSkill : AISkillBase {
        private readonly IMoveStrategy _moveStrategy;
        private FollowerMotherOfTaiShiCi followerMotherOfTaiShiCi;
        private CGMob _targetMob;
        private RGEController _targetNormal;

        private int attackTrack;
        private bool hasPostProcessSkillData;

        public TaiShiCiMuSkill(AIController aiController, SkillData data) : base(aiController, data) {
            _moveStrategy = StrategyManager.GetStrategy<IMoveStrategy>(AIController, SkillConfig.MoveStrategy);

            followerMotherOfTaiShiCi = AIController.Brain as FollowerMotherOfTaiShiCi;
            if (null == followerMotherOfTaiShiCi) {
                LogUtil.LogError("FollowerMotherOfTaiShiCi brain is null");
            }
        }

        public void TryHit() {
            attackTrack += followerMotherOfTaiShiCi.effectDatas[0].intValue2;
            OnArrived();
        }

        public override bool CanUseSkill() {
            if (BattleData.data.InComboGunGameScene) {
                AIController.Brain.TryGetParameter(AIBrainParameters.MasterTargetCgMob, out CGMob target);
                if (null == target) {
                    return false;
                }

                _targetMob = target;

                if (target.Dead) {
                    return false;
                }

                return null != _targetMob;
            }

            AIController.Brain.TryGetParameter(AIBrainParameters.TargetNormalEnemy, out RGEController target2);
            if (null == target2) {
                return false;
            }

            _targetNormal = target2;

            if (target2.dead) {
                return false;
            }

            return null != _targetNormal;
        }

        public override bool DoSkill(bool isEnforce = false) {
            if (!base.DoSkill(isEnforce)) {
                return false;
            }
            
            if (!hasPostProcessSkillData) {
                if (followerMotherOfTaiShiCi.FollowerLevel >= 2) {
                    Data.SkillCd -= Data.SkillCd * followerMotherOfTaiShiCi.effectDatas[1].value;
                }
                hasPostProcessSkillData = true;
            }

            autoAttacking = false;
            AIController.Target = BattleData.data.InComboGunGameScene ? _targetMob.transform : _targetNormal.transform;
            return true;
        }

        public override Vector2 GetMoveDirection() {
            Transform targetTR;
            if (BattleData.data.InComboGunGameScene) {
                if (null == _targetMob) {
                    UnexpectedEnd();
                    return AIController.BodyWorldPosition;
                }

                if (null == _targetMob || _targetMob.Dead) {
                    UnexpectedEnd();
                    return AIController.BodyWorldPosition;
                }

                targetTR = _targetMob.transform;
            } else {
                if (null == _targetNormal) {
                    UnexpectedEnd();
                    return AIController.BodyWorldPosition;
                }

                if (null == _targetNormal || _targetNormal.dead) {
                    UnexpectedEnd();
                    return AIController.BodyWorldPosition;
                }

                targetTR = _targetNormal.transform;
            }

            AIController.Target = targetTR;
            var moveDir = _moveStrategy.Execute(targetTR.position);
            if (Vector2.Distance(AIController.BodyWorldPosition, AIController.Target.position) <
                followerMotherOfTaiShiCi.maxDistance) {
                if (!attacking && !autoAttacking) {
                    attackTrack++;
                    autoAttacking = true;
                    OnArrived();
                }
            }

            return moveDir;
        }

        void OnArrived() {
            if (attacking) return;
            if (!CanUseSkill() || followerMotherOfTaiShiCi.FollowerController.followerState ==
                RGFollowerController.FollowerState.Follow) {
                attackTrack = 0;
                return;
            }

            Loom.Current.StartCoroutine(CreateBullet());
        }

        protected override void OnExpectedEndHandler() {
            base.OnExpectedEndHandler();
            GenericEndProcess();
        }

        protected override void OnUnexpectedEndHandler() {
            base.OnUnexpectedEndHandler();
            GenericEndProcess();
        }

        private void GenericEndProcess() {
            _moveStrategy?.Finish();
        }

        private bool autoAttacking = false;
        private bool attacking = false;

        IEnumerator CreateBullet() {
            if (attackTrack <= 0) {
                ExpectedEnd();
                yield break;
            }

            attackTrack--;
            attacking = true;

            var localRotation = AIController.BodyTransform.localRotation;
            var position = AIController.BodyTransform.position;

            var effect = GameObject.Instantiate(ResourcesUtil.Load<GameObject>(followerMotherOfTaiShiCi.taishiciPath));
            effect.transform.position = position;
            effect.transform.localRotation = localRotation;

            GameObject.Destroy(effect, 1);

            Vector3 startPosition = position;
            float startTime = Time.time;
            float endTime = startTime + followerMotherOfTaiShiCi.animTime;

            while (Time.time < endTime) {
                if (_targetMob == null && _targetNormal == null) yield break;
                effect.transform.position = Vector3.Lerp(startPosition,
                    (_targetMob != null ? _targetMob.transform : _targetNormal.transform).position,
                    (Time.time - startTime) / (endTime - startTime));
                yield return 0;
            }
            
            GameUtil.CameraShake(1);
            effect = GameObject.Instantiate(ResourcesUtil.Load<GameObject>(followerMotherOfTaiShiCi.effectPath));
            effect.transform.position =
                (_targetMob != null ? _targetMob.GetColliderCenter() : _targetNormal.transform.position);
            effect.transform.localRotation = localRotation;
            GameObject.Destroy(effect, 1);

            IHitable hitable = _targetMob != null ? _targetMob : _targetNormal;
            hitable.TakeDamage(new CGDamageInfo {
                damage = followerMotherOfTaiShiCi.damage,
            });
            CGBuffManager.BuffManager.AddBuff(AIController.BodyTransform.gameObject, _targetMob != null ? _targetMob.gameObject : _targetNormal.gameObject, "stun0");

            attacking = false;
            OnArrived();
        }
    }
}