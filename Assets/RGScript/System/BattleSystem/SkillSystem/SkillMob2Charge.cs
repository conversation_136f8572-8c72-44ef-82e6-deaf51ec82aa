using BattleSystem.Skill;
using BattleSystem.Strategy;
using RGScript.Character.Player;
using System.Collections;
using UnityEngine;

// ReSharper disable once CheckNamespace
namespace BattleSystem.SkillSystem {
    public class SkillMob2Charge : SkillEditable {
        #region 参数

        private const int RepelFactor = 5;
        private const float BulletSize = 1.6f;
        private const float ChargeSpeedFactor = 0.8f;
        private const string WarnTime = "WarnTime";
        private const string WarnAreaSize = "WarnAreaSize";
        private const string ChargeSpeedBuffKey = "ChargeSpeedBuff";
        private const string ChargeTime = "ChargeTime";
        private const string ChargeCount = "ChargeCount";

        #endregion

        private readonly GameObject _bulletProto;
        private readonly IMoveStrategy _moveStrategy;
        private GameObject _bullet;
        private bool _startMove;
        private readonly int _skillDamage;
        private readonly IHand _hand;
        private static readonly int AtkB = Animator.StringToHash("atk_b");
        private WarnArea _warnArea;
        private Coroutine _chargeCoroutine;

        public SkillMob2Charge(AIController aiController, SkillData data) : base(aiController, data) {
            _bulletProto = ResourcesUtil.Load<GameObject>(SkillConfig.Prefabs[0]);
            _moveStrategy = StrategyManager.GetStrategy<IMoveStrategy>(AIController, SkillConfig.MoveStrategy);
            _skillDamage = DamageSystem.GetSkillDamage(AIController.AIAttributeConfig.PhysicalAttack, SkillConfig.SkillDamageFactor[0]);
            _hand = aiController.Hand;
            AIController.Brain.SetParameter(AIBrainParameters.NeedReflectMove, false);
        }

        protected override void InitParameters() {
            AddParameter(UseSkillCondition.MinUseDistance, 12f);
            AddParameter(WarnAreaSize, new Vector2(10, 2));
            AddParameter(WarnTime, 0.3f);
            AddParameter(ChargeTime, 0.8f);
        }

        public override bool DoSkill(bool isEnforce = false) {
            if (!base.DoSkill(isEnforce)) {
                return false;
            }

            _chargeCoroutine = AIController.StartCoroutine(Charge());
            return true;
        }

        private IEnumerator Charge() {
            var chargeCount = GetParameter<int>(ChargeCount);
            for (int i = 0; i < chargeCount; i++) {
                if (Target == null) {
                    continue;
                }

                _startMove = false;
                _moveStrategy.Execute(Target.position); //预先执行一次，固定移动方向
                var size = GetParameter<Vector2>(WarnAreaSize);
                var warnTime = GetParameter<float>(WarnTime);
                var selfToTarget = AIController.GetSelfToTarget(Target);
                var angle = Vector2.Angle(selfToTarget, Vector2.right);
                if (selfToTarget.y < 0) {
                    angle = -angle;
                }
                _warnArea = WarnArea.BoxDirSpread(AIController.BodyTransform.position, angle, size, warnTime);
                yield return new WaitForSeconds(warnTime);
                _bullet = BulletFactory.TakeBullet(
                    new BulletInfo().SetUp(_bulletProto, AIController.BodyTransform.gameObject, 0, AIController.BodyWorldPosition, 0, AIController.Camp).SetBulletSize(BulletSize),
                    new DamageInfo().SetUp(_bulletProto, _skillDamage, 0, RepelFactor, AIController.Camp), true, AIController.BodyTransform);
                _bullet.transform.localRotation = Quaternion.identity;
                _bullet.transform.localScale = new Vector3(BodySize, BodySize, 1);
                AIController.AttributeBridge.GetSpeedAttributeValue().AddMultiplicationValue(ChargeSpeedBuffKey, 1 + ChargeSpeedFactor);
                RGEWeapon weapon = null;
                if (_hand != null && _hand.GetRGEWeapon() is { } w && w != null) {
                    weapon = w;
                    weapon.GetComponent<Animator>().SetBool(AtkB, true);
                }
                _startMove = true;

                var chargeTime = GetParameter<float>(ChargeTime);
                yield return new WaitForSeconds(chargeTime);
                if (weapon != null) {
                    weapon.GetComponent<Animator>().SetBool(AtkB, false);
                }
                DestroyBullet();
                _moveStrategy.Finish();
                AIController.AttributeBridge.GetSpeedAttributeValue().RemoveValue(ChargeSpeedBuffKey);
            }

            ExpectedEnd();
        }

        private void DestroyBullet() {
            if (_bullet && PrefabPool.Inst) {
                PrefabPool.Inst.Store(_bullet);
            }
        }

        protected override void OnExpectedEndHandler() {
            GenericEndProcess();
            base.OnExpectedEndHandler();
        }

        protected override void OnUnexpectedEndHandler() {
            if (_chargeCoroutine != null) {
                AIController.StopCoroutine(_chargeCoroutine);
            }

            GenericEndProcess();
            base.OnUnexpectedEndHandler();
        }

        private void GenericEndProcess() {
            _startMove = false;
            _moveStrategy.Finish();
            DestroyBullet();
            AIController.Animator.SetInteger(AIController.AnimatorHashSkill, 0);
            AIController.AttributeBridge.GetSpeedAttributeValue().RemoveValue(ChargeSpeedBuffKey);
            if (_hand != null && _hand.GetRGEWeapon() is { } w && w != null) {
                w.GetComponent<Animator>().SetBool(AtkB, false);
            }
            if (_warnArea != null && _warnArea.gameObject != null) {
                Object.Destroy(_warnArea.gameObject);
            }
        }

        public override Vector2 GetMoveDirection() {
            if (!_startMove) {
                return AIController.BodyWorldPosition;
            }

            var moveIntention = _moveStrategy.Execute(Target.position);
            var selfToTarget = moveIntention - AIController.BodyWorldPosition;
            AIController.Facing = selfToTarget.x > 0 ? 1 : -1;
            return moveIntention;
        }
    }
}