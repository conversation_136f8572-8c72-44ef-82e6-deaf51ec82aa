using BattleSystem.Skill;
using BattleSystem.Strategy;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace BattleSystem.SkillSystem {
    public class SkillBossVoidAbsorb : SkillEditable {
        #region 参数

        private const string AbsorbForceByDistanceInLevel = "AbsorbForceByDistanceInLevel";
        private const string AbsorbForceFixedInLevel = "AbsorbForceFixedInLevel";
        private const string BulletPath = "RGPrefab/Bullet/EnemyBullet/bullet_e_circle_kinematic.prefab";
        private const string BulletSize = "BulletSize";
        private const int RepelFactor = 6;

        #endregion

        private readonly List<RGController> _playerControllers = new List<RGController>();
        private Coroutine _absorbCoroutine;
        private readonly GameObject _circleProto;
        private readonly List<ParticleSystem> _absorbParticles;
        private GameObject _circle;
        private readonly int _skillDamage;

        public SkillBossVoidAbsorb(AIController aiController, SkillData data) : base(aiController, data) {
            _skillDamage = DamageSystem.GetSkillDamage(AIController.AIAttributeConfig.PhysicalAttack, SkillConfig.SkillDamageFactor[0]);
            _absorbParticles = AIController.BodyTransform.Find("img/gate_slot/boss_void_gate/absorb").GetComponentsInChildren<ParticleSystem>().ToList();
            _circleProto = ResourcesUtil.Load<GameObject>(BulletPath);
            foreach (var particle in _absorbParticles) {
                particle.Stop();
            }
        }

        protected override void InitParameters() {
            AddParameter(UseSkillCondition.MinUseDistance, 15f);
            AddParameter(AbsorbForceByDistanceInLevel, 2f);
            AddParameter(AbsorbForceFixedInLevel, 2f);
            AddParameter(BulletSize, 3.5f);
        }

        public override bool DoSkill(bool isEnforce = false) {
            if (!base.DoSkill(isEnforce)) {
                return false;
            }

            AIController.LookAtTarget(Target, out _);
            Absorb();
            CreateBullet();
            return true;
        }

        private void Absorb() {
            foreach (var particle in _absorbParticles) {
                particle.Play();
            }
            _playerControllers.Clear();
            var players = Targeting.FindAllPlayers(AIController.BodyTransform, 50);
            foreach (var player in players) {
                _playerControllers.Add(player.GetComponent<RGController>());
            }
            _absorbCoroutine = AIController.StartCoroutine(AbsorbUpdate());
        }

        private void CreateBullet() {
            var bulletSize = GetParameter<float>(BulletSize);
            _circle = BulletFactory.TakeBullet(
                new BulletInfo().SetUp(_circleProto, AIController.BodyTransform.gameObject, 0, Vector3.zero, 0, AIController.Camp).SetBulletSize(bulletSize),
                new DamageInfo().SetUp(_circleProto, _skillDamage, 0, RepelFactor, AIController.Camp), true, AIController.BodyTransform);
            _circle.GetComponent<ExplodeHammer>().destroyTime = 999f;
            _circle.transform.localPosition = Vector3.zero + new Vector3(0, 1.81f, 0);
        }

        private IEnumerator AbsorbUpdate() {
            while (_playerControllers.Count > 0) {
                foreach (var playerController in _playerControllers) {
                    playerController.AddAdditionalVelocity(GetAbsorbForce(playerController.transform));
                }
                yield return new WaitForFixedUpdate();
            }
        }

        private Vector2 GetAbsorbForce(Transform target) {
            float absorbForceFactor = GetParameter<float>(AbsorbForceByDistanceInLevel);
            float absorbForceFixed = GetParameter<float>(AbsorbForceFixedInLevel);
            var vec = AIController.BodyWorldPosition - (Vector2)target.position;
            var dir = vec.normalized;
            var mag = vec.magnitude;
            var result = (absorbForceFixed + absorbForceFactor * (1 / Mathf.Clamp(mag, 1f, 10f))) * dir;
            return result;
        }


        protected override void OnExpectedEndHandler() {
            GenericEndProcess();
            base.OnExpectedEndHandler();
        }

        protected override void OnUnexpectedEndHandler() {
            GenericEndProcess();
            base.OnUnexpectedEndHandler();
        }

        private void GenericEndProcess() {
            foreach (var particle in _absorbParticles) {
                particle.Stop();
            }
            _playerControllers.Clear();
            if (_absorbCoroutine != null) {
                AIController.StopCoroutine(_absorbCoroutine);
            }

            if (!_circle) {
                return;
            }

            PrefabPool.Inst?.Store(_circle);
            _circle = null;
        }
    }
}