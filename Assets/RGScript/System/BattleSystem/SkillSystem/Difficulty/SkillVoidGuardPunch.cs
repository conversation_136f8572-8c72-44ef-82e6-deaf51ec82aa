using BattleSystem.Skill;
using System.Collections;
using UnityEngine;

namespace BattleSystem.SkillSystem {
    public class SkillVoidGuardPunch : SkillEditable {
        #region 参数

        private const float ForceFactor = 20f;
        private const int AnimatorSkillIndex = 1;
        private const string BulletSize = "BulletSize";
        private const int RepelFactor = 6;
        private const string BulletPath = "Level/difficulty/Enemy/e_void_guard/e_void_guard_skill_1_bullet.prefab";
        private const int PunchMaxCount = 2;

        #endregion

        private readonly int _skillDamage;
        private Vector2 _selfToTarget;
        private readonly Transform _leftGunPoint;
        private readonly Transform _rightGunPoint;
        private readonly GameObject _bulletProto;
        private int _punchCount;

        public SkillVoidGuardPunch(AIController aiController, SkillData data) : base(aiController, data) {
            _skillDamage = DamageSystem.GetSkillDamage(AIController.AIAttributeConfig.PhysicalAttack, SkillConfig.SkillDamageFactor[0]);
            _bulletProto = ResourcesUtil.Load<GameObject>(BulletPath);
            var hand = aiController.Hand.GetTransform();
            _rightGunPoint = hand.Find("weapon/h1/w/gun_point");
            _leftGunPoint = hand.Find("weapon/h2/w/gun_point");
        }

        protected override void InitParameters() {
            AddParameter(UseSkillCondition.MinUseDistance, 15f);
            AddParameter(BulletSize, 2.5f);
        }

        public override bool DoSkill(bool isEnforce = false) {
            if (!base.DoSkill(isEnforce)) {
                return false;
            }

            _punchCount = 0;
            AIController.LookAtTarget(Target, out _selfToTarget);
            AIController.Animator.SetInteger(AIController.AnimatorHashSkill, AnimatorSkillIndex);
            return true;
        }

        public override void OnEffectiveStartHandler() {
            var angle = Mathf.Clamp(AIController.AimAngle, -VoidGuardFistLookAtTarget.ClampAngle, VoidGuardFistLookAtTarget.ClampAngle);
            angle = AIController.Facing < 0 ? 180 - angle : angle;
            var gunPoint = _punchCount % 2 == 0 ? _rightGunPoint : _leftGunPoint;
            AIController.GetForceActive(_selfToTarget.normalized * ForceFactor);
            var bulletSize = GetParameter<float>(BulletSize);
            BulletFactory.TakeBullet(
                new BulletInfo().SetUp(_bulletProto, AIController.BodyTransform.gameObject, 0, gunPoint.position, angle, AIController.Camp).SetBulletSize(bulletSize),
                new DamageInfo().SetUp(_bulletProto, _skillDamage, 0, RepelFactor, AIController.Camp));
            _punchCount++;
        }

        public override void OnEffectiveEndHandler() {
            if (_punchCount >= PunchMaxCount) {
                ExpectedEnd();
            }
        }

        protected override void OnExpectedEndHandler() {
            GenericEndProcess();
            base.OnExpectedEndHandler();
        }

        protected override void OnUnexpectedEndHandler() {
            GenericEndProcess();
            base.OnUnexpectedEndHandler();
        }

        private void GenericEndProcess() {
            _punchCount = 0;
        }
    }
}