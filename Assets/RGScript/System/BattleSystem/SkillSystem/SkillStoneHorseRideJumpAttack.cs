using BattleSystem.Skill;
using BattleSystem.Strategy;
using UnityEngine;

namespace BattleSystem.SkillSystem {
    public class SkillStoneHorseRideJumpAttack : AISkillBase {
        #region 参数

        private const float ForceFactor = 30f;
        private const int RepelFactor = 0;
        private const int AnimatorSkillIndex = 3;
        private const float BulletSize = 1.25f;

        #endregion

        private readonly GameObject _bulletProto;
        private readonly ITargetingStrategy _targetingStrategy;
        private Transform _target;
        private readonly int _skillDamage;

        public SkillStoneHorseRideJumpAttack(AIController aiController, SkillData data) : base(aiController, data) {
            _bulletProto = ResourcesUtil.Load<GameObject>(SkillConfig.Prefabs[0]);
            _targetingStrategy = StrategyManager.GetStrategy<ITargetingStrategy>(AIController, SkillConfig.TargetingStrategy);
            _skillDamage = DamageSystem.GetSkillDamage(AIController.AIAttributeConfig.PhysicalAttack, SkillConfig.SkillDamageFactor[0]);
        }

        public override bool CanUseSkill() {
            _target = _targetingStrategy.Execute();
            return _target;
        }

        public override bool DoSkill(bool isEnforce = false) {
            if (!base.DoSkill(isEnforce)) {
                return false;
            }

            AIController.Animator.SetInteger(AIController.AnimatorHashSkill, AnimatorSkillIndex);
            return true;
        }

        public override void OnEffectiveStartHandler() {
            if (!_target) {
                return;
            }

            var jumpIntention = (Vector2)_target.position - AIController.BodyWorldPosition;
            jumpIntention = jumpIntention.RandomRotate(-25, 25, AIController.RGRandom);
            if (jumpIntention.magnitude < 1) {
                jumpIntention = -jumpIntention;
            }

            AIController.Facing = AIController.GetFacingAndAimingAngle(_target).facing;
            AIController.GetForceActive((jumpIntention).normalized * ForceFactor);
        }

        public override void OnEffectiveEndHandler() {
            var bullet = BulletFactory.TakeBullet(
                new BulletInfo().SetUp(_bulletProto, AIController.BodyTransform.gameObject, 0, AIController.BodyWorldPosition, 0, AIController.Camp),
                new DamageInfo().SetUp(_bulletProto, _skillDamage, 0, RepelFactor, AIController.Camp));

            var sign = AIController.Facing < 0 ? -1 : 1;
            bullet.transform.localScale = new Vector3(sign * BulletSize * BodySize, BulletSize * BodySize, 1);
            AIController.Animator.SetInteger(AIController.AnimatorHashSkill, 0);
        }

        protected override void OnExpectedEndHandler() {
            AIController.ClearInertialVelocity();
            base.OnExpectedEndHandler();
        }
    }
}