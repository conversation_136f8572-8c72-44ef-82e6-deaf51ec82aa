using BattleSystem.Skill;
using BattleSystem.SkillSystem;
using cfg.game;
using RGScript.Data;

namespace BattleSystem {
    public class EnemyStoneOx : EnemyMount {
        private SkillConfig _configRideAttack;
        private SkillBase _rideAttack;
        private const int SkillStoneOxRideTrampleID = 7;
        private int _originWeight;

        public override void Init(AIController aiController) {
            base.Init(aiController);
            _configRideAttack = DataMgr.ConfigData.Tables.TbSkill.DataMap[SkillStoneOxRideTrampleID];
            _rideAttack = AIController.SkillManager.GetSkill(_configRideAttack.Id);
            _originWeight = _rideAttack.Data.Weight;
            _rideAttack.Data.Weight = 0;
        }

        public override void Mount() {
            _rideAttack.Data.Weight = _originWeight;
            base.Mount();
        }

        /// <summary>
        /// 骑手会调用坐骑的Dismount
        /// </summary>
        public override void Dismount() {
            _rideAttack.Data.Weight = 0;
            base.Dismount();
        }
    }
}