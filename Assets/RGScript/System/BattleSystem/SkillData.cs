using cfg.game;
using MycroftToolkit.QuickCode;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace BattleSystem.Skill {
    public struct SkillData {
        public int SkillID;                             // 技能ID
        public string SkillName;                        // 技能名称，暂时用于debug
        public string SkillClass;                       // 技能类名
        public float SkillCd;                           // 技能CD
        public float SkillGcd;                          // 技能公共CD
        public float SkillStartUp;                      // 释放技能前的idle时间
        public int Expenses;                            // 技能开销
        public float SkillDuration;                     // 技能持续时间
        public bool UseAnimatorCtrlEnd;                 // 是否使用动画机控制结束(自动结束)
        public bool IsUseTrigger;                       // 是否为单次触发技能，若为false则SkillDuration才起作用
        public bool NeedFixedRotation;                  // 是否需要调整方向
        public bool NeedMoveCtrl;                       // 需要在技能中移动
        public int Weight;                              // 技能权重
        public int SoundEffectID;                       // 使用音效的ID
        public GeneralDictionary<string> CustomData;    // 自定义数据
        public int RandomSeed;

        public SkillData(SkillData data) {
            SkillID = data.SkillID;
            SkillName = data.SkillName;
            SkillClass = data.SkillClass;
            SkillCd = data.SkillCd;
            SkillGcd = data.SkillGcd;
            SkillStartUp = data.SkillStartUp;
            Expenses = data.Expenses;
            SkillDuration = data.SkillDuration;
            UseAnimatorCtrlEnd = data.UseAnimatorCtrlEnd;
            IsUseTrigger = data.IsUseTrigger;
            NeedFixedRotation = data.NeedFixedRotation;
            NeedMoveCtrl = false;
            Weight = data.Weight;
            SoundEffectID = data.SoundEffectID;
            CustomData = data.CustomData != null ? data.CustomData.DeepCopy() : new GeneralDictionary<string>();
            RandomSeed = 0;
        }

        public static SkillData SkillConfigToSkillData(SkillConfig config) {
            var skillData = new SkillData {
                SkillID = config.Id,
                SkillClass = config.Class,
                SkillName = config.SkillName,
                SkillCd = config.SkillCd,
                SkillGcd = config.SkillGcd,
                SkillStartUp = config.SkillStartUp,
                Expenses = config.Expenses,
                SkillDuration = config.SkillDuration,
                UseAnimatorCtrlEnd = config.UseAnimatorCtrlEnd,
                IsUseTrigger = config.IsUseTrigger,
                NeedFixedRotation = config.NeedFixedRotation,
                NeedMoveCtrl = config.NeedMoveCtrl,
                CustomData = GetCustomData(config.CustomData),
            };

            return skillData;
        }

        public static string SerializeCustomDataDict(Dictionary<string, object> customDataDict) {
            return JsonConvert.SerializeObject(customDataDict);
        }

        public static GeneralDictionary<string> GetCustomData(string customData) {
            var dict = JsonConvert.DeserializeObject<Dictionary<string, object>>(customData);
            if (dict == null) {
                dict = new Dictionary<string, object>();
            }
            ConvertValueType<double, float>(dict);
            ConvertValueType<long, int>(dict);
            GeneralDictionary<string> result = new GeneralDictionary<string> {
                Dict = dict,
            };

            return result;
        }

        private static void ConvertValueType<TFrom, TTo>(Dictionary<string, object> dict) {
            List<(string key, TTo value)> needConvert = new List<(string, TTo)>();
            foreach (var pair in dict) {
                if (pair.Value is not TFrom fromValue) {
                    continue;
                }
                var value = (TTo)Convert.ChangeType(fromValue, typeof(TTo));
                needConvert.Add((pair.Key, value));
            }

            foreach (var pair in needConvert) {
                dict[pair.key] = pair.value;
            }
        }

        public static GeneralDictionary<string> MergeCustomData(GeneralDictionary<string> lowPriority, GeneralDictionary<string> highPriority) {
            foreach (var data in highPriority) {
                lowPriority.Set(data.Key, data.Value, true);
            }

            return lowPriority;
        }
    }
}