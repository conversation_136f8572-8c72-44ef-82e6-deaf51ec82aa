using System;
using UnityEngine;

namespace BattleSystem {
    public class EnemyMount : AIBrain, IMount {
        public Transform SelfTransform { get => transform; }
        public Action<HurtInfo, bool> OnMountGetHurt { get; set; }
        public Action OnMountDead { get; set; }
        public Action OnMountDestroy { get; set; }

        private const string MountPointPath = "img/mount_point";
        private Transform _mountPoint;
        private const float MountFrictionChangeValue = -0.2f;
        private bool _hasRider;
        private Action _onDestroy;

        protected Material DefaultMaterial;
        protected Material MountMaterial;
        public bool CanMount() {
            return !_hasRider && !AIController.IsDead;
        }
        public Transform MountPoint {
            get {
                if (!_mountPoint) {
                    _mountPoint = transform.Find(MountPointPath);
                }

                return _mountPoint;
            }
        }

        public override void Init(AIController aiController) {
            base.Init(aiController);
            FindReferences();
            AIController.OnDead += MountDead;
            AIController.OnGetHurt += OnGetHurt;
        }

        private void FindReferences() {
            DefaultMaterial = AIController.BodyMaterial;
            MountMaterial = ResourcesUtil.Load<Material>("RGPrefab/Enemy/4-monolithicMountainsRuins/Common/MountOutline.mat");
        }

        public virtual void Mount() {
            _hasRider = true;
            AIController.Friction.AddAdditionValue("mount", MountFrictionChangeValue);
            AIController.BodyMaterial = MountMaterial;
            if(LogUtil.IsShowLog){LogUtil.Log($"[EnemyMount]{transform.name}骑乘");}
        }

        /// <summary>
        /// 骑手会调用坐骑的Dismount
        /// </summary>
        public virtual void Dismount() {
            AIController.BodyMaterial = DefaultMaterial;
            AIController.Friction.RemoveValue("mount");
            _hasRider = false;
            if(LogUtil.IsShowLog){LogUtil.Log($"[EnemyMount]{transform.name}下骑");}
        }

        private void OnGetHurt(HurtInfo hurtInfo, bool fromSync) {
            OnMountGetHurt?.Invoke(hurtInfo, fromSync);
        }

        private void MountDead() {
            Dismount();
            OnMountDead?.Invoke();
        }

        private void OnDestroy() {
            OnMountDestroy?.Invoke();
        }
    }
}