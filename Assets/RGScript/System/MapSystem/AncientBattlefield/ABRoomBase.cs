using MapSystem.QuickTile;
using System.Collections.Generic;
using UnityEngine.Tilemaps;

namespace MapSystem {
    public class ABRoomBase : RoomBase {
        protected override void InitTileMaps() {
            var baseTf = transform.Find("Floor/Base");
            TileMaps = new Dictionary<string, QTileMap>();

            Tilemap tileMap = baseTf.Find("Floor_0").GetComponent<Tilemap>();
            QTileMap qTileMap = new QTileMap(FullSize / 2, tileMap);
            TileMaps.Add("Floor_0", qTileMap);

            tileMap = baseTf.Find("Floor_1").GetComponent<Tilemap>();
            qTileMap = new QTileMap(FullSize / 2, tileMap);
            TileMaps.Add("Floor_1", qTileMap);

            tileMap = baseTf.Find("Floor_2").GetComponent<Tilemap>();
            qTileMap = new QTileMap(FullSize / 2, tileMap);
            TileMaps.Add("Floor_2", qTileMap);
        }
    }
}