using MapSystem.QuickTile;
using UnityEngine;
using UnityEngine.U2D;

namespace MapSystem.Builder.SubBuilder {
    public class CommonHideRoomSubBuilder : IRoomBaseSubBuilder {
        public RoomBaseBuildConfig Config { get; set; }
        public RoomBase CreatingRoom { get; private set; }

        private RGRandom _random;
        private readonly QTileRandom _rbFloorTile;
        private readonly QTileRandom _rbWallTile;
        private readonly QTileRandom _rbWallTopTile;

        private GameObject _doorPrefab;
        
        public CommonHideRoomSubBuilder(RoomBaseBuildConfig config) {
            Config = config;
            _rbFloorTile = new QTileRandom("floor", new Vector2Int(1, 1));
            _rbWallTile = new QTileRandom("wall", new Vector2Int(1, 1));
            _rbWallTopTile = new QTileRandom("wallTop", new Vector2Int(1, 1));
        }
        
        public void LoadRes(SpriteAtlas spriteAtlas) {
            Sprite[] allSprites = new Sprite[spriteAtlas.spriteCount];
            spriteAtlas.GetSprites(allSprites);
            foreach (var sprite in allSprites) {
                string[] nameSplit = sprite.name.Split('_');
                if (nameSplit == null || nameSplit.Length == 0 || nameSplit.Length < 5) {
                    continue;
                }
                if (nameSplit[0] != "common" || nameSplit[1] != "hideRoom") {
                    continue;
                }

                switch (nameSplit[3]) {
                    case "floor":
                        int weight = int.Parse(nameSplit[4][1].ToString());
                        _rbFloorTile.AddTile(sprite, weight);
                        continue;
                    case "wall":
                        _rbWallTile.AddTile(sprite);
                        continue;
                    case "wallTop":
                        _rbWallTopTile.AddTile(sprite);
                        continue;
                    default:
                        continue;
                }
            }
            
            string doorPrefabPath = Config.OtherPrefabPaths["HideRoomDoor"];
            _doorPrefab = ResourcesUtil.Load<GameObject>(doorPrefabPath);
        }

        public void BuildRoom(RoomBase creating, RGRandom rgRandom) {
            CreatingRoom = creating;
            _random = rgRandom;
            BuildFloor();
            BuildWalls();
            BuildDoor();
            BuildBackground();
            MapOutOfEdgeChecker.CreateCheckerForHideRoom(CreatingRoom);
        }

        private void BuildFloor() {
            QTileMap map = CreatingRoom.GetQTileMap("B_Floor");
            RectInt fillRect = new RectInt(Vector2Int.zero, CreatingRoom.BaseSize + Vector2Int.one);
            map.FillTile(fillRect, _rbFloorTile, Vector2Int.zero);
        }

        private void BuildWalls() {
            // 前方外墙
            QTileMap map = CreatingRoom.GetQTileMap("B_OutsideWall");
            Vector2Int startPos = Vector2Int.zero;
            Vector2Int endPos = new Vector2Int(CreatingRoom.BaseSize.x + Config.WallSize.x, 0);
            map.FillTile(startPos, endPos, _rbWallTile, Vector2Int.zero);
            
            // 后方内墙
            map = CreatingRoom.GetQTileMap("B_InsideWall");
            startPos = new Vector2Int(Config.WallSize.x, CreatingRoom.FullSize.y - Config.WallSize.y);
            endPos = new Vector2Int(CreatingRoom.FullSize.x - Config.WallSize.x, CreatingRoom.FullSize.y - Config.WallSize.x);
            map.FillTile(startPos, endPos, _rbWallTile, Vector2Int.zero);
            Rect r = new Rect {
                size = new Vector2(endPos.x - startPos.x, 0.25f),
                position = startPos - new Vector2(0,0.25f)
            };
            CreatColorBlock("shadow", map.Tilemap.transform, new Color(0,0,0,0.5f), r, "Shadow", 0);
            
            // 两侧顶墙
            map = CreatingRoom.GetQTileMap("B_TopWall");
            startPos = new Vector2Int(0, Config.WallSize.y);
            endPos = new Vector2Int(0, CreatingRoom.FullSize.y);
            map.FillTile(startPos, endPos, _rbWallTopTile, Vector2Int.zero);
            startPos = new Vector2Int(CreatingRoom.FullSize.x - Config.WallSize.x, Config.WallSize.y);
            endPos = new Vector2Int(CreatingRoom.FullSize.x - Config.WallSize.x, CreatingRoom.FullSize.y);
            map.FillTile(startPos, endPos, _rbWallTopTile, Vector2Int.zero);
        }

        private void BuildDoor() {
            Transform parent = CreatingRoom.transform.Find("Doors");
            GameObject enter = Object.Instantiate(_doorPrefab, parent);
            Transform tr = enter.transform;
            Vector3 targetPos = new Vector3(CreatingRoom.FullSize.x / 2f, 3);
            tr.localPosition = targetPos;
        }

        private void BuildBackground() {
            Rect r = new Rect {
                size = CreatingRoom.FullSize.Vec3() * 10,
                center = CreatingRoom.FullSize.Vec3() / 2
            };
            CreatColorBlock("background", CreatingRoom.transform, Color.black, r, "Floor", -1);
        }

        private SpriteRenderer CreatColorBlock(string name, Transform parent, Color color, Rect rect, string sortingLayer, int sortingOder) {
            GameObject target = new GameObject(name);
            Texture2D tex = new Texture2D(1, 1);
            tex.SetPixel(0, 0, color);
            Sprite sprite = Sprite.Create(tex, new Rect(0.0f, 0.0f, tex.width, tex.height), new Vector2(0.5f, 0.5f), 1f);
            var sr = target.AddComponent<SpriteRenderer>();
            sr.sprite = sprite;
            sr.sortingLayerName = sortingLayer;
            sr.sortingOrder = sortingOder;
            sr.color = color;
            target.transform.parent = parent;
            target.transform.localScale = rect.size;
            target.transform.localPosition = rect.center;
            return sr;
        }
    }
}