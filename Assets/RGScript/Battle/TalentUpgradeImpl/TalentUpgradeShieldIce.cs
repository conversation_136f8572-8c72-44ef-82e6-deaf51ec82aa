using UnityEngine;

namespace RGScript.Battle {
    public partial class TalentBuff {
        static BuffFuncNode InitUpgradeShieldIce(RGController controller, emBuff buffId) {
            return new UpgradeShieldIce().Setup(controller, buffId);
        }

        class UpgradeShieldIce {
            RGRandom _RNG = new RGRandom();
            public RGController controller;
            float _lastTime;

            public BuffFuncNode Setup(RGController controller, emBuff buffId) {
                this.controller = controller;
                _RNG.SetRandomSeed(controller.rg_random.Range(0, 999999));
                var buffEvent = new BuffFuncOnEvent<PlayerBulletPreHitEnemyEvent>() {
                    onEvent = OnPreHitEnemy
                };
                return buffEvent;
            }


            bool OnPreHitEnemy(PlayerBulletPreHitEnemyEvent ev) {
                if (ev.sourceObj != controller.gameObject || ev.enemy == null) {
                    return false;
                }

                if (ev.enemy.buffMgr.HasBuff<BuffIce>() && _RNG.Range(0, 1f) < Probability()) {
                    ev.ModifyDamage(AddDamageFactor(), Cicada.AttributeModifierType.PercentAdd);
                }

                return true;
            }


            public static float Probability() {
                return 0.5f;
            }

            public static float AddDamageFactor() {
                return 1;
            }
        }

        public static string GetUpgradeShieldIceDesc(string descKey) {
            return I2.Loc.ScriptLocalization.Get(descKey, "#免疫冰冻，对敌人的冰冻时间增加，敌人在冰冻期间受到的伤害有概率翻倍");
        }
    }
}