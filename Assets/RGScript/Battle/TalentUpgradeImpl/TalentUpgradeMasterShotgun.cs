using SoulKnight.Runtime.Weapon;
using UnityEngine;

namespace RGScript.Battle {
    public partial class TalentBuff {
        static BuffFuncNode InitUpgradeMasterShotgun(RGController controller, emBuff buffId) {
            return new UpgradeMasterShotgun().Setup(controller, buffId);
        }

        class UpgradeMasterShotgun {
            public RGController controller;
            float _lastTime;
            public BuffFuncNode Setup(RGController controller, emBuff buffId) {
                this.controller = controller;
                var buffEvent = new BuffFuncOnEvent<CreateBulletEvent>() {
                    onEvent = OnCreateBullet
                };
                return buffEvent;
            }

            bool OnCreateBullet(CreateBulletEvent ev) {
                if (ev.bullet == null || ev.bullet.GetSourceObject() != controller.gameObject || ev.bullet.bulletInfo.sourceWeapon == null) {
                    return false;
                }

                if (ev.bullet is not RGBullet) {
                    return false;
                }

                var isAffected = false;  
                if (ev.bullet.bulletInfo.sourceWeapon.weapon_type == emWeaponType.ShotGun ||
                    ev.bullet.bulletInfo.sourceWeapon.GetTalentList().Contains(emBuff.MasterShotgun)) {
                    isAffected = true;
                }

                if (!isAffected) {
                    return false;
                }

                if (ev.bullet.bulletInfo.isFromEffect) {
                    return false;
                }

                if (Time.timeSinceLevelLoad - _lastTime < Cooldown()) {
                    return false;
                }

                var angle = controller.oriantationAngle - 180;
                var bulletObj = DefaultWeaponBulletSpliter.CopyBullet(ev.bullet.gameObject, ev.bullet, Quaternion.Euler(0, 0, angle), true);
                DefaultWeaponBulletSpliter.CopyDamageCarrier(ev.bullet.gameObject, bulletObj);

                _lastTime = Time.timeSinceLevelLoad;

                return true;
            }

            static float Cooldown() {
                return 0.25f;
            }
        }
        

        public static string GetUpgradeMasterShotgunDesc(string descKey) {
            return string.Format(I2.Loc.ScriptLocalization.Get(descKey, "#每次攻击时身后同时发射一枚子弹"));
        }
    }
}