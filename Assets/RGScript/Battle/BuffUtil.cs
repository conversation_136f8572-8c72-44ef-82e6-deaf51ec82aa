using UnityEngine;
using RGScript.Character;
using System;
using Object = UnityEngine.Object;

namespace RGScript.Battle{

    public abstract class BuffWithIconImplementation {
        public IBuffFuncTarget source;
        public IBuffFuncTarget target;
        public abstract BuffDisplayBasicInfo OnCreateDisplayInfo();
        public virtual void OnBuffSetSourceObject() {}
        public virtual void OnBuffAttached() {}
        public virtual bool OnBuffUpdate(float dt) {
            return true;
        }
        public virtual void OnBuffDetached() {}
    }

    public class Buff : IBuff{
        public string name;
        public int buffTypeId{get; set;}
        public IBuffFuncTarget source;
        public IBuffFuncTarget owner;
        public BuffFuncNode buffFuncNode;

        public bool IsValid => buffFuncNode.IsValid;
        public bool IsRunning => buffFuncNode.IsRunning;
        public bool IsComplete => buffFuncNode.IsComplete;
        public bool IsEnd => buffFuncNode.IsEnd;
        public bool canStack { get; set; }
        public bool IsDebuff { get; set;}

        public void Start(){
            buffFuncNode.Start(source, owner);
        }
        public void Update(float dt){
            buffFuncNode.Update(dt);
        }
        public void End(){
            if(!buffFuncNode.IsEnd){
                buffFuncNode.End();
            }
        }

        public Buff(int type, IBuffFuncTarget source, IBuffFuncTarget target, BuffFuncNode node){
            this.buffTypeId = type;
            this.source = source;
            this.owner = target;
            this.buffFuncNode = node;
        }

        public void ResetLifeTime(){
            buffFuncNode.ResetLifeTime();
        }

        public void ResetLifeTime(float time){
            buffFuncNode.ResetLifeTime(time);
        }
    }

    [Serializable]
    public class CommonBuffConfig {
        public CommonBuffTypeID id;
        public float duration;
        [Range(0f, 1f)] public float rate;

        public void TakeEffect(IUnit source, IUnit target) {
            if (source == null || target == null) {
                return;
            }
            
            if (source.RGRandom.Range(0f, 1f) < rate) {
                BuffUtil.AddCommonBuff(source, target, id, duration);
            }
        }
    }

    public class BuffUtil{
        public static int GetSubBuffId(int mainBuffId, int idx){
            return mainBuffId * 10000 + idx;
        }
        
        public static int GetUpgradeSubBuffId(int mainBuffId, int idx){
            return mainBuffId * 10000 + 500 + idx;
        }

        public static bool IsSubBuffId(int mainBuffId, int buffId) {
            return mainBuffId == buffId || buffId / 10000 == mainBuffId;
        }

        public static bool IsUpgradeSubBuffId(int mainBuffId, int buffId) {
            return mainBuffId == buffId / 10000 && mainBuffId * 10000 - buffId >= 500;
        }
        
        // 一般buff
        public static Buff AddCommonBuff(object source, object target, CommonBuffTypeID buffTypeId, params object[] args) {
            var buffTarget = GetBuffTarget(target);
            if (buffTarget != null) {
                var buffSource = GetBuffTarget(source);
                return CommonBuff.AddBuff(buffSource, buffTarget, buffTypeId, args);
            }
            return null;
        }

        // 天赋
        public static void AddTalentBuff(RGController controller, BuffFuncNode node, int buffTypeId){
            var target = GetBuffTarget(controller);
            var buff = new Buff(buffTypeId, null, target, node);
            target.AddBuff(buff);
        }

        public static IBuffFuncTarget GetBuffTarget(object rawTarget){
            IBuffFuncTarget target = null;
            if(rawTarget is IBuffFuncTarget){
                target = rawTarget as IBuffFuncTarget;
            }
            else if(rawTarget is GameObject obj){
                if(obj.GetComponent<RGBaseController>() is var rgController && rgController != null){
                    target = GetBuffTarget(rgController);
                }
                else if(obj.GetComponent<RGEController>() is var rgeController && rgeController != null){
                    target = GetBuffTarget(rgeController);
                }
            }
            else if(rawTarget is Transform trans){
                if(trans.GetComponent<RGBaseController>() is var rgController && rgController != null){
                    target = GetBuffTarget(rgController);
                }
                else if(trans.GetComponent<RGEController>() is var rgeController && rgeController != null){
                    target = GetBuffTarget(rgeController);
                }
            }
            else{
                if(rawTarget is RGBaseController rgController && rgController != null){
                    target = GetBuffTarget(rgController);
                }
                else if(rawTarget is RGEController rgeController && rgeController != null){
                    target = GetBuffTarget(rgeController);
                }
            }
            return target;
        }

        static IBuffFuncTarget GetBuffTarget(RGBaseController controller){
            if (controller.buffMgr == null) {
                return null;
            }
            IBuffFuncTarget target = null;
            if(controller.buffMgr.buffTargetSelf == null){
                target = new RGControllerTarget(controller);
                controller.buffMgr.buffTargetSelf = target;
            }
            else{
                target = controller.buffMgr.buffTargetSelf;
            }
            return target;
        }

        static IBuffFuncTarget GetBuffTarget(RGEController controller){
            if (controller.buffMgr == null) {
                return null;
            }
            IBuffFuncTarget target = null;
            if(controller.buffMgr.buffTargetSelf == null){
                target = new RGEControllerTarget(controller);
                controller.buffMgr.buffTargetSelf = target;
            }
            else{
                target = controller.buffMgr.buffTargetSelf;
            }
            return target;
        }

        class RGControllerTarget : IBuffFuncTarget{
            RGBaseController _controller;

            BuffFuncEventHub _eventHub;

            public RGControllerTarget(RGBaseController controller){
                _controller = controller;
            }

            public IBuffFuncTarget GetTarget(object rawObj){
                return GetBuffTarget(rawObj);
            }
            public object rawObject => _controller;
            public int targetCamp => _controller.camp;
            public Transform transform => _controller != null ? _controller.transform : null;
            public Transform body => _controller.transform.Find("img");
            public bool dead => _controller.dead;
            public bool isAwake => _controller.awake;
            public GameObject gameObject => _controller != null ? _controller.gameObject : null;
            public BuffFuncEventHub eventHub{
                get{
                    if(_eventHub == null){
                        _eventHub = new BuffFuncEventHub();
                    }
                    return _eventHub;
                }
            }
            public RoleAttributeProxy attribute => _controller.attribute;

            public GameObject EmitBullet(BulletInfo binfo, DamageInfo dinfo, float deviate, AimDirectionType dirType, float dirFix, System.Func<Vector3, float, IBuffFuncTarget, object, Vector3> SpecifyEmitPosition, System.Action<GameObject> onBulletEmit){
                var emitPos = _controller.transform.position + Vector3.up * 0.6f;
                object emitPosCalculationArg = null;
                binfo = binfo.SetCamp(_controller.camp).SetSourceObject(_controller.gameObject);
                object latestHitTarget = null;
                if(eventHub.GetCachedEventData(out PlayerBulletHitEnemyEvent latestEventData)){
                    binfo.SetSourceWeapon(latestEventData.sourceWeapon);
                    latestHitTarget = latestEventData.enemy;
                }
                else if(eventHub.GetCachedEventData(out PlayerBulletPreHitEnemyEvent latestPreHitEventData)){
                    binfo.SetSourceWeapon(latestPreHitEventData.sourceWeapon);
                    latestHitTarget = latestPreHitEventData.enemy;
                }
                else if(eventHub.GetCachedEventData(out EnemyGetHurt latestEnemyGetHurt)){
                    binfo.SetSourceWeapon(latestEnemyGetHurt.hurtInfo.SourceWeapon);
                    latestHitTarget = latestEnemyGetHurt.enemy;
                }
                dinfo.SetCamp(_controller.camp);
                if(dirType == AimDirectionType.None){
                    binfo.SetAngle(_controller.rg_random.Range(-deviate, deviate) + dirFix);
                }
                else if(dirType == AimDirectionType.OnwerDir){
                    binfo.SetAngle(Vector2.SignedAngle(Vector2.right, _controller.latestAimDir) + _controller.rg_random.Range(-deviate, deviate) + dirFix);
                }
                else if(dirType == AimDirectionType.MoveDir){
                    binfo.SetAngle(Vector2.SignedAngle(Vector2.right, _controller.latestMoveDir) + _controller.rg_random.Range(-deviate, deviate) + dirFix);
                }
                else if(dirType == AimDirectionType.OwnerDirHorizontal){
                    binfo.SetAngle((_controller.facing == 1 ? 0 : 180) + _controller.rg_random.Range(-deviate, deviate) + dirFix);
                }
                else if(dirType == AimDirectionType.RandomEnemy){
                    var enemies = _controller.FindEnemies(14, false);
                    if(enemies.Count > 0){
                        var enemy = enemies[_controller.rg_random.Range(0, enemies.Count)];
                        binfo.SetAngle(Vector2.SignedAngle(Vector2.right, enemy.transform.position - emitPos) + _controller.rg_random.Range(-deviate, deviate) + dirFix);
                        if(latestHitTarget != null && Object.Equals(latestHitTarget, enemy)){
                            binfo.SetAngle(RandomRange01() * 360f + dirFix);
                        }
                        emitPosCalculationArg = enemy;
                    }
                    else{
                        binfo.SetAngle(RandomRange01() * 360f + dirFix);
                    }
                }
                else if(dirType == AimDirectionType.Random){
                    binfo.SetAngle(RandomRange01() * 360f + dirFix);
                }

                if(SpecifyEmitPosition != null){
                    emitPos = SpecifyEmitPosition(emitPos, binfo.directionAngle, this, emitPosCalculationArg);
                }
                binfo.SetPosition(emitPos);

                Transform parent = null;
                if(dinfo.damageType.HasFlag(emDamageType.Melle)){
                    parent = _controller.transform;
                }
                try {
                    var bullet = BulletFactory.TakeBullet(binfo, dinfo, false, parent);
                    if (bullet != null) {
                        var dmgCarrier = bullet.GetComponent<DamageCarrier>();
                        bullet.transform.position = binfo.createPosition;

                        if (latestHitTarget != null && latestHitTarget is RGEController ectrl) {
                            // ingore latest hit enemy
                            dmgCarrier.bulletInfo.IgnoreTarget(ectrl.gameObject);
                            RGGameProcess.StartCommand(CommandUtil.CommandDuration.Delay(1).SetFinCallback(() => {
                                if (dmgCarrier != null && ectrl != null) {
                                    dmgCarrier.bulletInfo.ClearIgnoreTargets();
                                }
                            }));
                        }
                        onBulletEmit?.Invoke(bullet);
                        BulletFactory.OnBulletTaken(bullet);
                        _controller.OnBulletCreate(bullet);
                    }
                    return bullet;
                }
                catch (Exception e) {
                    BuglyUtil.ReportException("RGControllerTarget.EmitBullet", e);
                }
                return null;
            }

            public void AddBuff(Buff buff){
                _controller.buffMgr.AddBuff(buff);
            }

            public void AddBuff(RGBuff buff){
                _controller.buffMgr.AddBuff(buff);
            }

            public bool HasBuff(RGBuff buff) {
                return _controller.buffMgr.HasBuff(buff);
            }

            public bool HasBuff(int buffTypeId){
                return _controller.buffMgr.FindBuff(buffTypeId) != null;
            }

            public bool HasBuff<T>() where T : RGBuff{
                return _controller.buffMgr.HasBuff<T>();
            }

            public Buff FindBuff(int buffTypeId){
                return _controller.buffMgr.FindBuff(buffTypeId) as Buff;
            }

            public IBuff[] FindBuffs(System.Func<IBuff, bool> pred){
                return _controller.buffMgr.FindBuffs(pred);
            }

            public T FindBuff<T>() where T : RGBuff{
                return _controller.buffMgr.FindBuff<T>();
            }
            
            public int SearchBuffs(System.Func<object, bool> pred) {
                return _controller.buffMgr.SearchBuffs(pred);
            }

            public void RemoveBuff(int buffTypeId){
                _controller.buffMgr.RemoveBuffs(buffTypeId);
            }

            public void RemoveBuff(IBuff buff) {
                _controller.buffMgr.RemoveBuff(buff);
            }

            public void RemoveBuff(RGBuff buff){
                _controller.buffMgr.RemoveBuff(buff);
            }

            public void RemoveRGBuffs(System.Func<RGBuff, bool> predicate){
                _controller.buffMgr.RemoveRGBuffs(predicate);
            }

            public void RemoveBuffs(System.Func<IBuff, bool> predicate){
                _controller.buffMgr.RemoveBuffs(predicate);
            }

            public float RandomRange01(){
                return _controller.rg_random.Range(0, 1f);
            }

            public void GetHurt(HurtInfo hurtInfo){
                _controller.GetHurt(hurtInfo);
            }

            public RGWeapon Weapon(){
                return (_controller is RGController rgController && rgController.hand != null) ? rgController.hand.front_weapon : null;
            }

            public void StopAttack(){
                if(_controller is RGController rgController){
                    rgController.BtnAtkClick(false);
                }
            }

            public float RandomRange(float min, float max){
                return _controller.rg_random.Range(min, max);
            }
        }

        class RGEControllerTarget : IBuffFuncTarget{
            RGEController _controller;
            public IBuffFuncTarget GetTarget(object rawObj){
                return GetBuffTarget(rawObj);
            }
            public object rawObject => _controller;
            public int targetCamp => _controller.camp;
            BuffFuncEventHub _eventHub = new BuffFuncEventHub();
            public Transform transform => _controller != null ? _controller.transform : null;
            public Transform body => _controller.transform.Find("img");
            public bool dead => _controller.dead;
            public bool isAwake => _controller.awake;
            public GameObject gameObject => transform != null ? transform.gameObject : null;
            public BuffFuncEventHub eventHub => _eventHub;
            public RoleAttributeProxy attribute => _controller.attributeProxy;

            public RGEControllerTarget(RGEController controller){
                _controller = controller;
                _eventHub = new BuffFuncEventHub();
            }
            public GameObject EmitBullet(BulletInfo bulletInfo, DamageInfo damageInfo, float deviate, AimDirectionType dirType, float dirFix, System.Func<Vector3, float, IBuffFuncTarget, object, Vector3> specifyEmitPosition, System.Action<GameObject> onBulletEmit){
                return null;
            }

            public void AddBuff(Buff buff){
                _controller.buffMgr.AddBuff(buff);
            }

            public void AddBuff(RGBuff buff){
                _controller.buffMgr.AddBuff(buff);
            }

            public bool HasBuff(RGBuff buff) {
                return _controller.buffMgr.HasBuff(buff);
            }

            public bool HasBuff(int buffTypeId){
                return _controller.buffMgr.FindBuff(buffTypeId) != null;
            }

            public bool HasBuff<T>() where T : RGBuff{
                return _controller.buffMgr.HasBuff<T>();
            }

            public Buff FindBuff(int buffTypeId){
                return _controller.buffMgr.FindBuff(buffTypeId) as Buff;
            }

            public IBuff[] FindBuffs(System.Func<IBuff, bool> pred){
                return _controller.buffMgr.FindBuffs(pred);
            }

            public int SearchBuffs(System.Func<object, bool> pred) {
                return _controller.buffMgr.SearchBuffs(pred);
            }

            public T FindBuff<T>() where T : RGBuff{
                return _controller.buffMgr.FindBuff<T>();
            }

            public void RemoveBuff(int buffTypeId){
                _controller.buffMgr.RemoveBuffs(buffTypeId);
            }

            public void RemoveBuff(IBuff buff) {
                _controller.buffMgr.RemoveBuff(buff);
            }

            public void RemoveBuff(RGBuff buff){
                _controller.buffMgr.RemoveBuff(buff);
            }

            public void RemoveRGBuffs(System.Func<RGBuff, bool> predicate){
                _controller.buffMgr.RemoveRGBuffs(predicate);
            }

            public void RemoveBuffs(System.Func<IBuff, bool> predicate){
                _controller.buffMgr.RemoveBuffs(predicate);
            }

            public float RandomRange01(){
                return _controller.rg_random.Range(0, 1f);
            }
            public void GetHurt(HurtInfo hurtInfo){
                DamageCarrierHelper.ProcessEnemyGetHurtDamage(_controller, hurtInfo);
            }

            public RGWeapon Weapon(){
                return null;
            }

            public void StopAttack(){
                _controller.ResetAI();
            }

            public float RandomRange(float min, float max){
                return _controller.rg_random.Range(min, max);
            }
        }

    }
}