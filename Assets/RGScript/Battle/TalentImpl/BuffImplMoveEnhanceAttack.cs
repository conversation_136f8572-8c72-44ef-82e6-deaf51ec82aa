using System.Collections.Generic;
using UnityEngine;

namespace RGScript.Battle {
    public partial class TalentBuff {
        //子弹分裂 ExtraEmit
        static BuffFuncNode InitBuffMoveEnhanceAttack(RGController controller, emBuff buff) {
            return new BuffMoveEnhanceAttack().Setup(controller, buff);
        }

        class BuffMoveEnhanceAttack {
            RGController controller;
            bool isUpgraded;
            BuffStackCfgMoveEnhanceAttack.Param param;
            float accumulatedDistance;
            Vector2 lastPos;
            emBuff buffId;
            public BuffFuncNode Setup(RGController controller, emBuff buff) {
                isUpgraded = controller.thisBattleData.GetBuffUpgradeTimes(buff) > 0;
                this.controller = controller;
                this.buffId = buff;
                controller.thisBattleData.GetBuffRuntimeData("MoveEnhanceAttack_dist", out int distInt);
                accumulatedDistance = distInt / 1000f;
                lastPos = GetCurrentPos();
                param = BuffStackCfgMoveEnhanceAttack.GetParam(6, 3, 0, 0.5f, 1);

                var monitor = new BuffFuncNodeMoniter {
                    onUpdate = Update
                };

                var createBulletEventNode = new BuffFuncOnEvent<RGBaseControllerCreateBulletEvent> {
                    onEvent = OnCreateBullet
                };

                var playerBulletPreHitEventNode = new BuffFuncOnEvent<PlayerBulletPreHitEnemyEvent> {
                    onEvent = OnBulletPreHit
                };

                var killingEventNode = new BuffFuncOnEvent<KillEnemyEvent> { 
                    onEvent = OnKill
                };

                var displayInfo = new BuffDisplayBasicInfo {
                    name = "MoveEnhanceAttack",
                    iconName = "level_buff_move_enhance_atk",
                    descKey = "Buff_info_2106",
                    descParams = new string[] { param.addDamage.ToString() },
                    defaultDesc = GetDesc(),
                    onFetchCounter = FetchCount
                };

                return new BuffFuncComposite(monitor, createBulletEventNode, playerBulletPreHitEventNode, killingEventNode).AsRGBuff(BuffUtil.GetSubBuffId((int)buff, 1), displayInfo, false, -1, false, true);
            }

            int FetchCount() {
                return Mathf.RoundToInt(F() * 100);
            }

            string GetDesc() {
                if (isUpgraded) {
                    return GetUpgradeMoveEnhanceAttackDesc($"Buff_upgrade_{(int)buffId}");
                }
                return string.Format("移动时积攒动能，下一次攻击命中时消耗全部动能造成最多{0}额外伤害，动能攒满时必定暴击并眩晕目标。", param.addDamage);
            }

            void Update(float dt) {
                var p = GetCurrentPos();
                var disp = p - lastPos;
                lastPos = p;
                var delta = disp.magnitude;
                if (delta > 0) {
                    if (delta > 1) {
                        delta = 1;
                    }
                    accumulatedDistance += delta;
                    if (accumulatedDistance > param.distance) {
                        accumulatedDistance = param.distance;
                    }
                    controller.thisBattleData.SetBuffRuntimeData("MoveEnhanceAttack_dist", (int)(accumulatedDistance * 1000));
                }
            }

            Vector2 GetCurrentPos() {
                var p = controller.transform.position.Vec2();
                if (controller.mount != null) {
                    p = controller.mount.transform.position;
                }
                return p;
            }

            float F() {
                var f = accumulatedDistance / param.distance;
                f = 1 - Mathf.Pow(1 - f, 2);
                return f;
            }

            bool OnCreateBullet(RGBaseControllerCreateBulletEvent evData) {
                if (evData.bullet != null &&
                           evData.owner is RGController rgController &&
                           rgController == controller &&
                           !(evData.bullet.GetComponent<DamageCarrier>() is RGLaser) &&
                           !(evData.bullet.GetComponent<DamageCarrier>() is BezierLaser)) {

                    var f = F();
                    var addDamage = Mathf.RoundToInt(param.addDamage * f);
                    var addBulletScale = param.addBulletScale * f;
                    var addCritic = 0;

                    if (accumulatedDistance >= param.distance) {
                        // 眩晕，暴击
                        addCritic = 1000; // 某些武器暴击率是负数，+1000保证暴击
                        if (evData.bullet.GetComponent<DamageCarrier>() is var dmgCarrier && dmgCarrier != null) {
                            dmgCarrier.AddHitCallback(OnHitTargetWithFullEnergy);
                        }
                    }
                    ModifyBulletHelper.ModifyBullet(evData.bullet, addDamage, 0, addCritic, addBulletScale, GetHashCode().ToString());
                    accumulatedDistance = 0;
                    controller.thisBattleData.SetBuffRuntimeData("MoveEnhanceAttack_dist", (int)(accumulatedDistance * 1000));
                    return true;
                }
                return false;
            }

            bool OnBulletPreHit(PlayerBulletPreHitEnemyEvent e) {
                if ((e.damageCarrier is RGLaser || e.damageCarrier is BezierLaser) &&
                    e.sourceObj == controller.gameObject && accumulatedDistance >= param.distance) {
                    var addDamage = Mathf.RoundToInt(param.addDamage * F());
                    e.ModifyDamage(addDamage, Cicada.AttributeModifierType.Flat);
                    e.ModifyCritical(true, 10);
                    e.damageCarrier.AddHitCallback(OnHitTargetWithFullEnergy);
                    accumulatedDistance = 0;
                    return true;
                }
                return false;
            }

            void OnHitTargetWithFullEnergy(DamageCarrier bullet, GameObject target, BulletHitData hitData) {
                var buffTarget = BuffUtil.GetBuffTarget(target);
                if (buffTarget != null) {
                    BuffUtil.AddCommonBuff(controller != null ? controller.gameObject : null, buffTarget, CommonBuffTypeID.Dizzy, param.dizzyTime);
                }
            }

            float _lastFastChargeTime;
            bool OnKill(KillEnemyEvent e) {
                if (e.controller != controller || !isUpgraded || Time.timeSinceLevelLoad - _lastFastChargeTime < FastChargeCooldown()) {
                    return false;
                }
                _lastFastChargeTime = Time.timeSinceLevelLoad;
                accumulatedDistance = param.distance;
                controller.thisBattleData.SetBuffRuntimeData("MoveEnhanceAttack_dist", (int)(accumulatedDistance * 1000));
                return true;
            }

            public static float FastChargeCooldown() {
                return 2;
            }

        }

        public static string GetUpgradeMoveEnhanceAttackDesc(string descKey) {
            return string.Format(I2.Loc.ScriptLocalization.Get(descKey, "#移动时积攒动能，下一次攻击消耗动能造成额外伤害，满能时必定暴击并眩晕目标。击败敌人时立即充满动能，冷却{0}秒。"), BuffMoveEnhanceAttack.FastChargeCooldown());
        }

    }
}