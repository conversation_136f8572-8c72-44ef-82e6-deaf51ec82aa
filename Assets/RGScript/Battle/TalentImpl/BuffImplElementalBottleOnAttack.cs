using UnityEngine;
using RGScript.Battle;
using System.Collections.Generic;

namespace TalentImpl
{
    public class BuffImplElementalBottleOnAttack {
        private RGController controller;
        private float checkDuration;
        private int energyThreshold;
        private float cooldown;
        private RGRandom rg_random;
        private float throwCooldownRemain;
        List<string> bulletResList = new();

        class EnergyStatInfo {
            public int consume;
            public float expire;
        }
        DisorderList<EnergyStatInfo> energyStatList = new(true);
        int energyConsumed;

        public BuffFuncNode Init(RGController controller, float checkDuration, int energyThreshold, float cooldown) {
            this.controller = controller;
            this.checkDuration = checkDuration;
            this.energyThreshold = energyThreshold;
            this.cooldown = cooldown;
            this.rg_random = new RGRandom(controller.rg_random.Range(0, 9999999));
            throwCooldownRemain = 0;
            bulletResList.Add("RGPrefab/Bullet/bullet_bottle_troop_fire.prefab");
            bulletResList.Add("RGPrefab/Bullet/bullet_bottle_troop_gas.prefab");
            bulletResList.Add("RGPrefab/Bullet/bullet_bottle_troop_ice.prefab");

            var monitor = new BuffFuncNodeMoniter {
                onUpdate = OnUpdate
            };

            var attackNode = new BuffFuncOnEvent<RGBaseControllerCreateBulletEvent> {
                onEvent = OnEventAttack
            };

            var consumeEnergyNode = new BuffFuncOnEvent<EnergyChangedEvent> {
                onEvent = OnEnergyChanged
            };

            var ret = new BuffFuncComposite(monitor, attackNode, consumeEnergyNode);
            var buffInfo = BuffInfoTable.info.GetInfo(emBuff.ElementalBottleOnAttack);
            Sprite sprite = null;
            if (buffInfo != null) {
                sprite = buffInfo.icon;
            }
            return ret.AsRGBuff((int)emBuff.ElementalBottleOnAttack, new BuffDisplayBasicInfo {
                innerIcon = sprite,
                defaultDesc = RGScript.Util.NameUtil.GetBuffDesc(emBuff.ElementalBottleOnAttack, 1),
                onFetchCounter = EmitCount,
                onFetchCooldownProgress = CooldownProgress
            }, false, -1, false, true);
        }

        int EmitCount() {
            return energyConsumed / energyThreshold + 1;
        }

        float CooldownProgress() {
            return throwCooldownRemain / cooldown;
        }

        void OnUpdate(float dt) {
            energyConsumed = 0;
            for (var i = energyStatList.Count - 1; i >= 0; --i) {
                var info = energyStatList[i];
                info.expire -= dt;
                if (info.expire <= 0) {
                    energyStatList.RemoveAt(i);
                } else {
                    energyConsumed += info.consume;
                }
            }

            if (throwCooldownRemain >= 0) {
                throwCooldownRemain -= dt;
            }
        }

        bool OnEnergyChanged(EnergyChangedEvent evData) {
            if (evData.rgController != controller) {
                return false;
            }

            if (evData.value > 0) {
                return false;
            }

            OnEnergyConsume(-evData.value);

            return true;
        }

        bool OnEventAttack(RGBaseControllerCreateBulletEvent evData) {
            if (!controller.Equals(evData.owner))
                return false;

            if (throwCooldownRemain >= 0)
                return false;

            throwCooldownRemain = cooldown;

            ThrowElementalBottles(EmitCount());
            return true;
        }

        void ThrowElementalBottles(int count) {
            for (int i = 0; i < count; i++) {
                var bulletInfo = new BulletInfo().SetCamp(controller.camp).SetBullet(GetBulletPrefab()).SetSourceObject(controller.gameObject).
                    SetPosition(controller.transform.position + Vector3.up * 0.5f).SetAngle(controller.oriantationAngle + rg_random.Range(-8, 8)).
                    SetSpeed(12).SetBulletSize(1);
                var damageInfo = new DamageInfo().SetDamage(4).SetCamp(controller.camp);
                RGGameProcess.StartTimer(i * 0.1f, new FuncObj(this, nameof(Emit), bulletInfo, damageInfo));
            }
        }

        void Emit(BulletInfo bulletInfo, DamageInfo damageInfo) {
            BulletFactory.TakeBullet(bulletInfo, damageInfo);
        }

        GameObject GetBulletPrefab() {
            return ResourcesUtil.Load<GameObject>(bulletResList[rg_random.Range(0, bulletResList.Count - 1)]);
        }

        public void OnEnergyConsume(int amount) {
            energyStatList.Add(new EnergyStatInfo {
                consume = amount,
                expire = checkDuration
            });
        }
    }
}