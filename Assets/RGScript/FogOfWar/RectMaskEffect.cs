using System.Collections.Generic;
using UnityEngine;

namespace FogOfWar {
    [ExecuteInEditMode]
    [RequireComponent(typeof(Camera))]
    public class RectMaskEffect : MonoBehaviour {
        public List<Vector4> rects = new List<Vector4>();
        public Material rectMaskMaterial;
        private readonly Vector4[] _screenRect = new Vector4[16];
        private Camera _camera;
        private static readonly int ShaderIdMaskRects = Shader.PropertyToID("_MaskRects");
        private static readonly int ShaderIdRectCount = Shader.PropertyToID("_RectCount");

        private void OnRenderImage(RenderTexture src, RenderTexture dest) {
            if (_camera == null) {
                _camera = GetComponent<Camera>();
            }
            if (rectMaskMaterial != null && rects.Count > 0) {
                for (var i = 0; i < rects.Count; i++) {
                    var vector4 = rects[i];
                    var from = _camera.WorldToViewportPoint(new Vector3(vector4.x, vector4.y));
                    var to = _camera.WorldToViewportPoint(new Vector3(vector4.z, vector4.w));
                    _screenRect[i] = new Vector4(from.x, from.y, to.x, to.y);
                }

                rectMaskMaterial.SetVectorArray(ShaderIdMaskRects, _screenRect);
                rectMaskMaterial.SetInt(ShaderIdRectCount, rects.Count);
                Graphics.Blit(src, dest, rectMaskMaterial);
            } else {
                Graphics.Blit(src, dest);
            }
        }
    }
}
