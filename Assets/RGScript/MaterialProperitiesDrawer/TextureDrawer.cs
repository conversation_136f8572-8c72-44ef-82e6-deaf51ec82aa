#if UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System;

public class TextureDrawer : MaterialPropertyDrawerEX
{
    private enum TextureMode
    {
        Normal,
        NoScaleOffset,
        NoScale,
        NoOffset,
        NonModifiableTexture,
        NonModifiable,
    }
    private bool isDisableScale
    {
        get { return this.mode == TextureMode.NonModifiable || this.mode == TextureMode.NoScaleOffset || this.mode == TextureMode.NoScale; }
    }
    private bool isDisableOffset
    {
        get { return this.mode == TextureMode.NonModifiable || this.mode == TextureMode.NoScaleOffset || this.mode == TextureMode.NoOffset; }
    }
    private bool isDisableTexture
    {
        get { return this.mode == TextureMode.NonModifiable || this.mode == TextureMode.NonModifiableTexture; }
    }
    private TextureMode mode = TextureMode.Normal;
    public TextureDrawer() {}
    public TextureDrawer(string mode = "Normal")
    {
        Enum.TryParse<TextureMode>(mode, out this.mode);
    }
    private float height = GetMultipleLinesHeight(3);
    public override void OnGUIWithResetDrawerState(Rect position, MaterialProperty prop, GUIContent label, MaterialEditor editor)
    {
        position = position.SetHeight(height);

        // Values is Changed Highlight
        DrawPropertyValueChangedNotificationBackground(position);

        Material mat = (Material)editor.target;
        Rect leftPropertiesRect = GetLeftPropertiesRect(position, GetRightAlignedRect(position, position.height), 3);
        // Label
        EditorGUI.LabelField(VerticalSplitRect(leftPropertiesRect, 3, 0), label);
        // Scale And Offset
        Vector2 scale = mat.GetTextureScale(prop.name);
        Vector2 offset = mat.GetTextureOffset(prop.name);
        float labelWidth = 70f + EditorGUI.indentLevel * 15f;


        // Tiling
        if(isDisableScale) GUI.enabled = false;
        // Tiling Label
        EditorGUI.LabelField(GetSplitScaleOrOffsetLabelRect(VerticalSplitRect(leftPropertiesRect, 3, 1), labelWidth, 10), new GUIContent("Tiling"));
        // Tiling Field
        IgnoreIndentLevel();
        EditorGUI.BeginChangeCheck();
        scale = EditorGUI.Vector2Field(GetSplitScaleOrOffsetFieldRect(VerticalSplitRect(leftPropertiesRect, 3, 1), labelWidth), GUIContent.none, scale);
        if(EditorGUI.EndChangeCheck())
        {
            Undo.RecordObject(mat, "Changed material texture tiling");
            mat.SetTextureScale(prop.name, scale);
            PropertyValueChangeCheck();
        }
        RestoreIndentLevel();
        if(isDisableScale)  GUI.enabled = true;


        // Offset
        if(isDisableOffset) GUI.enabled = false;
        // Offset Label
        EditorGUI.LabelField(GetSplitScaleOrOffsetLabelRect(VerticalSplitRect(leftPropertiesRect, 3, 2), labelWidth, 10), new GUIContent("Offset"));
        // Offset Field
        IgnoreIndentLevel();
        EditorGUI.BeginChangeCheck();
        offset = EditorGUI.Vector2Field(GetSplitScaleOrOffsetFieldRect(VerticalSplitRect(leftPropertiesRect, 3, 2), labelWidth), GUIContent.none, offset);
        if(EditorGUI.EndChangeCheck())
        {
            Undo.RecordObject(mat, "Changed material texture offset");
            mat.SetTextureOffset(prop.name, offset);
            PropertyValueChangeCheck();
        }
        RestoreIndentLevel();
        if(isDisableOffset) GUI.enabled = true;

        // Texture
        IgnoreIndentLevel();
        if(isDisableTexture)
        {
            if(prop.textureValue == null)
                GUI.Box(GetRightAlignedRect(position, position.height), new GUIContent(EditorGUIUtility.IconContent("Texture Icon").image));
            else
            {
                GUI.enabled = false;
                EditorGUI.ObjectField(GetRightAlignedRect(position, position.height), GUIContent.none, prop.textureValue, typeof(Texture), true);
                GUI.enabled = true;
            } 
        }
        else
        {
            prop.textureValue = (Texture)EditorGUI.ObjectField(GetRightAlignedRect(position, position.height), GUIContent.none, prop.textureValue, typeof(Texture), true);
        }
        RestoreIndentLevel();
    } 

    public override float GetPropertyHeight(MaterialProperty prop, string label, MaterialEditor editor)
    {
        return height + EditorGUIUtility.standardVerticalSpacing;
    }

    private Rect GetLeftPropertiesRect(Rect rect, Rect textureRect, float border)
    {
        Rect result = rect;
        result.width -= textureRect.width + border;
        return result;
    }

    private Rect GetSplitScaleOrOffsetFieldRect(Rect rect, float labelWidth)
    {
        return rect.SetWidth(rect.width - labelWidth).Offset(new Vector2(labelWidth, 0));;
    }

    private Rect GetSplitScaleOrOffsetLabelRect(Rect rect, float labelWidth, float leftBorder)
    {
        return rect.SetWidth(labelWidth - leftBorder).Offset(new Vector2(leftBorder, 0));
    }
}
#endif