using UnityEngine;

public class RoomGoldCoinReward : HideRoomContent {
    public override string ContentKey => "r_coins_reward";
    
    public Vector2Int totalGoldCoin = new Vector2Int(80, 100);
    public int goldMineCount = 3;
    
    private GameObject _blockPrefab;
    private GameObject _movingCoinPrefab;

    protected override void BeforeRoomCreateHandler(RGRoomX room) {
        base.BeforeRoomCreateHandler(room);
        var random = room.rg_random;
        var coins = random.Range(totalGoldCoin.x, totalGoldCoin.y);

        room.Grid.ProhibitedZone.AddPoints(new RectInt(0, 0, 2, room.room_height));
        room.Grid.ProhibitedZone.AddPoints(new RectInt(room.room_width - 2, 0, 2, room.room_height));
        room.Grid.ProhibitedZone.AddPoints(new RectInt(0, 0, room.room_width, 2));
        room.Grid.ProhibitedZone.AddPoints(new RectInt(0, room.room_height - 2, room.room_width, 2));

        for (var i = 0; i < goldMineCount; ++i) {
            var (pos, placeable) = room.Grid.GetRandomPlaceableGridPosition(new Vector2Int(2, 2), false, room.rg_random);
            if (!placeable) {
                break;
            }
            room.Grid.ProhibitedZone.AddPoints(new RectInt(pos, new Vector2Int(2,2)));
            GameObject prefab = ResourcesUtil.LoadEnemy("mineral_gold_special");
            var mine = Instantiate(prefab, room.transform, true);
            mine.name = $"{transform.name}_g_{i}";
            mine.transform.localPosition = room.Grid.GridPos2LocalPos(pos) + new Vector3(1, 0, 0) +
                                           new Vector3(room.rg_random.Range(-0.5f, 0.5f), room.rg_random.Range(-0.5f, 0.5f), 0);
            mine.transform.localScale = Vector3.one * room.rg_random.Range(0.9f, 1.1f);
            mine.GetComponent<GoldMineSpecial>().dropCoins = coins / goldMineCount;
        }

        RGGameProcess.StartTimer(1, room.OpenDoor);
    }
}
