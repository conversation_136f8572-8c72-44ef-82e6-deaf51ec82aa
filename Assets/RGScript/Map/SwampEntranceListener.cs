using RGScript.Map;
using SoulKnight.Runtime.Enemy;
using System.Collections;
using UnityEngine;

public class SwampEntranceListener : MonoBehaviour {
    public const string BossName = "boss25";
    public int bossRoomType = 2 ;
    public GameObject entranceGatePrefab;
    public Vector3 originalGatePosition;
    public Vector3 swampGatePosition;
    private RGRoomX _entranceBossRoom;
    private RGRoomX _originalBossRoom;
    private readonly ISwampSacrificeRecorder _swampSacrificeRecorder = SwampSacrificeRecordFactory.GetRecorder();
    // 实际生效的传送门
    private GameObject _entranceTargetGate;

    private bool _hasChangeBossRoom;
    private void Awake() {
        _swampSacrificeRecorder.AfterAddRecord += AfterSwampRecord;
    }

    private void OnDestroy() {
        _swampSacrificeRecorder.AfterAddRecord -= AfterSwampRecord;
        StopAllCoroutines();
    }

    public void AfterRoomCreated(MapManagerLevel mapManagerLevel) {
        foreach (var room in mapManagerLevel.AllRoom) {
            if (room.room_type != bossRoomType) {
                continue;
            }
            _originalBossRoom = room;
            if (_swampSacrificeRecorder.HasEnoughWeapon()) {
                _swampSacrificeRecorder.ClearRecord();
                StartCoroutine(InitBossRoom());
            }
        }
    }

    private void EntranceShowGate() {
        if (_entranceTargetGate) {
            _entranceTargetGate.SetActive(true);
        }
    }

    private void EntranceInstantiateGate(GameObject gate) {
        _entranceTargetGate = Instantiate(entranceGatePrefab, gate.transform.parent);
        _entranceTargetGate.SetActive(false);
        _entranceTargetGate.transform.localPosition = swampGatePosition;
        gate.transform.localPosition = originalGatePosition;
    }

    private void AfterSwampRecord() {
        if (BattleData.data.CompareFactor(emBattleFactor.SleepWalking)) {
            return;
        }
        if (_swampSacrificeRecorder.HasEnoughWeapon()) {
            StartCoroutine(InitBossRoom());
        }
    }

    private IEnumerator InitBossRoom() {
        if (_originalBossRoom == null) {
            yield break;
        }
        if (_hasChangeBossRoom) {
            yield break;
        }
        
        var bossData = EnemyConfigLoader.GetBossData(BossName);
        if (bossData == null) {
            Debug.LogError($"Cant find boss {BossName} data! Please check enemy editor config!");
            yield break;
        }

        string bossAbName = bossData.AssetBundle;
        yield return AssetBundleLoader.Inst.LoadBundle(bossAbName);
        
        string bossRoomPath = bossData.BossRoom;
        GameObject bossRoomPrefab = ResourcesUtil.Load<GameObject>(bossRoomPath);
        var bossRoomGo = MapManagerLevel.Instance.InstantiateExtraRoom(bossRoomPrefab, _originalBossRoom.room_x, _originalBossRoom.room_y);
        _entranceBossRoom = bossRoomGo.GetComponent<RGRoomX>();
        
        if (_entranceBossRoom == null) {
            yield break;
        }
        
        var bossCreator = bossRoomGo.GetComponentInChildren<BossCreator>();
        bossCreator.OnInstantiateGate += EntranceInstantiateGate;
        bossCreator.OnShowGate += EntranceShowGate;
        
        _entranceBossRoom.createType = _originalBossRoom.createType;
        _entranceBossRoom.hasBoss = _originalBossRoom.hasBoss;
        _entranceBossRoom.room_type = _originalBossRoom.room_type;
        
        _swampSacrificeRecorder.ClearRecord();
        _swampSacrificeRecorder.BossCreated();
        _entranceBossRoom.gameObject.SetActive(true);
        _originalBossRoom.gameObject.SetActive(false);
        var boss = _originalBossRoom.GetComponentInChildren<RGEBossController>(true);
        if (boss != null && boss.boss_info) {
            Destroy(boss.boss_info.gameObject);
        }
        _hasChangeBossRoom = true;
    }
}
