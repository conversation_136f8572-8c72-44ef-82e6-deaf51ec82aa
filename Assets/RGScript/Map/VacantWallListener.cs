using System;
using UnityEngine;

namespace VacantWall {
    public class VacantWallListener : MonoBehaviour {
        [NonSerialized]
        public RGRoomX attachedRoom;
        [NonSerialized]
        public RGAisle attachedAisle;

        public static event Action<EnterVacantWall> onTriggerEnter;

        // ReSharper disable once Unity.RedundantEventFunction
        private void Awake() {
        }
    
        private void OnTriggerEnter2D(Collider2D other) {
            onTriggerEnter?.Invoke(new EnterVacantWall() {
                attachedRoom = attachedRoom,
                attachedAisle = attachedAisle,
                collider2D = other
            });
        }
    }
    

    public struct EnterVacantWall {
        public RGRoomX attachedRoom;
        public RGAisle attachedAisle;
        public Collider2D collider2D;
    }
}