using Activities.Dream.Scripts;
using Activities.Fire.Scripts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using EnemyGenerator;
using EnemyRoomReward;
using I2.Loc;
using MapSystem;
using ModeLoopTravel.Scripts;
using Newtonsoft.Json;
using ObstacleProcessor;
using RGScript.Config.Manager;
using RGScript.DifficultyLevel;
using RGScript.Mode.ModeMapLevelController;
using RGScript.Net.StateSync.Core;
using RGScript.Net.StateSync.Core.Components;
using RGScript.WeaponAffix;
using RoomLayout;
using Sirenix.OdinInspector;
using SoulKnight.Runtime.Analyze;
using SoulKnight.Runtime.MapLevel;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.Serialization;

namespace RGScript.Map {
    public partial class MapManagerLevel : MapManager, IStateSync {
        [Serializable]
        public class EnemyInfo : IWeightObject {
            public GameObject enemy;
            public GameObject exEnemy;
            public int weight;
            public int Weight => weight;

            [ValueDropdown(nameof(AllConditions))]
            public List<string> generateConditions;

            public static List<string> AllConditions => EnemyInfoExt.EnemyConditions;
            public int maxCountPerWave = 0;
        }

        [Serializable]
        public class EnemyIDInfo : IWeightObject {
            public string enemyID;
            public string exEnemyID;
            public int weight;
            public int Weight => weight;

            [ValueDropdown(nameof(AllConditions))]
            public List<string> generateConditions;

            public static List<string> AllConditions => EnemyInfoExt.EnemyConditions;
            
            public EnemyInfo ToEnemyInfo() {
                EnemyInfo info = new EnemyInfo();
                info.weight = weight;
                info.generateConditions = generateConditions;
                if (!string.IsNullOrEmpty(enemyID)) {
                    info.enemy = ResourcesUtil.LoadEnemy(enemyID);
                }
                if (string.IsNullOrEmpty(exEnemyID)) {
                    info.exEnemy = ResourcesUtil.LoadEnemy(exEnemyID);
                }
                return info;
            }
        }

        // ReSharper disable once InconsistentNaming
        public static MapManagerLevel instance;
        public new static MapManagerLevel Instance => MapManager.Instance as MapManagerLevel;

        public static bool IsTestLevelConfigOpen;

        public static bool IsGenerateTestLevel {
            get {
                if (Application.isEditor) {
                    return IsTestLevelConfigOpen;
                }
                return IsTestLevelConfigOpen && GameConfigManager.GetInstance().Config.IsDebug;
            }
        }

        public MapManagerLevel BaseMng {
            get {
                if (_baseManager && _baseManager.GetComponent<BaseManagerSwitch>()) {
                    // 尝试替换根部MapManager
                    return _baseManager.GetComponent<BaseManagerSwitch>().GetManager();
                }
                return _baseManager;
            }
        }

        public string elementLibraryKey;

        public T GetComponentFromManager<T>() {
            var curManager = this;
            T ret;
            do {
                ret = curManager.GetComponent<T>();
                if (ret == null) {
                    curManager = curManager.BaseMng;
                }
            } while (ret == null && curManager != null);

            return ret;
        }

        [NonSerialized] private string _levelName;
        public string LevelName {
            get => _levelName ?? name;
            private set => _levelName = value;
        }

        // 提供特殊房间的基本配置
        // ReSharper disable once InconsistentNaming
        [FormerlySerializedAs("baseManager")]
        public MapManagerLevel _baseManager;

        // ReSharper disable once InconsistentNaming
        [TabGroup(tab: "关卡配置", order: 3)]
        public int chest_level;

        [NonSerialized]
        public int ThisRoomX = 2;

        [NonSerialized]
        public int ThisRoomY = 2;

        // 当前房间RGRoom
        [NonSerialized]
        public RGRoomX TheRoom;

        // 房间关系数组
        [NonSerialized]
        public int[,] RoomRelation = new int[5, 5];

        // ReSharper disable once InconsistentNaming
        public readonly RGRoomX[,] map = new RGRoomX[5, 5];

        // ReSharper disable once InconsistentNaming
        [TabGroup(tab: "关卡配置", order: 3)]
        [LabelText("主线长度")]
        public int map_long;

        // ReSharper disable once InconsistentNaming
        private readonly List<string> special_rooms = new List<string>();

        /// <summary>
        /// 房间配置传承方式
        /// </summary>
        public enum RoomListImpartType {
            All,
            OnlyBase,
            NotImpart
        }

        [LabelText("房间间距")]
        public int roomSpacing = 41;
        [LabelText("是否需要最后单独生成走廊")]
        public bool needSeparatelyGeneratedCorridor;

        // 初始房间
        // ReSharper disable once Unity.RedundantSerializeFieldAttribute
        // ReSharper disable once InconsistentNaming
        [LabelText("起始房间")]
        [TabGroup(tab: "房间配置", order: 1)]
        [SerializeField]
        private ObjectDistribution[] room_start_distributions;

        [LabelText("初始房间继承方式")]
        [TabGroup(tab: "房间配置", order: 1)]
        [ListDrawerSettings(ShowIndexLabels = true)]
        public RoomListImpartType startRoomImpartType = RoomListImpartType.All;

        // 传送门、boss房间
        // ReSharper disable once InconsistentNaming
        [LabelText("终点房间")]
        [SerializeField]
        [TabGroup(tab: "房间配置", order: 1)]
        private GameObject[] room_end_list;

        // 传送门、boss房间
        // ReSharper disable once Unity.RedundantSerializeFieldAttribute
        // ReSharper disable once InconsistentNaming
        [LabelText("额外终点房间")]
        [SerializeField]
        [TabGroup(tab: "房间配置", order: 1)]
        private ObjectDistribution[] extra_room_end_list;

        // ReSharper disable once Unity.RedundantSerializeFieldAttribute
        // ReSharper disable once InconsistentNaming
        [LabelText("宝箱房间")]
        [SerializeField]
        [TabGroup(tab: "房间配置", order: 1)]
        [ListDrawerSettings(ShowIndexLabels = true)]
        private ObjectDistribution[] room_chest_distributions;

        [LabelText("联机武器宝箱房间")]
        [TabGroup(tab: "房间配置", order: 1)]
        [ListDrawerSettings(ShowIndexLabels = true)]
        public GameObject coopChestRoom;

        [LabelText("宝箱房间继承方式")]
        [TabGroup(tab: "房间配置", order: 1)]
        [ListDrawerSettings(ShowIndexLabels = true)]
        public RoomListImpartType chestRoomImpartType = RoomListImpartType.All;

        // ReSharper disable once Unity.RedundantSerializeFieldAttribute
        // ReSharper disable once InconsistentNaming
        [LabelText("特殊房间")]
        [SerializeField]
        [TabGroup(tab: "房间配置", order: 1)]
        [ListDrawerSettings(ShowIndexLabels = true)]
        private ObjectDistribution[] room_special_distributions;

        [LabelText("特殊房间继承方式")]
        [TabGroup(tab: "房间配置", order: 1)]
        [ListDrawerSettings(ShowIndexLabels = true)]
        public RoomListImpartType specialRoomImpartType = RoomListImpartType.All;

        // ReSharper disable once InconsistentNaming
        [LabelText("地板")]
        [SerializeField]
        [TabGroup(tab: "场景配置", order: 2)]
        private GameObject[] floor_list;

        [TabGroup(tab: "场景配置", order: 2), PropertyOrder(1)]
        public bool useWeightedPattern;

        [LabelText("地板权重")]
        [TabGroup(tab: "场景配置", order: 2), PropertyOrder(1), ShowIf("useWeightedPattern")]
        public int[] floorWeight;

        [TabGroup(tab: "场景配置", order: 2), PropertyOrder(1), ShowIf("useWeightedPattern")]
        public int floorChangeNeedCount;

        [TabGroup(tab: "场景配置", order: 2), PropertyOrder(1), ShowIf("useWeightedPattern")]
        public float baseTileChangeRate;

        // ReSharper disable once InconsistentNaming
        [LabelText("墙体")]
        [SerializeField]
        [TabGroup(tab: "场景配置", order: 2)]
        private GameObject[] wall_list;

        // ReSharper disable once InconsistentNaming
        [LabelText("障碍与装饰")]
        [SerializeField]
        [TabGroup(tab: "场景配置", order: 2)]
        private GameObject[] obstacle_list;

        // 默认的是aisle_e aisle_n aisle_s aisle_w，其前缀就是 aisle。前作联动需要可配置，因此暴露出来
        [LabelText("自定义门")]
        public bool customAislePrefabPrefix;

        [TabGroup(tab: "场景配置", order: 2)]
        [LabelText("门 prefab 前缀"), ShowIf(nameof(customAislePrefabPrefix))]
        public string aislePrefabPrefix;
        
        [TabGroup(tab: "场景配置", order: 2)]
        [LabelText("门 prefab 路径"), ShowIf(nameof(customAislePrefabPrefix))]
        public string aislePrefabPath;

        [TabGroup(tab: "场景配置", order: 2)]
        [Title("勾选后添加实现ICustomObstacleList的脚本")]
        public bool useCustomObstacleList;

        // ReSharper disable once InconsistentNaming
        [TabGroup(tab: "场景配置", order: 2)]
        public bool use_new_floor_pattern;

        public bool UseNewFloorPattern => use_new_floor_pattern;
        
        // ReSharper disable once InconsistentNaming
        [TabGroup(tab: "关卡配置", order: 3), FormerlySerializedAs("enemy_infos")]
        public EnemyInfo[] enemy_config;

        public EnemyInfo[] EnemyInfos {
            get {
                var enemies = MapLevelConfigLoader.GetEnemies(LevelName);
                if (enemies == null || enemies.Count == 0) {
                    return enemy_config;
                }
                return enemies.ToArray();
            }
        }

        [TabGroup("关卡配置", Order = 4)]
        public UnityEvent onManagerInstantiate;

        [TabGroup("关卡配置"), LabelText("房间尺寸确定事件")]
        public RoomEvent onDetermineRoomSize;

        [TabGroup("关卡配置", Order = 4), LabelText("房间RoomLayout创建房间之后")]
        public RoomEvent onRoomCreated;

        [TabGroup("关卡配置"), LabelText("房间Start事件RoomLayout创建房间之前")]
        public RoomEvent beforeRoomCreated;

        [TabGroup("关卡配置", Order = 4), LabelText("所有房间都实列化完成")]
        public MapManagerLevelEvent afterRoomCreatedEvent;

        [TabGroup("关卡配置", Order = 4), LabelText("生成room pattern事件")]
        public MapManagerLevelEvent onAfterGenerateRoomPattern;

        [TabGroup("关卡配置"), LabelText("实列化房间事件")]
        public RoomEvent onRoomInstantiate;

        [TabGroup("关卡配置"), LabelText("实例化障碍物事件")]
        public RoomInstantiateObstacleEvent onRoomInstantiateObstacle;

        [FormerlySerializedAs("alwaysRandomRoom")] [LabelText("房间总是采用随机模板房间布局")]
        public bool alwaysUsePatternRoom;
        [LabelText("房间从不采用随机模板房间布局")]
        public bool neverUsePatternRoom;

        [NonSerialized]
        public IObstacleProcessor ObstacleProcessor;

        public override int ChestLevel => Mathf.Min(chest_level + GameUtil.ModeLoopTravelPressureLv() % 3, 6);

        public override string LevelText => GetLevelText(BattleData.data);

        #region 涉及基本房间的配置获取

        private bool IsPrefab => gameObject.scene.rootCount == 0;

        // 背景音乐获取
        public override AudioClip BGM {
            get {
                var bgm = MapLevelConfigLoader.GetBgm(LevelName);
                if (bgm) {
                    return bgm;
                }
                
                return bgm_clip ? bgm_clip : BaseMng ? BaseMng.BGM : null;
            }
        }

        public int CageCreateRate {
            get {
                int rate = MapLevelConfigLoader.GetCageCreateRate(LevelName);
                return rate != 0 ? rate : BaseMng.CageCreateRate;
            }
        }

        public int BranchCageCreateRate {
            get {
                int rate = MapLevelConfigLoader.GetBranchCageCreateRate(LevelName);
                return rate != 0 ? rate : BaseMng.BranchCageCreateRate;
            }
        }

        public override AudioClip GetLoopFxAudioClip() {
            var loopFx = MapLevelConfigLoader.GetLoopFx(LevelName);
            if (loopFx) {
                return loopFx;
            }
            
            return loopFxAudioClip ? loopFxAudioClip : BaseMng ? BaseMng.GetLoopFxAudioClip() : null;
        }

        // 特殊房间获取
        private ObjectDistribution[] _specialRooms;

        public ObjectDistribution[] SpecialRooms {
            get {
                if (!IsArrayEmpty(_specialRooms)) {
                    return _specialRooms;
                }
                
                List<ObjectDistribution> rooms;
                
                var specialRooms = MapLevelConfigLoader.GetSpecialRoomObjectDistributions(LevelName);
                if (specialRooms == null || specialRooms.Count == 0) {
                    room_special_distributions ??= Array.Empty<ObjectDistribution>();
                    rooms = new List<ObjectDistribution>(room_special_distributions);
                } else {
                    rooms = specialRooms;
                }

                if (BaseMng) {
                    switch (BaseMng.specialRoomImpartType) {
                        case RoomListImpartType.All:
                            rooms.AddRange(BaseMng.SpecialRooms);
                            break;
                        case RoomListImpartType.OnlyBase:
                            rooms.Clear();
                            rooms.AddRange(BaseMng.SpecialRooms);
                            break;
                        case RoomListImpartType.NotImpart:
                            break;
                        default:
                            throw new ArgumentOutOfRangeException();
                    }
                }

                // 清除指定值
                rooms.ForEach(room => {
                    var go = room.gameObject;
                    if (!go) {
                        return;
                    }
                    var randomObjects = room.gameObject.GetComponentsInChildren<RandomObject>(true).ToList();
                    randomObjects.ForEach(r => r.FixedSelectIndex = -1);
                });

                if (IsPrefab) {
                    return rooms.ToArray();
                }

                _specialRooms = rooms.ToArray();
                return _specialRooms;
            }
        }

        // 宝箱房间获取
        private ObjectDistribution[] _chestRooms;

        public RGRoomX GetClosestRoomToPlayer() {
            Vector2 playerPos = Vector2.zero;
            if (RGGameSceneManager.GetInstance() != null && RGGameSceneManager.GetInstance().controller != null) {
                playerPos = RGGameSceneManager.GetInstance().controller.transform.position.Vec2();
            }

            var closestRoom = GetClosestRoom(playerPos);
            return closestRoom;
        }
        
        public RGRoomX GetClosestRoom(Vector3 pos) {
            float closestD = Mathf.Infinity;
            RGRoomX closestRoom = null;
            foreach (var room in MapGenerator.RoomList) {
                Vector2 roomPos = room.transform.position.Vec2();
                float d = Vector2.Distance(pos, roomPos);
                if (!(d < closestD)) {
                    continue;
                }
                closestRoom = room;
                closestD = d;
            }

            return closestRoom;
        }

        public ObjectDistribution[] ChestRooms {
            get {
                if (!IsArrayEmpty(_chestRooms)) {
                    return _chestRooms;
                }

                List<ObjectDistribution> rooms;
                
                var chestRooms = MapLevelConfigLoader.GetChestRoomObjectDistributions(LevelName);
                if (chestRooms == null || chestRooms.Count == 0) {
                    room_chest_distributions ??= Array.Empty<ObjectDistribution>();
                    rooms = new List<ObjectDistribution>(room_chest_distributions);
                } else {
                    rooms = chestRooms;
                }
                
                if (BaseMng) {
                    switch (BaseMng.chestRoomImpartType) {
                        case RoomListImpartType.All:
                            rooms.AddRange(BaseMng.ChestRooms);
                            break;
                        case RoomListImpartType.OnlyBase:
                            rooms.Clear();
                            rooms.AddRange(BaseMng.ChestRooms);
                            break;
                        case RoomListImpartType.NotImpart:
                            break;
                        default:
                            throw new ArgumentOutOfRangeException();
                    }
                }
                // 清除指定值
                rooms.ForEach(room => {
                    var go = room.gameObject;
                    if (!go) {
                        return;
                    }
                    var randomObjects = room.gameObject.GetComponentsInChildren<RandomObject>(true).ToList();
                    randomObjects.ForEach(r => r.FixedSelectIndex = -1);
                });
                
                if (IsPrefab) {
                    return rooms.ToArray();
                }

                _chestRooms = rooms.ToArray();
                return _chestRooms;
            }
        }

        private GameObject CoopChestRoom {
            get {
                var chestRoom = MapLevelConfigLoader.GetCoopChestRoom(LevelName);
                if (chestRoom) {
                    return chestRoom;
                }
                
                return coopChestRoom ? coopChestRoom : BaseMng ? BaseMng.CoopChestRoom : null;
            }
        }

        // 开始房间获取
        // 宝箱房间获取
        private ObjectDistribution[] _startRooms;

        public ObjectDistribution[] StartRooms {
            get {
                if (!IsArrayEmpty(_startRooms)) {
                    return _startRooms;
                }

                var rooms = MapLevelConfigLoader.GetStartRoomObjectDistributions(LevelName);
                rooms ??= new List<ObjectDistribution>();
                if (rooms.Count == 0) {
                    if (!IsArrayEmpty(room_start_distributions)) {
                        rooms.AddRange(new List<ObjectDistribution>(room_start_distributions));
                    }
                }

                if (BaseMng) {
                    switch (BaseMng.startRoomImpartType) {
                        case RoomListImpartType.All:
                            rooms.AddRange(BaseMng.StartRooms);
                            break;
                        case RoomListImpartType.OnlyBase:
                            rooms.Clear();
                            rooms.AddRange(BaseMng.StartRooms);
                            break;
                        case RoomListImpartType.NotImpart:
                            break;
                        default:
                            throw new ArgumentOutOfRangeException();
                    }
                }

                if (IsPrefab) {
                    return rooms.ToArray();
                }

                _startRooms = rooms.ToArray();
                return _startRooms;
            }
        }

        public GameObject[] EndRooms {
            get {
                var endRooms = MapLevelConfigLoader.GetEndRoomsPrefab(LevelName);
                if (endRooms == null || endRooms.Count == 0) {
                    if (!IsArrayEmpty(room_end_list)) {
                        endRooms = room_end_list.ToList();
                    }
                }
                
                if (endRooms?.Count > 0) {
                    return endRooms.ToArray();
                }

                return endRooms is { Count: > 0 } ? endRooms.ToArray() : (BaseMng ? BaseMng.EndRooms : null);
            }
        }

        public List<string> EndRoomsName => MapLevelConfigLoader.GetEndRoomsName(LevelName);
        
        public List<string> EndRoomsPath => MapLevelConfigLoader.GetEndRoomsPath(LevelName);

        // 地板获取
        public GameObject[] Floors {
            // 取最近的地板
            get {
                var floorList = MapLevelConfigLoader.GetFloors(LevelName);
                if (floorList == null || floorList.Count == 0) {
                    if (!IsArrayEmpty(floor_list)) {
                        floorList = floor_list.ToList();
                    }
                }

                if (floorList?.Count > 0) {
                    return floorList.ToArray();
                }

                return BaseMng ? BaseMng.Floors : null;
            }
        }

        public GameObject[] Walls {
            get {
                var wallList = MapLevelConfigLoader.GetWalls(LevelName);
                if (wallList == null || wallList.Count == 0) {
                    if (!IsArrayEmpty(wall_list)) {
                        wallList = wall_list.ToList();
                    }
                }

                if (wallList?.Count > 0) {
                    return wallList.ToArray();
                }
                
                return BaseMng ? BaseMng.Walls : null;
            }
        }

        public GameObject[] Obstacles {
            get {
                var obstacleList = MapLevelConfigLoader.GetObstacles(LevelName);
                if (obstacleList == null || obstacleList.Count == 0) {
                    if (!IsArrayEmpty(obstacle_list)) {
                        obstacleList = obstacle_list.ToList();
                    }
                }
                
                if (obstacleList?.Count > 0) {
                    return obstacleList.ToArray();
                }
                
                return BaseMng ? BaseMng.Obstacles : null;
            }
        }

        private IEnemyRoomReward _enemyRoomReward;
        protected ICustomRoomLayout CustomRoomLayout;

        public ICustomRoomLayout GetCustomRoomLayout() {
            return CustomRoomLayout;
        }

        private static bool IsArrayEmpty(IReadOnlyCollection<GameObject> array) {
            return array == null || array.Count == 0;
        }

        private static bool IsArrayEmpty(IReadOnlyCollection<ObjectDistribution> array) {
            return array == null || array.Count == 0;
        }

        #endregion

        public static MapManagerLevel GetInstance() {
            if (!instance) {
                instance = (MapManagerLevel)FindObjectOfType(typeof(MapManagerLevel));
            }
            return instance;
        }
        
        #region StateSync

        public uint StateSyncId { get; set; }
        public bool IsStateSyncEnabled { get; set; }
        public bool IsCloseStrategy { get; set; }
        public StateEnum SyncState { get; set; }
        
        private ReadyComponent _readyComponent;
        
        public Dictionary<StateEnum, StateSyncStrategy> StateSyncStrategyConfig { get; set; } = new() {
            { StateEnum.Sync, StateSyncStrategy.SelfRoundTrip }
        };
        
        private void OnReady(StateEnum state, SortedDictionary<int, string> playersData) {
            string serverBranchIndex = string.Empty;
            foreach ((int playerId, string data) in playersData) {
                if (serverBranchIndex == string.Empty) {
                    serverBranchIndex = data;
                    continue;
                }
                
                if (serverBranchIndex != data) {
                    // Player not in same branch
                    StateSynchronizer.ServerChangeClientState(playerId, StateSyncId, StateEnum.Report);
                }
            }
        }
        
        public void PreServerProcessData(StateEnum state, int sourcePlayerIndex, ref string data) {
            _readyComponent.PreServerProcessData(state, sourcePlayerIndex, data);
        }
        
        public void OnStateChanged(int sourcePlayerIndex, string data) {
            if (SyncState != StateEnum.Report) {
                return;
            }

            Dictionary<uint, string> reportData = new();
            // ReSharper disable once LoopCanBeConvertedToQuery
            foreach (var netController in NetControllerManager.Instance.controllers) {
                reportData.Add(netController.netId,
                    $"{netController.battleData.BranchIndex} {netController.battleData.levelIndex}");
            }

            BuglyUtil.ReportException(
                "NotInSameBranch",
                JsonConvert.SerializeObject(reportData),
                RecordCallStack.GetRecordDataStr("SetBranchIndex"));
        }

        public bool CanChangeState(StateEnum state) {
            return true;
        }

        #endregion
        
        private void Awake() {
            this.RegisterStateSyncObject();
            _readyComponent = new ReadyComponent(this, OnReady);
            _readyComponent.ChangeStateLocalPlayer(StateEnum.Sync, BattleData.data.BranchIndex);
            
            // 执行玩法因子相关的初始化（例如赛季因子）
            BaseManagerSwitch.NormalModeInitByFactor();

            var extraResLoader = GetComponentFromManager<MapResloader>();
            extraResLoader?.StartLoadingRes();

            AllRoom = new List<RGRoomX>();
            var mainCamera = Camera.main;
            if (mainCamera != null) {
                var cameraTransform = mainCamera.transform;
                cameraTransform.position = new Vector3(CenterPoint.x, CenterPoint.y, cameraTransform.position.z) +
                                           new Vector3(-1, 0);
            }

            ObstacleProcessor = GetComponent<IObstacleProcessor>();
            onManagerInstantiate.Invoke();
            _enemyRoomReward = GetComponent<IEnemyRoomReward>();
            CustomRoomLayout = GetComponent<ICustomRoomLayout>();
            HandleRoomConfig();

            LevelName = name.Replace("(Clone)", "");
            RoomPatternMgr = new RoomPatternManager();
            RoomPatternMgr.LoadMapConfig();
            if (neverUsePatternRoom) {
                RoomPatternMgr.RandomPatternRoomProbability = 0;
            } else {
                if (BattleData.data.IsTroop || alwaysUsePatternRoom) {
                    RoomPatternMgr.RandomPatternRoomProbability = 100;
                } else {
                    RoomPatternMgr.RandomPatternRoomProbability =
                        NetControllerManager.Inst.playerCount <= 2 ? 50 :
                        NetControllerManager.Inst.playerCount <= 3 ? 60 : 70;
                }
            }

            DifficultyLevelManager.Init(this);
            WeaponAffixLevelManager.Init(this);

            MapGenerator = new MapStructureGenerator();
            MapGenerator.Init();
        }

        public MapStructureGenerator MapGenerator { get; protected set; }

        // 创建本层地图
        protected override void CreateMap() {
            SimpleEventManager.Raise(new MapManagerLevelCreateMapEvent());
            MapGenerator.CreateMapStructure();
            MapGenerator.CreateRooms();
        }

        public bool IsBranchRoom(RGRoomX roomX) => !MapGenerator.StartToEndShortestPath.Nodes.Contains(roomX);

        public int BossRoomRelationIndex => map_long + 1;
        public int InitializedRoomCount { get; set; }
        private bool _isRoomHasBoss;
        private bool _isRoomUsePattern;
        
        public virtual void OnAfterAllRoomsInitialized() {
            MapGenerator.AnalyzeMapAdjacencyMatrix();
            if (needSeparatelyGeneratedCorridor) {
                CustomRoomLayout?.CreateCorridor(MapGenerator.RoomMatrix.UndirectedEdges);
            }
            CreateSpecialRoomObjects();
            InitLevelRoomEnemy();
            afterRoomCreatedEvent.Invoke(this);
            SimpleEventManager.Raise(new MapCreateCompleteEvent { mapManager = this });
            
            UICanvas.GetInstance().InitNewMiniMap();
        }

        private bool _hasAllBadassRoom;
        protected virtual void InitLevelRoomEnemy() {
            foreach (var roomX in AllRoom) {
                if (roomX.createType != RoomCreateType.NormalRoom) {
                    continue;
                }
                
                var enemyMaker = roomX.GetComponent<EnemyMaker>();
                if (!enemyMaker) {
                    continue;
                }
                
                bool isAllBadAss; // 是否是全精英怪
                if (enemyMaker.all_badass) {
                    isAllBadAss = true;
                } else {
                    isAllBadAss = !_hasAllBadassRoom && IsBranchRoom(roomX) && RgRandom.GetBool(0.08f);
                }
                enemyMaker.AlwaysBadass = isAllBadAss;
                if (isAllBadAss) {
                    roomX.ChangeDoorsColor(true);
                    _hasAllBadassRoom = true;
                }

                roomX.InitRoomEnemy();
            }
        }

        private void CreateSpecialRoomObjects() {
            var specialObjectCreator = GetComponent<RoomSpecialObjectCreator>();
            var baseMapMgr = BaseMng;
            while (specialObjectCreator == null && baseMapMgr) {
                var mapMngLv = baseMapMgr;
                baseMapMgr = mapMngLv.BaseMng;
                specialObjectCreator = mapMngLv.GetComponent<RoomSpecialObjectCreator>();
            }

            if (specialObjectCreator == null) {
                return;
            }
            specialObjectCreator.CreateLevelSpecialObject(this);
        }

        private void UpdateRoomDirection(int vx, int vy, RGRoomX room) {
            if (vx > 0 && Mathf.Abs(RoomRelation[vx, vy] - RoomRelation[vx - 1, vy]) <= 1 &&
                RoomRelation[vx - 1, vy] > 0) {
                room.openDirection |= emDirection.Left;
            }

            if (vy > 0 && Mathf.Abs(RoomRelation[vx, vy] - RoomRelation[vx, vy - 1]) <= 1 &&
                RoomRelation[vx, vy - 1] > 0) {
                room.openDirection |= emDirection.Down;
            }

            if (vx < 4 && Mathf.Abs(RoomRelation[vx, vy] - RoomRelation[vx + 1, vy]) <= 1 &&
                RoomRelation[vx + 1, vy] > 0) {
                room.openDirection |= emDirection.Right;
            }

            if (vy < 4 && Mathf.Abs(RoomRelation[vx, vy] - RoomRelation[vx, vy + 1]) <= 1 &&
                RoomRelation[vx, vy + 1] > 0) {
                room.openDirection |= emDirection.Up;
            }
        }

        /// <summary>
        /// 在已经有房间的位置生成额外房间
        /// </summary>
        /// <param name="roomPrefab">房间Prefab</param>
        /// <param name="positionX">房间在生成矩阵中的位置x轴坐标</param>
        /// <param name="positionY">房间在生成矩阵中的位置y轴坐标</param>
        /// <returns></returns>
        public GameObject InstantiateExtraRoom(GameObject roomPrefab, int positionX, int positionY) {
            var roomObject = Instantiate(roomPrefab, transform);
            roomObject.transform.localPosition = new Vector3(roomSpacing * positionX, roomSpacing * positionY);
            roomObject.name = $"room_{positionX}_{positionY}_extra";
            var room = roomObject.GetComponent<RGRoomX>();
            UpdateRoomDirection(positionX, positionY, room);
            return roomObject;
        }

        /// <summary>
        /// 设置随机数种子、生成地图
        /// </summary>
        /// <param name="seed"></param>
        protected override void SetRgRandomSeed(int seed) {
            OnPreSetRandomSeed(seed);
            base.SetRgRandomSeed(seed);

            if (RGGameProcess.Inst.this_index <= 0) {
                return;
            }

            if (BattleData.data.IsNormalMode) {
                if (BattleData.data.CompareFactor(emBattleFactor.LongMap) && RGGameProcess.Inst.this_index > 0 &&
                    RGGameProcess.Inst.this_index < LevelSelector.GetMaxLevelIndex(emGameMode.Normal) &&
                    RgRandom.Range(0, 100) < 50) {
                    map_long += 1;
                }
            }

            CreateMap();

            if (BattleData.data.IsBossRushMode) {
                var levelIdx = BattleData.data.levelIndex;
                if (levelIdx >= LevelSelector.GetMaxLevelIndex(emGameMode.BossRush)) {
                    level = ScriptLocalization.Get($"item/br_talk {RGGameConst.BossrushBigLevelCount}-5");
                }
            }

            if (DataUtil.IsMultiRoom()) {
                return;
            }
            const char branch1 = 'A';
            var branchIndex = BattleData.data.BranchIndex[0] - branch1 + 1;
            if (!BattleData.data.IsARAM && this is not MapManagerFire) {
                UICanvas.GetInstance().ShowLevelMessage(branchIndex, LevelText, 1.5f);
            }
        }

        private void OnPreSetRandomSeed(int seed) {
            if (BattleData.data.IsLoopTravel) {
                modeController = new ModeLoopTravelController(seed);
            }

            if (BattleData.data.IsARAM) {
                modeController = new ModeARAMController(seed);
            }
        }

        /// <summary>
        /// 根据因子修改房间配置
        /// </summary>
        private void HandleRoomConfig() {
            if (!BattleData.data.IsTroop || RGGameProcess.Inst.this_index >= 16) {
                return;
            }

            // 佣兵团因子
            var index = (BattleData.data.levelIndex + 1) % 5;
            if (index <= 2) {
                map_long = Mathf.Max(1, map_long - 1);
            }

            map_long += BattleData.data.CompareFactor(emBattleFactor.LongMap) ? 1 : 0;
        }

        private ICustomObstacleList _customObstacleList;

        /// <summary>
        /// 根据下标获取障碍物的prefab
        /// </summary>
        /// <param name="obstacleIndex">障碍物下标</param>
        /// <param name="random"></param>
        /// <returns>获取障碍物的prefab</returns>
        public GameObject GetObstaclePrefab(int obstacleIndex, RGRandom random) {
            if (!useCustomObstacleList) {
                return Obstacles[obstacleIndex];
            }

            _customObstacleList ??= GetComponent<ICustomObstacleList>();
            return _customObstacleList.GetObstaclePrefab(obstacleIndex, random);
        }

        /// <summary>
        /// 是否有指定下标的障碍物prefab
        /// </summary>
        /// <param name="obstacleIndex">障碍物下标</param>
        /// <param name="random"></param>
        /// <returns>是否有指定下标的障碍物prefab</returns>
        public bool CanInstantiateObstacle(int obstacleIndex, RGRandom random) {
            if (!useCustomObstacleList) {
                bool canInit = Obstacles.Length > obstacleIndex && Obstacles[obstacleIndex];
                if (!canInit) {
                    Debug.LogWarning($"序号为{obstacleIndex}的房间物体不存在，请检查配置工具!");
                }
                return canInit;
            }

            _customObstacleList ??= GetComponent<ICustomObstacleList>();
            if (_customObstacleList == null) {
                Debug.LogWarning("未找到ICustomObstacleList组件");
                return false;
            }
            
            return _customObstacleList.CanInstantiateObstacle(obstacleIndex, random);
        }

        public GameObject GetElementPrefab(string elementName) {
            var prefab = RoomPatternMgr.GetElementPrefab(elementName);
            return prefab;
        }

        private bool _additionDamageCalculated;
        private int _additionDamage;

        public override int GetAdditionDamage(int camp, GameObject sourceObject) {
            if (camp != 0) {
                return 0;
            }

            if (_additionDamageCalculated) {
                return _additionDamage;
            }

            _additionDamageCalculated = true;
            _additionDamage +=
                base.GetAdditionDamage(camp, sourceObject) + FormulaUtil.CalcAdditionPlayerGetHurtDamage();
            if (BattleData.data.IsTroop) {
                _additionDamage += 1 + BattleData.data.sceneIndex * 2;
            }

            return _additionDamage;
        }

#if UNITY_EDITOR
        [Button(Name = "输出特殊房间列表")]
        private void PrintEventRooms() {
            if (null == SpecialRooms || SpecialRooms.Length == 0) {
                Debug.LogError("未配置特殊房间");
                return;
            }

            var stringBuilder = new StringBuilder("特殊房间:\n");
            foreach (var specialRoomDistribution in SpecialRooms) {
                stringBuilder.Append($"{specialRoomDistribution.gameObject.name}\n");
            }

            Debug.Log(stringBuilder.ToString());
        }
#endif

        [Serializable]
        public class RoomEvent : UnityEvent<RGRoomX> {
        }

        [Serializable]
        public class MapManagerLevelEvent : UnityEvent<MapManagerLevel> {
        }

        [Serializable]
        public class RoomInstantiateObstacleEvent : UnityEvent<GameObject> {
        }

        public Action<RGRoomX> OnHideRoomCreated;
    }

    public static class EnemyInfoExt {
        /// <summary>
        /// 刷出怪物的条件
        /// </summary>
        public static readonly List<string> EnemyConditions = new List<string>() {
            ConditionFirstRoundOnly,DreamEnemiesAvailable
        };

        private const string ConditionFirstRoundOnly = "firstRoundOnly";
        private const string DreamEnemiesAvailable = "dreamEnemiesAvailable";
        
        private static readonly List<MapManagerLevel.EnemyInfo> ValidEnemyInfoBuffer =
            new List<MapManagerLevel.EnemyInfo>();

        public static MapManagerLevel.EnemyInfo
            GetRandomEnemyInfo(this IEnumerable<MapManagerLevel.EnemyInfo> infos, RGRandom random) {
            ValidEnemyInfoBuffer.Clear();
            foreach (var enemyInfo in infos) {
                ValidEnemyInfoBuffer.Add(enemyInfo);
            }

            var result = ValidEnemyInfoBuffer.GetRandomWeightObject(random);
            return result;
        }

        public static MapManagerLevel.EnemyInfo GetRandomEnemyInfoWithCondition(
            this IEnumerable<MapManagerLevel.EnemyInfo> infos,
            RGRandom random,
            EnemyMaker.EnemyMakerState state
        ) {
            ValidEnemyInfoBuffer.Clear();
            foreach (var enemyInfo in infos) {
                if (enemyInfo.CanGenerate(ref state)) {
                    ValidEnemyInfoBuffer.Add(enemyInfo);
                }
            }

            var result = ValidEnemyInfoBuffer.GetRandomWeightObject(random);
            return result;
        }

        private static bool CanGenerate(
            this MapManagerLevel.EnemyInfo enemyInfo, ref EnemyMaker.EnemyMakerState state) {
            if (enemyInfo.generateConditions == null || enemyInfo.generateConditions.Count == 0) {
                return true;
            }

            foreach (var condition in enemyInfo.generateConditions) {
                switch (condition) {
                    case ConditionFirstRoundOnly:
                        if (!state.isInitRound) {
                            return false;
                        }
                        break;
                    case DreamEnemiesAvailable:
                        if (!ActivityDreamManager.IsDreamEnemiesAvailable()) {
                            return false;
                        }
                        break;
                }
            }

            return true;
        }
    }
}
