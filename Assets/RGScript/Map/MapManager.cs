using Activities.BugFeatures.Scripts;
using Activities.Fire.Scripts;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using MapSystem;
using Notification;
using RGScript.Data;
using RGScript.Mode.ModeMapLevelController;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.Tilemaps;

namespace RGScript.Map {
    [SelectionBase]
    public class MapManager : SerializedMonoBehaviour {
        internal readonly RGRandom RgRandom = new RGRandom();
        public string level = "1-1";

        public virtual string LevelText => level;

        [TabGroup(tab: "场景配置", order: 2)]
        [LabelText("使用默认门")]
        public bool isUseOldDoor = true;
    
        // ReSharper disable once InconsistentNaming
        protected AudioClip bgm_clip;

        [SerializeField]
        public AudioClip loopFxAudioClip;
    
        // ReSharper disable once InconsistentNaming
        public Color camera_bg;
    
        [FormerlySerializedAs("centerPoint")]
        public Vector2Int centerConfig = new Vector2Int(82, 82);
        public virtual Vector2Int CenterPoint => centerConfig;
    
        [NonSerialized]
        public List<RGRoomX> AllRoom;
        internal bool EnterNextMap;
        
        private static MapManager _instance;
        public static MapManager Instance {
            get {
                if (!_instance) {
                    _instance = FindObjectOfType<MapManager>();
                }

                return _instance;
            }
        }

        public virtual int ChestLevel => 0;

        //无尽因子下表示循环了多少次.1-1值为0, 4-1值为1
        public static int LoopCount => GameUtil.ModeLoopTravelLoopCount();

        private int _pressureLv = -1;
        /// <summary>
        /// 关卡的威压等级，1代表打过了1-5，4代表打过了4-5
        /// </summary>
        public int PressureLv {
            get {
                if (_pressureLv == -1) {
                    _pressureLv = GameUtil.ModeLoopTravelPressureLv();
                }

                return _pressureLv;
            }
        }

        protected virtual bool PlayBGM => true;
        public virtual AudioClip BGM => bgm_clip;

        public virtual AudioClip GetLoopFxAudioClip() {
            return loopFxAudioClip;
        }

        // ReSharper disable once InconsistentNaming
        public BaseModeController modeController;

        protected virtual void Start() {
            SimpleEventManager.Raise(new MapManagerStartEvent());
        
            SetRgRandomSeed(RGGameInfo.Inst.map_random_seed);

            if (PlayBGM) {
                var bgm = BGM;
                if (BattleData.data.CompareActivityFactor(emActivityFactor.MagicBgm) && DataUtil.IsBossLevel()) {
                    var magicBgm = ActivityBugFeatureManager.GetMagicBGM();
                    if (magicBgm != null) {
                        bgm = magicBgm;
                    }
                }
                
                RGMusicManager.GetInstance().PlayBGM(bgm, RGMusicManager.emBGM.Map);
            }
        
            if (GetLoopFxAudioClip()) {
                RGMusicManager.Inst.PlayLoopEffect(GetLoopFxAudioClip());
            }

            if (Camera.main != null) {
                Camera.main.backgroundColor = camera_bg;
            }
            
            RGGetPath.RegisterTransform(transform);
            InitCamera();
            StatisticGameStart();
        }

        protected virtual void InitCamera() {
#if UNITY_IOS
        var camAnimator = Camera.main.GetComponent<Animator>();
        if (null != camAnimator) {
            camAnimator.enabled = false;
        }

        GameUtil.EnlargeCamera();
#endif
        }

        protected virtual void SetRgRandomSeed(int seed) {
            RgRandom.SetRandomSeed(seed);
        }

        public virtual void OnDestroy() {
            modeController?.OnDestroy();
        }

        [SuppressMessage("ReSharper", "StringLiteralTypo")]
        private static string GetNormalModeMapManagerName(int levelIndex) {
            if (DataUtil.CurIsTargetScene(RGGameConst.SCENE_MULTI_ROOM)) {//联机房间
                return "map_A_MR";
            }

            switch (levelIndex) {
                case < 0:
                    switch (levelIndex) { //教程
                        case -1: {
                            // 2025年3月版本第二次测试
                            var testGroup = ABTest.Get710FirstTutorialTestGroup();
                            return testGroup == ABTestGroup.B ? "map_Scene_toturial_group_b" : "map_Scene_toturial";

                            // ABTest ios能进优化后的新手关卡
                            // var testGroup = ABTest.Get610FirstTutorialTestGroup();
                            // return testGroup == ABTestGroup.B ? "map_Scene_tutorial_first" : "map_Scene_toturial";
                        }
                        case -2:
                            return "map_A1";
                        case -3:
                            return "map_Scene_toturial_advance";
                        case -4:
                            return "map_Scene_return_player_intro";
                    }
                    break;
                case 0: // 客厅
                    return "map_A0";
            }
            
            if (BattleData.data.CompareFactor(emBattleFactor.Loop)) { // 无尽模式处理
                var finalLevelIndex = GameUtil.CalculateFinalGameProcessLevelIndex(levelIndex);
                return $"map_{BattleData.data.BranchIndex}{finalLevelIndex}";
            }

            var normalGameModeProcess = (NormalGameModeProcess)RGGameProcess.Inst.modeProcess;
            if (levelIndex == LevelSelector.GetMaxLevelIndex(emGameMode.Normal) + 1) {
                return "map_GameOver";// 通关场景
            }
            
            if (ActivityFireManager.InFireActivityMap()) {
                return "map_fire";
            }
            
            //正常关卡
            return normalGameModeProcess.GetMapManagerName(BattleData.data.BranchIndex, levelIndex);
        }

        private static string GetBossRushMapManagerName(int levelIndex) {
            if (DataUtil.CurIsTargetScene(RGGameConst.SCENE_MULTI_ROOM)) {
                return "map_A_MR";
            }

            //无尽模式处理
            if (BattleData.data.CompareFactor(emBattleFactor.Loop) && GameUtil.InGameScene) {
                var finalLevelIndex =
                    (levelIndex - 1) % (
                        BattleData.data.IsBossRushMode ?
                            RGGameConst.BossrushBaseLevelIndexCount :
                            RGGameConst.NormalBaseLevelIndexCount) + 1;
                return "map_" + BattleData.data.BranchIndex +
                       ((finalLevelIndex - 1) / RGGameConst.BossrushSubLevelCount + 1) + "_BR"; //每3*BR_PER_LEVEL_COUNT关
            }

            if (RGGameProcess.Inst.modeProcess is BossRushGameModeProcess) {
                if (levelIndex > BossRushGameModeProcess.NecessaryPassLevelCount()) {
                    return "map_End_BR";
                }

                var finalBoss = BossRushGameModeProcess.GetExtraFinalBoss(levelIndex);
                if (finalBoss != BossRushGameModeProcess.ExtraFinalBosses.None) {
                    return BossRushGameModeProcess.FinalBossIDMapManagerNameDict[finalBoss].mapManagerName;
                }
            }

            if (levelIndex == 0) {
                return "map_A0";
            }
        
            int subLevelCount = RGGameConst.BossrushSubLevelCount;
            var mapIndex = levelIndex;
            return $"map_{BattleData.data.BranchIndex}{((mapIndex - 1) / subLevelCount + 1)}_BR"; //每3*BR_PER_LEVEL_COUNT关
        }

        public static string GetMapManagerName(int levelIndex) {
            if (!string.IsNullOrEmpty(BattleData.data.patternMap)) {
                var mapName = BattleData.data.patternMap;
                BattleData.data.patternMap = null;
                return mapName;
            }

            // ReSharper disable once SwitchStatementMissingSomeEnumCasesNoDefault
            return BattleData.data.gameMode switch {
                emGameMode.Normal => GetNormalModeMapManagerName(levelIndex),
                emGameMode.BossRush => GetBossRushMapManagerName(levelIndex),
                emGameMode.Defence => "map_defence",
                emGameMode.Endless => "map_endless",
                emGameMode.Soccer => "map_soccer",
                emGameMode.IronTide => "map_irontide",
                emGameMode.MonsterRise => "map_monster_rise",
                emGameMode.ARAM => "map_ARAM",
                emGameMode.ComboGun => "map_cg",
                emGameMode.Troop2 => "map_troop2",
                emGameMode.Sandbox => "map_sandbox",
                _ => "map_A0"
            };
        }

        protected virtual void CreateMap() { }

        public virtual int GetAdditionDamage(int camp, GameObject sourceObject) => 0;
    
        /// <summary>
        /// 通过名字获取对应的RGRoomX
        /// </summary>
        /// <param name="roomName"></param>
        /// <returns></returns>
        public RGRoomX GetRoomByName(string roomName) {
            return AllRoom?.FirstOrDefault(tmpRoom => null != tmpRoom && tmpRoom.name == roomName);
        }

        public static string GetLevelText(BattleData battleData, bool includeHeroRoom = false) {
            return GetLevelText(
                battleData.levelIndex,
                battleData.IsBossRushMode,
                battleData.CompareFactor(emBattleFactor.Loop),
                includeHeroRoom, true, battleData);
        }

        public static string GetLevelText(
            int index, bool isBossRush, bool isLoop, bool includeHeroRoom, bool inDungeon = true, BattleData data = null) {
            //客厅
            if (DataUtil.CurIsTargetScene(RGGameConst.SCENE_HERO_ROOM) && !includeHeroRoom) {
                return string.Empty;
            }

            if (index >= 0) {
                return $"{GetSceneIndex(index, isBossRush, isLoop, inDungeon, data)}-" +
                       $"{GetLevelIndex(index, isBossRush, isLoop, inDungeon, data)}";
            }

            return string.Empty;
        }

        public static int GetSceneIndex(int index, bool isBossRush, bool isLoop, bool inDungeon = true, BattleData data = null) {
            // 每大关包含的小关卡数量
            var levelDensity = isBossRush ? RGGameConst.BossrushSubLevelCount : RGGameConst.NormalSubLevelCount;
            var useData = data ?? BattleData.data; 
            // 魔法石出现的关卡数
            var maxLevel = LevelSelector.GetMaxLevelIndex(useData.gameMode, data);
            var sceneIndex = index / levelDensity + 1;
            if (inDungeon && index >= maxLevel && !isLoop) {
                sceneIndex = maxLevel / levelDensity;
            }
            
            if (data != null) {
                if (data.factors.Contains(emBattleFactor.ReturnPlayer)) {
                    sceneIndex = (data.localLevelIndex - 1) / 3 + 1;
                    if (data.localLevelIndex == 10) {
                        sceneIndex = (data.localLevelIndex - 1) / 3;
                    }
                }
             
            } else {
                if (BattleData.data.factors.Contains(emBattleFactor.ReturnPlayer)) {
                    sceneIndex = (BattleData.data.localLevelIndex - 1) / 3 + 1;
                    if (BattleData.data.localLevelIndex == 10) {
                        sceneIndex = (BattleData.data.localLevelIndex - 1) / 3;
                    }
                }
                
            }
            return sceneIndex;
        }

        private static int GetLevelIndex(int index, bool isBossRush, bool isLoop, bool inDungeon = true, BattleData data = null) {
            // 每大关包含的小关卡数量
            var levelDensity = isBossRush ? RGGameConst.BossrushSubLevelCount : RGGameConst.NormalSubLevelCount;
            var useData = data ?? BattleData.data;
            // 魔法石出现的关卡数
            var maxLevel = LevelSelector.GetMaxLevelIndex(useData.gameMode, data);
            var levelIndex = index % levelDensity + 1;
            // 地牢内,魔法石关卡特殊显示
            if (inDungeon && index >= maxLevel && !isLoop) {
                levelIndex = index - maxLevel + levelDensity + 1;
            }
            
            if (data != null) {
                if (!data.factors.Contains(emBattleFactor.ReturnPlayer)) {
                    return levelIndex;
                }

                levelIndex = (data.localLevelIndex - 1) % 3 + 1;
                if (data.localLevelIndex == 10) {
                    levelIndex = 4;
                }
            } else {
                if (!BattleData.data.factors.Contains(emBattleFactor.ReturnPlayer)) {
                    return levelIndex;
                }

                levelIndex = (BattleData.data.localLevelIndex - 1) % 3 + 1;
                if (BattleData.data.localLevelIndex == 10) {
                    levelIndex = 4;
                }
            }

            return levelIndex;
        }

        private static bool _gameStartTracked;
        private static void StatisticGameStart() {
            if (_gameStartTracked) {
                return;
            }
        
            try {
                var properties = new Dictionary<string, object>() {
                    { "from_push", NotifyMgr.IsAppStartFromPush() }
                };
                TAUtil.Track("game_start", properties);
                _gameStartTracked = true;
            } catch (Exception e) {
                BuglyUtil.ReportException("TA埋点 StatisticGameStart", e);
            }
        }

        public Material environmentSpriteMaterial;
        public Material environmentAlphaMaterial;
        [Button(ButtonSizes.Medium)]
        public virtual void ReplaceEnvironmentMaterials() {
            var effectLayerID = SortingLayer.NameToID("Effect");
            foreach (var sr in GetComponentsInChildren<SpriteRenderer>()) {
                sr.sharedMaterial = sr.sortingLayerID != effectLayerID ? 
                    environmentSpriteMaterial : environmentAlphaMaterial;
            }

            foreach (var tr in GetComponentsInChildren<TilemapRenderer>()) {
                if (tr.gameObject.name.Contains("stencil")) {
                    continue;
                }
            
                tr.sharedMaterial = tr.sortingLayerID != effectLayerID ?
                    environmentSpriteMaterial : environmentAlphaMaterial;
            }

            var tilemapRenderer = RGRoomLayout.floorTiles.Tilemap.gameObject.GetComponent<TilemapRenderer>();
            tilemapRenderer.material = environmentSpriteMaterial;
        }
        public struct RoomStateResult {
            public bool HitRoom;
            public RoomState RoomState;
            public RGRoomX RoomX;
        }

        public enum RoomState {
            UnDetect = 0,
            Detecting = 1,
            Detected = 2,
        }
        static RaycastHit2D[] _cachedRaycastHit = new RaycastHit2D[64];
        public RoomStateResult DetectRoomState(Vector2 testPos) {
            RoomStateResult result = default;
            var castResult = Physics2D.CircleCastNonAlloc(testPos, .1f, Vector3.zero, _cachedRaycastHit, 0, PhysicsUtil.DefaultMask);
            if (castResult > 0) {
                for (int i = castResult - 1; i >= 0; i--) {
                    if (_cachedRaycastHit[i].collider.CompareTag("Room")) {
                        var room = _cachedRaycastHit[i].collider.GetComponent<RGRoomX>();
                        if (room != null) {
                            result.HitRoom = true;
                            result.RoomState = (RoomState)room.process;
                            result.RoomX = room;
                            break;
                        }
                    }
                }
            }
            return result;
        }
        public bool DetectIsInAisle(Vector2 testPos) {
            var castResult = Physics2D.CircleCastNonAlloc(testPos, .1f, Vector3.zero, _cachedRaycastHit, 0, PhysicsUtil.DefaultMask);
            if (castResult > 0) {
                for (int i = castResult - 1; i >= 0; i--) {
                    if (_cachedRaycastHit[i].collider.CompareTag("Room")) {
                        var aisle = _cachedRaycastHit[i].collider.GetComponent<RGAisle>();
                        if (aisle != null) {
                            return true;
                        }
                    }
                }
            }
            return false;
        }
    }
}
