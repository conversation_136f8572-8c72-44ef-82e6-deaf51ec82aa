using System;
using System.Collections.Generic;
using LevelPattern;
using MapSystem;
using RGScript.Map;
using UnityEngine;

namespace RoomLayout {
    public class RelicCustomRoomLayout : MonoBehaviour, ICustomRoomLayout {
        public int emptyIndex;
        public int hollowIndex;
        public int obstacleBoxIndex;
        public int awardBoxIndex;
        public GameObject relicFloorPrefab;
        public Vector3 floorPositionOffset = new Vector3(-.5f, 0);
        public BoxModel obstacleBox;
        public BoxModel awardBox;
        public LevelPatternConfig levelPatternConfig;
        public int processRoomType = 1;
        public GameObject brazierPrefab;
        public Obstacle[] obstacles;
        private List<LevelPatternConfig.MapModel> _mapModels;

        private void Awake() {
            _mapModels = new List<LevelPatternConfig.MapModel>(levelPatternConfig.mapModels);
        }

        public void CreateRoom(RGRoomX room) {
            if (_mapModels.Count == 0) {
                _mapModels.AddRange(levelPatternConfig.mapModels);
            }
            var roomLayout = room.GetComponent<RGRoomLayout>();
            if (!roomLayout) {
                roomLayout = room.gameObject.AddComponent<RGRoomLayout>();
            }
            var index = room.rg_random.Range(0, _mapModels.Count);
            var currentModel = _mapModels[index];
            _mapModels.RemoveAt(index);
            room.room_width = currentModel.size.x;
            room.room_height = currentModel.size.y;
            room.rect = new RectInt(
                (int)transform.position.x - room.room_width / 2,
                (int)transform.position.y - room.room_height / 2,
                room.room_width,
                room.room_height
            );
            var pattern = new int[currentModel.size.x][];
            for (int i = 0; i < currentModel.size.x; i++) {
                pattern[i] = new int[currentModel.size.y];
            }
            for (int x = 0; x < currentModel.size.x; x++) {
                for (int y = 0; y < currentModel.size.y; y++) {
                    pattern[x][y] = currentModel[x, y];
                }
            }
            var floorHollowPositions = new List<Vector3Int>();
            var floorTopEdgePositions = new List<Vector3Int>();
            var leftFusionPositions = new List<Vector3Int>();
            var upFusionPositions = new List<Vector3Int>();
            var rightFusionPositions = new List<Vector3Int>();
            var downFusionPositions = new List<Vector3Int>();
            var obstacleBoxPositions = new List<Vector3>();
            var obstacleAwardPositions = new List<Vector3>();
            
            room.Grid.transform.localPosition += new Vector3(0, 0.5f, 0);
            var roomOffset = new Vector3(-room.room_width / 2, -room.room_height / 2);
            for (int x = 0; x < currentModel.size.x; x++) {
                for (int y = 0; y < currentModel.size.y; y++) {
                    if (pattern[x][y] != 0) {
                        DefaultOccupiedRoomObject pipe = new DefaultOccupiedRoomObject(relicFloorPrefab);
                        room.Grid.AddObject(pipe, new Vector2Int(x, y), false);
                    }
                    
                    if (pattern[x][y] == hollowIndex) {
                        floorHollowPositions.Add(new Vector3Int(x, y, 0));
                        if (y == currentModel.size.y - 1||
                            pattern[x][y + 1] != hollowIndex 
                        ) {
                            floorTopEdgePositions.Add(new Vector3Int(x, y, 0));
                        }

                        if (x > 0 && pattern[x - 1][y] == emptyIndex) {
                            leftFusionPositions.Add(new Vector3Int(x - 1, y, 0));
                        }

                        if (y < currentModel.size.y - 1 && pattern[x][y + 1] == emptyIndex) {
                            upFusionPositions.Add(new Vector3Int(x, y + 1, 0));
                        }
                        if (x < currentModel.size.x - 1 && pattern[x + 1][y] == emptyIndex) {
                            rightFusionPositions.Add(new Vector3Int(x + 1, y, 0));
                        }
                        if (y > 0 && pattern[x][y - 1] == emptyIndex) {
                            downFusionPositions.Add(new Vector3Int(x, y - 1, 0));
                        }
                    } else if (pattern[x][y] == obstacleBoxIndex) {
                        obstacleBoxPositions.Add(new Vector3(x, y));
                        for (int z = 0; z < obstacleBox.size.x; z++) {
                            for (int w = 0; w < obstacleBox.size.y; w++) {
                                pattern[x + z][y + w] = -1;
                            }
                        }
                    } else if (pattern[x][y] == awardBoxIndex) {
                        obstacleAwardPositions.Add(new Vector3(x, y));
                        for (int z = 0; z < awardBox.size.x; z++) {
                            for (int w = 0; w < awardBox.size.y; w++) {
                                pattern[x + z][y + w] = -1;
                            }
                        }
                    }
                }
            }

            room.dir2Entrances[emDirection.Up].aisle.transform.localPosition = (room.room_height / 2 + 1) * Vector3.up;
            room.dir2Entrances[emDirection.Down].aisle.transform.localPosition = (room.room_height / 2 + 1) * Vector3.down;
            room.dir2Entrances[emDirection.Right].aisle.transform.localPosition = (room.room_width / 2 + 1) * Vector3.right;
            room.dir2Entrances[emDirection.Left].aisle.transform.localPosition = (room.room_width / 2 + 1) * Vector3.left;
            var destroyWallCount = room.wall_group.childCount;
            var wallObjects = new List<GameObject>();
            for (int i = 0; i < destroyWallCount; i++) {
                wallObjects.Add(room.wall_group.GetChild(i).gameObject);
            }
            foreach (var wallObject in wallObjects) {
                Destroy(wallObject);
            }
            roomLayout.Init(currentModel.size, 0, 0, room.rg_random);
            roomLayout.CreateAisle();
            roomLayout.CreateFloor();
            roomLayout.CreateWall();
            roomLayout.CreateVacantWall();
            
            var relicFloorObject = Instantiate(
                relicFloorPrefab,
                room.floor_group.TransformPoint(roomOffset + floorPositionOffset),
                Quaternion.identity,
                room.floor_group
            );
            var relicFloor = relicFloorObject.GetComponent<RelicFloor>();
            relicFloor.Generate(
                floorHollowPositions.ToArray(),
                floorTopEdgePositions.ToArray(),
                leftFusionPositions.ToArray(),
                upFusionPositions.ToArray(),
                rightFusionPositions.ToArray(),
                downFusionPositions.ToArray()
            );

            foreach (var position in obstacleBoxPositions) {
                var obj = Instantiate(
                    obstacleBox.prefab,
                    room.wall_group.TransformPoint(position + roomOffset + obstacleBox.positionOffset),
                    Quaternion.identity, 
                    room.wall_group
                );
                obj.SetObstacleZPos();
            }

            foreach (var position in obstacleAwardPositions) {
                var awardBoxObject = Instantiate(
                    awardBox.prefab,
                    room.wall_group.TransformPoint(position + roomOffset + awardBox.positionOffset),
                    Quaternion.identity, 
                    room.wall_group
                );
                var relicChest = awardBoxObject.GetComponent<RelicChest>();
                relicChest.Init(room);
            }
            
            InstantiateBrazier(room, pattern);
            InstantiateObstacle(room, pattern);
            
            var fallColliderPrefab = currentModel.extraData;
            Instantiate(fallColliderPrefab, room.wall_group.TransformPoint(roomOffset), Quaternion.identity,
                room.wall_group);
        }

        public void CreateCorridor(List<(RGRoomX, RGRoomX, int)> corridorInfoList) {
            throw new NotImplementedException();
        }

        public bool CanProcess(RGRoomX room) {
            return room.room_type == processRoomType;
        }
        
        private void InstantiateBrazier(RGRoomX room, int[][] emptyPositions) {
            var instantiatePositions = new List<Vector3>();
            var offset = new Vector3(-room.room_width / 2, -room.room_height / 2);
            var openDirection = room.openDirection;
            if ((openDirection & emDirection.Left) == emDirection.Left) {
                var index0 = (room.room_height - 5) / 2 - 1;
                var index1 = index0 + 6;
                if (emptyPositions[0][index0] == emptyIndex && emptyPositions[0][index1] == emptyIndex) {
                    for (int i = index0; i <= index1; i++) {
                        emptyPositions[0][i] = -1;
                    }
                    instantiatePositions.Add(new Vector3(0, index0) + offset);
                    instantiatePositions.Add(new Vector3(0, index1) + offset);
                }
            }

            if ((openDirection & emDirection.Up) == emDirection.Up) {
                var index0 = (room.room_width - 5) / 2 - 1;
                var index1 = index0 + 6;
                var y = room.room_height - 1;
                if (emptyPositions[index0][y] == emptyIndex && emptyPositions[index1][y] == emptyIndex) {
                    for (int i = index0; i <= index1; i++) {
                        emptyPositions[i][y] = -1;
                    }
                    instantiatePositions.Add(new Vector3(index0, y) + offset);
                    instantiatePositions.Add(new Vector3(index1, y) + offset);
                }
            }

            if ((openDirection & emDirection.Right) == emDirection.Right) {
                var index0 = (room.room_height - 5) / 2 - 1;
                var index1 = index0 + 6;
                var x = room.room_width - 1;
                if (emptyPositions[x][index0] == emptyIndex && emptyPositions[x][index1] == emptyIndex) {
                    for (int i = index0; i <= index1; i++) {
                        emptyPositions[x][i] = -1;
                    }
                    instantiatePositions.Add(new Vector3(x, index0) + offset);
                    instantiatePositions.Add(new Vector3(x, index1) + offset);
                }
            }
            
            if ((openDirection & emDirection.Down) == emDirection.Down) {
                var index0 = (room.room_width - 5) / 2 - 1;
                var index1 = index0 + 6;
                if (emptyPositions[index0][0] == emptyIndex && emptyPositions[index1][0] == emptyIndex) {
                    for (int i = index0; i < index1; i++) {
                        emptyPositions[i][0] = -1;
                    }
                    instantiatePositions.Add(new Vector3(index0, 0) + offset);
                    instantiatePositions.Add(new Vector3(index1, 0) + offset);
                }
            }
            
            foreach (var instantiatePosition in instantiatePositions) {
                var brazierObject = Instantiate(
                    brazierPrefab, 
                    room.wall_group.TransformPoint(instantiatePosition),
                    Quaternion.identity,
                    room.wall_group
                );
                var relicBrazier = brazierObject.GetComponent<RelicBrazier>();
                room.onRoomStart += _ => relicBrazier.Active();
            }
        }

        private void InstantiateObstacle(RGRoomX room, int[][] emptyPositions) {
            var floorCount = room.room_width * room.room_height;
            var positions = new List<Vector3Int>();
            var positionOffset = new Vector3(- room.room_width / 2, - room.room_height / 2);
            foreach (var obstacle in obstacles) {
                positions.Clear();
                var count = Mathf.RoundToInt(
                    room.rg_random.Range(obstacle.densityRange.x, obstacle.densityRange.y) * 
                    floorCount
                );
                var xMax = room.room_width - obstacle.size.x;
                var yMax = room.room_height - obstacle.size.y;
                for (int i = 0; i < count; i++) {
                    var positionX = room.rg_random.Range(0, xMax);
                    var positionY = room.rg_random.Range(0, yMax);
                    var canInstantiate = true;
                    for (int x = 0; x < obstacle.size.x; x++) {
                        for (int y = 0; y < obstacle.size.y; y++) {
                            canInstantiate = canInstantiate && emptyPositions[x + positionX][y + positionY] == emptyIndex;
                        }
                    }

                    if (!canInstantiate) {
                        continue;
                    }

                    positions.Add(new Vector3Int(positionX, positionY, 0));
                    for (int x = 0; x < obstacle.size.x; x++) {
                        for (int y = 0; y < obstacle.size.y; y++) {
                            emptyPositions[x + positionX][y + positionY] = -1;
                        }
                    }
                    
                }
                
                foreach (var position in positions) {
                    Vector3 worldPos = room.wall_group.TransformPoint(position + positionOffset + obstacle.positionOffset);
                    GameObject bonePileGo = Instantiate(obstacle.prefab, worldPos , Quaternion.identity, room.wall_group);
                    IOccupiedRoomObject ro = bonePileGo.GetComponent<IOccupiedRoomObject>();
                    var logicPos = room.Grid.WorldPos2GridPos(worldPos);
                    room.Grid.AddObject(ro, logicPos, false);
                    
                    // TODO: 箱子效果
                    if (obstacle.floors.Count == 0) {
                        continue;
                    }
                    for (int x = 0; x < obstacle.size.x + 2; x++) {
                        for (int y = 0; y < obstacle.size.y + 2; y++) {
                            var xIndex = position.x + x - 1;
                            var yIndex = position.y + y - 1;
                            if (xIndex < 0 ||
                                xIndex > room.room_width - 1 ||
                                yIndex < 0 ||
                                yIndex > room.room_height - 1
                            ) {
                                continue;
                            }

                            var isExtraEdge = xIndex == position.x - 1 ||
                                              xIndex == position.x + obstacle.size.x + 1 ||
                                              yIndex == position.y - 1 ||
                                              yIndex == position.y + obstacle.size.y + 1;
                            if (
                                isExtraEdge && 
                                (emptyPositions[xIndex][yIndex] != emptyIndex ||
                                 room.rg_random.Range(0, 100) > obstacle.extraFloorRate)
                            ) {
                                continue;
                            }
                            var floorPosition = room.floor_group.TransformPoint(
                                new Vector3(xIndex, yIndex) + positionOffset
                            );
                            var floorPrefab = obstacle.floors.GetRandomObject(room.rg_random);
                            Instantiate(floorPrefab, floorPosition, Quaternion.identity, room.floor_group);
                        }
                    }
                }
            }
        }

        [Serializable]
        public class Obstacle {
            public GameObject prefab;
            public Vector2 densityRange;
            public Vector2Int size;
            public List<ObstacleBoxContent> boxContents;
            public List<GameObject> floors;
            public Vector3 positionOffset;
            [Range(0, 100)]
            public int extraFloorRate;
        }
        
        [Serializable]
        public class ObstacleBoxContent : IWeightObject {
            public GameObject prefab;
            public int weight;
            public int Weight => weight;
        }

        [Serializable]
        public class BoxModel {
            public GameObject prefab;
            public Vector2Int size;
            public Vector3 positionOffset;
        }
    }
}