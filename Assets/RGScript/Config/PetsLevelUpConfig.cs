using System.Collections.Generic;
using UnityEngine;

namespace RGScript.Config {
    public static class PetsLevelUpConfig {
        public readonly struct PetsLevelUpData {
            public readonly int PetIndex;
            public readonly int[] FoodPrefer;
            public readonly string[] ExtraPrefer;
            public readonly int PetType;
            public readonly int[] SkillsIntimacy;
            public readonly int ActionIntimacy;

            public PetsLevelUpData(int petIndex, int[] foodPrefer, string[] extraPrefer, int petType, int[] skillsIntimacy, int actionIntimacy) {
                PetIndex = petIndex;
                FoodPrefer = foodPrefer;
                ExtraPrefer = extraPrefer;
                PetType = petType;
                SkillsIntimacy = skillsIntimacy;
                ActionIntimacy = actionIntimacy;
            }
        }

        public static readonly Dictionary<int, PetsLevelUpData> Data = new Dictionary<int, PetsLevelUpData> {
            { 0, new PetsLevelUpData(0, new int[] {4}, new string[] {"material_grain"}, 1, new int[] {240}, 120) },
            { 1, new PetsLevelUpData(1, new int[] {1}, new string[] {"material_grain", "wi26_sea_cucumber", "wi33_sardine"}, 2, new int[] {240}, 120) },
            { 2, new PetsLevelUpData(2, new int[] {2}, new string[] {"material_grain", "wi26_sea_cucumber", "wi33_sardine"}, 0, new int[] {240}, 120) },
            { 3, new PetsLevelUpData(3, new int[] {2}, new string[] {"material_grain"}, 0, new int[] {240}, 120) },
            { 4, new PetsLevelUpData(4, new int[] {3}, new string[] {}, 0, new int[] {240}, 120) },
            { 5, new PetsLevelUpData(5, new int[] {}, new string[] {"weapon_305", "material_grain", "weapon_203"}, 0, new int[] {240}, 120) },
            { 6, new PetsLevelUpData(6, new int[] {2}, new string[] {"material_grain"}, 0, new int[] {240}, 120) },
            { 7, new PetsLevelUpData(7, new int[] {2}, new string[] {"material_grain"}, 0, new int[] {240}, 120) },
            { 8, new PetsLevelUpData(8, new int[] {}, new string[] {}, 0, new int[] {240}, 120) },
            { 9, new PetsLevelUpData(9, new int[] {2}, new string[] {"material_grain"}, 0, new int[] {240}, 120) },
            { 10, new PetsLevelUpData(10, new int[] {}, new string[] {"material_grain"}, 0, new int[] {240}, 120) },
            { 11, new PetsLevelUpData(11, new int[] {}, new string[] {"material_grain"}, 0, new int[] {240}, 120) },
            { 12, new PetsLevelUpData(12, new int[] {4}, new string[] {"material_grain"}, 0, new int[] {240}, 120) },
            { 13, new PetsLevelUpData(13, new int[] {1, 2, 4, 5}, new string[] {"weapon_fertilizer", "material_grain"}, 0, new int[] {240}, 0) },
            { 14, new PetsLevelUpData(14, new int[] {1}, new string[] {"weapon_init_vampire", "material_grain"}, 0, new int[] {240}, 120) },
            { 15, new PetsLevelUpData(15, new int[] {2, 4}, new string[] {"weapon_900", "material_grain"}, 0, new int[] {240}, 120) },
            { 16, new PetsLevelUpData(16, new int[] {1}, new string[] {"weapon_281", "weapon_284", "weapon_279", "material_grain"}, 0, new int[] {240}, 120) },
            { 17, new PetsLevelUpData(17, new int[] {}, new string[] {}, 0, new int[] {240}, 120) },
            { 18, new PetsLevelUpData(18, new int[] {1}, new string[] {"material_grain", "wi26_sea_cucumber", "wi33_sardine"}, 2, new int[] {240}, 120) },
            { 19, new PetsLevelUpData(19, new int[] {1}, new string[] {"material_grain", "wi26_sea_cucumber", "wi33_sardine"}, 2, new int[] {240}, 120) },
            { 20, new PetsLevelUpData(20, new int[] {4}, new string[] {"material_grain"}, 1, new int[] {240}, 120) },
            { 21, new PetsLevelUpData(21, new int[] {4}, new string[] {"material_grain"}, 1, new int[] {240}, 120) },
            { 22, new PetsLevelUpData(22, new int[] {2}, new string[] {"material_grain"}, 0, new int[] {240}, 120) },
            { 23, new PetsLevelUpData(23, new int[] {2}, new string[] {"material_grain", "wi26_sea_cucumber", "wi33_sardine"}, 0, new int[] {240}, 120) },
            { 24, new PetsLevelUpData(24, new int[] {4}, new string[] {"material_grain"}, 1, new int[] {240}, 120) },
            { 25, new PetsLevelUpData(25, new int[] {2}, new string[] {"weapon_078", "weapon_221", "material_grain"}, 0, new int[] {240}, 120) },
            { 26, new PetsLevelUpData(26, new int[] {2}, new string[] {"material_grain"}, 0, new int[] {240}, 120) },
            { 27, new PetsLevelUpData(27, new int[] {}, new string[] {"material_grain"}, 0, new int[] {0}, 0) },
            { 28, new PetsLevelUpData(28, new int[] {1}, new string[] {"material_grain", "wi26_sea_cucumber", "wi33_sardine"}, 2, new int[] {240}, 120) },
            { 29, new PetsLevelUpData(29, new int[] {4}, new string[] {"material_grain"}, 1, new int[] {240}, 120) },
            { 30, new PetsLevelUpData(30, new int[] {4}, new string[] {"material_grain"}, 1, new int[] {180}, 90) },
            { 31, new PetsLevelUpData(31, new int[] {1}, new string[] {"material_grain", "wi26_sea_cucumber", "wi33_sardine"}, 2, new int[] {180}, 90) },
            { 32, new PetsLevelUpData(32, new int[] {1}, new string[] {"material_grain", "wi26_sea_cucumber", "wi33_sardine"}, 2, new int[] {240}, 120) },
            { 33, new PetsLevelUpData(33, new int[] {1}, new string[] {"material_grain", "wi26_sea_cucumber", "wi33_sardine"}, 0, new int[] {240}, 120) },
            { 34, new PetsLevelUpData(34, new int[] {1, 2, 4, 5}, new string[] {"material_grain"}, 1, new int[] {300}, 150) },
            { 35, new PetsLevelUpData(35, new int[] {2}, new string[] {"material_grain", "weapon_bossrushfinalA", "weapon_bossrushfinalB", "weapon_335", "weapon_116", "weapon_215"}, 1, new int[] {240}, 120) },
            { 36, new PetsLevelUpData(36, new int[] {3}, new string[] {"material_grain"}, 0, new int[] {240}, 120) },
            { 37, new PetsLevelUpData(37, new int[] {}, new string[] {"material_grain"}, 0, new int[] {240}, 120) },
            { 38, new PetsLevelUpData(38, new int[] {}, new string[] {"material_grain"}, 0, new int[] {240}, 120) },
            { 39, new PetsLevelUpData(39, new int[] {4}, new string[] {"material_grain"}, 1, new int[] {240}, 120) },
            { 40, new PetsLevelUpData(40, new int[] {}, new string[] {"material_grain"}, 0, new int[] {240}, 120) },
            { 41, new PetsLevelUpData(41, new int[] {}, new string[] {"material_grain"}, 0, new int[] {240}, 120) },
            { 42, new PetsLevelUpData(42, new int[] {4}, new string[] {"material_grain"}, 1, new int[] {240}, 120) },
            { 43, new PetsLevelUpData(43, new int[] {1, 2, 4, 5}, new string[] {"weapon_fertilizer", "material_grain"}, 0, new int[] {240}, 120) },
            { 44, new PetsLevelUpData(44, new int[] {}, new string[] {"material_grain"}, 0, new int[] {240}, 120) },
            { 45, new PetsLevelUpData(45, new int[] {}, new string[] {"material_grain"}, 0, new int[] {240}, 120) },
            { 46, new PetsLevelUpData(46, new int[] {}, new string[] {"material_grain"}, 0, new int[] {240}, 120) },
            { 47, new PetsLevelUpData(47, new int[] {}, new string[] {"material_grain"}, 0, new int[] {240}, 120) },
            { 48, new PetsLevelUpData(48, new int[] {}, new string[] {"material_grain"}, 0, new int[] {240}, 120) },
            { 49, new PetsLevelUpData(49, new int[] {}, new string[] {"material_grain"}, 0, new int[] {240}, 120) }
        };
    }
}
