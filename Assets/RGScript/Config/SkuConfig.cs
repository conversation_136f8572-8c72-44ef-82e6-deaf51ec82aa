using System.Collections.Generic;
using UnityEngine;

namespace RGScript.Config {
    public static class SkuConfig {
        public readonly struct SkuData {
            public readonly string PropId;
            public readonly string Display;
            public readonly string DisplayEN;
            public readonly string DisplayHK;
            public readonly string DisplayVNM;
            public readonly int Count;
            public readonly int ProductType;
            public readonly string Price;
            public readonly string Price_gp_ios;
            public readonly string Price_ru;
            public readonly string Price_vn;
            public readonly string ProductId;
            public readonly string ProductId_huawei;
            public readonly string ProductId_harmony;
            public readonly string ProductId_huawei_car;
            public readonly string ProductId_lenovo;
            public readonly string ProductId_coolpad;
            public readonly string ProductId_ios_new;
            public readonly string ProductId_gp_ios_old;
            public readonly string PriceTplID;
            public readonly string ItemType;
            public readonly string ByChannel;
            public readonly string IconPath;
            public readonly string ReleaseVersion;

            public SkuData(string propId, string display, string displayEN, string displayHK, string displayVNM, int count, int productType, string price, string price_gp_ios, string price_ru, string price_vn, string productId, string productId_huawei, string productId_harmony, string productId_huawei_car, string productId_lenovo, string productId_coolpad, string productId_ios_new, string productId_gp_ios_old, string priceTplID, string itemType, string byChannel, string iconPath, string releaseVersion) {
                PropId = propId;
                Display = display;
                DisplayEN = displayEN;
                DisplayHK = displayHK;
                DisplayVNM = displayVNM;
                Count = count;
                ProductType = productType;
                Price = price;
                Price_gp_ios = price_gp_ios;
                Price_ru = price_ru;
                Price_vn = price_vn;
                ProductId = productId;
                ProductId_huawei = productId_huawei;
                ProductId_harmony = productId_harmony;
                ProductId_huawei_car = productId_huawei_car;
                ProductId_lenovo = productId_lenovo;
                ProductId_coolpad = productId_coolpad;
                ProductId_ios_new = productId_ios_new;
                ProductId_gp_ios_old = productId_gp_ios_old;
                PriceTplID = priceTplID;
                ItemType = itemType;
                ByChannel = byChannel;
                IconPath = iconPath;
                ReleaseVersion = releaseVersion;
            }
        }

        public static readonly Dictionary<string, SkuData> Data = new Dictionary<string, SkuData> {
            { "engineer", new SkuData("engineer", "角色 - 工程师", "Character - Engineer", "", "Nhân vật - Kỹ Sư", 1, 1, "5", "$0.99/¥6", "₽79/¥6", "₫25000/$0.99", "1", "1", "20001", "", "198205", "5000024643", "ios.com.chillyroom.dungeonshooter.engineer", "com.chillyroom.dungeonshooter.engineer", "*******************/*******************", "Hero", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_5", "1.1.5") },
            { "vampire", new SkuData("vampire", "角色 - 吸血鬼", "Character - Vampire", "", "Nhân vật - Ma Ca Rồng", 1, 1, "5", "$0.99/¥6", "₽79/¥6", "₫25000/$0.99", "2", "10002", "20002", "", "198206", "5000042420", "ios.com.chillyroom.dungeonshooter.vampire", "com.chillyroom.dungeonshooter.vampire", "*******************/*******************", "Hero", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_6", "1.1.5") },
            { "paladin", new SkuData("paladin", "角色 - 圣骑士", "Character - Paladin", "", "Nhân vật - Kỵ Sĩ Thánh", 1, 1, "5", "$0.99/¥6", "₽79/¥6", "₫25000/$0.99", "3", "10003", "20003", "", "198207", "5000060197", "ios.com.chillyroom.dungeonshooter.paladin", "com.chillyroom.dungeonshooter.paladin", "*******************/*******************", "Hero", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_7", "1.1.5") },
            { "reborn_card", new SkuData("reborn_card", "超级复活卡", "Super Revival Card", "", "Thẻ Hồi Sinh Siêu Cấp", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "4", "4", "20004", "", "198208", "5000077974", "ios.com.chillyroom.dungeonshooter.reborn_card", "com.chillyroom.dungeonshooter.reborn_card", "*******************/*******************", "RebornCard", "All", "Assets/RGTexture/ui/common/ui.png#ui_68", "1.1.5") },
            { "purchase_gem_1", new SkuData("purchase_gem_1", "5000 宝石", "Gem - 5000", "", "Đá - 5000", 5000, 0, "5", "$0.99/¥6", "₽79/¥6", "₫25000/$0.99", "5", "5", "20005", "", "198209", "5000095751", "ios.com.chillyroom.dungeonshooter.purchase_gem_1", "com.chillyroom.dungeonshooter.purchase_gem_1", "*******************/*******************", "Gem", "All", "Assets/RGTexture/ui/common/shop_gem.png#shop_gem_1", "1.1.5") },
            { "purchase_gem_2", new SkuData("purchase_gem_2", "12000 宝石", "Gem - 12000", "12000 寶石", "Đá - 12000", 12000, 0, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "6", "6", "20006", "", "198210", "5000113528", "ios.com.chillyroom.dungeonshooter.purchase_gem_2", "com.chillyroom.dungeonshooter.purchase_gem_2", "*******************/*******************", "Gem", "All", "Assets/RGTexture/ui/common/shop_gem.png#shop_gem_2", "1.1.5") },
            { "purchase_gem_3", new SkuData("purchase_gem_3", "25000 宝石", "Gem - 25000", "", "", 25000, 0, "15", "", "", "", "7", "7", "20007", "", "198211", "5000131305", "", "", "", "Gem", "All", "Assets/RGTexture/ui/common/shop_gem.png#shop_gem_3", "") },
            { "purchase_gem_5", new SkuData("purchase_gem_5", "800 宝石", "Gem - 800", "", "", 800, 0, "1", "", "", "", "8", "8", "20008", "", "198212", "5000149082", "", "", "", "Gem", "All", "Assets/RGTexture/ui/common/shop_gem.png#shop_gem_0", "") },
            { "werewolf", new SkuData("werewolf", "角色 - 狼人", "Character - Werewolf", "角色 - 狼人", "Nhân vật - Người Sói", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "9", "9", "20009", "", "198213", "5000166859", "ios.com.chillyroom.dungeonshooter.werewolf", "com.chillyroom.dungeonshooter.werewolf", "*******************/*******************", "Hero", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_9", "1.2.0") },
            { "druid", new SkuData("druid", "角色 - 德鲁伊", "Character - Druid", "角色 - 德魯伊", "Nhân vật - Druid", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "11", "11", "20011", "", "198215", "5000202413", "ios.com.chillyroom.dungeonshooter.druid", "com.chillyroom.dungeonshooter.druid", "*******************/*******************", "Hero", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_11", "1.4.1-2017国庆版本") },
            { "vampire_skin_2", new SkuData("vampire_skin_2", "吸血鬼皮肤 - 警长", "Skin - Vampire - Vampire Police", "", "Skin - Ma Ca Rồng - Cảnh Sát Trưởng", 1, 1, "5", "$0.99/¥6", "₽79/¥6", "₫25000/$0.99", "12", "12", "20012", "", "198216", "5000220190", "ios.com.chillyroom.dungeonshooter.vampire_skin_2", "com.chillyroom.dungeonshooter.vampire_skin_2", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_18", "1.4.1-2017国庆版本") },
            { "paladin_skin_2", new SkuData("paladin_skin_2", "圣骑士皮肤 - 工业化", "Skin - Paladin - CH3COOH", "", "Skin - Kỵ Sĩ Thánh - Công Nghiệp Hóa", 1, 1, "5", "$0.99/¥6", "₽79/¥6", "₫25000/$0.99", "13", "13", "20013", "", "198217", "5000237967", "ios.com.chillyroom.dungeonshooter.paladin_skin_2", "com.chillyroom.dungeonshooter.paladin_skin_2", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_19", "1.4.1-2017国庆版本") },
            { "elf_skin_2", new SkuData("elf_skin_2", "精灵皮肤 - 冰雪女王", "Skin - Elf - Ice Queen", "", "Skin - Tinh Linh - Nữ Hoàng Băng Tuyết", 1, 1, "5", "$0.99/¥6", "₽79/¥6", "₫25000/$0.99", "14", "14", "20014", "", "198218", "5000255744", "ios.com.chillyroom.dungeonshooter.elf_skin_2", "com.chillyroom.dungeonshooter.elf_skin_2", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_20", "1.4.1-2017国庆版本") },
            { "werewolf_skin_2", new SkuData("werewolf_skin_2", "狼人皮肤 - 熊孩子", "Skin - Werewolf - Werebear", "", "Skin - Người Sói - Trẻ Bướng Bỉnh", 1, 1, "5", "$0.99/¥6", "₽79/¥6", "₫25000/$0.99", "15", "15", "20015", "", "198219", "5000273521", "ios.com.chillyroom.dungeonshooter.werewolf_skin_2", "com.chillyroom.dungeonshooter.werewolf_skin_2", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_21", "1.4.1-2017国庆版本") },
            { "pastor_skin_2", new SkuData("pastor_skin_2", "牧师皮肤 - 红衣教主", "", "", "", 1, 1, "5", "", "", "", "16", "10016", "20016", "", "198220", "5000291298", "", "", "", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_22", "1.4.1-2017国庆版本") },
            { "druid_skin_2", new SkuData("druid_skin_2", "德鲁伊皮肤 - 上班族", "Skin - Druid - Workaholic", "", "Skin - Druid - Dân Công Sở", 1, 1, "5", "$0.99/¥6", "₽79/¥6", "₫25000/$0.99", "17", "17", "20017", "", "198221", "5000309075", "ios.com.chillyroom.dungeonshooter.druid_skin_2", "com.chillyroom.dungeonshooter.druid_skin_2", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_23", "1.4.1-2017国庆版本") },
            { "alchemist_skin_3", new SkuData("alchemist_skin_3", "炼金术士皮肤 - 财神爷", "Skin - Alchemist - The Fortune God", "煉金術士皮膚 - 財神爺", "Skin - Thuật Sĩ Luyện Kim - Thần Tài", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "18", "18", "20018", "", "198222", "5000326852", "ios.com.chillyroom.dungeonshooter.alchemist_skin_3", "com.chillyroom.dungeonshooter.alchemist_skin_3", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_62", "1.6.0-2018春节版本") },
            { "assassin_skin_3", new SkuData("assassin_skin_3", "刺客皮肤 - 暗部", "Skin - Assassin - Anbu", "刺客皮膚 - 暗部", "Skin - Thích Khách - Ám Bộ", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "19", "19", "20019", "", "198223", "5000344629", "ios.com.chillyroom.dungeonshooter.assassin_skin_3", "com.chillyroom.dungeonshooter.assassin_skin_3", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_59", "1.6.0-2018春节版本") },
            { "druid_skin_4", new SkuData("druid_skin_4", "德鲁伊皮肤 - 龙王", "Skin - Druid - Dragon King", "德魯伊皮膚 - 龍王", "Skin - Druid - Long Vương", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "20", "20", "20020", "", "198224", "5000362406", "ios.com.chillyroom.dungeonshooter.druid_skin_4", "com.chillyroom.dungeonshooter.druid_skin_4", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_61", "1.6.0-2018春节版本") },
            { "elf_skin_3", new SkuData("elf_skin_3", "精灵皮肤 - 嫦娥", "Skin - Elf - Chang'e", "精靈皮膚 - 嫦娥", "Skin - Tinh Linh - Hằng Nga", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "21", "21", "20021", "", "198225", "5000380183", "ios.com.chillyroom.dungeonshooter.elf_skin_3", "com.chillyroom.dungeonshooter.elf_skin_3", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_56", "1.6.0-2018春节版本") },
            { "engineer_skin_3", new SkuData("engineer_skin_3", "工程师皮肤 - 孔明灯", "Skin - Engineer - Sky Lantern", "工程師皮膚 - 孔明燈", "Skin - Kỹ Sư - Đèn Trời", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "22", "22", "20022", "", "198226", "5000397960", "ios.com.chillyroom.dungeonshooter.engineer_skin_3", "com.chillyroom.dungeonshooter.engineer_skin_3", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_63", "1.6.0-2018春节版本") },
            { "paladin_skin_4", new SkuData("paladin_skin_4", "圣骑士皮肤 - 增长天王", "Skin - Paladin - Virudhaka", "聖騎士皮膚 - 增長天王", "Skin - Kỵ Sĩ Thánh - Tăng Trưởng Thiên Vương", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "23", "23", "20023", "", "198227", "5000415737", "ios.com.chillyroom.dungeonshooter.paladin_skin_4", "com.chillyroom.dungeonshooter.paladin_skin_4", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_55", "1.6.0-2018春节版本") },
            { "pastor_skin_3", new SkuData("pastor_skin_3", "牧师皮肤 - 兔兔", "Skin - Priestess - Bunny", "牧師皮膚 - 兔兔", "Skin - Mục Sư - Thỏ Thỏ", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "24", "24", "20024", "", "198228", "5000433514", "ios.com.chillyroom.dungeonshooter.pastor_skin_3", "com.chillyroom.dungeonshooter.pastor_skin_3", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_65", "1.6.0-2018春节版本") },
            { "rogue_skin_4", new SkuData("rogue_skin_4", "游侠皮肤 - 啾啾", "Skin - Rogue - Bubbles", "遊俠皮膚 - 啾啾", "Skin - Hiệp Sĩ - Chiuchiu", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "25", "25", "20025", "", "198229", "5000451291", "ios.com.chillyroom.dungeonshooter.ranger_skin_4", "com.chillyroom.dungeonshooter.ranger_skin_4", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_58", "1.6.0-2018春节版本") },
            { "werewolf_skin_5", new SkuData("werewolf_skin_5", "狼人皮肤 - 小虎队", "Skin - Werewolf - Tiger Cub", "狼人皮膚 - 小虎隊", "Skin - Người Sói - Đội Tiểu Hổ", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "26", "26", "20026", "", "198230", "5000469068", "ios.com.chillyroom.dungeonshooter.werewolf_skin_5", "com.chillyroom.dungeonshooter.werewolf_skin_5", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_60", "1.6.0-2018春节版本") },
            { "wizard_skin_5", new SkuData("wizard_skin_5", "法师皮肤 - 春节", "Skin - Witch - Chinese New Year", "法師皮膚 - 春節", "Skin - Pháp Sư - Tết", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "27", "27", "20027", "", "198231", "5000486845", "ios.com.chillyroom.dungeonshooter.mage_skin_5", "com.chillyroom.dungeonshooter.mage_skin_5", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_54", "1.6.0-2018春节版本") },
            { "viking", new SkuData("viking", "角色 - 狂战士", "Character - Berserker", "角色 - 狂戰士", "Nhân vật - Berserker", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "28", "28", "20028", "", "215526", "5000504622", "ios.com.chillyroom.dungeonshooter.viking", "com.chillyroom.dungeonshooter.viking", "*******************/*******************", "Hero", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_69", "1.8.2-2018暑假版本") },
            { "plantpot_1", new SkuData("plantpot_1", "花圃#5", "Garden Plot 5", "", "Vườn Hoa 5", 1, 1, "5", "$0.99/¥6", "₽79/¥6", "₫25000/$0.99", "29", "29", "20029", "", "215527", "5000522399", "ios.com.chillyroom.dungeonshooter.gardenpot_1", "com.chillyroom.dungeonshooter.gardenpot_1", "*******************/*******************", "PlantPot", "All", "Assets/RGTexture/heroroom/decoration/common/common_room_item_2.png#common_room_item_2_4", "1.8.2-2018暑假版本") },
            { "plantpot_2", new SkuData("plantpot_2", "花圃#6", "Garden Plot 6", "花圃#6", "Vườn Hoa 6", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "30", "30", "20030", "", "231799", "5000540176", "ios.com.chillyroom.dungeonshooter.gardenpot_2", "com.chillyroom.dungeonshooter.gardenpot_2", "*******************/*******************", "PlantPot", "All", "Assets/RGTexture/heroroom/decoration/common/common_room_item_2.png#common_room_item_2_4", "1.8.2-2018暑假版本") },
            { "werewolf_skin_7", new SkuData("werewolf_skin_7", "狼人皮肤 - 美洲豹", "Skin - WereWolf - Jaguar", "狼人皮膚 - 美洲豹", "Skin - Người Sói - Báo Châu Mỹ", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "31", "31", "20031", "", "231800", "5000557953", "ios.com.chillyroom.dungeonshooter.werewolf_skin_7", "com.chillyroom.dungeonshooter.werewolf_skin_7", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_76", "1.8.5-2018独立日版本") },
            { "viking_skin_1", new SkuData("viking_skin_1", "狂战士皮肤 - 摔跤大师", "Skin - Berserker - Wrestler", "狂戰士皮膚 - 摔跤大師", "Skin - Berserker - Chuyên Gia Đấu Vật", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "32", "32", "20032", "", "231801", "5000575730", "ios.com.chillyroom.dungeonshooter.viking_skin_1", "com.chillyroom.dungeonshooter.viking_skin_1", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_77", "1.8.5-2018独立日版本") },
            { "rogue_skin_5", new SkuData("rogue_skin_5", "游侠皮肤 - 夜莺", "Skin - Rogue - Nightingale", "遊俠皮膚 - 夜鶯", "Skin - Hiệp Sĩ - Dạ Oanh", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "33", "33", "20033", "", "252567", "5000593507", "ios.com.chillyroom.dungeonshooter.ranger_skin_master", "com.chillyroom.dungeonshooter.ranger_skin_master", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_84", "2.0.0-2019春节版本") },
            { "wizard_skin_6", new SkuData("wizard_skin_6", "法师皮肤 - 拘魂者", "Skin - Witch - Soul Snare", "法師皮膚 - 拘魂者", "Skin - Pháp Sư - Người Bắt Hồn", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "34", "34", "20034", "", "252568", "5000611284", "ios.com.chillyroom.dungeonshooter.mage_skin_master", "com.chillyroom.dungeonshooter.mage_skin_master", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_85", "2.0.0-2019春节版本") },
            { "assassin_skin_6", new SkuData("assassin_skin_6", "刺客皮肤 - 风林火山", "Skin - Assassin - Crimson", "刺客皮膚 - 風林火山", "Skin - Thích Khách - Núi Lửa Rừng Gió", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "35", "35", "20035", "", "252569", "5000629061", "ios.com.chillyroom.dungeonshooter.assassin_skin_master", "com.chillyroom.dungeonshooter.assassin_skin_master", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/assassin_6_ui.png", "2.0.0-2019春节版本") },
            { "vampire_skin_6", new SkuData("vampire_skin_6", "吸血鬼皮肤 - 银爵", "Skin - Vampire - Duke Silver", "吸血鬼皮膚 - 銀爵", "Skin - Ma Ca Rồng - Silver", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "36", "36", "20036", "", "252570", "5000646838", "ios.com.chillyroom.dungeonshooter.vampire_skin_master", "com.chillyroom.dungeonshooter.vampire_skin_master", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_81", "2.0.0-2019春节版本") },
            { "elf_skin_5", new SkuData("elf_skin_5", "精灵皮肤 - 巡林者", "Skin - Elf - Forest Keeper", "精靈皮膚 - 巡林者", "Skin - Tinh Linh - Người Tuần Tra Rừng", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "37", "37", "20037", "", "252571", "5000664615", "ios.com.chillyroom.dungeonshooter.elves_skin_master", "com.chillyroom.dungeonshooter.elves_skin_master", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_89", "2.0.0-2019春节版本") },
            { "druid_skin_5", new SkuData("druid_skin_5", "德鲁伊皮肤 - 霜烬", "Skin - Druid - Frostash", "德魯伊皮膚 - 霜燼", "Skin - Druid - Khói Sương", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "38", "38", "20038", "", "252572", "5000682392", "ios.com.chillyroom.dungeonshooter.druid_skin_master", "com.chillyroom.dungeonshooter.druid_skin_master", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_91", "2.0.0-2019春节版本") },
            { "engineer_skin_6", new SkuData("engineer_skin_6", "工程师皮肤 - 世界你好", "Skin - Engineer - HELLO WORLD", "工程師皮膚 - 世界你好", "Skin - Kỹ Sư - Chào Thế Giới", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "39", "39", "20039", "", "252573", "5000700169", "ios.com.chillyroom.dungeonshooter.engineer_skin_master", "com.chillyroom.dungeonshooter.engineer_skin_master", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_87", "2.0.0-2019春节版本") },
            { "werewolf_skin_8", new SkuData("werewolf_skin_8", "狼人皮肤 - 火焰地狱契约", "Skin - Werewolf - Hellpact", "狼人皮膚 - 火焰地獄契約", "Skin - Người Sói - Kế Ước Địa Ngục Lửa", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "40", "40", "20040", "", "252574", "5000717946", "ios.com.chillyroom.dungeonshooter.werewolf_skin_master", "com.chillyroom.dungeonshooter.werewolf_skin_master", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_90", "2.0.0-2019春节版本") },
            { "skill_assassin_1", new SkuData("skill_assassin_1", "刺客二技能 - 分身术", "", "", "", 1, 1, "10", "", "", "", "41", "41", "20041", "", "231802", "5000735723", "", "", "", "HeroSkill", "All", "", "2.2.0-2019暑假版本一") },
            { "skill_engineer_1", new SkuData("skill_engineer_1", "工程师二技能 - 机甲武装", "Skill - Engineer - Armor Mount", "", "Kỹ năng - Kỹ Sư - Vũ Trang Cơ Giáp", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "42", "42", "20042", "", "231803", "5000753500", "ios.com.chillyroom.dungeonshooter.skill_engineer_1", "com.chillyroom.dungeonshooter.skill_engineer_1", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/ui3.png#skill_5_1", "2.2.0-2019暑假版本一") },
            { "skill_vampire_1", new SkuData("skill_vampire_1", "吸血鬼二技能 - 异界漩涡", "Skill - Vampire - Alien Swirl", "吸血鬼二技能 - 異界漩渦", "Kỹ năng - Ma Ca Rồng - Lốc Dị Giới", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "43", "43", "20043", "", "231804", "5000771277", "ios.com.chillyroom.dungeonshooter.skill_vampire_1", "com.chillyroom.dungeonshooter.skill_vampire_1", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/ui3.png#skill_6_1", "2.2.0-2019暑假版本一") },
            { "skill_paladin_1", new SkuData("skill_paladin_1", "圣骑士二技能 - 神化", "Skill - Paladin - Holy Warrior", "聖騎士二技能 - 神化", "Kỹ năng - Kỵ Sĩ Thánh - Thần Hóa", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "44", "44", "20044", "", "231805", "5000789054", "ios.com.chillyroom.dungeonshooter.skill_paladin_1", "com.chillyroom.dungeonshooter.skill_paladin_1", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/ui3.png#skill_7_1", "2.2.0-2019暑假版本一") },
            { "skill_priest_1", new SkuData("skill_priest_1", "牧师二技能 - 祈祷", "", "", "", 1, 1, "10", "", "", "", "45", "45", "20045", "", "231806", "5000806831", "", "", "", "HeroSkill", "All", "", "2.2.0-2019暑假版本一") },
            { "skill_robot_1", new SkuData("skill_robot_1", "机器人二技能 - 无人机集群", "Skill - Robot - Drone Swarm", "機器人二技能 - 無人機集群", "Kỹ năng - Robot - Drone Tấn Công", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "46", "46", "20046", "", "231807", "5000824608", "ios.com.chillyroom.dungeonshooter.skill_robot_1", "com.chillyroom.dungeonshooter.skill_robot_1", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/ui3.png#skill_12_1", "2.2.5-2019暑假版本二") },
            { "viking_skin_3", new SkuData("viking_skin_3", "狂战士皮肤 - 丧尸", "Skin - Berserker - Zombie", "狂戰士皮膚 - 喪屍", "Skin - Berserker - Zombie", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "47", "47", "20047", "", "231808", "5000842385", "ios.com.chillyroom.dungeonshooter.viking_skin_3", "com.chillyroom.dungeonshooter.viking_skin_3", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_94", "2.2.0-2019暑假版本一") },
            { "knight_skin_6", new SkuData("knight_skin_6", "骑士皮肤 - 电子骑士", "Skin - Knight - CyberKnight", "", "Skin - Kỵ Sĩ - Kỵ Sĩ Điện Tử", 1, 1, "5", "$0.99/¥6", "₽79/¥6", "₫25000/$0.99", "48", "48", "20048", "", "231809", "5000860162", "ios.com.chillyroom.dungeonshooter.knight_skin_6", "com.chillyroom.dungeonshooter.knight_skin_6", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_95", "2.2.0-2019暑假版本一") },
            { "knight_skin_8", new SkuData("knight_skin_8", "骑士皮肤 - 暗黑大骑士", "Skin - Knight - Dark Grand Knight Skin", "騎士皮膚 - 暗黑大騎士", "Skin - Kỵ Sĩ - Skin Kỵ Sĩ Bóng Tối", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "49", "49", "20049", "", "231810", "5000877939", "ios.com.chillyroom.dungeonshooter.knight_skin_8", "com.chillyroom.dungeonshooter.knight_skin_8", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_98", "2.2.0-2019暑假版本一") },
            { "robot_skin_4", new SkuData("robot_skin_4", "机器人皮肤 - 巨像祖兰", "Skin - Robot - Zulan The Colossus", "機器人皮膚 - 巨像祖蘭", "Skin - Robot - Skin Zulan The Colossus", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "50", "50", "20050", "", "231811", "5000895716", "ios.com.chillyroom.dungeonshooter.robot_skin_4", "com.chillyroom.dungeonshooter.robot_skin_4", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_102", "2.2.0-2019暑假版本一") },
            { "viking_skin_5", new SkuData("viking_skin_5", "狂战士皮肤 - 龙的传人", "Skin - Berserker - Bruce Lee", "狂戰士皮膚 - 龍的傳人", "Skin - Berserker - Truyền Nhân Rồng", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "51", "51", "20051", "", "231812", "5000913493", "ios.com.chillyroom.dungeonshooter.viking_skin_5", "com.chillyroom.dungeonshooter.viking_skin_5", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_105", "2.2.0-2019暑假版本一") },
            { "alchemist_skin_5", new SkuData("alchemist_skin_5", "炼金术士皮肤 - 瓦克恩头目", "Skin - Alchemist - Varkolyn Leader", "煉金術士皮膚 - 瓦克恩頭目", "Skin - Thuật Sĩ Luyện Kim - Thủ Lĩnh Wackern", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "52", "52", "20052", "", "231813", "5000931270", "ios.com.chillyroom.dungeonshooter.alchemist_skin_5", "com.chillyroom.dungeonshooter.alchemist_skin_5", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_100", "2.2.0-2019暑假版本一") },
            { "skill_ranger_1", new SkuData("skill_ranger_1", "游侠二技能 - 居合斩", "Skill - Rogue - Iaido", "遊俠二技能 - 居合斬", "Kỹ năng - Hiệp Sĩ - Iaido", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "53", "53", "20053", "", "231814", "5000949047", "ios.com.chillyroom.dungeonshooter.skill_ranger_1", "com.chillyroom.dungeonshooter.skill_ranger_1", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/ui3.png#skill_1_1", "2.2.0-2019暑假版本一") },
            { "skill_elves_1", new SkuData("skill_elves_1", "精灵二技能 - 箭雨", "Skill - Elf - Arrow Rain", "精靈二技能 - 箭雨", "Kỹ năng - Tinh Linh - Mưa Tên", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "54", "54", "20054", "", "231815", "5000966824", "ios.com.chillyroom.dungeonshooter.skill_elves_1", "com.chillyroom.dungeonshooter.skill_elves_1", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/ui3.png#skill_8_1", "2.2.5-2019暑假版本二") },
            { "assassin_skin_7", new SkuData("assassin_skin_7", "刺客皮肤 - 骷髅王", "Skin - Assassin - Skeleton King", "刺客皮膚 - 骷髏王", "Skin - Thích Khách - Vua Xương", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "55", "55", "20055", "", "231816", "5000984601", "ios.com.chillyroom.dungeonshooter.assassin_skin_7", "com.chillyroom.dungeonshooter.assassin_skin_7", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_109", "2.2.5-2019暑假版本二") },
            { "engineer_skin_7", new SkuData("engineer_skin_7", "工程师皮肤 - 镭射幽浮", "Skin - Engineer - Floating Laser UFO", "工程師皮膚 - 鐳射幽浮", "Skin - Kỹ Sư - Đĩa Nổi Laser", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "56", "56", "20056", "", "231817", "5001002378", "ios.com.chillyroom.dungeonshooter.engineer_skin_7", "com.chillyroom.dungeonshooter.engineer_skin_7", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_106", "2.2.5-2019暑假版本二") },
            { "druid_skin_6", new SkuData("druid_skin_6", "德鲁伊皮肤 - 年兽", "Skin - Druid - Nian", "德魯伊皮膚 - 年獸", "Skin - Druid - Zodiac", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "57", "57", "20057", "", "231818", "5001020155", "ios.com.chillyroom.dungeonshooter.druid_skin_6", "com.chillyroom.dungeonshooter.druid_skin_6", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_110", "2.2.5-2019暑假版本二") },
            { "paladin_skin_9", new SkuData("paladin_skin_9", "圣骑士皮肤 - 帝江", "Skin - Paladin - Dijiang Bird", "聖騎士皮膚 - 帝江", "Skin - Kỵ Sĩ Thánh - Đế Giang", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "58", "58", "20058", "", "231819", "5001037932", "ios.com.chillyroom.dungeonshooter.paladin_skin_9", "com.chillyroom.dungeonshooter.paladin_skin_9", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_124", "2.5.0-2020春节版本") },
            { "rogue_skin_7", new SkuData("rogue_skin_7", "游侠皮肤 - 应龙", "Skin - Rogue - Winged Long", "遊俠皮膚 - 應龍", "Skin - Hiệp Sĩ - Ứng Long", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "59", "59", "20059", "", "231820", "5001055709", "ios.com.chillyroom.dungeonshooter.rogue_skin_7", "com.chillyroom.dungeonshooter.rogue_skin_7", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_118", "2.5.0-2020春节版本") },
            { "assassin_skin_8", new SkuData("assassin_skin_8", "刺客皮肤 - 狰", "Skin - Assassin - Zheng Beast", "刺客皮膚 - 猙", "Skin - Thích Khách - Tranh", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "60", "60", "20060", "", "231821", "5001073486", "ios.com.chillyroom.dungeonshooter.assassin_skin_8", "com.chillyroom.dungeonshooter.assassin_skin_8", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_120", "2.5.0-2020春节版本") },
            { "elf_skin_7", new SkuData("elf_skin_7", "精灵皮肤 - 毕方", "Skin - Elf - Bifang Bird", "精靈皮膚 - 畢方", "Skin - Tinh Linh - Tất Phương", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "61", "61", "20061", "", "231822", "5001091263", "ios.com.chillyroom.dungeonshooter.elf_skin_7", "com.chillyroom.dungeonshooter.elf_skin_7", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_125", "2.5.0-2020春节版本") },
            { "wizard_skin_9", new SkuData("wizard_skin_9", "法师皮肤 - 九尾狐", "Skin - Witch - Nine-Tail Fox", "法師皮膚 - 九尾狐", "Skin - Pháp Sư - Cửu Vĩ Hồ", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "62", "62", "20062", "", "231823", "5001109040", "ios.com.chillyroom.dungeonshooter.mage_skin_9", "com.chillyroom.dungeonshooter.mage_skin_9", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_119", "2.5.0-2020春节版本") },
            { "pastor_skin_7", new SkuData("pastor_skin_7", "牧师皮肤 - 猼訑", "Skin - Priestess - Boyi the Valor", "牧師皮膚 - 猼訑", "Skin - Mục Sư - Bác Di", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "63", "63", "20063", "", "231824", "5001126817", "ios.com.chillyroom.dungeonshooter.pastor_skin_7", "com.chillyroom.dungeonshooter.pastor_skin_7", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_127", "2.5.0-2020春节版本") },
            { "vampire_skin_8", new SkuData("vampire_skin_8", "吸血鬼皮肤 - 梼杌", "Skin - Vampire - Firebrand Taowu", "吸血鬼皮膚 - 檮杌", "Skin - Ma Ca Rồng - Đào Ngột", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "64", "64", "20064", "", "231825", "5001144594", "ios.com.chillyroom.dungeonshooter.vampire_skin_8", "com.chillyroom.dungeonshooter.vampire_skin_8", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_123", "2.5.0-2020春节版本") },
            { "druid_skin_7", new SkuData("druid_skin_7", "德鲁伊皮肤 - 白泽", "Skin - Druid - Baize the Wise", "德魯伊皮膚 - 白澤", "Skin - Druid - Bạch Trạch", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "65", "65", "20065", "", "231826", "5001162371", "ios.com.chillyroom.dungeonshooter.druid_skin_7", "com.chillyroom.dungeonshooter.druid_skin_7", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_128", "2.5.0-2020春节版本") },
            { "alchemist_skin_6", new SkuData("alchemist_skin_6", "炼金术士皮肤 - 獓因", "Skin - Alchemist - Ogre Aoyin", "煉金術士皮膚 - 獓因", "Skin - Thuật Sĩ Luyện Kim - Ngao Nhân", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "66", "66", "20066", "", "231827", "5001180148", "ios.com.chillyroom.dungeonshooter.alchemist_skin_6", "com.chillyroom.dungeonshooter.alchemist_skin_6", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_121", "2.5.0-2020春节版本") },
            { "necromancer", new SkuData("necromancer", "角色 - 死灵法师", "Character - Necromancer", "", "Nhân vật - Pháp Sư Tử Linh", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "67", "67", "20067", "", "231828", "5001197925", "ios.com.chillyroom.dungeonshooter.necromancer", "com.chillyroom.dungeonshooter.necromancer", "*******************/*******************", "Hero", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_132", "2.5.0-2020春节版本") },
            { "skill_viking_1", new SkuData("skill_viking_1", "狂战士二技能 - 散打", "Skill - Berserker - Free Style", "狂戰士二技能 - 散打", "Kỹ năng - Berserker - Sanda", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "68", "68", "20068", "", "231829", "5001215702", "ios.com.chillyroom.dungeonshooter.skill_viking_1", "com.chillyroom.dungeonshooter.skill_viking_1", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/ui.png#ui_skill17", "2.5.0-2020春节版本") },
            { "skill_necromancer_1", new SkuData("skill_necromancer_1", "死灵法师二技能 - 灾祸之石", "Skill - Necromancer - Omen Stone", "死靈法師二技能 - 災禍之石", "Kỹ năng - Pháp Sư Tử Linh - Đá Tai Họa", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "69", "69", "20069", "", "231830", "5001233479", "ios.com.chillyroom.dungeonshooter.skill_necromancer_1", "com.chillyroom.dungeonshooter.skill_necromancer_1", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/ui.png#ui_skill16", "2.5.0-2020春节版本") },
            { "plantpot_3", new SkuData("plantpot_3", "花圃#8", "Garden Plot 8", "花圃#8", "Vườn Hoa 8", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "70", "70", "20070", "", "231831", "5001251256", "ios.com.chillyroom.dungeonshooter.gardenpot_3", "com.chillyroom.dungeonshooter.gardenpot_3", "*******************/*******************", "PlantPot", "All", "Assets/RGTexture/heroroom/decoration/common/common_room_item_2.png#common_room_item_2_4", "2.5.0-2020春节版本") },
            { "taoist", new SkuData("taoist", "角色 - 道士", "Character - Taoist", "角色 - 道士", "Nhân vật - Đạo Sĩ", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "71", "71", "20071", "", "231832", "5001269033", "ios.com.chillyroom.dungeonshooter.taoist", "com.chillyroom.dungeonshooter.taoist", "*******************/*******************", "Hero", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_138", "2.7.0-2020暑假版本") },
            { "skill_taoist_1", new SkuData("skill_taoist_1", "道士二技能 - 乾坤大挪移", "Skill - Taoist - Bagua", "道士二技能 - 乾坤大挪移", "Kỹ năng - Đạo Sĩ - Càn Khôn Đại Na Di", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "72", "72", "20072", "", "231833", "5001286810", "ios.com.chillyroom.dungeonshooter.skill_taoist_1", "com.chillyroom.dungeonshooter.skill_taoist_1", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/ui3.png#skill_16_2", "2.7.0-2020暑假版本") },
            { "skill_mage_2", new SkuData("skill_mage_2", "法师三技能-火焰飓风", "Skill - Witch - Firestorm", "法師三技能-火焰颶風", "Kỹ năng - Pháp Sư - Bão Lửa", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "73", "73", "20073", "", "231834", "5001304587", "ios.com.chillyroom.dungeonshooter.skill_mage_2", "com.chillyroom.dungeonshooter.skill_mage_2", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/ui3.png#skill_2_2", "2.7.0-2020暑假版本") },
            { "skill_engineer_2", new SkuData("skill_engineer_2", "工程师三技能 - 迎击炮台", "Skill - Engineer - Interceptor", "工程師三技能 - 迎擊砲台", "Kỹ năng - Kỹ Sư - Pháo Đài Ngăn Cản", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "74", "74", "20074", "", "231835", "5001322364", "ios.com.chillyroom.dungeonshooter.skill_engineer_2", "com.chillyroom.dungeonshooter.skill_engineer_2", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/ui3.png#skill_5_2", "2.7.0-2020暑假版本") },
            { "skill_werewolf_2", new SkuData("skill_werewolf_2", "狼人三技能 - 吞噬", "Skill - Werewolf - Devour", "狼人三技能 - 吞噬", "Kỹ năng - Người Sói - Nuốt Chửng", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "75", "75", "20075", "", "231836", "5001340141", "ios.com.chillyroom.dungeonshooter.skill_werewolf_2", "com.chillyroom.dungeonshooter.skill_werewolf_2", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/ui3.png#skill_9_2", "2.7.0-2020暑假版本") },
            { "skill_priest_2", new SkuData("skill_priest_2", "牧师三技能 - 月影之息", "Skill - Priestess - Moon Shadow", "牧師三技能 - 月影之息", "Kỹ năng - Mục Sư - Khí Tức Bóng Trăng", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "76", "76", "20076", "", "231837", "5001357918", "ios.com.chillyroom.dungeonshooter.skill_priest_2", "com.chillyroom.dungeonshooter.skill_priest_2", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/ui3.png#skill_10_2", "2.7.0-2020暑假版本") },
            { "rogue_skin_8", new SkuData("rogue_skin_8", "游侠皮肤 - 立体主义", "Skin - Rogue - Cubism", "遊俠皮膚 - 立體主義", "Skin - Hiệp Sĩ - Chủ Nghĩa Lập Thể", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "77", "77", "20077", "", "231838", "5001375695", "ios.com.chillyroom.dungeonshooter.rogue_skin_8", "com.chillyroom.dungeonshooter.rogue_skin_8", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_139", "2.7.0-2020暑假版本") },
            { "necromancer_skin_4", new SkuData("necromancer_skin_4", "死灵法师皮肤 - 陆吾", "Skin - Necromancer - Guardian Lu Wu", "死靈法師皮膚 - 陸吾", "Skin - Pháp Sư Tử Linh - Lục Ngô", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "78", "78", "20078", "", "231839", "5001393472", "ios.com.chillyroom.dungeonshooter.necromancer_skin_4", "com.chillyroom.dungeonshooter.necromancer_skin_4", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_142", "2.7.0-2020暑假版本") },
            { "officer_skin_2", new SkuData("officer_skin_2", "警官皮肤 - 锦衣卫", "Skin - Officer - Jin Yi Wei", "警官皮膚 - 錦衣衛", "Skin - Cảnh Sát - Cẩm Y Vệ", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "79", "79", "20079", "", "231840", "5001411249", "ios.com.chillyroom.dungeonshooter.officer_skin_2", "com.chillyroom.dungeonshooter.officer_skin_2", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_144", "2.7.0-2020暑假版本") },
            { "taoist_skin_3", new SkuData("taoist_skin_3", "道士皮肤 - 天君", "Skin - Taoist - Sky Ruler", "道士皮膚 - 天君", "Skin - Đạo Sĩ - Thiên Quân", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "80", "80", "20080", "", "231841", "5001429026", "ios.com.chillyroom.dungeonshooter.taoist_skin_3", "com.chillyroom.dungeonshooter.taoist_skin_3", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_147", "2.7.0-2020暑假版本") },
            { "fish_chips_5", new SkuData("fish_chips_5", "小鱼干5", "Fish Chips*5", "", "Cá Khô*5", 5, 0, "5", "$0.99/¥6", "₽79/¥6", "₫25000/$0.99", "81", "81", "20081", "", "231842", "5001446803", "ios.com.chillyroom.dungeonshooter.fish_chips_5", "com.chillyroom.dungeonshooter.fish_chips_5", "*******************/*******************", "FishChip", "All", "Assets/RGTexture/ui/common/ui.png#ui_84", "2.7.0-2020暑假版本") },
            { "fish_chips_10", new SkuData("fish_chips_10", "小鱼干12", "Fish Chips*12", "小魚乾12", "Cá Khô*12", 12, 0, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "82", "82", "20082", "", "231843", "5001464580", "ios.com.chillyroom.dungeonshooter.fish_chips_10", "com.chillyroom.dungeonshooter.fish_chips_10", "*******************/*******************", "FishChip", "All", "Assets/RGTexture/ui/common/ui.png#ui_85", "2.7.0-2020暑假版本") },
            { "fish_chips_20", new SkuData("fish_chips_20", "小鱼干28", "Fish Chips*28", "小魚乾28", "Cá Khô*28", 28, 0, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "83", "83", "20083", "83838", "231844", "5001482357", "ios.com.chillyroom.dungeonshooter.fish_chips_20", "com.chillyroom.dungeonshooter.fish_chips_20", "4637121953801084017/4636472375067537543", "FishChip", "All", "Assets/RGTexture/ui/common/ui.png#ui_86", "2.7.0-2020暑假版本") },
            { "viking_skin_10", new SkuData("viking_skin_10", "狂战士皮肤 - 皇帝的新衣", "Skin- Berserker - Emperor's New Clothes", "狂戰士皮膚 - 皇帝的新衣", "Skin- Berserker - Áo Mới Của Vua", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "84", "84", "20084", "", "231845", "5001500134", "ios.com.chillyroom.dungeonshooter.viking_skin_10", "com.chillyroom.dungeonshooter.viking_skin_10", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_157", "2.8.0-2020国庆版本") },
            { "skill_alchemist_2", new SkuData("skill_alchemist_2", "炼金术士三技能 - 蜜汁饮剂", "Skill - Alchemist - Concoction", "煉金術士三技能 - 蜜汁飲劑", "Kỹ năng - Thuật Sĩ Luyện Kim - Thuốc Mật Ong", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "85", "85", "20085", "", "231846", "5001517911", "ios.com.chillyroom.dungeonshooter.skill_alchemist_2", "com.chillyroom.dungeonshooter.skill_alchemist_2", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/ui3.png#skill_4_2", "2.8.0-2020国庆版本") },
            { "elf_skin_8", new SkuData("elf_skin_8", "精灵皮肤 - 糖梅仙子", "Skin - Elf - Sugar Plum Fairy", "精靈皮膚 - 糖梅仙子", "Skin - Tinh Linh - Tiên Nữ Mai Ngọt", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "86", "86", "20086", "", "231847", "5001535688", "ios.com.chillyroom.dungeonshooter.elf_skin_8", "com.chillyroom.dungeonshooter.elf_skin_8", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_160", "2.9.0-2020感恩节版本") },
            { "skill_ranger_2", new SkuData("skill_ranger_2", "游侠三技能 - 地堂刀", "Skill - Rogue - Cartwheel", "遊俠三技能 - 地堂刀", "Kỹ năng - Hiệp Sĩ - Dao Địa Đường", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "87", "87", "20087", "", "231848", "5001553465", "ios.com.chillyroom.dungeonshooter.skill_ranger_2", "com.chillyroom.dungeonshooter.skill_ranger_2", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/ui3.png#skill_1_2", "3.0.0-2021春节版本") },
            { "skill_necromancer_2", new SkuData("skill_necromancer_2", "死灵法师三技能 - 亡灵复生", "Skill - Necromancer - Souls Resurrect", "死靈法師三技能 - 亡靈復生", "Kỹ năng - Pháp Sư Tử Linh - Vong Linh Hồi Sinh", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "88", "88", "20088", "", "231849", "5001571242", "ios.com.chillyroom.dungeonshooter.skill_necromancer_2", "com.chillyroom.dungeonshooter.skill_necromancer_2", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/ui3.png#skill_14_2", "3.0.0-2021春节版本") },
            { "viking_skin_11", new SkuData("viking_skin_11", "狂战士皮肤 - 武松", "Skin - Berserker - Fighter Wu Song", "狂戰士皮膚 - 武松", "Skin - Berserker - Võ Tòng", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "89", "89", "20089", "", "231850", "5001589019", "ios.com.chillyroom.dungeonshooter.viking_skin_11", "com.chillyroom.dungeonshooter.viking_skin_11", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_167", "3.0.0-2021春节版本") },
            { "paladin_skin_10", new SkuData("paladin_skin_10", "圣骑士皮肤 - 关羽", "Skin - Paladin - Guan Yu", "聖騎士皮膚 - 關羽", "Skin - Kỵ Sĩ Thánh - Quan Vũ", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "90", "90", "20090", "", "231851", "5001606796", "ios.com.chillyroom.dungeonshooter.paladin_skin_10", "com.chillyroom.dungeonshooter.paladin_skin_10", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_32", "3.0.0-2021春节版本") },
            { "rogue_skin_10", new SkuData("rogue_skin_10", "游侠皮肤 - 变脸", "Skin - Rogue - Multi-Faced", "遊俠皮膚 - 變臉", "Skin - Hiệp Sĩ - Đổi Mặt", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "91", "91", "20091", "", "231852", "5001624573", "ios.com.chillyroom.dungeonshooter.rogue_skin_10", "com.chillyroom.dungeonshooter.rogue_skin_10", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_25", "3.0.0-2021春节版本") },
            { "vampire_skin_9", new SkuData("vampire_skin_9", "吸血鬼皮肤 - 范无救与谢必安", "Skin - Vampire - Soul Escorts", "", "Skin - Ma Ca Rồng - Phạm Vô Cứu vs Tạ Bất An", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "92", "92", "20092", "", "231853", "5001642350", "ios.com.chillyroom.dungeonshooter.vampire_skin_9", "com.chillyroom.dungeonshooter.vampire_skin_9", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_31", "3.0.0-2021春节版本") },
            { "werewolf_skin_13", new SkuData("werewolf_skin_13", "狼人皮肤 - 哪吒", "Skin - Werewolf - Li'l Rascal Nezha", "狼人皮膚 - 哪吒", "Skin - Người Sói - Na Tra", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "93", "93", "20093", "", "231854", "5001660127", "ios.com.chillyroom.dungeonshooter.werewolf_skin_13", "com.chillyroom.dungeonshooter.werewolf_skin_13", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_163", "3.0.0-2021春节版本") },
            { "necromancer_skin_5", new SkuData("necromancer_skin_5", "死灵法师皮肤 - 青蛇", "Skin - Necromancer - Green Snake", "", "Skin - Pháp Sư Tử Linh - Thanh Xà", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "94", "94", "20094", "", "231855", "5001677904", "ios.com.chillyroom.dungeonshooter.necromancer_skin_5", "com.chillyroom.dungeonshooter.necromancer_skin_5", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_168", "3.0.0-2021春节版本") },
            { "pastor_skin_10", new SkuData("pastor_skin_10", "牧师皮肤 - 白蛇", "Skin - Priestess - White Snake", "牧師皮膚 - 白蛇", "Skin - Mục Sư - Bạch Xà", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "95", "95", "20095", "", "231856", "5001695681", "ios.com.chillyroom.dungeonshooter.pastor_skin_10", "com.chillyroom.dungeonshooter.pastor_skin_10", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_164", "3.0.0-2021春节版本") },
            { "taoist_skin_4", new SkuData("taoist_skin_4", "道士皮肤 - 太上老君", "Skin - Taoist - Sage of Taoism", "道士皮膚 - 太上老君", "Skin - Đạo Sĩ - Thái Thượng Lão Quân", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "96", "96", "20096", "", "231857", "5001713458", "ios.com.chillyroom.dungeonshooter.taoist_skin_4", "com.chillyroom.dungeonshooter.taoist_skin_4", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_170", "3.0.0-2021春节版本") },
            { "assassin_skin_10", new SkuData("assassin_skin_10", "刺客皮肤 - 伍子胥", "Skin - Assassin - King's Hand Wu Zixu", "刺客皮膚 - 伍子胥", "Skin - Thích Khách - Ngũ Tử Tư", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "97", "97", "20097", "", "231858", "5001731235", "ios.com.chillyroom.dungeonshooter.assassin_skin_10", "com.chillyroom.dungeonshooter.assassin_skin_10", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_27", "3.0.0-2021春节版本") },
            { "robot_skin_10", new SkuData("robot_skin_10", "机器人皮肤 - 咯咯作响的提线木偶", "Skin - Robot - Squeaky Marionette", "機器人皮膚 - 咯咯作響的提線木偶", "Skin - Robot - Con Rối Lọc Cọc", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "98", "98", "20098", "", "231859", "5001749012", "ios.com.chillyroom.dungeonshooter.robot_skin_10", "com.chillyroom.dungeonshooter.robot_skin_10", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_175", "3.1.5-2021加固测试版本") },
            { "engineer_skin_11", new SkuData("engineer_skin_11", "工程师皮肤 - 行为怪诞的小丑", "Skin - Engineer - Strange Clown", "工程師皮膚 - 行為怪誕的小丑", "Skin - Kỹ Sư - Chú Hề Kỳ Quặc", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "99", "100", "20099", "", "231860", "5001766789", "ios.com.chillyroom.dungeonshooter.engineer_skin_11", "com.chillyroom.dungeonshooter.engineer_skin_11", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_176", "3.1.5-2021加固测试版本") },
            { "taoist_skin_7", new SkuData("taoist_skin_7", "道士皮肤 - 雷霆", "Skin - Taoist - Thunderstorm", "道士皮膚 - 雷霆", "Skin - Đạo Sĩ - Lôi Bạo", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "102", "103", "20102", "", "231863", "5001820120", "ios.com.chillyroom.dungeonshooter.taoist_skin_7", "com.chillyroom.dungeonshooter.taoist_skin_7", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_186", "3.2.0-2021暑假版本") },
            { "assassin_skin_12", new SkuData("assassin_skin_12", "刺客皮肤 - 欺诈", "Skin - Assassin - Treachery", "刺客皮膚 - 欺詐", "Skin - Thích Khách - Lừa Đảo", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "103", "104", "20103", "", "231864", "5001837897", "ios.com.chillyroom.dungeonshooter.assassin_skin_12", "com.chillyroom.dungeonshooter.assassin_skin_12", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_188", "3.2.0-2021暑假版本") },
            { "werewolf_skin_14", new SkuData("werewolf_skin_14", "狼人皮肤 - 天狼", "Skin - Werewolf - Sirius", "狼人皮膚 - 天狼", "Skin - Người Sói - Thiên Lang", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "104", "105", "20104", "", "231865", "5001855674", "ios.com.chillyroom.dungeonshooter.werewolf_skin_14", "com.chillyroom.dungeonshooter.werewolf_skin_14", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_179", "3.2.0-2021暑假版本") },
            { "vampire_skin_10", new SkuData("vampire_skin_10", "吸血鬼皮肤 - 湮灭", "Skin - Vampire - Annihilation", "吸血鬼皮膚 - 湮滅", "Skin - Ma Ca Rồng - Chôn Vùi", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "105", "106", "20105", "", "231866", "5001873451", "ios.com.chillyroom.dungeonshooter.vampire_skin_10", "com.chillyroom.dungeonshooter.vampire_skin_10", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_193", "3.2.0-2021暑假版本") },
            { "elf_skin_10", new SkuData("elf_skin_10", "精灵皮肤 - 人鱼公主", "Skin - Elf - Princess Mermaid", "精靈皮膚 - 人魚公主", "Skin - Tinh Linh - Công Chúa Người Cá", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "106", "107", "20106", "", "231867", "5001891228", "ios.com.chillyroom.dungeonshooter.elf_skin_10", "com.chillyroom.dungeonshooter.elf_skin_10", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_182", "3.2.0-2021暑假版本") },
            { "officer_skin_6", new SkuData("officer_skin_6", "警官皮肤 - 幽灵船长", "Skin - Officer - Captain Specter", "", "Skin - Cảnh Sát - Thuyền Trưởng U Linh", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "107", "108", "20107", "", "231868", "5001909005", "ios.com.chillyroom.dungeonshooter.officer_skin_6", "com.chillyroom.dungeonshooter.officer_skin_6", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_180", "3.2.0-2021暑假版本") },
            { "engineer_skin_12", new SkuData("engineer_skin_12", "工程师皮肤 - 罐装大王", "Skin - Engineer - Crowned Can", "工程師皮膚 - 罐裝大王", "Skin - Kỹ Sư - Vua Đồ Hộp", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "108", "109", "20108", "", "231869", "5001926782", "ios.com.chillyroom.dungeonshooter.engineer_skin_12", "com.chillyroom.dungeonshooter.engineer_skin_12", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_183", "3.2.0-2021暑假版本") },
            { "paladin_skin_13", new SkuData("paladin_skin_13", "圣骑士皮肤 - 锈蚀战甲", "Skin - Paladin - Eroded Mecha", "聖騎士皮膚 - 鏽蝕戰甲", "Skin - Kỵ Sĩ Thánh - Chiến Giáp Gỉ Sét", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "109", "110", "20109", "", "231870", "5001944559", "ios.com.chillyroom.dungeonshooter.paladin_skin_13", "com.chillyroom.dungeonshooter.paladin_skin_13", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_197", "3.3.0-2021国庆版本") },
            { "druid_skin_10", new SkuData("druid_skin_10", "德鲁伊皮肤 - 大发明家", "Skin - Druid - Great Inventor", "德魯伊皮膚 - 大發明家", "Skin - Druid - Nhà Phát Minh", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "110", "111", "20110", "", "231871", "5001962336", "ios.com.chillyroom.dungeonshooter.druid_skin_10", "com.chillyroom.dungeonshooter.druid_skin_10", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_202", "3.3.0-2021国庆版本") },
            { "viking_skin_13", new SkuData("viking_skin_13", "狂战士皮肤 - 钢铁焚化者", "Skin - Berserker - Iron Boiler", "狂戰士皮膚 - 鋼鐵焚化者", "Skin - Berserker - Kẻ Thiêu Đốt Sắt Thép", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "111", "112", "20111", "", "231872", "5001980113", "ios.com.chillyroom.dungeonshooter.viking_skin_13", "com.chillyroom.dungeonshooter.viking_skin_13", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ui2.png#ui2_198", "3.3.0-2021国庆版本") },
            { "knight_skin_14", new SkuData("knight_skin_14", "骑士皮肤 - 首席百夫长", "Skin - Knight - Chief Centurion", "騎士皮膚 - 首席百夫長", "Skin - Kỵ Sĩ - Bách Phu Trưởng", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "112", "113", "20112", "", "231873", "6000013645", "ios.com.chillyroom.dungeonshooter.knight_skin_14", "com.chillyroom.dungeonshooter.knight_skin_14", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/knight_14_ui.png", "4.0.0-2022春节版本") },
            { "paladin_skin_14", new SkuData("paladin_skin_14", "圣骑士皮肤 - 斯巴达战士", "Skin - Paladin - Spartan Warrior", "聖騎士皮膚 - 斯巴達戰士", "Skin - Kỵ Sĩ Thánh - Chiến Binh Spartan", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "113", "114", "20113", "", "231874", "6000013646", "ios.com.chillyroom.dungeonshooter.paladin_skin_14", "com.chillyroom.dungeonshooter.paladin_skin_14", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/paladin_14_ui.png", "4.0.0-2022春节版本") },
            { "assassin_skin_13", new SkuData("assassin_skin_13", "刺客皮肤 - 波斯王子", "Skin - Assassin - Prince of Persia", "刺客皮膚 - 波斯王子", "Skin - Thích Khách - Hoàng Tử Ba Tư", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "114", "115", "20114", "", "231875", "6000013647", "ios.com.chillyroom.dungeonshooter.assassin_skin_13", "com.chillyroom.dungeonshooter.assassin_skin_13", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/assassin_13_ui.png", "4.0.0-2022春节版本") },
            { "taoist_skin_9", new SkuData("taoist_skin_9", "道士皮肤 - 描仙郎", "Skin - Taoist - Ink Wash - Miao Xian Lang", "道士皮膚 - 描仙郎", "Skin - Đạo Sĩ - Makisenro", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "115", "116", "20115", "", "231876", "6000013648", "ios.com.chillyroom.dungeonshooter.taoist_skin_9", "com.chillyroom.dungeonshooter.taoist_skin_9", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/taoist_9_ui.png", "4.0.0-2022春节版本") },
            { "druid_skin_11", new SkuData("druid_skin_11", "德鲁伊皮肤 - 神鹿", "Skin - Druid - Sacred Deer", "德魯伊皮膚 - 神鹿", "Skin - Druid - Hươu Thần", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "116", "117", "20116", "", "231877", "6000013649", "ios.com.chillyroom.dungeonshooter.druid_skin_11", "com.chillyroom.dungeonshooter.druid_skin_11", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/druid_11_ui.png", "4.0.0-2022春节版本") },
            { "rogue_skin_13", new SkuData("rogue_skin_13", "游侠皮肤 - 汉谟拉比王", "Skin - Rogue - King Hammurabi", "遊俠皮膚 - 漢謨拉比王", "Skin - Hiệp Sĩ - Vua Hammurabi", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "117", "118", "20117", "", "231878", "6000013650", "ios.com.chillyroom.dungeonshooter.rogue_skin_13", "com.chillyroom.dungeonshooter.rogue_skin_13", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ranger_13_ui.png", "4.0.0-2022春节版本") },
            { "engineer_skin_13", new SkuData("engineer_skin_13", "工程师皮肤 - 古祖铸铜者", "Skin - Engineer - Ancient Bronze Smith", "工程師皮膚 - 古祖鑄銅者", "Skin - Kỹ Sư - Người Đúc Đồng Cổ Đại", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "118", "119", "20118", "", "231879", "6000013651", "ios.com.chillyroom.dungeonshooter.engineer_skin_13", "com.chillyroom.dungeonshooter.engineer_skin_13", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/engineer_13_ui.png", "4.0.0-2022春节版本") },
            { "alchemist_skin_10", new SkuData("alchemist_skin_10", "炼金术士皮肤 - 阿兹台克祭祀长", "Skin - Alchemist - Azteca Priest", "煉金術士皮膚 - 阿茲台克祭祀長", "Skin - Thuật Sĩ Luyện Kim - Tế Tư Trưởng Aztec", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "119", "120", "20119", "", "231880", "6000013652", "ios.com.chillyroom.dungeonshooter.alchemist_skin_10", "com.chillyroom.dungeonshooter.alchemist_skin_10", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/alchemist_10_ui.png", "4.0.0-2022春节版本") },
            { "wizard_skin_14", new SkuData("wizard_skin_14", "法师皮肤 - 御神子", "Skin - Witch - Mikan Ko", "法師皮膚 - 御神子", "Skin - Pháp Sư - Mikako", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "120", "121", "20120", "", "231881", "6000013653", "ios.com.chillyroom.dungeonshooter.mage_skin_14", "com.chillyroom.dungeonshooter.mage_skin_14", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/mage_14_ui.png", "4.0.0-2022春节版本") },
            { "warlock", new SkuData("warlock", "角色 - 恶魔术士", "Character - Demonmancer", "角色 - 惡魔術士", "Nhân vật - Thuật sĩ ác ma", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "123", "124", "20123", "", "231884", "6000018330", "ios.com.chillyroom.dungeonshooter.warlock", "com.chillyroom.dungeonshooter.warlock", "*******************/*******************", "Hero", "All", "Assets/RGTexture/ui/skin/warlock_0_ui.png", "4.2.0-2022暑假版本一") },
            { "assassin_skin_14", new SkuData("assassin_skin_14", "刺客皮肤 - 南十字星", "Skin - Assassin - Crux", "刺客皮膚 - 南十字星", "Skin - Thích Khách - Nam Thập Tự", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "124", "125", "20124", "", "231885", "6000018331", "ios.com.chillyroom.dungeonshooter.assassin_skin_14", "com.chillyroom.dungeonshooter.assassin_skin_14", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/assassin_14_ui.png", "4.2.0-2022暑假版本一") },
            { "elf_skin_12", new SkuData("elf_skin_12", "精灵皮肤 - 闪回", "Skin - Elf - Flashback", "精靈皮膚 - 閃回", "Skin - Tinh Linh - Hồi tưởng", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "125", "126", "20125", "", "231886", "6000018332", "ios.com.chillyroom.dungeonshooter.elf_skin_12", "com.chillyroom.dungeonshooter.elf_skin_12", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/elves_12_ui.png", "4.2.0-2022暑假版本一") },
            { "werewolf_skin_16", new SkuData("werewolf_skin_16", "狼人皮肤 - 惊骇", "Skin - Werewolf - Horror", "狼人皮膚 - 驚駭", "Skin - Người Sói - Kinh sợ", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "126", "127", "20126", "", "231887", "6000018334", "ios.com.chillyroom.dungeonshooter.werewolf_skin_16", "com.chillyroom.dungeonshooter.werewolf_skin_16", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/werewolf_15_ui.png", "4.2.0-2022暑假版本一") },
            { "knight_skin_15", new SkuData("knight_skin_15", "骑士皮肤 - 圣骑士我们走！", "Skin - Knight - Paladin, Let's Go!", "騎士皮膚 - 聖騎士我們走！", "Skin - Kỵ Sĩ - Chúng ta đi!", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "127", "128", "20127", "", "231888", "6000018336", "ios.com.chillyroom.dungeonshooter.knight_skin_15", "com.chillyroom.dungeonshooter.knight_skin_15", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/knight_15_ui.png", "4.2.0-2022暑假版本一") },
            { "druid_skin_12", new SkuData("druid_skin_12", "德鲁伊皮肤 - 悖论", "Skin - Druid - Paradox", "德魯伊皮膚 - 悖論", "Skin - Druid - Nghịch lý", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "128", "129", "20128", "", "231889", "6000018338", "ios.com.chillyroom.dungeonshooter.druid_skin_12", "com.chillyroom.dungeonshooter.druid_skin_12", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/druid_12_ui.png", "4.2.0-2022暑假版本一") },
            { "warlock_skin_1", new SkuData("warlock_skin_1", "恶魔术士皮肤 - 关于我转生为一个少女在异世界修习恶魔法术这档事", "Skin - Demonmancer - How I Reincarnated as a Girl…", "惡魔術士皮膚 - 關於我轉生為一個少女在異世界修習惡魔法術這檔事", "Skin - Thuật sĩ ác ma - Chuyện về tôi chuyển sinh...", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "129", "130", "20129", "", "231890", "6000018340", "ios.com.chillyroom.dungeonshooter.warlock_skin_1", "com.chillyroom.dungeonshooter.warlock_skin_1", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/warlock_1_ui.png", "4.2.0-2022暑假版本一") },
            { "airbender_skin_1", new SkuData("airbender_skin_1", "气宗皮肤 - 魔道巨擘", "Skin - Airbender - Devildom Magnate", "氣宗皮膚 - 魔道巨擘", "Skin - Khí Tông - Ngón tay ma đạo", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "130", "131", "20130", "", "231891", "6000018344", "ios.com.chillyroom.dungeonshooter.airbender_skin_1", "com.chillyroom.dungeonshooter.airbender_skin_1", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/airbender_1_ui.png", "4.2.0-2022暑假版本一") },
            { "season_coin_5", new SkuData("season_coin_5", "500赛季币", "Season Coin*500", "", "Xu Mùa Giải*500", 500, 0, "5", "$0.99/¥6", "₽79/¥6", "₫25000/$0.99", "131", "132", "20131", "", "231892", "6000019503", "ios.com.chillyroom.dungeonshooter.season_coin_5", "com.chillyroom.dungeonshooter.season_coin_5", "*******************/*******************", "SeasonCoin", "All", "Assets/RGTexture/ui/common/ui_season_coin.png#ui_season_coin_0", "4.2.5-2022暑假版本二") },
            { "season_coin_15", new SkuData("season_coin_15", "1700赛季币", "Season Coin*1700", "1700賽季幣", "Xu Mùa Giải*1700", 1700, 0, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "132", "133", "20132", "", "231893", "6000019504", "ios.com.chillyroom.dungeonshooter.season_coin_15", "com.chillyroom.dungeonshooter.season_coin_15", "4635639827510787213/4634914334553984538", "SeasonCoin", "All", "Assets/RGTexture/ui/common/ui_season_coin.png#ui_season_coin_1", "4.2.5-2022暑假版本二") },
            { "season_coin_30", new SkuData("season_coin_30", "3500赛季币", "Season Coin*3500", "3500賽季幣", "Xu Mùa Giải*3500", 3500, 0, "30", "$4.99/¥30", "₽399/¥30", "₫124000/$4.99", "133", "134", "20133", "", "231894", "6000019505", "ios.com.chillyroom.dungeonshooter.season_coin_30", "com.chillyroom.dungeonshooter.season_coin_30", "4637558555263124425/4637165942710777751", "SeasonCoin", "All", "Assets/RGTexture/ui/common/ui_season_coin.png#ui_season_coin_6", "4.2.5-2022暑假版本二") },
            { "skill_airbender_1", new SkuData("skill_airbender_1", "气宗技能 - 斗转星移", "Skill - Airbender - Orbiting Stars", "氣宗技能 - 斗轉星移", "Kỹ năng - Khí Tông - Vật Đổi Sao Dời", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "134", "135", "20134", "", "231895", "6000021326", "ios.com.chillyroom.dungeonshooter.skill_airbender_1", "com.chillyroom.dungeonshooter.skill_airbender_1", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/ui3.png#ui3_144", "4.3.0-2022秋季版本一") },
            { "wizard_skin_15", new SkuData("wizard_skin_15", "法师皮肤 - 幻纶", "Skin - Witch - Mirabella", "", "Skin - Pháp Sư - Khăn Phép", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "135", "136", "20135", "", "231896", "6000021327", "ios.com.chillyroom.dungeonshooter.mage_skin_15", "com.chillyroom.dungeonshooter.mage_skin_15", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/mage_15_ui.png", "4.3.0-2022秋季版本一") },
            { "taoist_skin_10", new SkuData("taoist_skin_10", "道士皮肤 - 汐羽", "Skin - Taoist - Eventide Angel", "道士皮膚 - 汐羽", "Skin - Đạo Sĩ - Cánh Nước", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "136", "137", "20136", "", "231897", "6000021328", "ios.com.chillyroom.dungeonshooter.taoist_skin_10", "com.chillyroom.dungeonshooter.taoist_skin_10", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/taoist_10_ui.png", "4.3.0-2022秋季版本一") },
            { "necromancer_skin_9", new SkuData("necromancer_skin_9", "死灵法师皮肤 - 冷月", "Skin - Necromancer - Dead Luna", "死靈法師皮膚 - 冷月", "Skin - Pháp Sư Tử Linh - Trăng Lạnh", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "137", "138", "20137", "", "231898", "6000021329", "ios.com.chillyroom.dungeonshooter.necromancer_skin_9", "com.chillyroom.dungeonshooter.necromancer_skin_9", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/necromancer_9_ui.png", "4.3.0-2022秋季版本一") },
            { "pastor_skin_14", new SkuData("pastor_skin_14", "牧师皮肤 - 圣洁", "Skin - Priest - Sanctity", "牧師皮膚 - 聖潔", "Skin - Mục Sư - Thánh Thiện", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "138", "139", "20138", "", "231899", "6000021697", "ios.com.chillyroom.dungeonshooter.preist_skin_14", "com.chillyroom.dungeonshooter.preist_skin_14", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/priest_14_ui.png", "4.3.0-2022秋季版本一") },
            { "viking_skin_16", new SkuData("viking_skin_16", "狂战士皮肤 - 利爪", "Skin - Berserker - Alpha Claw", "狂戰士皮膚 - 利爪", "Skin - Berserker - Vuốt Nhọn", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "139", "141", "20140", "", "231901", "10567", "ios.com.chillyroom.dungeonshooter.viking_skin_16", "com.chillyroom.dungeonshooter.viking_skin_16", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/viking_16_ui.png", "5.0.0-2023春节版本") },
            { "engineer_skin_14", new SkuData("engineer_skin_14", "工程师皮肤 - 伪装战甲", "Skin - Engineer - Impostor", "", "Skin - Kỹ Sư - Giáp Ngụy Trang", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "140", "140", "20139", "", "231900", "10566", "ios.com.chillyroom.dungeonshooter.engineer_skin_14", "com.chillyroom.dungeonshooter.engineer_skin_14", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/engineer_14_ui.png", "5.0.0-2023春节版本") },
            { "trapmaster", new SkuData("trapmaster", "角色 - 陷阱大师", "Character - Trap Master", "", "Nhân vật - Bậc Thầy Cạm Bẫy", 1, 1, "5", "$0.99/¥6", "₽79/¥6", "₫25000/$0.99", "141", "142", "20141", "", "231902", "12296", "ios.com.chillyroom.dungeonshooter.trapmaster", "com.chillyroom.dungeonshooter.trapmaster", "*******************/*******************", "Hero", "All", "Assets/RGTexture/ui/skin/trapMaster_0_ui.png", "5.0.0-2023春节版本") },
            { "hall_skin_1", new SkuData("hall_skin_1", "骑士之家大厅装饰-水帘洞府", "Lobby Decor - Cascade Cavern's Parlor", "", "Trang Trí Sảnh - Thủy Liêm Động", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "142", "143", "20142", "", "231903", "12297", "ios.com.chillyroom.dungeonshooter.hall_skin_1", "com.chillyroom.dungeonshooter.hall_skin_1", "4635639827510787213/4634914334553984538", "Other", "All", "Assets/RGTexture/ui/hero_room_skin/hall/hall_skin_icon_1.png", "5.0.0-2023春节版本") },
            { "airbender_skin_3", new SkuData("airbender_skin_3", "气宗皮肤 - 空", "Skin - Airbender - Sunyata", "氣宗皮膚 - 空", "Skin - Khí Tông - Không", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "143", "144", "20143", "", "231904", "12298", "ios.com.chillyroom.dungeonshooter.airbender_skin_3", "com.chillyroom.dungeonshooter.airbender_skin_3", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/airbender_3_ui.png", "5.0.0-2023春节版本") },
            { "rogue_skin_16", new SkuData("rogue_skin_16", "游侠皮肤 - 玉龙三太子", "Skin - Rogue - Yulong, Prince of the Western Sea", "遊俠皮膚 - 玉龍三太子", "Skin - Hiệp Sĩ - Ngọc Long Tam Thái Tử", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "144", "145", "20144", "", "231905", "12299", "ios.com.chillyroom.dungeonshooter.rogue_skin_16", "com.chillyroom.dungeonshooter.rogue_skin_16", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ranger_16_ui.png", "5.0.0-2023春节版本") },
            { "vampire_skin_13", new SkuData("vampire_skin_13", "吸血鬼皮肤 - 晶晶", "Skin - Vampire - Boneta", "吸血鬼皮膚 - 晶晶", "Skin - Ma Ca Rồng - Tinh Tinh", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "145", "146", "20145", "", "231906", "12300", "ios.com.chillyroom.dungeonshooter.vampire_skin_13", "com.chillyroom.dungeonshooter.vampire_skin_13", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/vampire_13_ui.png", "5.0.0-2023春节版本") },
            { "miner_skin_1", new SkuData("miner_skin_1", "矿工皮肤 - 玉兔", "Skin - Miner - Moon Rabbit", "礦工皮膚 - 玉兔", "Skin - Thợ Mỏ - Thỏ Ngọc", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "146", "147", "20146", "", "231907", "12301", "ios.com.chillyroom.dungeonshooter.miner_skin_1", "com.chillyroom.dungeonshooter.miner_skin_1", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/miner_1_ui.png", "5.0.0-2023春节版本") },
            { "druid_skin_13", new SkuData("druid_skin_13", "德鲁伊皮肤 - 我不是德鲁伊", "Skin - Druid - I Am Not Druid", "", "Skin - Druid - Tôi không phải Druid", 1, 1, "5", "$0.99/¥6", "₽79/¥6", "₫25000/$0.99", "147", "148", "20147", "", "231908", "12302", "ios.com.chillyroom.dungeonshooter.druid_skin_13", "com.chillyroom.dungeonshooter.druid_skin_13", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/druid_13_ui.png", "5.0.0-2023春节版本") },
            { "werewolf_skin_17", new SkuData("werewolf_skin_17", "狼人皮肤 - 大力牛魔王", "Skin - WereWolf - Bull Demon King", "狼人皮膚 - 大力牛魔王", "Skin - Người Sói - Đại Lực Ngưu Ma Vương", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "148", "149", "20148", "", "231909", "16896", "ios.com.chillyroom.dungeonshooter.werewolf_skin_17", "com.chillyroom.dungeonshooter.werewolf_skin_17", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/werewolf_16_ui.png", "5.1.0-2023三月版本") },
            { "taoist_skin_11", new SkuData("taoist_skin_11", "道士皮肤 - 昊天金阙无上至尊自然妙有弥罗至真玉皇上帝", "Skin - Taoist - Jade Emperor the Great", "道士皮膚 - 昊天金闕無上至尊自然妙有彌羅至真玉皇上帝", "Skin - Đạo Sĩ - Ngọc Hoàng Đại đế...", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "149", "150", "20149", "", "231910", "16897", "ios.com.chillyroom.dungeonshooter.taoist_skin_11", "com.chillyroom.dungeonshooter.taoist_skin_11", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/taoist_11_ui.png", "5.1.0-2023三月版本") },
            { "viking_skin_18", new SkuData("viking_skin_18", "狂战士皮肤 - 金翅大鹏雕", "Skin - Berserker - Golden-Winged Great Peng", "狂戰士皮膚 - 金翅大鵬雕", "Skin - Berserker - Đại Bàng Cánh Vàng", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "150", "151", "20150", "", "231911", "20447", "ios.com.chillyroom.dungeonshooter.viking_skin_18", "com.chillyroom.dungeonshooter.viking_skin_18", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/viking_18_ui.png", "5.2.0-2023四月版本") },
            { "wizard_skin_17", new SkuData("wizard_skin_17", "法师皮肤 - 孔雀公主", "Skin - Witch - Peafowl Princess", "法師皮膚 - 孔雀公主", "Skin - Pháp Sư - Khổng Tước Công Chúa", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "151", "152", "20151", "", "231912", "20448", "ios.com.chillyroom.dungeonshooter.mage_skin_17", "com.chillyroom.dungeonshooter.mage_skin_17", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/mage_17_ui.png", "5.2.0-2023四月版本") },
            { "new_pkg_16", new SkuData("new_pkg_16", "超级入门礼包", "Super Novice Pack", "超級入門禮包", "Quà Nhập Môn Siêu Cấp", 1, 1, "1", "$0.99/¥1", "₽79/¥1", "₫25000/$0.99", "152", "153", "20152", "", "231913", "25321", "ios.com.chillyroom.dungeonshooter.new_pkg_16", "com.chillyroom.dungeonshooter.new_pkg_16", "*******************/*******************", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010003.png", "5.3.0-2023七月版本") },
            { "new_pkg_17", new SkuData("new_pkg_17", "入门角色皮肤礼包", "Starter Character & Skin Pack", "", "Quà Skin Nhân Vật Nhập Môn", 1, 1, "5", "$0.99/¥6", "₽79/¥6", "₫25000/$0.99", "153", "154", "20153", "", "231914", "25322", "ios.com.chillyroom.dungeonshooter.new_pkg_17", "com.chillyroom.dungeonshooter.new_pkg_17", "*******************/*******************", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010004.png", "5.3.0-2023七月版本") },
            { "new_pkg_5", new SkuData("new_pkg_5", "优惠宝石礼包", "Special Offer Gem Pack", "", "Quà Đá Ưu Đãi", 1, 0, "5", "$0.99/¥6", "₽79/¥6", "₫25000/$0.99", "154", "155", "20154", "", "231915", "25323", "ios.com.chillyroom.dungeonshooter.new_pkg_5", "com.chillyroom.dungeonshooter.new_pkg_5", "*******************/*******************", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010005.png", "5.3.0-2023七月版本") },
            { "new_pkg_6", new SkuData("new_pkg_6", "超值宝石礼包", "Premium Gem Pack", "", "Quà Đá Giá Trị", 1, 0, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "155", "156", "20155", "", "231916", "25324", "ios.com.chillyroom.dungeonshooter.new_pkg_6", "com.chillyroom.dungeonshooter.new_pkg_6", "4635639827510787213/4634914334553984538", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010006.png", "5.3.0-2023七月版本") },
            { "new_pkg_18", new SkuData("new_pkg_18", "陷阱大师+矿工技能礼包", "Trap Master&Miner Skill Pack", "陷阱大師+礦工技能禮包", "Bậc Thầy Cạm Bẫy+Quà Kỹ Năng Thợ Mỏ", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "156", "157", "20156", "", "231917", "25325", "ios.com.chillyroom.dungeonshooter.new_pkg_18", "com.chillyroom.dungeonshooter.new_pkg_18", "*******************/*******************", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010007.png", "5.3.0-2023七月版本") },
            { "new_pkg_19", new SkuData("new_pkg_19", "陷阱大师+矿工角色技能礼包", "Trap Master&Miner Character+Skill Pack", "陷阱大師+礦工角色技能禮包", "Bậc Thầy Cạm Bẫy+Quà Kỹ Năng N.Vật Thợ Mỏ", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "157", "158", "20157", "", "231918", "25326", "ios.com.chillyroom.dungeonshooter.new_pkg_19", "com.chillyroom.dungeonshooter.new_pkg_19", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010008.png", "5.3.0-2023七月版本") },
            { "new_pkg_9", new SkuData("new_pkg_9", "回归礼包", "Welcome Back Pack", "", "Quà Trở Về", 1, 0, "1", "$0.99/¥1", "₽79/¥1", "₫25000/$0.99", "158", "159", "20158", "", "231919", "25327", "ios.com.chillyroom.dungeonshooter.new_pkg_9", "com.chillyroom.dungeonshooter.new_pkg_9", "*******************/*******************", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010009.png", "5.3.0-2023七月版本") },
            { "new_pkg_20", new SkuData("new_pkg_20", "群星的召唤皮肤礼包", "Call of Stars Skin Pack", "群星的召喚皮膚禮包", "Quà Skin Triệu Hồi Chòm Sao", 1, 1, "45", "$8.99/¥54", "₽749/¥54", "₫223000/$8.99", "159", "160", "20159", "", "231920", "25328", "ios.com.chillyroom.dungeonshooter.new_pkg_20", "com.chillyroom.dungeonshooter.new_pkg_20", "4634958946912594240/4634716269219238891", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010010.png", "5.3.0-2023七月版本") },
            { "new_pkg_21", new SkuData("new_pkg_21", "欢乐无限皮肤礼包", "Infinite Joy Skin Pack", "歡樂無限皮膚禮包", "Quà Skin Vui Vẻ", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "160", "161", "20160", "", "231921", "25329", "ios.com.chillyroom.dungeonshooter.new_pkg_21", "com.chillyroom.dungeonshooter.new_pkg_21", "4635639827510787213/4634914334553984538", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010011.png", "5.3.0-2023七月版本") },
            { "new_pkg_22", new SkuData("new_pkg_22", "机械狂潮皮肤礼包", "Robotic Frenzy Skin Pack", "機械狂潮皮膚禮包", "Quà Skin Trào Lưu Cơ Giới", 1, 1, "40", "$7.99/¥48", "₽649/¥48", "₫199000/$7.99", "161", "162", "20161", "", "231922", "25330", "ios.com.chillyroom.dungeonshooter.new_pkg_22", "com.chillyroom.dungeonshooter.new_pkg_22", "4638592971698023487/4637530393979686162", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010012.png", "5.3.0-2023七月版本") },
            { "new_pkg_13", new SkuData("new_pkg_13", "星辰与大海皮肤礼包", "Stars & Seven Seas Skin Pack", "星辰與大海皮膚禮包", "Quà Skin Sao Và Biển", 1, 1, "25", "$4.99/¥30", "₽399/¥30", "₫124000/$4.99", "162", "163", "20162", "", "231923", "25331", "ios.com.chillyroom.dungeonshooter.new_pkg_13", "com.chillyroom.dungeonshooter.new_pkg_13", "4637558555263124425/4637165942710777751", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010013.png", "5.3.0-2023七月版本") },
            { "new_pkg_14", new SkuData("new_pkg_14", "远古神话皮肤礼包", "Ancient Legends Skin Pack", "遠古神話皮膚禮包", "Quà Skin Thần Thoại Viễn Cổ", 1, 1, "40", "$7.99/¥48", "₽649/¥48", "₫199000/$7.99", "163", "164", "20163", "", "231924", "25332", "ios.com.chillyroom.dungeonshooter.new_pkg_14", "com.chillyroom.dungeonshooter.new_pkg_14", "4638592971698023487/4637530393979686162", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010014.png", "5.3.0-2023七月版本") },
            { "new_pkg_15", new SkuData("new_pkg_15", "神仙？妖怪！皮肤礼包", "Celestial? Or Bestial Skin Pack", "神仙？妖怪！皮膚禮包", "Quà Thần tiên? Yêu quái!", 1, 1, "40", "$7.99/¥48", "₽649/¥48", "₫199000/$7.99", "164", "165", "20164", "", "231925", "25333", "ios.com.chillyroom.dungeonshooter.new_pkg_15", "com.chillyroom.dungeonshooter.new_pkg_15", "4638592971698023487/4637530393979686162", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010015.png", "5.3.0-2023七月版本") },
            { "skill_miner_1", new SkuData("skill_miner_1", "矿工二技能 - 矿车速递", "Skill - Miner - Cart Delivery", "礦工二技能 - 礦車速遞", "Kỹ năng - Thợ Mỏ - Giao Hàng Xe Khoáng", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "165", "166", "20165", "", "231926", "25334", "ios.com.chillyroom.dungeonshooter.skill_miner_1", "com.chillyroom.dungeonshooter.skill_miner_1", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/miner_skill_1_icon.png#miner_skill_1_icon_0", "5.3.0-2023七月版本") },
            { "wizard_skin_19", new SkuData("wizard_skin_19", "法师皮肤 - 天鹅座", "Skin - Witch - Cygnus", "法師皮膚 - 天鵝座", "Skin - Pháp Sư - Chòm Thiên Nga", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "166", "167", "20166", "", "231927", "25335", "ios.com.chillyroom.dungeonshooter.mage_skin_19", "com.chillyroom.dungeonshooter.mage_skin_19", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/mage_19_ui.png", "5.3.0-2023七月版本") },
            { "paladin_skin_19", new SkuData("paladin_skin_19", "圣骑士皮肤 - 盾牌座", "Skin - Paladin - Scutum", "聖騎士皮膚 - 盾牌座", "Skin - Kỵ Sĩ Thánh - Chòm Thuẫn Bài", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "167", "168", "20167", "", "231928", "25336", "ios.com.chillyroom.dungeonshooter.paladin_skin_19", "com.chillyroom.dungeonshooter.paladin_skin_19", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/paladin_19_ui.png", "5.3.0-2023七月版本") },
            { "assassin_skin_19", new SkuData("assassin_skin_19", "刺客皮肤 - 天蝎座", "Skin - Assassin - Scorpius", "刺客皮膚 - 天蠍座", "Skin - Thích Khách - Chòm Bò Cạp", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "168", "169", "20168", "", "231929", "25337", "ios.com.chillyroom.dungeonshooter.assassin_skin_19", "com.chillyroom.dungeonshooter.assassin_skin_19", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/assassin_19_ui.png", "5.3.0-2023七月版本") },
            { "elf_skin_15", new SkuData("elf_skin_15", "精灵皮肤 - 射手座", "Skin - Elf - Sagittarius", "精靈皮膚 - 射手座", "Skin - Tinh Linh - Chòm Nhân Mã", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "169", "170", "20169", "", "231930", "25338", "ios.com.chillyroom.dungeonshooter.elf_skin_15", "com.chillyroom.dungeonshooter.elf_skin_15", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/elves_15_ui.png", "5.3.0-2023七月版本") },
            { "rogue_skin_19", new SkuData("rogue_skin_19", "游侠皮肤 - 双子座", "Skin - Rogue - Gemini", "遊俠皮膚 - 雙子座", "Skin - Hiệp Sĩ - Chòm Song Tử", 1, 1, "20", "$3.99/¥22", "₽299/¥22", "₫99000/$3.99", "170", "171", "20170", "", "231931", "25339", "ios.com.chillyroom.dungeonshooter.rogue_skin_19", "com.chillyroom.dungeonshooter.rogue_skin_19", "4637121953801084017/4636472375067537543", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ranger_19_ui.png", "5.3.0-2023七月版本") },
            { "pastor_skin_17", new SkuData("pastor_skin_17", "牧师皮肤 - 处女座", "Skin - Priest - Virgo", "牧師皮膚 - 處女座", "Skin - Mục Sư - Chòm Xử Nữ", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "171", "172", "20171", "", "231932", "35442", "ios.com.chillyroom.dungeonshooter.pastor_skin_17", "com.chillyroom.dungeonshooter.pastor_skin_17", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/priest_17_ui.png", "5.3.5-2023八月版本") },
            { "warlock_skin_6", new SkuData("warlock_skin_6", "恶魔术士皮肤 - 小熊座与大熊座", "Skin - Demonmancer - Ursa Minor & Major", "惡魔術士皮膚 - 小熊座與大熊座", "Skin - Thuật sĩ ác ma - Chòm Tiểu Hùng và Chòm Đại Hùng", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "172", "173", "20172", "", "231933", "35443", "ios.com.chillyroom.dungeonshooter.warlock_skin_6", "com.chillyroom.dungeonshooter.warlock_skin_6", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/warlock_6_ui.png", "5.3.5-2023八月版本") },
            { "new_pkg_23", new SkuData("new_pkg_23", "皮套王子礼包", "Costume Prince Pack", "皮套王子禮包", "Quà Trang Phục Hoàng Tử", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "173", "174", "20173", "", "231934", "39717", "ios.com.chillyroom.dungeonshooter.new_pkg_23", "com.chillyroom.dungeonshooter.new_pkg_23", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010023.png", "5.4.0-2023九月版本") },
            { "new_pkg_24", new SkuData("new_pkg_24", "矿工+陷阱大师礼包", "Miner+Trap Master Pack", "礦工+陷阱大師禮包", "Quà Thợ Mỏ+Bậc Thầy Cạm Bẫy", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "174", "175", "20174", "", "231935", "40060", "ios.com.chillyroom.dungeonshooter.new_pkg_24", "com.chillyroom.dungeonshooter.new_pkg_24", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010024.png", "5.4.0-2023九月版本") },
            { "costumeprince_skin_1", new SkuData("costumeprince_skin_1", "皮套王子皮肤 - 欧博士的火箭（低配版）", "Skin - Costume Prince - Dr. O's Rocket (Knock-off)", "皮套王子皮膚 - 歐博士的火箭（低配版）", "Skin - Trang Phục Hoàng Tử - Hỏa Tiễn của Ouboshi...", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "175", "176", "20175", "", "231936", "39719", "ios.com.chillyroom.dungeonshooter.costumeprince_skin_1", "com.chillyroom.dungeonshooter.costumeprince_skin_1", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/costumePrince_1_ui.png", "5.4.0-2023九月版本") },
            { "necromancer_skin_12", new SkuData("necromancer_skin_12", "死灵法师皮肤 - 仙后座", "Skin - Necromancer - Cassiopeia", "死靈法師皮膚 - 仙后座", "Skin - Pháp Sư Tử Linh - Chòm Tiên Hậu", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "176", "177", "20176", "", "231937", "39720", "ios.com.chillyroom.dungeonshooter.necromancer_skin_12", "com.chillyroom.dungeonshooter.necromancer_skin_12", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/necromancer_12_ui.png", "5.4.0-2023九月版本") },
            { "vampire_skin_16", new SkuData("vampire_skin_16", "吸血鬼皮肤 - 司马懿·仲达", "Skin - Vampire - Sima Yi - Zhongda", "吸血鬼皮膚 - 司馬懿·仲達", "Skin - Ma Ca Rồng - Tư Mã Ý, tự Trọng Đạt", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "177", "178", "20177", "", "231938", "39721", "ios.com.chillyroom.dungeonshooter.vampire_skin_16", "com.chillyroom.dungeonshooter.vampire_skin_16", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/vampire_16_ui.png", "5.4.0-2023九月版本") },
            { "druid_skin_17", new SkuData("druid_skin_17", "德鲁伊皮肤 - 祝融姊姊", "Skin - Druid - Sister Zhurong", "德魯伊皮膚 - 祝融姊姊", "Skin - Druid - Chúc Dung Tỷ Tỷ", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "178", "179", "20178", "", "231939", "39722", "ios.com.chillyroom.dungeonshooter.druid_skin_17", "com.chillyroom.dungeonshooter.druid_skin_17", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/druid_17_ui.png", "5.4.0-2023九月版本") },
            { "hall_skin_3", new SkuData("hall_skin_3", "大厅装饰 - 观星领域-议事厅", "Lobby Decor - Stargaze Realm – Grand Hall", "大廳裝飾 - 觀星領域-議事廳", "Trang Trí Sảnh - Lĩnh Vực Ngắm Sao-Sảnh Nghị Sự", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "179", "180", "20179", "", "231940", "39723", "ios.com.chillyroom.dungeonshooter.hall_skin_3", "com.chillyroom.dungeonshooter.hall_skin_3", "4635639827510787213/4634914334553984538", "Other", "All", "Assets/RGTexture/ui/hero_room_skin/hall/hall_skin_icon_3.png", "5.4.0-2023九月版本") },
            { "skill_trapmaster_2", new SkuData("skill_trapmaster_2", "陷阱大师三技能 - 帽子戏法", "Skill - Trap Master - Hat Trick", "陷阱大師三技能 - 帽子戲法", "Kỹ năng - Bậc Thầy Cạm Bẫy - Cách chơi Nón", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "180", "181", "20180", "", "231941", "39724", "ios.com.chillyroom.dungeonshooter.skill_trapmaster_2", "com.chillyroom.dungeonshooter.skill_trapmaster_2", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/tarpMaster_skillIcon_3.png#tarpMaster_skillIcon_3_0", "5.4.0-2023九月版本") },
            { "new_pkg_25", new SkuData("new_pkg_25", "璀璨文明皮肤礼包", "Brilliant Civilizations Skin Pack", "", "Quà Skin Nền Văn Minh Rực Rỡ", 1, 1, "40", "$7.99/¥48", "₽649/¥48", "₫199000/$7.99", "181", "", "20181", "", "", "", "ios.com.chillyroom.dungeonshooter.new_pkg_25", "com.chillyroom.dungeonshooter.new_pkg_25", "4638592971698023487/4637530393979686162", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010025.png", "5.4.5-2023九月修复版本") },
            { "new_pkg_26", new SkuData("new_pkg_26", "博士礼包", "Physicist Pack", "博士禮包", "Quà Nhà Vật Lý", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "182", "182", "20182", "", "231942", "46748", "ios.com.chillyroom.dungeonshooter.new_pkg_26", "com.chillyroom.dungeonshooter.new_pkg_26", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010026.png", "5.5.0-2023十一月版本") },
            { "new_pkg_27", new SkuData("new_pkg_27", "群星的召唤皮肤礼包2", "Call of Stars Skin Pack 2", "群星的召喚皮膚禮包2", "Quà Skin Lời Gọi Của Sao 2", 1, 1, "45", "$8.99/¥54", "₽749/¥54", "₫223000/$8.99", "183", "183", "20183", "", "231943", "46749", "ios.com.chillyroom.dungeonshooter.new_pkg_27", "com.chillyroom.dungeonshooter.new_pkg_27", "4634958946912594240/4634716269219238891", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010027.png", "5.5.0-2023十一月版本") },
            { "viking_skin_20", new SkuData("viking_skin_20", "狂战士皮肤 - 狮子座", "Skin - Berserker - Leo", "狂戰士皮膚 - 獅子座", "Skin - Berserker - Chòm Sư Tử", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "184", "184", "20184", "", "231944", "46750", "ios.com.chillyroom.dungeonshooter.viking_skin_20", "com.chillyroom.dungeonshooter.viking_skin_20", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/viking_20_ui.png", "5.5.0-2023十一月版本") },
            { "doctor", new SkuData("doctor", "角色 - 博士", "Character - Physicist", "角色 - 博士", "Nhân vật - Nhà Vật Lý", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "185", "185", "20185", "", "231945", "46751", "ios.com.chillyroom.dungeonshooter.doctor", "com.chillyroom.dungeonshooter.doctor", "*******************/*******************", "Hero", "All", "Assets/Skin/Character/Doctor/Skin_0/Doctor_0.png", "5.5.0-2023十一月版本") },
            { "doctor_skin_1", new SkuData("doctor_skin_1", "博士皮肤 - 高级指挥官", "Skin - Physicist - Senior Commander", "博士皮膚 - 高級指揮官", "Skin - Nhà Vật Lý - Chỉ Huy Cấp Cao", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "186", "186", "20186", "", "231946", "46752", "ios.com.chillyroom.dungeonshooter.doctor_skin_1", "com.chillyroom.dungeonshooter.doctor_skin_1", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/Skin/Character/Doctor/Skin_1/Doctor_1.png", "5.5.0-2023十一月版本") },
            { "skill_warlock_2", new SkuData("skill_warlock_2", "恶魔术士三技能 - 阿蒙的庇护", "Skill - Demonmancer - Amon's Protection", "惡魔術士三技能 - 阿蒙的庇護", "Kỹ năng - Thuật sĩ ác ma - Sự Bảo Vệ Của Amon", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "187", "187", "20187", "", "231947", "46753", "ios.com.chillyroom.dungeonshooter.skill_warlock_2", "com.chillyroom.dungeonshooter.skill_warlock_2", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/warlock_skill_2_icon.png", "5.5.0-2023十一月版本") },
            { "season_coin_68", new SkuData("season_coin_68", "8000赛季币", "Season Coin*8000", "8000賽季幣", "Xu Mùa Giải*8000", 8000, 0, "68", "$10.99/¥68", "₽949/¥68", "₫273000/$10.99", "188", "188", "20188", "", "231948", "50981", "ios.com.chillyroom.dungeonshooter.season_coin_68", "com.chillyroom.dungeonshooter.season_coin_68", "4637094491513796933/4636770401783805406", "SeasonCoin", "All", "Assets/RGTexture/ui/common/ui_season_coin.png#ui_season_coin_2", "6.0.0-2024春节版本") },
            { "lancer", new SkuData("lancer", "角色 - 枪客", "Character - Lancer", "角色 - 槍客", "Nhân vật - Thương Khách", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "189", "189", "20189", "", "231949", "50982", "ios.com.chillyroom.dungeonshooter.lancer", "com.chillyroom.dungeonshooter.lancer", "*******************/*******************", "Hero", "All", "Assets/RGTexture/ui/skin/lancer_0_ui.png", "6.0.0-2024春节版本") },
            { "assassin_skin_20", new SkuData("assassin_skin_20", "刺客皮肤 - 吕布 字奉先", "Skin - Assassin - Lu Bu - Fengxian", "刺客皮膚 - 呂布 字奉先", "Skin - Thích Khách - Lữ Bố, tự Phụng Tiên", 1, 1, "20", "$3.99/¥22", "₽299/¥22", "₫99000/$3.99", "190", "190", "20190", "", "231950", "50983", "ios.com.chillyroom.dungeonshooter.assassin_skin_20", "com.chillyroom.dungeonshooter.assassin_skin_20", "4637121953801084017/4636472375067537543", "HeroSkin", "All", "Assets/RGTexture/ui/skin/assassin_20_ui.png", "6.0.0-2024春节版本") },
            { "necromancer_skin_14", new SkuData("necromancer_skin_14", "死灵法师皮肤 - 貂蝉", "Skin - Necromancer - Diao Chan", "死靈法師皮膚 - 貂蟬", "Skin - Pháp Sư Tử Linh - Điêu Thuyền", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "191", "191", "20191", "", "231951", "50984", "ios.com.chillyroom.dungeonshooter.necromancer_skin_14", "com.chillyroom.dungeonshooter.necromancer_skin_14", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/necromancer_14_ui.png", "6.0.0-2024春节版本") },
            { "lancer_skin_1", new SkuData("lancer_skin_1", "枪客皮肤 - 赵云 字子龙", "Skin - Lancer - Zhao Yun - Zilong", "槍客皮膚 - 趙雲 字子龍", "Skin - Thương Khách - Triệu Vân, tự Tử Long", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "192", "192", "20192", "", "231952", "50985", "ios.com.chillyroom.dungeonshooter.lancer_skin_1", "com.chillyroom.dungeonshooter.lancer_skin_1", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/lancer_1_ui.png", "6.0.0-2024春节版本") },
            { "swordmaster_skin_1", new SkuData("swordmaster_skin_1", "剑宗皮肤 - 孙尚香", "Skin - Sword Master - Sun Shangxiang", "劍宗皮膚 - 孫尚香", "Skin - Kiếm Tông - Tôn Thượng Hương", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "193", "193", "20193", "", "231953", "50986", "ios.com.chillyroom.dungeonshooter.swordmaster_skin_1", "com.chillyroom.dungeonshooter.swordmaster_skin_1", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/swordMaster_1_ui.png", "6.0.0-2024春节版本") },
            { "miner_skin_6", new SkuData("miner_skin_6", "矿工皮肤 - 大乔", "Skin - Miner - Da Qiao", "礦工皮膚 - 大喬", "Skin - Thợ Mỏ - Đại Kiều", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "194", "194", "20194", "", "231954", "50987", "ios.com.chillyroom.dungeonshooter.miner_skin_6", "com.chillyroom.dungeonshooter.miner_skin_6", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/miner_6_ui.png", "6.0.0-2024春节版本") },
            { "engineer_skin_19", new SkuData("engineer_skin_19", "工程师皮肤 - 华佗 字元化", "Skin - Engineer - Hua Tuo - Yuanhua", "工程師皮膚 - 華佗 字元化", "Skin - Kỹ Sư - Hoa Đà, tự Nguyên Hóa", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "195", "195", "20195", "", "231955", "50988", "ios.com.chillyroom.dungeonshooter.engineer_skin_19", "com.chillyroom.dungeonshooter.engineer_skin_19", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/engineer_19_ui.png", "6.0.0-2024春节版本") },
            { "wizard_skin_20", new SkuData("wizard_skin_20", "法师皮肤 -  元气奇事—宝箱法师", "Skin - Witch - Soul Knight Strange Tales: Chest Witch", "法師皮膚 - 元氣奇事—寶箱法師", "Skin-Pháp Sư-Những Chuyện Kỳ Lạ của Soul Knight-Pháp...", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "196", "196", "20196", "", "231956", "50989", "ios.com.chillyroom.dungeonshooter.mage_skin_20", "com.chillyroom.dungeonshooter.mage_skin_20", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/mage_20_ui.png", "6.0.0-2024春节版本") },
            { "hall_skin_4", new SkuData("hall_skin_4", "大厅装饰 - 连环战船（壹）", "Lobby Decor - Chained Warships (I)", "大廳裝飾 - 連環戰船（壹）", "Trang Trí Sảnh - Tàu Chiến Liên Hoàn (1)", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "197", "197", "20197", "", "231957", "50990", "ios.com.chillyroom.dungeonshooter.hall_skin_4", "com.chillyroom.dungeonshooter.hall_skin_4", "4635639827510787213/4634914334553984538", "Other", "All", "Assets/RGTexture/ui/hero_room_skin/hall/hall_skin_icon_4.png", "6.0.0-2024春节版本") },
            { "new_pkg_28", new SkuData("new_pkg_28", "枪客礼包", "Lancer Pack", "槍客禮包", "Quà Thương Khách", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "198", "198", "20198", "", "231958", "50991", "ios.com.chillyroom.dungeonshooter.new_pkg_28", "com.chillyroom.dungeonshooter.new_pkg_28", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010028.png", "6.0.0-2024春节版本") },
            { "new_pkg_29", new SkuData("new_pkg_29", "群雄逐鹿皮肤礼包", "War of Warlords Skin Pack", "群雄逐鹿皮膚禮包", "Quà Skin Quần Hùng Trục Lộc", 1, 1, "45", "$8.99/¥54", "₽749/¥54", "₫223000/$8.99", "199", "199", "20199", "", "231959", "50992", "ios.com.chillyroom.dungeonshooter.new_pkg_29", "com.chillyroom.dungeonshooter.new_pkg_29", "4634958946912594240/4634716269219238891", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010029.png", "6.0.0-2024春节版本") },
            { "knight_skin_22", new SkuData("knight_skin_22", "骑士皮肤 - 元气奇事—亚伦", "Skin - Knight - Soul Knight Strange Tales: Aaron", "騎士皮膚 - 元氣奇事—亞倫", "Skin - Kỵ Sĩ - Những Chuyện Kỳ Lạ của Soul Knight-Aaron", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "200", "200", "20200", "", "231960", "56451", "ios.com.chillyroom.dungeonshooter.knight_skin_22", "com.chillyroom.dungeonshooter.knight_skin_22", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/knight_22_ui.png", "6.0.5-2024春节联动版本") },
            { "new_pkg_31", new SkuData("new_pkg_31", "新角色合集礼包", "New Characters Pack", "新角色合集禮包", "Quà BST nhân vật mới", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "201", "201", "20201", "", "231961", "56452", "ios.com.chillyroom.dungeonshooter.new_pkg_31", "com.chillyroom.dungeonshooter.new_pkg_31", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010031.png", "6.0.5-2024春节联动版本") },
            { "arcaneknight_skin_1", new SkuData("arcaneknight_skin_1", "咒法骑士皮肤 - 周瑜", "Skin - Arcane Knight - Zhou Yu - Gongjin", "咒法騎士皮膚 - 周瑜", "Skin - Kỵ Sĩ Bùa Chú - Chu Du tự Công Cẩn", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "202", "202", "20202", "", "231962", "57480", "ios.com.chillyroom.dungeonshooter.arcaneknight_skin_1", "com.chillyroom.dungeonshooter.arcaneknight_skin_1", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/arcaneKnight_1_ui.png", "6.1.0-2024春季版本") },
            { "warliege_skin_4", new SkuData("warliege_skin_4", "领主皮肤 - 影月", "Skin - Warliege - Shadowmoon", "領主皮膚 - 影月", "Skin - Lãnh chúa - Ảnh Nguyệt", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "203", "203", "20203", "", "231963", "57481", "ios.com.chillyroom.dungeonshooter.warliege_skin_4", "com.chillyroom.dungeonshooter.warliege_skin_4", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/warliege_4_ui.png", "6.1.0-2024春季版本") },
            { "new_pkg_34", new SkuData("new_pkg_34", "咒法骑士礼包", "Arcane Knight Pack", "咒法騎士禮包", "Quà Kỵ Sĩ Bùa Chú", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "204", "204", "20204", "", "231964", "57482", "ios.com.chillyroom.dungeonshooter.new_pkg_34", "com.chillyroom.dungeonshooter.new_pkg_34", "4635639827510787213/4634914334553984538", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010034.png", "6.1.0-2024春季版本") },
            { "new_pkg_32", new SkuData("new_pkg_32", "《元气奇事》动画皮肤礼包1", "Soul Knight Strange Tales Skin Pack 1", "《元氣奇事》動畫皮膚禮包1", "Quà Skin Soul Knight Strange Tales 1", 1, 1, "35", "$6.99/¥42", "₽549/¥42", "₫174000/$6.99", "205", "205", "20205", "", "231965", "57483", "ios.com.chillyroom.dungeonshooter.new_pkg_32", "com.chillyroom.dungeonshooter.new_pkg_32", "4636817703706715376/4635839191945952686", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010032.png", "6.1.0-2024春季版本") },
            { "new_pkg_33", new SkuData("new_pkg_33", "《元气奇事》动画皮肤礼包2", "Soul Knight Strange Tales Skin Pack 2", "《元氣奇事》動畫皮膚禮包2", "Quà Skin Soul Knight Strange Tales 2", 1, 1, "25", "$4.99/¥30", "₽399/¥30", "₫124000/$4.99", "206", "206", "20206", "", "231966", "57484", "ios.com.chillyroom.dungeonshooter.new_pkg_33", "com.chillyroom.dungeonshooter.new_pkg_33", "4637558555263124425/4637165942710777751", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010033.png", "6.1.0-2024春季版本") },
            { "doctor_skin_4", new SkuData("doctor_skin_4", "博士皮肤 - 罗刹姬", "Skin - Physicist - Rakshasi", "博士皮膚 - 羅剎姬", "Skin - Nhà Vật Lý - Luocha Ji", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "207", "207", "20207", "", "231967", "58621", "ios.com.chillyroom.dungeonshooter.doctor_skin_4", "com.chillyroom.dungeonshooter.doctor_skin_4", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/doctor_4_ui.png", "6.2.0-2024四月版本") },
            { "skill_costumeprince_1", new SkuData("skill_costumeprince_1", "皮套王子二技能 - 进击！水晶王蟹！", "Skill - Costume Prince - Forward! Crystal Crab King!", "皮套王子二技能 - 進擊！ 水晶王蟹！", "Kỹ năng-Trang Phục Hoàng Tử-Tiến công! Cua Vua Pha Lê!", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "208", "208", "20208", "", "231968", "58622", "ios.com.chillyroom.dungeonshooter.skill_costumeprince_1", "com.chillyroom.dungeonshooter.skill_costumeprince_1", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/costumePrince_skillIcon_2.png", "6.2.0-2024四月版本") },
            { "costumeprince_skin_6", new SkuData("costumeprince_skin_6", "皮套王子皮肤 - 大家饿少女", "Skin - Costume Prince - We Happy Lady", "皮套王子皮膚 - 大家餓少女", "Skin - Trang Phục Hoàng Tử - Thiếu Nữ Đói", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "209", "209", "20209", "", "231969", "58623", "ios.com.chillyroom.dungeonshooter.costumeprince_skin_6", "com.chillyroom.dungeonshooter.costumeprince_skin_6", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/costumePrince_6_ui.png", "6.2.0-2024四月版本") },
            { "rogue_skin_22", new SkuData("rogue_skin_22", "游侠皮肤 - 刘备 字玄德", "Skin - Rogue - Liu Bei - Xuande", "遊俠皮膚 - 劉備 字玄德", "Skin - Hiệp Sĩ - Lưu Bị, tự Huyền Đức", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "210", "210", "20210", "", "231970", "58624", "ios.com.chillyroom.dungeonshooter.rogue_skin_22", "com.chillyroom.dungeonshooter.rogue_skin_22", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ranger_22_ui.png", "6.2.0-2024四月版本") },
            { "new_pkg_36", new SkuData("new_pkg_36", "群雄逐鹿皮肤礼包2", "War of Warlords Skin Pack 2", "群雄逐鹿皮膚禮包2", "Quà Skin Quần Hùng Trục Lộc 2", 1, 1, "45", "$8.99/¥54", "₽749/¥54", "₫223000/$8.99", "211", "211", "20211", "", "231971", "58625", "ios.com.chillyroom.dungeonshooter.new_pkg_36", "com.chillyroom.dungeonshooter.new_pkg_36", "4634958946912594240/4634716269219238891", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010036.png", "6.2.0-2024四月版本") },
            { "new_pkg_37", new SkuData("new_pkg_37", "博士技能礼包1", "Physicist Skill Pack 1", "博士技能禮包1", "Quà Kỹ Năng Nhà Vật Lý 1", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "212", "212", "20212", "", "231972", "58626", "ios.com.chillyroom.dungeonshooter.new_pkg_37", "com.chillyroom.dungeonshooter.new_pkg_37", "4635639827510787213/4634914334553984538", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010037.png", "6.2.0-2024四月版本") },
            { "new_pkg_38", new SkuData("new_pkg_38", "博士技能礼包2", "Physicist Skill Pack 2", "博士技能禮包2", "Quà Kỹ Năng Nhà Vật Lý 2", 1, 1, "25", "$4.99/¥30", "₽399/¥30", "₫124000/$4.99", "213", "213", "20213", "", "231973", "58627", "ios.com.chillyroom.dungeonshooter.new_pkg_38", "com.chillyroom.dungeonshooter.new_pkg_38", "4637558555263124425/4637165942710777751", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010038.png", "6.2.0-2024四月版本") },
            { "new_pkg_39", new SkuData("new_pkg_39", "皮套王子技能礼包1", "Costume Prince Skill Pack 1", "皮套王子技能禮包1", "Quà Kỹ Năng Trang Phục Hoàng Tử 1", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "214", "214", "20214", "", "231974", "58628", "ios.com.chillyroom.dungeonshooter.new_pkg_39", "com.chillyroom.dungeonshooter.new_pkg_39", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010039.png", "6.2.0-2024四月版本") },
            { "new_pkg_40", new SkuData("new_pkg_40", "皮套王子技能礼包2", "Costume Prince Skill Pack 2", "皮套王子技能禮包2", "Quà Kỹ Năng Trang Phục Hoàng Tử 2", 1, 1, "25", "$4.99/¥30", "₽399/¥30", "₫124000/$4.99", "215", "215", "20215", "", "231975", "58629", "ios.com.chillyroom.dungeonshooter.new_pkg_40", "com.chillyroom.dungeonshooter.new_pkg_40", "4637558555263124425/4637165942710777751", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010040.png", "6.2.0-2024四月版本") },
            { "skill_swordmaster_1", new SkuData("skill_swordmaster_1", "剑宗二技能 - 流云飞剑", "Skill - Sword Master - Wisps of Clouds", "劍宗二技能 - 流雲飛劍", "Kỹ năng - Kiếm Tông - Kiếm Lưu Vân", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "216", "216", "20216", "", "231976", "63444", "ios.com.chillyroom.dungeonshooter.skill_swordmaster_1", "com.chillyroom.dungeonshooter.skill_swordmaster_1", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/sword_master_skillIcon_2_0.png", "6.3.0-2024六月版本") },
            { "trapmaster_skin_6", new SkuData("trapmaster_skin_6", "陷阱大师皮肤 - 星算子", "Skin - Trap Master - Astral Oracle", "陷阱大師皮膚 - 星算子", "Skin - Bậc Thầy Cạm Bẫy - Bói Sao", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "217", "217", "20217", "", "231977", "63445", "ios.com.chillyroom.dungeonshooter.trapmaster_skin_6", "com.chillyroom.dungeonshooter.trapmaster_skin_6", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/trapMaster_6_ui.png", "6.3.0-2024六月版本") },
            { "new_pkg_43", new SkuData("new_pkg_43", "陷阱大师礼包1", "Trap Master Pack 1", "陷阱大師禮包1", "Quà Bậc Thầy Cạm Bẫy 1", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "218", "218", "20218", "", "231978", "63446", "ios.com.chillyroom.dungeonshooter.new_pkg_43", "com.chillyroom.dungeonshooter.new_pkg_43", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010043.png", "6.3.0-2024六月版本") },
            { "new_pkg_44", new SkuData("new_pkg_44", "陷阱大师礼包2", "Trap Master Pack 2", "陷阱大師禮包2", "Quà Bậc Thầy Cạm Bẫy 2", 1, 1, "30", "$5.99/¥36", "₽449/¥36", "₫149000/$5.99", "219", "219", "20219", "", "231979", "63447", "ios.com.chillyroom.dungeonshooter.new_pkg_44", "com.chillyroom.dungeonshooter.new_pkg_44", "4637366503109523235/4635933743767826148", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010044.png", "6.3.0-2024六月版本") },
            { "new_pkg_45", new SkuData("new_pkg_45", "童话皮肤礼包1", "Fairy Tales Skin Pack 1", "童話皮膚禮包1", "Quà Skin Cổ Tích 1", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "220", "220", "20220", "", "231980", "63448", "ios.com.chillyroom.dungeonshooter.new_pkg_45", "com.chillyroom.dungeonshooter.new_pkg_45", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010045.png", "6.3.0-2024六月版本") },
            { "astrologist", new SkuData("astrologist", "角色 - 占星师", "Character - Astromancer", "角色 - 占星師", "Nhân vật - Chiêm Tinh Sư", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "221", "221", "20221", "", "231981", "66416", "ios.com.chillyroom.dungeonshooter.astrologist", "com.chillyroom.dungeonshooter.astrologist", "*******************/*******************", "Hero", "All", "Assets/RGTexture/ui/skin/astrologist_0_ui.png", "6.4.0-2024七月版本") },
            { "astrologist_skin_1", new SkuData("astrologist_skin_1", "占星师皮肤 - 诸葛亮", "Skin - Astromancer - Zhuge Liang - Kongming", "占星師皮膚 - 諸葛亮", "Skin - Chiêm Tinh Sư - Gia Cát Lượng tự Khổng Minh", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "222", "222", "20222", "", "231982", "66417", "ios.com.chillyroom.dungeonshooter.astrologist_skin_1", "com.chillyroom.dungeonshooter.astrologist_skin_1", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/astrologist_1_ui.png", "6.4.0-2024七月版本") },
            { "viking_skin_22", new SkuData("viking_skin_22", "狂战士皮肤 - 关羽", "Skin - Berserker - Guan Yu - Yunchang", "狂戰士皮膚 - 關羽", "Skin - Berserker - Quan Vũ tự Vân Trường", 1, 1, "20", "$3.99/¥22", "₽299/¥22", "₫99000/$3.99", "223", "223", "20223", "", "231983", "66418", "ios.com.chillyroom.dungeonshooter.viking_skin_22", "com.chillyroom.dungeonshooter.viking_skin_22", "4637121953801084017/4636472375067537543", "HeroSkin", "All", "Assets/RGTexture/ui/skin/viking_22_ui.png", "6.4.0-2024七月版本") },
            { "fighter_skin_1", new SkuData("fighter_skin_1", "武斗家皮肤 - 关银屏", "Skin - Fighter - Guan Yinping", "武鬥家皮膚 - 關銀屏", "Skin - Võ Đấu Gia - Quan Ngân Bình", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "224", "224", "20224", "", "231984", "66419", "ios.com.chillyroom.dungeonshooter.fighter_skin_1", "com.chillyroom.dungeonshooter.fighter_skin_1", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/fighter_1_ui.png", "6.4.0-2024七月版本") },
            { "pastor_skin_19", new SkuData("pastor_skin_19", "牧师皮肤 - 蔡文姬", "Skin - Priestess - Cai Wenji", "牧師皮膚 - 蔡文姬", "Skin - Mục Sư - Thái Văn Cơ", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "225", "225", "20225", "", "231985", "66420", "ios.com.chillyroom.dungeonshooter.priest_skin_19", "com.chillyroom.dungeonshooter.priest_skin_19", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/priest_19_ui.png", "6.4.0-2024七月版本") },
            { "warlock_skin_8", new SkuData("warlock_skin_8", "恶魔术士皮肤 - 小乔", "Skin - Demonmancer - Xiao Qiao", "惡魔術士皮膚 - 小喬", "Skin - Thuật sĩ ác ma - Tiểu Kiều", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "226", "226", "20226", "", "231986", "66421", "ios.com.chillyroom.dungeonshooter.warlock_skin_8", "com.chillyroom.dungeonshooter.warlock_skin_8", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/warlock_8_ui.png", "6.4.0-2024七月版本") },
            { "miner_skin_8", new SkuData("miner_skin_8", "矿工皮肤 - 蜜糖", "Skin - Miner - Honeybee", "礦工皮膚 - 蜜糖", "Skin - Thợ Mỏ - Mật", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "227", "227", "20227", "", "231987", "66422", "ios.com.chillyroom.dungeonshooter.miner_skin_8", "com.chillyroom.dungeonshooter.miner_skin_8", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/miner_8_ui.png", "6.4.0-2024七月版本") },
            { "wizard_skin_23", new SkuData("wizard_skin_23", "法师皮肤 - 卞玲珑", "Skin - Witch - Bian Linglong", "法師皮膚 - 卞玲瓏", "Skin - Pháp Sư - Biện Linh Lung", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "228", "228", "20228", "", "231988", "66423", "ios.com.chillyroom.dungeonshooter.wizard_skin_23", "com.chillyroom.dungeonshooter.wizard_skin_23", "*******************/*******************", "HeroSkin", "All", "Assets/RGTexture/ui/skin/mage_23_ui.png", "6.4.0-2024七月版本") },
            { "fish_chips_45", new SkuData("fish_chips_45", "小鱼干63", "Fish Chips*63", "小魚乾63", "Cá Khô*63", 63, 0, "45", "$8.99/¥54", "₽749/¥54", "₫223000/$8.99", "229", "229", "20229", "", "231989", "66424", "ios.com.chillyroom.dungeonshooter.fish_chips_45", "com.chillyroom.dungeonshooter.fish_chips_45", "4634958946912594240/4634716269219238891", "FishChip", "All", "Assets/RGTexture/ui/common/ui.png#ui_336", "6.4.0-2024七月版本") },
            { "new_pkg_50", new SkuData("new_pkg_50", "占星师礼包", "Astromancer Pack", "占星師禮包", "Quà Chiêm Tinh Sư", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "230", "230", "20230", "", "231990", "66425", "ios.com.chillyroom.dungeonshooter.new_pkg_50", "com.chillyroom.dungeonshooter.new_pkg_50", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010050.png", "6.4.0-2024七月版本") },
            { "new_pkg_51", new SkuData("new_pkg_51", "群雄逐鹿皮肤礼包3", "War of Warlords Skin Pack 3", "群雄逐鹿皮膚禮包3", "Quà Skin Quần Hùng Trục Lộc 3", 1, 1, "60", "$11.99/¥70", "₽1049/¥70", "₫298000/$11.99", "231", "231", "20231", "", "231991", "66426", "ios.com.chillyroom.dungeonshooter.new_pkg_51", "com.chillyroom.dungeonshooter.new_pkg_51", "4637487303161483554/4635690707082638268", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010051.png", "6.4.0-2024七月版本") },
            { "new_pkg_53", new SkuData("new_pkg_53", "机械狂潮皮肤礼包2", "Robotic Frenzy Skin Pack 2", "機械狂潮皮膚禮包2", "Quà Skin Làn sóng robot 2", 1, 1, "55", "$10.99/¥65", "₽949/¥65", "₫273000/$10.99", "232", "232", "20232", "", "231992", "66427", "ios.com.chillyroom.dungeonshooter.new_pkg_53", "com.chillyroom.dungeonshooter.new_pkg_53", "4637094491513796933/4636770401783805406", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010053.png", "6.4.0-2024七月版本") },
            { "new_pkg_54", new SkuData("new_pkg_54", "矿工礼包1", "Miner Pack 1", "礦工禮包1", "Quà Thợ Mỏ 1", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "233", "233", "20233", "", "231993", "66428", "ios.com.chillyroom.dungeonshooter.new_pkg_54", "com.chillyroom.dungeonshooter.new_pkg_54", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010054.png", "6.4.0-2024七月版本") },
            { "new_pkg_55", new SkuData("new_pkg_55", "矿工礼包2", "Miner Pack 2", "礦工禮包2", "Quà Thợ Mỏ 2", 1, 1, "35", "$6.99/¥42", "₽549/¥42", "₫174000/$6.99", "234", "234", "20234", "", "231994", "66429", "ios.com.chillyroom.dungeonshooter.new_pkg_55", "com.chillyroom.dungeonshooter.new_pkg_55", "4636817703706715376/4635839191945952686", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010055.png", "6.4.0-2024七月版本") },
            { "new_pkg_56", new SkuData("new_pkg_56", "机械狂潮皮肤礼包3", "Robotic Frenzy Skin Pack 3", "機械狂潮皮膚禮包3", "Quà Skin Làn sóng robot 3", 1, 1, "40", "$7.99/¥48", "₽649/¥48", "₫199000/$7.99", "235", "235", "20235", "", "231995", "66430", "ios.com.chillyroom.dungeonshooter.new_pkg_56", "com.chillyroom.dungeonshooter.new_pkg_56", "4638592971698023487/4637530393979686162", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010056.png", "6.4.0-2024七月版本") },
            { "new_pkg_58", new SkuData("new_pkg_58", "夏日宝石礼包1", "Summer Gem Pack 1", "夏日寶石禮包1", "Quà Đá Ngày Hè 1", 1, 0, "5", "$0.99/¥6", "₽79/¥6", "₫25000/$0.99", "236", "236", "20236", "", "231996", "66431", "ios.com.chillyroom.dungeonshooter.new_pkg_58", "com.chillyroom.dungeonshooter.new_pkg_58", "*******************/*******************", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010058.png", "6.4.0-2024七月版本") },
            { "new_pkg_59", new SkuData("new_pkg_59", "夏日宝石礼包2", "Summer Gem Pack 2", "夏日寶石禮包2", "Quà Đá Ngày Hè 2", 1, 0, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "237", "237", "20237", "", "231997", "66432", "ios.com.chillyroom.dungeonshooter.new_pkg_59", "com.chillyroom.dungeonshooter.new_pkg_59", "4635639827510787213/4634914334553984538", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010059.png", "6.4.0-2024七月版本") },
            { "new_pkg_60", new SkuData("new_pkg_60", "夏日宝石礼包3", "Summer Gem Pack 3", "夏日寶石禮包3", "Quà Đá Ngày Hè 3", 1, 0, "30", "$5.99/¥36", "₽449/¥36", "₫149000/$5.99", "238", "238", "20238", "", "231998", "66433", "ios.com.chillyroom.dungeonshooter.new_pkg_60", "com.chillyroom.dungeonshooter.new_pkg_60", "4637366503109523235/4635933743767826148", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010060.png", "6.4.0-2024七月版本") },
            { "new_pkg_61", new SkuData("new_pkg_61", "夏日小鱼干礼包1", "Summer Fish Chip Pack 1", "夏日小魚乾禮包1", "Quà Cá Khô Ngày Hè 1", 1, 0, "5", "$0.99/¥6", "₽79/¥6", "₫25000/$0.99", "239", "239", "20239", "", "231999", "66434", "ios.com.chillyroom.dungeonshooter.new_pkg_61", "com.chillyroom.dungeonshooter.new_pkg_61", "*******************/*******************", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010061.png", "6.4.0-2024七月版本") },
            { "new_pkg_62", new SkuData("new_pkg_62", "夏日小鱼干礼包2", "Summer Fish Chip Pack 2", "夏日小魚乾禮包2", "Quà Cá Khô Ngày Hè 2", 1, 0, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "240", "240", "20240", "", "232000", "66435", "ios.com.chillyroom.dungeonshooter.new_pkg_62", "com.chillyroom.dungeonshooter.new_pkg_62", "4635639827510787213/4634914334553984538", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010062.png", "6.4.0-2024七月版本") },
            { "new_pkg_63", new SkuData("new_pkg_63", "夏日小鱼干礼包3", "Summer Fish Chip Pack 3", "夏日小魚乾禮包3", "Quà Cá Khô Ngày Hè 3", 1, 0, "30", "$5.99/¥36", "₽449/¥36", "₫149000/$5.99", "241", "241", "20241", "", "232001", "66436", "ios.com.chillyroom.dungeonshooter.new_pkg_63", "com.chillyroom.dungeonshooter.new_pkg_63", "4637366503109523235/4635933743767826148", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010063.png", "6.4.0-2024七月版本") },
            { "taoist_skin_16", new SkuData("taoist_skin_16", "道士皮肤 - 陆逊 字伯言", "Skin - Taoist - Lu Xun - Boyan", "道士皮膚 - 陸遜 字伯言", "Skin - Đạo Sĩ - Lục Tốn tự Bá Ngôn", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "242", "242", "20242", "", "232002", "75745", "ios.com.chillyroom.dungeonshooter.taoist_skin_16", "com.chillyroom.dungeonshooter.taoist_skin_16", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/taoist_16_ui.png", "6.5.0-2024八月版本") },
            { "lancer_skin_5", new SkuData("lancer_skin_5", "枪客皮肤 - 骸星", "Skin - Lancer - Fallen Star", "槍客皮膚 - 骸星", "Skin - Lancer - Hài Tinh", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "243", "243", "20243", "", "232003", "75746", "ios.com.chillyroom.dungeonshooter.lancer_skin_5", "com.chillyroom.dungeonshooter.lancer_skin_5", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/lancer_5_ui.png", "6.5.0-2024八月版本") },
            { "rogue_skin_23", new SkuData("rogue_skin_23", "游侠皮肤 - 夜游神 皮肤", "Skin - Rogue - Night Wanderer Skin", "遊俠皮膚 - 夜遊神 皮膚", "Skin - Hiệp Sĩ - Skin Dạ Du Thần", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "244", "244", "20244", "", "232004", "75747", "ios.com.chillyroom.dungeonshooter.rogue_skin_23", "com.chillyroom.dungeonshooter.rogue_skin_23", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ranger_23_ui.png", "6.5.0-2024八月版本") },
            { "new_pkg_71", new SkuData("new_pkg_71", "枪客技能礼包1", "Lancer Skill Pack 1", "槍客技能禮包1", "Quà Kỹ Năng Thương Khách 1", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "245", "245", "20245", "", "232005", "75748", "ios.com.chillyroom.dungeonshooter.new_pkg_71", "com.chillyroom.dungeonshooter.new_pkg_71", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010071.png", "6.5.0-2024八月版本") },
            { "new_pkg_72", new SkuData("new_pkg_72", "枪客技能礼包2", "Lancer Skill Pack 2", "槍客技能禮包2", "Quà Kỹ Năng Thương Khách 2", 1, 1, "30", "$5.99/¥36", "₽449/¥36", "₫149000/$5.99", "246", "246", "20246", "", "232006", "75749", "ios.com.chillyroom.dungeonshooter.new_pkg_72", "com.chillyroom.dungeonshooter.new_pkg_72", "4637366503109523235/4635933743767826148", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010072.png", "6.5.0-2024八月版本") },
            { "new_pkg_70", new SkuData("new_pkg_70", "道士礼包1", "Taoist Pack 1", "道士禮包1", "Quà Đạo Sĩ 1", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "247", "247", "20247", "", "232007", "75750", "ios.com.chillyroom.dungeonshooter.new_pkg_70", "com.chillyroom.dungeonshooter.new_pkg_70", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010070.png", "6.5.0-2024八月版本") },
            { "fighter_skin_3", new SkuData("fighter_skin_3", "武斗家皮肤 - 玉面狐狸", "Skin - Fighter - Jade-Faced Princess", "武鬥家皮膚 - 玉面狐狸", "Skin - Võ Đấu Gia - Hồ Ly Mặt Ngọc", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "248", "248", "20248", "", "232008", "78917", "ios.com.chillyroom.dungeonshooter.fighter_skin_3", "com.chillyroom.dungeonshooter.fighter_skin_3", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/fighter_3_ui.png", "6.6.0-2024九月版本") },
            { "elf_skin_18", new SkuData("elf_skin_18", "精灵皮肤 - 步练师", "Skin - Elf - Bu Lianshi", "精靈皮膚 - 步練師", "Skin - Tinh Linh - Bộ Luyện Sư", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "249", "249", "20249", "", "232009", "78918", "ios.com.chillyroom.dungeonshooter.elf_skin_18", "com.chillyroom.dungeonshooter.elf_skin_18", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/elves_18_ui.png", "6.6.0-2024九月版本") },
            { "astrologist_skin_3", new SkuData("astrologist_skin_3", "占星师皮肤 - 极光圣骑", "Skin - Astromancer - Aurora Cavalier", "占星師皮膚 - 極光聖騎", "Skin - Chiêm Tinh Sư - Cực Quang Thánh Kỵ", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "250", "250", "20250", "", "232010", "78919", "ios.com.chillyroom.dungeonshooter.astrologist_skin_3", "com.chillyroom.dungeonshooter.astrologist_skin_3", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/astrologist_3_ui.png", "6.6.0-2024九月版本") },
            { "skill_fighter_1", new SkuData("skill_fighter_1", "武斗家二技能 - 旋风腿", "Skill - Fighter - Whirlwind Kick", "武鬥家二技能 - 旋風腿", "Kỹ năng - Võ Đấu Gia - Cú Đá Lốc Xoáy", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "251", "251", "20251", "", "232011", "78920", "ios.com.chillyroom.dungeonshooter.skill_fighter_1", "com.chillyroom.dungeonshooter.skill_fighter_1", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/fighter_skill2Icon_2.png", "6.6.0-2024九月版本") },
            { "new_pkg_74", new SkuData("new_pkg_74", "占星师技能礼包1", "Astromancer Skill Pack 1", "占星師技能禮包1", "Quà Kỹ Năng Chiêm Tinh Sư 1", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "252", "252", "20252", "", "232012", "78921", "ios.com.chillyroom.dungeonshooter.new_pkg_74", "com.chillyroom.dungeonshooter.new_pkg_74", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010074.png", "6.6.0-2024九月版本") },
            { "new_pkg_75", new SkuData("new_pkg_75", "占星师技能礼包2", "Astromancer Skill Pack 2", "占星師技能禮包2", "Quà Kỹ Năng Chiêm Tinh Sư 2", 1, 1, "30", "$5.99/¥36", "₽449/¥36", "₫149000/$5.99", "253", "253", "20253", "", "232013", "78922", "ios.com.chillyroom.dungeonshooter.new_pkg_75", "com.chillyroom.dungeonshooter.new_pkg_75", "4637366503109523235/4635933743767826148", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010075.png", "6.6.0-2024九月版本") },
            { "new_pkg_76", new SkuData("new_pkg_76", "武斗家技能礼包1", "Fighter Skill Pack 1", "武鬥家技能禮包1", "Quà Kỹ Năng Võ Đấu Gia 1", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "254", "254", "20254", "", "232014", "78923", "ios.com.chillyroom.dungeonshooter.new_pkg_76", "com.chillyroom.dungeonshooter.new_pkg_76", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010076.png", "6.6.0-2024九月版本") },
            { "new_pkg_77", new SkuData("new_pkg_77", "武斗家技能礼包2", "Fighter Skill Pack 2", "武鬥家技能禮包2", "Quà Kỹ Năng Võ Đấu Gia 2", 1, 1, "30", "$5.99/¥36", "₽449/¥36", "₫149000/$5.99", "255", "255", "20255", "", "232015", "78924", "ios.com.chillyroom.dungeonshooter.new_pkg_77", "com.chillyroom.dungeonshooter.new_pkg_77", "4637366503109523235/4635933743767826148", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010077.png", "6.6.0-2024九月版本") },
            { "new_pkg_78", new SkuData("new_pkg_78", "工程师礼包1", "Engineer Pack 1", "工程師禮包1", "Quà Kỹ Sư 1", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "256", "256", "20256", "", "232016", "78925", "ios.com.chillyroom.dungeonshooter.new_pkg_78", "com.chillyroom.dungeonshooter.new_pkg_78", "4635639827510787213/4634914334553984538", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010078.png", "6.6.0-2024九月版本") },
            { "arcaneknight_skin_4", new SkuData("arcaneknight_skin_4", "咒法骑士皮肤 - 铁扇公主", "Skin - Arcane Knight - Princess Iron Fan", "咒法騎士皮膚 - 鐵扇公主", "Skin - Kỵ Sĩ Bùa Chú - Thiết Phiết Công Chúa", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "257", "257", "20257", "", "232017", "82477", "ios.com.chillyroom.dungeonshooter.arcaneknight_skin_4", "com.chillyroom.dungeonshooter.arcaneknight_skin_4", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/arcaneKnight_4_ui.png", "6.7.0-2024十月版本") },
            { "warliege_skin_5", new SkuData("warliege_skin_5", "领主皮肤 - 寅将军", "Skin - Warliege - General Yin", "領主皮膚 - 寅將軍", "Skin - Lãnh chúa - Dần Tướng Quân", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "258", "258", "20258", "", "232018", "82478", "ios.com.chillyroom.dungeonshooter.warliege_skin_5", "com.chillyroom.dungeonshooter.warliege_skin_5", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/warliege_5_ui.png", "6.7.0-2024十月版本") },
            { "skill_arcaneknight_1", new SkuData("skill_arcaneknight_1", "咒法骑士二技能 - 庇护法阵", "Skill - Arcane Knight - Aegis Circle", "咒法騎士二技能 - 庇護法陣", "Kỹ năng - Kỵ Sĩ Bùa Chú - Pháp Trận Bảo Hộ", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "259", "259", "20259", "", "232019", "82479", "ios.com.chillyroom.dungeonshooter.skill_arcaneknight_1", "com.chillyroom.dungeonshooter.skill_arcaneknight_1", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/arcaneknight_skillIcon_1.png", "6.7.0-2024十月版本") },
            { "new_pkg_79", new SkuData("new_pkg_79", "咒法骑士技能礼包1", "Arcane Knight Skill Pack 1", "咒法騎士技能禮包1", "Quà Kỹ Năng Kỵ Sĩ Bùa Chú 1", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "260", "260", "20260", "", "232020", "82480", "ios.com.chillyroom.dungeonshooter.new_pkg_79", "com.chillyroom.dungeonshooter.new_pkg_79", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010079.png", "6.7.0-2024十月版本") },
            { "new_pkg_80", new SkuData("new_pkg_80", "咒法骑士技能礼包2", "Arcane Knight Skill Pack 2", "咒法騎士技能禮包2", "Quà Kỹ Năng Kỵ Sĩ Bùa Chú 2", 1, 1, "25", "$4.99/¥30", "₽399/¥30", "₫124000/$4.99", "261", "261", "20261", "", "232021", "82481", "ios.com.chillyroom.dungeonshooter.new_pkg_80", "com.chillyroom.dungeonshooter.new_pkg_80", "4637558555263124425/4637165942710777751", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010080.png", "6.7.0-2024十月版本") },
            { "swordmaster_skin_5", new SkuData("swordmaster_skin_5", "剑宗皮肤 - 青锋", "Skin - Sword Master - Jade Blade", "劍宗皮膚 - 青鋒", "Skin - Kiếm Tông - Kiếm Xanh", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "262", "262", "20262", "", "232022", "83248", "ios.com.chillyroom.dungeonshooter.swordmaster_skin_5", "com.chillyroom.dungeonshooter.swordmaster_skin_5", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/swordMaster_5_ui.png", "6.8.0-2024十一月版本") },
            { "druid_skin_20", new SkuData("druid_skin_20", "德鲁伊皮肤 - 风纪·爱丽丝", "Skin - Druid - Justice Alice", "德魯伊皮膚 - 風紀·愛麗絲", "Skin - Druid - Alice-Kỷ Luật", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "263", "263", "20263", "", "232023", "83249", "ios.com.chillyroom.dungeonshooter.druid_skin_20", "com.chillyroom.dungeonshooter.druid_skin_20", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/druid_20_ui.png", "6.8.0-2024十一月版本") },
            { "necromancer_skin_17", new SkuData("necromancer_skin_17", "死灵法师皮肤 - 手机党·小梦", "Skin - Necromancer - Mobile Maven Meng", "死靈法師皮膚 - 手機黨·小夢", "Skin - Pháp Sư Tử Linh - Điện Thoại-Giấc Mơ", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "264", "264", "20264", "", "232024", "83250", "ios.com.chillyroom.dungeonshooter.necromancer_skin_17", "com.chillyroom.dungeonshooter.necromancer_skin_17", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/necromancer_17_ui.png", "6.8.0-2024十一月版本") },
            { "new_pkg_86", new SkuData("new_pkg_86", "校园时光皮肤礼包1", "School Life Skin Pack 1", "校園時光皮膚禮包1", "Quà Skin Thời Gian Vườn Trường 1", 1, 1, "45", "$8.99/¥54", "₽749/¥54", "₫223000/$8.99", "265", "265", "20265", "", "232025", "83251", "ios.com.chillyroom.dungeonshooter.new_pkg_86", "com.chillyroom.dungeonshooter.new_pkg_86", "4634958946912594240/4634716269219238891", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010086.png", "6.8.0-2024十一月版本") },
            { "new_pkg_87", new SkuData("new_pkg_87", "青锋礼包1", "Jade Blade Pack 1", "青鋒禮包1", "Quà Kiếm Xanh 1", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "266", "266", "20266", "", "232026", "83252", "ios.com.chillyroom.dungeonshooter.new_pkg_87", "com.chillyroom.dungeonshooter.new_pkg_87", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010087.png", "6.8.0-2024十一月版本") },
            { "new_pkg_88", new SkuData("new_pkg_88", "青锋礼包2", "Jade Blade Pack 2", "青鋒禮包2", "Quà Kiếm Xanh 2", 1, 1, "30", "$5.99/¥36", "₽449/¥36", "₫149000/$5.99", "267", "267", "20267", "", "232027", "83253", "ios.com.chillyroom.dungeonshooter.new_pkg_88", "com.chillyroom.dungeonshooter.new_pkg_88", "4637366503109523235/4635933743767826148", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010088.png", "6.8.0-2024十一月版本") },
            { "new_pkg_89", new SkuData("new_pkg_89", "影月礼包1", "Shadowmoon Pack 1", "影月禮包1", "Quà Ảnh Nguyệt 1", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "268", "268", "20268", "", "232028", "83254", "ios.com.chillyroom.dungeonshooter.new_pkg_89", "com.chillyroom.dungeonshooter.new_pkg_89", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010089.png", "6.8.0-2024十一月版本") },
            { "bard_skin_1", new SkuData("bard_skin_1", "吟游诗人皮肤 - 猫语歌者·妮芙蒂", "Skin - Bard - Feline Songstress Nefriti", "吟遊詩人皮膚 - 貓語歌者·妮芙蒂", "Skin - Người Hát Rong - Ca Sĩ Mèo Nefriti", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "269", "269", "20269", "", "232029", "84759", "ios.com.chillyroom.dungeonshooter.bard_skin_1", "com.chillyroom.dungeonshooter.bard_skin_1", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/bard_1_ui.png", "7.0.0-2025一月版本") },
            { "warlock_skin_9", new SkuData("warlock_skin_9", "恶魔术士皮肤 - 混沌魔王·暗月", "Skin - Demonmancer - Voidlord Eclipsa", "惡魔術士皮膚 - 混沌魔王·暗月", "Skin - Thuật sĩ ác ma - Ma Vương Hỗn Độn-Ám Nguyệt", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "270", "270", "20270", "", "232030", "84760", "ios.com.chillyroom.dungeonshooter.warlock_skin_9", "com.chillyroom.dungeonshooter.warlock_skin_9", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/warlock_9_ui.png", "7.0.0-2025一月版本") },
            { "pastor_skin_20", new SkuData("pastor_skin_20", "牧师皮肤 - 辉洁星使·伊丽莎白", "Skin - Priestess - Stellar Envoy Elizabeth", "牧師皮膚 - 輝潔星使·伊莉莎白", "Skin - Mục Sư - Tinh Sứ Elizabeth", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "271", "271", "20271", "", "232031", "84761", "ios.com.chillyroom.dungeonshooter.pastor_skin_20", "com.chillyroom.dungeonshooter.pastor_skin_20", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/priest_20_ui.png", "7.0.0-2025一月版本") },
            { "wizard_skin_24", new SkuData("wizard_skin_24", "法师皮肤 - 古灵精怪·幽铃", "Skin - Witch - Soulweaver Lunelle", "法師皮膚 - 古靈精怪·幽鈴", "Skin - Pháp Sư - Cổ Linh Quái - Hữu Linh", 1, 1, "20", "$3.99/¥22", "₽299/¥22", "₫99000/$3.99", "272", "272", "20272", "", "232032", "84762", "ios.com.chillyroom.dungeonshooter.wizard_skin_24", "com.chillyroom.dungeonshooter.wizard_skin_24", "4637121953801084017/4636472375067537543", "HeroSkin", "All", "Assets/RGTexture/ui/skin/mage_24_ui.png", "7.0.0-2025一月版本") },
            { "alchemist_skin_19", new SkuData("alchemist_skin_19", "炼金术士皮肤 - 实验室·叶梨", "Skin - Alchemist - Experimenter Ellie", "煉金術士皮膚 - 實驗室·葉梨", "Skin - Thuật Sĩ Luyện Kim - Phòng Thí Nghiệm-Ellie", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "273", "273", "20273", "", "232033", "84763", "ios.com.chillyroom.dungeonshooter.alchemist_skin_19", "com.chillyroom.dungeonshooter.alchemist_skin_19", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/alchemist_19_ui.png", "7.0.0-2025一月版本") },
            { "shooter", new SkuData("shooter", "角色 - 枪手", "Character - Gunner", "角色 - 槍手", "Nhân vật - Tay Súng", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "274", "274", "20274", "", "232034", "84764", "ios.com.chillyroom.dungeonshooter.shooter", "com.chillyroom.dungeonshooter.shooter", "*******************/*******************", "Hero", "All", "Assets/RGTexture/ui/skin/shooter_0_ui.png", "7.0.0-2025一月版本") },
            { "rogue_skin_24", new SkuData("rogue_skin_24", "游侠皮肤 - 猎魔男爵·克雷顿", "Skin - Rogue - Demonbane Crayton", "遊俠皮膚 - 獵魔男爵·克雷頓", "Skin - Hiệp Sĩ - Nam Tước Săn Ma-Crayton", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "275", "275", "20275", "", "232035", "84765", "ios.com.chillyroom.dungeonshooter.rogue_skin_24", "com.chillyroom.dungeonshooter.rogue_skin_24", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ranger_24_ui.png", "7.0.0-2025一月版本") },
            { "warliege_skin_6", new SkuData("warliege_skin_6", "领主皮肤 - 魔械统帅·奥古斯都", "Skin - Warliege - Armsmaster Augustus", "領主皮膚 - 魔械統帥·奧古斯都", "Skin - Lãnh chúa - Thống Soái Augustus", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "276", "276", "20276", "", "232036", "84766", "ios.com.chillyroom.dungeonshooter.warliege_skin_6", "com.chillyroom.dungeonshooter.warliege_skin_6", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/warliege_6_ui.png", "7.0.0-2025一月版本") },
            { "shooter_skin_1", new SkuData("shooter_skin_1", "枪手皮肤 - 炽焰亡徒·卢西恩", "Skin - Gunner - Renegade Lucian", "槍手皮膚 - 熾焰亡徒·盧西恩", "Skin - Tay Súng - Vong Đồ Lucian", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "277", "277", "20277", "", "232037", "84767", "ios.com.chillyroom.dungeonshooter.shooter_skin_1", "com.chillyroom.dungeonshooter.shooter_skin_1", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/shooter_1_ui.png", "7.0.0-2025一月版本") },
            { "officer_skin_17", new SkuData("officer_skin_17", "警官皮肤 - 赤色暴君·里昂", "Skin - Officer - Bloodlord Lyon", "警官皮膚 - 赤色暴君·里昂", "Skin - Cảnh Sát - Bạo Quân Lyon", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "278", "278", "20278", "", "232038", "84768", "ios.com.chillyroom.dungeonshooter.officer_skin_17", "com.chillyroom.dungeonshooter.officer_skin_17", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/officer_17_ui.png", "7.0.0-2025一月版本") },
            { "new_pkg_91", new SkuData("new_pkg_91", "魔法之旅皮肤礼包1", "Into the Wonderland Skin Pack 1", "魔法之旅皮膚禮包1", "Quà Skin Hành Trình Phép Thuật 1", 1, 1, "50", "$9.99/¥60", "₽899/¥60", "₫248000/$9.99", "279", "279", "20279", "", "232039", "84769", "ios.com.chillyroom.dungeonshooter.new_pkg_91", "com.chillyroom.dungeonshooter.new_pkg_91", "4637080718904886127/4636247701399558970", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010091.png", "7.0.0-2025一月版本") },
            { "new_pkg_92", new SkuData("new_pkg_92", "吟游诗人礼包1", "Bard Pack 1", "吟遊詩人禮包1", "Quà Người Hát Rong 1", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "280", "280", "20280", "", "232040", "84770", "ios.com.chillyroom.dungeonshooter.new_pkg_92", "com.chillyroom.dungeonshooter.new_pkg_92", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010092.png", "7.0.0-2025一月版本") },
            { "new_pkg_96", new SkuData("new_pkg_96", "新角色合集礼包2", "New Characters Pack 2", "新角色合集禮包2", "Quà BST nhân vật mới 2", 1, 1, "25", "$4.99/¥30", "₽399/¥30", "₫124000/$4.99", "281", "281", "20281", "", "232041", "84771", "ios.com.chillyroom.dungeonshooter.new_pkg_96", "com.chillyroom.dungeonshooter.new_pkg_96", "4637558555263124425/4637165942710777751", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010096.png", "7.0.0-2025一月版本") },
            { "new_pkg_98", new SkuData("new_pkg_98", "荒野焰影皮肤礼包1", "Blaze of the Wastes Skin Pack 1", "荒野焰影皮膚禮包1", "Quà Skin Lửa Ảnh Hoang Dã 1", 1, 1, "45", "$8.99/¥54", "₽749/¥54", "₫223000/$8.99", "282", "282", "20282", "", "232042", "84772", "ios.com.chillyroom.dungeonshooter.new_pkg_98", "com.chillyroom.dungeonshooter.new_pkg_98", "4634958946912594240/4634716269219238891", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010098.png", "7.0.0-2025一月版本") },
            { "new_pkg_99", new SkuData("new_pkg_99", "枪手礼包1", "Gunner Pack 1", "槍手禮包1", "Quà Tay Súng 1", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "283", "283", "20283", "", "232043", "84773", "ios.com.chillyroom.dungeonshooter.new_pkg_99", "com.chillyroom.dungeonshooter.new_pkg_99", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010099.png", "7.0.0-2025一月版本") },
            { "new_pkg_102", new SkuData("new_pkg_102", "新角色合集礼包3", "New Characters Pack 3", "新角色合集禮包3", "Quà BST nhân vật mới 3", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "284", "284", "20284", "", "232044", "84774", "ios.com.chillyroom.dungeonshooter.new_pkg_102", "com.chillyroom.dungeonshooter.new_pkg_102", "4635639827510787213/4634914334553984538", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010102.png", "7.0.0-2025一月版本") },
            { "new_pkg_103", new SkuData("new_pkg_103", "回流礼包", "Welcome Back Pack 2", "回流禮包", "Quà Trở Về 2", 1, 0, "1", "$0.99/¥1", "₽79/¥1", "₫25000/$0.99", "285", "285", "20285", "", "232045", "84775", "ios.com.chillyroom.dungeonshooter.new_pkg_103", "com.chillyroom.dungeonshooter.new_pkg_103", "*******************/*******************", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010103.png", "7.0.0-2025一月版本") },
            { "hall_skin_5", new SkuData("hall_skin_5", "大厅装饰 - 欧博士的会客间", "Lobby Decor - Dr. O's Parlor", "大廳裝飾 - 歐博士的會客間", "Trang Trí Sảnh - Phòng Tiếp Khách Của Ouboshi", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "286", "286", "20286", "", "232046", "84776", "ios.com.chillyroom.dungeonshooter.hall_skin_5", "com.chillyroom.dungeonshooter.hall_skin_5", "4635639827510787213/4634914334553984538", "Other", "All", "Assets/RGTexture/ui/hero_room_skin/hall/hall_skin_icon_5.png", "7.0.0-2025一月版本") },
            { "elf_skin_19", new SkuData("elf_skin_19", "精灵皮肤 - 体育生·柚希", "Skin - Elf - Athlete Ayuki", "精靈皮膚 - 體育生·柚希", "Skin - Tinh Linh - Vận Động Viên-Yuuhi", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "287", "287", "20287", "", "232047", "", "ios.com.chillyroom.dungeonshooter.elf_skin_19", "com.chillyroom.dungeonshooter.elf_skin_19", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/elves_19_ui.png", "7.1.0-2025三月版本") },
            { "druid_skin_22", new SkuData("druid_skin_22", "德鲁伊皮肤 - 花蝶仙姬·洛芙蕾", "Skin - Druid - Butterflette Lofeyra", "德魯伊皮膚 - 花蝶仙姬·洛芙蕾", "Skin - Druid - Tiên Cơ Hoa Điệp-Loffrey", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "288", "288", "20288", "", "232048", "", "ios.com.chillyroom.dungeonshooter.druid_skin_22", "com.chillyroom.dungeonshooter.druid_skin_22", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/druid_22_ui.png", "7.1.0-2025三月版本") },
            { "taoist_skin_20", new SkuData("taoist_skin_20", "道士皮肤 - 教主·浮士德", "Skin - Taoist - Dominus Faustus", "道士皮膚 - 教主·浮士德", "Skin - Đạo Sĩ - Giáo Chủ-Faust", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "289", "289", "20289", "", "232049", "", "ios.com.chillyroom.dungeonshooter.taoist_skin_20", "com.chillyroom.dungeonshooter.taoist_skin_20", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/taoist_20_ui.png", "7.1.0-2025三月版本") },
            { "skill_costumeprince_2", new SkuData("skill_costumeprince_2", "皮套王子三技能 - 降临！紫爵士！", "Skill - Costume Prince - Manifest! King Violet!", "皮套王子三技能 - 降臨！紫爵士！", "Kỹ năng-Trang Phục Hoàng Tử-Giáng Lâm!Tước Sĩ Tím!", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "290", "290", "20290", "", "232050", "", "ios.com.chillyroom.dungeonshooter.skill_costumeprince_2", "com.chillyroom.dungeonshooter.skill_costumeprince_2", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/costumePrince_skillIcon_3.png", "7.1.0-2025三月版本") },
            { "new_pkg_107", new SkuData("new_pkg_107", "体育生·柚希礼包1", "Athlete Ayuki Pack 1", "體育生·柚希禮包1", "Quà Vận Động Viên-Yuuhi 1", 1, 1, "30", "$5.99/¥36", "₽449/¥36", "₫149000/$5.99", "291", "291", "20291", "", "232051", "", "ios.com.chillyroom.dungeonshooter.new_pkg_107", "com.chillyroom.dungeonshooter.new_pkg_107", "4637366503109523235/4635933743767826148", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010107.png", "7.1.0-2025三月版本") },
            { "new_pkg_108", new SkuData("new_pkg_108", "体育生·柚希礼包2", "Athlete Ayuki Pack 2", "體育生·柚希禮包2", "Quà Vận Động Viên-Yuuhi 2", 1, 1, "35", "$6.99/¥42", "₽549/¥42", "₫174000/$6.99", "292", "292", "20292", "", "232052", "", "ios.com.chillyroom.dungeonshooter.new_pkg_108", "com.chillyroom.dungeonshooter.new_pkg_108", "4636817703706715376/4635839191945952686", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010108.png", "7.1.0-2025三月版本") },
            { "new_pkg_109", new SkuData("new_pkg_109", "教主·浮士德礼包1", "Dominus Faustus Pack 1", "教主·浮士德禮包1", "Quà Giáo Chủ-Faust 1", 1, 1, "30", "$5.99/¥36", "₽449/¥36", "₫149000/$5.99", "293", "293", "20293", "", "232053", "", "ios.com.chillyroom.dungeonshooter.new_pkg_109", "com.chillyroom.dungeonshooter.new_pkg_109", "4637366503109523235/4635933743767826148", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010109.png", "7.1.0-2025三月版本") },
            { "new_pkg_110", new SkuData("new_pkg_110", "教主·浮士德礼包2", "Dominus Faustus Pack 2", "教主·浮士德禮包2", "Quà Giáo Chủ-Faust 2", 1, 1, "35", "$6.99/¥42", "₽549/¥42", "₫174000/$6.99", "294", "294", "20294", "", "232054", "", "ios.com.chillyroom.dungeonshooter.new_pkg_110", "com.chillyroom.dungeonshooter.new_pkg_110", "4636817703706715376/4635839191945952686", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010110.png", "7.1.0-2025三月版本") },
            { "new_pkg_111", new SkuData("new_pkg_111", "花蝶仙姬·洛芙蕾礼包1", "Butterflette Lofeyra Pack 1", "花蝶仙姬·洛芙蕾禮包1", "Quà Tiên Cơ Hoa Điệp-Loffrey 1", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "295", "295", "20295", "", "232055", "", "ios.com.chillyroom.dungeonshooter.new_pkg_111", "com.chillyroom.dungeonshooter.new_pkg_111", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010111.png", "7.1.0-2025三月版本") },
            { "new_pkg_112", new SkuData("new_pkg_112", "花蝶仙姬·洛芙蕾礼包2", "Butterflette Lofeyra Pack 2", "花蝶仙姬·洛芙蕾禮包2", "Quà Tiên Cơ Hoa Điệp-Loffrey 2", 1, 1, "30", "$5.99/¥36", "₽449/¥36", "₫149000/$5.99", "296", "296", "20296", "", "232056", "", "ios.com.chillyroom.dungeonshooter.new_pkg_112", "com.chillyroom.dungeonshooter.new_pkg_112", "4637366503109523235/4635933743767826148", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010112.png", "7.1.0-2025三月版本") },
            { "swordmaster_skin_7", new SkuData("swordmaster_skin_7", "剑宗皮肤 - 绯蔷之主·伊芙琳", "Skin - Sword Master - Rosadame Evelyn", "劍宗皮膚 - 緋薔之主·伊芙琳", "Skin - Kiếm Tông - Chủ Nhân Hoa Hồng-Evelyn", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "297", "297", "20297", "", "232057", "", "ios.com.chillyroom.dungeonshooter.swordmaster_skin_7", "com.chillyroom.dungeonshooter.swordmaster_skin_7", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/swordMaster_7_ui.png", "7.2.0-2024四月版本") },
            { "knight_skin_29", new SkuData("knight_skin_29", "骑士皮肤 - 狱火审判·杰洛特", "Skin - Knight - Pyroverdict Geralt", "騎士皮膚 - 獄火審判·傑洛特", "Skin - Kỵ Sĩ - Phán Quyết Ngục Hỏa-Geralt", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "298", "298", "20298", "", "232058", "", "ios.com.chillyroom.dungeonshooter.knight_skin_29", "com.chillyroom.dungeonshooter.knight_skin_29", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/knight_29_ui.png", "7.2.0-2024四月版本") },
            { "bard_skin_4", new SkuData("bard_skin_4", "吟游诗人皮肤 - 啦啦队·晴空", "Skin - Bard - Cheerleader Clara", "吟遊詩人皮膚 - 啦啦隊·晴空", "Skin - Người Hát Rong - Đội Cổ Vũ·Clara", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "299", "299", "20299", "", "232059", "", "ios.com.chillyroom.dungeonshooter.bard_skin_4", "com.chillyroom.dungeonshooter.bard_skin_4", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/bard_4_ui.png", "7.2.0-2024四月版本") },
            { "aigirl_skin_1", new SkuData("aigirl_skin_1", "机械姬皮肤 - 煌蜂", "Skin - Machina - Hexagon", "機械姬皮膚 - 煌蜂", "Skin - Nữ Hoàng Cơ Giới - Hoàng Phong", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "300", "300", "20300", "", "232060", "", "ios.com.chillyroom.dungeonshooter.aigirl_skin_1", "com.chillyroom.dungeonshooter.aigirl_skin_1", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/aiGirl_1_ui.png", "7.2.0-2024四月版本") },
            { "aigirl", new SkuData("aigirl", "角色 - 机械姬", "Character - Machina", "角色 - 機械姬", "Nhân vật - Nữ Hoàng Cơ Giới", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "301", "301", "20301", "", "232061", "", "ios.com.chillyroom.dungeonshooter.aigirl", "com.chillyroom.dungeonshooter.aigirl", "*******************/*******************", "Hero", "All", "Assets/RGTexture/ui/skin/aiGirl_0_ui.png", "7.2.0-2024四月版本") },
            { "new_pkg_115", new SkuData("new_pkg_115", "机械姬礼包", "Machina Pack", "機械姬禮包", "Quà Nữ Hoàng Cơ Giới", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "302", "302", "20302", "", "232062", "", "ios.com.chillyroom.dungeonshooter.new_pkg_115", "com.chillyroom.dungeonshooter.new_pkg_115", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010115.png", "7.2.0-2024四月版本") },
            { "new_pkg_118", new SkuData("new_pkg_118", "绯蔷之主·伊芙琳礼包1", "Rosadame Evelyn Pack 1", "緋薔之主·伊芙琳禮包1", "Quà Chủ Nhân Hoa Hồng-Evelyn 1", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "303", "303", "20303", "", "232063", "", "ios.com.chillyroom.dungeonshooter.new_pkg_118", "com.chillyroom.dungeonshooter.new_pkg_118", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010118.png", "7.2.0-2024四月版本") },
            { "new_pkg_119", new SkuData("new_pkg_119", "绯蔷之主·伊芙琳礼包2", "Rosadame Evelyn Pack 2", "緋薔之主·伊芙琳禮包2", "Quà Chủ Nhân Hoa Hồng-Evelyn 2", 1, 1, "30", "$5.99/¥36", "₽449/¥36", "₫149000/$5.99", "304", "304", "20304", "", "232064", "", "ios.com.chillyroom.dungeonshooter.new_pkg_119", "com.chillyroom.dungeonshooter.new_pkg_119", "4637366503109523235/4635933743767826148", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010119.png", "7.2.0-2024四月版本") },
            { "new_pkg_120", new SkuData("new_pkg_120", "啦啦队·晴空礼包", "Cheerleader Clara Pack", "啦啦隊·晴空禮包", "Quà Đội Cổ Vũ·Clara", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "305", "305", "20305", "", "232065", "", "ios.com.chillyroom.dungeonshooter.new_pkg_120", "com.chillyroom.dungeonshooter.new_pkg_120", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010120.png", "7.2.0-2024四月版本") },
            { "new_pkg_121", new SkuData("new_pkg_121", "狱火审判·杰洛特礼包", "Pyroverdict Geralt Pack", "獄火審判·傑洛特禮包", "Quà Phán Quyết Ngục Hỏa-Geralt", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "306", "306", "20306", "", "232066", "", "ios.com.chillyroom.dungeonshooter.new_pkg_121", "com.chillyroom.dungeonshooter.new_pkg_121", "4635639827510787213/4634914334553984538", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010121.png", "7.2.0-2024四月版本") },
            { "fighter_skin_6", new SkuData("fighter_skin_6", "武斗家皮肤 - 新闻社·知夏", "Skin - Fighter - Journalist Junie", "武鬥家皮膚 - 新聞社·知夏", "Skin - Võ Đấu Gia - Thông Tấn Xã-Junie", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "307", "307", "20307", "", "232067", "", "ios.com.chillyroom.dungeonshooter.fighter_skin_6", "com.chillyroom.dungeonshooter.fighter_skin_6", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/fighter_6_ui.png", "7.3.0-2025六月版本") },
            { "warliege_skin_8", new SkuData("warliege_skin_8", "领主皮肤 - 摇滚社·佐克", "Skin - Warliege - Rockstar Zock", "領主皮膚 - 搖滾社·佐克", "Skin - Lãnh chúa - Rockstar-Zock", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "308", "308", "20308", "", "232068", "", "ios.com.chillyroom.dungeonshooter.warliege_skin_8", "com.chillyroom.dungeonshooter.warliege_skin_8", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/warliege_8_ui.png", "7.3.0-2025六月版本") },
            { "vampire_skin_21", new SkuData("vampire_skin_21", "吸血鬼皮肤 - 红影裁决·维恩", "Skin - Vampire - Shadowthane Vane", "吸血鬼皮膚 - 紅影裁決·維恩", "Skin - Ma Ca Rồng - Phán Quyết Bóng Đỏ-Vane", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "309", "309", "20309", "", "232069", "", "ios.com.chillyroom.dungeonshooter.vampire_skin_21", "com.chillyroom.dungeonshooter.vampire_skin_21", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/vampire_21_ui.png", "7.3.0-2025六月版本") },
            { "skill_warliege_1", new SkuData("skill_warliege_1", "领主二技能 - 决心猛冲", "Skill - Warliege - Resolute Rush", "領主二技能 - 決心猛衝", "Kỹ năng - Lãnh chúa - Quyết Tâm Xung Phong", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "310", "310", "20310", "", "232070", "", "ios.com.chillyroom.dungeonshooter.skill_warliege_1", "com.chillyroom.dungeonshooter.skill_warliege_1", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/warliege_skillIcon_1.png", "7.3.0-2025六月版本") },
            { "new_pkg_124", new SkuData("new_pkg_124", "领主技能礼包1", "Warliege Skill Pack 1", "領主技能禮包1", "Quà Kỹ Năng Lãnh chúa 1", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "311", "311", "20311", "", "232071", "", "ios.com.chillyroom.dungeonshooter.new_pkg_124", "com.chillyroom.dungeonshooter.new_pkg_124", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010124.png", "7.3.0-2025六月版本") },
            { "new_pkg_125", new SkuData("new_pkg_125", "领主技能礼包2", "Warliege Skill Pack 2", "領主技能禮包2", "Quà Kỹ Năng Lãnh chúa 2", 1, 1, "30", "$5.99/¥36", "₽449/¥36", "₫149000/$5.99", "312", "312", "20312", "", "232072", "", "ios.com.chillyroom.dungeonshooter.new_pkg_125", "com.chillyroom.dungeonshooter.new_pkg_125", "4637366503109523235/4635933743767826148", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010125.png", "7.3.0-2025六月版本") },
            { "new_pkg_130", new SkuData("new_pkg_130", "红影裁决·维恩礼包1", "Shadowthane Vane Pack 1", "紅影裁決·維恩禮包1", "Quà Phán Quyết Bóng Đỏ-Vane 1", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "313", "313", "20313", "", "232073", "", "ios.com.chillyroom.dungeonshooter.new_pkg_130", "com.chillyroom.dungeonshooter.new_pkg_130", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010130.png", "7.3.0-2025六月版本") },
            { "new_pkg_131", new SkuData("new_pkg_131", "红影裁决·维恩礼包2", "Shadowthane Vane Pack 2", "紅影裁決·維恩禮包2", "Quà Phán Quyết Bóng Đỏ-Vane 2", 1, 1, "30", "$5.99/¥36", "₽449/¥36", "₫149000/$5.99", "314", "314", "20314", "", "232074", "", "ios.com.chillyroom.dungeonshooter.new_pkg_131", "com.chillyroom.dungeonshooter.new_pkg_131", "4637366503109523235/4635933743767826148", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010131.png", "7.3.0-2025六月版本") },
            { "new_pkg_128", new SkuData("new_pkg_128", "新闻社·知夏礼包1", "Journalist Junie Pack 1", "新聞社·知夏禮包1", "Quà Thông Tấn Xã-Junie 1", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "315", "315", "20315", "", "232075", "", "ios.com.chillyroom.dungeonshooter.new_pkg_128", "com.chillyroom.dungeonshooter.new_pkg_128", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010128.png", "7.3.0-2025六月版本") },
            { "new_pkg_129", new SkuData("new_pkg_129", "新闻社·知夏礼包2", "Journalist Junie Pack 2", "新聞社·知夏禮包2", "Quà Thông Tấn Xã-Junie 2", 1, 1, "35", "$6.99/¥42", "₽549/¥42", "₫174000/$6.99", "316", "316", "20316", "", "232076", "", "ios.com.chillyroom.dungeonshooter.new_pkg_129", "com.chillyroom.dungeonshooter.new_pkg_129", "4636817703706715376/4635839191945952686", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010129.png", "7.3.0-2025六月版本") },
            { "new_pkg_132", new SkuData("new_pkg_132", "蝠翼冥冠·卡蜜拉礼包1", "Nyxcrown Camilla Pack 1", "蝠翼冥冠·卡蜜拉禮包1", "Quà Vương Miện Dơi Đêm-Camilla 1", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "317", "317", "20317", "", "232077", "", "ios.com.chillyroom.dungeonshooter.new_pkg_132", "com.chillyroom.dungeonshooter.new_pkg_132", "4635639827510787213/4634914334553984538", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010132.png", "7.3.0-2025六月版本") },
            { "assassin_skin_26", new SkuData("assassin_skin_26", "刺客皮肤 - 海神波塞冬", "Skin - Assassin - Poseidon, King of the Sea", "刺客皮膚 - 海神波塞冬", "Skin - Thích Khách - Thần Biển Poseidon", 1, 1, "20", "$3.99/¥22", "₽299/¥22", "₫99000/$3.99", "318", "318", "20318", "", "232078", "", "ios.com.chillyroom.dungeonshooter.assassin_skin_26", "com.chillyroom.dungeonshooter.assassin_skin_26", "4637121953801084017/4636472375067537543", "HeroSkin", "All", "Assets/RGTexture/ui/skin/assassin_26_ui.png", "7.4.0-2025七月版本") },
            { "wizard_skin_27", new SkuData("wizard_skin_27", "法师皮肤 - 学园偶像·歌穗", "Skin - Witch - Idol Isla", "法師皮膚 - 學園偶像·歌穗", "Skin - Pháp Sư - Thần Tượng Học Đường-Isla", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "319", "319", "20319", "", "232079", "", "ios.com.chillyroom.dungeonshooter.wizard_skin_27", "com.chillyroom.dungeonshooter.wizard_skin_27", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/mage_27_ui.png", "7.4.0-2025七月版本") },
            { "elf_skin_21", new SkuData("elf_skin_21", "精灵皮肤 - 云裳灵羽·琉璃", "Skin - Elf - Apsara Veluriya", "精靈皮膚 - 雲裳靈羽·琉璃", "Skin - Tinh Linh - Vân Thường Linh Vũ-Veluriya", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "320", "320", "20320", "", "232080", "", "ios.com.chillyroom.dungeonshooter.elf_skin_21", "com.chillyroom.dungeonshooter.elf_skin_21", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/elves_21_ui.png", "7.4.0-2025七月版本") },
            { "necromancer_skin_20", new SkuData("necromancer_skin_20", "死灵法师皮肤 - 奇迹乐章·奏", "Skin - Necromancer - Miraculous Sonatia", "死靈法師皮膚 - 奇跡樂章·奏", "Skin - Pháp Sư Tử Linh - Giai Điệu Phép Màu-Sonatia", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "321", "321", "20321", "", "232081", "", "ios.com.chillyroom.dungeonshooter.necromancer_skin_20", "com.chillyroom.dungeonshooter.necromancer_skin_20", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/necromancer_20_ui.png", "7.4.0-2025七月版本") },
            { "airbender_skin_11", new SkuData("airbender_skin_11", "气宗皮肤 - 苍风掠霄·岚枭", "Skin - Airbender - Stormcrest Falco", "氣宗皮膚 - 蒼風掠霄·嵐梟", "Skin - Khí Tông - Gió Lộng Vút Trời-Falco", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "322", "322", "20322", "", "232082", "", "ios.com.chillyroom.dungeonshooter.airbender_skin_11", "com.chillyroom.dungeonshooter.airbender_skin_11", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/airbender_11_ui.png", "7.4.0-2025七月版本") },
            { "captain_skin_1", new SkuData("captain_skin_1", "船长皮肤 - 幽冥船长·斯派克", "Skin - Captain - Dreadwake Stryke", "船長皮膚 - 幽冥船長·斯派克", "Skin - Thuyền Trưởng - Thuyền Trưởng Âm Giới-Stryke", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "323", "323", "20323", "", "232083", "", "ios.com.chillyroom.dungeonshooter.captain_skin_1", "com.chillyroom.dungeonshooter.captain_skin_1", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/captain_1_ui.png", "7.4.0-2025七月版本") },
            { "aigirl_skin_2", new SkuData("aigirl_skin_2", "机械姬皮肤 - 沧海遗珠·蓝歌", "Skin - Machina - Lostpearl Lazura", "機械姬皮膚 - 滄海遺珠·藍歌", "Skin - Nữ Hoàng Cơ Giới - Sóng Biển Xanh Thẳm-Lazura", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "324", "324", "20324", "", "232084", "", "ios.com.chillyroom.dungeonshooter.aigirl_skin_2", "com.chillyroom.dungeonshooter.aigirl_skin_2", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/aigirl_2_ui.png", "7.4.0-2025七月版本") },
            { "viking_skin_26", new SkuData("viking_skin_26", "狂战士皮肤 - 冲浪社·凌涛", "Skin - Berserker - Surfer Sean", "狂戰士皮膚 - 衝浪社·淩濤", "Skin - Berserker - CLB Lướt Sóng-Sean", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "325", "325", "20325", "", "232085", "", "ios.com.chillyroom.dungeonshooter.viking_skin_26", "com.chillyroom.dungeonshooter.viking_skin_26", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/viking_26_ui.png", "7.4.0-2025七月版本") },
            { "new_pkg_137", new SkuData("new_pkg_137", "沧溟逐浪礼包1", "Seekers of Neversea Pack 1", "滄溟逐浪禮包1", "Quà Đuổi Theo Những Con Sóng 1", 1, 1, "45", "$8.99/¥54", "₽749/¥54", "₫223000/$8.99", "326", "326", "20326", "", "232086", "", "ios.com.chillyroom.dungeonshooternew_pkg_137", "com.chillyroom.dungeonshooter.new_pkg_137", "4634958946912594240/4634716269219238891", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010137.png", "7.4.0-2025七月版本") },
            { "new_pkg_138", new SkuData("new_pkg_138", "船长礼包", "Captain Pack", "船長禮包", "Quà Thuyền Trưởng", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "327", "327", "20327", "", "232087", "", "ios.com.chillyroom.dungeonshooternew_pkg_138", "com.chillyroom.dungeonshooter.new_pkg_138", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010138.png", "7.4.0-2025七月版本") },
            { "new_pkg_139", new SkuData("new_pkg_139", "海神波塞冬礼包1", "Poseidon, King of the Sea Pack 1", "海神波塞冬禮包1", "Quà Thần Biển Poseidon 1", 1, 1, "25", "$4.99/¥30", "₽399/¥30", "₫124000/$4.99", "328", "328", "20328", "", "232088", "", "ios.com.chillyroom.dungeonshooternew_pkg_139", "com.chillyroom.dungeonshooter.new_pkg_139", "4637558555263124425/4637165942710777751", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010139.png", "7.4.0-2025七月版本") },
            { "new_pkg_140", new SkuData("new_pkg_140", "海神波塞冬礼包2", "Poseidon, King of the Sea Pack 2", "海神波塞冬禮包2", "Quà Thần Biển Poseidon 2", 1, 1, "30", "$5.99/¥36", "₽449/¥36", "₫149000/$5.99", "329", "329", "20329", "", "232089", "", "ios.com.chillyroom.dungeonshooternew_pkg_140", "com.chillyroom.dungeonshooter.new_pkg_140", "4637366503109523235/4635933743767826148", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010140.png", "7.4.0-2025七月版本") },
            { "new_pkg_141", new SkuData("new_pkg_141", "海神波塞冬礼包3", "Poseidon, King of the Sea Pack 3", "海神波塞冬禮包3", "Quà Thần Biển Poseidon 3", 1, 1, "20", "$3.99/¥22", "₽299/¥22", "₫99000/$3.99", "330", "330", "20330", "", "232090", "", "ios.com.chillyroom.dungeonshooternew_pkg_141", "com.chillyroom.dungeonshooter.new_pkg_141", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010141.png", "7.4.0-2025七月版本") },
            { "new_pkg_142", new SkuData("new_pkg_142", "学园偶像·歌穗礼包1", "Idol Isla Pack 1", "學園偶像·歌穗禮包1", "Quà Thần Tượng Học Đường-Isla 1", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "331", "331", "20331", "", "232091", "", "ios.com.chillyroom.dungeonshooternew_pkg_142", "com.chillyroom.dungeonshooter.new_pkg_142", "4635639827510787213/4634914334553984538", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010142.png", "7.4.0-2025七月版本") },
            { "new_pkg_143", new SkuData("new_pkg_143", "学园偶像·歌穗礼包2", "Idol Isla Pack 2", "學園偶像·歌穗禮包2", "Quà Thần Tượng Học Đường-Isla 2", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "332", "332", "20332", "", "232092", "", "ios.com.chillyroom.dungeonshooternew_pkg_143", "com.chillyroom.dungeonshooter.new_pkg_143", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010143.png", "7.4.0-2025七月版本") },
            { "new_pkg_146", new SkuData("new_pkg_146", "新角色合集礼包4", "New Characters Pack 4", "新角色合集禮包4", "Quà BST nhân vật mới 4", 1, 1, "25", "$4.99/¥30", "₽399/¥30", "₫124000/$4.99", "333", "333", "20333", "", "232093", "", "ios.com.chillyroom.dungeonshooternew_pkg_146", "com.chillyroom.dungeonshooter.new_pkg_146", "4637558555263124425/4637165942710777751", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010146.png", "7.4.0-2025七月版本") },
            { "fighter_skin_8", new SkuData("fighter_skin_8", "武斗家皮肤 - 炎之魔使·伊格尼丝", "Skin - Fighter - Pyropact Ignis", "武鬥家皮膚 - 炎之魔使·伊格尼絲", "", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "334", "334", "20334", "", "232094", "", "ios.com.chillyroom.dungeonshooter.fighter_skin_8", "com.chillyroom.dungeonshooter.fighter_skin_8", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/fighter_8_ui.png", "7.5.0-2025八月版本") },
            { "ninja_skin_1", new SkuData("ninja_skin_1", "超时空忍者皮肤 - 青空之翼·鸢", "Skin - Time Traveling Ninja - Skyveil Sylph", "超時空忍者皮膚 - 青空之翼·鳶", "", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "335", "335", "20335", "", "232095", "", "ios.com.chillyroom.dungeonshooter.ninja_skin_1", "com.chillyroom.dungeonshooter.ninja_skin_1", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/ninja_1_ui.png", "7.5.0-2025八月版本") },
            { "pastor_skin_24", new SkuData("pastor_skin_24", "牧师皮肤 - 校医室·云蓁", "Skin - Priestess - Clinic Cloudia", "牧師皮膚 - 校醫室·雲蓁", "", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "336", "336", "20336", "", "232096", "", "ios.com.chillyroom.dungeonshooter.pastor_skin_24", "com.chillyroom.dungeonshooter.pastor_skin_24", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/priest_24_ui.png", "7.5.0-2025八月版本") },
            { "paladin_skin_26", new SkuData("paladin_skin_26", "圣骑士皮肤 - 海焰战姬·希格", "Skin - Paladin - Flametide Sigrid", "聖騎士皮膚 - 海焰戰姬·希格", "", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "337", "337", "20337", "", "232097", "", "ios.com.chillyroom.dungeonshooter.paladin_skin_26", "com.chillyroom.dungeonshooter.paladin_skin_26", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/paladin_26_ui.png", "7.5.0-2025八月版本") },
            { "swordmaster_skin_9", new SkuData("swordmaster_skin_9", "剑宗皮肤 - 罪恶佳人·罗娜", "Skin - Sword Master - Hellbelle Lorna", "劍宗皮膚 - 罪惡佳人·羅娜", "", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "338", "338", "20338", "", "232098", "", "ios.com.chillyroom.dungeonshooter.swordmaster_skin_9", "com.chillyroom.dungeonshooter.swordmaster_skin_9", "4635639827510787213/4634914334553984538", "HeroSkin", "All", "Assets/RGTexture/ui/skin/swordmaster_9_ui.png", "7.5.0-2025八月版本") },
            { "new_pkg_150", new SkuData("new_pkg_150", "青空之翼·鸢礼包1", "Skyveil Sylph Pack 1", "青空之翼·鳶禮包1", "", 1, 1, "25", "$4.99/¥30", "₽399/¥30", "₫124000/$4.99", "339", "339", "20339", "", "232099", "", "ios.com.chillyroom.dungeonshooter.new_pkg_150", "com.chillyroom.dungeonshooter.new_pkg_150", "4637558555263124425/4637165942710777751", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010150.png", "7.5.0-2025八月版本") },
            { "new_pkg_151", new SkuData("new_pkg_151", "青空之翼·鸢礼包2", "Skyveil Sylph Pack 2", "青空之翼·鳶禮包2", "", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "340", "340", "20340", "", "232100", "", "ios.com.chillyroom.dungeonshooter.new_pkg_151", "com.chillyroom.dungeonshooter.new_pkg_151", "4635639827510787213/4634914334553984538", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010151.png", "7.5.0-2025八月版本") },
            { "new_pkg_152", new SkuData("new_pkg_152", "罪恶佳人·罗娜礼包1", "Hellbelle Lorna Pack 1", "罪惡佳人·羅娜禮包1", "", 1, 1, "30", "$5.99/¥36", "₽449/¥36", "₫149000/$5.99", "341", "341", "20341", "", "232101", "", "ios.com.chillyroom.dungeonshooter.new_pkg_152", "com.chillyroom.dungeonshooter.new_pkg_152", "4637366503109523235/4635933743767826148", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010152.png", "7.5.0-2025八月版本") },
            { "new_pkg_153", new SkuData("new_pkg_153", "罪恶佳人·罗娜礼包2", "Hellbelle Lorna Pack 2", "罪惡佳人·羅娜禮包2", "", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "342", "342", "20342", "", "232102", "", "ios.com.chillyroom.dungeonshooter.new_pkg_153", "com.chillyroom.dungeonshooter.new_pkg_153", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010153.png", "7.5.0-2025八月版本") },
            { "new_pkg_154", new SkuData("new_pkg_154", "罪恶佳人·罗娜礼包3", "Hellbelle Lorna Pack 3", "罪惡佳人·羅娜禮包3", "", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "343", "343", "20343", "", "232103", "", "ios.com.chillyroom.dungeonshooter.new_pkg_154", "com.chillyroom.dungeonshooter.new_pkg_154", "4635639827510787213/4634914334553984538", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010154.png", "7.5.0-2025八月版本") },
            { "new_pkg_155", new SkuData("new_pkg_155", "校医室·云蓁礼包1", "Clinic Cloudia Pack 1", "校醫室·雲蓁禮包1", "", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "344", "344", "20344", "", "232104", "", "ios.com.chillyroom.dungeonshooter.new_pkg_155", "com.chillyroom.dungeonshooter.new_pkg_155", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010155.png", "7.5.0-2025八月版本") },
            { "new_pkg_156", new SkuData("new_pkg_156", "校医室·云蓁礼包2", "Clinic Cloudia Pack 2", "校醫室·雲蓁禮包2", "", 1, 1, "15", "$2.99/¥18", "₽249/¥18", "₫74000/$2.99", "345", "345", "20345", "", "232105", "", "ios.com.chillyroom.dungeonshooter.new_pkg_156", "com.chillyroom.dungeonshooter.new_pkg_156", "4635639827510787213/4634914334553984538", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010156.png", "7.5.0-2025八月版本") },
            { "new_pkg_159", new SkuData("new_pkg_159", "海焰战姬·希格礼包1", "Flametide Sigrid Pack 1", "海焰戰姬·希格禮包1", "", 1, 1, "25", "$4.99/¥30", "₽399/¥30", "₫124000/$4.99", "346", "346", "20346", "", "232106", "", "ios.com.chillyroom.dungeonshooter.new_pkg_159", "com.chillyroom.dungeonshooter.new_pkg_159", "4637558555263124425/4637165942710777751", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010159.png", "7.5.0-2025八月版本") },
            { "new_pkg_160", new SkuData("new_pkg_160", "海焰战姬·希格礼包2", "Flametide Sigrid Pack 2", "海焰戰姬·希格禮包2", "", 1, 1, "20", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "347", "347", "20347", "", "232107", "", "ios.com.chillyroom.dungeonshooter.new_pkg_160", "com.chillyroom.dungeonshooter.new_pkg_160", "4637121953801084017/4636472375067537543", "Bundle", "All", "Assets/RGTexture/mall/bundle/bundle_7010160.png", "7.5.0-2025八月版本") },
            { "skill_shooter_1", new SkuData("skill_shooter_1", "枪手二技能 - 狩猎领域", "Skill - Gunner - Deadeye Domain", "槍手二技能 - 狩獵領域", "", 1, 1, "10", "$1.99/¥12", "₽149/¥12", "₫49000/$1.99", "348", "348", "20348", "", "232108", "", "ios.com.chillyroom.dungeonshooter.skill_shooter_1", "com.chillyroom.dungeonshooter.skill_shooter_1", "*******************/*******************", "HeroSkill", "All", "Assets/RGTexture/ui/common/shooter_skillIcon_1.png", "7.5.0-2025八月版本") }
        };
        public static readonly Dictionary<string, SkuData> SpecialData = new Dictionary<string, SkuData> {
            { "purchase_gem_4", new SkuData("purchase_gem_4", "35000 宝石", "Gem - 35000", "", "Đá - 35000", 35000, 0, "0", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "", "", "", "", "", "", "", "com.chillyroom.dungeonshooter.purchase_gem_4", "4637121953801084017/4636472375067537543", "Gem", "GP", "Assets/RGTexture/ui/common/shop_gem.png#shop_gem_3", "") },
            { "purchase_gem_5", new SkuData("purchase_gem_5", "35000 宝石", "Gem - 35000", "35000 寶石", "Đá - 35000", 35000, 0, "0", "$3.99/¥25", "₽349/¥25", "₫99000/$3.99", "", "", "", "", "", "", "ios.com.chillyroom.dungeonshooter.purchase_gem_5", "com.chillyroom.dungeonshooter.purchase_gem_5", "", "Gem", "iOS", "Assets/RGTexture/ui/common/shop_gem.png#shop_gem_3", "") },
            { "new_pkg_0", new SkuData("new_pkg_0", "入门角色皮肤礼包", "Starter Character & Skin Pack", "", "Quà Skin Nhân Vật Nhập Môn", 1, 1, "5", "$0.99/¥6", "₽79/¥6", "₫25000/$0.99", "", "", "", "", "", "", "ios.com.chillyroom.dungeonshooter.new_player_pack_0", "com.chillyroom.dungeonshooter.new_player_pack_0", "", "Bundle", "iOS", "", "") }
        };
    }
}
