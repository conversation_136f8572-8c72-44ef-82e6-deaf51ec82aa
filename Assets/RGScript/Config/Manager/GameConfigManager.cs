using ChillyRoom.Services.Accounts.Utils;
using ChillyRoom.Utils;
using Newtonsoft.Json;
using System.Diagnostics.CodeAnalysis;
using System;
using System.IO;
using System.Text;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

#if UNITY_ANDROID
using RGScript.Util.AndroidDialog;
#endif

namespace RGScript.Config.Manager {
    public class GameConfigManager {
        private static GameConfigManager _instance;
        
        public GameConfig Config { get; private set; }
        public const string UseDebugConfigInEditorKey = "useDebugConfigInEditor";
        public const string OpenDebugConsoleKey = "OpenDebugConsole";

        public static string SecretKey => XORCrypt.Instance.Decrypt("*1\"8ku|", "-PS`2AWO&");

        private static string ConfigPath => Path.Combine(Application.streamingAssetsPath, "game_config");
        
#if UNITY_EDITOR
        private static bool _useDebugConfigInEditor;
        private static bool _openDebugConsole;
        private readonly GameConfig _debugGameConfig;
        private readonly GameConfig _releaseGameConfig;

        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSceneLoad)]
        public static void InitCache() {
            _useDebugConfigInEditor = EditorPrefs.GetBool(UseDebugConfigInEditorKey, false);
            _openDebugConsole = EditorPrefs.GetBool(OpenDebugConsoleKey, false);
        }

        [SuppressMessage("ReSharper", "StringLiteralTypo")]
        private GameConfigManager() {
            _debugGameConfig = new GameConfig {
                RemoteOnlineService = "api.soulknight.test.chilly.tech",
                AccountService = "161.189.123.149:7030",
                RealNameService = "aas.chillyroom.com:443",
                ConfigService = "http://cdn.chillyroom.com/game-test/",
                CloudSaveService = "127.0.0.1", // UnUsed
                IsOpenLog = false,
                IsOpenLua = false,
                PatchService = "https://cdn.chillyroom.com/game/soulknight/patches_test",
                IsDebug = true,
                AccountLogOffService = "http://accounts.chilly.room/logoff-account",
                OssLogService = "http://192.168.4.52:3000/file/SoulKnightFileUpload",
                ImServiceBrokerEndpoint = "192.168.1.210",
                BlobSaveEndpoint = "soulknight.blobsave.test.chilly.tech",
            };

            _releaseGameConfig = new GameConfig {
                RemoteOnlineService = "api-gateway.soulknight.chillyroom.com",
                AccountService = "",
                RealNameService = "aas.chillyroom.com:443",
                ConfigService = "http://cdn.chillyroom.com/game/",
                CloudSaveService = "127.0.0.1", // UnUsed
                IsOpenLog = false,
                IsOpenLua = false,
                PatchService = "https://cdn.chillyroom.com/game/soulknight/patches",
                IsDebug = false,
                AccountLogOffService = "https://accounts.chillyroom.com/logoff-account",
                OssLogService = "https://logs.dev.chillyroom.com/file/SoulKnightFileUpload",
                ImServiceBrokerEndpoint = "live.prod.chillyroom.com",
                BlobSaveEndpoint = "save.soulknight.chillyroom.com",
            };
        }

        private void SaveDebugConfig() {
            Config = _debugGameConfig;
            SaveGameConfig();
            
            Debug.Log("Saved debug game config.");
        }
        
        public void SaveReleaseConfig() {
            Config = _releaseGameConfig;
            SaveGameConfig();
            
            Debug.Log("Saved release game config.");
        }

        private void SaveGameConfig() {
            var json = JsonConvert.SerializeObject(Config);
            var cypher = DESCrypt.Instance.Encrypt(json, SecretKey);
            File.WriteAllText(ConfigPath, cypher);
        }
#endif

        private void LoadGameConfig() {
            var cypher = Abo.FileUtil.ReadFile(ConfigPath);
            var json = DESCrypt.Instance.Decrypt(cypher, SecretKey);
            Config = JsonConvert.DeserializeObject<GameConfig>(json);
        }
        
        private void CheckConfig() {
            if (!Config.IsDebug) {
                return;
            }
            
            var stringBuilder = new StringBuilder();
            stringBuilder.AppendLine($"RemoteOnlineService: {Config.RemoteOnlineService}");
            stringBuilder.AppendLine($"AccountService: {Config.AccountService}");
            stringBuilder.AppendLine($"RealNameService: {Config.RealNameService}");
            stringBuilder.AppendLine($"ConfigService: {Config.ConfigService}");
            stringBuilder.AppendLine($"PatchService: {Config.PatchService}");
            
          #if UNITY_EDITOR
            Debug.Log($"GameConfig: <color=#3DE25AFF>{stringBuilder}</color>");
            #endif
#if UNITY_ANDROID
            stringBuilder.ToString().showAsToast();
#endif
        }

        private void InitConfig() {
            try {
#if UNITY_EDITOR
            if (_useDebugConfigInEditor) {
                SaveDebugConfig();
            } else {
                SaveReleaseConfig();
            }
#endif
            LoadGameConfig();
            CheckConfig();
            
#if UNITY_EDITOR
            Config.IsOpenLog = _openDebugConsole;
#endif
                
            } catch (Exception e) {
                Debug.LogError("InitConfig" + e);
            }
        }

        public static GameConfigManager GetInstance() {
            _instance ??= new GameConfigManager();
            if (_instance.Config == null) {
                _instance.InitConfig();
            }
            
            return _instance;
        }
    }
}
