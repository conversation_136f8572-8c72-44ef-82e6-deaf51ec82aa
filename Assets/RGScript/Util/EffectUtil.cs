// unset

using UnityEngine;

namespace RGScript.Util
{
    public static class EffectUtil {
        public static GameObject ShowPrefabEffectLocPos(PrefabName prefabEffect, Transform parent, Vector3 locPos) { 
            return Object.Instantiate(PrefabManager.GetPrefab(prefabEffect), parent.position + locPos, Quaternion.identity, parent);
        }

        public static GameObject ShowPrefabEffectWorldPos(PrefabName prefabEffect, Transform target) {
            return ShowPrefabEffectWorldPos(prefabEffect, target, Vector3.zero);
        }

        public static GameObject ShowPrefabEffectWorldPos(PrefabName prefabEffect, Transform target, Vector3 offset) {
            if (null == target) {
                return null;
            }
            return Object.Instantiate(PrefabManager.GetPrefab(prefabEffect), target.position + offset, Quaternion.identity);
        }
    
    
    }
}