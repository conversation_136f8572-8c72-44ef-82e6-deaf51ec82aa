
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 刷新挑战因子，游戏模式栏
/// </summary>
public class UpdateFactorBarEvent : EventBase { }

public class UpdateRoomIdEvent : EventBase { }

public class ShowTempTipsEvent : EventBase {
    public string key;
    public float showTime = 3f;
    public bool needLocalized = true;
}

public class UpdateSelectHeroCDTimeEvent : EventBase {
    public int restTime;
}

public struct NetPlayerScreenInfo {
    public bool valid;
    public int hero_index;
    public int skin_index;
    public int skill_index;
    public int rank;
    public bool ready;
}

public class UpdateMultiRoomSkinEvent : EventBase {
    public NetPlayerScreenInfo[] infos;
}

public class UIBanFactorEvent : EventBase {
    public emBattleFactor Factor;
    public Transform target;
    public float offset;
}

public class StaticBtnRetryClickEvent : EventBase {
    
}

// 依赖于ReloadAllData后执行逻辑的，可以监听此事件
public class AfterReloadDataEvent : EventBase {
}
