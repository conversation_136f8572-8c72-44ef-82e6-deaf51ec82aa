using Newtonsoft.Json;
using RGScript.Util.ForcePatch;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using UnityEngine;
using FileUtil = Abo.FileUtil;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace RGScript.Util.HotUpdate {
    public static class HotUpdateManager {
        private static readonly Dictionary<string, string> PlatformAndBuildTargetDict = new() {
            { nameof(RuntimePlatform.OSXEditor), "StandaloneOSX" },
            { nameof(RuntimePlatform.WindowsEditor), "StandaloneWindows64" },
            { nameof(RuntimePlatform.IPhonePlayer), "iOS" },
            { nameof(RuntimePlatform.Android), "Android" }
        };
        
        private const string RemoteUrl = "http://cdn.chillyroom.com/game/soulknight/hot_update";
        private static readonly string HotUpdateDir =
            $"{FileUtil.InternalPersistentPath}/hot_update".Replace('\\', '/');
        private const string HotUpdateFileDirName = "data";
        private static readonly string FilesDir = $"{HotUpdateDir}/{HotUpdateFileDirName}";
        private static readonly string ManifestPath = $"{FilesDir}/{ManifestName}";

        private static HotUpdateManifest _currentManifest;
        
        public const string ManifestName = "hot_update_manifest.json";
        public static string Version => CurrentBundleVersion.currentBundleVersion.version;

        private static async Task<string> DownloadFile(
            string remotePath, string downloadPath, ForcePatchManager.DownloadEventHandler handler = null) {
            var url = $"{RemoteUrl}/{remotePath}";
            if (!await ForcePatchManager.FileExistsAsync(url)) {
                return string.Empty;
            }
            
            var filePath = $"{HotUpdateDir}/{downloadPath}";
            try {
                string directoryPath = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directoryPath) && !Directory.Exists(directoryPath)) {
                    Directory.CreateDirectory(directoryPath);
                }
            
                var ret = await ForcePatchManager.Download(
                    url, filePath, false, null, handler);
                if (ret) {
                    return await File.ReadAllTextAsync(filePath);
                }
            } catch (Exception e) {
                Debug.LogError(e);
            }

            return string.Empty;
        }
        
        private static async Task<string> DownloadFile(string relativePath) {
            return await DownloadFile(relativePath, relativePath);
        }
        
        public static string GetAssetBundleFullPath(string relativePath) {
            return $"{Application.streamingAssetsPath}/AssetBundles/{relativePath}";
        }

        // Oss path
        public static string GetDirectoryRelativePath(bool isDebug, string platform) {
            if (string.IsNullOrEmpty(platform) || string.IsNullOrEmpty(Version)) {
                return string.Empty;
            }
            
            var environment = isDebug ? "debug" : "release";
            return $"{environment}/{Version}/{platform}";
        }
        
        // Oss path
        // ReSharper disable once MemberCanBePrivate.Global
        public static string GetFileDownloadRelativePath(bool isDebug, string platform, string filePath) {
            var directoryRelativePath = GetDirectoryRelativePath(isDebug, platform);
            return string.IsNullOrEmpty(directoryRelativePath) ? string.Empty : $"{directoryRelativePath}/{filePath}";
        }

        public static string GetMd5Path(HotUpdateFileInfo fileInfo) {
            var path = fileInfo.path;
            var md5 = fileInfo.md5;
            return $"{path.Replace(".ab", "")}_{md5}.ab";
        }
        
        public static async Task<HotUpdateManifest> DownloadRemoteConfig(string buildTarget) {
            var relativePath = $"remote_configs/{Version}/{buildTarget}/{ManifestName}";
            var data = await DownloadFile(relativePath);
            try {
                return JsonConvert.DeserializeObject<HotUpdateManifest>(data);
            } catch (Exception) {
                // ignored
            }

            return null;
        }

        private static HotUpdateManifest GetManifest() {
            try {
                if (!File.Exists(ManifestPath)) {
                    return null;
                }
                
                var manifestData = File.ReadAllText(ManifestPath);
                if (string.IsNullOrEmpty(manifestData)) {
                    return null;
                }
                
                var manifest = JsonConvert.DeserializeObject<HotUpdateManifest>(manifestData);
                if (manifest == null) {
                    throw new Exception("Invalid manifest file");
                }

                return manifest;
            } catch (Exception e) {
                Debug.LogError(e);
            }

            return null;
        }
        
        public static void DeleteExpiredFiles() {
            try {
                var manifest = GetManifest();
                if (manifest == null) {
                    return;
                }
                
                if (manifest.version != Version) {
                    // Expired and delete dir
                    Directory.Delete(FilesDir, true);
                }
            } catch (Exception e) {
                Debug.LogError(e);
            }
        }
        
        // If downloadPath == "", fetch manifest for editor
        public static async Task<HotUpdateManifest> FetchManifest(
            bool isDebug, string platform, string downloadPath = "") {
            var relativePath = GetFileDownloadRelativePath(isDebug, platform, ManifestName);
            var data = string.IsNullOrEmpty(downloadPath) ?
                await DownloadFile(relativePath) : await DownloadFile(relativePath, downloadPath);
            if (string.IsNullOrEmpty(data)) {
                // Download failed
                return null;
            }
            
            try {
                return JsonConvert.DeserializeObject<HotUpdateManifest>(data);
            } catch (Exception) {
                // ignored
            }

            return null;
        }
        
        // Download manifest for runtime
        public static async Task<HotUpdateManifest> DownloadManifest(bool isDebug, string source) {
            var downloadPath = $"{HotUpdateFileDirName}/{ManifestName}";
            if (PlatformAndBuildTargetDict.TryGetValue(source, out var buildTarget)) {
                return await FetchManifest(isDebug, buildTarget, downloadPath);
            }
            return await FetchManifest(isDebug, source, downloadPath);
        }

        private static void DeleteUnusedFiles(HotUpdateManifest manifest) {
            try {
                // Delete unused file
                var files = Directory.GetFiles(FilesDir, "*", SearchOption.AllDirectories);
                foreach (var file in files) {
                    var path = file.Replace('\\', '/');
                    var relativePath = path.Replace($"{FilesDir}/", string.Empty);
                    if (relativePath == ManifestName) {
                        continue;
                    }
                
                    var underscoreIndex = relativePath.LastIndexOf('_');
                    if (underscoreIndex == -1) {
                        // Not a correct file name
                        File.Delete(file);
                        continue;
                    }
                    
                    relativePath = relativePath[..underscoreIndex] + ".ab";
                    if (!manifest.TryGetFileInfo(relativePath, out _)) {
                        File.Delete(file);
                    }
                }
            } catch (Exception e) {
                Debug.LogError(e);
            }
        }
        
        private static void DeleteEmptyDirectories(string dir) {
            try {
                if (!Directory.Exists(dir)) {
                    return;
                }

                foreach (var subDir in Directory.GetDirectories(dir)) {
                    DeleteEmptyDirectories(subDir);
                }

                if (Directory.GetFiles(dir).Length == 0 &&
                    Directory.GetDirectories(dir).Length == 0) {
                    Directory.Delete(dir);
                }
            } catch (Exception e) {
                Debug.LogError(e);
            }
        }

        private static async Task SendStartDownloadForcePatchEvent(
            HotUpdateManifest manifest, List<HotUpdateFileInfo> filesToDownload) {
            var fileNames = new List<string>();
            var totalSize = 0L;
            foreach (var fileInfo in filesToDownload) {
                fileNames.Add(fileInfo.path);
                var md5Path = GetMd5Path(fileInfo);
                var relativePath = GetFileDownloadRelativePath(manifest.isDebug, manifest.platform, md5Path);
                totalSize += await ForcePatchManager.FileSizeAsync($"{RemoteUrl}/{relativePath}");
            }
            
            SimpleEventManager.Raise(new StartDownloadForcePatchEvent {
                fileNames = fileNames,
                totalSize = (int)totalSize
            });
        }

        public static async Task DownloadByManifest(HotUpdateManifest manifest) {
            _currentManifest = manifest;
            
            if (!Directory.Exists(FilesDir)) {
                // Make dir
                Directory.CreateDirectory(FilesDir);
            }

            // Delete unused file
            DeleteUnusedFiles(manifest);
            
            // Delete empty dir
            DeleteEmptyDirectories(FilesDir);

            // Add file info to a download list
            var filesToDownload = new List<HotUpdateFileInfo>();
            foreach (var fileInfo in manifest.fileInfoList) {
                var path = $"{FilesDir}/{GetMd5Path(fileInfo)}";
                if (!File.Exists(path)) {
                    // Not download yet
                    filesToDownload.Add(fileInfo);
                    continue;
                }

                var md5 = NetUtility.GetMD5HashFromFile(path);
                if (md5 != fileInfo.md5) {
                    // Expired file
                    filesToDownload.Add(fileInfo);
                }
            }

            if (filesToDownload.Count == 0) {
                return;
            }
            
            await SendStartDownloadForcePatchEvent(manifest, filesToDownload);

            // Download files
            foreach (var fileInfo in filesToDownload) {
                var filePath = GetMd5Path(fileInfo);
                var relativePath = GetFileDownloadRelativePath(manifest.isDebug, manifest.platform, filePath);
                var downloadPath = $"{HotUpdateFileDirName}/{filePath}";
                var data = await DownloadFile(
                    relativePath, downloadPath, (_, timespan, fileSize) => {
                        SimpleEventManager.Raise(new UpdateDownloadForcePatchEvent {
                            fileName = fileInfo.path,
                            fileSize = fileSize,
                            timeSpan = timespan
                        });
                    });
                
                if (string.IsNullOrEmpty(data)) {
                    throw new Exception($"Hot update download empty file {filePath}");
                }

                // Check file integrity
                var path = $"{FilesDir}/{filePath}";
                var md5 = NetUtility.GetMD5HashFromFile(path);
                if (md5 != fileInfo.md5) {
                    throw new Exception($"Hot update download file md5 incorrect {filePath} {md5}");
                }
            }
        }

        public static string GetBundleHotUpdatePath(string bundleName) {
            bundleName += ".ab";
            if (_currentManifest == null || !_currentManifest.TryGetFileInfo(bundleName, out var info)) {
                return string.Empty;
            }

            var pathWithMd5 = GetMd5Path(info);
            return $"{FilesDir}/{pathWithMd5}";
        }

#if UNITY_EDITOR
        public static bool IsOpenHotUpdateInEditor() {
            return EditorPrefs.GetBool("OpenHotUpdate", false);
        }
#endif
    }
}