using UnityEngine;

namespace BulletPattern {
    public class EExtraBullet : MonoBehaviour {
        public BulletInfoInspector bulletInfo;
        public Transform releasePositionTransform;
        public void ReleaseBullet(RGEController controller) {
            var releaseAngle = controller.facing > 0 ? controller.fixed_angle : 180 - controller.fixed_angle;
            BulletFactory.TakeBullet(
                bulletInfo.GetBulletInfo().SetSourceObject(controller.gameObject).SetCamp(controller.camp)
                    .SetPosition(releasePositionTransform.position).SetAngle(releaseAngle),
                bulletInfo.GetDamageInfo().SetCamp(controller.camp)
            );
        }
    }
}
