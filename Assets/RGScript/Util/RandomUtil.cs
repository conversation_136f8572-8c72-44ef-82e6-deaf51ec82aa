using Sirenix.Utilities;
using SoulKnight.Runtime.Config2Code.Config;
using SoulKnight.Runtime.Enemy;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Object = UnityEngine.Object;
using Random = UnityEngine.Random;

public static class RandomUtil {
	/// <summary>
	/// 随机获取一个对象, 字典中的int表权重. 必定能够返回一个值(除非字典为空)
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="distribution"></param>
	/// <param name="rgRandom"></param>
	/// <returns></returns>
	public static T GetRandomObject<T>(this Dictionary<T, int> distribution, RGRandom rgRandom) {
		int total = 0;
		foreach (var pair in distribution) {
			total += pair.Value;
		}
		int ran = rgRandom.Range(0, total);
		total = 0;
		foreach (var pair in distribution) {
			total += pair.Value;
			if (ran < total) {
				return pair.Key;
			}
		}
		return default;
	}
	public static T GetRandomObject<T>(this List<T> distribution, RGRandom rgRandom) {
        if (distribution.Count <= 0) {
            return default;
        }

        return rgRandom == null
            ? distribution[Random.Range(0, distribution.Count)]
            : distribution[rgRandom.Range(0, distribution.Count)];
    }
	public static T GetRandomObject<T>(this T[] distribution, RGRandom rgRandom) {
        if (distribution.Length <= 0) {
            return default;
        }
        return rgRandom == null ? 
            distribution[Random.Range(0, distribution.Length)] : distribution[rgRandom.Range(0, distribution.Length)];
	}
    public static T GetRandomWeightObject<T>(this IDictionary<T,int> distribution, RGRandom rgRandom) {
        return GetRandomWeightObject(distribution.Keys,distribution.Values, rgRandom);
    }
    /// <summary>
    /// 根据权重随机取值
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="distribution"></param>
    /// <param name="weight"></param>
    /// <param name="rgRandom"></param>
    /// <returns></returns>
    public static T GetRandomWeightObject<T>(this IEnumerable<T> distribution, IEnumerable<int> weight, RGRandom rgRandom) {
        int count = distribution.Count();
        if (count <= 0) {
            return default;
        }

        int index = GetRandomWeightIndex(weight, rgRandom);
        index = ValidValue(index, count);
        return distribution.ElementAt(index);
    }
    
    /// <summary>
    /// 根据权重随机取值
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="distribution"></param>
    /// <param name="weight"></param>
    /// <param name="rgRandom"></param>
    /// <returns></returns>
    public static int GetRandomWeightObjectIndex<T>(this IEnumerable<T> distribution, IEnumerable<int> weight, RGRandom rgRandom) {
        int count = distribution.Count();
        if (count <= 0) {
            return -1;
        }

        int index = GetRandomWeightIndex(weight, rgRandom);
        index = ValidValue(index, count);
        return index;
    }
    
    public static int GetRandomWeightObjectIndex<T>(this IEnumerable<T> distribution, IEnumerable<int> weight, Func<int, int, int> randomFunc) {
        int count = distribution.Count();
        if (count <= 0) {
            return -1;
        }
        int index = GetRandomWeightIndex(weight, randomFunc);
        index = ValidValue(index, count);
        return index;
    }

    public static T GetRandomObject<T>(this IEnumerable<T> distribution) {
        var array = distribution.ToArray();
        return array.IsNullOrEmpty() ? default : array[Random.Range(0, array.Length)];
    }

    public static List<T> GetRandomElementsDistinct<T>(this IEnumerable<T> distribution, int count, RGRandom random = null) {
        var list = distribution.ToList();
        if (list.Count == 0) {
            return new List<T>();
        }

        var result = new List<T>();
        for (int i = 0; i < count; i++) {
            var index = random?.Range(0, list.Count) ?? Random.Range(0, list.Count);
            result.Add(list[index]);
            list.RemoveAt(index);
        }

        return result;
    }

    public static T GetRandomObject<T>(this IEnumerable<T> distribution, RGRandom rgRandom) {
        var array = distribution.ToArray();
        if (array.Length == 0) {
            return default;
        }
        int index = rgRandom?.Range(0, array.Length) ?? Random.Range(0, array.Length);
        return array[index];
    }

    public static T GetRandomWeightObject<T>(this IEnumerable<T> distribution, RGRandom rgRandom) where T:IWeightObject {
	    var totalWeight = distribution.Aggregate(0, (total, current) => total + current.Weight);
	    var targetWeight=0;
	    var index = 0;
	    if (distribution.Count() > 0) {
            targetWeight = rgRandom?.Range(0, totalWeight) ?? Random.Range(0, totalWeight);
        }
	    foreach (var item in distribution) {
		    targetWeight -= item.Weight;
		    if (targetWeight < 0) {
			    return distribution.ElementAt(index);
		    }
		    index++;
	    }
	    return distribution.FirstOrDefault();
    }

    public static T GetRandomWeightObject<T>(this Dictionary<T, int> distribution, RGRandom rgRandom) where T : IWeightObject {
        if (distribution == null || distribution.Count == 0) {
            throw new ArgumentException("Distribution cannot be null or empty.");
        }
        if (distribution.Any(kvp => kvp.Value < 0)) {
            throw new ArgumentException("All weights must be non-negative.");
        }
        
        var totalWeight = distribution.Sum(kvp => kvp.Value);
        if (totalWeight == 0) {
            return distribution.Keys.GetRandomObject(rgRandom);
        }
        
        var targetWeight = rgRandom?.Range(0, totalWeight) ?? Random.Range(0, totalWeight);
        foreach (var item in distribution) {
            targetWeight -= item.Value;
            if (targetWeight < 0) {
                return item.Key;
            }
        }
        throw new InvalidOperationException("Unexpected logic error in GetRandomWeightObject.");
    }

    public static int ValidValue(int index,int count) {
        index = Mathf.Max(index, 0);
        index = Mathf.Min(index, count - 1);
        return index;
    }
    public static int GetRandomWeightIndex(IEnumerable<int> weights, RGRandom random) {
        return GetRandomWeightIndex(weights, random != null ? random.Range : null);
    }

    public static int GetRandomWeightIndex(IEnumerable<int> weights, Func<int, int, int> randomFunc) {
        int targetWeight = 0;
        int index = 0;
        var enumerable = weights as int[] ?? weights.ToArray();
        if (enumerable.Any()) {
            targetWeight = randomFunc?.Invoke(0, enumerable.Sum()) ?? Random.Range(0, enumerable.Sum());
        }

        foreach (var item in enumerable) {
            targetWeight -= item;
            if (targetWeight < 0 && item != 0) {
                return index;
            }

            index++;
        }

        return 0;
    }

	/// <summary>
	/// 随机获取一个对象, 字典中的float表示真实概率(取值0~1), 因此有可能返回空对象
	/// </summary>
	/// <returns></returns>
	public static T GetRandomObject<T>(this Dictionary<T, float> distribution, RGRandom rgRandom) where T : Object {
		float total = 0;
		float ran = rgRandom.Range(0f, 1f);
		foreach (var pair in distribution) {
			total += pair.Value;
			if (ran < total) {
				return pair.Key;
			}
		}
		return null;
	}

    public static bool GuaranteeDrop(string enemyName, string dropItemName) {
        var enemyData = EnemyTable.GetEnemyData(enemyName);
        if (enemyData == null) {
            return false;
        }

        EnemyDropInfo dropInfo = null;
        foreach (var enemyDropInfo in enemyData.Drops.Where(
                     enemyDropInfo => enemyDropInfo.item.key == dropItemName)) {
            dropInfo = enemyDropInfo;
        }
        if (dropInfo is not { guaranteeDrop: true }) {
            return false;
        }

        var certainDropCount = dropInfo.guaranteeDropCount;
        if (certainDropCount < 1) {
            return false;
        }
        
        string key = $"{enemyName}_{dropItemName}";
        var guaranteeDropCount = StatisticData.data.GetGuaranteeDropCount(key);
        if (++guaranteeDropCount >= certainDropCount) {
            // Drop and remove data
            StatisticData.data.SetGuaranteeDropCount(key, 0);
            return true;
        }
        
        StatisticData.data.SetGuaranteeDropCount(key, guaranteeDropCount);
        return false;
    }
    
	// 随机获取多个对象, 字典中的float表示真实概率, 大于1则可能有多个, 有可能返回空数组
    public static List<T> GetRandomObjects<T>(
        this Dictionary<T, float> distribution, string enemyName, RGRandom random) where T : Object {
		List<T> objects = new List<T>();
		foreach (var pair in distribution) {
			if (pair.Value < 1) {
                if (GuaranteeDrop(enemyName, pair.Key.name)) {
                    objects.Add(pair.Key);
                } else {
                    if (random.Range(0.0f, 1.0f) >= pair.Value) {
                        continue;
                    }

                    string guaranteeDropKey = $"{enemyName}_{pair.Key.name}";
                    StatisticData.data.SetGuaranteeDropCount(guaranteeDropKey, 0);
                    objects.Add(pair.Key);
                }
            } else {
                var count = Mathf.RoundToInt(random.Range(1.0f, pair.Value));
				for (int i = 0; i < count; i++) {
					objects.Add(pair.Key);
				}
			}
		}
        
		return objects;
	}
	
	/// <summary>
	/// 根据时间获取随机数种子
	/// </summary>
	/// <returns></returns>
	public static int GetRandomSeedCurTime() {
		return (int) System.DateTime.Now.Ticks + Random.Range(int.MinValue, int.MaxValue);
	}
	
	/// <summary>
	/// 获取一个新的随机数
	/// </summary>
	/// <returns></returns>
	public static RGRandom GetNewRandomWithCurTimeSeed() {
		RGRandom ran = new RGRandom();
		ran.SetRandomSeed(GetRandomSeedCurTime());
		return ran;
	}

    private static List<emStatueBuff> statueBuffList = null;
    public static int GetRandomStatue(RGRandom random) {
        if (null == random) {
            return 1;
        }
        int rndStatue = random.Range((int)emStatueBuff.Wizard, (int)emStatueBuff.Count);
        return rndStatue;
    }

    public static void ShuffleList<T>(List<T> list, RGRandom rgRandom) {
        for (var i = 0; i < list.Count; ++i) {
            var r = rgRandom?.Range(0, 1.0f) ?? Random.Range(0, 1.0f);
            var idx = Mathf.Clamp((int)(r * list.Count), 0, list.Count - 1);
            (list[idx], list[i]) = (list[i], list[idx]);
        }
    }

    public static int Range(int min, int max, RGRandom rgRandom = null) {
        return rgRandom?.Range(min, max) ?? Random.Range(min, max);
    }
    
    private static readonly System.Random StrRnd = new System.Random();
    public static string RandomString(int length) {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        return new string(Enumerable.Repeat(chars, length)
            .Select(s => s[StrRnd.Next(s.Length)]).ToArray());
    }

    public static bool RandomShoot(this RGRandom rgRandom, int min, int max, int weight) {
        return rgRandom.Range(min, max) < weight;
    }
    
    public static Vector2 RandomOffset(this Vector2 v, float range) {
        return v += (Quaternion.Euler(0, 0, Random.Range(0, 360f)) * Vector2.right * Random.Range(0, range)).Vec2();
    }

    public static Vector2 RandomOffset(this Vector2 v, float minRange, float maxRange) {
        return v += (Quaternion.Euler(0, 0, Random.Range(0, 360f)) * Vector2.right * Random.Range(minRange, maxRange)).Vec2();
    }

    public static Vector2 RandomOffset(this Vector3 v, float range) {
        return v.Vec2().RandomOffset(range);
    }

    public static Vector2 RandomOffset(this Vector3 v, float minRange, float maxRange) {
        return v.Vec2().RandomOffset(minRange, maxRange);
    }
    
    public static IEnumerable<T> Shuffle<T>(this IEnumerable<T> source, RGRandom random = null) {
        return random != null ? source.OrderBy(_ => random.Range(0f, 1f)) : source.OrderBy(_ => Random.value);
    }
    
	public static long GetRandomPositiveCurTime() {
        return DateTime.Now.Ticks + Random.Range(0, int.MaxValue);
    }
}

public interface IWeightObject {
	int Weight { get; }
}
