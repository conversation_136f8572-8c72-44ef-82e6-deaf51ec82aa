using DG.Tweening;
using Sirenix.OdinInspector;
using UnityEngine;

namespace RGScript.Util {
    public class LeaveFall : MonoBehaviour {
        public SpriteAnimation leavesFall;
        public bool isShake;

        [Button]
        private void OnTriggerEnter2D(Collider2D other) {
            transform.DOKill(transform);

            leavesFall.Play();
            if (isShake) {
                transform.DOShakeScale(0.6f, 0.3f);
            }
        }
    }
}