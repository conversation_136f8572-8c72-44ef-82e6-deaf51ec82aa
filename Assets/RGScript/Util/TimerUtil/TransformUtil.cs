using UnityEngine;

namespace RGScript.Util.TimerUtil {
    public static class TransformUtil {
        public static float UpdateMovePosition(this Transform self, float deltaTime, Vector2 targetPosition,
            float speed) {
            var disp = targetPosition - (Vector2)self.position;
            var dir = disp.normalized;
            var dist = Mathf.Min(disp.magnitude, speed * deltaTime);
            self.position += (Vector3)dir * dist;
            return dist;
        }
    }
}