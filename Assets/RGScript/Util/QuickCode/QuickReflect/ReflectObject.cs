using System;
using System.Reflection;

namespace MycroftToolkit.QuickCode {
 public class ReflectObject {
        private readonly object _obj;
        public Type ObjectType { get; }

        public ReflectObject(string fullName, object[] parameters = null) {
            _obj = QuickReflect.CreateInstance<object>(fullName, parameters);
            ObjectType = _obj.GetType();
        }
        
        public ReflectObject(string fullName,AssemblyName assemblyName, object[] parameters = null) {
            _obj = QuickReflect.CreateInstance<object>(fullName,assemblyName, parameters);
            ObjectType = _obj.GetType();
        }

        public ReflectObject(object o) {
            _obj = o;
            ObjectType = _obj.GetType();
        }
        
        public T GetObject<T>() => (T) _obj;

        #region 字段
        public bool HasField(string fieldName) {
            if (_obj == null || string.IsNullOrEmpty(fieldName)) return false;
            return ObjectType.HasField(fieldName);
        }
        public dynamic GetField(string fieldName) {
            if (_obj == null || string.IsNullOrEmpty(fieldName)) return null;
            return ObjectType.GetField(fieldName)?.GetValue(_obj);
        }

        public T GetField<T>(string fieldName) {
            if (_obj == null || string.IsNullOrEmpty(fieldName)) return default;
            return (T)ObjectType.GetField(fieldName)?.GetValue(_obj);
        }
        
        public void SetField<T>(string fieldName, T newVal) {
            ObjectType.SetField(fieldName, newVal);
        }
        #endregion

        
        #region 属性
        public bool HasProperty(string propertyName) {
            if (_obj == null || string.IsNullOrEmpty(propertyName)) return false;
            return ObjectType.HasProperty(propertyName);
        }
        
        public dynamic GetProperty(string propertyName) {
            if (_obj == null || string.IsNullOrEmpty(propertyName)) return null;
            return ObjectType.GetProperty(propertyName)?.GetValue(_obj);
        }

        public T GetProperty<T>(string propertyName) {
            if (_obj == null || string.IsNullOrEmpty(propertyName)) return default;
            return (T)ObjectType.GetProperty(propertyName)?.GetValue(_obj);
        }
        
        public void SetProperty<T>(string propertyName, T newVal) {
            ObjectType.SetProperty(propertyName, newVal);
        }
        #endregion

        public bool SetValue(string name, object value) {
            var field = ObjectType.GetField(name);
            if (field != null) {
                if (field.FieldType == typeof(string)) {
                    field.SetValue(_obj, value != null ? value.ToString() : null);
                }
                else if (field.FieldType == typeof(int) || field.FieldType == typeof(uint)) {
                    field.SetValue(_obj, ConvertToInt(value));
                }
                else if (field.FieldType == typeof(float) || field.FieldType == typeof(double)) {
                    field.SetValue(_obj, ConvertToFloat(value));
                }
                else if (field.FieldType.IsAssignableFrom(value != null ? value.GetType() : null)) {
                    field.SetValue(_obj, value);
                }
                return true;
            }
            
            var property = ObjectType.GetProperty(name);
            if (property != null) {
                if (property.PropertyType == typeof(string)) {
                    property.SetValue(_obj, value != null ? value.ToString() : null);
                }
                else if (property.PropertyType == typeof(int) || property.PropertyType == typeof(uint)) {
                    property.SetValue(_obj, ConvertToInt(value));
                }
                else if (property.PropertyType == typeof(float) || property.PropertyType == typeof(double)) {
                    property.SetValue(_obj, ConvertToFloat(value));
                }
                else if (property.PropertyType.IsAssignableFrom(property != null ? value.GetType() : null)) {
                    property.SetValue(_obj, value);
                }
                return true;
            }
            return false;
        }

        public object GetValue(string name) {
            var field = ObjectType.GetField(name);
            if (field != null) {
                return field.GetValue(_obj);
            }
            var property = ObjectType.GetProperty(name);
            if (property != null) {
                return property.GetValue(_obj);
            }
            return null;
        }

        int ConvertToInt(object obj) {
            if (obj is string) {
                int.TryParse(obj.ToString(), out var result);
                return result;
            }
            if (obj is int || obj is long || obj is sbyte || obj is short ||
                obj is uint || obj is ulong || obj is byte || obj is ushort || obj is bool) {
                return (int)obj;
            }
            if (obj == null) {
                return 0;
            }
            return 1;
        }

        float ConvertToFloat(object obj) {
            if (obj is string) {
                float.TryParse(obj.ToString(), out var result);
                return result;
            }
            if (obj is float || obj is double || obj is decimal) {
                return (float)obj;
            }
            if (obj == null) {
                return 0;
            }
            return 1;
        }
        
        
        #region 方法
        public bool HasMethod(string methodName) {
            if (_obj == null || string.IsNullOrEmpty(methodName)) return false;
            return ObjectType.HasMethod(methodName);
        }
        
        public bool HasMethod(string methodName,Type[] argsTypes) {
            if (_obj == null || string.IsNullOrEmpty(methodName)) return false;
            return ObjectType.FindMethod(methodName, argsTypes) != null;
        }
        
        public T InvokeMethod<T>(string methodName, object[] parameters = null) {
            if (_obj == null || string.IsNullOrEmpty(methodName)) return default;
            return (T)QuickReflect.RawCall(ObjectType, _obj, methodName, parameters, false);
        }
        
        #endregion

    }
}