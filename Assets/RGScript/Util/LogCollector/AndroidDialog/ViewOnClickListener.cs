using UnityEngine;

namespace RGScript.Util.AndroidDialog {
    public class ViewOnClickListener :AndroidJavaProxy {
        public delegate void OnClickDelegate (AndroidJavaObject view);
 
        public ViewOnClickListener.OnClickDelegate onClickDelegate;
 
        public ViewOnClickListener():base("android.view.View$OnClickListener"){
        }
 
        public void onClick(AndroidJavaObject view) {
            //
            if(onClickDelegate!=null){
                onClickDelegate (view);
            }
        }
    }
}