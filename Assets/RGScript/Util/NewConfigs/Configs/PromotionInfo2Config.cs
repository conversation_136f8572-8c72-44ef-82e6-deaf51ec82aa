using System;
using System.Collections;
using NewDynamicConfig;
using ChillyRoom;

[Serializable]
public class PromotionInfo2Config : IConfig {
    public PromotionInfo2 promotionInfo2;
    public PromotionInfo2Config() {
        this.IsForceOrAdd = true;
        this.CanMerge = false;
    }
    public override void ForceConfig() {
        // LogUtil.Log("ForceConfig-="+Abo.JsonUtil.ToJson(promotionInfo2,true));
        PromotionInfo2.SetPromotionInfo2(promotionInfo2);
        PromotionInfo2.SetPromotionInfo2Path(ConfigManager.LocalFileRoot + MyLocalConfigPath);
    }
}
