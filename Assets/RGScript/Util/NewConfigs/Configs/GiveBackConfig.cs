using System;
using System.Collections.Generic;


namespace NewDynamicConfig {
    [Serializable]
    public class GiveBackConfig : IConfig {
        /// <summary>
        /// 开始时间戳
        /// </summary>
        public string TimeStart;

        /// <summary>
        /// 结束时间戳
        /// </summary>
        public string TimeEnd;

        /// <summary>
        /// 全渠道关闭活动
        /// </summary>
        public bool Closed = false;
        
        /// <summary>
        /// 禁止的渠道
        /// </summary>
        public List<string> DisableChannels = new List<string>() {};
        
        /// <summary>
        /// 控制某些渠道是否弹评分弹窗
        /// </summary>
        public List<string> DisableReviewChannels = new List<string>() {};
        
        
        public GiveBackConfig() {
            this.IsForceOrAdd = true;
            this.CanMerge = true;
        }
    }
}

