using System;
using System.Collections.Generic;

namespace NewDynamicConfig {
    [Serializable]
    public class TrialCharacter {
        /// <summary>
        /// 角色索引
        /// </summary>
        public int CharacterId;

        /// <summary>
        /// 技能索引
        /// </summary>
        public int SkillId;

        /// <summary>
        /// 开始时间戳
        /// </summary>
        public string TimeStart;

        /// <summary>
        /// 结束时间戳
        /// </summary>
        public string TimeEnd;
    }

    [Serializable]
    public class TrialKnightConfig : IConfig {
        public List<TrialCharacter> list = new List<TrialCharacter>();

        public TrialKnightConfig() {
            this.IsForceOrAdd = true;
            this.CanMerge = false;
        }
    }
}
