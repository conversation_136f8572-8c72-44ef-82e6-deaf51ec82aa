using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using NewDynamicConfig;
using RGScript.Util;
using UnityEngine;

namespace NewDynamicConfig {
    [System.Serializable]
    public class DialogConfig:IConfig {
        public Dictionary<string,DialogData[]> Channe2UpdateData; 
        public DialogConfig() {
            this.IsForceOrAdd = true;
            this.CanMerge = false;
        }
        
        /// <summary>
        /// 当前的配置 如果没有则会返回
        /// </summary>
        public static DialogConfig Config {
            get {
                if (IConfig.currectUseConfig.TryGetValue(typeof(DialogConfig),out IConfig config)) {
                    return config as DialogConfig;
                } else {
                    return null;
                }
            }
        }

        public override void ForceConfig() {
            base.ForceConfig();
            ShowDialog();
        }
        /// <summary>
        /// 是否展示过dialog 防止一次游戏多次弹窗
        /// </summary>
        private static bool hasShowDialog;
        void ShowDialog() {
            if (Channe2UpdateData!=null && Channe2UpdateData.TryGetValue(ChannelConfig.GetChannelName(), out DialogData[] dialogDatas)) {
                if (dialogDatas != null && dialogDatas.Length > 0 && !hasShowDialog) {
                    DialogRunner.TryShowDialogs(dialogDatas);
                    hasShowDialog = true;
                }
            }
        }

        private static string url = "http://cdn.chillyroom.com/game/SoulKnight/Test/DialogConfig.json";
        public static async Task<(bool isSuccess,string msg)> FetchConfig() {
            //测试路径
            if (Application.isEditor||Debug.isDebugBuild) {
                url = "http://192.168.3.153/DialogText/DialogConfig.json";
            } 
            Func<Task<string>> func = async () => {
                // Debug.LogError("start FetchConfig");
                HttpResponseMessage res = await LoginApi.CommonHttpClient.GetAsync(url);
                // Debug.LogError(res.StatusCode);
                if (res.StatusCode == System.Net.HttpStatusCode.OK) {
                    string data = await res.Content.ReadAsStringAsync();
                    return data;
                }
                return string.Empty;
            };
            Func<string,(bool isSuccess,string msg)> dealReceiveData = (data) => {
                if (!string.IsNullOrEmpty(data)) {
                    try {
                        string originData = Abo.CryptUtil.DecryptDES(data, "x#s0*");
                        DialogConfig config = Abo.JsonUtil.ParseJson<DialogConfig>(originData);
                        config.TryUseConfig();
                    } catch (Exception ex) {
                        Debug.LogError(ex);
                    }
                    return (true, "获取成功");
                } else {
                    return (true, "网络异常");
                }
            };
            Func<Exception, (bool isSuccess, string msg)> exceptionFunc = (exception) => {
                return (false, $"{exception.GetType()}");
            };
            return await TaskUtil.ReCall(func,dealReceiveData,exceptionFunc);
        }
    }
}