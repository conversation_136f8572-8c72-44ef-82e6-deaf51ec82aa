using System;

namespace NewDynamicConfig {
    /// <summary>
    /// 武斗家限时免费配置
    /// </summary>
    [Serializable]
    public class FighterLimitFreeConfig : IConfig {
        /// <summary>
        /// 开始时间戳
        /// </summary>
        public string TimeStart;

        /// <summary>
        /// 结束时间戳
        /// </summary>
        public string TimeEnd;

        
        public FighterLimitFreeConfig() {
            this.IsForceOrAdd = true;
            this.CanMerge = true;
        }
    }
}

