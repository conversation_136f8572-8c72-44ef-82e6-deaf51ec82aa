namespace NewDynamicConfig {
	using UnityEngine;
	using System.Collections;
    using ChillyRoomAccount;
    using System;

    [Serializable]
    public class CloudSaveConfig : IConfig {
        public EmailUrl emailUrl;
        public CloudSaveConfig() {
            this.IsForceOrAdd = true;
            this.CanMerge = false;
        }
        public override void ForceConfig() {
            EmailUrl.SetConfigEmail(emailUrl);
            //Debug.Log(EmailUrl.emailUrl.PingRate);
        }
    }
}
