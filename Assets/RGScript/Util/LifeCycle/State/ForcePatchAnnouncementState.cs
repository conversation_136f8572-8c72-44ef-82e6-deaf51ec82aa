using IFix;
using RGScript.Util.ForcePatch;

namespace RGScript.Util.LifeCycle.State {
    /// <summary>
    /// 等待下载强制热更新的公告（若有） 并展示公告
    /// </summary>
    public class ForcePatchAnnouncementState : BaseLifeState {
        public async override void Start() {
            var enable = ForcePatchManager.EnableForcePatch;
            // 如果强制热更新功能开启，就要判断一下有没有获取渠道名，然后开始下载 manifest 和 公告
            if (enable) {
                async void OnConfirmChannelName(bool success, string channelName) {
                    await ForcePatchManager.InitDownloader();
                    await ForcePatchManager.HandleAnnouncement(() => {
                        Context.ChangeState(new ApplyForcePatchState());
                    });
                }

                // 要等待获取了渠道名才能知道是不是43盒子，如果以后全渠道开放的话就可以不用等
                if (LifeCycleManager.Instance.IsChannelNameConfirmed) {
                    OnConfirmChannelName(true, LifeCycleManager.Instance.ChannelName);
                } else {
                    LifeCycleManager.Instance.OnConfirmChannelNameOnce += OnConfirmChannelName;
                }
            } else {
                RGPatchManager.FetchPatchList();
                // 如果强制热更新功能未开启，就跳过强更流程，直接开始跳到下载运营配置 state
                Context.ChangeState(new DownloadConfigAndChangeSceneState());
            }
        }

        public override void End() {
        }
    }
}