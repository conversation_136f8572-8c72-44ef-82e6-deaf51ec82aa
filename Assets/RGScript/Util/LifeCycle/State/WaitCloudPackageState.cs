using I2.Loc;
using RGScript.Data;
using RGScript.Util.ForcePatch;
using System.Threading.Tasks;
using UIFramework;
using UnityEngine;
using UnityEngine.UI;

namespace RGScript.Util.LifeCycle {
    public class WaitCloudPackageState : BaseLifeState {
        public async override void Start() {
#if UNITY_EDITOR
            if (UnityEditor.EditorPrefs.GetBool("HideLogin", true)) {
                NextState();
                return;
            }
#endif
            
            // 入口不开的话直接到下一个状态
            var isOpen = ForcePatchManager.IsEntryOpen(ForcePatchEntry.FallbackCloudPackageRestore, false);
            if (!isOpen) {
                NextState();
                return;
            }

            while (true) {
                Debug.Log("kkk LoginProcessDataAsync");
                var success = await DataMgr.CloudPackageData.LoginProcessDataAsync();
                if (success) {
                    NextState();
                    break;
                }

                // 窗口问
                TaskCompletionSource<bool> tcs = new TaskCompletionSource<bool>();
                var window = UIWindowDialog.ShowDialogNew(UICanvasRoot.Inst.transform, ScriptLocalization.Get("I_tip"),
                    ScriptLocalization.Get("ui_cloud_package_fail_retry_content", "读取用户数据失败，请尝试重试。"),
                    (ForcePatchManager.retryText, (dialog) => {
                        // 重试
                        tcs.TrySetResult(true);
                        dialog.OnClick_Close();
                    }), (ForcePatchManager.cancelText, (dialog) => {
                        // 取消
                        tcs.TrySetResult(false);
                    }), useAb: false, showMask: true, hideAfterClick: false);
                var button = window.transform.Find("mask").GetComponent<Button>();
                button.onClick.RemoveAllListeners();

                if (!await tcs.Task) {
                    ForcePatchManager.QuitApp(null, sendTrack: false);
                }
            }
        }

        private void NextState() {
            SimpleEventManager.Raise(new RefreshLoginProgressSlider());
            Context.ChangeState(new VerifyRealNameState());
        }

        public override void End() {
        }
    }
}