using UnityEngine;

public static class VectorTransfer {
    public static Vector2Int Vec2Int(this Vector3 v3) {
        return new Vector2Int((int)v3.x, (int)v3.y);
    }

    public static Vector3Int Vec3Int(this Vector3 v3) {
        return new Vector3Int((int)v3.x, (int)v3.y, (int)v3.z);
    }

    public static Vector2 Vec2(this Vector3 v3) {
        return new Vector2(v3.x, v3.y);
    }

    public static Vector2Int Vec2Int(this Vector3Int v3) {
        return new Vector2Int(v3.x, v3.y);
    }

    public static Vector2 Vec2(this Vector2Int v3) {
        return new Vector2(v3.x, v3.y);
    }
    public static Vector3Int Vec3Int(this Vector2Int v3) {
        return new Vector3Int(v3.x, v3.y, 0);
    }

    public static Vector3 Vec3(this Vector2Int v2) {
        return new Vector3(v2.x, v2.y, 0);
    }

    public static Vector3 Vec3(this Vector3Int v) {
        return new Vector3(v.x, v.y, v.z);
    }
    
    public static Vector3 Vec3(this Vector2 v2, float z = 0) {
        return new Vector3(v2.x, v2.y, z);
    }
    public static Vector3 Abs(this Vector3 v3) {
        return new Vector3(Mathf.Abs(v3.x), Mathf.Abs(v3.y));
    }

    public static Vector2Int Vec2Int(this Vector2 v2) {
        return new Vector2Int((int)v2.x, (int)v2.y);
    }

    public static Vector3Int Vec3FloorToInt(this Vector3 v3) {
        return new Vector3Int(Mathf.FloorToInt(v3.x), Mathf.FloorToInt(v3.y), Mathf.FloorToInt(v3.z));
    }

    public static Vector2Int Abs(this Vector2Int v2) {
        return new Vector2Int(Mathf.Abs(v2.x), Mathf.Abs(v2.y));
    }
    public static Vector3Int Vec3Int(this Vector2 v2){
        return new Vector3Int((int)v2.x, (int)v2.y, 0);
    }
}

