using System;

namespace RGScript.Util.BaseState {
    public interface IBaseState :IBaseParameter, IBaseTrigger{
        /// <summary>
        /// StateMachine对象
        /// </summary>
        IBaseStateMachine StateMachine { get; }
        void SetStateMachine(IBaseStateMachine machine);
        
        string StateName { get; }
        
        /// <summary>
        /// 是否被锁定，被锁定后不可切换到其他状态
        /// </summary>
        bool IsLock { get; set; }
        public event Action OnStart;
        public event Action<float> OnUpdate;
        public event Action<string> OnTrigger;
        public event Action OnPause;
        public event Action OnResume;
        public event Action OnEnd;
        public event Action OnReset;
        /// <summary>
        /// 状态开始方法
        /// </summary>
        void Start();
        /// <summary>
        /// 状态轮询方法 由machine来驱动
        /// </summary>
        void Update(float deltaTime);
        /// <summary>
        /// 状态结束方法
        /// </summary>
        void End();
        void Pause();
        void Resume();
        void Trigger(string triggerName);
        void Reset();
        
    }
}