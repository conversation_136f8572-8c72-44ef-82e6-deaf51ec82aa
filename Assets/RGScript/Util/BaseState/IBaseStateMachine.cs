using System;

namespace RGScript.Util.BaseState {
    public interface IBaseStateMachine: IBaseTriggerContainer, IBaseTrigger {
        public event Action<float> OnUpdate;
        String MachineName { get; }
        IBaseState CurrentState { get; }
        bool IsStop { get; }
        void StartState(IBaseState state);
        /// <summary>
        /// 轮询驱动 需要外部调用驱动！！！
        /// </summary>
        void Update(float deltaTime);
        void PauseState();
        void ResumeState();
        void ChangeState(IBaseState state);
        void Destroy();
    }
}