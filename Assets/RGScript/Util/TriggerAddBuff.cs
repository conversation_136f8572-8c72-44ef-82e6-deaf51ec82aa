using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.Serialization;

namespace RGScript.Util {
    public class TriggerAddBuff : MonoBehaviour {
        public GameObject buffStealth;
        public float buffTime = 0.5f;
        public float cd = 4;
        [FormerlySerializedAs("SpriteRenderer")] public SpriteRenderer spriteRenderer;

        private float lastTriggerTime = -8;
        public GameObject circle;
        RGGameProcess.TimerWrapper _hideCircleTimer;

        [Button]
        private void OnTriggerEnter2D(Collider2D other) {
            if (other.transform.parent == null) return;
            if (other.transform.parent.TryGetComponent<RGController>(out var controller)) {
                
                if (controller.transform.Find("buff_stealth")) {
                    return;
                }
                
                float currentTime = Time.time;
                if (currentTime - lastTriggerTime < cd) {
                    return;
                }
                lastTriggerTime = currentTime;

                var tempObject = new GameObject("AvoidWind");
                tempObject.transform.SetParent(controller.transform);

                controller.attribute.speed_rate += 0.5f;
                var buff = Instantiate(this.buffStealth).GetComponent<BuffStealth>();
                buff.gameObject.name = "buff_stealth";
                buff.buff_time = buffTime;
                buff.transform.SetParent(controller.transform, false);
                buff.BuffSetting(controller, true);
                
                buff.OnBuffDestroy += () => {
                    controller.attribute.speed_rate -= 0.5f;
                };

                spriteRenderer.color = new Color(1,1,1,0.6f);
                circle.SetActive(false);
                RGGameProcess.CancelTimer(ref _hideCircleTimer);
                _hideCircleTimer = RGGameProcess.StartTimer(cd, () => {
                    circle.SetActive(true);
                    spriteRenderer.color = new Color(1,1,1,1);
                });
            }
        }

        private void OnTriggerExit2D(Collider2D other) {
            if (other.transform.parent == null) return;
            if (other.transform.parent.TryGetComponent<RGController>(out var controller)) {
                if (controller.transform.Find("AvoidWind")) {
                    Destroy(controller.transform.Find("AvoidWind").gameObject);
                }
            }
        }
    }
}