using RGScript.Data;
using RGScript.Data.Mall;
using Sirenix.OdinInspector;
using Sirenix.OdinInspector.Editor;
using System.Collections.Generic;
using System.Linq;
using TaskSystem;
using UnityEditor;
using UnityEngine;
using System;

namespace RGScript.Util.Editor {
    public class ReturnPlayerH5DebugWindow : OdinEditorWindow {
        [MenuItem("SoulKnight/Other Tool/Return Player H5 Debug Window")]
        public static void ShowWindow() {
            GetWindow<ReturnPlayerH5DebugWindow>("骑士召回调试");
        }

        #region 任务状态显示

        [Title("任务状态监控")]
        [ShowInInspector, ReadOnly]
        [PropertyOrder(-10)]
        public bool IsReturnPlayerH5Active => DataMgr.ReturnPlayerH5Data?.IsEnable() ?? false;

        [ShowInInspector, ReadOnly]
        [PropertyOrder(-9)]
        public string ActivityStatus => GetActivityStatusText();

        [ShowInInspector, ReadOnly]
        [PropertyOrder(-8)]
        public string CurrentTime => MallUtility.CurrentTime.ToString("yyyy-MM-dd HH:mm:ss");

        [ShowInInspector, ReadOnly]
        [PropertyOrder(-6)]
        public string BattleFactorStatus => GetBattleFactorStatus();

        [ShowInInspector, ReadOnly]
        [PropertyOrder(-5)]
        public string ServerDataStatus => GetServerDataStatus();

        [Button("刷新任务数据", ButtonSizes.Medium)]
        [PropertyOrder(-4)]
        public void RefreshTaskData() {
            if (Application.isPlaying) {
                TaskManager.Instance.LoadReturnPlayerH5Task();
                TaskManager.Instance.CheckReturnPlayerH5Tasks();
                Debug.Log("已刷新 ReturnPlayerH5 任务数据");
            } else {
                Debug.LogWarning("需要在运行时才能刷新任务数据");
            }
        }

        [ShowInInspector, ReadOnly]
        [PropertyOrder(-3)]
        [ListDrawerSettings(ShowIndexLabels = true, ListElementLabelName = "GetTaskDisplayName")]
        public List<TaskDisplayInfo> AllReturnPlayerH5Tasks => GetAllTaskDisplayInfo();

        #endregion

        #region 数据结构

        [System.Serializable]
        public class TaskDisplayInfo {
            [ReadOnly] public int TaskID;
            [ReadOnly] public string Status;
            [ReadOnly] public string Progress;
            [ReadOnly] public string Description;
            [ReadOnly] public bool CanGetReward;
            [ReadOnly] public string TaskType;

            public string GetTaskDisplayName() {
                return $"任务 {TaskID} - {Status} ({Progress})";
            }
        }

        #endregion

        #region 原有调试功能

        [Title("任务调试工具")]
        [PropertyOrder(0)]
        [Button("清除所有任务数据")]
        public static void CleanReturnPlayerH5TaskData() {
            TaskManager.Instance.RemoveAllReturnPlayerH5TaskData();
        }

        [PropertyOrder(1)]
        [LabelWidth(100)] public int taskId = 0;
        [PropertyOrder(2)]
        [LabelWidth(100)] public int progress = 0;

        [PropertyOrder(3)]
        [Button("设置任务进度")]
        public void Debug_SetReturnPlayerH5TaskProgress() {
            TaskManager.Instance.Debug_SetReturnPlayerH5TaskProgress(taskId, progress);
            Debug.Log("设置任务进度完成，id:" + taskId + " 进度:" + progress);
        }

        [PropertyOrder(4)]
        [Button("查询任务")]
        public void Debug_CheckReturnPlayerH5TaskType() {
            TaskManager.Instance.Debug_CheckReturnPlayerH5TaskType(taskId);
        }

        [PropertyOrder(5)]
        [Button("设置任务完成")]
        public void Debug_SetReturnPlayerH5TaskCompleted() {
            TaskManager.Instance.Debug_SetReturnPlayerH5TaskCompleted(taskId);
            Debug.Log("设置任务完成 id:" + taskId);
        }

        [PropertyOrder(6)]
        [LabelText("多个任务id 英文逗号分割")] [LabelWidth(200)]
        public string taskIds = "";

        [PropertyOrder(7)]
        [Button("设置多个任务完成")]
        public void Debug_SetReturnPlayerH5TasksCompleted() {
            var taskIdsList = taskIds.Split(',').Select(int.Parse).ToList();
            TaskManager.Instance.Debug_SetReturnPlayerH5TasksCompleted(taskIdsList);
            Debug.Log("设置任务完成:" + taskIds);
        }

        [PropertyOrder(8)]
        [Button("重置单个任务")]
        public void Debug_ResetSingleReturnPlayerH5Task() {
            TaskManager.Instance.Debug_ResetSingleReturnPlayerH5Task(taskId);
            Debug.Log("重置任务完成 id:" + taskId);
        }

        [PropertyOrder(9)]
        [Button("重置所有任务", ButtonSizes.Large)]
        [GUIColor(1f, 0.5f, 0.5f)]
        public void Debug_ResetAllReturnPlayerH5Tasks() {
            if (EditorUtility.DisplayDialog("确认重置", "确定要重置所有 ReturnPlayerH5 任务吗？这将清除所有任务数据！", "确定", "取消")) {
                TaskManager.Instance.Debug_ResetAllReturnPlayerH5Task();
                Debug.Log("已重置所有 ReturnPlayerH5 任务");
            }
        }

        #endregion

        #region 高级调试功能

        [Title("高级调试功能")]
        [PropertyOrder(10)]
        [Button("强制检查所有任务")]
        public void ForceCheckAllTasks() {
            if (Application.isPlaying) {
                TaskManager.Instance.CheckReturnPlayerH5Tasks();
                Debug.Log("已强制检查所有 ReturnPlayerH5 任务");
            } else {
                Debug.LogWarning("需要在运行时才能检查任务");
            }
        }

        [PropertyOrder(11)]
        [Button("刷新每日任务")]
        public void RefreshDailyTasks() {
            if (Application.isPlaying) {
                TaskManager.Instance.RefreshDailyReturnPlayerTasks();
                Debug.Log("已刷新每日 ReturnPlayerH5 任务");
            } else {
                Debug.LogWarning("需要在运行时才能刷新每日任务");
            }
        }

        [PropertyOrder(12)]
        [Button("启用战斗因子")]
        public void EnableBattleFactor() {
            if (Application.isPlaying) {
                DataMgr.ReturnPlayerH5Data.EnableFactor();
                Debug.Log("已启用 ReturnPlayerH5 战斗因子");
            } else {
                Debug.LogWarning("需要在运行时才能启用战斗因子");
            }
        }

        [PropertyOrder(13)]
        [Range(0, 100)]
        [LabelText("进度滑动条")]
        public int progressSlider = 0;

        [PropertyOrder(14)]
        [Button("使用滑动条设置进度")]
        public void SetProgressWithSlider() {
            progress = progressSlider;
            Debug_SetReturnPlayerH5TaskProgress();
        }

        #endregion

        #region 快速操作

        [Title("快速操作")]
        [PropertyOrder(20)]
        [Button("完整重置并重新加载", ButtonSizes.Large)]
        [GUIColor(1f, 0.8f, 0.5f)]
        public void FullResetAndReload() {
            if (!Application.isPlaying) {
                Debug.LogWarning("需要在运行时才能执行此操作");
                return;
            }

            if (EditorUtility.DisplayDialog("完整重置", "这将执行以下操作：\n1. 清除所有任务数据\n2. 重新加载任务\n3. 检查所有任务\n4. 刷新每日任务\n\n确定继续吗？", "确定", "取消")) {
                TaskManager.Instance.RemoveAllReturnPlayerH5TaskData();
                TaskManager.Instance.LoadReturnPlayerH5Task();
                TaskManager.Instance.CheckReturnPlayerH5Tasks();
                TaskManager.Instance.RefreshDailyReturnPlayerTasks();
                Debug.Log("完整重置并重新加载完成");
            }
        }

        [PropertyOrder(21)]
        [Button("模拟完成所有当前任务")]
        [GUIColor(0.5f, 1f, 0.5f)]
        public void CompleteAllCurrentTasks() {
            if (!Application.isPlaying) {
                Debug.LogWarning("需要在运行时才能执行此操作");
                return;
            }

            try {
                var taskDict = TaskManager.Instance.GetReturnPlayerH5TaskDict();
                if (taskDict == null || taskDict.Count == 0) {
                    Debug.LogWarning("没有找到任何任务");
                    return;
                }

                var taskIds = taskDict.Keys.ToList();
                TaskManager.Instance.Debug_SetReturnPlayerH5TasksCompleted(taskIds);
                Debug.Log($"已完成 {taskIds.Count} 个任务: {string.Join(", ", taskIds)}");
            } catch (Exception e) {
                Debug.LogError($"完成所有任务失败: {e.Message}");
            }
        }

        #endregion

        #region 辅助方法

        private string GetActivityStatusText() {
            if (!Application.isPlaying) {
                return "需要运行时才能获取状态";
            }

            try {
                var data = DataMgr.ReturnPlayerH5Data;
                if (data == null) {
                    return "ReturnPlayerH5Data 未初始化";
                }

                var isEnabled = data.IsEnable();
                var chinaTime = data.GetChinaTime();
                var currentMonth = data.GetCurrentActivityMonth(chinaTime);

                string monthText = currentMonth switch {
                    0 => "7月活动期间",
                    1 => "8月活动期间",
                    _ => "不在活动期间"
                };

                return $"{monthText} - 活动{(isEnabled ? "开启" : "关闭")} - 中国时间: {chinaTime:MM-dd HH:mm}";
            } catch (Exception e) {
                return $"获取状态失败: {e.Message}";
            }
        }

        private string GetBattleFactorStatus() {
            if (!Application.isPlaying) {
                return "需要运行时才能获取状态";
            }

            try {
                var data = DataMgr.ReturnPlayerH5Data;
                if (data == null) {
                    return "ReturnPlayerH5Data 未初始化";
                }

                var canEnableByServer = data.CanEnableFactorByServer();
                var canEnableByClient = data.CanEnableFactorByClient();
                var hasEnabled = data.HasEnableFactor();

                return $"服务器条件: {(canEnableByServer ? "满足" : "不满足")} | 客户端条件: {(canEnableByClient ? "满足" : "不满足")} | 已启用: {(hasEnabled ? "是" : "否")}";
            } catch (Exception e) {
                return $"获取战斗因子状态失败: {e.Message}";
            }
        }

        private string GetServerDataStatus() {
            if (!Application.isPlaying) {
                return "需要运行时才能获取状态";
            }

            try {
                var data = DataMgr.ReturnPlayerH5Data;
                if (data == null) {
                    return "ReturnPlayerH5Data 未初始化";
                }

                var hasFormedFullTeam = data.GetHasFormedFullTeam();

                return $"已组满队伍: {(hasFormedFullTeam ? "是" : "否")}";
            } catch (Exception e) {
                return $"获取服务器数据状态失败: {e.Message}";
            }
        }

        private List<TaskDisplayInfo> GetAllTaskDisplayInfo() {
            var result = new List<TaskDisplayInfo>();

            if (!Application.isPlaying) {
                result.Add(new TaskDisplayInfo {
                    TaskID = 0,
                    Status = "需要运行时才能获取任务信息",
                    Progress = "",
                    Description = "",
                    CanGetReward = false,
                    TaskType = ""
                });
                return result;
            }

            try {
                var taskDict = TaskManager.Instance.GetReturnPlayerH5TaskDict();
                if (taskDict == null || taskDict.Count == 0) {
                    result.Add(new TaskDisplayInfo {
                        TaskID = 0,
                        Status = "没有找到任何 ReturnPlayerH5 任务",
                        Progress = "",
                        Description = "可能需要先刷新任务数据",
                        CanGetReward = false,
                        TaskType = ""
                    });
                    return result;
                }

                foreach (var kvp in taskDict.OrderBy(x => x.Key)) {
                    var task = kvp.Value;
                    var config = task.Config;

                    result.Add(new TaskDisplayInfo {
                        TaskID = task.ID,
                        Status = task.Status.ToString(),
                        Progress = $"{task.CurrentProgress}/{task.TargetProgress}",
                        Description = GetTaskDescription(task),
                        CanGetReward = task.CanGetReward,
                        TaskType = task.IsMainTask ? "主任务" : "子任务"
                    });
                }
            } catch (Exception e) {
                result.Add(new TaskDisplayInfo {
                    TaskID = -1,
                    Status = "获取任务信息失败",
                    Progress = "",
                    Description = e.Message,
                    CanGetReward = false,
                    TaskType = "错误"
                });
            }

            return result;
        }

        private string GetTaskDescription(ReturnPlayerH5Task task) {
            try {
                var config = task.Config;
                if (config == null) {
                    return "配置为空";
                }

                return $"[{config.MissionDescription}] Day{config.Day}";
            } catch (Exception e) {
                return $"获取描述失败: {e.Message}";
            }
        }

        #endregion
    }
}