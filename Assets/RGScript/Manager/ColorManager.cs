using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public static class ColorManager {
    static public Color LightColor {
        get {
            if (BattleData.data.IsIronTide) {
                return Color.white;
            }
            return new Color(193f / 255f, 159f / 255f, 109f / 255f);
        }
    }

    static public Color BackColor {
        get {
            if (BattleData.data.IsIronTide) {
                return Color.gray;
            }
            return new Color(88f / 255f, 77f / 255f, 62f / 255f);
        }
    }
}