using UnityEngine;

/// <summary>
/// 攻击后产生额外事件的
/// </summary>
public class WIAfterAttack : WeaponItemBase {
    [Range(0, 1)]
	public float probability = 1;
    public override void MakeEffect(bool isSeup) {
        base.MakeEffect(isSeup);
        if (owner == null) return;
		owner.afterAttack += AdditionalAttack;
    }

    public override void RemoveEffect(bool drop = true) {
        if (owner == null) return;
        owner.afterAttack -= AdditionalAttack;
    }
    
    public void AdditionalAttack () {
        if (owner == null) return;
        if (owner.rg_random.Range(0f, 1f) < probability) 
        {
            AttackEffect();
        }
    }

    public virtual void AttackEffect() {}

    
}
