using System;
using UnityEngine;

public class WIStandCritical : WeaponItemBase {
    private float _standTime;
    private bool _hasEffect;
    private bool _hasMove;
    private const float CheckTime = 0.1f;
    private Timer _standTimer;
    private int _totalCritical;
    public float minStandTime;
    public float deltaTimeToCriticalFactor;
    public GameObject effect;
    private GameObject _effect;
    private Vector3 _posLast;
    public override void MakeEffect(bool isSetup) {
        base.MakeEffect(isSetup);
        if (!owner) {
            return;
        }

        _hasEffect = true;
        _standTimer = Timer.Register(CheckTime, true, false, CheckStand);
    }

    private void CheckStand() {
        if (!_hasEffect) {
            return;
        }
        
        if (_hasMove) {
            _standTime = 0;
            _hasMove = false;
            owner.ChangeCritical(-_totalCritical,emChangeValueType.Add);
            _totalCritical = 0;
            if (_effect) {
                Destroy(_effect.gameObject);
                _effect = null;
            }
            
            _posLast = owner.controller.transform.position;
            return;
        }
        
        _standTime += CheckTime;
        _posLast = owner.controller.transform.position;
        if (_standTime > minStandTime && _totalCritical < 999) {
            var deltaCritical = (int)(CheckTime * deltaTimeToCriticalFactor);
            _totalCritical += deltaCritical;
            owner.ChangeCritical(deltaCritical, emChangeValueType.Add);
            if (!_effect) {
                _effect = Instantiate(effect, owner.controller.transform, true);
                _effect.transform.localPosition = Vector3.zero;
            }
        }

    }

    public override void RemoveEffect(bool drop = true) {
        base.RemoveEffect(drop);
        _hasEffect = false;
        owner.ChangeCritical(-_totalCritical, emChangeValueType.Add);
        _standTimer?.Cancel();
        if (_effect) {
            Destroy(_effect.gameObject);
            _effect = null;
        }
    }

    private void FixedUpdate() {
        if (!_hasEffect) {
            return;
        }

        if (owner.controller && (owner.controller.GetFixedVelocity != Vector2.zero|| owner.controller.MovementInput.weight!= 0)) {
            _hasMove = true;
        }

        if ((owner.controller.transform.position - _posLast).magnitude > 0.1f) {
            _hasMove = true;
        }
    }

    protected override void OnDestroy() {
        base.OnDestroy();
        _standTimer?.Cancel();
    }
}
