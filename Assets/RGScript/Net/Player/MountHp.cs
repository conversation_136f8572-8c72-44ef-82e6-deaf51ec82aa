using DG.Tweening;
using UnityEngine;
using Vector3 = UnityEngine.Vector3;

public class MountHp : MonoBehaviour {
    bool local_player = false;
    Transform bar_hp;
    Transform bar_hp_extra;
    Transform bar_hp_extra_reverse;
    int max_hp = 0;
    int extra_hp = 0;
    Animator anim;
    SpriteRenderer sprite_bar;
    SpriteRenderer sprite_hp;
    RGMountController mount;

    Vector3 relativePos;
    private Vector2 _driverOffset = Vector2.zero; 

    public void ResetPosition(){
        mount = GetComponentInParent<RGMountController>();
        relativePos = transform.position - mount.transform.position;
    }

    public void SetDriverOffset(Vector2 driverOffset) {
        _driverOffset = driverOffset;
        Debug.Log($"driverOffset: {driverOffset}");
    }

    // Use this for initialization
    void Awake() {
        bar_hp = transform.Find("bar/hp");
        bar_hp_extra = transform.Find("bar/extra_hp");
        bar_hp_extra_reverse = transform.Find("bar/extra_hp_reverse");
        anim = transform.GetComponent<Animator>();
        sprite_hp = transform.Find("bar/hp").GetComponent<SpriteRenderer>();
        sprite_bar = transform.Find("bar").GetComponent<SpriteRenderer>();
        ResetPosition();
        max_hp = mount.GetComponent<RoleAttributePlayer>().max_hp;
        if (!sprite_bar) {
            sprite_bar = transform.Find("bar/edge").GetComponent<SpriteRenderer>();
        }
        HideBar();
    }

    private void Start() {
    }

    public void SetMaxHp(int maxHp) {
        max_hp = maxHp;
    }

    public void UpdateHpBar(int value, int maxValue) {
        if (max_hp != 0) {
            if (value <= 0) {
                HideBar();
            } 
            else if (anim.GetBool("dead")) {
                anim.SetBool("dead", false);
                if (transform.parent.GetComponent<RGController>().IsLocalPlayer()) {
                    HideBar();
                }
            }
            else if(!sprite_hp.enabled){
                ShowBar();
            }

            float proportion = value * 1.0f / maxValue;
            bar_hp.localScale = new Vector3(proportion, 1, 1);

            if (extra_hp > 0 && maxValue == value) {
                // 受伤就不展示了
                float extra_proportion = 1 - (maxValue - value) * 1.0f / extra_hp;
                bar_hp_extra.localScale = new Vector3(Mathf.Max(extra_proportion, 0), 1, 1);
            } else {
                if (bar_hp_extra) {
                    bar_hp_extra_reverse.gameObject.SetActive(false);
                    bar_hp_extra.gameObject.SetActive(false);
                }
            }
        }
    }

    /// <summary>
    /// 额外蓝条血量
    /// </summary>
    /// <param name="extraHp"></param>
    public void AddExtraHp(int extraHp) {
        if (!bar_hp_extra) return;
        
        hasRemove = false;
        bar_hp_extra_reverse.gameObject.SetActive(true);
        bar_hp_extra_reverse.DOScaleX(-0.1f, 0.5f).OnComplete(() => {
            bar_hp_extra_reverse.gameObject.SetActive(false);
            if (!hasRemove) {
                bar_hp_extra.gameObject.SetActive(true);
                bar_hp_extra.localScale = new Vector3(1, 1, 1);
                bar_hp_extra_reverse.localScale = new Vector3(0, 1, 1);
                extra_hp = extraHp;
            }
        });
    }

    private bool hasRemove = false;
    public void RemoveExtraHp() {
        if (!bar_hp_extra) return;
        
        extra_hp = 0;
        hasRemove = true;
        bar_hp_extra.localScale = new Vector3(0, 1, 1);
        bar_hp_extra_reverse.localScale = new Vector3(0, 1, 1);
        bar_hp_extra_reverse.gameObject.SetActive(false);
        bar_hp_extra.gameObject.SetActive(false);
    }

    public void ShowBar() {
        if (mount.driver && mount.driver.IsLocalPlayer()) {
            sprite_hp.enabled = true;
            sprite_bar.enabled = true;
        }
    }

    public void HideBar() {
        sprite_hp.enabled = false;
        sprite_bar.enabled = false;
    }

    public void Update() {
        UpdateTransform();
    }

    public void UpdateTransform() {
        if (!mount.inBuild) {
            transform.eulerAngles = Vector3.zero;
            if (mount == null) {
                return;
            }
            
            if (null == mount.transform) {
                return;
            }

            transform.position = mount.transform.position + relativePos;
            if (_driverOffset != Vector2.zero && mount.driver != null) {
                transform.position += new Vector3(mount.driver.facing > 0 ? _driverOffset.x : -_driverOffset.x, _driverOffset.y, 0);
            }
        }
    }
}