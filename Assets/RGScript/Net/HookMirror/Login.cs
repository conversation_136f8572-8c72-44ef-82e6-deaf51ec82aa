using System.Net.Sockets;
using System.Threading;
using UnityEngine;

public class NetLogic : MonoBehaviour
{
    public static NetLogic Inst => USingleton<NetLogic>.Inst;

    TcpLink _relay = new TcpLink();
    public Rpc     rpc;

    void Start()
    {
        _relay.Connect("192.168.1.111", 9999, (err, str) => {
            if (err == 0) {
                rpc = new Rpc();
                rpc.SetNetLink(_relay);
                rpc.RegistRpc(Player.Inst);
                var firstMsg = new byte[4]; Utility.WriteUInt32ToBuffer(Player.Inst.m_connId, firstMsg, 0);
                _relay.SendBuf(firstMsg); //首包，上报connId
            } else {
                Debug.LogError(str);
            }
        });
    }
    void Update()
    {
        if (rpc != null) rpc.Update();
    }
}

public class Player : MonoBehaviour
{
    public static Player Inst => USingleton<Player>.Inst;

    public uint playerId;
    public uint m_connId = 0;

    public void Rpc_svr_accept(NetPack recvBuf, NetPack backBuf) {
        m_connId = recvBuf.ReadUInt32();
        Debug.LogWarningFormat("Rpc_svr_accept: {0}", m_connId);
        NetLogic.Inst.rpc.CallRpc(RpcEnum.Rpc_set_identity, buf => {
            buf.WriteUInt32(m_connId);
        });
    }

    public NetBuffer RecvBuf = new NetBuffer(4096);
    public readonly Object _lock = new Object();
    public void Rpc_client_handle_spawn_attrs(NetPack recvBuf, NetPack backBuf) {
        RecvBuf.Write(recvBuf.LeftBuf, (uint)recvBuf.LeftBuf.Length);
        Monitor.Enter(_lock);
        Monitor.Pulse(_lock);
        Monitor.Exit(_lock);
    }
}
