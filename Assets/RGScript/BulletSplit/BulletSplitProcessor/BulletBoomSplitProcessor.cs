using UnityEngine;
using DG.Tweening;

[RequireComponent(typeof(BulletBoom))]
public class BulletBoomSplitProcessor : MonoBehaviour, IBulletSplitProcessor {
    private static GameObjectSingleCache<BulletBoom> componentCache = new GameObjectSingleCache<BulletBoom>();
    public float scaleFactor = .75f;
    public float newBulletShowInterval = .02f;
    public float positionOffset = 1.5f;
    private BulletBoom bulletBoom;
    public void NewBulletOnSplit(GameObject original, int index, ref BulletSplitConfig splitConfig) {
        var originalBulletBoom = componentCache.GetCache(original);
        bulletBoom = GetComponent<BulletBoom>();
        bulletBoom.UpdateInfo(originalBulletBoom.bulletInfo, originalBulletBoom.damageInfo);
        bulletBoom.boom_time = originalBulletBoom.boom_time;
        bulletBoom.boom_at_start = false;
        bulletBoom.bulletInfo.size *= scaleFactor;
        gameObject.SetActive(false);
        DOTween.To(() => 0f, _ => {}, 0f, newBulletShowInterval * (index + 1)).OnComplete(() => {
            gameObject.SetActive(true);
            bulletBoom.StartBoom();
        });
        SetPosition();
        SetDamageWithFactor(splitConfig.damageFactor);
    }

    public void OriginalBulletOnSplit(ref BulletSplitConfig splitConfig) {
        bulletBoom = componentCache.GetCache(gameObject);
        bulletBoom.bulletInfo.size *= scaleFactor;
        SetPosition();
        SetDamageWithFactor(splitConfig.damageFactor);
    }

    private void SetPosition() {
        var random = bulletBoom.bulletInfo.sourceWeapon.rg_random;
        transform.position += random.RandomInCircle(positionOffset);
    }

    private void SetDamageWithFactor(float factor) {
        bulletBoom.damageInfo = bulletBoom.damageInfo.SetDamageWithFactor(factor);
    }
}
