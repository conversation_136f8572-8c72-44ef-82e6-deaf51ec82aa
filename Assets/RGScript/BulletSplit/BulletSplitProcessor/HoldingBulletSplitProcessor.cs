using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 扫荡激光子弹分裂器
/// </summary>
public class HoldingBulletSplitProcessor : MonoBehaviour, IBulletSplitProcessor {
    public void NewBulletOnSplit(GameObject original, int index, ref BulletSplitConfig splitConfig) {
        transform.Rotate(0, 0, splitConfig.GetSplitAngle(index));
        SetScale(ref splitConfig);
        var prefabPoolObjects = GetComponents<IPrefabPoolObject>();
        foreach (var o in prefabPoolObjects) {
            o.OnTaken();
        }
        SetDamageFactor(splitConfig.damageFactor);
        if (GetComponent<DamageCarrier>()) {
            GetComponent<DamageCarrier>().OnSplit(index, ref splitConfig);
        }
    }

    public void OriginalBulletOnSplit(ref BulletSplitConfig splitConfig) {
        transform.Rotate(0, 0, splitConfig.GetSplitAngle());
        SetScale(ref splitConfig);
        SetDamageFactor(splitConfig.damageFactor);
        if (GetComponent<DamageCarrier>()) {
            GetComponent<DamageCarrier>().OnSplit(0, ref splitConfig);
        }
    }

    private void SetScale(ref BulletSplitConfig splitConfig) {
        var scale = transform.localScale;
        scale.y *= splitConfig.scaleFactor;
        transform.localScale = scale;
    }

    private void SetDamageFactor(float damageFactor) {
        var damageCarrier = GetComponent<DamageCarrier>();
        damageCarrier.SetDamageWithFactor(damageFactor);
        if (GetComponent<DamageCarrier>()) {
            GetComponent<DamageCarrier>().RefreshInfo();
        }
    }
}
