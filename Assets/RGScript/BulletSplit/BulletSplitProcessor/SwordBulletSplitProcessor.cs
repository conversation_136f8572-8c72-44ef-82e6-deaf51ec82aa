using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;

public class SwordBulletSplitProcessor : MonoBehaviour, IBulletSplitProcessor {
    public float exactAngle = 30f;
    public float timeInterval = .05f;

    public bool useOffsetList;
    public List<Vector2> offsetList;
    public List<int> ignoreRotIdx;
    public bool isAllIgnoreRot;
    public bool needOffset = true;
    public float offset = 0.4f;
    //cache action info for delay active logic
    private ActionInfo _cachedBulletActionInfo;
    private bool isFaceRight => transform.localScale.x > 0;
    public void NewBulletOnSplit(GameObject original, int index, ref BulletSplitConfig splitConfig) {
        SplitProcess(ref splitConfig, index);
        var prefabObjects = GetComponents<IPrefabPoolObject>();
        foreach (var o in prefabObjects) {
            o.OnTaken();
        }
        DelayedActive(index, splitConfig.count + 1);
    }

    public void OriginalBulletOnSplit(ref BulletSplitConfig splitConfig) {
        SplitProcess(ref splitConfig);
        DelayedActive(0, splitConfig.count + 1);
    }

    private void DelayedActive(int index, int count) {
        if (TryGetComponent(out DamageCarrier dmgCarrier)) {
            _cachedBulletActionInfo = dmgCarrier.actionInfo;
        }
        gameObject.SetActive(false);
        float duration;
        if (isFaceRight) {
            duration = (count - index) * timeInterval;
        } else {
            duration = index * timeInterval;
        }
        DOTween.To(() => 0f, _ => { }, 0f, duration).OnComplete(() => {
            gameObject.SetActive(true);
            if (GetComponent<RGSwordMobile>()) {
                GetComponent<RGSwordMobile>().StartMove();
            }
            if (TryGetComponent(out DamageCarrier dc)) {
                dc.actionInfo = _cachedBulletActionInfo;
            }
        });
    }

    private void SplitProcess(ref BulletSplitConfig splitConfig, int index = 0) {
        var angle = splitConfig.angle;
        splitConfig.angle = exactAngle;
        if (isAllIgnoreRot || (null != ignoreRotIdx && ignoreRotIdx.Contains(index))) {
            if (needOffset) {
                var newOffset = offset * (index % 2 == 0 ? 1f : -1f);
                var dir = Vector3.zero;
                dir.y = Mathf.Cos(transform.eulerAngles.z * Mathf.Deg2Rad);
                dir.x = Mathf.Sin(transform.eulerAngles.z * Mathf.Deg2Rad);
                transform.position += dir * newOffset;
            }
            splitConfig.angle = 0;
        }

        if (useOffsetList) {
            transform.position += offsetList[index].Vec3();
        }
        transform.Rotate(0, 0, splitConfig.GetSplitAngle(index));
        splitConfig.angle = angle;
        var damageCarrier = GetComponent<DamageCarrier>();
        damageCarrier.SetDamageWithFactor(splitConfig.damageFactor);
        var sword = GetComponent<RGSword>();
        if (sword != null) {
            sword.FlushInfo();
        }
    }
}
