using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[RequireComponent(typeof(Bullet03))]
public class Bullet03SplitProcessor : MonoBehaviour, IBulletSplitProcessor {
    private GameObjectSingleCache<Bullet03> bulletCache = new GameObjectSingleCache<Bullet03>();
    public void NewBulletOnSplit(GameObject originBullet, int currentIndex, ref BulletSplitConfig splitConfig) {
        SplitProcess(ref splitConfig, currentIndex);
        var bullet = bulletCache.GetCache(gameObject);
        bullet.OnTaken();
        bullet.max_size *= splitConfig.scaleFactor;
        bullet.max_time *= splitConfig.scaleFactor;
        
    }

    public void OriginalBulletOnSplit(ref BulletSplitConfig splitConfig) {
        SplitProcess(ref splitConfig);
    }

    private void SplitProcess(ref BulletSplitConfig splitConfig, int index = 0) {
        var bullet = bulletCache.GetCache(gameObject);
        bullet.SetDamageWithFactor(splitConfig.damageFactor);
        bullet.UpdateInfo(bullet.bulletInfo, bullet.damageInfo);
        // bullet.max_size *= splitConfig.scaleFactor;
        // bullet.max_time *= splitConfig.scaleFactor;
        var angle = splitConfig.angle;
        splitConfig.angle = angle;
        transform.Rotate(0, 0, splitConfig.GetSplitAngle(index));
    }
}
