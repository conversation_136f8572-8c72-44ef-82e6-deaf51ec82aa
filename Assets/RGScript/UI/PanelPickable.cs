using System.Collections.Generic;
using UnityEngine;

public class PanelPickable : MonoBehaviour {
	GameObject protoSeed;
	GameObject protoMaterial;
	GameObject protoBlueprint;

	void Awake () {
		Init();
	}

	bool inited;
	void Init() {
		if (!inited) {
			protoSeed = transform.Find("seed").gameObject;
			protoMaterial = transform.Find("material").gameObject;
			protoBlueprint = transform.Find("blueprint").gameObject;
			protoSeed.gameObject.SetActive(false);
			protoMaterial.gameObject.SetActive(false);
			protoBlueprint.gameObject.SetActive(false);
			inited = true;
		}
	}

	public void FlushPickable(List<PickableInfo> pickables) {
	}
	public void FlushPickable(Dictionary<string, int> name2Count) {
	}

	Stack<GameObject> itemSeeds = new Stack<GameObject>();
	Stack<GameObject> itemBlueprints = new Stack<GameObject>();
	Stack<GameObject> itemMaterials = new Stack<GameObject>();
	public void ClearAll() {
		for (int i = 3; i < transform.childCount; i++) {
			var go = transform.GetChild(i).gameObject;
			if (go.activeSelf) {
				go.SetActive(false);
				if (go.name == "seed") {
					itemSeeds.Push(go);
				} else if (go.name == "material") {
					itemMaterials.Push(go);
				} else if (go.name == "blueprint") {
					itemBlueprints.Push(go);
				}
			}

		}
	}

	public GameObject GetItem(emItemType pickableType) {
		GameObject go = null;
		switch (pickableType) {
			case emItemType.Seed:
				go = itemSeeds.Count == 0 ? GameObject.Instantiate<GameObject>(protoSeed, transform) : itemSeeds.Pop();
				break;
			case emItemType.BluePrint:
				go = itemBlueprints.Count == 0 ? GameObject.Instantiate<GameObject>(protoBlueprint, transform) : itemBlueprints.Pop();
				break;
			case emItemType.Material:
				go = itemMaterials.Count == 0 ? GameObject.Instantiate<GameObject>(protoMaterial, transform) : itemMaterials.Pop();
				break;
			default:
				break;
		}
		go.SetActive(true);
		return go;
	}

}
