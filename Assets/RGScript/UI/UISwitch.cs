using UnityEngine;
using System.Collections;
using UIFramework;
using UnityCommon.UI;
using UnityEngine.UI;

public class UISwitch : BaseUIView {
    Text the_text;
    public bool awake = false;

    [HideInInspector]
    public UIBtnInterface the_mission;

    public override void Awake() {
        base.Awake();
        the_text = transform.Find("Text").GetComponent<Text>();
    }

    void Update() {
        if (awake) {
            if (InputManager.Inst.IsPassCancelKey()) {
                CloseBtnClick();
            }

            if (Input.GetButtonDown("BtnR")) {
                BtnYesClick();
			}
			if (Input.GetKeyDown(KeyCode.Escape))
			{
				// close window
				CloseBtnClick();
			}
		}
	}
	public void SetQuestionText(UIBtnInterface value1, string value2){
		the_mission=value1;
		the_text.text=value2;
		awake=true;
	}

	public void BtnYesClick(){
		if(awake){
			the_mission.BtnClick1();
			awake=false;
            UIManager.Inst.CloseUIView(this, true, false);
			RGMusicManager.GetInstance ().PlayEffect (8);
		}
	}

	public void CloseBtnClick(){
		if(awake){
			the_mission.BtnClick2();
			awake=false;
            UIManager.Inst.CloseUIView(this, true, false);
            RGMusicManager.GetInstance ().PlayEffect (8);
		}
	}
}
