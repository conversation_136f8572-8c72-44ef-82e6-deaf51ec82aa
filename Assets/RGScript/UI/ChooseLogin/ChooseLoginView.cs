using ChillyRoom.Services.Accounts.Sdk;
using ChillyRoom.Services.Accounts.Sdk.UI;
using ChillyRoom.Services.Core;
using CoreKit.Config;
using DG.Tweening;
using Generated.ChillyRoomSdkClient;
using I2.Loc;
using Newtonsoft.Json;
using RGScript.Other;
using RGScript.Other.Globalize;
using RGScript.Other.LegencyAdapter;
using RGScript.Other.NewSDK;
using RGScript.UI.MVC;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Text;
using UnityCommon.UI;
using UnityEngine;
using UnityEngine.UI;
using Debug = UnityEngine.Debug;

namespace RGScript.UI.ChooseLogin {
    public class ChooseLoginView : View<ChooseLoginData> {
        private const string Viewname = "ChooseLoginView";
        private Transform  _loginBtns;
        private RectTransform _loginPar;
        private Button _appleLoginBtn, _googleLoginBtn, _channelLoginBtn, _chillyLoginBtn, _guestLoginBtn;
        private Button _languageBtn,_exitLanguageBtn,_ageBtn;
        private RectTransform _languagePanelRectTransform;
        private bool _isLanguagePanelShow;
        private Sequence _languageSequence, _exitBtnSequence, _loginingTextSequence;
        private bool _isExitGameBtnShow;
        private Button _exitGameBtn;
        private RectTransform _exitGameBtnRectTransform;
        private readonly Color _hideColor = new Color(1, 1, 1, 0);

        private bool HasShowGuestGuide {
            get {
                var result = PlayerSaveData.GetIntLocal("HasShowGuestGuide", 0) > 0;
                LogUtil.Log($"[ChooseLoginView]HasShowGuestGuide : {result}");
                return result;
            }

            set {
                PlayerSaveData.SetIntLocal("HasShowGuestGuide", value ? 1 : 0);
            }
        }
        
        private GameObject _healthyGameAdviceObject;
        private Text _loginText;
        private Button _infoBtn;
        private UIWindowBroadcast _broadcast;
        private Button _infoCloseBtn;
        private bool _isInfoShow;
        private bool _awake;
        private Transform _privacyTog;
        private event Action AfterInit;

        protected override void Awake() {
            base.Awake();
            StartCoroutine(DelayInit());
        }

        IEnumerator DelayInit() {
            _privacyTog = transform.Find("StealthConsent");
            _loginPar= transform.Find("LoginBtnPar").GetComponent<RectTransform>();
            _loginBtns = _loginPar.Find("LoginBtns");
            _appleLoginBtn = _loginBtns.Find("appleLogin").GetComponent<Button>();
            _googleLoginBtn = _loginBtns.Find("googleLogin").GetComponent<Button>();
            _channelLoginBtn = _loginBtns.Find("channelLogin").GetComponent<Button>();
            _chillyLoginBtn = _loginBtns.Find("chillyLogin").GetComponent<Button>();
            _guestLoginBtn = _loginBtns.Find("guestLogin").GetComponent<Button>();
            _languageBtn = transform.Find("button_langue").GetComponent<Button>();
#if UNITY_IOS
            // iOS审核需求，登陆按钮下面不能有字
            for (int i = 0; i < _loginBtns.childCount; i++) {
                var btn = _loginBtns.GetChild(i);
                var textTrans = btn.Find("Text");
                if (!textTrans) {
                    continue;
                }

                textTrans.gameObject.SetActive(false);
            }
#endif
            _languagePanelRectTransform = transform.Find("window_language").GetComponent<RectTransform>();
            _exitLanguageBtn = _languagePanelRectTransform.Find("btn_no").GetComponent<Button>();
            _exitGameBtn = transform.Find("btn_quit").GetComponent<Button>();
            _exitGameBtnRectTransform = _exitGameBtn.GetComponent<RectTransform>();

            AnimateButtonText(_appleLoginBtn);
            AnimateButtonText(_googleLoginBtn);
            AnimateButtonText(_channelLoginBtn);
            AnimateButtonText(_chillyLoginBtn);
            AnimateButtonText(_guestLoginBtn);

            yield return null;
            _ageBtn = UiUtil.InitAgeBtn(transform);
            _healthyGameAdviceObject = transform.Find("health_game_advice").gameObject;
            _loginText = transform.Find("LoginingText").GetComponent<Text>();
            _appleLoginBtn.onClick.AddListener(ClickAppleLogin);
            _googleLoginBtn.onClick.AddListener(ClickGoogleLogin);
            _channelLoginBtn.onClick.AddListener(ClickChannelLogin);
            _chillyLoginBtn.onClick.AddListener(ClickChillyLogin);
            _guestLoginBtn.onClick.AddListener(ClickGuestLogin);
            _languageBtn.onClick.AddListener(ClickLanguage);
            _exitLanguageBtn.onClick.AddListener(ClickExitLanguage);
            _exitGameBtn.onClick.AddListener(ClickExitBtn);
            yield return null;
            var element1 = _languagePanelRectTransform.Find("Grid/element1");
            var element2 = _languagePanelRectTransform.Find("Grid/element2");
            var element3 = _languagePanelRectTransform.Find("Grid/element3");
            AddLanguageListener(element1);
            AddLanguageListener(element2);
            AddLanguageListener(element3);
            #region 部分渠道关闭button_Farsi和button_Arabic
            if (!LanguageUtil.NeedShowFarsiLanguageBtn()) {
                HideLanguageBtn(element3, "button_Farsi", _hideColor);
            }
            if (!LanguageUtil.NeedShowArabicLanguageBtn()) {
                HideLanguageBtn(element3, "button_Arabic", _hideColor);
            }
            #endregion
            yield return null;
            _languageSequence = DOTween.Sequence().SetAutoKill(false);
            _exitBtnSequence = DOTween.Sequence().SetAutoKill(false);
#if UNITY_IOS
            _appleLoginBtn.gameObject.SetActive(GameUtil.GetLegencyAvailable());
            _googleLoginBtn.gameObject.SetActive(false);
          
#elif UNITY_ANDROID
            _appleLoginBtn.gameObject.SetActive(false);
            _googleLoginBtn.gameObject.SetActive(GameUtil.GetLegencyAvailable() && ChannelConfig.IsAllGP && SdkConfigManager.gameConfig.Distro.GrantTypes.Any(_ => _.Type=="GooglePlay"));
#elif UNITY_EDITOR
            _appleLoginBtn.gameObject.SetActive(false);
            _googleLoginBtn.gameObject.SetActive(false);
#endif
            //如果AccountStateManager没初始化好 则可能为空
            if (AccountStateManager.Instance == null) {
                _channelLoginBtn.gameObject.SetActive(false);
                _chillyLoginBtn.gameObject.SetActive(false);
                _guestLoginBtn.gameObject.SetActive(false);
                SdkEvents.OnInitialized += InitBtn;
            } else {
                InitBtn();
            }
            _healthyGameAdviceObject.gameObject.SetActive(false);
            FixOperateText();
            _loginText.gameObject.SetActive(false);
            _infoBtn = transform.Find("button_info").GetComponent<Button>();
            _infoBtn.onClick.AddListener(ClickInfoBtn);
            _infoBtn.gameObject.SetActive(LanguageUtil.IsChinaMainland() && !ChannelConfig.IsHuaWeiOutseas && !ChannelConfig.IsBazaar);
            _broadcast = transform.Find("window_broadcast").GetComponent<UIWindowBroadcast>();
            _infoCloseBtn = _broadcast.transform.Find("panel/Title/btn_close").GetComponent<Button>();
            _infoCloseBtn.onClick.AddListener(ClickCloseInfoBtn);

            RefreshLogo();
            yield return null;
            _awake = true;
            OnAfterInit();
            
            var rules = GlobalizeManager.Instance.UseGlobalizeByName(RGGameConst.CommonRules, "ChangeChillyRoomIconToEmail");
            if (rules != null && rules.Open) {
                var chillyText = _chillyLoginBtn.transform.Find("Text").GetComponent<Text>();
                GameObject.Destroy(chillyText.transform.GetComponent<Localize>());
                chillyText.text = "Tài khoản Email";
                _chillyLoginBtn.transform.Find("Image").gameObject.SetActive(false);
                _chillyLoginBtn.transform.Find("mailImage").gameObject.SetActive(true);
            }
        }

        void AnimateButtonText(Button btn) {
            var text = btn.transform.Find("Text").GetComponent<Text>();
            text.DOFade(0.5f, 2f).SetLoops(-1, LoopType.Yoyo).SetEase(Ease.Linear);
        }
        
        void InitBtn() {
            BlockGpChannelChineseGuestLogin();
            
            var acceptLoginByEmail = AccountStateManager.Instance.AcceptLoginType(UniversalSdkType.Email);
            var acceptLoginByPhone = AccountStateManager.Instance.AcceptLoginType(UniversalSdkType.Phone);
            var acceptLoginByGuest = AccountStateManager.Instance.AcceptLoginType(UniversalSdkType.Guest);
            _channelLoginBtn.gameObject.SetActive(NewSDKManager.Inst.HasChannelLogin && !ChannelConfig.IsAllIOS && !ChannelConfig.IsAllGP);
            _chillyLoginBtn.gameObject.SetActive(acceptLoginByEmail || acceptLoginByPhone);
            _guestLoginBtn.gameObject.SetActive(acceptLoginByGuest);
        }

        private static void BlockGpChannelChineseGuestLogin() {
            if (ChannelConfig.IsAllGP) {
                if (ChooseLoginController.NeedAutoLogin &&
                    ChooseLoginController.LastLoginType == ChooseLoginData.LoginType.Guest) {
                    //   - 本地有游客登录记录的，可以继续使用游客登录
                    PlayerPrefs.SetInt("already_guest", 1);
                    PlayerPrefs.Save();
                }

                if ((Application.systemLanguage == SystemLanguage.ChineseSimplified
                     || CultureInfo.CurrentCulture.ThreeLetterWindowsLanguageName.Equals("CHS",
                         StringComparison.OrdinalIgnoreCase))
                    && !ChooseLoginController.NeedAutoLogin
                    && PlayerPrefs.GetInt("already_guest", 0) != 1) {
                    //   - 无游客登录记录的，中国大陆地区，则不再展示游客登录入口
                    for (int i = AccountStateManager.Instance.Config.Distro.GrantTypes.Count - 1; i >= 0; i--) {
                        if (AccountStateManager.Instance.Config.Distro.GrantTypes[i].Type ==
                            UniversalSdkType.Guest.ToString()) {
                            AccountStateManager.Instance.Config.Distro.GrantTypes.RemoveAt(i);
                            break;
                        }
                    }
                }
            }
        }

        void AddLanguageListener(Transform element) {
            for (int i = 0; i < element.childCount; i++) {
                var childBtn = element.GetChild(i);
                var spliteName = childBtn.name.Split('_');
                if (spliteName.Length == 2) {
                    childBtn.GetComponent<Button>().onClick.AddListener(()=>ClickSwitchLanguage(spliteName[1]));
                }
            }
        }
        void HideLanguageBtn(Transform languageParent, string btnName, Color hideColor) {
            var farsiBtn = languageParent.Find(btnName).GetComponent<Button>();
            farsiBtn.image.color = hideColor;
            farsiBtn.interactable = false;
            farsiBtn.transform.Find("Text").gameObject.SetActive(false);
            farsiBtn.transform.SetAsLastSibling();
        }

        void ClickAppleLogin() {
            if (!_awake) {
                LogUtil.Log("[ChooseLoginView]ClickAppleLogin awake is false");
                return;
            }
            var accountSdkUIFacade = AccountSdkUIFacade.GetAccountSdkUIFacade();
            if (accountSdkUIFacade.GetAccountisActiveAndEnabled()) {
                LogUtil.Log("[ChooseLoginView]ClickAppleLogin GetAccountisActiveAndEnabled");
                return;
            }
            DispatchMessage(new Message {
                Command = ChooseLoginCommand.ClickAppleLogin
            });
            RGMusicManager.GetInstance().PlayEffect(RGMusicManager.emEffect.Button);
        }

        void ClickGoogleLogin() {
            if (!_awake) {
                return;
            }
            //有密码登陆
            if (NewSDKManager.Inst.HasChillyLogin) {
                var uiFac = AccountSdkUIFacade.GetAccountSdkUIFacade();
                var loginView = uiFac.GetLoginView();
                if (uiFac.Active && loginView.Active) {
                    return;
                }
            }
            DispatchMessage(new Message {
                Command = ChooseLoginCommand.ClickGoogleLogin
            });
            RGMusicManager.GetInstance().PlayEffect(RGMusicManager.emEffect.Button);
        }
        
        void ClickChannelLogin() {
            if (!_awake) {
                return;
            }
            DispatchMessage(new Message {
                Command = ChooseLoginCommand.ClickChannelLogin
            });
            RGMusicManager.GetInstance().PlayEffect(RGMusicManager.emEffect.Button);
        }

        void ClickChillyLogin() {
            if (!_awake) {
                return;
            }
            DispatchMessage(new Message {
                Command = ChooseLoginCommand.ClickChillyLogin
            });
            RGMusicManager.GetInstance().PlayEffect(RGMusicManager.emEffect.Button);
        }

        void ClickGuestLogin() {
            if (!_awake) {
                return;
            }
            DispatchMessage(new Message {
                Command = ChooseLoginCommand.ClickGuestLogin
            });
            RGMusicManager.GetInstance().PlayEffect(RGMusicManager.emEffect.Button);
        }

        void ClickLanguage() {
            if (!_awake) {
                return;
            }
            DispatchMessage(new Message {
                Command = ChooseLoginCommand.ClickLanguage
            });
            RGMusicManager.GetInstance().PlayEffect(RGMusicManager.emEffect.Button);
        }

        void ClickExitLanguage() {
            if (!_awake) {
                return;
            }
            DispatchMessage(new Message {
                Command = ChooseLoginCommand.CloseLanguage
            });
            RGMusicManager.GetInstance().PlayEffect(RGMusicManager.emEffect.Button);
        }

        void ClickSwitchLanguage(string languageKey) {
            if (!_awake) {
                return;
            }
            DispatchMessage(new Message {
                Command = ChooseLoginCommand.SwitchLanguage,
                ExtraParams = ValueTuple.Create(languageKey),
            });
            RGMusicManager.GetInstance().PlayEffect(RGMusicManager.emEffect.Button);
        }

        void ClickExitBtn() {
            if (!_awake) {
                return;
            }
            DispatchMessage(new Message {
                Command = ChooseLoginCommand.ClickExitGame,
            });
        }

        void ClickInfoBtn() {
            if (!_awake) {
                return;
            }
            DispatchMessage(new Message {
                Command = ChooseLoginCommand.DealInfoBtn,
            });
        }

        void ClickCloseInfoBtn() {
            if (!_awake) {
                return;
            }
            DispatchMessage(new Message {
                Command = ChooseLoginCommand.HideInfoBtn,
            });
        }
        
        void DealExitBtn() {
            if (!_awake) {
                return;
            }
            DispatchMessage(new Message {
                Command = ChooseLoginCommand.DealExitBtn,
            });
        }

        protected override void OnShow(ChooseLoginData data) {
            // Debug.Log(new StackTrace().GetFrame(0).GetMethod().Name);
            gameObject.SetActive(true);
            transform.SetAsFirstSibling();
        }

        protected override void OnHide(ChooseLoginData data) {
            // Debug.Log(new StackTrace().GetFrame(0).GetMethod().Name);

            switch (data.hideType) {
                case ChooseLoginData.HideType.Back:
                    // DoBackHide(data);
                    break;
                case ChooseLoginData.HideType.Confirm:
                    // DoConfirmBack(data);
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }

            // Animation hide
            gameObject.SetActive(false);
        }

        protected override void OnRefresh(ChooseLoginData data) {
            // Debug.Log(new StackTrace().GetFrame(0).GetMethod().Name);
        }

        private bool ShowAgeBtn(ValueType command) {
            return command is ChooseLoginCommand.ClickAppleLogin ||
                   command is ChooseLoginCommand.ClickGoogleLogin ||
                   command is ChooseLoginCommand.ClickChannelLogin ||
                   command is ChooseLoginCommand.ClickChillyLogin ||
                   command is ChooseLoginCommand.ClickGuestLogin;
        }

        protected override void ProcessMessage(ValueType command, ChooseLoginData data) {
            if (!_awake) {
                void DelayCallMsg() {
                    AfterInit -= DelayCallMsg;
                    ProcessMessage(command, data);
                }

                AfterInit += DelayCallMsg;
                return;
            }
            
            switch (command) {
                case ChooseLoginCommand.InitView:
                    _loginPar.gameObject.SetActive(data.NeedShowLoginGroup);
                    _privacyTog.gameObject.SetActive(data.NeedShowPrivatePolicyToggle);
                    if (ChooseLoginController.NeedPrivatePolicyToggled) {
                        _privacyTog.GetComponent<RectTransform>().anchoredPosition = new Vector2(36, 110);
                    }
                    _healthyGameAdviceObject.gameObject.SetActive(data.NeedShowHealthyAdvice);
                    ShowOrHideLanguagePanel(true);
                    ShowOrHideExitBtn(true);
                    ShowOrHideInfoPanel(true);
                    HideLoginText();
                    break;
                case ChooseLoginCommand.ClickChannelLogin:
                    TAUtil.Track("click_login_btn","clogin_type","channel");
                    (_controller as ChooseLoginController)?.LoginByChannel();
                    break;
                case ChooseLoginCommand.ClickChillyLogin:
                    TAUtil.Track("click_login_btn","clogin_type","chilly");
                    (_controller as ChooseLoginController)?.LoginByChillyRoom();
                    break;
                case ChooseLoginCommand.ClickGoogleLogin:
                case ChooseLoginCommand.ClickAppleLogin:
                    if ((ChooseLoginCommand)command == ChooseLoginCommand.ClickGoogleLogin) {
                        TAUtil.Track("click_login_btn","clogin_type","google");
                        if (ChannelConfig.IsAllGP && NetworkUtil.NetworkCanUse()) {
                            (_controller as ChooseLoginController).ClickGoogleLoginCount ++;
                        }
                    } else if ((ChooseLoginCommand)command == ChooseLoginCommand.ClickAppleLogin) {
                        TAUtil.Track("click_login_btn","clogin_type","apple");
                    }
                    ChooseLoginData.LoginType loginType =
                        (ChooseLoginCommand)command == ChooseLoginCommand.ClickGoogleLogin
                            ? ChooseLoginData.LoginType.Google
                            : ChooseLoginData.LoginType.Apple;
                    (_controller as ChooseLoginController)?.LoginByLegacy(loginType);
                    break;
                case ChooseLoginCommand.ClickGuestLogin:
                    TAUtil.Track("click_login_btn","clogin_type","guest");
                    (_controller as ChooseLoginController)?.LoginByGuest();
                    break;
                case ChooseLoginCommand.ClickGoogleLoginButLoginFailedMultiTimes:
                    if (ChannelConfig.IsAllGP) {
                        UIWindowMutiTextDialog.ShowDialog(UICanvasRoot.Inst.transform,
                            new UIWindowMutiTextDialog.DialogParams() {
                                title = ScriptLocalization.Get("I_tip", "提示"),
                                content = I2.Loc.ScriptLocalization.Get("ui/google_loginfail_guide",
                                    "当前无法正常使用Google账号登录，请参考以下资料设置自动登录后重启游戏并重试；或使用凉屋游戏邮箱账号进行登录"),
                                showOption = UIWindowDialog.ShowOption.ShowConfirm |
                                             UIWindowDialog.ShowOption.ShowCancel,
                                confirmText = ScriptLocalization.Get("ui/set_goolge_auto_login", "设置Google账号自动登录"),
                                cancelText = ScriptLocalization.Get("ui/use_chilly_login", "使用凉屋游戏邮箱账号登录"),
                                lineSpace = 1.3f,
                                contentFontSize = 26,
                                contentMinFontSize = 26,
                                onConfirm = GotoGoogleLoginGuideURL,
                                onCancel = GoogleFailAndChooseChilly
                            }
                        );
                    }
                    break;
                case ChooseLoginCommand.AutoLogin:
                    (_controller as ChooseLoginController)?.AutoLogin();
                    break;
                case ChooseLoginCommand.ClickLanguage:
                    ShowOrHideLanguagePanel();
                    break;
                case ChooseLoginCommand.CloseLanguage:
                    ShowOrHideLanguagePanel(true);
                    break;
                case ChooseLoginCommand.SwitchLanguage:
                    UITitle.StaticSwitchLanguage(data.LanguageKey);
                    RefreshLogo();
                    ShowOrHideLanguagePanel(true);
                    break;
                case ChooseLoginCommand.DealExitBtn:
                    ShowOrHideExitBtn();
                    break;
                case ChooseLoginCommand.ClickExitGame:
                    UITitle.StaticBtnQuitClick();
                    break;
                case ChooseLoginCommand.ShowPhoneOrEmailBtn:
                    _chillyLoginBtn.gameObject.SetActive(true);
                    break;
                case ChooseLoginCommand.HideGuestBtn:
                    _guestLoginBtn.gameObject.SetActive(false);
                    break;
                case ChooseLoginCommand.ChangeLoginGroupActive:
                    _loginPar.gameObject.SetActive(data.NeedShowLoginGroup);
                    break;
                case ChooseLoginCommand.ChangePrivatePolicyToggleGameObjectActive:
                    _privacyTog.gameObject.SetActive(data.NeedShowPrivatePolicyToggle);
                    break;
                case ChooseLoginCommand.ChangeLoginTextActive:
                    if (data.NeedShowLoginText) {
                        ShowLoginText();
                    } else {
                        HideLoginText();
                    }
                    break;
                case ChooseLoginCommand.DealInfoBtn:
                    ShowOrHideInfoPanel();
                    break;
                case ChooseLoginCommand.HideInfoBtn:
                    ShowOrHideInfoPanel(true);
                    break;
            }

            if (ShowAgeBtn(command)) {
                UiUtil.InitAgeBtn(GetOpenParent());
            }
        }
        
        private Transform GetOpenParent() {
            var root = AccountSdkUIFacade.GetAccountSdkUIFacade().transform;
            for (var i = 0; i < root.childCount; i++) {
                if (root.GetChild(i).gameObject.activeSelf) {
                    return root.GetChild(i);
                }
            }

            return null;
        }

        private void RefreshLogo() {
            //刷新左上角title的显示
            if (ChannelConfig.IsAllVNM) {
                transform.Find("title/title_cn").gameObject.SetActive(false);
                transform.Find("title/title_en").gameObject.SetActive(false);
                transform.Find("title/title_vn").gameObject.SetActive(true);
            } else {
                transform.Find("title/title_cn").GetComponent<ShowInChannelConfig>().Refresh();
                transform.Find("title/title_en").GetComponent<ShowInChannelConfig>().Refresh();
            }
        }
        
        protected override bool OnBackButtonClicked() {
            if (_isLanguagePanelShow) {
                //语言界面响应返回键关闭
                ShowOrHideLanguagePanel(true);
            } if (_isInfoShow) {
                //个人信息界面响应返回键关闭
                ClickCloseInfoBtn();
            } else {
                //退出游戏响应返回键
                DealExitBtn();
            }

            return true;
        }
        
        void ShowOrHideInfoPanel(bool forceClose=false) {
            if (_isInfoShow|| forceClose) {
                _isInfoShow = false;
                _broadcast.GetComponent<RectTransform>().DOAnchorPosY(-1600, .25f);
            } else {
                _isInfoShow = true;
                _broadcast.RefreshOpen();
                _broadcast.GetComponent<RectTransform>().DOAnchorPosY(0, .25f);
                ShowOrHideExitBtn(true);
                ShowOrHideLanguagePanel(true);
            }
        }

        void ShowOrHideLanguagePanel(bool forceClose=false) {
            if (_isLanguagePanelShow|| forceClose) {
                _isLanguagePanelShow = false;
                _languageSequence?.Kill();
                _languageSequence = DOTween.Sequence().Append(_languagePanelRectTransform.DOAnchorPosY(-720, .25f));
            } else {
                _isLanguagePanelShow = true;
                _languageSequence?.Kill();
                _languageSequence = DOTween.Sequence().Append(_languagePanelRectTransform.DOAnchorPosY(0, .25f));
                ShowOrHideExitBtn(true);
            }
        }

        void ShowOrHideExitBtn(bool forceClose=false) {
            if (_isExitGameBtnShow|| forceClose) {
                _isExitGameBtnShow = false;
                _exitBtnSequence?.Kill();
                _exitBtnSequence = DOTween.Sequence().Append(_exitGameBtnRectTransform.DOAnchorPosY(-480, .25f))
                    .Join(_loginPar.DOAnchorPosY(60f,.25f));
            } else {
                _isExitGameBtnShow = true;
                _exitBtnSequence?.Kill();
                _exitBtnSequence = DOTween.Sequence().Append(_exitGameBtnRectTransform.DOAnchorPosY(-200, .25f))
                    .Join(_loginPar.DOAnchorPosY(-400,.25f));
            }
        }

        void ShowLoginText() {
            var loginText = I2.Loc.ScriptLocalization.Get("I_logining");
            _loginText.gameObject.SetActive(true);
            _loginText.text = loginText;
            var sperator = ".";
            StringBuilder sb = new StringBuilder();
            string[] data = Enumerable.Range(1, 3).Select(_ => {
                sb.Clear();
                sb.Append(loginText);
                for (int i = 0; i < _; i++) {
                    sb.Append(sperator);
                }
                return sb.ToString();
            }).ToArray();
            _loginingTextSequence?.Kill();
            _loginingTextSequence = DOTween.Sequence();
            for (int i = 0; i < 3; i++) {
                _loginingTextSequence.Append(_loginText.DOText(data[i],.3f));
            }
            _loginingTextSequence.SetLoops(-1);
        }

        void HideLoginText() {
            _loginingTextSequence?.Kill();
            _loginText.gameObject.SetActive(false);
        } 
        
        public static void ShowView(Transform transform) {
            var child = transform.Find(Viewname);
            if (child != null) {
                child.GetComponent<ChooseLoginController>().Show(Message.CommonCommand.Show);
                return;
            }

            var prefab = ResourcesUtil.Load<GameObject>("RGPrefab/UI/Login/ChooseLoginView.prefab");
            var ga = GameObject.Instantiate(prefab, transform);
            ga.name = Viewname;
            ga.GetComponent<ChooseLoginController>().Show(Message.CommonCommand.Show);
        }

        protected virtual void OnAfterInit() {
            AfterInit?.Invoke();
        }

        private void FixOperateText() {
            Text infoText = _healthyGameAdviceObject.transform.Find("info3").GetComponent<Text>();
            if (ChannelConfig.IsShangYou) {
                infoText.text = infoText.text.Replace("运营单位：深圳市凉屋游戏科技有限公司", string.Empty);
                infoText.transform.SetAsFirstSibling();
            } else if (ChannelConfig.Fn4399) {
                infoText.text = infoText.text.Replace("深圳市凉屋游戏科技有限公司", "广州市四三九九信息科技有限公司");
            }
        }

        private void GotoGoogleLoginGuideURL() {
            Application.OpenURL("https://support.google.com/googleplay/answer/9140426");
        }
        private void GoogleFailAndChooseChilly() {
            //拉起邮箱登录
            ChooseLoginController chooseLoginController = ((ChooseLoginController)_controller);
            if (null != chooseLoginController) {
                chooseLoginController.ShowPhoneOrEmailBtn();
                chooseLoginController.LoginByChillyRoom();
            }
        }

        private void TrackLegacyLoginWarn() {
            TAUtil.Track("click_login_warn",new Dictionary<string, object>() {
                {"register_type",(int)(_controller as ChooseLoginController).RegisterPlaneType},
                {"ever_sign_up",false},
                {"clogin_type",ChannelConfig.IsAllGP ? "google":"apple"},
            });
        }
    }
}