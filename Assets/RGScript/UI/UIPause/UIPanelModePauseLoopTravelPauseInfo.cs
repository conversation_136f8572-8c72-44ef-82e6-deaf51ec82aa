using I2.Loc;
using ModeLoopTravel.Scripts;
using UnityEngine.UI;

namespace RGScript.UI.UIPause {
    public class UIPanelModePauseLoopTravelPauseInfo : UIPanelModePauseBase {
        private Text _buffCountText;
        private Text _factorCountText;

        private void Awake() {
            // transform.GetComponent<RectTransform>().position = Vector3.up * 80;
            var buffCountDesc = transform.Find("model_content/buff_item/name_text").GetComponent<Text>();
            _buffCountText = transform.Find("model_content/buff_item/state_text").GetComponent<Text>();
            var factorCountDesc = transform.Find("model_content/factor_item/name_text").GetComponent<Text>();
            _factorCountText = transform.Find("model_content/factor_item/state_text").GetComponent<Text>();

            buffCountDesc.text = I2.Loc.ScriptLocalization.Get("mode_loop/buff_max", "天赋上限") + ":";
            factorCountDesc.text = I2.Loc.ScriptLocalization.Get("mode_loop/factor_count", "因子数量") + ":";
        }


        public override void ShowView(params object[] objects) {
            base.ShowView(objects);

            _buffCountText.text = $"{BattleData.data.buffs.Count}/{BattleData.data.buffCount}";
            var factors = BattleData.data.factors;
            int factorCount = 0;
            if (null != factors && factors.Contains(emBattleFactor.Loop)) {
                factorCount = factors.Count - 1; //扣除无尽因子本身
            }

            _factorCountText.text = factorCount.ToString();

            if (null != pause) {
                pause.transform.Find("title/Text").GetComponent<Text>().text = ScriptLocalization.Get("mode_loop/you_are_lazy", "喜欢偷懒?");    
            }
            
            
        }
    }
}