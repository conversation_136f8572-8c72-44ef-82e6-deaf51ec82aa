using RGScript.Data;
using RGScript.UI.Drawing;
using RGScript.Util;
using RGScript.Util.AssetBundle;
using Spine.Unity;
using System.IO;
using UI.Base;
using UIFramework;
using UnityEngine;
using UnityEngine.UI;

namespace RGScript.UI.Handbook.Skin.Widgets {
    public class UIHBHeroSkinWidget : BaseUIWidget {
        private const float Scale = 2.1f;
        private const float PosX = 0f;
        private const float PosY = -36f;
        
        [SerializeField] private int upHideY = 650;
        [SerializeField] private int downHideY = -300;
        [SerializeField] private Graphic[] graphics;
        [SerializeField] private Graphic[] frameGraphics;
        [SerializeField] private Color unlockColor;
        [SerializeField] private Color lockColor;
        
        private Text _nameText;
        private Text _unlockText;
        private Image _icon;
        private Image _mask;
        private Button _detailButton;
        private Transform _baseBottomTf;
        private GameObject _goldFrame;
        private GameObject _silverFrame;

        private emHero _heroType;
        private string _id;
        private int _skinIdx;
        private bool _unLock;
        private bool _isLoad;
        private ulong _bundleId;
        private UIHBSkinOutlineWindow _window;
        
        private HeroDrawingComp _drawingComp; //缓存，记得要销毁
        private RawImage _drawingRawImage;

        protected override void OnInit() {
            base.OnInit();

            var drawingP = transform.Find("Root/Middle/hero_drawing");
            _drawingRawImage = drawingP.Find("drawing_raw_image").GetComponent<RawImage>();
            _mask = drawingP.Find("mask").GetComponent<Image>();
            _baseBottomTf = transform.Find("BaseCellView_Bottom");
            _icon = drawingP.Find("Icon").GetComponent<Image>();
            _nameText = transform.Find("Root/Middle/F1/Name").GetComponent<Text>();
            _unlockText = transform.Find("Root/Middle/Unlock").GetComponent<Text>();
            _detailButton = _baseBottomTf.Find("Button").GetComponent<Button>();
            _goldFrame = transform.Find("Root/Middle/G").gameObject;
            _silverFrame = transform.Find("Root/Middle/S").gameObject;
            _detailButton.onClick.AddListener(OnClick);

            _skinIdx = -1;
        }

        private void OnClick() {
            if (!_unLock ) return;
            
            RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Button);
            _window.CreateWindowSkin(_heroType, _skinIdx);
        }

        protected override void OnStartUp(params object[] args) {
            base.OnStartUp(args);
            if (args.Length == 0) {
                Debug.LogError("args should not be empty");
                return;
            }
            
            _heroType = (emHero)args[0];
            _skinIdx = (int)args[1];

            _window = (UIHBSkinOutlineWindow)args[2];

            _unLock = DataUtil.GetSkinUnlock(_heroType, _skinIdx);
            _isLoad = false;

            _drawingRawImage.texture = _mask.sprite.texture;
            if (DataMgr.HeroDrawingsData.IsHeroSkinHasDrawing(_heroType, _skinIdx)) {
                _bundleId = AssetBundleRefCountLoader.Instance.LoadAssetBundle(GetPeekPath(), AfterLoad);
            }
        }

        private void AfterLoad() {
            if (!UIManager.Inst.HasUIViewOpened<UIHBSkinOutlineWindow>()) return;

            RectTransform imgRt = _drawingRawImage.GetComponent<RectTransform>();
            var size = imgRt.sizeDelta;
            if (ResourcesUtil.Load<Texture2D>($"RGTexture/ui/skin_peek/{_heroType}_{_skinIdx}.png") is { } texture2D) {
                _drawingRawImage.texture = texture2D;
                size.x = size.y * 2 / 3;  //比例为2:3
                imgRt.sizeDelta = size;
                imgRt.localScale = Vector3.one;
            } else {
                InitHeroDrawing(_heroType, _skinIdx);
                size.x = size.y;
                imgRt.sizeDelta = size;
                imgRt.localScale = Vector3.one * Scale;
            }

            foreach (var graphic in graphics) {
                graphic.color = _unLock ? Color.white : (graphic is Text ? Color.gray : new Color(.25f, .25f, .25f, 1));
            }

            foreach (var graphic in frameGraphics) {
                graphic.color = _unLock ? unlockColor : lockColor;
            }

            _drawingRawImage.GetComponent<RectTransform>().localPosition = new Vector3(PosX, PosY, 0);
            
            var sprite = RGSaveManager.Inst.GetHeroUISprite((int)_heroType, _skinIdx);
            _icon.sprite = sprite;
            _icon.SetNativeSize();
            _nameText.text = NameUtil.GetSkinName(_heroType, _skinIdx);
            _unlockText.gameObject.SetActive(!_unLock);
            _silverFrame.SetActive(StatisticData.data.GetPassLevel(_heroType.ToString() + _skinIdx.ToString()) == emPassGameLevel.Normal);
            _goldFrame.SetActive(StatisticData.data.GetPassLevel(_heroType.ToString() + _skinIdx.ToString()) == emPassGameLevel.Badass);
        }

        private void OnEnable() {
            if (_drawingComp != null) {
                _drawingComp.gameObject.SetActive(true);
            }
        }

        private void OnDisable() {
            if (_drawingComp != null) {
                _drawingComp.gameObject.SetActive(false);
            }
            UnLoadPeek();
        }
        
        private void Update() {
            if (!_isLoad) return;
            
            float ySpace = transform.parent.parent.localPosition.y + transform.localPosition.y;
            if ((ySpace > upHideY || ySpace < downHideY) && _drawingComp.gameObject.activeSelf) {
                _drawingComp.gameObject.SetActive(false);
            } else if ((ySpace < upHideY && ySpace > downHideY) && !_drawingComp.gameObject.activeSelf) {
                _drawingComp.gameObject.SetActive(true);
            }
        }

        void UnLoadPeek() {
            if (_skinIdx == -1) {
                return;
            }

            if (DataMgr.HeroDrawingsData.IsHeroSkinHasDrawing(_heroType, _skinIdx)) {
                AssetBundleRefCountLoader.Instance.UnloadAssetBundle(GetPeekPath(), _bundleId);
            }
        }
        
#if UNITY_EDITOR
        [Sirenix.OdinInspector.Button]
        public async void SaveToPNG() {
            string path = Application.dataPath + $"/RGTexture/ui/skin_peek/{_heroType}_{_skinIdx}.png";
            if (File.Exists(path)) {
                Debug.Log($"-- exists skip {_heroType}_{_skinIdx}.png");
                return;
            }
            
            if (_drawingComp != null) {
                await Cysharp.Threading.Tasks.UniTask.DelayFrame(0);
                var renderTexture = _drawingComp.texture;
                RenderTexture.active = renderTexture;
                Texture2D texture = new Texture2D(renderTexture.width, renderTexture.height, TextureFormat.RGBA32, false);
                texture.ReadPixels(new Rect(0, 0, renderTexture.width, renderTexture.height), 0, 0);
                texture.Apply();

                int w = 256;
                int h = 384;
                
                Texture2D nt = new Texture2D(w, h, TextureFormat.RGBAHalf, false);
                nt.SetPixels(texture.GetPixels(texture.width / 2 - w/2, texture.height / 2 - h/2, w, h));
                nt.Apply();
        
                byte[] bytes = nt.EncodeToPNG();
                System.IO.File.WriteAllBytes(path, bytes);
                RenderTexture.active = null;
                UnityEditor.AssetDatabase.Refresh();
            
                await Cysharp.Threading.Tasks.UniTask.DelayFrame(0);
                UnityEditor.AssetImporter.GetAtPath($"Assets/RGTexture/ui/skin_peek/{_heroType}_{_skinIdx}.png").assetBundleName = GetPeekPath();
                Debug.Log("Saved RenderTexture to " + path);
            }
        }
#endif

        private string GetPeekPath() {
            return DataUtil.GetSkinPeekAbName(_heroType, _skinIdx);
        }

        private void InitHeroDrawing(emHero hero, int skinIndex) {
            if (null == _drawingComp) {
                _drawingComp = HeroDrawingComp.CreateHeroDrawingComp(hero, skinIndex, OnDrawingLoad, false, true);
                _drawingComp.texture.filterMode = FilterMode.Point;
            } else {
                _drawingComp.LoadHeroDrawing(hero, skinIndex, OnDrawingLoad, true);
            }

            var key = new MallBuySkinSpritePlayerDataKey {
                hero = hero, skinIndex = skinIndex
            };
            var find = MallConfig.FindSkinDrawingCameraProperties(key, out var cameraProperties);
            if (find) {
                var pos = new Vector3(cameraProperties.localPos.x, cameraProperties.localPos.y, -10);
                _drawingComp.SetCameraProperties(pos, cameraProperties.size);
            } else {
                _drawingComp.SetCameraProperties(MallConfig.Config.defaultSkinDrawingCameraDeltaPos,
                    MallConfig.Config.defaultSkinDrawingCameraSize);
            }

            SetHeroDrawingCompGoPosition((int)hero, skinIndex);
        }

        private void OnDrawingLoad(RenderTexture renderTexture) {
            if (null == renderTexture || _drawingRawImage == null) {
                return;
            }

            _drawingRawImage.texture = renderTexture;
            _isLoad = true;
            RefreshSpeed();
        }

        public void SetHeroDrawingCompGoPosition(int x, int y) {
            var baseNum = 10000;
            _drawingComp.transform.position = new Vector3(baseNum + x * 32, baseNum + y * 32, 0);
        }

        private void RefreshSpeed() {
            foreach (Animator animator in _drawingComp.GetComponentsInChildren<Animator>()) {
                animator.speed = _unLock ? 1 : 0.05f;
            }
            foreach (var system in _drawingComp.GetComponentsInChildren<ParticleSystem>()) {
                system.playbackSpeed = _unLock ? 1 : 0.05f;
            }
            foreach (var animation in _drawingComp.GetComponentsInChildren<SkeletonAnimation>()) {
                animation.timeScale = _unLock ? 1 : 0.05f;
            }
            foreach (var animation in _drawingComp.GetComponentsInChildren<SpriteAnimation>()) {
                animation.timeScale = _unLock ? 1 : 0.05f;
            }
        }

        private void OnDestroy() {
            DestroyDrawingComp();
        }

        private void DestroyDrawingComp() {
            if (null != _drawingComp) {
                Destroy(_drawingComp.gameObject);
                _drawingComp = null;
            }
        }
    }
}