using RGScript.UI.Handbook.Enemy;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityCommon.UI;
using UnityEngine;
using UnityEngine.UI;

namespace RGScript.UI.Handbook {
    public class UIHBShareWindowDesc {
        public List<UIHBShareItemWidgetDesc> widgetDescs;
        public string title;
    }

    public class UIHBShareWindow : BaseUIView {
        public Button btnShare;
        public Button btnBack;
        private Transform _listRoot;
        private Animator _anim;
        private Text _titleText;

        private List<UIHBShareItemWidget> _widgets;
        private UIHBShareWindowDesc _desc;

        private static readonly int AnimatorParameterShow = Animator.StringToHash("show");

        public override void InitView() {
            base.InitView();
            pauseWhenShow = true;

            _titleText = transform.Find("title/text_name").GetComponent<Text>();
            _anim = transform.GetComponent<Animator>();
            _listRoot = transform.Find("panel/List/LayoutGroup");
            _widgets = new List<UIHBShareItemWidget>();
        }

        public override void ShowView(params object[] objects) {
            base.ShowView(objects);
            if (objects.Length == 0) {
                Debug.LogError("args should not be empty");
                return;
            }

            _desc = (UIHBShareWindowDesc)objects[0];
            _titleText.text = _desc.title;

            AddButtonListeners();

            _anim.SetBool(AnimatorParameterShow, true);

            InitRow();
        }

        private void AddButtonListeners() {
            btnShare.onClick.AddListener(OnClickShare);
            btnBack.onClick.AddListener(OnClick_Close);
        }

        private void RemoveButtonListeners() {
            btnShare.onClick.RemoveListener(OnClickShare);
            btnBack.onClick.RemoveListener(OnClick_Close);
        }

        private void OnClickShare() {
            btnShare.gameObject.SetActive(false);
            StartCoroutine(CaptureCamera());
        }

        public override void HideView(params object[] objects) {
            base.HideView(objects);
            RemoveButtonListeners();
            _anim.SetBool(AnimatorParameterShow, false);
        }

        IEnumerator CaptureCamera() {
            btnBack.gameObject.SetActive(false);
            yield return new WaitForEndOfFrame();
            // int t_width = (int)(1.0f * 400 / 1280 * Screen.width);
            // int t_height = (int)(1.0f * 600 / 720 * Screen.height);
            // int t_x = (Screen.width - t_width) / 2;
            // int t_y = (Screen.height - t_height) / 2;
            // Rect rect = new Rect(t_x, t_y, t_width, t_height);
            Rect rect = new Rect(0, 0, Screen.width, Screen.height);
            Texture2D screenShot = new Texture2D((int)rect.width, (int)rect.height, TextureFormat.RGB24, false);
            screenShot.ReadPixels(rect, 0, 0); // 注：这个时候，它是从RenderTexture.active中读取像素  
            screenShot.Apply();
#if UNITY_EDITOR
            byte[] byt = screenShot.EncodeToPNG();
            File.WriteAllBytes(Application.dataPath + "/vvv.png", byt);
#endif
            btnBack.gameObject.SetActive(true);
            NativeShare.Init();
            NativeShare.ShareTexture(screenShot);
        }

        private void InitRow() {
            int dataCount = _desc.widgetDescs.Count;
            var widgetProto =
                ResourcesUtil.Load<GameObject>("RGPrefab/UI/Handbook/Widgets/widget_hb_share_item.prefab");

            if (dataCount > _widgets.Count) {
                var needCount = dataCount - _widgets.Count;
                for (int i = 0; i < needCount; i++) {
                    var widget = Instantiate(widgetProto, _listRoot).GetComponent<UIHBShareItemWidget>();
                    RegisterUIWidget(widget);
                    _widgets.Add(widget);
                    widget.gameObject.SetActive(true);
                }
            } else if (dataCount < _widgets.Count) {
                for (int i = dataCount; i < _widgets.Count; i++) {
                    _widgets[i].gameObject.SetActive(false);
                }
            }

            for (int i = 0; i < dataCount; i++) {
                if (i < 0 || i >= _widgets.Count) {
                    Debug.LogError($"i is {i} count {_widgets.Count}");
                }

                var widget = _widgets[i];
                widget.gameObject.SetActive(true);
                widget.StartUp(_desc.widgetDescs[i]);
            }
        }
    }
}