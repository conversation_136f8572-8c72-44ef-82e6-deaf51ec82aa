using Activities;
using I2.Loc;
using RGScript.Data;
using UnityEngine;

namespace RGScript.UI.Handbook.Weapon {
    public class HBWeaponExhibitionData {
        public Sprite sprite;
        public string weaponID;
        public string name;
        public emPassGameLevel passGameLevel;
        public int obtainCount;
        public bool unlocked;
        public bool showInfo;
        public bool isMythicWeapon;
        public GameObject forgeObject;

        public HBWeaponExhibitionData(string weaponName) {
            var obtainWeaponName = RGWeapon.WeaponNameNormalized(weaponName);
            obtainCount = StatisticData.data.GetObtainTime(obtainWeaponName);
            if (obtainCount > 9999) { obtainCount = 9999; }

            forgeObject = ResourcesUtil.LoadWeapon(weaponName);
            weaponID = weaponName;
            name = ScriptLocalization.Get("weapon/" + weaponName);

            passGameLevel = HandbookWeaponData.GetWeaponPassLevel(obtainWeaponName);

            unlocked = HandbookWeaponData.IsUnlocked(obtainWeaponName);
            showInfo = obtainCount > 0;
            isMythicWeapon = ActivityUtil.IsMythicWeapon(weaponName);
            if (isMythicWeapon) {
                showInfo |= unlocked;
            }

            if (forgeObject == null) {
                sprite = null;
                Debug.LogError($"[HBWeaponExhibitionData] 找不到武器 {weaponName} 的贴图");
            } else {
                sprite = forgeObject.transform.GetChild(0).GetComponent<SpriteRenderer>().sprite;
            }
        }

        public string GetShowName() {
            return unlocked ? name : "???";
        }


        public string GetShowInfo() {
            if (showInfo) {
                if (isMythicWeapon) { return ""; }

                return ScriptLocalization.Get("I_get") + " " + obtainCount;
            }

            return ScriptLocalization.Get("I_did_not_found");
        }

        public Color GetShowColor() {
            return showInfo ? Color.white : Color.black;
        }
    }
}