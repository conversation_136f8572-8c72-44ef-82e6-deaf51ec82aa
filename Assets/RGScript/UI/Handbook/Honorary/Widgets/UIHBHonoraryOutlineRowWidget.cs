using System.Collections.Generic;
using UI.Base;
using UnityEngine;

namespace RGScript.UI.Handbook.Honorary.Widgets {
    public class UIHBHonoraryOutlineRowWidget : BaseUIWidget {
        private List<cfg.progress.HonoraryTitle> _configs;

        // 4 或 5个一行
        private List<UIHBHonoraryTitleWidget> _widgets;

        private Transform _listRoot;

        protected override void OnInit() {
            base.OnInit();
            // 最多5个
            _widgets = new List<UIHBHonoraryTitleWidget>(5);
            _listRoot = transform.Find("Bg/LayoutGroup");
        }

        protected override void OnStartUp(params object[] args) {
            base.OnStartUp(args);
            if (args.Length == 0) {
                Debug.LogError("args should not be empty");
                return;
            }

            _configs = (List<cfg.progress.HonoraryTitle>)args[0];


            int dataCount = _configs.Count;
            var widgetProto =
                ResourcesUtil.Load<GameObject>("RGPrefab/UI/Handbook/Widgets/widget_hb_honorary_title.prefab");
            if (dataCount > _widgets.Count) {
                var needCount = dataCount - _widgets.Count;
                for (int i = 0; i < needCount; i++) {
                    var widget = Instantiate(widgetProto, _listRoot).GetComponent<UIHBHonoraryTitleWidget>();
                    RegisterUIWidget(widget);
                    _widgets.Add(widget);
                    widget.gameObject.SetActive(true);
                }
            } else if (dataCount < _widgets.Count) {
                for (int i = dataCount; i < _widgets.Count; i++) {
                    _widgets[i].gameObject.SetActive(false);
                }
            }

            for (int i = 0; i < dataCount; i++) {
                if (i < 0 || i >= _widgets.Count) {
                    Debug.LogError($"i is {i} count {_widgets.Count}");
                }

                var widget = _widgets[i];
                var config = _configs[i];
                widget.gameObject.SetActive(true);
                widget.StartUp(config);
            }
        }

        protected override void OnClear() {
            base.OnClear();
            foreach (var widget in _widgets) {
                widget.Clear();
            }
        }
    }
}