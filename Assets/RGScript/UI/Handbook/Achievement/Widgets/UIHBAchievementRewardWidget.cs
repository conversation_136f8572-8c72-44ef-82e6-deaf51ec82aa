using cfg.achievement;
using RGScript.Data;
using System.Collections.Generic;
using UI.Base;
using UIFramework;
using UnityEngine;
using UnityEngine.UI;

namespace RGScript.UI.Handbook.Achievement.Widgets {
    public class UIHBAchievementRewardWidget : BaseUIWidget {
        private AchievementRewards _config;

        private Transform _root;

        // private RectTransform _starRoot;
        // private GameObject _starProto;
        // private List<GameObject> _stars;
        private UIHBAchievementChestWidget _chestWidget;

        private int _id;

        protected override void OnInit() {
            base.OnInit();
            _root = transform.Find("Root");
            _chestWidget = _root.Find("ChestWidget").GetComponent<UIHBAchievementChestWidget>();
            // _starRoot = _root.Find("StarRoot").GetComponent<RectTransform>();
            // _starProto = _starRoot.Find("StarProto").gameObject;
            // _starProto.SetActive(false);
            // _stars = new List<GameObject>(4);
        }

        protected override void OnRegisterEvent() {
            base.OnRegisterEvent();
            SimpleEventManager.AddEventListener<AchievementRewardRefreshEvent>(OnAchievementRewardRefreshEvent);
        }

        protected override void OnUnRegisterEvent() {
            base.OnUnRegisterEvent();
            SimpleEventManager.RemoveListener<AchievementRewardRefreshEvent>(OnAchievementRewardRefreshEvent);
        }

        private void OnAchievementRewardRefreshEvent(AchievementRewardRefreshEvent e) {
            if (_config != null && e.groupId == _id) {
                RefreshChestWidget(DataMgr.AchievementData.GroupConfig[e.groupId]);
            }
        }

        private void RefreshStar(GameObject star, bool light) {
            star.transform.Find("Light").gameObject.SetActive(light);
            star.transform.Find("Gray").gameObject.SetActive(!light);
        }

        protected override void OnStartUp(params object[] args) {
            base.OnStartUp(args);
            if (args.Length < 1) {
                Debug.LogError("args count not meet requirement.");
                return;
            }

            _config = (AchievementRewards)args[0];

            RegisterUIWidget(_chestWidget);

            if (args[1] is AchievementGroups groupConfig) {
                InitGroupReward(groupConfig);
                _id = groupConfig.Id;
            } else {
                _id = -1;
            }
        }

        private void InitGroupReward(AchievementGroups groupConfig) {
            bool isCompleteGroup = DataMgr.AchievementData.groupCompleteSet.Contains(groupConfig.Id);
            int completeCount = 0;
            int totalCount = groupConfig.Achievements.Count;
            foreach (int achievementId in groupConfig.Achievements) {
                bool unlock = AchievementData.HasUnlockAchievement(achievementId);
                if (unlock) {
                    completeCount++;
                }
            }

            // _starRoot.gameObject.SetActive(true);
            // RefreshStars(completeCount, totalCount);

#if UNITY_EDITOR
            if ((completeCount < totalCount && isCompleteGroup) || (completeCount == totalCount && !isCompleteGroup)) {
                Debug.LogError(
                    $"成就组id {groupConfig.Id} {groupConfig.Name} 数据有问题, isComplete: {isCompleteGroup}, completeCount {completeCount}, totalCount {totalCount}");
            }
#endif

            RefreshChestWidget(groupConfig);
        }

        private void RefreshChestWidget(AchievementGroups groupConfig) {
            var canGetReward = DataMgr.AchievementData.CanPlayerGetGroupReward(groupConfig.Id, out _);

            var newDesc = new UIHBAchievementChestDesc {
                onOpen = widget => { OpenShowRewardWindow(groupConfig); },
                showChestIndex = groupConfig.Reward_Ref.UiChestIndex,
                result = canGetReward,
                alwaysCanClick = true,
                runDefault = false
            };

            _chestWidget.Clear();
            _chestWidget.StartUp(newDesc);
        }

        // private void RefreshStars(int count, int totalCount) {
        //     int dataCount = totalCount;
        //     var widgetProto = _starProto;
        //
        //     var starCount = _stars.Count;
        //     if (dataCount > starCount) {
        //         for (int i = 0; i < (dataCount - starCount); i++) {
        //             var star = Instantiate(widgetProto, _starRoot);
        //             _stars.Add(star);
        //             star.gameObject.SetActive(true);
        //         }
        //     } else if (dataCount < starCount) {
        //         for (int i = dataCount; i < _stars.Count; i++) {
        //             _stars[i].gameObject.SetActive(false);
        //         }
        //     }
        //
        //     for (int i = 0; i < dataCount; i++) {
        //         var star = _stars[i];
        //         RefreshStar(star, i < count);
        //     }
        // }

        private void OpenShowRewardWindow(AchievementGroups groupConfig) {
            var rewards = groupConfig.Reward_Ref.RewardList;
            if (rewards.Count == 0) {
                Debug.LogError($"groupId {groupConfig.Id} 没有奖励");
                return;
            }

            UIShowRewardDesc desc = new UIShowRewardDesc {
                titleKey = groupConfig.Name,
                rewards = rewards,
                onReceive = () => {
                    RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Button);
                    DataMgr.AchievementData.GetGroupReward(groupConfig.Id);
                    SimpleEventManager.Raise(new AchievementRewardRefreshEvent(groupConfig.Id));
                },
                onClose = null,
                canGetReward = () =>
                    DataMgr.AchievementData.CanPlayerGetGroupReward(groupConfig.Id, out _) == GetRewardResult.Success,
                hasGetReward = () =>
                    DataMgr.AchievementData.CanPlayerGetGroupReward(groupConfig.Id, out _) ==
                    GetRewardResult.HasGetReward
            };
            RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Button);

            UIManager.Inst.OpenUIView<UIShowRewardWindow>("Handbook/window_hb_achievement_group_reward",
                new object[] { desc });
        }
    }
}