using cfg.achievement;
using I2.Loc;
using RGScript.Data;
using RGScript.UI.Handbook.Achievement.Widgets;
using System.Collections.Generic;
using UIFramework;
using UnityCommon.UI;
using UnityEngine;
using UnityEngine.UI;

namespace RGScript.UI.Handbook.Achievement {
    public class UIHBAchievementGroupsWindow : BaseUIView {
        public Button btnBack;
        public Button btnClose;
        private Slider _slider;
        private Text _textProgress;
        private Text _textTitle;
        private Transform _chestRoot;
        private UIHBAchievementGroupsListWidget _listWidget;
        private AchievementCategories _config;
        private Toggle _toggleShowPoint;


        public override void InitView() {
            base.InitView();
            useMask = true;
            pauseWhenShow = true;

            _listWidget = transform.Find("body/bg/GroupList").GetComponent<UIHBAchievementGroupsListWidget>();
            _slider = transform.Find("body/Slider").GetComponent<Slider>();
            _textProgress = transform.Find("body/ProgressTitle/TxtProgress").GetComponent<Text>();
            _textTitle = transform.Find("body/title/text_name").GetComponent<Text>();
            _chestRoot = transform.Find("body/Slider/ChestRoot");
            _toggleShowPoint = transform.Find("body/Toggle").GetComponent<Toggle>();

            RegisterUIWidget(_listWidget);
            _toggleShowPoint.onValueChanged.AddListener(OnClickToggle);
        }

        private void OnClickToggle(bool isOn) {
            DataMgr.AchievementData.showPointIcon = !isOn;
            SimpleEventManager.Raise(AchievementRefreshPointIconEvent.UseCache());
        }

        public override void ShowView(params object[] args) {
            base.ShowView(args);
            if (args.Length == 0) {
                Debug.LogError("args should not be empty");
                return;
            }

            AddButtonListeners();

            _config = (AchievementCategories)args[0];
            RefreshUI();
        }

        private void OnStartLoadSceneEvent(StartLoadSceneEvent e) {
            OnClick_Close();
        }

        private void OnUpdateAchievementDataEvent(UpdateAchievementDataEvent e) {
            if (!UIManager.Inst.HasUIViewOpened<UIHBAchievementGroupsWindow>()) {
                return;
            }

            RefreshUI();
        }

        private void RefreshUI() {
            InitChestWidgets(_config);
            _listWidget.StartUp(_config.Groups_Ref);
            _textTitle.text = ScriptLocalization.Get(_config.Name, $"没有本地化：{_config.Name}");
            var totalPoint = DataMgr.AchievementData.categoryTotalPointDict[_config.Id];
            DataMgr.AchievementData.categoryPointsDict.TryGetValue(_config.Id, out var point);
            var percentage = (float)point / totalPoint;
            percentage = Mathf.Clamp01(percentage);
            _slider.value = percentage;
            _textProgress.text = $"{point}/{totalPoint}";
            _toggleShowPoint.isOn = DataMgr.AchievementData.showPointIcon;
        }

        private void AddButtonListeners() {
            btnBack.onClick.AddListener(OnClick_Close1);
            btnClose.onClick.AddListener(OnClickCloseFamily);
        }

        private void RemoveButtonListeners() {
            btnBack.onClick.RemoveListener(OnClick_Close1);
            btnClose.onClick.RemoveListener(OnClickCloseFamily);
        }

        private void OnClick_Close1() {
            base.OnClick_Close();
            RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Button);
        }

        public override void HideView(params object[] objects) {
            base.HideView(objects);
            RemoveButtonListeners();
            _listWidget.Clear();
        }

        protected override void OnRegisterEvent() {
            base.OnRegisterEvent();
            SimpleEventManager.AddEventListener<StartLoadSceneEvent>(OnStartLoadSceneEvent);
            SimpleEventManager.AddEventListener<UpdateAchievementDataEvent>(OnUpdateAchievementDataEvent);
        }

        protected override void OnUnRegisterEvent() {
            base.OnUnRegisterEvent();
            SimpleEventManager.RemoveListener<StartLoadSceneEvent>(OnStartLoadSceneEvent);
            SimpleEventManager.RemoveListener<UpdateAchievementDataEvent>(OnUpdateAchievementDataEvent);
        }

        private List<UIHBAchievementChestWidget> _widgets;

        private void InitChestWidgets(AchievementCategories categoryConfig) {
            if (_widgets == null) {
                _widgets = new List<UIHBAchievementChestWidget>(4);
            }

            int dataCount = categoryConfig.RewardList.Count;

            var widgetCount = _widgets.Count;
            if (dataCount > widgetCount) {
                for (int i = 0; i < (dataCount - widgetCount); i++) {
                    var widget = InstantiateAndRegisterUIWidget<UIHBAchievementChestWidget>(
                        "Handbook/Widgets/widget_hb_achievement_chest", _chestRoot);
                    _widgets.Add(widget);
                }
            } else if (dataCount < widgetCount) {
                for (int i = dataCount; i < _widgets.Count; i++) {
                    _widgets[i].gameObject.SetActive(false);
                }
            }

            for (int i = 0; i < dataCount; i++) {
                var chestWidget = _widgets[i];
                var configReward = categoryConfig.RewardList[i];

                var canGetReward = DataMgr.AchievementData.CanPlayerGetCategoryReward(categoryConfig.Id,
                    configReward.Step, out _,
                    out _);

                var newDesc = new UIHBAchievementChestDesc {
                    onOpen = widget => {
                        RGMusicManager.Inst.PlayEffect(RGMusicManager.emEffect.Coin);
                        DataMgr.AchievementData.GetCategoryReward(categoryConfig.Id, configReward.Step);
                        RefreshChestWidgets(categoryConfig);
                    },
                    showChestIndex = configReward.Reward_Ref.UiChestIndex,
                    result = canGetReward
                };
                chestWidget.Clear();
                chestWidget.StartUp(newDesc);

                var width = _slider.gameObject.GetComponent<RectTransform>().sizeDelta.x;
                var widgetRt = chestWidget.gameObject.GetComponent<RectTransform>();
                widgetRt.SetAnchor(AnchorPresets.MiddleLeft);
                widgetRt.SetPivot(PivotPresets.MiddleLeft);
                widgetRt.sizeDelta = Vector2.one;
                widgetRt.localScale = new Vector3(57.6f, 59.2f, 1);
                var totalPoint = DataMgr.AchievementData.categoryTotalPointDict[_config.Id];
                var percentage = (float)configReward.Step / totalPoint;
                percentage = Mathf.Clamp01(percentage);
                widgetRt.anchoredPosition = new Vector2(width * percentage, -40.6f);
            }
        }

        private void RefreshChestWidgets(AchievementCategories categoryConfig) {
            for (int i = 0; i < categoryConfig.RewardList.Count; i++) {
                CategoryReward reward = categoryConfig.RewardList[i];
                var canGetReward = DataMgr.AchievementData.CanPlayerGetCategoryReward(categoryConfig.Id,
                    reward.Step, out _,
                    out _);
                _widgets[i].RefreshUnlockState(canGetReward);
            }
        }
    }
}