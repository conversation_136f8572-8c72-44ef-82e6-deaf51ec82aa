using Activities.Dream.Scripts;
using ChillyRoom.Services.Core.Libs;
using System;
using System.Collections.Generic;
using ChillyRoom.Utils;
using Com.LuisPedroFonseca.ProCamera2D;
using I2.Loc;
using ModeSeason.ComboGun.UI.Windows;
using MuseumUnlock;
using RGScript.Config.Manager;
using RGScript.Data;
using RGScript.Other.InputControl;
using RGScript.UI.ChooseHero;
using RGScript.UI.ChoosePet;
using RGScript.UI.MVC;
using Sirenix.OdinInspector;
using SoulKnight.Runtime.Item;
using System.Globalization;
using UIFramework;
using UnityCommon.UI;
using UnityEngine;
using UnityEngine.UI;
using Util.TAUtil;

#if UNITY_SWITCH
using nn.hid;
#endif

public class UIChoseHero : BaseUIView {
    [HideInInspector]
    public Animator anim;

    public bool awake {
        get { return Active; }
        private set { Active = value; }
    }

    [System.Serializable]
    public struct WeaponIcon {
        public Sprite icon;
        public Sprite[] iconUpdateds;
        public int angle;
    }

    public enum emMode { <PERSON>, <PERSON>, Pet, Mech }

    internal GameObject[] _hero_list;

    internal static Dictionary<int, RGController> HeroList => MapManagerHall.Inst.Controllers;

    public Sprite[] skill_icons;
    public Sprite[] buff_sprite;
    public WeaponIcon[] weapon_icon_infos;
    public AudioClip levelup_clip;
    public AudioClip start_clip;
    public Vector3 max_buff_position;
    public Vector3 max_weapon_position;
    Transform _partnerTR;
    UIHeroInfo hero_info;
    UILevelBar level_bar;
    Transform temp_target;
    Transform panel_right_down;
    Transform skillList;
    int character_count;
    int char_index = 0;
    int _skin_index;

    int skin_index {
        get {
            return _skin_index;
        }
        set {
            _skin_index = value;
        }
    }

    public emMode Mode { get; private set; } = emMode.None;

    //int skin_index = 0;
    Text hero_name;
    Text skin_subtitle;
    Text skill_name;
    Text skill_info;
    Text skill_info_x;
    Image skill_icon;
    Image buff_icon;
    Image weapon_icon;
    Transform skin_subtitle_background;
    bool skin_mode = false;
    bool first_select = true;
    UIBtnMaskDown ui_mask_down;
    Vector3 playerPosition;
    GameObject selectHeroroot;

    GameObject selectHerDef;

    //
    GameObject uiUpDown;

    bool cameraReturned;
    private GameObject countdownRoot;
    private bool NeedShowCountdown => GameUtil.IsRemoteGame() && MultiGameManager.Inst.OpenGame;

    public override void Awake() {
        base.Awake();
        character_count = (int)emHero.Count;
        // uiOperationMode = UIOperationMode.UISimpleOp;
        if (UnityEngine.SceneManagement.SceneManager.GetActiveScene().name == "HeroRoom") {
            temp_target = GameObject.Find("/room").transform;
            anim = transform.GetComponent<Animator>();
            hero_info = transform.Find("ui_left").GetComponent<UIHeroInfo>();
            level_bar = transform.Find("star_bar").GetComponent<UILevelBar>();
            hero_name = transform.Find("mask_up/Layout/text_name").GetComponent<Text>();
            skin_subtitle = transform.Find("mask_up/Layout/text_name/text_subtitle").GetComponent<Text>();
            skin_subtitle_background = transform.Find("mask_up/Layout/text_name/background");
            skill_name = transform.Find("ui_rigjt/title").GetComponent<Text>();
            skill_info = transform.Find("ui_rigjt/info").GetComponent<Text>();
            skill_info_x = transform.Find("ui_rigjt/infox").GetComponent<Text>();
            skill_icon = transform.Find("ui_rigjt/icon").GetComponent<Image>();
            buff_icon = transform.Find("ui_left/buff").GetComponent<Image>();
            weapon_icon = transform.Find("ui_left/weapon/icon").GetComponent<Image>();
            ui_mask_down = transform.Find("mask_down").GetComponent<UIBtnMaskDown>();
            skillList = transform.Find("ui_rigjt/list_skill");
            skillList.GetChild(0).gameObject.SetActive(false);
            panel_right_down = transform.Find("ui_rigjt/panel/down");
            SetupHeroList();
            countdownRoot = transform.Find("count_down").gameObject;
            var btnFollower = transform.Find("mask_up/btn_follower");
            if (btnFollower) {
                btnFollower.GetComponent<Button>().onClick.RemoveListener(OnOpenFollowerClicked);
                btnFollower.gameObject.SetActive(false);
            }
            transform.Find("mask_up/Layout").GetComponent<RectTransform>().anchoredPosition = Vector2.zero;
            RefreshPartnerGo(false);
        } else {
            gameObject.SetActive(false);
        }


#if UNITY_SWITCH
        transform.Find("btn_group/btn_shop").gameObject.SetActive(false);

        InputManager.AddButtonTip(transform.Find("btn_group/btn_home"), "-").SetBrownColor();

        ShowMaskUpButton(true);

        var left = transform.Find("ui_left _skin/Button/Image");
        if (left != null) {
            left.gameObject.SetActive(false);
        }

        var right = transform.Find("ui_rigjt_skin/Button/Image");
        if (right != null) {
            right.gameObject.SetActive(false);
        }

        InputManager.AddButtonTip(transform.Find("ui_left _skin/Button"), NpadButton.L.ToString(), true);
        InputManager.AddButtonTip(transform.Find("ui_rigjt_skin/Button"), NpadButton.R.ToString(), true);

        InputManager.AddButtonTip(transform.Find("mask_down/btn_back"), InputManager.Inst.cancelKey.ToString()).SetBrownColor();
        InputManager.AddButtonTip(transform.Find("mask_down/btn_ok"), InputManager.Inst.submitKey.ToString()).SetBrownColor();
        InputManager.AddButtonTip(transform.Find("mask_down/btn_levelup"), NpadButton.X.ToString());

        //select hero
        if (UnityEngine.SceneManagement.SceneManager.GetActiveScene().name == "HeroRoom") {
            selectHerDef = GameObject.Find("Canvas_world").transform.Find("hero_btn/Button00").gameObject;
        }

        if (UnityEngine.SceneManagement.SceneManager.GetActiveScene().name == "HeroRoom") {
            selectHeroroot = new GameObject("selectHeroroot");
            InputManager.Inst.AddForce(selectHeroroot);
        }

        uiUpDown = GameObject.Instantiate(SwitchResourceManager.Inst.PrefabUiUpDown);
        uiUpDown.transform.parent = transform.Find("ui_rigjt").gameObject.transform;
        uiUpDown.transform.localPosition = new Vector3(-260 - 150, 135, 0);

        cameraReturned = true;
#endif

#if UNITY_SWITCH
        if (UnityEngine.SceneManagement.SceneManager.GetActiveScene().name == "HeroRoom") {
            UiNavigationManager.Inst.Select(selectHerDef, new Vector2(64, 64));
        }
#endif
    }

    public void RefreshPartnerGo(bool usePet) {
        if (RGGameSceneManager.Inst.partner_proto != null) {
            DestroyImmediate(RGGameSceneManager.Inst.partner_proto);
            RGGameSceneManager.Inst.partner_proto = null;
        }

        // 上次选择的数据
        (emPartner partner, string idxOrName) = RGSaveManager.Inst.GetLastPartner();
        
        var petIndex = -1;
        _partnerTR = GameObject.Find("partner").transform;
        if (usePet && partner == emPartner.Follower) {
            // 需要展示宠物，但是上次选择的是亲卫，则创建阿凉
            petIndex = 0;
            RGGameSceneManager.Inst.partner_proto = DataUtil.CreatePetProto();
        } else {
            RGGameSceneManager.Inst.partner_proto = DataUtil.CreateLastPartnerProto();
        }

        RGGameSceneManager.Inst.partner_proto.transform.parent = _partnerTR;
        RGGameSceneManager.Inst.partner_proto.transform.localPosition = Vector3.zero;
        
        var isPet = RGGameSceneManager.Inst.partner_proto.GetComponent<RGPetController>() != null;
        var petFoodSlot= RoomObjectManager.Inst.transform.Find("hall_slot/hall/function/slots/pet_food_slot");
        if (petFoodSlot != null) {
            petFoodSlot.gameObject.SetActive(isPet);
        }
        
        if (isPet) {
            if (petIndex < 0) {
                if (int.TryParse(idxOrName, out int idx)) {
                    petIndex = idx;
                }
            }
            RGGameSceneManager.Inst.partner_proto.GetComponent<RGPetController>().GrayBack();
            RGGameSceneManager.Inst.partner_proto.BroadcastMessage(nameof(RGPetController.OnSkinChanged), petIndex, SendMessageOptions.DontRequireReceiver);
        }
    }

    void ShowMaskUpButton(bool show) {
        //Mask up
        var left = transform.Find("mask_up/Layout/Left");
        if (left != null) {
            left.gameObject.SetActive(show);
        }

        var right = transform.Find("mask_up/Layout/Right");
        if (right != null) {
            right.gameObject.SetActive(show);
        }
    }
    
    private void OnOpenFollowerClicked() {
        BtnBack();
        UIManager.Inst.OpenUIView<UIComboGunFollowerWindow>(
            "ModeSeason/ComboGun/Prefabs/UI/window/window_cb_follower.prefab", 
            new object[] { emPageType.follower, "oldPet" }
        );
        var chooseHeroController = UICanvas.GetInstance().GetController<ChooseHeroController>();
        chooseHeroController.DispatchMessage(new Message {
            Command = ChooseHeroCommand.HideGroupButton
        });
    }

    void SetupHeroList() {
        Transform charactorRoot = GameObject.Find("characters").transform;
        _hero_list = new GameObject[charactorRoot.childCount];
        foreach (var ctrl in charactorRoot.GetComponentsInChildren<RGController>(true)) {
            _hero_list[(int)ctrl.GetHeroType()] = ctrl.gameObject;
        }
    }

#if UNITY_EDITOR
    [Button(Name = "截屏")]
    void TakeScreenShot() {
        ScreenCapture.CaptureScreenshot(Application.persistentDataPath + "/" + DateTime.Now.Unix() + ".png");
    }
#endif

    // Use this for initialization
    void Start() {
        //UICanvas.GetInstance().ShowGem();

        if (GameUtil.CompleteOptionalAdvTutorial() && !DataUtil.GetSkinUnlock(emHero.Knight, 2)) {
            DataUtil.SetSkinUnlock(emHero.Knight, 2, true);
            DataUtil.SetPetUnlock(emPet.Dog, true);
        }

#if UNITY_SWITCH
#else
        TutorialSequenceAbTest();
#endif
        PlayerSaveData.Inst.first_in_room += 1;
        PlayerSaveData.Save();
        NewPlayerTip();
        SelectTip();
    }

    #region TutorialSequenceAbTest

    private void TutorialSequenceAbTest() {
        var group = ABTest.GetTutorialSequenceTestGroup();
        switch (group) {
            case ABTestGroup.A:
                TutorialSequenceGroupA();
                break;
            case ABTestGroup.B:
                TutorialSequenceGroupB();
                break;
            case ABTestGroup.C:
                TutorialSequenceGroupC();
                break;
            default:
                TutorialSequenceGroupDefault();
                break;
        }
    }

    private void TutorialSequenceGroupA() {
        TutorialSequenceGroupDefault();
    }

    private void TutorialSequenceGroupB() {
        if (GameUtil.NeedNewPlayerTutorial() && !GameUtil.CompleteUseTicket()) {
            StartTicketTutorial();
            return;
        }

        if (!GameUtil.CompleteOptionalAdvTutorial()) {
            var uiTeaching = UIManager.Inst.OpenUIView<UITeaching>("ui_teaching");
            uiTeaching.current_teach = UITeaching.Teach.optional_adv_tutorial;

            var canvasTrans = UICanvas.GetInstance().transform;
            for (int i = 0; i < canvasTrans.childCount; i++) {
                var newPlayerFactorWindow = canvasTrans.GetChild(i);
                if (newPlayerFactorWindow.name == "new_player_factor_window") {
                    newPlayerFactorWindow.transform.SetAsLastSibling();
                }
            }

            bool needRestInfo = !GameUtil.InMultiGame();
            if (needRestInfo) {
                RGGameProcess.Inst.ReSetInfo(true, false);
            }
        } else if (GameUtil.NeedNewPlayerTutorial() && GameUtil.CompleteAdvTutorial() && !GameUtil.CompleteChangeSkin()) {
            var uiTeaching = UIManager.Inst.OpenUIView<UITeaching>("ui_teaching");
            uiTeaching.current_teach = UITeaching.Teach.change_skin;
        }
    }

    private void TutorialSequenceGroupC() {
        //新手初始游戏相关
        if (!GameUtil.CompleteNewPlayertoturial()) {
            var uiTeaching = UIManager.Inst.OpenUIView<UITeaching>("ui_teaching");
            uiTeaching.current_teach = UITeaching.Teach.chose_hero;
            var canvasTrans = UICanvas.GetInstance().transform;
            for (int i = 0; i < canvasTrans.childCount; i++) {
                var newPlayerFactorWindow = canvasTrans.GetChild(i);
                if (newPlayerFactorWindow.name == "new_player_factor_window") {
                    newPlayerFactorWindow.transform.SetAsLastSibling();
                }
            }

            bool needRestInfo = !GameUtil.InMultiGame();
            if (needRestInfo) {
                RGGameProcess.Inst.ReSetInfo(true, false);
            }
        } else if (GameUtil.NeedNewPlayerTutorial() && !GameUtil.CompleteUseTicket()) {
            StartTicketTutorial();
        } else if (PlayerSaveData.Inst.first_in_room == 2) {
            var uiTeaching = UIManager.Inst.OpenUIView<UITeaching>("ui_teaching");
            uiTeaching.current_teach = UITeaching.Teach.optional_adv_tutorial;
            var canvasTrans = UICanvas.GetInstance().transform;
            for (int i = 0; i < canvasTrans.childCount; i++) {
                var newPlayerFactorWindow = canvasTrans.GetChild(i);
                if (newPlayerFactorWindow.name == "new_player_factor_window") {
                    newPlayerFactorWindow.transform.SetAsLastSibling();
                }
            }

            bool needRestInfo = !GameUtil.InMultiGame();
            if (needRestInfo) {
                RGGameProcess.Inst.ReSetInfo(true, false);
            }
        } else if (GameUtil.NeedNewPlayerTutorial() && GameUtil.CompleteAdvTutorial() && !GameUtil.CompleteChangeSkin()) {
            var uiTeaching = UIManager.Inst.OpenUIView<UITeaching>("ui_teaching");
            uiTeaching.current_teach = UITeaching.Teach.change_skin;
        } 
    }

    private void TutorialSequenceGroupDefault() {
        //新手初始游戏相关
        if (!GameUtil.CompleteNewPlayertoturial() || !GameUtil.CompleteOptionalAdvTutorial()) {
            var uiTeaching = UIManager.Inst.OpenUIView<UITeaching>("ui_teaching");
            uiTeaching.current_teach = UITeaching.Teach.chose_hero;
            if (GameUtil.CompleteNewPlayertoturial()) {
                uiTeaching.current_teach = UITeaching.Teach.optional_adv_tutorial;
            }

            var canvasTrans = UICanvas.GetInstance().transform;
            for (int i = 0; i < canvasTrans.childCount; i++) {
                var newPlayerFactorWindow = canvasTrans.GetChild(i);
                if (newPlayerFactorWindow.name == "new_player_factor_window") {
                    newPlayerFactorWindow.transform.SetAsLastSibling();
                }
            }

            bool needRestInfo = !GameUtil.InMultiGame();
            if (needRestInfo) {
                RGGameProcess.Inst.ReSetInfo(true, false);
            }
        } else if (GameUtil.NeedNewPlayerTutorial() && GameUtil.CompleteAdvTutorial() && !GameUtil.CompleteChangeSkin()) {
            var uiTeaching = UIManager.Inst.OpenUIView<UITeaching>("ui_teaching");
            uiTeaching.current_teach = UITeaching.Teach.change_skin;
        } else if (GameUtil.NeedNewPlayerTutorial() && !GameUtil.CompleteUseTicket()) {
            StartTicketTutorial();
        }
    }

    #endregion
    
    void StartTicketTutorial() {
        var seq = new CommandUtil.CommandList(false);
        seq.Add(CommandUtil.CommandDuration.Delay(0.15f));
        seq.Add(CommandUtil.Command.WaitUntil(() => { 
            var hasFullScreenView = UIFramework.UIManager.Inst.HasFullScreenView();
            return !hasFullScreenView;
        }));
        seq.Add(TicketTutorialAbTest);
        RGGameProcess.StartCommand(seq);
    }

    #region TicketTutorialABTest

    private void TicketTutorialAbTest() {
        var uiTeaching = UIManager.Inst.OpenUIView<UITeaching>("ui_teaching");
        uiTeaching.current_teach = UITeaching.Teach.ticket;
        var group = ABTest.GetTicketTutorialTestGroup();
        switch (group) {
            case ABTestGroup.B:
                uiTeaching.teach_target_infos_map[UITeaching.Teach.ticket][0].canvas_path = "hero_btn/Button04";
                break;
            case ABTestGroup.C: {
                uiTeaching.teach_target_infos_map[UITeaching.Teach.ticket][0].canvas_path = "hero_btn/Button28";
                TeachTargetInfo[] infos = new TeachTargetInfo[3];
                infos[0] = new TeachTargetInfo {
                    canvas_name = "canvas_screen_space",
                    canvas_path = "ui_choose_hero/btn_group/vertical_bar/btn_switch",
                };
                infos[1] = uiTeaching.teach_target_infos_map[UITeaching.Teach.ticket][0];
                infos[2] = uiTeaching.teach_target_infos_map[UITeaching.Teach.ticket][1];
                uiTeaching.teach_target_infos_map[UITeaching.Teach.ticket] = infos;
                break;
            }
            case ABTestGroup.D: {
                TeachTargetInfo[] infos = new TeachTargetInfo[3];
                infos[0] = new TeachTargetInfo {
                    canvas_name = "canvas_screen_space",
                    canvas_path = "ui_choose_hero/btn_group/btn_hero_list",
                };
                // infos[1]为空。因为hero list中的指引由transform来指定位置而不是路径和下标，且UITeaching的Update会一直按照下标刷新位置，所以这里空置，且update中排出这个1下标的刷新。
                infos[2] = uiTeaching.teach_target_infos_map[UITeaching.Teach.ticket][1];
                uiTeaching.teach_target_infos_map[UITeaching.Teach.ticket] = infos;
                break;
            }
        }
    }

    #endregion

    private void AddKeyBinding() {
#if UNITY_SWITCH
        InputManager.Inst.AddKeyBinding(gameObject, InputManager.Inst.cancelKey, delegate { return awake; }, () => {
            BtnBack();
        });
        InputManager.Inst.AddKeyBinding(gameObject, NpadButton.X, delegate { return awake; }, () => {
            ui_mask_down.BtnLevelUp();
        });
        InputManager.Inst.AddKeyBinding(gameObject, InputManager.Inst.submitKey, delegate { return awake; }, () => {
            BtnConfirm();
        });
        InputManager.Inst.AddKeyBinding(gameObject, NpadButton.L, delegate { return awake; }, () => {
            OnDirection(KeyDirection.Left);
        });
        InputManager.Inst.AddKeyBinding(gameObject, NpadButton.R, delegate { return awake; }, () => {
            OnDirection(KeyDirection.Right);
        });
        InputManager.Inst.AddKeyBinding(gameObject, NpadButton.Up, delegate { return awake; }, () => {
            SkillToggleSelect((int) KeyDirection.Left);
        });
        InputManager.Inst.AddKeyBinding(gameObject, NpadButton.Down, delegate { return awake; }, () => {
            SkillToggleSelect((int) KeyDirection.Right);
        });
#endif
    }

    void RemoveBinding() {
#if UNITY_SWITCH
        InputManager.Inst.RemoveKeyBinding(gameObject);
#endif
    }

    internal bool backPressed; //此帧是否按下了隐藏按键,防止重复处理

    void Update() {
        backPressed = false;
#if UNITY_SWITCH
        if (InputManager.Inst.IsForceObject(selectHeroroot) && InputManager.Inst.GetButtonDown(NpadButton.Minus)) {
            BtnHomeClick();
        }

#else
        if (awake) {
            // if (InputControl.Inst.GetButtonDown("BtnA")) {
            //     BtnConfirm();
            // }
            
            if (InputManager.Inst.IsPassCancelKey() || InputControl.Inst.GetUIButtonDown("BtnB")) {
                backPressed = true;
                BtnBack();
            }
            
            // if (InputControl.Inst.GetButtonDown("BtnL")) {
            //     OnDirection(KeyDirection.Left);
            // }
            //
            // if (InputControl.Inst.GetButtonDown("BtnR")) {
            //     OnDirection(KeyDirection.Right);
            // }
        }
#endif
    }

    public override void OnJoystickButtonDown(JoystickButton button) {
        if (!awake) return;
        base.OnJoystickButtonDown(button);
        if (button == JoystickButton.LeftShoulder1) {
            OnDirection(KeyDirection.Left);
        } else if (button == JoystickButton.RightShoulder1) {
            OnDirection(KeyDirection.Right);
        } else if (button == JoystickButton.Confirm) {
            BtnConfirm();
        } else if (button == JoystickButton.Cancel) {
            backPressed = true;
            BtnBack();
        }
    }

    enum KeyDirection {
        Left = -1,
        Right = 1,
        None = 0
    }

    void OnDirection(KeyDirection dir) {
        if (Mode == emMode.None || !awake) return;

        if (skin_mode) {
            SkinBtnNext((int)dir);
        } else {
            if (Mode == emMode.Mech) {
            } else {
                OnDirChoseHero(dir);
                var chooseHeroController = UICanvas.GetInstance().GetController<ChooseHeroController>();
                chooseHeroController.Show(char_index);
            }
        }
    }

    void OnDirChoseHero(KeyDirection dir) {
        while (true) {
            char_index += (int)dir;
            if (char_index < 0) {
                char_index = character_count - 1;
            }

            if (char_index > character_count - 1) {
                char_index = 0;
            }

            var controller = MapManagerHall.Inst.Controllers[char_index];
            bool isCharUnlock = DataUtil.GetHeroUnlock((emHero)char_index);
            bool needSkip = char_index == (int)emHero.Robot && !isCharUnlock || //屏蔽机器人
                            char_index == (int)emHero.Officer && !isCharUnlock || //屏蔽未解锁的警官
                            CharactersLevelUpConfigManager.IsHeroChar(char_index) && !isCharUnlock || //屏蔽未解锁英雄角色
                            CharactersLevelUpConfigManager.IsHeroChar(char_index) && !controller.gameObject.activeInHierarchy; //屏蔽未可用但解锁的英雄角色 


            if (needSkip) {
                continue;
            }

            break;
        }
    }

    public void DoChoicePet() {
        var choosePetController = UICanvas.GetInstance().GetController<ChoosePetController>();
        if (choosePetController.IsShowing) {
            return;
        }

        (emPartner partner, string idxOrName) = RGSaveManager.Inst.GetLastPartner();
        var petIndex = partner == emPartner.Pet ? int.Parse(idxOrName) : 0;
        choosePetController.Show(petIndex);

        var chooseHeroController = UICanvas.GetInstance().GetController<ChooseHeroController>();
        chooseHeroController.DispatchMessage(new Message {
            Command = ChooseHeroCommand.HideGroupButton
        });
        
        SimpleEventManager.Raise(new ShowPetChoseEvent());
    }

    public void GetChoicePet() {
#if UNITY_SWITCH
        if (cameraReturned == false) return;
#endif
        if (!awake) {
            var chose_hero = GameObject.Find("/Canvas/ui_chose").GetComponent<UIChoseHero>();
            chose_hero.RefreshPartnerGo(true);
            
            Mode = emMode.Pet;
            skin_subtitle.gameObject.SetActive(false);
            skin_subtitle_background.gameObject.SetActive(false);
            ProCamera2D.Instance.RemoveAllCameraTargets();
            ProCamera2D.Instance.AddCameraTarget(_partnerTR, 1, 1, 0, new Vector2(0, 0.6f));
            ShowSkinUI();
            UIManager.Inst.OpenExistedUIView(this, false);

            (emPartner partner, string idxOrName) = RGSaveManager.Inst.GetLastPartner();
            skin_index = partner == emPartner.Pet ? int.Parse(idxOrName) : 0;
            hero_name.text = ScriptLocalization.Get("Pet_name_" + (skin_index));
            ui_mask_down.ShowBtnPet(skin_index);
            UICanvas.GetInstance().EndTempMessage();

            var chooseHeroController = UICanvas.GetInstance().GetController<ChooseHeroController>();
            chooseHeroController.DispatchMessage(new Message {
                Command = ChooseHeroCommand.HideGroupButton
            });

            var btnFollower = transform.Find("mask_up/btn_follower");
            if (btnFollower) {
                btnFollower.gameObject.SetActive(true);
                btnFollower.GetComponent<Button>().onClick.RemoveListener(OnOpenFollowerClicked);
                btnFollower.GetComponent<Button>().onClick.AddListener(OnOpenFollowerClicked);
            }

            transform.Find("mask_up/Layout").GetComponent<RectTransform>().anchoredPosition = new Vector2(0, -72.2f);

#if UNITY_SWITCH
            UiNavigationManager.Inst.SetLastSelectWithCurrent(selectHeroroot);
            UiNavigationManager.Inst.Select(null);
            InputManager.Inst.AddForce(gameObject);
            AddKeyBinding();
            ShowMaskUpButton(false);

            cameraReturned = false;
#endif
        }

        if (NeedShowCountdown) {
            countdownRoot.SetActive(true);
        }

        SimpleEventManager.Raise(new ShowPetChoseEvent());
    }

    public void SkinBtnNext(int value) {
        if (awake) {
            skin_subtitle.gameObject.SetActive(false);
            skin_subtitle_background.gameObject.SetActive(false);

            int max_skin_index = RGSaveManager.Inst.char_list[char_index].skin_list.Length;
            if (Mode == emMode.Pet) {
                max_skin_index = RGSaveManager.Inst.pet_list.Length;
            } else if (Mode == emMode.Mech) {
                max_skin_index = MechInfos.info.mechInfos.Count;
            }

            do {
                if (value > 0) {
                    skin_index += 1;
                    if (skin_index >= max_skin_index)
                        skin_index = 0;
                } else {
                    skin_index -= 1;
                    if (skin_index < 0)
                        skin_index = max_skin_index - 1;
                }
#if UNITY_EDITOR
                Debug.Log((emHero)char_index + ":" + skin_index);
#endif
            } while ((Mode == emMode.Hero && (RGSaveManager.Inst.char_list[char_index].skin_list[skin_index] == -3 ||
                                              RGSaveManager.Inst.char_list[char_index].skin_list[skin_index] == -8)) ||
                     (Mode == emMode.Pet && RGSaveManager.Inst.pet_list[skin_index].unlock_gem == -3 &&
                      !RGSaveManager.Inst.pet_list[skin_index].unlock) ||
                     (Mode == emMode.Mech && MechInfos.info.mechInfos[skin_index].isHidden &&
                      ItemData.data.GetBluePrintStatus(MechInfos.info.mechInfos[skin_index].unlockKey) ==
                      emBluePrintStatus.None) ||
                     (Mode == emMode.Mech &&
                      ActivityDreamManager.NeedHideCoinMech(MechInfos.info.mechInfos[skin_index].name)));

            if (Mode == emMode.Pet) {
                // todo: 兼容亲卫逻辑
                ui_mask_down.ShowBtnPet(skin_index);
                if (RGSaveManager.Inst.pet_list[skin_index].unlock) {
                    RGGameSceneManager.Inst.partner_proto.GetComponent<RGPetController>().GrayBack();
                } else {
                    RGGameSceneManager.Inst.partner_proto.GetComponent<RGPetController>().GetGray();
                }

                //-1 成就解锁或敬请期待
                //-2 隐藏皮肤
                //-3 特定渠道皮肤,正常需隐藏
                //-4 小鱼干商店获取
                bool petUnlocked = RGSaveManager.Inst.pet_list[skin_index].unlock;
                int unlockGem = RGSaveManager.Inst.pet_list[skin_index].unlock_gem;
                if (!petUnlocked && unlockGem == -1) {
                    if (!string.IsNullOrEmpty(ScriptLocalization.Get("Pet_name_" + (skin_index) + "_lock"))) {
                        hero_name.text = ScriptLocalization.Get("Pet_name_" + (skin_index) + "_lock");
                    } else {
                        hero_name.text = ScriptLocalization.Get("I_ComingSoon");
                    }
                } else if (!petUnlocked && unlockGem == -2) {
                    SkinBtnNext(value);
                } else if (!petUnlocked && unlockGem == -4) {
                    hero_name.text = ScriptLocalization.Get("multi_room_skin_ui_fish_chip_store");
                } else {
                    hero_name.text = ScriptLocalization.Get("Pet_name_" + (skin_index));
                }

                RGGameSceneManager.Inst.partner_proto.GetComponent<RGPetController>().anim.runtimeAnimatorController =
                    ResourcesUtil.LoadPetSkin(skin_index, 0);
                RGGameSceneManager.Inst.partner_proto.BroadcastMessage("OnSkinChanged", skin_index, SendMessageOptions.DontRequireReceiver);
            } else if (Mode == emMode.Hero) {
                CheckCharGray();
                ui_mask_down.ShowBtnCharSkin(char_index, skin_index);
                bool unlock = DataUtil.GetSkinUnlock((emHero)char_index, skin_index);
                if (!unlock && RGSaveManager.Inst.char_list[char_index].skin_list[skin_index] == -1) {
                    hero_name.text = ScriptLocalization.Get("I_ComingSoon");
                } else if (!unlock && RGSaveManager.Inst.char_list[char_index].skin_list[skin_index] == -4) {
                    hero_name.text = ScriptLocalization.Get("I_AchievementSkin");
                } else if (!unlock && RGSaveManager.Inst.char_list[char_index].skin_list[skin_index] == -5) {
                    hero_name.text = ScriptLocalization.Get("I_FragmentSkin");
                } else if (!unlock && RGSaveManager.Inst.char_list[char_index].skin_list[skin_index] == -6) {
                    hero_name.text = ScriptLocalization.Get("multi_room_skin_ui_fish_chip_store");
                } else if (!unlock && RGSaveManager.Inst.char_list[char_index].skin_list[skin_index] == -7) {
                    hero_name.text = MuseumUnlockConfig.config.GetSkinUnlockText((emHero)char_index, skin_index);
                } else {
                    string name = DataUtil.GetSkinName((emHero)char_index, skin_index);
                    hero_name.text = name;
                    string subtitle = DataUtil.GetSkinSubtitle((emHero)char_index, skin_index);
                    skin_subtitle.text = subtitle;
                    if (!string.IsNullOrEmpty(subtitle)) {
                        skin_subtitle.gameObject.SetActive(true);
                        skin_subtitle_background.gameObject.SetActive(true);
                    } else {
                        skin_subtitle.gameObject.SetActive(false);
                        skin_subtitle_background.gameObject.SetActive(false);
                    }
                }

                var tmp_hero_list = HeroList;
                tmp_hero_list[char_index].GetComponent<RGController>().anim.runtimeAnimatorController =
                    ResourcesUtil.LoadHeroSkin(char_index, skin_index);
                tmp_hero_list[char_index]
                    .BroadcastMessage("OnSkinChanged", skin_index, SendMessageOptions.DontRequireReceiver);
                SimpleEventManager.Raise(new ChoseSkinChangeSelection() {
                    skinIndex = skin_index,
                    hero = (emHero)char_index,
                    dir = value,
                });
                CommonStatistics.TrackSelectSkin((emHero)char_index, skin_index);
            } else if (Mode == emMode.Mech) {
                if (skin_mode) {
                    RefreshMech(skin_index);
                    ui_mask_down.ShowBtnMech(mech, skin_index, true);
                }
            }
        }
    }

    /// <summary>
    /// 红点
    /// </summary>
    void RefreshSkillNew() {
        int skillIndex = 2;
        if (skillList.childCount > skillIndex + 1) {
            //skillList的child包括了proto,故要+1
            var skillItem = skillList.GetChild(skillIndex + 1);
            string readTag = string.Format("spot_skill_{0}_{1}", (emHero)char_index, skillIndex);
            skillItem.Find("bg/red_point").GetComponent<UnReadSpot>().ResetReadTag(readTag);
        }
    }

    public void BtnBack() {
        TargetFinger.Clear();
        if (awake) {
            if (Mode == emMode.Mech) {
                if (skin_mode) {
                    RGGameSceneManager.GetInstance().controller.transform.position = playerPosition;
                    if (mech) {
                        Destroy(mech.gameObject);
                    }

                    if (mechDialog) {
                        mechDialog.OnClick_Cancel();
                    }

                    ProCamera2D.Instance.RemoveAllCameraTargets();
                    ProCamera2D.Instance.AddCameraTarget(RGGameSceneManager.GetInstance().controller.camera_focus, 1, 1,
                        0, new Vector2(0, 0f));
                    UICanvas.GetInstance().anim.SetBool("show", true);
                    UICanvas.GetInstance().anim.Play("show_ui", 0, 1);
                    HideUI();
                    RGMusicManager.GetInstance().PlayEffect(8);
#if UNITY_SWITCH
                    InputManager.Inst.RemoveForce(gameObject);
                    RemoveBinding();
#endif
                } else {
                    ShowSkinUI();
                    ui_mask_down.ShowBtnMech(mech, skin_index, true);
                    RGMusicManager.GetInstance().PlayEffect(8);
                }
            } else {
                skin_index = RGSaveManager.Inst.GetCurrentSkin((emHero)char_index);
                if (Mode == emMode.Pet) {
                    var chooseHeroController = UICanvas.GetInstance().GetController<ChooseHeroController>();
                    chooseHeroController.DispatchMessage(new Message {
                        Command = ChooseHeroCommand.ShowGroupButton
                    });

                    var chose_hero = GameObject.Find("/Canvas/ui_chose").GetComponent<UIChoseHero>();
                    chose_hero.RefreshPartnerGo(false);
                    
                    (emPartner partner, string idxOrName) = RGSaveManager.Inst.GetLastPartner();
                    if (partner == emPartner.Pet) {
                        var pIdx = int.Parse(idxOrName, CultureInfo.InvariantCulture);
                        RGGameSceneManager.Inst.partner_proto.GetComponent<RGPetController>().GrayBack();
                        RGGameSceneManager.Inst.partner_proto.BroadcastMessage("OnSkinChanged", pIdx, SendMessageOptions.DontRequireReceiver);
                    }
#if UNITY_SWITCH
                    UiNavigationManager.Inst.Select(UiNavigationManager.Inst.GetLastSelect(selectHeroroot), new Vector2(64, 64));
                    InputManager.Inst.RemoveForce(gameObject);
                    RemoveBinding();
#endif
                } else {
                    if (skin_mode) {
                        hero_name.text = DataUtil.GetSkinName((emHero)char_index, skin_index);

                        string subtitle = DataUtil.GetSkinSubtitle((emHero)char_index, skin_index);
                        skin_subtitle.text = subtitle;
                        if (!string.IsNullOrEmpty(subtitle)) {
                            skin_subtitle.gameObject.SetActive(true);
                            skin_subtitle_background.gameObject.SetActive(true);
                        } else {
                            skin_subtitle.gameObject.SetActive(false);
                            skin_subtitle_background.gameObject.SetActive(false);
                        }

                        // ScriptLocalization.Get("Character" + (char_index) + "_name_skin" + skin_index);
                        HeroList[char_index].GetComponent<RGController>().anim.runtimeAnimatorController =
                            ResourcesUtil.LoadHeroSkin(char_index, skin_index);
                        HeroList[char_index].BroadcastMessage("OnSkinChanged", skin_index,
                            SendMessageOptions.DontRequireReceiver);
                        ShowCharInfoUI();
                        ui_mask_down.ShowBtnChar(char_index);
                        RGMusicManager.GetInstance().PlayEffect(8);
#if UNITY_SWITCH
                        ShowMaskUpButton(true);
#endif
                        SimpleEventManager.Raise(new ChoseSkinCancel() {hero = (emHero)char_index, skinIndex = skin_index});
                        return;
                    }
                }

                ProCamera2D.Instance.RemoveAllCameraTargets();
                // 摄像机看向（0，1）才是正中间
                ProCamera2D.Instance.AddCameraTarget(temp_target, 1, 1, 0, MapManagerHall.Inst.CenterPoint);
                //HeroList[char_index].GetComponent<Animator>().enabled = false;
                HideUI();
                Mode = emMode.None;
                SelectTip();
                RGMusicManager.GetInstance().PlayEffect(8);
#if UNITY_SWITCH
                UiNavigationManager.Inst.Select(UiNavigationManager.Inst.GetLastSelect(selectHeroroot), new Vector2(64, 64));
                InputManager.Inst.RemoveForce(gameObject);
                RemoveBinding();
#endif
            }
        }
    }

    public void BtnConfirm() {
        TargetFinger.Clear();
        if (!ui_mask_down.IsBtnConfirmActive()) return;
        if (awake) {
            if (Mode == emMode.Pet) {
                RGSaveManager.Inst.SetLastPartner(emPartner.Pet, skin_index.ToString());
                BtnBack();
                var selectPet = (emPet)skin_index;
                //选择宠物事件
                var selectPetEvent = new SelectPetEvent {
                    pet = selectPet
                };
                SimpleEventManager.Raise(selectPetEvent);
                BehaviourPathStatistics.TrackBehaviour(emBehaviourPoint.Select_Pet, new Dictionary<string, object> {
                    {"skin_idx", (int)selectPet}
                });
                
                var btnFollower = transform.Find("mask_up/btn_follower");
                if (btnFollower) {
                    btnFollower.GetComponent<Button>().onClick.RemoveListener(OnOpenFollowerClicked);
                    btnFollower.gameObject.SetActive(false);
                }
                transform.Find("mask_up/Layout").GetComponent<RectTransform>().anchoredPosition = Vector2.zero;
            } else if (Mode == emMode.Hero &&
                       (DataUtil.GetHeroUnlock((emHero)char_index) || ui_mask_down.tryMode)) {
            } else if (Mode == emMode.Mech) {
                if (mech) {
                    if (skin_mode) {
                        ShowCharInfoUI();
                        RefreshMechDetail();
                        RGMusicManager.GetInstance().PlayEffect(8);
                    } else {
                        var materials = DataUtil.GetMechForgeMaterials(mech.name);
                        if (null != materials && materials.Count > 0 && ItemData.data.HasEnough(materials)) {
                            RGGameSceneManager.GetInstance().controller.transform.position = playerPosition;
                            mech.GetComponent<Collider2D>().enabled = true;
                            mech.inBuild = false;
                            ProCamera2D.Instance.RemoveAllCameraTargets();
                            ProCamera2D.Instance.AddCameraTarget(
                                RGGameSceneManager.GetInstance().controller.camera_focus, 1, 1, 0, new Vector2(0, 0f));
                            HideUI();
                            UICanvas.GetInstance().anim.SetBool("show", true);
                            UICanvas.GetInstance().anim.Play("show_ui", 0, 1);
                            ItemData.data.mechInRoom = mech.name;
                            var roomManager = RoomObjectManager.Inst;
                            roomManager.mechInRoom = mech.gameObject;
                            ItemData.data.ConsumeMaterial(materials, false, false);
                            mech.NeedRecord = true;
                            ItemData.Save();
                            RGMusicManager.GetInstance().PlayEffect(start_clip);
//选择机甲事件
                            var selectMechEvent = new SelectMechEvent {
                                mech = mech.name
                            };
                            SimpleEventManager.Raise(selectMechEvent);
                            ConsumeStatistics.TrackConsumeType(emStatisticsType.BuildMech, materials);
                            SimpleEventManager.Raise(new BuiltMechaEvent{mechaName = mech.name});
                            if (mech.isCreature) {
                                mech.ResetBodyMaterial();
                            }
#if UNITY_SWITCH
                            InputManager.Inst.RemoveForce(gameObject);
                            RemoveBinding();
#endif
                        }
                    }
                }
            }
        }
    }

    private static SkinAudioConfig _skinAudioConfig;

    private static SkinAudioConfig SkinAudioConfig {
        get {
            if (_skinAudioConfig == null) {
                _skinAudioConfig = Resources.Load<SkinAudioConfig>("SkinAudioConfig");
            }

            return _skinAudioConfig;
        }
    }

    public void UnLockPet(int f_index) {
        if (RGSaveManager.Inst.PetUnLock(f_index)) {
            ui_mask_down.ShowBtnPet(f_index);
            RGGameSceneManager.Inst.partner_proto.GetComponent<RGPetController>().GrayBack();
            UnLockEffect(_partnerTR, RGSaveManager.Inst.pet_list[f_index].icon,
                ScriptLocalization.Get("Pet_name_" + (f_index)), true);
            FirebaseUtil.LogEvent("unlock_pet", "pet_name", ((emPet)f_index).ToString());
        } else {
            UICanvas.ShowUIWindowShop(this, buyWay: ConsumeStatistics.BuyWay.UnlockPet);
        }
    }

    void UnLockEffect(Transform f_target) {
        GameObject temp_obj = Instantiate(PrefabManager.GetPrefab(PrefabName.effect_show_out)) as GameObject;
        temp_obj.transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;
        temp_obj.transform.position = f_target.position;
        RGMusicManager.GetInstance().PlayEffect(levelup_clip);
    }

    void UnLockEffect(Transform f_target, Sprite f_sprite, string f_text, bool showUI) {
        GameObject temp_obj = Instantiate(PrefabManager.GetPrefab(PrefabName.effect_show_out)) as GameObject;
        temp_obj.transform.parent = RGGameSceneManager.GetInstance().temp_objects_parent;
        temp_obj.transform.position = f_target.position;
        if (showUI) {
            UICanvas.ShowUIWindowObject(f_sprite, f_text);
        }
    }

    void ShowLR(int value) {
        anim.SetInteger("show_lr", value);
    }

    void ShowCharInfoUI() {
        anim.SetBool("show_ui", true);
        ShowLR(1);
        GameUtil.NarrowCamera(Camera.main);
        anim.SetBool("show_star_bar", true);
        skin_mode = false;
        CheckCharGray();

        awake = true;
        //Invoke("SetAwakeTrue", 0);
    }

    void ShowSkinUI() {
        anim.SetBool("show_ui", true);
        ShowLR(2);
        GameUtil.NarrowCamera(Camera.main);
        anim.SetBool("show_star_bar", false);
        skin_mode = true;

        awake = true;
        //Invoke("SetAwakeTrue", 0);
    }

    void HideUI() {
        HideView();
        GameUtil.EnlargeCamera();
        anim.SetBool("show_ui", false);
        UIManager.Inst.CloseUIView(this, false, false);
        ShowLR(0);
        anim.SetBool("show_star_bar", false);
        skin_mode = false;

        awake = false;
        //Invoke("SetAwakeFalse", 0);

        Invoke("CameraReturn", 0.5f);
        countdownRoot.SetActive(false);
        SimpleEventManager.Raise(new HideHeroChoseEvent());
    }

    void CameraReturn() {
        cameraReturned = true;
    }

    void SetAwakeFalse() {
        awake = false;
    }

    void SetAwakeTrue() {
        awake = true;
    }

    public void CheckCharGray() {
        bool gray = true;
        if (skin_mode) {
            if (DataUtil.GetSkinUnlock((emHero)char_index, skin_index)) {
                gray = false;
            }
        } else if (DataUtil.GetHeroUnlock((emHero)char_index)) {
            gray = false;
        }

        if (Mode == emMode.Mech) {
            gray = false;
        }

        if (gray) {
            HeroList[char_index].GetComponent<RGController>().GetGray();
        } else {
            HeroList[char_index].GetComponent<RGController>().GrayBack();
        }
    }

    void SelectTip() {
        if (GameUtil.InMultiGame()) {
            UICanvas.GetInstance().ShowTempMessage("I_mul_select_char", -1);
        } else {
            var ui_teaching = FindObjectOfType<UITeaching>();
            if (ui_teaching && ui_teaching.current_teach.Equals(UITeaching.Teach.ticket))
                UICanvas.GetInstance().ShowTempMessage(ScriptLocalization.Get("teaching/use_ticket", "选择其他角色，尝试不同的闯关体验"), -1, false);
            else
                UICanvas.GetInstance().ShowTempMessage("I_teaching_01", -1);
        }
    }

    public void NewPlayerTip() {
        Invoke("CreateTargetFinger", 5);
    }

    public void CreateTargetFinger() {
        if (StatisticData.data.GetEventCount(RGGameConst.NEW_PLAYER_FACTOR_COUNT) == 0 ||
            FindObjectOfType<UITeaching>()) return;
        if (UIManager.Inst.HasUIViewOpened<UIHeroListWindow>()) return;
        if (anim == null) return;
        if (!anim.GetBool("show_ui")) TargetFinger.CreateTargetFinger().MoveToKnight();
        Invoke("CreateTargetFinger", 10);
    }

    #region 选择机甲

    Transform mechContainer;
    UIWindowMechDialog mechDialog;
    RGMountController mech;

    public void GetChoiceMech(Transform mechContainer) {
        if (!awake) {
            Mode = emMode.Mech;
            playerPosition = RGGameSceneManager.GetInstance().controller.transform.position;
            RGGameSceneManager.GetInstance().controller.transform.position = new Vector3(-100, -100);
            this.mechContainer = mechContainer;
            skin_index = 0;
            RefreshMech(skin_index);
            ProCamera2D.Instance.RemoveAllCameraTargets();
            ProCamera2D.Instance.AddCameraTarget(mechContainer, 1, 1, 0, new Vector2(0, 1.0f));
            ShowSkinUI();
            UIManager.Inst.OpenExistedUIView(this, false);
            hero_name.text = ScriptLocalization.Get("m_mech_" + (skin_index));
            ui_mask_down.ShowBtnMech(mech, skin_index, true);
            UICanvas.GetInstance().EndTempMessage();
            UICanvas.GetInstance().anim.SetBool("show", false);
            UICanvas.GetInstance().anim.Play("hide_ui", 0, 1);

#if UNITY_SWITCH
            InputManager.Inst.AddForce(gameObject);
            AddKeyBinding();
            ShowMaskUpButton(false);
#endif
        }

        if (NeedShowCountdown) {
            countdownRoot.SetActive(true);
        }

        SimpleEventManager.Raise(new ShowHeroChoseEvent());
    }

    public void RefreshMech(int mechIndex) {
        if (mechContainer) {
            skin_subtitle.gameObject.SetActive(false);
            skin_subtitle_background.gameObject.SetActive(false);
            foreach (var mech in mechContainer.GetComponentsInChildren<RGMountController>()) {
                Destroy(mech.gameObject);
            }

            var mechName = MechInfos.info.mechInfos[mechIndex].name;
            LoadMech(mechName);
        }
    }

    private void LoadMech(string mechName)
    {
        mech = Instantiate(ResourcesUtil.LoadMount(mechName), mechContainer)
            .GetComponent<RGMountController>();
        mech.GetComponent<Collider2D>().enabled = false;
        mech.name = mechName;
        hero_name.text =
            ItemData.data.GetBluePrintStatus(MechInfos.info.GetMechInfo(mechName).unlockKey) ==
            emBluePrintStatus.Researched
                ? ScriptLocalization.Get(mechName)
                : ScriptLocalization.Get("mech/lock");
        if (mech.itemLevel == 6 && mech.unlocked) {
            hero_name.text = DataMgr.CGMountData.GetMountName(mech.unlock_key);
        }
        mech.transform.localPosition = Vector3.zero;
        mech.inBuild = true;
        mech.CheckGray();
        if (mech.isCreature) {
            mech.SetBodyMaterial(ResourcesUtil.Load<Material>("Plugins/Holographical2D/Core/BuiltIn/Sprites_ChillyRoom_Holographical2D.mat"));
        }
    }

    void RefreshMechDetail() {
        //屏蔽角色信息, 显示机甲信息
        transform.Find("star_bar").gameObject.SetActive(false);
        var uiLeft = transform.Find("ui_left");
        var uiRight = transform.Find("ui_rigjt");
        for (int i = uiLeft.transform.childCount - 1; i >= 0; i--) {
            uiLeft.GetChild(i).gameObject.SetActive(i == 1);
        }

        for (int i = uiRight.transform.childCount - 1; i >= 0; i--) {
            uiRight.GetChild(i).gameObject.SetActive(i == 1);
        }

        //刷新机甲三维
        uiLeft.Find("mech_panel/value1/Text").GetComponent<Text>().text =
            mech.GetComponent<RoleAttributePlayer>().max_hp.ToString();
        uiLeft.Find("mech_panel/value2/Text").GetComponent<Text>().text = mech.defence.ToString();
        uiLeft.Find("mech_panel/value3/Text").GetComponent<Text>().text = (mech.speedRate * 100).ToString();
        float maxLength = 140;
        uiLeft.Find("mech_panel/value1/Image").GetComponent<Image>().rectTransform.sizeDelta = new Vector2(
            (Mathf.Max(0.1f, Mathf.Min(4, Mathf.Max(0, (mech.GetComponent<RoleAttributePlayer>().max_hp - 2) / 2)))) *
            maxLength / 4, 24);
        uiLeft.Find("mech_panel/value2/Image").GetComponent<Image>().rectTransform.sizeDelta =
            new Vector2((Mathf.Max(0.1f, Mathf.Min(4, Mathf.Max(0, (mech.defence * 2))))) * maxLength / 4, 24);
        uiLeft.Find("mech_panel/value3/Image").GetComponent<Image>().rectTransform.sizeDelta = new Vector2(
            (Mathf.Max(0.1f, Mathf.Min(4, Mathf.Max(0, (mech.speedRate * 100 + 20) / 10)))) * maxLength / 4, 24);
        //刷新所需材料
        var materialRoot = uiRight.Find("mech_panel/materials");
        var item = materialRoot.GetChild(0).gameObject;
        for (int i = 1; i < materialRoot.childCount; i++) {
            Destroy(materialRoot.GetChild(i).gameObject);
        }

        var info = MechInfos.info.GetMechInfo(mech.name);
        Dictionary<string, int> materials = new();
        for (int i = 0; i < info.materials.Count; i++) {
            var itemMaterial = Instantiate(item, materialRoot);
            var materialConfig = ItemConfigLoader.GetItemConfig(info.materials[i].material);
            if (materialConfig == null) {
                continue;
            }

            itemMaterial.transform.GetComponentInChildren<Image>().sprite = materialConfig.Icon.GetSprite();
            int ownCount = ItemData.data.GetMaterialCount(materialConfig.Name);
            itemMaterial.transform.GetComponentInChildren<Text>().text = LanguageUtil.GetRTL()
                ? string.Format("{0}\n){1}(", info.materials[i].count.ToString(), ownCount)
                : string.Format("{0}\n({1})", info.materials[i].count.ToString(), ownCount);
            itemMaterial.transform.GetComponentInChildren<Text>().color =
                ownCount < info.materials[i].count ? Color.red : Color.white;
            materials.Add(materialConfig.Name, info.materials[i].count);
            itemMaterial.SetActive(true);
        }

        bool materialEnough = ItemData.data.HasEnough(materials);
        uiRight.Find("mech_panel/title").GetComponent<Text>().text =
            I2.Loc.ScriptLocalization.Get(materialEnough ? "I_material" : "object/no_enough_material");
        uiRight.Find("mech_panel/title").GetComponent<Text>().color = materialEnough ? Color.white : Color.red;
        ui_mask_down.ShowBtnMech(mech, skin_index, materialEnough);
    }

    #endregion

    #region KTplay

    public void BtnKtPlayClick() {
        TAUtil.Track("btn_ktplay", "where", "heroroom");
    }
    

    #endregion
}