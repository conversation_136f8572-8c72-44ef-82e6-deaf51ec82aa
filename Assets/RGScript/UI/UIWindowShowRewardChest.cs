using Coffee.UIExtensions;
using DG.Tweening;
using FishChipStore;
using I2.Loc;
using IronTide;
using Papa.Util;
using RGScript.Other.InputControl;
using System;
using Sirenix.OdinInspector;
using System.Collections;
using System.Collections.Generic;
using UIFramework;
using UnityCommon.UI;
using UnityEngine;
using UnityEngine.UI;
using Object = System.Object;

/// <summary>
/// 赛季结算奖励宝箱
/// </summary>
public class UIWindowShowRewardChest : RGUIBaseUIView {
    [NonSerialized] public Image img_icon;

    [NonSerialized] public Image img_icon_open;

    [NonSerialized] public Image img_light;

    // Image img_flag;
    public Shader textShader;
    public AudioClip audio_clip;
    public event Action onWindowCloseClick;
    public event Action onAfterShowItems;
    Material textMaterial;

    public float duration = 2f;
    public Vector3 strength = new Vector3(1, 2, 1);
    public int vibrato = 3;
    public float randomness = 1f;
    private List<Tween> _tweeners;
    private int _state = 0;
    private Object[] _pickableObj;
    private bool _hasShowReward = false;

    private UIParticle _fx;
    private Transform pickableList;
    private Transform box;
    public bool autoCloseView = true;

    public override void Awake() {
        base.Awake();
        // 有手动绑定的手柄按键，点确认键关闭窗口
        uiOperationMode = UIOperationMode.UISimpleOp;

        box = transform.Find("box");
        img_icon = box.Find("img_icon").GetComponent<Image>();
        img_icon_open = box.Find("img_icon_open").GetComponent<Image>();
        img_light = transform.Find("img_light").GetComponent<Image>();
        _fx = transform.Find("fx/ps").GetComponent<UIParticle>();
        ParticleSystem[] particleSystems = _fx.gameObject.GetComponentsInChildren<ParticleSystem>();
        foreach (ParticleSystem system in particleSystems) {
            ParticleSystem.MainModule main = system.main;
            main.useUnscaledTime = true;
        }

        pickableList = transform.Find("pickables");

        var texts = transform.GetComponentsInChildren<Text>();
        textMaterial = new Material(textShader);
        foreach (var text in texts) {
            text.material = textMaterial;
        }

        _tweeners = new List<Tween>();
    }

    public override void InitView() {
        base.InitView();
        handleEsc = true;
    }

    public override bool OnEvent(NavigationEvent navigationEvent) {
        if (this == null) {
            return false;
        }

        switch (navigationEvent) {
            case NavigationEvent.Back:
                BtnClick();
                break;
            default:
                base.OnEvent(navigationEvent);
                break;
        }

        return true;
    }

    private void OnEnable() {
#if UNITY_SWITCH
        InputManager.Inst.AddForce(gameObject);
        InputManager.Inst.AddKeyBinding(gameObject, InputManager.Inst.submitKey, delegate { return awake; }, BtnClick);
        UiNavigationManager.Inst.RecordHighlightAndHide();
#endif
    }

    private void OnDisable() {
#if UNITY_SWITCH
        InputManager.Inst.RemoveForce(gameObject);
        InputManager.Inst.RemoveKeyBinding(gameObject);
        UiNavigationManager.Inst.RecoveryHighlight();
#endif
    }

    #region UIManager 按键绑定

    public override void OnJoystickButtonDown(JoystickButton button) {
        if (!awake) return;
        base.OnJoystickButtonDown(button);

        if (InputControl.Inst.GetAnyUIConfirmButtonDown()) {
            BtnClick();
        }
    }

    public override void ShowView(params object[] objects) {
        base.ShowView(objects);

        if (audio_clip) {
            RGMusicManager.GetInstance().PlayEffect(audio_clip);
        }

        _pickableObj = objects;
        ShowEffect();
    }

    [Button("测试宝箱")]
    private void ShowEffect() {
        KillTweeners();
        _hasShowReward = false;
        _state = 0;
        img_icon.gameObject.SetActive(true);
        img_icon_open.gameObject.SetActive(false);
        _fx.gameObject.SetActive(false);
        img_icon.rectTransform.localPosition = new Vector3(0, -16, 0);
        img_icon_open.rectTransform.localPosition = Vector3.zero;
        Tweener tweener1 = img_icon.transform.DOShakePosition(duration, strength, vibrato, randomness, false, true)
            .OnComplete(() => {
                State1();
            }).SetUpdate(true);
        _tweeners.Add(tweener1);
        Tweener tweener2 = img_light.transform.DOLocalRotate(Vector3.zero, 2f, RotateMode.FastBeyond360).SetLoops(-1);
        _tweeners.Add(tweener2);
    }

    private void ShowItems() {
        foreach (var item in _pickableObj) {
            var go = (GameObject)item;
            go.transform.SetParent(pickableList);
            go.transform.localScale = Vector3.one * 0.5f;
            go.transform.localPosition = Vector3.zero;
            go.SetActive(false);
        }
        ShowAnim();
        Timer.Register(1.2f, false, true, () => {
            onAfterShowItems?.Invoke();
        });
    }

    private void ShowAnim() {
        RectTransform rt = pickableList.GetComponent<RectTransform>();
        rt.anchoredPosition = new Vector2(0, 0);

        DOVirtual.DelayedCall(0.2f, () => {
            foreach (var item in _pickableObj) {
                var go = (GameObject)item;
                go.gameObject.SetActive(true);
                go.transform.DOScale(new Vector3(1.3f, 1.3f, 1.3f), 0.5f).SetUpdate(true);
            }

            rt.DOAnchorPosY(100, 0.5f).SetEase(Ease.OutBack).SetUpdate(true);
        });
    }

    private void State1() {
        img_icon.gameObject.SetActive(false);
        img_icon_open.gameObject.SetActive(true);

        _fx.gameObject.SetActive(true);
        _fx.GetComponent<RectTransform>().localPosition = Vector3.zero;
        _fx.Play();

        _state = 1;
        Tween tweener = DOVirtual.DelayedCall(0.7f, () => {
            State2();
        });
        _tweeners.Add(tweener);
        Tweener tweener2 = img_light.transform.DOLocalRotate(Vector3.zero, 2f, RotateMode.FastBeyond360).SetLoops(-1);
        _tweeners.Add(tweener2);
    }

    private void State2() {
        _state = 2;
        img_light.transform.DOScale(Vector3.zero, 0.3f).SetUpdate(true);
        box.transform.DOScale(Vector3.zero, 0.3f).SetUpdate(true);
        ShowItems();
        Tween tweener = DOVirtual.DelayedCall(2.5f, () => {
            State3();
        });
        _tweeners.Add(tweener);
    }

    private void State3() {
        _state = 3;
        // 用完隐藏掉，避免影响后续的UIParticle
        _fx.gameObject.SetActive(false);
        if (!autoCloseView) {
            return;
        }

        if (!_hasShowReward) {
            _hasShowReward = true;
            OnClick_Close();
        }
    }

    private void KillTweeners() {
        for (var i = 0; i < _tweeners.Count; i++) {
            var tweener = _tweeners[i];
            tweener.Kill();
            _tweeners[i] = null;
        }

        _tweeners.Clear();
    }

    public override void HideView(params object[] objects) {
        GameManager.Inst.ContinueGame();
        // anim.SetTrigger("hide");

        if (UnityEngine.SceneManagement.SceneManager.GetActiveScene().name != "Scene_Splash" &&
            RGMusicManager.GetInstance()) {
            RGMusicManager.GetInstance().PlayEffect(8);
        }

        onWindowCloseClick?.Invoke();
        KillTweeners();
    }

    #endregion


    public void BtnClick() {
        KillTweeners();
        if (_state == 0) {
            State1();
            return;
        }

        if (_state == 1) {
            State2();
            return;
        }

        if (_state == 3) {
            State3();
            return;
        }

        OnClick_Close();
    }

    public override void ManualCloseView() {
        UIManager.Inst.CloseUIView(this);
    }


    IEnumerator DestorySelf(float delay) {
        yield return new WaitForSecondsRealtime(delay);
        Destroy(gameObject);
    }

    void TextFadeIn(float fadeInTime) {
        // StartCoroutine(TextFadeInAnime(fadeInTime));
    }

    IEnumerator TextFadeInAnime(float fadeInTime) {
        yield return AnimeUtil.AnimationRealTime(fadeInTime, (percentage) => {
            textMaterial.color = new Color(1, 1, 1, percentage);
        });
        textMaterial.color = Color.white;
    }

    void TextFadeOut(float fadeOutTime) {
        // StartCoroutine(TextFadeOutAnime(fadeOutTime));
    }

    IEnumerator TextFadeOutAnime(float fadeOutTime) {
        yield return AnimeUtil.Animation(fadeOutTime, (percentage) => {
            textMaterial.color = new Color(1, 1, 1, 1 - percentage);
        });
        textMaterial.color = new Color(1, 1, 1, 0);
    }
}