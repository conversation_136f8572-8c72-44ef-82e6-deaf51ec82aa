using ModeSeason.Artifacts;
using Spine.Unity.AttachmentTools;
using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using System.Linq;
using Util.TAUtil;

namespace RGScript.UI.ModeStatement {
    public class ModeArtifactsStatement : BaseModeStatement {

        public override bool MultiPlayerStatement => false;

        Text _seasonCoinText;
        Text _gemText;
        int _seasonCoin;
        int _gem;
        bool gotReward;
        int _gainedSeasonCoin;
        int[] _coinUIPositionList = new int[] {
            -1575,
            -1420,
            -1230,
        };

        public override void OnInit(RectTransform uiStatement, bool isPass) {
            base.OnInit(uiStatement, isPass);
            uiStatement.transform.Find("gem").gameObject.SetActive(false);
            _gemText = uiStatement.transform.Find("gem/Text").GetComponent<Text>();
            uiStatement.transform.Find("text_gems").gameObject.SetActive(false);
            uiStatement.transform.Find("info/detial/kill").position = uiStatement.transform.Find("gem").position;
            uiStatement.transform.Find("info/detial/coin").gameObject.SetActive(false);
            _seasonCoinText = uiStatement.transform.Find("info/detial/seasonCoin/Text").GetComponent<Text>();
            _seasonCoinText.text = "0";
            _gainedSeasonCoin = SeasonData.data.gainedCoinThisWeek;

            DisplayWeaponImage(uiStatement.gameObject);
            _seasonCoin = CalculateSeasonCoin(isPass);
            _gem = 0;
            if (SeasonData.data.gainedCoinThisWeek + _seasonCoin > SeasonDataUtil.weeklySeasonCoinLimitation) {
                var realSeasonCoin = SeasonDataUtil.weeklySeasonCoinLimitation - SeasonData.data.gainedCoinThisWeek;
                _gem = Mathf.Clamp(_seasonCoin - realSeasonCoin, 0, BattleData.data.isBadass ? 2000 : 1500);
                _seasonCoin = realSeasonCoin;
            }

            var gameObjectToDisplay = new List<GameObject>();
            gameObjectToDisplay.Add(uiStatement.transform.Find("info/detial/seasonCoin").gameObject);

            if (_gem > 0) {
                gameObjectToDisplay.Add(uiStatement.transform.Find("gem").gameObject);
            }

            gameObjectToDisplay.Add(uiStatement.transform.Find("info/detial/kill").gameObject);

            OnUpdateLevelAxis(0);

            for (var i = 0; i < gameObjectToDisplay.Count; ++i) {
                var posX = _coinUIPositionList[i];
                var localPos = new Vector3(posX, gameObjectToDisplay[i].transform.localPosition.y, gameObjectToDisplay[i].transform.localPosition.z);
                gameObjectToDisplay[i].transform.position = new Vector3(uiStatement.transform.Find("info/detial").TransformPoint(localPos).x, gameObjectToDisplay[i].transform.position.y, 0);
                gameObjectToDisplay[i].SetActive(true);
            }
        }

        public void DisplayWeaponImage(GameObject uiStatement) {
            // 隐藏魔法石
            uiStatement.transform.Find("info/detial/stone_base_old").gameObject.SetActive(false);
            var magicStoneTrans = uiStatement.transform.Find("info/detial/stone_base");
            magicStoneTrans.gameObject.SetActive(false);

            void OnBundleLoaded() {
                var glowObj = GameObject.Instantiate(ResourcesUtil.Load<GameObject>("ModeArtifacts/Prefab/UI/ArtifactUIGlow.prefab"), magicStoneTrans.parent); // 发光

                if (BattleData.data.artifactsMode != null && BattleData.data.artifactsMode.artifactWeaponParts != null) {
                    glowObj.SetActive(true);
                    glowObj.transform.localPosition = magicStoneTrans.localPosition;
                    glowObj.transform.GetChild(0).gameObject.SetActive(true);

                    var weaponType = BattleData.data.artifactsMode.weaponType;
                    var partNames = new List<string>();
                    for (var i = 0; i < BattleData.data.artifactsMode.artifactWeaponParts.Length; ++i) {
                        partNames.Add(BattleData.data.artifactsMode.artifactWeaponParts[i].name);
                    }
                    var weaponUIObj = UiUtil.CreateRectTransformFromSpriteRenderers(glowObj.transform as RectTransform, RGWeaponArtifactBody.CreateImageObject(weaponType, partNames), 100);
                    weaponUIObj.transform.localPosition = new Vector3(0, 0, 0);
                } 
                else {
                    glowObj.SetActive(false);
                }
            }

            AssetBundleLoader.Inst.LoadBundle("artifacts2/weapon", OnBundleLoaded);
        }

        public override void StatisticDatas(bool isPass) {
            var seasonTaskInfoGetter = new Artifacts2SeasonTaskInfoGetter();
            seasonTaskInfoGetter.RefreshTaskScore("GameOver", isPass);
        }

        public override bool NeedOverrideGetEndGem() {
            return true;
        }

        public override bool NeedCalcLevelGem() {
            return false;
        }

        public override bool NeedRecordPassHistory() {
            return false;
        }

        int GetKillCount() {
            return BattleData.data.continueEndGame ? BattleData.data.kill_count : RGGameProcess.Inst.kill_count;
        }

        int CalculateSeasonCoin(bool isPass) {
            var passLevels = BattleData.data.levelIndex + 1;
            if (isPass) {
                passLevels -= 1; // 忽略最终魔法石层
            }
            passLevels = Mathf.Clamp(passLevels, 0, 25);

            _seasonCoin = 0;
            var list = ArtifactsUtil.GetConstIntList(BattleData.data.isBadass ? "seasonCoinRewardBadass" : "seasonCoinReward");
            if (list != null && list.Length > 0) {
                for (var i = 0; i < passLevels; ++i) {
                    _seasonCoin += list[Mathf.Min(i, list.Length - 1)];
                }
            }

            var estimateKillCount = 1; // 防止除0
            var estimateKillCfgList = ArtifactsUtil.GetConstIntList("estKillPerLevel");
            for (var i = 0; i < passLevels; ++i) {
                var idx = i / 5;
                estimateKillCount += estimateKillCfgList[Mathf.Clamp(idx, 0, estimateKillCfgList.Length - 1)];
            }
            _seasonCoin = Mathf.RoundToInt(_seasonCoin * Mathf.Min(GetKillCount() * 1f / estimateKillCount, 1));

            return _seasonCoin;
        }

        public override void OnBtnClick(int tap_count) {
            if (tap_count == 0) {
                _seasonCoinText.text = _seasonCoin.ToString();
                if (!gotReward) {
                    if (_seasonCoin > 0) {
                        SeasonDataUtil.AddCoin(_seasonCoin, SeasonCoinChangeReason.GameStatement, "", BattleData.data.season.ToString());
                        SeasonData.Save();
                    }
                    if (_gem > 0) {
                        var dicParam = BattleStatistics.GetBattleDataGameInfo();
                        RGSaveManager.Inst.AddGem(_gem, emObtainGemType.Statement2, dicParam);
                        RGSaveManager.Inst.SaveGameData();
                    }
                    gotReward = true;
                }
            }
        }

        public override void OnUpdateLevelAxis(float progress) {
            var val = (int)(_seasonCoin * progress);
            _seasonCoinText.text = val.ToString() + "\n" + string.Format("{0}/{1}", val + _gainedSeasonCoin, SeasonDataUtil.weeklySeasonCoinLimitation);
            _gemText.text = ((int)(_gem * progress)).ToString();
        }

        public override void OnDestroy() {
            if (GameUtil.IsMultiGame()) {
                BattleData.data.gameMode = emGameMode.Artifacts2;
            }
        }
    }
}