using DG.Tweening;
using I2.Loc;
using RGScript.Util;
using RGScript.WeaponAffix;
using System;
using System.Collections;
using System.Collections.Generic;
using UIFramework;
using UnityCommon.UI;
using UnityEngine;
using UnityEngine.U2D;
using UnityEngine.UI;
using Util.TAUtil;

namespace RGScript.UI {
    public class UIWindowWeaponAffixShop : BaseUIView {
        public Sprite addBtnSprite;
        public Sprite upgradeBtnSprite;
        public Sprite replaceBtnSprite;
        public Sprite grayBtnSprite;

        private int _addPrice = 100;
        private int _upgradePrice = 40;
        private int _replacePrice = 20;

        private const float AnimSpeed = 0.2f;
        private Image _mask;
        private Button _closeBtn;
        private GameObject _weaponProto;
        private GameObject _affixProto;
        private static RGController Controller => RGGameSceneManager.Inst.controller;

        private List<Transform> _affixTransList = new List<Transform>();
        private RectTransform _selectFrame;
        private List<(Button btn, RGWeapon weapon)> _weaponList = new List<(Button btn, RGWeapon weapon)>();
        private SpriteAtlas _iconAtlas;
        private const string IconAtlasPath = "RGTexture/sprite_atlas/ui/weapon_affix.spriteatlasv2";
        private Text _remainCountText;
        private Text _coinText;
        private NpcWeaponAffix _npc;
        public static UIWindowWeaponAffixShop ShowWindow(NpcWeaponAffix npc) {
            var window = UIManager.Inst.OpenUIView(new UIViewOpenSetting<UIWindowWeaponAffixShop>() {
                uiViewAssetPath = "window_weapon_affix_shop",
                userData = new object[] {npc},
            });

            return window;
        }

        private Sprite GetLevelIcon(int level) {
            if (!_iconAtlas) {
                _iconAtlas = ResourcesUtil.Load<SpriteAtlas>(IconAtlasPath);
            }

            return _iconAtlas.GetSprite($"weapon_affix_level_{level}");
        }

        private int GetRemainUseCount() {
            return _npc.GetRemainUseCount();
        }

        private void ConsumeRemainUseCount() {
            _npc.ConsumeRemainUseCount();
        }

        public override void InitView() {
            base.InitView();
            pauseWhenShow = true;
            handleEsc = true;
            FindReferences();
        }

        private void InitAffix() {
            if (!Controller) {
                Debug.LogError($"[UIWindowWeaponAffixLevel] Controller is null");
            }
            var weapons = Controller.GetAllWeapons();
            foreach (var weapon in weapons) {
                var weaponGo = Instantiate(_weaponProto, _weaponProto.transform.parent);
                var weaponId = weapon.GetID();
                var weaponTrans = weaponGo.transform;
                weaponTrans.Find("weapon_img").GetComponent<Image>().sprite = SpriteMap.weaponSprite.GetSprite(weaponId);
                var weaponNameText = weaponTrans.Find("name").GetComponent<Text>();
                weaponNameText.text = NameUtil.GetWeaponName(weaponId);
                weaponNameText.color = weapon.item_level.ToItemLevel().ToColor();
                var weaponBtn = weaponTrans.GetComponent<Button>();
                weaponBtn.onClick.RemoveAllListeners();
                weaponBtn.onClick.AddListener(() => {
                    RefreshAffix(weaponBtn, weapon);
                });
                weaponGo.SetActive(true);
                _weaponList.Add((weaponBtn, weapon));
            }

            for (int i = 0; i < RGGameConst.NORMAL_DIFFICULTY_WEAPON_AFFIX_MAX_COUNT; i++) {
                var affixGo = Instantiate(_affixProto, _affixProto.transform.parent);
                _affixTransList.Add(affixGo.transform);
                affixGo.SetActive(true);
            }

            StartCoroutine(DelayInitRefreshAffix());
        }

        private IEnumerator DelayInitRefreshAffix() {
            yield return new WaitForEndOfFrame();
            if (_weaponList.Count > 0) {
                RefreshAffix(_weaponList[0].btn, _weaponList[0].weapon);
            }
        }

        private void RefreshTop() {
            _remainCountText.text = $"{ScriptLocalization.Get("item/use_count_left")}{GetRemainUseCount()}";
            _coinText.text = RGGameProcess.Inst.GetCoin(ItemLevel.VoidCoin).ToString();
        }

        private void RefreshAffix(Button btn, RGWeapon weapon) {
            _selectFrame.position = btn.transform.position;
            var weaponAffixList = weapon.GetWeaponAffixList();
            _affixTransList.ForEach(t => {
                t.gameObject.SetActive(true);
                t.Find("empty").gameObject.SetActive(true);
            });
            for (int i = 0; i < weaponAffixList.Count; i++) {
                var affix = weaponAffixList[i];
                var affixTrans = _affixTransList[i];
                var levelImg = affixTrans.Find("level").GetComponent<Image>();
                levelImg.sprite = GetLevelIcon(affix.Level);
                var nameText = affixTrans.Find("name").GetComponent<Text>();
                nameText.text = affix.GetNameWithBrackets();
                nameText.color = affix.GetLevelColor();
                affixTrans.Find("desc").GetComponent<Text>().text = affix.GetDesc();
                affixTrans.Find("empty").gameObject.SetActive(false);
                affixTrans.Find("line").gameObject.SetActive(i < 2);
                var replaceBtn = affixTrans.Find("btn_list/btn_0").GetComponent<Button>();
                var upgradeBtn = affixTrans.Find("btn_list/btn_1").GetComponent<Button>();
                replaceBtn.transform.Find("text").GetComponent<Text>().text = $"{ScriptLocalization.Get("REPLACE")} {GetCurrencyString(_replacePrice)}";
                upgradeBtn.transform.Find("text").GetComponent<Text>().text = $"{ScriptLocalization.Get("UPGRADE")} {GetCurrencyString(_upgradePrice)}";
                replaceBtn.onClick.RemoveAllListeners();
                upgradeBtn.onClick.RemoveAllListeners();

                replaceBtn.image.sprite = CanServe(_replacePrice) ? replaceBtnSprite : grayBtnSprite;
                upgradeBtn.image.sprite = CanServe(_upgradePrice) ? upgradeBtnSprite : grayBtnSprite;

                var index = i;
                replaceBtn.onClick.AddListener(() => ReplaceAffix(weapon, index));
                upgradeBtn.onClick.AddListener(() => UpgradeAffix(weapon, index));
            }

            var addBtn = transform.Find("body/affix_list/content/btn_add").GetComponent<Button>();
            addBtn.onClick.RemoveAllListeners();
            addBtn.transform.SetAsLastSibling();
            if (CanShowAddBtn(weapon)) {
                for (var i = 0; i < _affixTransList.Count; i++) {
                    if (i >= weaponAffixList.Count) {
                        _affixTransList[i].gameObject.SetActive(false);
                    }
                }

                addBtn.image.sprite = CanServe(_addPrice) ? addBtnSprite : grayBtnSprite;
                addBtn.gameObject.SetActive(true);
                addBtn.onClick.AddListener(() => AddAffix(weapon));
                addBtn.transform.Find("text").GetComponent<Text>().text = $"{ScriptLocalization.Get("ui/add_affix")} {GetCurrencyString(_addPrice)}";
            } else {
                addBtn.gameObject.SetActive(false);
            }
        }

        private bool EnoughCoin(int price) {
            return RGGameProcess.Inst.GetCoin(ItemLevel.VoidCoin) >= price;
        }

        public bool CanServe(int price, bool showMessage = false) {
            if (GetRemainUseCount() == 0) {
                if (showMessage) {
                    UICanvas.Inst.ShowTempMessage("item/use_max", 2f);
                }
                return false;
            }

            if (!EnoughCoin(price)) {
                if (showMessage) {
                    UICanvas.Inst.ShowTempMessage("ui/not_enough_void_coin", 2f);
                }
                return false;
            }

            return true;
        }

        private static bool CanShowAddBtn(RGWeapon weapon) {
            return weapon.GetWeaponAffixList().Count < RGGameConst.NORMAL_DIFFICULTY_WEAPON_AFFIX_MAX_COUNT && BattleData.data.levelIndex >= 9;
        }

        private void ReplaceAffix(RGWeapon weapon, int index) {
            if (!CanServe(_replacePrice, true)) {
                return;
            }

            ConsumeRemainUseCount();
            RGGameProcess.Inst.ConsumeCoin(_replacePrice, emStatisticsType.WeaponAffixReplace, ItemLevel.VoidCoin);
            var oldWeaponAffix = weapon.GetWeaponAffixList()[index];
            weapon.RemoveWeaponAffixAndRefreshUI(oldWeaponAffix);
            var weaponAffixId = WeaponAffixFactory.GetRandomAffix(WeaponAffixLevelManager.Inst.GetRGRandom(), weapon);
            var newWeaponAffix = WeaponAffixFactory.Create(weaponAffixId);
            weapon.AddWeaponAffixAtIndexAndRefreshUI(newWeaponAffix, index);
            var item = _weaponList.Find(i => i.weapon == weapon);
            RefreshAffix(item.btn, item.weapon);
            RefreshTop();
        }

        private void UpgradeAffix(RGWeapon weapon, int index) {
            if (!CanServe(_upgradePrice, true)) {
                return;
            }

            var weaponAffix = weapon.GetWeaponAffixList()[index];

            if (!weaponAffix.CanUpgrade()) {
                UICanvas.Inst.ShowTempMessage("ui/affix_upgrade_max", 2f);
                return;
            }

            ConsumeRemainUseCount();
            RGGameProcess.Inst.ConsumeCoin(_upgradePrice, emStatisticsType.WeaponAffixUpgrade, ItemLevel.VoidCoin);
            weaponAffix.Upgrade();
            var item = _weaponList.Find(i => i.weapon == weapon);
            RefreshAffix(item.btn, item.weapon);
            RefreshTop();
        }

        private void AddAffix(RGWeapon weapon) {
            if (!CanServe(_addPrice, true)) {
                return;
            }

            ConsumeRemainUseCount();
            RGGameProcess.Inst.ConsumeCoin(_addPrice, emStatisticsType.WeaponAffixAdd, ItemLevel.VoidCoin);
            var weaponAffixId = WeaponAffixFactory.GetRandomAffix(WeaponAffixLevelManager.Inst.GetRGRandom(), weapon);
            var newWeaponAffix = WeaponAffixFactory.Create(weaponAffixId);
            weapon.AddWeaponAffixAndRefreshUI(newWeaponAffix);
            var item = _weaponList.Find(i => i.weapon == weapon);
            RefreshAffix(item.btn, item.weapon);
            RefreshTop();
        }

        private static string GetCurrencyString(int value) {
            var s = string.Format(ItemLevel.VoidCoin.ToRichText(), value);
            s = $"<quad name=coin_void width=1 y=-0.33/>{s}";
            return s;
        }

        public override void ShowView(params object[] args) {
            base.ShowView(args);
            _npc = (NpcWeaponAffix)args[0];
            RefreshTop();
            _mask.GetComponent<Button>().onClick.AddListener(OnClick_Close);
            _closeBtn.onClick.AddListener(OnClick_Close);

            _weaponProto.gameObject.SetActive(false);
            _affixProto.gameObject.SetActive(false);
            InitAffix();


            ShowAnim(transform.Find("body"));
        }

        private void FindReferences() {
            _mask = transform.Find("mask").GetComponent<Image>();
            _closeBtn = transform.Find("body/close_btn").GetComponent<Button>();
            _weaponProto = transform.Find("body/weapon_list/viewport/content/item_proto").gameObject;
            _affixProto = transform.Find("body/affix_list/content/item_proto").gameObject;
            _selectFrame = transform.Find("body/weapon_list/select_frame").GetComponent<RectTransform>();
            _remainCountText = transform.Find("body/text").GetComponent<Text>();
            _coinText = transform.Find("body/coin/Bg/Text").GetComponent<Text>();
        }

        private void ShowAnim(Transform trans) {
            if (trans == null)
                return;
            trans.localPosition = new Vector3(0, Screen.height, 0);
            trans.gameObject.SetActive(true);

            trans.DOLocalMoveY(0, AnimSpeed).SetUpdate(true).SetEase(Ease.OutSine);
            _mask.DOFade(0.5f, AnimSpeed);
            RGMusicManager.GetInstance().PlayEffect(8);
        }

        private void HideAnim(Transform trans, Action callback) {
            trans.localPosition = new Vector3(0, 0, 0);
            trans.DOLocalMoveY(Screen.height, AnimSpeed).SetUpdate(true).SetEase(Ease.InSine).onComplete += () => {
                trans.gameObject.SetActive(false);
                callback?.Invoke();
            };
            _mask.DOFade(0, AnimSpeed);
            RGMusicManager.GetInstance().PlayEffect(8);
        }

        private void Close() {
            UIManager.Inst.CloseUIView(this);
        }

        public override void OnClick_Close() {
            HideAnim(transform.Find("body").GetComponent<RectTransform>(), Close);
        }
    }
}