using System;
using UIFramework;
using UnityCommon.UI;
using UnityEngine;
using UnityEngine.UI;

namespace RGScript.UI.MultiRoomSkin {
    public class MultiRoomSkinUISeasonCoin : BaseUIView {
        private Transform _text;

        public override void Awake() {
            _text = transform.Find("img/text");
            _text.GetComponent<Text>().text = SeasonDataUtil.coin.ToString();
        }

        private void OnEnable() {
            MultiRoomSkinUIBuyMecha.OnBuyMecha += FreshSeasonCoinUI;
            MultiRoomSkinUIBuyMecha.OnCloseBuyMechaView += Close;
        }

        void FreshSeasonCoinUI() {
            var coin = SeasonDataUtil.coin;
            _text.GetComponent<Text>().text = coin.ToString();
        }

        void Close() {
            UIManager.Inst.CloseUIViewDelay(this, 1.5f);
        }

        private void OnDisable() {
            MultiRoomSkinUIBuyMecha.OnBuyMecha -= FreshSeasonCoinUI;
            MultiRoomSkinUIBuyMecha.OnCloseBuyMechaView -= Close;
        }
    }
}