using DG.Tweening;
using System;
using System.Collections;
using UnityEngine;
using UnityEngine.Serialization;

namespace RGScript.UI.MultiRoomSkin {
    public class MultiRoomSkin19Poster :MonoBehaviour {
        public Vector3 startPos;
        public Vector3 endPos;
        public float moveTime = 2;
        public float spanTime = 0.8f;
        
        
        public SpriteRenderer sr1;
        public SpriteRenderer sr2;
        public Sprite[] sprites;
        public int index;
        private const int MaxCount = 4;
        private Vector3 _offset;

        private void Start() {
            _offset = endPos - startPos;
            sr2.sprite = sprites[(index+sprites.Length-1) % sprites.Length];
            sr1.sprite = sprites[(index) % sprites.Length];
            StartCoroutine(IEMoveTween());
        }

        // ReSharper disable once FunctionRecursiveOnAllPaths
        IEnumerator IEMoveTween() {
            float timer = 0;
            Vector3 endPos2 = endPos +_offset;
            while (timer < moveTime) {
                float p = timer / moveTime;
                sr1.transform.localPosition = Vector3.Lerp(startPos, endPos, p);
                sr2.transform.localPosition = Vector3.Lerp(endPos, endPos2, p);
                timer += Time.deltaTime;
                yield return null;
            }
            sr1.transform.localPosition = startPos;
            sr2.transform.localPosition = endPos;
            
            sr2.sprite = sprites[index % sprites.Length];
            sr1.sprite = sprites[(index+1) % sprites.Length];
            index++;
            yield return new WaitForSeconds(spanTime);
            //Restart
            StartCoroutine(IEMoveTween());
        }


        
    }
}