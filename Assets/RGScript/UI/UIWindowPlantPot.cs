using RGScript.Other.LegencyAdapter;
using RGScript.Other.NewSDK;
using RGScript.Util.LifeCycle;
using RGScript.Data.Mall;
using RGScript.Other.InputControl;
using System.Collections;
using Sirenix.OdinInspector;
using UIFramework;
using UnityCommon.UI;
using UnityEngine;
using UnityEngine.UI;
using Util.TAUtil;

public class UIWindowPlantPot : BaseUIView {
    [HideInInspector]
    public Animator anim;

    public bool awake { get { return Active; } private set { Active = value; } }

    [PreviewField] public Sprite[] buttons;

    ItemPlantPot pot;
    Text text_gems;
    public Text text_btn_price;
    string payId;
    public AudioClip levelup_clip;

    private Button btn_confirm;
    // Use this for initialization
    public override void Awake() {
        base.Awake();
        anim = transform.GetComponent<Animator>();
        // text_btn_price = transform.Find("btn_confirm/text2").GetComponent<Text>();
        text_gems = GameObject.Find("/Canvas/text_gems/GemLayout/Text").GetComponent<Text>();
        btn_confirm = transform.Find("btn_confirm").GetComponent<Button>();
#if UNITY_SWITCH
        InputManager.AddButtonTip(transform.Find("btn_confirm"), InputManager.Inst.submitKey.ToString());
        InputManager.AddButtonTip(transform.Find("btn_back"), InputManager.Inst.cancelKey.ToString()).SetBrownColor();
#endif
    }
    private void OnEnable() {
#if UNITY_SWITCH
        InputManager.Inst.AddForce(gameObject);
        InputManager.Inst.AddKeyBinding(gameObject, InputManager.Inst.submitKey, delegate { return awake; }, OnClick_Unlock);
        InputManager.Inst.AddKeyBinding(gameObject, InputManager.Inst.cancelKey, delegate { return awake; }, CloseBtnClick);
#endif
        LifeCycleManager.Instance.OnSetSupportPay += OnSetSupportPay;
        NewSDKManager.Inst.OnFetchedSKUS += RefreshProductDesc;
    }

    private void OnDisable() {
#if UNITY_SWITCH
        InputManager.Inst.RemoveForce(gameObject);
        InputManager.Inst.RemoveKeyBinding(gameObject);
#endif
        LifeCycleManager.Instance.OnSetSupportPay -= OnSetSupportPay;
    }

    private void OnDestroy() {
        NewSDKManager.Inst.OnFetchedSKUS -= RefreshProductDesc;
    }

    public override void OnJoystickButtonDown(JoystickButton button) {
        if (awake) {
            base.OnJoystickButtonDown(button);
            if (Input.GetButtonDown("BtnR")) {
                OnClick_Unlock();
                return;
            }
            if (InputControl.Inst.GetAnyUICancelButtonDown()) {
                CloseBtnClick();
            }
        }
    }

    private void RefreshProductDesc() {
        if (this.pot) {
            payId = this.pot.potIndex == 4 ? "plantpot_1" : this.pot.potIndex == 5 ? "plantpot_2" : this.pot.potIndex == 7 ? "plantpot_3" : string.Empty;
            text_btn_price.text = this.pot.potIndex == 3 || this.pot.potIndex == 6 ? "g" + this.pot.unlockPrice : NewSDKManager.Inst.GetPriceDescription(payId, true);
        }
    }
    
    public void ShowWindow(ItemPlantPot pot) {
        this.pot = pot;
        RefreshProductDesc();
        OnSetSupportPay(LifeCycleManager.Instance.SupportPay);
        ShowWindow();
    }
    private void ShowWindow() {
        if (!awake) {
            GameManager.Inst.PauseGame();
            anim.SetBool("show", true);
            UICanvas.Inst.ShowGem();
            awake = true;
            UICanvas.GetInstance().anim.SetBool("show", false);
            UICanvas.GetInstance().anim.SetBool("curtain_alpha", true);
            RGMusicManager.GetInstance().PlayEffect(8);
            ViewManager.PushView(this);
        }
    }
    public void HideWindow() {
        GameManager.Inst.ContinueGame();
        awake = false;
        anim.SetBool("show", false);
        UICanvas.Inst.HideGem();
        UICanvas.GetInstance().anim.SetBool("show", true);
        UICanvas.GetInstance().anim.SetBool("curtain_alpha", false);
    }

    public void OnClick_Unlock() {
        if (awake) {
            switch (pot.potIndex) {
#if UNITY_SWITCH
                case 3:
                case 4:
                case 5:
                case 6:
                case 7:
                    if (RGSaveManager.Inst.ConsumeGem(pot.unlockPrice)) {
                        text_gems.text = "" + RGSaveManager.Inst.GetGem();
                        RGMusicManager.GetInstance().PlayEffect(levelup_clip);
                        OnPurchase(true);
                    } else {
                        UICanvas.GetInstance().ShowUIWinowShop(this);
                    }
                    break;
#else
                case 3:
                //TODO 修改对应解锁方式
                case 6:
                    if (RGSaveManager.Inst.ConsumeGem(pot.unlockPrice, false)) {
                        text_gems.text = "" + RGSaveManager.Inst.GetGem();
                        RGMusicManager.GetInstance().PlayEffect(levelup_clip);
                        OnPurchase(true);
                        var unlockType = emStatisticsType.UnlockGround4;
                        if (pot.potIndex == 6) {
                            unlockType = emStatisticsType.UnlockGround7;
                        }
                        ConsumeStatistics.TrackConsumeType(pot.unlockPrice, unlockType, BattleStatistics.GetBattleDataGameInfo());
                        SimpleEventManager.Raise(CurrencyPaySuccessEvent.UseCache(CurrencyType.Gem, MallItemType.Item.ToString()));
                    } else {
                        UICanvas.ShowUIWindowShop(this, buyWay: ConsumeStatistics.BuyWay.PlantPot);
                    }

                    break;
                case 4:
                case 5:
                case 7:
                    NewSDKManager.Inst.BuyProduct(payId, OnPurchase);
#if UNITY_EDITOR
                    OnPurchase(true);
#endif
                    break;
#endif
                default:
                    break;
            }
        }
    }
    public void OnPurchase(bool isSuccess) {
        if (isSuccess) {
            pot.UnLock();
            CloseBtnClick();
            var sprite = transform.Find("img/Image").GetComponent<Image>().sprite;
#if UNITY_SWITCH
            if (pot.potIndex >= 3) {
                UICanvas.GetInstance().ShowUIWinowObject(sprite, "");
            }
#else
            //人民币购买的, 弹出逻辑在到账逻辑那边
            if (pot.potIndex == 3) {
                var window = UICanvas.ShowUIWindowObject(sprite, "");
                window.onAfterHideView += dialog => {
                    SimpleEventManager.Raise(new ClosePurchaseUIEvent {});
                };
            }
#endif
        }
    }

    public void CloseBtnClick() {
        if (awake) {
            awake = false;
            UIManager.Inst.CloseUIViewDelay(this);
        }
    }

    public override void HideView(params object[] objects) {
        base.HideView(objects);
        anim.SetBool("show", false);
        UICanvas.Inst.HideGem();
        UICanvas.GetInstance().anim.SetBool("show", true);
        UICanvas.GetInstance().anim.SetBool("curtain_alpha", false);
        RGMusicManager.GetInstance().PlayEffect(8);
        GameManager.Inst.ContinueGame();
        ViewManager.RemoveView(this);
    }

    private void OnSetSupportPay(bool support) {
        btn_confirm.image.sprite = pot.potIndex == 3 || pot.potIndex == 6 
            ? buttons[0] 
            : (support? buttons[1] : buttons[2]);
    }
}