using RGScript.Other.InputControl;
using SoulKnight.Runtime.Common;
using SoulKnight.Runtime.Item;
using UIFramework;
using UnityEngine;
using UnityEngine.UI;
using Util.TAUtil;

namespace RGScript.UI {
    public class UIRoomDecorateUnlock : RGUIBaseUIView {
        private Transform _trMaterialList;
        private GameObject _itemMaterial;

        private emRoomDecorateItem _decorateItem;

        private string _blueprintName;
        private BlueprintConfig _blueprintConfig;

        private Text _furnitureText;
        private Image _furnitureImage;
        private static readonly int Show = Animator.StringToHash("show");
        private static readonly int Alpha = Animator.StringToHash("curtain_alpha");

        public override void Awake() {
            pauseWhenShow = true;
            handleEsc = true;
            
            anim = GetComponent<Animator>();
            _trMaterialList = transform.Find("detail_panel/panel/materials").transform;
            _itemMaterial = _trMaterialList.GetChild(0).gameObject;
            _itemMaterial.SetActive(false);
            _furnitureText = transform.Find("icon_panel/panel/title").GetComponent<Text>();
            _furnitureImage = transform.Find("icon_panel/panel/icon").GetComponent<Image>();
        }

        public override void OnJoystickButtonDown(JoystickButton button) {
            if (!awake) return;
            base.OnJoystickButtonDown(button);

            if (InputControl.Inst.GetAnyUICancelButtonDown()) {
                CloseBtnClick();
            }
        }

        public void ShowWindow(emRoomDecorateItem decorateItem, string titleName) {
            this._decorateItem = decorateItem;
            string decorateName = RGGameConst.ROOM_DECORATE_ITEM_DIC[decorateItem][0];
            _blueprintName = "blueprint_" + decorateName;
            _blueprintConfig = ItemConfigLoader.GetItemConfig<BlueprintConfig>(_blueprintName);
            if (_blueprintConfig == null) {
                return;
            }
            
            _furnitureImage.sprite = _blueprintConfig.Icon.GetSprite();
            this.FlushMaterials();
            _furnitureText.text = titleName;
            this.ShowWindow();
        }

        private void ShowWindow() {
            if (awake) {
                return;
            }

            anim.SetBool(Show, true);
            UICanvas.Inst.ShowGem();
            awake = true;
            UICanvas.GetInstance().anim.SetBool(Show, false);
            UICanvas.GetInstance().anim.SetBool(Alpha, true);
            RGMusicManager.GetInstance().PlayEffect(8);
        }

        public void CloseBtnClick() {
            if (!awake) {
                return;
            }

            awake = false;
            UIManager.Inst.CloseUIViewDelay(this);
        }

        public override void HideView(params object[] objects) {
            base.HideView(objects);
            anim.SetBool(Show, false);
            UICanvas.Inst.HideGem();
            UICanvas.GetInstance().anim.SetBool(Show, true);
            UICanvas.GetInstance().anim.SetBool(Alpha, false);
            RGMusicManager.GetInstance().PlayEffect(8);
        }

        public void OnClick_Unlock() {
            if (_blueprintConfig == null) {
                return;
            }
            
            if (_blueprintConfig.NotRequiredInResearch || DataUtil.IsBlueprintGot(_blueprintConfig.Name)) {
                if (ItemData.data.HasEnough(_blueprintConfig.ResearchMaterials)) {
                    var researchMaterialsDict = _blueprintConfig.ResearchMaterials.ToDictionary();
                    ItemData.data.ConsumeMaterial(researchMaterialsDict);
                    ItemData.data.ResearchBlueprint(_blueprintConfig.Name);
                    ItemData.data.UnlockRoomDecorateItem(_decorateItem, 0);
                    ItemRoomDecorateSlot.FlushSomeRoomDecorate(_decorateItem);
                    ConsumeStatistics.TrackConsumeType(emStatisticsType.UnlockDecorate, researchMaterialsDict);
                    CloseBtnClick();
                } else {
                    UICanvas.GetInstance().ShowTempMessage("object/no_enough_material", 2f);
                }
            } else {
                UICanvas.GetInstance().ShowTempMessage("object/no_enough_material", 2f);
            }
        }

        private void FlushMaterials() {
            var materials = _blueprintConfig.ResearchMaterials.ToDictionary();
            if (!_blueprintConfig.NotRequiredInResearch) {
                materials.Add(_blueprintName, 1);
            }
            
            for (int i = 0; i < _trMaterialList.childCount; i++) {
                _trMaterialList.GetChild(i).gameObject.SetActive(false);
            }

            int childIndex = 0;
            foreach (var pair in materials) {
                var tempItem = childIndex < _trMaterialList.childCount ?
                    _trMaterialList.transform.GetChild(childIndex).gameObject :
                    Instantiate(_itemMaterial, _trMaterialList);

                tempItem.SetActive(true);

                var item = tempItem.transform.Find("item").GetComponent<Widget.Item>();
                item.SetItemWithRequiredNum(pair.Key, pair.Value);

                childIndex++;
            }
        }
    }
}