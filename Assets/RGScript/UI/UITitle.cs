using System.Collections;
using ChillyRoom.RealName;
using ChillyRoom.Services.Accounts.Features;
using ChillyRoom.Services.Core.Libs;
using I2.Loc;
using UnityEngine;
using Mirror;
using NewDynamicConfig;
using RGScript.Net.Player;
using RGScript.Data;
using RGScript.UI;
using RGScript.Util;
using RGScript.Util.Fonts;
using RGScript.Util.ForcePatch;
using System.Globalization;
using UnityCommon.UI;
using UnityEngine.SceneManagement;
using I2.Loc;
using RGScript.Other;
using RGScript.Other.CloudSave;
using RGScript.Other.LegencyAdapter;
using RGScript.Other.NewSDK;
using UnityEngine.UI;
using Util.TAUtil;
using System.Collections.Generic;
using RGScript.Util.LifeCycle;
using RGScript.Util.NewConfigs;
using System;
using System.Linq;
using TaskSystem;
using UIFramework;
using Utils.CommonUtils;

#if UNITY_SWITCH
using nn.hid;
#endif

public partial class UITitle : BaseUIView {
    public static bool disconnect;
    public static string disconnect_scene;
    Animator anim;
    Animator button_quit_anim;
    internal bool awake = true;
    public GameObject music_object;
    public AudioClip bgm_clip;
    public AudioClip btn_clip;
    public Sprite titile_sprite_cn;
    public Sprite restore_icon;
    Image icon_bgm;
    Image icon_effect;

    private Transform title_root;
    private LinkImageText Tips;
    private static bool alreadyShowAnnouncement = false;
    private Canvas titleCanvas = null;
    /// <summary>
    /// 是否能进入游戏的限制
    /// </summary>
    public List<Func<bool>> CanEnterGame=new List<Func<bool>>();

    private UIWindowSetting _uiWindowSetting;
    private bool _hasShowedContinueMultiGameDialog;
    private RectTransform _announcementButtonRt;

    private static bool hasInitJax;
    public override void Awake() {
        base.Awake();
        AssetBundleLoader.Inst.UnloadUnnecessaryAssetBundles();
        
        anim = transform.GetComponent<Animator>();
        button_quit_anim = transform.Find("button_quit").GetComponent<Animator>();
        icon_bgm = transform.Find("window_setting/content/viewport/buttons/button_bgm/Image").GetComponent<Image>();
        icon_effect = transform.Find("window_setting/content/viewport/buttons/button_effect/Image").GetComponent<Image>();
        title_root = transform.Find("window_setting/title");
        if (GameObject.Find("/RGMusicManager") == null) {
            GameObject m_object = Instantiate(music_object)as GameObject;
            m_object.name = "RGMusicManager";
        }

        Tips = transform.Find("tips").GetComponent<LinkImageText>();
        FixTipText();

        // 新版预览
        InitAnnouncement();
        SimpleEventManager.AddEventListener<DataMgrInitEvent>(OnDataMgrInitEvent);

#if UNITY_SWITCH
        transform.Find("button_social").gameObject.SetActive(false);
        transform.Find("button_info").gameObject.SetActive(false);

        transform.Find("window_setting/content/viewport/buttons/button_key").gameObject.SetActive(false);
        transform.Find("window_setting/content/viewport/buttons/button_cloud_save").gameObject.SetActive(false);
        transform.Find("window_setting/content/viewport/buttons/Google_Sign_in").gameObject.SetActive(false);
        transform.Find("window_setting/content/viewport/buttons/Google_Sign_in").gameObject.SetActive(false);

        transform.Find("button_bar/btn_multiplayer").gameObject.SetActive(false);

        InputManager.AddButtonTip(transform.Find("button_bar/btn_new_game"), InputManager.Inst.submitKey.ToString());
        InputManager.AddButtonTip(transform.Find("button_bar/btn_continue"), "X");
        InputManager.AddButtonTip(transform.Find("button_setting"), "-").SetBrownColor();

        transform.Find("tips").gameObject.SetActive(false);

        transform.Find("VersionText").gameObject.SetActive(false);

        transform.Find("window_language/Grid/button_Farsi").gameObject.SetActive(false);
        transform.Find("window_language/Grid/button_Arabic").gameObject.SetActive(false);
#endif
        titleCanvas = GameObject.Find("Canvas").GetComponent<Canvas>();
        _uiWindowSetting = transform.Find("window_setting").GetComponent<UIWindowSetting>();
        if (LanguageUtil.IsChinaMainland()) {
            transform.Find("healthy_tips").gameObject.SetActive(true);
        }
        
        if (ChannelConfig.IsTapTapTest) {
            if (!hasInitJax) {
                _jax.Init();
                hasInitJax = true;
            }
            transform.Find("button_restore").gameObject.SetActive(false);
        }
        //临时处理刷新获取存档标记，用于防止云端存档一直为老数据
        if (CloudSaveUtil.HasLogin() && NewCloudSaveAgent.HasFetchData) {
            NewCloudSaveAgent.RefreshFetchDataTag();
        }
        //临时处理刷新已经下载存档标记，用于防止在主界面一直显示下载按钮
        if (CloudSaveUtil.HasLogin() && CloudSaveDataView.hasDnloadData) {
            CloudSaveDataView.hasDnloadData = false;
        }
        
        NewSDKManager.Inst.UpdateRemoteVersion();
    }

    protected override void OnBeforeDestroy() {
        base.OnBeforeDestroy();
        SimpleEventManager.RemoveListener<DataMgrInitEvent>(OnDataMgrInitEvent);
    }

    private void OnDataMgrInitEvent(DataMgrInitEvent e) {
        InitAnnouncement();
    }

    private void InitAnnouncement() {
        var announcementButton = transform.Find("button_announcement").GetComponent<Button>();
        _announcementButtonRt = announcementButton.GetComponent<RectTransform>();
        announcementButton.gameObject.SetActive(DataMgr.isInit && DataMgr.AnnouncementData.IsFeatureEnable());
        announcementButton.onClick.AddListener(OnAnnouncementButtonClicked);
    }

    private void HandleAnnouncementInfo() {
        if (ConfigManager.HasLocalConfig && !ConfigManager.NetConfigNotRelyLoginIsReady) {
            //本地
            if (ConfigManager.LocalConfigNotRelyLoginIsReady) {
                ShowAnnouncement(null);
            } else {
                SimpleEventManager.AddEventListener<LocalConfigNotRelyLoginIsReadyEvent>(ShowAnnouncement);
            }
        } else {
            // 网络
            if (ConfigManager.NetConfigNotRelyLoginIsReady) {
                ShowAnnouncement(null);
            } else {
                SimpleEventManager.AddEventListener<NetConfigNotRelyLoginIsReadyEvent>(ShowAnnouncement);
            }
        }
    }
    
    public void ShowAnnouncement(EventBase e) {
        if (PrivacyPolicyUtil.NeedShowPrivacyPolicy && !PrivacyPolicyUtil.HasAgreePrivacyPolicy) {
            // 没用同意隐私协议之前 不弹出公告
        } else {
            SimpleEventManager.RemoveListener<LocalConfigNotRelyLoginIsReadyEvent>(ShowAnnouncement);
            SimpleEventManager.RemoveListener<NetConfigNotRelyLoginIsReadyEvent>(ShowAnnouncement);
            if (!alreadyShowAnnouncement) {
                alreadyShowAnnouncement = true;
                NewSDKManager.Inst.ShowAnnouncementWindow();
            }
        }
    }
    
    /// <summary>
    /// 新版本预览
    /// </summary>
    private void OnAnnouncementButtonClicked() {
        DataMgr.AnnouncementData.OpenUI(_announcementButtonRt);
        UIStatistics.TrackInGameAnnouncement("click_button", 0);
        RGMusicManager.GetInstance().PlayEffect(RGMusicManager.emEffect.Button);
    }
    
    private void OnEnable() {
#if UNITY_SWITCH
        InputManager.Inst.AddForce(gameObject);
        InputManager.Inst.AddKeyBinding(gameObject, InputManager.Inst.submitKey, delegate {
            return anim.GetInteger("show_window") == 0 &&
                anim.GetBool("show_setting_bar") == false &&
                anim.GetBool("show_btn_bar");
        }, () => {
            BtnNewGameClick();
        });

        InputManager.Inst.AddKeyBinding(gameObject, InputManager.Inst.submitKey, delegate {
            return anim.GetInteger("show_window") == 0 &&
                anim.GetBool("show_setting_bar") == false &&
                anim.GetBool("show_btn_bar") == false;
        }, () => {
            anim.SetBool("show_btn_bar", true);
            OnShowBtnBar();
        });

        InputManager.Inst.AddKeyBinding(gameObject, NpadButton.X, delegate {
            return anim.GetInteger("show_window") == 0 &&
                anim.GetBool("show_setting_bar") == false &&
                anim.GetBool("show_btn_bar") &&
        }, () => {
            BtnContinueClick();
        });

        InputManager.Inst.AddKeyBinding(gameObject, InputManager.Inst.cancelKey, delegate {
            return anim.GetBool("show_setting_bar") == true &&
                uIWindowKeyboard.isWaitKeyPress == false;
        }, () => {
            HideSettingBar();
        });

        InputManager.Inst.AddKeyBinding(gameObject, NpadButton.Minus, delegate {
            return anim.GetBool("show_setting_bar") == false &&
                uIWindowKeyboard.isWaitKeyPress == false;
        }, () => {
            ShowSettingBar();
        });
#endif
        LocalizationManager.OnLocalizeEvent+=FixTipText;
        LifeCycleManager.Instance.OnAccountChange += RefreshData;
    }

    private void OnDisable() {
#if UNITY_SWITCH
        InputManager.Inst.RemoveForce(gameObject);
        InputManager.Inst.RemoveKeyBinding(gameObject);
#endif
        LocalizationManager.OnLocalizeEvent-=FixTipText;
        LifeCycleManager.Instance.OnAccountChange -= RefreshData;
        RGMusicManager.GetInstance()?.StopBgm();
    }

    void Start() {
        UIManager.Inst.OpenExistedUIView(this);
        CountEnterUITitle();
        
        OnStartBegin();

        NetControllerManager.Inst.Clear();
        if (RGMusicManager.GetInstance().bgm.clip == null || !RGMusicManager.GetInstance().bgm.isPlaying || RGMusicManager.GetInstance().bgm.clip.name != bgm_clip.name ) {
            RGMusicManager.GetInstance().PlayBGM(bgm_clip, RGMusicManager.emBGM.UITitle);
        }
        if (RGMusicManager.GetInstance().open_bgm) {
            icon_bgm.color = ColorManager.LightColor;
        } else {
            icon_bgm.color = ColorManager.BackColor;
        }
        if (RGMusicManager.GetInstance().open_effect) {
            icon_effect.color = ColorManager.LightColor;
        } else {
            icon_effect.color = ColorManager.BackColor;
        }
        if (PlayerSaveData.Inst.has_setLanguage == 0) {
            SystemLanguage lang = Application.systemLanguage;
            if (lang == SystemLanguage.ChineseSimplified || lang == SystemLanguage.Chinese) {
                SwitchLanguage("Chinese");
            } else if (lang == SystemLanguage.ChineseTraditional) {
                SwitchLanguage("Chinese (Traditional)");
            } else if (lang == SystemLanguage.Russian) {
                SwitchLanguage("Russian");
            } else if (lang == SystemLanguage.German) {
                SwitchLanguage("German");
            } else if (lang == SystemLanguage.Portuguese) {
                SwitchLanguage("Portuguese");
            } else if (lang == SystemLanguage.French) {
                SwitchLanguage("French");
            } else if (lang == SystemLanguage.Japanese) {
                SwitchLanguage("Japanese");
            } else if (lang == SystemLanguage.Korean) {
                SwitchLanguage("Korean");
            } else if (lang == SystemLanguage.Spanish) {
                SwitchLanguage("Spanish");
            } else if (lang == SystemLanguage.Arabic) {
                SwitchLanguage("English");
            } else if (ChannelConfig.IsBazaar) {
                SwitchLanguage("Farsi (Iran)");
            } else if (lang == SystemLanguage.Thai) {
                SwitchLanguage("Thai");
            } else if (lang == SystemLanguage.Vietnamese) {
                SwitchLanguage("Vietnamese");
            } else {
                SwitchLanguage("English");
            } 
            SwitchFontAfterSwitchLanguage();
            PlayerSaveData.Inst.has_setLanguage = 1;
        }
        RefreshButtonBar();
#if UNITY_IPHONE && !UNITY_EDITOR
        transform.Find("window_setting/content/viewport/buttons/Google_Sign_in/Image").GetComponent<Image>().sprite = restore_icon;
        if (Application.systemLanguage == SystemLanguage.Chinese ||
            Application.systemLanguage == SystemLanguage.ChineseSimplified) {
            transform.Find("button_social").gameObject.SetActive(false);
            transform.Find("window_setting/title").GetComponent<Image>().sprite = titile_sprite_cn;
        }
#endif
        if (disconnect) {
            ShowReconnectDialog();
        }
        ShowLoginLoading(true);
        UIWindowNetBroadcast.TryShowWindow();

        #region 显示version 和mac

        transform.Find("window_language/button_DeviceId").gameObject.SetActive(false);
        #endregion

#if UNITY_SWITCH
        var textRectTransformNew = transform.Find("button_bar/btn_new_game/ButtonLayout(Clone)/Text").GetComponent<RectTransform>();
        textRectTransformNew.anchorMin = new Vector2(0, 1);
        textRectTransformNew.anchorMax = new Vector2(0, 1);
        textRectTransformNew.pivot = new Vector2(0, 0);
        textRectTransformNew.sizeDelta = new Vector2(200, 70);

        var textRectTransform = transform.Find("button_bar/btn_continue/ButtonLayout(Clone)/btn_info").GetComponent<RectTransform>();
        textRectTransform.anchorMin = new Vector2(0, 1);
        textRectTransform.anchorMax = new Vector2(0, 1);
        textRectTransform.pivot = new Vector2(0, 0);
        textRectTransform.sizeDelta = new Vector2(200, 90);
#endif
        
        HandleAnnouncementInfo();
        OnStartEnd();
        DealNetTime();
        RefreshTitleIcon();
        // AssetBundleLoader.Inst.UnloadModeBundles();

        DealSocialBtn();
        UIWindowGameCenter.AutoPop();
        HandleGameCenterBtn();
    }

    /// <summary>
    /// 处理GameCenter奖励弹窗按钮
    /// </summary>
    private void HandleGameCenterBtn() {
        var gameCenterBtn = transform.Find("button_game_center").GetComponent<Button>();
        gameCenterBtn.onClick.RemoveAllListeners();
        if (ChannelConfig.IsAllIOS) {
            gameCenterBtn.gameObject.SetActive(true);
            gameCenterBtn.onClick.AddListener(UIWindowGameCenter.ShowWindow);
        } else {
            gameCenterBtn.gameObject.SetActive(false);
        }
    }

    /// <summary>
    /// 处理社媒按钮
    /// </summary>
    private void DealSocialBtn() {
        var socialBtn = transform.Find("button_social");
        if (NewSDKManager.Inst.NeedShowSocialBtn) {
            socialBtn.GetComponent<Button>().onClick.AddListener(ShowSocialPage);            
        } else {
            socialBtn.gameObject.SetActive(false);
        }
        
        NewSDKManager.Inst.BindSocialPromoteUITree();
    }

    private void ShowSocialPage() {
        NewSDKManager.Inst.ShowSocialPromotionPage();
    }

    private static void DealNetTime() {
        if (!NetTime.GotNetworkTime || (NetTime.GotNetworkTime && NetTimeUtil.CurrentTime.Item1 &&
                                        (NetTimeUtil.CurrentTime.Item2 - NetTime.Time).TotalMinutes > 1)) {
            NetTime.UpdateNetTime();
        }
    }

    private static void CountEnterUITitle() {
        int thisDay = NetTime.Time.Subtract2000_01_01Day();
        if (PlayerSaveData.Inst.last_recode_enter_ui_title != thisDay) {
            // 今天第一次进游戏
            PlayerSaveData.Inst.last_recode_enter_ui_title = thisDay;
            PlayerSaveData.Inst.enter_ui_title_daily = 1;
        } else {
            PlayerSaveData.Inst.enter_ui_title_daily++;
        }

        SimpleEventManager.Raise(new EnterUITitleEvent { enterCountDaily = PlayerSaveData.Inst.enter_ui_title_daily });
    }

    private void KillMyself() {
        Application.Quit();
    }

    public override void OnJoystickButtonDown(JoystickButton button) {
        base.OnJoystickButtonDown(button);
        switch (button) {
            case JoystickButton.LeftShoulder1:
                if (anim.GetBool("show_setting_bar")) {
                    HideSettingBar();
                } else {
                    if (anim.GetBool("show_btn_bar"))
                        BtnNewGameClick();
                    else
                        OnBtnClick();
                }
                break;
            case JoystickButton.RightShoulder1:
                if (anim.GetBool("show_setting_bar")) {
                    HideSettingBar();
                } else {
                    if (anim.GetBool("show_btn_bar"))
                        BtnContinueClick();
                    else
                        OnBtnClick();
                }
                break;
        }
    }

    void Update() {
#if UNITY_SWITCH 
#else
        if (Input.GetKeyDown(KeyCode.H)) {
            OnBtnClick();
        }
        if (Input.GetButtonDown("BtnL")) {
            if (anim.GetBool("show_setting_bar")) {
                HideSettingBar();
            } else {
                if (anim.GetBool("show_btn_bar"))
                    BtnNewGameClick();
                else
                    OnBtnClick();
            }
        }
        if (Input.GetButtonDown("BtnR")) {
            if (anim.GetBool("show_setting_bar")) {
                HideSettingBar();
            } else {
                if (anim.GetBool("show_btn_bar"))
                    BtnContinueClick();
                else
                    OnBtnClick();
            }
        }

        if (InputManager.Inst.IsPassCancelKey()) {
            var animWinVal = anim.GetInteger("show_window");
            if (ViewManager.RunNavigationEvent(NavigationEvent.Back)) {
                //ViewManager已经处理了这个返回操作
            } else if (animWinVal == 3) { //处理联机界面
                GetComponentInChildren<UIWindowMultiplayer>().CloseBtnClick();
            } else if (animWinVal == 7) {//远程联机界面
                GetComponentInChildren<UIWindowMultiplayerRemote>().CloseBtnClick();
            } else if (animWinVal != 0) {
                anim.SetInteger("show_window", 0);
            } else if (anim.GetBool("show_btn_bar")) {
                anim.SetBool("show_btn_bar", false);
                OnHideBtnBar();
            } else {
                button_quit_anim.SetBool("show", !button_quit_anim.GetBool("show"));
            }
        }
#endif
    }

    public void ShowSettingBar() {
        // _uiWindowSetting.Show();
        // anim.SetBool("show_setting_bar", true);
        //2023三月版本，新设置界面
        UIWindowGameSetting.ShowWindow(true);
        BindEmailAndPhoneManager.Instance.BindUITree();
#if UNITY_SWITCH
        var g = GameObject.Find("Canvas/title_scene/window_setting/content/viewport/buttons/button_effect").gameObject;
        UiNavigationManager.Inst.Select(g);
#endif
    }

    public void HideSettingBar() {
        _uiWindowSetting.Hide();
        anim.SetBool("show_setting_bar", false);
        anim.SetInteger("show_window", 0);
#if UNITY_SWITCH
        UiNavigationManager.Inst.Select(null);
#endif
    }
    
    public void OnlyHideSettingBar() {
        anim.SetBool("show_setting_bar", false);
    }

    private void ShowContinueMultiGameDialog() {
        if (_hasShowedContinueMultiGameDialog) {
            return;
        }

        var battleData = BattleDataList.list.GetBattleData(savePlace);
        if (!IsBattleDataCanReconnection(battleData)) {
            return;
        }

        _hasShowedContinueMultiGameDialog = true;
        BtnContinueClick();
    }

    public void OnBtnClick() {
        if (PlayerSaveData.Inst.has_tutorial != 1) {
            EnterScenePlot();
            return;
        }
        if (anim.GetBool("show_setting_bar")) {
            HideSettingBar();
        } else {
            if (awake) {
                if (button_quit_anim.GetBool("show")) {
                    button_quit_anim.SetBool("show", false);
                } else {
                    anim.SetBool("show_btn_bar", !anim.GetBool("show_btn_bar"));
                    if (anim.GetBool("show_btn_bar")) {
                        OnShowBtnBar();
                    } else {
                        OnHideBtnBar();
                    }
                }
            }
        }

        ShowContinueMultiGameDialog();
    }

    void NextScene() {
        if (PlayerSaveData.Inst.has_tutorial == 1) {
            MultiGameManager.Inst.ContinueGame = false;
            StartCoroutine(StartingNextScene("HeroRoom"));
        } else {
            //如果玩家下载了一个空存档 此时可能是通过新游戏按钮调用到此处awake已经是true了，故此时先置为false，不然会卡在主界面
            awake = true;
            EnterScenePlot();
        }
    }
    void EnterScenePlot() {
        if (!awake) {
            return;
        }

        if (CanEnterGame.Any(_=>!_.Invoke())) {
            return;
        }
        
        PlayerSaveData.Inst.has_tutorial = 1;
        PlayerSaveData.Inst.tutorial_end_time = NetTime.Time.Ticks.ToString(CultureInfo.InvariantCulture);
        RGGameProcess.Inst.this_index = -1;
        //记录开启新引导流程
        StatisticData.data.RecordEvent(RGGameConst.NEED_NEW_PLAYER_TUTORIAL, false);

        NewPlayerProcess();

        StatisticData.data.RecordEvent(RGGameConst.NEED_SHOW_MULTI_REMOTE_GUIDE, false);
        StatisticData.data.RecordEvent(RGGameConst.NEW_PLAYER_MULTI_REMOTE_GUIDE, false);
        StatisticData.data.AddEventCount(RGGameConst.CAREER_TASK_BUBBLE_TIP, 3, true);

        if(LogUtil.IsShowLog){LogUtil.Log($"新手因子log : 新玩家进入教程，设置参数");}

        //新玩家使用新的UI布局
        SettingData.data.useNewLayout = true;
        SettingData.data.ResetPositions();
        // 进入新手流程的人，默认打开角色选择描边
        SettingData.data.enableCharacterSelectOutline = true;
        SettingData.Save();
        TAUtil.Track("click_enter_tourial");
        
        transform.Find("background_click").GetComponent<Button>().interactable = false;
        transform.Find("tap_to_start").gameObject.SetActive(false);
        awake = false;
        SimpleEventManager.Raise(EnterScenePlotEvent.UseCache());
        StartCoroutine(StartingNextScene("Scene_Plot"));
        StatisticData.data.SetEventCount("BeginnerTaskState", 1, true);
        if (TaskManager.Instance.CheckLockBeginnerTask()) {
            TaskManager.Instance.CreateNewBeginnerTaskByConfig();
        }
    }

    /// <summary>
    /// 新手流程ABTest
    /// 根据分组决定是否开启新手关卡
    /// </summary>
    private void NewPlayerProcess() {
        var group = ABTest.GetNewPlayerLevelTestGroup();
        switch (group) {
            case ABTestGroup.None:
                NewPlayerFactorProcess();
                break;
            case ABTestGroup.A:
                NewPlayerFactorProcess();
                break;
            case ABTestGroup.B:
                GameUtil.InitNewPlayerLevelCount();
                break;
            case ABTestGroup.C:
                GameUtil.InitNewPlayerLevelCount();
                break;
            case ABTestGroup.D:
                GameUtil.InitNewPlayerLevelCount();
                break;
            case ABTestGroup.E:
                GameUtil.InitNewPlayerLevelCount();
                break;
            case ABTestGroup.F:
                GameUtil.InitNewPlayerLevelCount();
                break;
            case ABTestGroup.G:
                GameUtil.InitNewPlayerLevelCount();
                break;
            default:
                NewPlayerFactorProcess();
                break;
        }
    }

    private void NewPlayerFactorProcess() {
        StatisticData.data.SetEventCount(RGGameConst.NEW_PLAYER_FACTOR_COUNT, RGGameConst.NEW_PLAYER_ENTER_NORMAL_MODE_VALUE, true);
    }

    public static IEnumerator StartingNextScene(string sceneName) {
        //发起进入游戏事件
        SimpleEventManager.Raise(new EnterGameEvent(){SceneName = sceneName});
        if (sceneName == "Scene_Plot") {
            yield return AssetBundleLoader.Inst.LoadBundle(AssetBundleLoader.LevelCommon);
            yield return AssetBundleLoader.Inst.LoadBundle(AssetBundleLoader.LevelObjects);
            yield return AssetBundleLoader.Inst.LoadBundle(AssetBundleLoader.PatternRoom);
            yield return AssetBundleLoader.Inst.LoadBundle("level/1/a");
            var group = ABTest.Get710FirstTutorialTestGroup();
            if (group == ABTestGroup.B) {
                AssetBundleLoader.Inst.UnLoadBundle(AssetBundleLoader.TutorialAbTestGroupB); // 这里要卸载一下，因为这里面的prefab引用了level/1/a内的怪物。level/1/a的卸载会导致这里关系断掉从而导致丢怪物引用
                yield return AssetBundleLoader.Inst.LoadBundle(AssetBundleLoader.TutorialAbTestGroupB);
                yield return AssetBundleLoader.Inst.LoadBundle("boss/boss30");
                yield return AssetBundleLoader.Inst.LoadBundle("bgm/bgm_2high");
            } else {
                AssetBundleLoader.Inst.UnLoadBundle(AssetBundleLoader.Tutorial); // 这里要卸载一下，因为这里面的prefab引用了level/1/a内的怪物。level/1/a的卸载会导致这里关系断掉从而导致丢怪物引用
                yield return AssetBundleLoader.Inst.LoadBundle(AssetBundleLoader.Tutorial);
            }
        }
        yield return new WaitForEndOfFrame();
        yield return AssetBundleLoader.Inst.LoadScene(sceneName);
    }
    /// <summary>
    /// 退出游戏 注意此方法在不通分支实现不一样
    /// </summary>
    public static void StaticBtnQuitClick() {
        NewSDKManager.Inst.ShowExitDialog();
    }
 
    public void BtnQuitClick() {
        StaticBtnQuitClick();
    }

    public void BtnNewGameClick() {
        if (!awake) {
            return;
        }

        if (CanEnterGame.Any(_=>!_.Invoke())) {
            return;
        }
        
        //赛季指引
        var isSeasonMode = StatisticData.data.GetEventCount(RGGameConst.TITLE_NEW_GAME_MODE) == 1;
        if (isSeasonMode) {
            if (!SeasonGuide.HasShowSeasonGuide && Season.IronTide != ConfigManager.GetCurrectUseConfig<SeasonConfig>().Season) {
                SeasonGuide.ShowWindow();
                return;
            }
        }

        UIStatistics.TrackEnterGame(emGameType.Single, isSeasonMode ? emGameMode.IronTide : emGameMode.Normal);

        BehaviourPathStatistics.TrackBehaviour(
            emBehaviourPoint.UI_Title_Enter, new Dictionary<string, object> {
                {"game_type", emGameType.Single.ToString()},
                {"enable_pure_multi_game", BattleData.data.netSyncData.enablePureMultiGame},
            });
        
        awake = false;
        RGGameProcess.Inst.ReSetInfo(true, false);
        RGMusicManager.GetInstance().PlayEffect(btn_clip);
        BattleData.Reset();
        NextScene();
        OnlyHideSettingBar();
        SimpleEventManager.Raise(PressNewGameButtonEvent.UseCache());
    }

    public void BtnMultiplayerClick() {
        if (!awake) {
            return;
        }

        if (CanEnterGame.Any(_=>!_.Invoke())) {
            return;
        }

        // 强制热更新动态配置判断入口开关
        if (!ForcePatchManager.IsEntryOpen(ForcePatchEntry.MultiPlayer)) {
            return;
        }
        
        if (!IsCompleteToturial()) {
            return;
        }
        
        UIStatistics.TrackEnterGame(emGameType.LocalMulti, emGameMode.Normal);
        BehaviourPathStatistics.TrackBehaviour(
            emBehaviourPoint.UI_Title_Enter, new Dictionary<string, object> {
                {"game_type", emGameType.Single.ToString()},
                {"enable_pure_multi_game", BattleData.data.netSyncData.enablePureMultiGame},
            });
        
        ShowTitleRoot(false);
        MultiGameManager.Inst.Lan_Game = true;
        MultiGameManager.Inst.EnterFromContinueGame = false;
        anim.SetInteger("show_window", 3);
        OnlyHideSettingBar();
    }
    
#if UNITY_EDITOR
    public static bool alwaysCanContinue = true;
#endif

    public void BtnContinueClick() {
        if (CanEnterGame.Any(_=>!_.Invoke())) {
            return;
        }
        
        NewSDKManager.Inst.UpdateRemoteVersion();
        if (awake) {
            ShowTitleRoot(false);
        }

        if (!awake || !BattleDataList.list.IsBattleDataValid(savePlace)) {
            return;
        }

        OnlyHideSettingBar();

        var battleData = BattleDataList.list.GetBattleData(savePlace);
        
        // 继续游戏判断沙盒模式功能开关
        if (battleData.IsSandbox) {
            if (!ModuleUtil.IsSandboxModuleEnable()) {
                return;
            }
        }
        
        //检测存档是否已经结算过
        if (BattleDataList.HasBattleDataDuplicateStatement(battleData)) {
            ShowEndSaveDataDialog();
            EndSaveData();
            OnStartBegin();
            RefreshBtnImage();
            RefreshButtonBar();
            return;
        }
        
        Debug.Log($"battleData season:{battleData.season} cfgSeason:{ConfigManager.GetCurrectUseConfig<SeasonConfig>().Season} gmode:{battleData.gameMode}");
        if (battleData.IsDefenceMode && battleData.IsOldDefenceMode) {
            // 老神殿存档处理
            UIWindowDialogTextSelect.TextSelectDialogParam param =
                new UIWindowDialogTextSelect.TextSelectDialogParam {
                    title = ScriptLocalization.Get("I_tip", "提示"),
                    content = ScriptLocalization.Get("multi/continue_or_end", "要在当前存档的基础上立即结算还是继续游戏？"),
                    text1 = ScriptLocalization.Get("multi/end_game", "立即结算")
                };
            UIWindowDialogTextSelect.ShowDialogTextSelect(transform.parent, param, EndSavedataGame);
        } else if (battleData.GetSeason() != Season.None &&
                   battleData.GetSeason() != ConfigManager.GetCurrectUseConfig<SeasonConfig>().Season) {
            // 赛季已经结束，赛季存档不可继续的情况
            UIWindowDialogTextSelect.TextSelectDialogParam param =
                new() {
                    title = ScriptLocalization.Get("I_tip", "提示"),
                    content = string.Format(
                        ScriptLocalization.Get("multi/season_save_end", "赛季\"{0}\"已经结束，不可继续游戏，点击下方按钮立即结算。"),
                        ScriptLocalization.Get("I_season_" + battleData.GetSeason().ToString())),
                    text1 = ScriptLocalization.Get("multi/end_game", "立即结算")
                };
            UIWindowDialogTextSelect.ShowDialogTextSelect(transform.parent, param, EndSavedataGame);
        } else if (battleData.season == Season.ComboGun && battleData.cgMode is {
                   } data && data.version < ModeSeason.ComboGun.CGModeData.CurrentVersion) {
            UIWindowDialogTextSelect.TextSelectDialogParam param =
                new() {
                    title = ScriptLocalization.Get("I_tip", "提示"),
                    content = ScriptLocalization.Get("tips/version_behind", "存档版本已落后，不可继续游戏，点击下方按钮立即结算。"),
                    text1 = ScriptLocalization.Get("multi/end_game", "立即结算")
                };
            UIWindowDialogTextSelect.ShowDialogTextSelect(transform.parent, param, EndSavedataGame);
        } else if (battleData.season == Season.Troop2 && battleData.troop2Mode is {
                   } troop2BattleData && troop2BattleData.version < ModeSeason.Troop2.Troop2BattleData.CurrentVersion) {
            UIWindowDialogTextSelect.TextSelectDialogParam param =
                new() {
                    title = ScriptLocalization.Get("I_tip", "提示"),
                    content = ScriptLocalization.Get("tips/version_behind", "存档版本已落后，不可继续游戏，点击下方按钮立即结算。"),
                    text1 = ScriptLocalization.Get("multi/end_game", "立即结算")
                };
            UIWindowDialogTextSelect.ShowDialogTextSelect(transform.parent, param, EndSavedataGame);
        } else if((battleData.gameMode == emGameMode.Normal && !battleData.IsLoopTravel && battleData.startBigLevelIndex == 1) ||
                  (battleData.IsArtifacts && battleData.levelIndex > RGGameConst.NormalMaxLevelIndexCount)) { // 5A旧存档处理
            UIWindowDialogTextSelect.TextSelectDialogParam param =
                new() {
                    title = ScriptLocalization.Get("I_tip", "提示"),
                    content = ScriptLocalization.Get(
                        "tips/5a_Cancelled", "原第五大关「海底」已迁移至第四大关，并取消从第二大关开始游戏的功能。" +
                                             "\n该存档版本与新关卡结构不兼容，为保证存档的战斗收益，请立即结算当前存档。"),
                    text1 = ScriptLocalization.Get("multi/end_game", "立即结算")
                };
            UIWindowDialogTextSelect.ShowDialogTextSelect(transform.parent, param, EndSavedataGame);
        } else {
            //排除其他游戏模式的干扰
            UIWindowDialogTextSelect.TextSelectDialogParam param;
            if (IsBattleDataCanReconnection(battleData)) {
                param =
                    new UIWindowDialogTextSelect.TextSelectDialogParam {
                        title = ScriptLocalization.Get("I_tip"),
                        content = ScriptLocalization.Get("tips/continue_multi_game"),
                        text1 = ScriptLocalization.Get("multi/end_game"),
                        text2 = ScriptLocalization.Get("multi/continue_game")
                    };
            } else {
                param =
                    new UIWindowDialogTextSelect.TextSelectDialogParam {
                        title = ScriptLocalization.Get("I_tip", "提示"),
                        content = ScriptLocalization.Get("multi/continue_or_end", "要在当前存档的基础上立即结算还是继续游戏？"),
                        text1 = ScriptLocalization.Get("multi/end_game", "立即结算"),
                        text2 = ScriptLocalization.Get("multi/continue_game", "继续游戏"),
                    };
            }
            UIWindowDialogTextSelect.ShowDialogTextSelect(transform.parent, param, ContinueGame, EndSavedataGame);
        }
    }

    public void BtnGooglePlayClick() {
    }
    
    public void RestorePurchases() {
        BtnRestoreClick();
    }
    
    public static void StaticSwitchLanguage(string language) {
        if (LocalizationManager.HasLanguage(language)) {
            string lastLanguage = LocalizationManager.CurrentLanguage;
            LocalizationManager.CurrentLanguage = language;
            SimpleEventManager.Raise(new ChangeLanguageEvent(){FromLanguage = lastLanguage, ToLanguage = LocalizationManager.CurrentLanguage});
            ChangeChillyRoomServiceLanguageHeader();
        }
        LanguageUtil.ResetRTL();
    }

    public static void ChangeChillyRoomServiceLanguageHeader() {
        Generated.ChillyRoomSdkClient.ChillyRoomService service = Generated.ChillyRoomSdkClient.ChillyRoomService.GetSdk();
        if (service != null && service.Core != null) {
            SystemLanguage lang = GameUtil.GetGameLanguage();
            var langStr = lang.ToString(System.Globalization.CultureInfo.InvariantCulture);
            if (langStr == "Arabic") {
                langStr = "ArabicEgypt";
            }
            service.Core.Context.GameLang = langStr;
        }
    }

    public static void SwitchLanguageBySystemLanguage() {
        if (PlayerSaveData.Inst.has_setLanguage == 0) {
            SystemLanguage lang = Application.systemLanguage;
            if (lang == SystemLanguage.ChineseSimplified || lang == SystemLanguage.Chinese) {
                StaticSwitchLanguage("Chinese");
            } else if (lang == SystemLanguage.ChineseTraditional) {
                StaticSwitchLanguage("Chinese (Traditional)");
            } else if (lang == SystemLanguage.Russian) {
                StaticSwitchLanguage("Russian");
            } else if (lang == SystemLanguage.German) {
                StaticSwitchLanguage("German");
            } else if (lang == SystemLanguage.Portuguese) {
                StaticSwitchLanguage("Portuguese");
            } else if (lang == SystemLanguage.French) {
                StaticSwitchLanguage("French");
            } else if (lang == SystemLanguage.Japanese) {
                StaticSwitchLanguage("Japanese");
            } else if (lang == SystemLanguage.Korean) {
                StaticSwitchLanguage("Korean");
            } else if (lang == SystemLanguage.Spanish) {
                StaticSwitchLanguage("Spanish");
            } else if (lang == SystemLanguage.Thai) {
                StaticSwitchLanguage("Thai");
            } else if (lang == SystemLanguage.Vietnamese) {
                StaticSwitchLanguage("Vietnamese");
            } else {
                StaticSwitchLanguage("English");
            }
            PlayerSaveData.Inst.has_setLanguage = 1;
        }
    }

    public void SwitchLanguage(string language) {
        StaticSwitchLanguage(language);
        anim.SetInteger("show_window", 0);
#if UNITY_SWITCH
        HideSettingBar();
#endif
        SwitchFontAfterSwitchLanguage();
        transform.Find("window_setting/title/title_cn").GetComponent<ShowInChannelConfig>().Refresh();
        transform.Find("window_setting/title/title_en").GetComponent<ShowInChannelConfig>().Refresh();
        RefreshTitleIcon();
        onSwitchLanguage?.Invoke();
    }
    
    private void RefreshTitleIcon() {
        if (ChannelConfig.IsAllVNM) {
            transform.Find("window_setting/title/title_cn").gameObject.SetActive(false);
            transform.Find("window_setting/title/title_en").gameObject.SetActive(false);
            transform.Find("window_setting/title/title_vn").gameObject.SetActive(true);
        } else {
            transform.Find("window_setting/title/title_cn").GetComponent<ShowInChannelConfig>().Refresh();
            transform.Find("window_setting/title/title_en").GetComponent<ShowInChannelConfig>().Refresh();
        }
    }
    
    static void SwitchFontAfterSwitchLanguage() {
        if (LanguageUtil.ForceChangeFont()) {
            FontUtil.Inst.SetFont(DataUtil.GetFontSetting());
        }
    }
    
    public void BtnBgmClick() {
        if (RGMusicManager.GetInstance().SwitchBgmOpen()) {
            icon_bgm.color = ColorManager.LightColor;
        } else {
            icon_bgm.color = ColorManager.BackColor;
        }
        RGMusicManager.GetInstance().PlayEffect(btn_clip);
    }

    public void BtnEffectClick() {
        if (RGMusicManager.GetInstance().SwitchEffectOpen()) {
            icon_effect.color = ColorManager.LightColor;
        } else {
            icon_effect.color = ColorManager.BackColor;
        }
        RGMusicManager.GetInstance().PlayEffect(btn_clip);
    }

    public void BtnWindowClick(int value) {
        if (anim.GetInteger("show_window") != value) {
            // 礼包码界面在本地存档槽位确定之前不能交互 因为可能会领取到默认存档槽造成数据丢失
            if (value == 2 && !NewSDKManager.IsLogined()) {
                UICanvasRoot.Inst.ShowMessage("请先登录渠道账号再使用礼包码",2f);
                return;
            }
            anim.SetInteger("show_window", value);
            
            // 隐私弹窗隐藏logo
            ShowTitleRoot(value != 8);

#if UNITY_SWITCH
            if (value == 1) {
                var g = GameObject.Find("Canvas/title_scene/window_language/Grid/button_English").gameObject;
                UiNavigationManager.Inst.Select(g);
            }

            if (value == 5) {
                var g = GameObject.Find("Canvas/title_scene/window_keyboard/btn_root/btn_save").gameObject;
                UiNavigationManager.Inst.Select(g);
            }
#endif

            if (value == 6) { //广播窗口
                RefreshBroadcastInfoWindow();
            }

        } else {
            anim.SetInteger("show_window", 0);
            ShowTitleRoot(true);
        }
    }
    
    public void BtnFaceBookClick() {
        Application.OpenURL("https://www.facebook.com/chillyroomgamesoulknight/");
    }

    public void BtnTwitterClick() {
        Application.OpenURL("https://twitter.com/ChillyRoom");
    }

    public void BtnTapTapClick() {}

    public void BtnRestoreClick() {
        if (NewSDKManager.Inst.ShouldLogin(BtnRestoreClick)) {
            return;
        }

        var orderPage = UIManager.Inst.OpenUIView<UIWindowShowHistoryOrders>("window_show_history_orders");
        SimpleEventManager.Raise(new RefreshCloudPackageEvent());
        orderPage.OnFetchedOrders = (Payment.RestoreOrdersResult? ordersResult) => {
            // 等拉取历史订单（会校验一遍未发货订单并转Claimed状态），再补单
            NewSDKManager.Inst.RestoreAllOrders(ordersResult);
            orderPage.OnFetchedOrders = null;
        };
    }
    
    public void BtnPurchaseRecordClick() {}

    public void ShowTempMessage(string value, float e_time, bool needLocalize = true) {
        if (GameObject.Find("/Canvas/temp_message") == null) {
            GameObject temp_obj = Instantiate(ResourcesUtil.Load(
                "RGPrefab/UI/temp_message.prefab"))as GameObject;
            temp_obj.transform.SetParent(transform.parent, false);
            temp_obj.name = "temp_message";
            temp_obj.GetComponent<UITempMessage>().SetMessage(value, e_time, needLocalize);
        } else {
            GameObject.Find("/Canvas/temp_message").GetComponent<UITempMessage>().SetMessage(value, e_time, needLocalize);
        }
    }

    public void BtnKtPlayClick() {
    }

    UIWindowCloudSave windowCloussave {
        get {
            return transform.Find("window_cloudsave").GetComponent<UIWindowCloudSave>();
        }
    }
    
    public void OnClick_CloudSave() {
        if (!awake) {
            return;
        }
        
        if (!NewSDKManager.Inst.ShouldLogin(OnClick_CloudSave)) {
            NewCloudSaveAgent.ShowView();
        }

        SimpleEventManager.Raise(new CustomEvent(RGGameConst.ClickCloudSaveBtn));
    }

    void ShowReconnectDialog() {
        if (disconnect) {
            disconnect = false;
            switch (disconType) {
                case DisconnectType.Normal:
                    ShowTempMessage(ScriptLocalization.Get("net/disconnect"), 10f, false);        
                    break;
                case DisconnectType.Kick:
                    ShowTempMessage(ScriptLocalization.Get("multi/kicked"), 10f, false);
                    break;
                case DisconnectType.RoomDestroy:
                    ShowTempMessage(ScriptLocalization.Get("multi/room_timeout_destroy"), 10f, false);
                    break;
                case DisconnectType.UnSelectHero:
                    ShowTempMessage(ScriptLocalization.Get("multi/unselect_hero_timeout"), 10f, false);
                    break;
            }

            disconType = DisconnectType.Normal;
            return;
        }
        disconnect = false;
        UIWindowDialog.ShowDialog(transform, ScriptLocalization.Get("net/timeout"), ScriptLocalization.Get("net/timeout_dialog"),
            () => {
                float duration = 10f;
                NetworkManager.singleton.networkAddress = BattleData.data.serverAddress;
                NetworkManager.singleton.StartClient();
                ShowTempMessage(ScriptLocalization.Get("net/reconnecting"), duration, false);
                ShowUiLoading(duration);
                Invoke("StopReconnect", duration);
            }, null);
    }
    
    
    void StopReconnect() {
        NetworkManager.singleton.StopClient(NetworkConnection.DisconnectReasonEnum.ClientSelfQuit);
        NetworkManager.singleton.networkAddress = "";
        ShowTempMessage(ScriptLocalization.Get("net/reconnect_fail"), 2f, false);
    }
    
    public void ShowUiLoading(float duration) {
        if (GameObject.Find("/Canvas/ui_loading") == null) {
            GameObject temp_obj = Instantiate(ResourcesUtil.Load(
                "RGPrefab/UI/ui_loading.prefab"))as GameObject;
            temp_obj.name = "ui_loading";
            temp_obj.transform.SetParent(transform, false);
            temp_obj.GetComponent<RGAutoDestoryUI>().d_time = duration;
        }
    }

    public void RefreshBroadcastInfoWindow() {
        transform.Find("window_broadcast").GetComponent<UIWindowBroadcast>().RefreshOpen();
    }
    
    /// <summary>
    /// 更改主界面Title显示状态
    /// </summary>
    /// <param name="show"></param>
    public void ShowTitleRoot(bool show = true) {
        if (null == title_root) {
            return;
        }
        RefreshButtonBar();
        title_root.gameObject.SetActive(show);
    } 
    
    void FixTipText() {
        Tips.text = ScriptLocalization.Get("I_teaching_5");
    }
    void RefreshData(string oldAccountId,string newAccountId) {
        OnStartBegin();
        RefreshBtnImage();
        RefreshButtonBar();
    }

    private void OnDestroy() {
        if (UIManager.Inst) {
            if (UIManager.Inst.HasUIViewOpened<UITitle>()) {
                UIManager.Inst.CloseUIView(this, true, false);
            }
        }

        SimpleEventManager.RemoveListener<LocalConfigNotRelyLoginIsReadyEvent>(ShowAnnouncement);
        SimpleEventManager.RemoveListener<NetConfigNotRelyLoginIsReadyEvent>(ShowAnnouncement);
        if (null != MyDiscovery.Inst) {
            MyDiscovery.Inst.StopDiscovering();        
        }

        if (AssetBundleLoader.Inst) {
            AssetBundleLoader.Inst.UnLoadBundle(AssetBundleLoader.Title);
        }

        SeasonAchievement.ARAM.InitTracking();
        SimpleEventManager.Raise(ExitUITitleEvent.UseCache());
    }
}
