namespace RGScript.UI.Tutorial {
    public class TutorialActionWrapper {
        public enum ActionState {
            None,
            Enter,
            Exit
        }
        
        public readonly TutorialAction.TutorialAction TutorialAction;
        public ActionState State { get; private set; }

        public string ActionName => TutorialAction.actionName;
        public string SwitchKey => TutorialAction.switchKey;

        public TutorialActionWrapper(TutorialAction.TutorialAction tutorialAction) {
            TutorialAction = tutorialAction;
        }
        
        public void Enter() {
            State = ActionState.Enter;
            TutorialAction.Enter();
        }

        public void Exit() {
            State = ActionState.Exit;
            TutorialAction.Exit();
        }

        public void Reset() {
            State = ActionState.None;
        }
    }
}