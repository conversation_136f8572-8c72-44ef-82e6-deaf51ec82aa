using ChillyRoom.IM;
using DG.Tweening;
using RGScript.Data;
using RGScript.Util;
using System;
using System.Collections.Generic;
using UIFramework;
using UnityCommon.UI;
using UnityEngine;
using UnityEngine.UI;

namespace RGScript.UI {
    public class UIWindowMultiplayerRoomInfo : BaseUIView {
        public List<Sprite> playerIndexSprites;
        public List<Sprite> playerBackgrounds;
        public List<Sprite> modeSprites;
        public List<Sprite> rankSprites;
        private Transform _modeListTransform;
        private Transform _teamInfoListTransform;
        private Button _closeButton;
        private Image _mask;

        public static UIWindowMultiplayerRoomInfo ShowWindow() {
            var window = UIManager.Inst.OpenUIView<UIWindowMultiplayerRoomInfo>("window_multiplayer_room_info");
            return window;
        }

        public override void InitView() {
            base.InitView();

            manualControlActive = false;
            FindReferences();

            _closeButton.onClick.AddListener(() => {
                HideAnim(transform.Find("body").GetComponent<RectTransform>(), Close);
            });

            _mask.GetComponent<Button>().onClick.AddListener(() => {
                HideAnim(transform.Find("body").GetComponent<RectTransform>(), Close);
            });
            
            Refresh();

            ShowAnim(transform.Find("body").GetComponent<RectTransform>());
        }


        private void OnEnable() {
            SimpleEventManager.AddEventListener<PlayerEnterMultiRoomEvent>(RefreshTeamInfo);
            SimpleEventManager.AddEventListener<UpdateNetPlayerInfo>(RefreshTeamInfo);
            SimpleEventManager.AddEventListener<PlayerLeaveEvent>(RefreshTeamInfo);
        }

        private void OnDisable() {
            SimpleEventManager.RemoveListener<PlayerEnterMultiRoomEvent>(RefreshTeamInfo);
            SimpleEventManager.RemoveListener<UpdateNetPlayerInfo>(RefreshTeamInfo);
            SimpleEventManager.RemoveListener<PlayerLeaveEvent>(RefreshTeamInfo);
        }

        private void FindReferences() {
            _modeListTransform = transform.Find("body/mode/list");
            _teamInfoListTransform = transform.Find("body/team_info/list");
            _mask = transform.Find("mask").GetComponent<Image>();
            _closeButton = transform.Find("body/close_btn").GetComponent<Button>();
        }

        private void Refresh() {
            RefreshMode();
            RefreshTeamInfo();
        }

        private void RefreshMode() {
            for (int i = _modeListTransform.transform.childCount - 1; i > 0; i--) {
                DestroyImmediate(_modeListTransform.GetChild(i).gameObject);
            }

            var proto = _modeListTransform.GetChild(0).gameObject;
            var factors = new List<emBattleFactor>(BattleData.data.factors);

            if (BattleData.data.season != Season.None) {
                System.Enum.TryParse<emBattleFactor>(BattleData.data.season.ToString(), out var currentBattleFactor);
                var seasonModeItem = Instantiate(proto, _modeListTransform);
                seasonModeItem.gameObject.SetActive(true);
                SetModeImg(seasonModeItem.transform, ChallengeInfo.info.GetSprite(currentBattleFactor));
                factors.Remove(currentBattleFactor);
            } 
            else {
                var modeItem = Instantiate(proto, _modeListTransform);
                SetModeImg(modeItem.transform, BattleData.data.IsBossRushMode ? modeSprites[2] : modeSprites[0]);
                modeItem.gameObject.SetActive(true);
            }

            if (BattleData.data.isBadass) {
                var item = Instantiate(proto, _modeListTransform);
                SetModeImg(item.transform, modeSprites[1]);
                item.gameObject.SetActive(true);
            }

            if (GameUtil.IsRemoteGame()) {
                foreach (var factor in BattleData.data.weeklyMissionFactor) {
                    var item = Instantiate(proto, _modeListTransform);
                    SetModeImg(item.transform, ChallengeInfo.info.GetSprite(factor));
                    item.gameObject.SetActive(true);
                }
            }
            
            foreach (var factor in factors) {
                var item = Instantiate(proto, _modeListTransform);
                SetModeImg(item.transform, ChallengeInfo.info.GetSprite(factor));
                item.gameObject.SetActive(true);
            }
        }

        private const int MaxWidth = 60;

        private static void SetModeImg(Transform trans, Sprite factorSp) {
            var factorImg = trans.Find("img").GetComponent<Image>();
            factorImg.preserveAspect = false;
            factorImg.sprite = factorSp;

            float x = factorSp.rect.width / factorImg.pixelsPerUnit;
            float y = factorSp.rect.height / factorImg.pixelsPerUnit;
            factorImg.rectTransform.anchorMax = factorImg.rectTransform.anchorMin;
            factorImg.rectTransform.sizeDelta = new Vector2(x, y);
            if (x > MaxWidth || y > MaxWidth) {
                factorImg.preserveAspect = true;
                factorImg.rectTransform.sizeDelta = Vector2.one * MaxWidth; //限定不超过最大像素
                factorImg.SetAllDirty();
            } else {
                factorImg.preserveAspect = false;
                //不超过最大则使用原图显示
                factorImg.SetNativeSize();
            }
        }

        private void RefreshTeamInfo(EventBase e = null) {
            var localController = NetControllerManager.Inst.localController;
            var localPlayerIndex = NetControllerManager.Inst.GetControllerIndex(localController);

            var slotDict = new Dictionary<int, Transform>();

            var slotIndex = 0;
            var maxSlotCount = 3;
            var roomMaxPlayerCount = MultiGameManager.Inst.theRoomInfo.maxPlayerCount;
            for (int i = 0; i < maxSlotCount + 1; i++) {
                if (i == localPlayerIndex) {
                    continue;
                }

                var slot = _teamInfoListTransform.GetChild(slotIndex);

                slot.Find("hero/background").GetComponent<Image>().sprite = playerBackgrounds[i];
                slot.Find("hero/player_index").GetComponent<Image>().sprite = playerIndexSprites[i];

                slotDict.Add(i, slot);
                ResetSlot(slot, i >= roomMaxPlayerCount);
                slotIndex++;
            }

            foreach (var controller in NetControllerManager.Inst.controllers) {
                if (controller != localController) {
                    SetSlot(slotDict[NetControllerManager.Inst.GetControllerIndex(controller)], controller);
                }
            }
        }

        private void SetSlot(Transform slot, NetPlayerController netPlayerController) {
            var rank = netPlayerController.netPlayerInfo.GetRank();
            slot.Find("hero/rank").GetComponent<Image>().sprite = rankSprites[rank];
            slot.Find("hero/background/mark").gameObject.SetActive(false);
            
            var honoraryTitleList = slot.Find("honorary_title_list").GetComponent<UIHonoraryTitleList>();
            honoraryTitleList.gameObject.SetActive(true);
            honoraryTitleList.Refresh((int)netPlayerController.netId, netPlayerController.netPlayerInfo.honoraryTitleList);

            var accountId = netPlayerController.battleData.chillyUid;
            var imOpened = DataMgr.IMData.IsOpenForBetaTesting();
            var nicknameText = slot.Find("hero/nickname").GetComponent<Text>();
            nicknameText.gameObject.SetActive(imOpened && accountId > 0);
            if (nicknameText.gameObject.activeSelf) {
                nicknameText.text = $"UID{accountId}";
                FriendUtils.TryAssignNickname(accountId, nicknameText);
            }

            if (!netPlayerController.netPlayerInfo.enterMultiRoom) {
                return;
            }

            if (!netPlayerController.battleDataReady) {
                return;
            }
            
            var heroIndex = netPlayerController.battleData.playerIndex;
            if(heroIndex == -1) {
                return;
            }
            
            slot.Find("hero/background/hero_mask").gameObject.SetActive(true);
            
            var skinIndex = netPlayerController.battleData.skinIndex;
            var skillIndex = netPlayerController.battleData.skillIndex;
            slot.Find("hero/background/hero_mask/hero").GetComponent<Image>().sprite =
                RGSaveManager.Inst.GetHeroUISprite(heroIndex, skinIndex);

            slot.Find("hero/background/hero_name").GetComponent<Text>().text =
                NameUtil.GetHeroName((emHero)heroIndex);
            slot.Find("hero/background/skin_name").GetComponent<Text>().text =
                NameUtil.GetSkinName((emHero)heroIndex, skinIndex);

            var skillImage = slot.Find("skill/icon").GetComponent<Image>();
            
            skillImage.gameObject.SetActive(true);
            slot.Find("skill/mark").gameObject.SetActive(false);
            skillImage.sprite = RGSaveManager.Inst.GetSkillInfo((emHero)heroIndex, skillIndex).icon;
            slot.Find("skill/skill_name").GetComponent<Text>().text =
                NameUtil.GetSkillName((emHero)heroIndex, skillIndex);
        }

        private void ResetSlot(Transform slot, bool gray) {
            slot.Find("hero/rank").GetComponent<Image>().sprite = rankSprites[0];
            slot.Find("hero/background/mark").gameObject.SetActive(true);
            slot.Find("hero/background/hero_mask").gameObject.SetActive(false);
            slot.Find("skill/mark").gameObject.SetActive(true);
            slot.Find("skill/icon").gameObject.SetActive(false);
            slot.Find("hero/background/hero_name").GetComponent<Text>().text = "-";
            slot.Find("hero/background/skin_name").GetComponent<Text>().text = "-";
            slot.Find("skill/skill_name").GetComponent<Text>().text = "-";
            slot.Find("honorary_title_list").gameObject.SetActive(false);
            if (gray) {
                slot.Find("hero/background").GetComponent<Image>().sprite = playerBackgrounds[4];
                slot.Find("hero/player_index").GetComponent<Image>().sprite = rankSprites[0];//空白图
                slot.Find("skill/mark").GetComponent<Image>().color = Color.gray;
            } else {
                slot.Find("skill/mark").GetComponent<Image>().color = Color.white;
            }
        }

        public override void ShowView(params object[] objects) {
            base.ShowView(objects);
            ViewManager.PushView(this);
        }

        public override void HideView(params object[] objects) {
            base.HideView(objects);
            ViewManager.RemoveView(this);
        }

        private void Close() {
            UIManager.Inst.CloseUIView(this);
        }

        private void ShowAnim(Transform trans) {
            if (trans == null)
                return;
            trans.localPosition = new Vector3(0, -Screen.height, 0);
            trans.gameObject.SetActive(true);

            trans.DOLocalMoveY(0, 0.25f).SetUpdate(true);
            _mask.DOFade(0.5f, 0.25f);
            RGMusicManager.GetInstance().PlayEffect(8);
        }

        private void HideAnim(Transform trans, Action callback) {
            trans.localPosition = new Vector3(0, 0, 0);
            trans.DOLocalMoveY(-Screen.height, 0.25f).SetUpdate(true).onComplete += () => {
                trans.gameObject.SetActive(false);
                callback?.Invoke();
            };
            _mask.DOFade(0, 0.25f);
            RGMusicManager.GetInstance().PlayEffect(8);
        }
    }
}