using UnityEngine;
using UnityEngine.UI;

public class UIProgressBar : Mono<PERSON>eh<PERSON><PERSON> {
    [SerializeField] protected Slider slider;

    public static UIProgressBar Create(string resName, Transform parent) {
        var progressBar = Instantiate(
            ResourcesUtil.Load<GameObject>(resName), parent).GetComponent<UIProgressBar>();
        if (progressBar) {
            progressBar.name = resName;
        } else {
            Debug.LogError("no UIProgressBar named " + resName);
        }

        return progressBar;
    }

    public void Refresh(int current, int max) {
        if (slider) {
            slider.value = current * 1.0f / max;
        }
    }

    public void Refresh(float progress) {
        float finalProgress = Mathf.Clamp01(progress);
        if (slider) {
            slider.value = finalProgress;
        }
    }
}