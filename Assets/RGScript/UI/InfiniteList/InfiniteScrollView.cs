using EasingCore;
using FancyScrollView;
using RGScript.UI.Tape;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace RGScript.UI.InfiniteList {
    public class InfiniteScrollView<T> : FancyScrollView<T, Context> {
        [SerializeField]
        public Scroller scroller;
        
        [SerializeField]
        GameObject cellPrefab;

        Action<int> _onSelectionChanged;
        Action<float> _onValueChanged;

        protected override GameObject CellPrefab => cellPrefab;

        public int Index => Context.SelectedIndex;

        protected override void Initialize() {
            base.Initialize();

            Context.OnCellClicked = SelectCell;

            scroller.OnValueChanged(UpdatePosition);
            scroller.OnSelectionChanged(UpdateSelection);
        }

        void UpdateSelection(int index) {
            if (Context.SelectedIndex == index) {
                return;
            }

            Context.SelectedIndex = index;
            Refresh();

            _onSelectionChanged?.Invoke(index);
        }

        public void UpdateData(IList<T> items) {
            UpdateContents(items);
            scroller.SetTotalCount(items.Count);
        }

        protected override void UpdatePosition(float position) {
            base.UpdatePosition(position);
            _onValueChanged?.Invoke(position);
        }

        public void OnSelectionChanged(Action<int> callback) {
            _onSelectionChanged = callback;
        }

        public void OnValueChanged(Action<float> callback) {
            _onValueChanged = callback;
        }
        
        public void SelectNextCell() {
            var index = (Context.SelectedIndex + 1);
            if (loop) {
                index %= ItemsSource.Count;
            }
            SelectCell(index);
        }

        public void SelectPrevCell() {
            var i = (Context.SelectedIndex - 1) % ItemsSource.Count;
            if (loop && i < 0) {
                i += ItemsSource.Count;
            }
            SelectCell(i);
        }

        // ReSharper disable once MemberCanBePrivate.Global
        public void SelectCell(int i) {
            SelectCell(i, 0.35f);
        }
        
        // ReSharper disable once MemberCanBePrivate.Global
        public void SelectCell(int i, float duration) {
            if (i < 0 || i >= ItemsSource.Count || i == Context.SelectedIndex) {
                return;
            }

            UpdateSelection(i);
            scroller.ScrollTo(i, duration, Ease.OutCubic);
        }

        public void ResetSelectedIndex() {
            Context.SelectedIndex = -1;
        }

        public bool IsDragging => scroller.isDragging;

        public void SetLoop(bool isLoop) {
            this.loop = isLoop;
        }
    }
}