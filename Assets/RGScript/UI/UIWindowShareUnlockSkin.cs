using RGScript.Other.InputControl;
using RGScript.UI.ChooseHero;
using RGScript.UI.MVC;
using System;
using System.Collections;
using UIFramework;
using UnityEngine;
using UnityEngine.UI;
using Random = UnityEngine.Random;

public class UIWindowShareUnlockSkin : RGUIBaseUIView {
	public Sprite [] sprite_info_list;
	public Sprite sprite_info_en;
	public Sprite title_cn;
	public Sprite title_en;
	Image img_info;
	Image img_title;
	Text text_share_btn;
	Transform btn_share;
	int info_index = 0;
	public int e_index=0;
	public int unlock_char_index=0;
	public int unlock_skin_index=0;
	public string tip="分享到朋友圈获得皮肤";
	bool is_cn=true;

	public override void Awake () {
        base.Awake();
		anim = transform.GetComponent<Animator> ();
		img_info = transform.Find ("panel/info").GetComponent<Image> ();
		img_title = transform.Find ("panel/title").GetComponent<Image> ();
        btn_share = transform.Find("btn_share");
        text_share_btn = transform.Find("btn_share/Text").GetComponent<Text>();
        
        if (!GameUtil.IsChannelSupportShare()) {
            btn_share.gameObject.SetActive(false);
        }
    }

    public override void OnJoystickButtonDown(JoystickButton button) {
        if (!awake) return;
        base.OnJoystickButtonDown(button);

        if (InputControl.Inst.GetAnyUICancelButtonDown()) {
            BtnCloseClick();
        }
    }

	public void ShowWindow(){
		if (!awake) {
			if (Application.systemLanguage == SystemLanguage.Chinese ||
				Application.systemLanguage == SystemLanguage.ChineseSimplified ||
				Application.systemLanguage == SystemLanguage.ChineseTraditional)
			{
				is_cn = true;
				text_share_btn.text = tip;
			}
			else
			{
				is_cn = false;
			}

			GameManager.Inst.PauseGame();
			info_index = Random.Range (0, sprite_info_list.Length);
			if (is_cn) {
				img_info.sprite = sprite_info_list [info_index];
				img_title.sprite = title_cn;
				if (RGCompiledSetting.show_QRcode) {
					img_title.GetComponent<RectTransform> ().localPosition = new Vector3 (-30, -140, 0);
				}
			} else {
				img_info.sprite = sprite_info_en;
				img_title.sprite = title_en;
			}
			img_title.SetNativeSize ();
			img_info.SetNativeSize ();
			anim.SetBool ("show", true);
			awake = true;
		}
	}

    public void BtnCloseClick() {
        if (awake) {
            awake = false;
            UIManager.Inst.CloseUIViewDelay(this, 0.5f);
        }
    }

    public override void HideView(params object[] objects) {
        base.HideView(objects);
        GameManager.Inst.ContinueGame();
        anim.SetBool("show", false);
        RGMusicManager.GetInstance().PlayEffect(8);
    }

    public void BtnShareClick(){
		if (awake) {
			btn_share.gameObject.SetActive (false);
			StartCoroutine( CaptureCamera());
		}
	}
	IEnumerator CaptureCamera() {
        yield return new WaitForEndOfFrame ();
		int t_width = (int)(1.0f * 405 / 1280 * Screen.width);
		int t_height = (int)(1.0f  * Screen.height);
		int t_x = (Screen.width - t_width) / 2;
		int t_y = (Screen.height - t_height) / 2;
		Rect rect = new Rect (t_x, t_y, t_width, t_height);
		Texture2D screenShot = new Texture2D((int)rect.width, (int)rect.height, TextureFormat.RGB24,false);  
		screenShot.ReadPixels(rect, 0, 0);// 注：这个时候，它是从RenderTexture.active中读取像素
		// if (is_cn && RGCompiledSetting.show_QRcode) {
		// 	Texture2D texture_info = (Texture2D)ResourcesUtil.Load (
  //               $"RGTexture/unpackaged/share/e{e_index}.png");
		// 	int ox = (int)(290.0f * Screen.width / 1280);
		// 	int oy = (int)(36.0f * Screen.height / 720);
		// 	for (int i = 0; i < texture_info.width; i++) {
		// 		for (int p = 0; p < texture_info.height; p++) {
		// 			if (texture_info.GetPixel (i, p).a > 0)
		// 				screenShot.SetPixel (ox + i, oy + p, texture_info.GetPixel (i, p));
		// 		}
		// 	} 
		// }
		screenShot.Apply();
		//byte[] byt = screenShot.EncodeToPNG ();
		//File.WriteAllBytes (Application.dataPath + "/vvv.png", byt);
		NativeShare.Init ();
		NativeShare.ShareTexture (screenShot);
		if(info_index==0)
			UnLockSkin ();
		BtnCloseClick ();
	}

	void UnLockSkin() {
        if (RGSaveManager.Inst.char_list[unlock_char_index].skin_list[unlock_skin_index] == 1) {
            return;
        }

        RGSaveManager.Inst.HeroSkinUnlock(unlock_char_index, unlock_skin_index);
        if (UICanvas.GetInstance() != null) {
            UICanvas.ShowUIWindowObject(
                RGSaveManager.Inst.GetHeroUISprite (unlock_char_index, unlock_skin_index), "");
        }
        
        var chooseHeroController = UICanvas.GetInstance().GetController<ChooseHeroController>();
        chooseHeroController.DispatchMessage(new Message {
            Command = ChooseHeroCommand.IAPRestoreSkin,
            ExtraParams = ValueTuple.Create(emHero.Knight, 2)
        });
    }

}
