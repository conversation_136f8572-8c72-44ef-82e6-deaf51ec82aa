using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace RGScript.UI.Emoticon {
    public class EdgeEmoticonTips : MonoBehaviour, IPrefabPoolObject {
        public float delayDestroy = 5f;
        private Transform _destTransform;
        private Transform _bg;
        private Image _emoticonImg;
        private RectTransform _rectTransform;

        public Color[] playerColor;

        private Canvas canvas {
            get {
                return UICanvas.Inst.GetComponent<Canvas>();
            }
        }

        public int angleOffset = 90;

        private static readonly Dictionary<Transform, EdgeEmoticonTips> EmoticonTipsDic =
            new Dictionary<Transform, EdgeEmoticonTips>();

        private void Awake() {
            _rectTransform = GetComponent<RectTransform>();
            _bg = transform.Find("bg");
            _emoticonImg = transform.Find("bg/emoticon").GetComponent<Image>();
        }

        public static EdgeEmoticonTips ShowEdgeImageTips(Transform srcTransform, Sprite sp, float destroyTimeDelay = 2f) {
            GameObject edgeTipsGo = PrefabPool.Inst.Take(ResourcesUtil.Load<GameObject>("RGPrefab/UI/Emoticon/edge_emoticon_tips.prefab"));
            if (null == edgeTipsGo) {
                return null;
            }

            var edgeEmoticonTips = edgeTipsGo.GetComponent<EdgeEmoticonTips>();
            if (null == edgeEmoticonTips) {
                return null;
            }

            edgeEmoticonTips.transform.SetParent(UICanvas.Inst.transform);
            edgeEmoticonTips.delayDestroy = destroyTimeDelay;
            edgeEmoticonTips.Show(srcTransform, sp);
            return edgeEmoticonTips;
        }

        public static void ShowEdgeEmotionTips(RGBaseController ctrl, SpriteRenderer rgCtrlBodyRenderer,
            int emoticonId, int seed, float delayDestroy = 5f) {
            var destTransform = ctrl.transform;
            GameObject emoticonTipsGo;
            if (EmoticonTipsDic.ContainsKey(destTransform)) {
                emoticonTipsGo = EmoticonTipsDic[destTransform].gameObject;
            } else {
                emoticonTipsGo = PrefabPool.Inst.Take(ResourcesUtil.Load<GameObject>(
                    "RGPrefab/UI/Emoticon/edge_emoticon_tips.prefab"));
                EmoticonTipsDic.Add(destTransform, emoticonTipsGo.GetComponent<EdgeEmoticonTips>());
            }

            if (null == emoticonTipsGo) {
                return;
            }

            var edgeEmoticonTips = emoticonTipsGo.GetComponent<EdgeEmoticonTips>();
            if (null == edgeEmoticonTips) {
                return;
            }

            edgeEmoticonTips.transform.SetParent(UICanvas.Inst.transform);
            var sp = EmoticonInfo.GetEmoticonSpriteWithType(emoticonId, seed);
            var emoType = EmoticonInfo.GetEmoticonType(emoticonId);
            float showTime = delayDestroy;
            if (emoType == emEmotionType.Dice) {
                showTime += 2;
            }

            edgeEmoticonTips.delayDestroy = showTime;
            var playerColor = edgeEmoticonTips.GetPlayerColor(ctrl);
            edgeEmoticonTips.SetBgColor(playerColor);
            edgeEmoticonTips.Show(destTransform, sp);
        }
        
        public Color GetPlayerColor(RGBaseController controller) {
            int index = 0;
            var netCtrls = NetControllerManager.Inst.controllers;
            for (int i = 0; i < netCtrls.Length; i++) {
                if (controller == netCtrls[i].controller) {
                    index = i;
                }
            }

            return playerColor[index];
        }
        

        private void Show(Transform destTransform, Sprite sp) {
            OnTaken();
            if (delayDestroy > 0) {
                StartCoroutine(Destroying(delayDestroy));
            }

            this._destTransform = destTransform;
            // this.masterCtrl = masterCtrl;
            // this.masterRenderer = masterBodyRenderer;
            _emoticonImg.sprite = sp;
            _emoticonImg.SetNativeSize();
            UpdatePosition(false);
        }

        void UpdatePosition(bool lerp) {
            if (null == _destTransform) {
                return;
            }

            if (!canvas) {
                return;
            }

            float canvasW = canvas.GetComponent<RectTransform>().sizeDelta.x;
            float canvasH = canvas.GetComponent<RectTransform>().sizeDelta.y;

            Vector2 relativePos =
                (_destTransform.position - RGGameSceneManager.Inst.controller.transform.position).normalized;
            //计算角度
            var atan = Mathf.Atan2(relativePos.y, relativePos.x);
            var angle = Mathf.Rad2Deg * atan % 180;
            _rectTransform.eulerAngles = new Vector3(0, 0, angle + angleOffset);
            var sizeDelta = _rectTransform.sizeDelta;
            var margin = Mathf.Max(sizeDelta.x, sizeDelta.y);
            //计算位置
            float radio = canvasW / canvasH;
            relativePos = new Vector2(relativePos.x * radio, relativePos.y);
            float signX = Mathf.Sign(relativePos.x);
            float signY = Mathf.Sign(relativePos.y);
            if (Mathf.Abs(relativePos.x) > Mathf.Abs(relativePos.y)) {
                float deltaY = (1 - Mathf.Abs(relativePos.x)) * Mathf.Abs(relativePos.y) / Mathf.Abs(relativePos.x);
                relativePos = new Vector2((1 - margin / canvasW) * signX,
                    (Mathf.Abs(relativePos.y + signY * deltaY) < 1 - margin / canvasH)
                        ? relativePos.y + signY * deltaY
                        : (1 - margin / canvasH) * signY);
            } else {
                float deltaX = (1 - Mathf.Abs(relativePos.y)) * Mathf.Abs(relativePos.x) / Mathf.Abs(relativePos.y);
                relativePos = new Vector2(
                    (Mathf.Abs(relativePos.x + signX * deltaX) < 1 - margin / canvasW)
                        ? relativePos.x + signX * deltaX
                        : (1 - _rectTransform.sizeDelta.x / canvasW) * signX,
                    (1 - margin / canvasH) * signY);
            }
            
            Vector2 position = new Vector2(
                (relativePos.x * canvas.GetComponent<RectTransform>().sizeDelta.x) / 2,
                (relativePos.y * canvas.GetComponent<RectTransform>().sizeDelta.y) / 2);
            _rectTransform.anchoredPosition =
                lerp ? Vector2.Lerp(_rectTransform.anchoredPosition, position, 0.2f) : position;
            _emoticonImg.rectTransform.rotation = Quaternion.identity;
        }

        private void Update() {
            if (null != _destTransform) {
                bool needShow = !GameUtil.InMainCam(_destTransform.transform.position);
                if (_bg.gameObject.activeSelf != needShow) {
                    _bg.gameObject.SetActive(needShow);
                }

                UpdatePosition(true);
            } else {
                _bg.gameObject.SetActive(false);
            }
        }

        public void SetBgColor(Color color) {
            _bg.GetComponent<Image>().color = color;
        }


        public void OnTaken() {
            _emoticonImg.sprite = null;
            _destTransform = null;
            transform.position = Vector3.zero;
            StopCoroutine(Destroying(delayDestroy));
        }

        IEnumerator Destroying(float delayTime) {
            yield return new WaitForSeconds(delayTime);
            Store();
        }

        public void Store() {
            if(LogUtil.IsShowLog){LogUtil.Log("EdgeEmotionTips Destroying");}
            EmoticonTipsDic.Remove(this._destTransform);
            PrefabPool.Inst.Store(gameObject);
        }
        
    }
}