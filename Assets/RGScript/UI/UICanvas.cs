using Activities;
using cfg.task;
using ChillyRoom;
using System;
using System.Collections.Generic;
using CustomFactor;
using DG.Tweening;
using GG.Extensions;
using I2.Loc;
using ModeDefence;
using NextLevel;
using RGScript.Character;
using RGScript.Data;
using RGScript.UI;
using System.Linq;
using RGScript.UI.MVC;
using System.Text;
using Mirror;
using ModeSeason.ComboGun;
using ModeSeason.ComboGun.UI.Widgets;
using ModeSeason.ComboGun.UI.Windows;
using RGScript.Config.Manager;
using RGScript.Data.GameItemData;
using RGScript.Data.Mall;
using RGScript.Item.RoomItem;
using RGScript.Mall;
using RGScript.Map;
using RGScript.Team.TeamInfo;
using RGScript.UI.Buff;
using RGScript.UI.SeasonEquipments;
using RGScript.Util.EventSystem.SimpleEventSystem.UI;
using Tayx.Graphy.Utils.NumString;
using UIFramework;
using UnityCommon.UI;
using UnityEngine;
using UnityEngine.Animations;
using UnityEngine.Events;
using UnityEngine.SceneManagement;
using UnityEngine.U2D;
using UnityEngine.UI;
using Util.TAUtil;
using RGScript.Util.QuickCode;
using UI.MiniMap;
using NewDynamicConfig;
using RGScript.Other.InputControl;
using RGScript.UI.Forge;
using RGScript.UI.Task.Beginner;
using RGScript.WeaponAffix;
using System.Collections;
using TaskSystem;
using Team.TeamInfo;

#if UNITY_SWITCH
using nn.hid;
#endif

public class UICanvas : MonoBehaviour, EventInterface, IBriefInfoDisplayer, IInputPanel, IObjectInfoDisplayer {
    public RGJoyStick joy_stick;
    public RGJoyControl joy_control;
    public RectTransform emotion_btn_rect;
    [NonSerialized] public UIItemInfo ui_item_info;
    public IWindowPause ui_window_pause { get; set; }
    
    private UISettingBar _uiSettingBar;
    private UIMessageBar _messageBar;
    private UILevelBuffDock _uiLevelBuffDock;
    private UIReReadGuildInfo _uiReReadGuildInfo;
    private RectTransform _animGem;

    public static Sprite ButtonAtkImageNormal => SpriteMap.commonSprite.GetSprite("char_atk");
    public static Sprite ButtonEventImageNormal => SpriteMap.commonSprite.GetSprite("event_normal");

    private Image _imgHp;
    private Text _textHp;
    private Image _imgArmor;
    private Text _textArmor;
    private Image _imgEnergy;
    private Text _textEnergy;
    private Image _imgPassive;
    private Image _imgPassiveFrame;
    private Image[] _imgPassives;
    private Image _imgPassiveIcon;
    private Text _textPassive;
    private Transform _passiveBar;
    private Transform _stateBarBottom;
    private Transform _verticalBarLeft;
    private Text _textCoin;
    private Text _textTokenCoin;
    private Text _textSeasonStoreCoin;
    private GameObject _coin;
    public GameObject coinIconObj => _coin;
    private GameObject _tokenCoin;
    private Material weaponIconInitMaterial;
    Image weapon_icon;
    public WeaponIcon weapon_item_icon;
    Text weapon_consume;
    Text weapon_corner;
    Text _weaponPressureLv;
    //武器左下角的数字Text，暂时只有警官一技能使用
    private Text _weaponLeftBottomText;  
    Text text_skill_count;
    Text _textSkillEnergy;
    Image image_skill_count;
    Text text_gems;
    Text text_gems2;
    Transform btnSpecial;
    public Transform btnSkill { get; private set; }
    Transform stateBar;
    Transform infoBar;
    public Sprite[] netSps;
    private Text netInfoText;
    private Image netInfoImg;
    private Button _btnComboGunSeasonEntry;
    private Button _btnSeasonTask;
    private Button _btnSeasonGear;
    private Button _btnSeasonStore;
    private Transform _btnActivityReward;
    private Transform _tfComboGunBubble;
    
    private float _stateSliderWidth;
    private float _stateSliderHeight;
    private float _passiveCurrentValue;
    private float _passiveMaxValue;
    private bool _passiveIsPercent;
    private string _passiveBarStyle;
    private Sprite _passiveBarIcon;
    private Sprite _passiveBarSlider;
    
    [NonSerialized] public Transform canvas_world;
    [NonSerialized] public Transform canvas_world_damages;
    [NonSerialized] public Transform canvas_world_critics;
    [NonSerialized] Transform object_tap, object_taps;
    [NonSerialized] UIWeaponTap _weaponObjectTap;

    [NonSerialized]
    public Transform temp_ui;

    [NonSerialized]
    public Animator anim;

    [NonSerialized]
    public int control_mode = 0; // 控制模式，0 自由摇杆，1 固定摇杆

    [NonSerialized]
    public int axis_mode = 0; // 轴模式，0 不限制，1 

    private static UICanvas instance;

    [NonSerialized]
    public SwitchBettleUI switchBettleUI;
#if UNITY_SWITCH
    public bool useSwitchControl = true;
#else
    public bool useSwitchControl = false;
#endif

    Transform gemParent;

    public Color stateBarInvalidColor;

    public string stateBarInvalidText = "???/???";
    public string coinInvalidText = "???";

    public Action onUpdateGemText;
    public Action OnBtnSwitchFloorClick;

    private static Color32 skillIconDefaultColor = new Color32(73, 217, 255, 255);
    private Color32 skillIconColor = skillIconDefaultColor;

    private Button _reenterBtn;
    private Button _beginnerTaskBtn;
    private Button _mallBtn;
    private Button _switchFloorBtn;
    private Button _quickAccessBtn;
    private RGJoyBtnSkill _skillBtn;
    private UIShowCurrencyGroupWidget _currencyGroupWidget;
    private int _damageNumberStyle = 0;
    private GameObject _redpoint;
    private GameObject _factorProto;
    private Transform _actAndFactorGroup;
    private Transform _uiposterTR;
    

    void Awake() {
        anim = transform.GetComponent<Animator>();
        _animGem = GameObject.Find("/Canvas/text_gems").GetComponent<RectTransform>();
        //some ui script
        joy_stick = transform.Find("control/joystick/btn").GetComponent<RGJoyStick>();
        joy_control = transform.GetComponent<RGJoyControl>();
        emotion_btn_rect = transform.Find("control/btn_emoticon").GetComponent<RectTransform>();
        _uiSettingBar = transform.Find("setting_bar").GetComponent<UISettingBar>();
        ui_window_pause = RGGameProcess.Inst.modeProcess.GetWindowPause() ?? transform.Find("window_pause").GetComponent<IWindowPause>();
        _messageBar = transform.Find("message_bar").GetComponent<UIMessageBar>();
        ui_item_info = transform.Find("item_info").GetComponent<UIItemInfo>();
        _uiLevelBuffDock = transform.Find("level_buff_info").GetComponent<UILevelBuffDock>();
        _uiReReadGuildInfo = transform.Find("re_read_guild_info").GetComponent<UIReReadGuildInfo>();
        //info ui
        stateBar = transform.Find("state_bar");
        _imgHp = transform.Find("state_bar/hp_bar/img").GetComponent<Image>();
        _textHp = transform.Find("state_bar/hp_bar/Text").GetComponent<Text>();
        _imgArmor = transform.Find("state_bar/armor_bar/img").GetComponent<Image>();
        _textArmor = transform.Find("state_bar/armor_bar/Text").GetComponent<Text>();
        _imgEnergy = transform.Find("state_bar/energy_bar/img").GetComponent<Image>();
        _textEnergy = transform.Find("state_bar/energy_bar/Text").GetComponent<Text>();
        _imgPassive = transform.Find("state_bar/skill_bar/img").GetComponent<Image>();
        _imgPassiveFrame = transform.Find("state_bar/skill_bar/skill_frame").GetComponent<Image>();
        _imgPassiveIcon = transform.Find("state_bar/skill_bar/icon").GetComponent<Image>();
        _textPassive = transform.Find("state_bar/skill_bar/Text").GetComponent<Text>();
        _btnActivityReward = transform.Find("state_bar/vertical_bar_left/root/btn_activity");
        _stateSliderHeight = _imgHp.GetComponent<RectTransform>().rect.height;
        _stateSliderWidth = _imgHp.GetComponent<RectTransform>().rect.width;
        _passiveBar = stateBar.Find("skill_bar");
        _stateBarBottom = stateBar.Find("bottom");
        _verticalBarLeft = stateBar.Find("vertical_bar_left");
        _actAndFactorGroup = stateBar.Find("actAndFactorGroup");
        _stateBarBottom.GetComponent<RectTransform>().anchoredPosition = new Vector2(0, -57);
        _verticalBarLeft.GetComponent<RectTransform>().anchoredPosition = new Vector2(15, -80);
        
        _btnComboGunSeasonEntry = transform.Find("state_bar/btnGroup/btn_season").GetComponent<Button>();
        _btnComboGunSeasonEntry.onClick.AddListener(ShowSeasonEntry);
        _btnComboGunSeasonEntry.gameObject.SetActive(false);
        _tfComboGunBubble = _btnComboGunSeasonEntry.transform.Find("bubble");
        _tfComboGunBubble.gameObject.SetActive(false);
        
        _btnSeasonTask = transform.Find("btn_season_prize").GetComponent<Button>();
        _btnSeasonTask.onClick.AddListener(ShowSeasonPrize);
        _btnSeasonTask.transform.Find("Text").GetComponent<Text>().text = ScriptLocalization.Get("ui/btn_season_task", "赛季任务");
        _btnSeasonGear = transform.Find("btn_season_gear").GetComponent<Button>();
        _btnSeasonGear.onClick.AddListener(OnSeasonGearButtonClicked);
        _btnSeasonGear.transform.Find("Text").GetComponent<Text>().text = ScriptLocalization.Get("ui/btn_season_equip", "赛季装备");
        _btnSeasonTask.gameObject.SetActive(false);
        _btnSeasonGear.gameObject.SetActive(false);
        
        _uiposterTR = transform.Find("ui_poster_scrollview");
        transform.Find("state_bar/bottom/honorary_title_list").gameObject.SetActive(false);
        
        infoBar = transform.Find("info_bar");
        _currencyGroupWidget = infoBar.Find("show_currency_group_widget").GetComponent<UIShowCurrencyGroupWidget>();
        _coin = transform.Find("info_bar/coin").gameObject;
        _tokenCoin = transform.Find("info_bar/token_coin").gameObject;
        _textCoin = transform.Find("info_bar/coin/Text").GetComponent<Text>();
        var _coinBackground = transform.Find("info_bar/coin/Background").GetComponent<RectTransform>();
        _textTokenCoin = transform.Find("info_bar/token_coin/Text").GetComponent<Text>();
        _btnSeasonStore = transform.Find("info_bar/btn_season_shop").GetComponent<Button>();
        _textSeasonStoreCoin = transform.Find("info_bar/btn_season_shop/Text").GetComponent<Text>();

        // 被 _currencyGroupWidget 取代了
        _coin.SetActive(false);
        
        _btnSeasonStore.gameObject.SetActive(false);
        transform.Find("info_bar/fish").gameObject.SetActive(false);
        
        text_skill_count = transform.Find("control/btn_skill/skill_count/Text").GetComponent<Text>();
        _textSkillEnergy = transform.Find("control/btn_skill/skill_energy/Text").GetComponent<Text>();
        image_skill_count = transform.Find("control/btn_skill/skill_count").GetComponent<Image>();
        btnSpecial = transform.Find("control/btn_special");
        btnSkill = transform.Find("control/btn_skill");
        weapon_icon = transform.Find("control/btn_weapon/img").GetComponent<Image>();
        weaponIconInitMaterial = weapon_icon.material;
        weapon_item_icon = transform.Find("control/btn_weapon").GetComponent<WeaponIcon>();
        weapon_consume = transform.Find("control/btn_weapon/Text").GetComponent<Text>();
        weapon_corner = Instantiate(weapon_consume.gameObject, weapon_consume.transform.parent).GetComponent<Text>();
        _weaponPressureLv = transform.Find("control/btn_weapon/pressureLv").GetComponent<Text>();
        _weaponPressureLv.enabled = false;
        _weaponLeftBottomText = transform.Find("control/btn_weapon/weaponLeftBottomText").GetComponent<Text>();
        _weaponLeftBottomText.enabled = false;
        text_gems = transform.Find("text_gems/GemLayout/Text").GetComponent<Text>();
        text_gems2 = transform.Find("info_bar/gem/Text").GetComponent<Text>();
        _mallBtn = transform.Find("info_bar/vertical_bar/btn_mall").GetComponent<Button>();
        _switchFloorBtn = transform.Find("info_bar/vertical_bar/btn_switch").GetComponent<Button>();
        _reenterBtn = transform.Find("state_bar/vertical_bar_left/root/btn_reenter").GetComponent<Button>();
        var net_info_root = transform.Find("info_bar/net_info");
        net_info_root.gameObject.SetActive(false); // 转移到 UIShowCurrencyWithLatencyWidget 了
        int coinPosY = GameUtil.InMultiGame() ? -50 : -30;
        float currencyGroupAnchorPosY = GameUtil.InMultiGame() ? -41.8f : -49.7f;
        _currencyGroupWidget.Root.anchoredPosition = new Vector2(_currencyGroupWidget.Root.anchoredPosition.x, currencyGroupAnchorPosY);
        if (GameUtil.InGameScene && BattleData.data.IsARAM) {
            ((RectTransform)_currencyGroupWidget.transform).anchoredPosition += new Vector2(-15, -15);
        }
        
        // int coinBackgroundHeight = GameUtil.InMultiGame() ? 32 : 46;
        Vector2 coinPosition = _textCoin.transform.parent.localPosition;
        coinPosition.y = coinPosY;
        // _textCoin.transform.parent.localPosition = coinPosition;
        // _coinBackground.sizeDelta = new Vector2(_coinBackground.sizeDelta.x, coinBackgroundHeight);
        // coinPosition = _textTokenCoin.transform.parent.localPosition;
        // coinPosition.y = coinPosY;
        _textTokenCoin.transform.parent.localPosition = coinPosition;
        _textTokenCoin.transform.parent.gameObject.SetActive(BattleData.data.tokenType != emTokenCoin.None);

        weapon_corner.text = "";
        weapon_corner.name = "CornerText";
        RectTransform rt = weapon_corner.GetComponent<RectTransform>();
        rt.anchorMin = new Vector2(1, 0.5f);
        rt.anchorMax = new Vector2(1, 0.5f);
        rt.anchoredPosition = new Vector2(-8f, -15f);
        
        netInfoText = transform.Find("info_bar/net_info/Text").GetComponent<Text>();
        netInfoImg = transform.Find("info_bar/net_info/Image").GetComponent<Image>();

        canvas_world = GameObject.Find("/Canvas_world").transform;
        canvas_world_damages = new GameObject("damages").transform;
        canvas_world_damages.transform.SetParent(canvas_world);
        canvas_world_critics = new GameObject("critics").transform;
        canvas_world_critics.transform.SetParent(canvas_world);
        canvas_world_damages.SetAsLastSibling();
        canvas_world_critics.SetAsLastSibling();
        object_tap = GameObject.Find("/Canvas_world/object_tap").transform;
        object_taps = GameObject.Find("/Canvas_world/object_taps")?.transform;
        temp_ui = transform.Find("temp_ui").transform;
        _redpoint = transform.Find("info_bar/btn_pause/red_point").gameObject;
        _factorProto = ResourcesUtil.Load<GameObject>("RGPrefab/UI/Widget/factor_proto.prefab");

        var weaponObjectTapPrefab =
            ResourcesUtil.Load<GameObject>("RGPrefab/Other/scene_object/weapon_object_tap.prefab");
        _weaponObjectTap = Instantiate(weaponObjectTapPrefab, object_tap.parent).GetComponent<UIWeaponTap>();

        control_mode = PlayerSaveData.Inst.control_mode;
        axis_mode = PlayerSaveData.Inst.axis_mode;

        _damageNumberStyle = PlayerSaveData.GetInt(RGGameConst.GameSettingDamageNumberStyle, 0);
        
        if (SceneManager.GetActiveScene().name == "Scene_Tutorial") {
            var uiTeaching = UIManager.Inst.OpenUIView<UITeaching>("ui_teaching_scene");
            uiTeaching.current_teach = UITeaching.Teach.first_tutorial;
            uiTeaching.gameObject.name = "ui_teaching";
            uiTeaching.gameObject.SetActive(false);
            transform.Find("window_pause").SetAsLastSibling();
            if (GameUtil.InFirstTutorial()) {
                SetSkillButtonActive(false);
                ShowEmoticonBtn(false);
            }
        }

        HandleExtra();

        if (GetComponent<Canvas>().worldCamera == null) {
            GetComponent<Canvas>().worldCamera = Camera.main;
        }

        GameUtil.EnlargeCamera();
        
        // 这个逻辑用于防止玩家卡广视角
        if (GameUtil.InGameScene) {
            Invoke(nameof(GameUtil.EnlargeCamera), 1f);
        }

        var btnKTPlay = transform.Find("btn_ktplay");
        if (null != btnKTPlay) {
            btnKTPlay.gameObject.SetActive(false);
        }
#if UNITY_SWITCH
        InputManager.AddButtonTip(transform.Find("info_bar/btn_pause"), "+").SetBrownColor();

        //Gem
        var gemImage = transform.Find("info_bar/gem/Image").GetComponent<Image>();
        gemImage.sprite = SwitchResourceManager.Inst.gemSprite;
        gemImage.SetNativeSize();

        gemParent = transform.Find("text_gems/GemLayout");
#else
#endif
        emoticonPanel = Instantiate(ResourcesUtil.Load<GameObject>(
            "RGPrefab/UI/Emoticon/ui_emoticon.prefab"), transform);
        emoticonPanel.name = "ui_emoticon";

        if (GameUtil.IsRemoteGame()) {
            var btnTf = transform.Find("info_bar/btn_pause");
            if (null != btnTf) {
                var imgs = btnTf.GetComponentsInChildren<Image>();
                foreach (var img in imgs) {
                    img.color = Color.green;
                }
            }
        }
        
        if (GameConfigManager.GetInstance().Config.IsOpenLua) {
            var luaEnvironment = new GameObject("lua_environment_text").AddComponent<Text>();
            luaEnvironment.transform.SetParent(transform);
            
            luaEnvironment.gameObject.AddComponent<Outline>();
            luaEnvironment.text = "Lua Environment";
            luaEnvironment.font = ResourcesUtil.LoadFont("pixel_bold.TTF");
            luaEnvironment.color = Color.red;
            luaEnvironment.fontSize = 20;
            luaEnvironment.rectTransform.anchorMin = Vector2.zero;
            luaEnvironment.rectTransform.anchorMax = Vector2.zero;
            luaEnvironment.rectTransform.anchoredPosition = new Vector2(105, 0);
            luaEnvironment.rectTransform.sizeDelta = new Vector2(200, 100);
        }

        // ReSharper disable once InvertIf
        if (DataUtil.IsHeroRoom()) {
            var canvasScreenSpace = GameObject.Find("canvas_screen_space").transform;
            var siblingIndex = transform.Find("text_gems").GetSiblingIndex();
            
            // Create choose hero panel
            AssetBundleLoader.Inst.LoadAsync<GameObject>(
                "RGPrefab/Other/scene_object/ChooseHero/ui_choose_hero.prefab", o => {
                    var chooseHeroNode = Instantiate(o, canvasScreenSpace);
                    chooseHeroNode.transform.SetSiblingIndex(siblingIndex - 1);
                    chooseHeroNode.name = "ui_choose_hero";
                });

            // Create choose pet panel
            AssetBundleLoader.Inst.LoadAsync<GameObject>(
                "RGPrefab/Other/scene_object/ui_choose_pet.prefab", o => {
                    var chooseHeroNode = Instantiate(o, transform);
                    chooseHeroNode.transform.SetSiblingIndex(siblingIndex - 2);
                    chooseHeroNode.name = "ui_choose_pet";
                });
        }

        _skillBtn = transform.GetComponent<RGJoyControl>().GetButton<RGJoyBtnSkill>();

        BriefInfoDisplayUtil.displayer = this;
        InputPanelUtil.panel = this;
        ObjectInfoDisplayUtil.displayer = this;

        TryShowTalentTipInNewPlayerBattle();
    }

    private void TryShowTalentTipInNewPlayerBattle() {
        if (!GameUtil.IsNewPlayerBattle()) {
            return;
        }

        if (StatisticData.data.IsEventRecord(RGGameConst.NEW_PLAYER_LEVEL_TALENT_TIP_PAUSE_BTN)) {
            return;
        }

        if (BattleData.data.buffs.Count == 0) {
            return;
        }

        var btnPause = transform.Find("info_bar/btn_pause");
        var proto = ResourcesUtil.Load<GameObject>("RGPrefab/UI/simple_target_finger.prefab");
        var fingerGo = Instantiate(proto, transform);
        var root = fingerGo.transform.Find("parent");
        root.gameObject.name = "finger";

        var rect = btnPause.GetComponent<RectTransform>();
        root.transform.SetParent(rect);
        Vector3 pos = rect.transform.position;
        Vector2 referenceResolution = GameObject.Find("Canvas").GetComponent<CanvasScaler>().referenceResolution;
        float ratioX = Screen.width / referenceResolution.x;
        float ratioY = Screen.height / referenceResolution.y;
        pos.x -= rect.rect.width * (rect.pivot.x - 0.5f) * ratioX;
        pos.y -= rect.rect.height * (rect.pivot.y - 0.5f) * ratioY;
        root.position = pos;
        Destroy(fingerGo);

        StatisticData.data.RecordEvent(RGGameConst.NEW_PLAYER_LEVEL_TALENT_TIP_PAUSE_BTN, true);
    }

    private bool _skillButtonEnable = true;

    public bool SkillButtonEnable {
        get {
            return _skillButtonEnable;
        }
        set {
            _skillButtonEnable = value;
            transform.Find("control/btn_skill").gameObject.SetActive(_skillButtonEnable);
        }
    }

    public void SetSkillButtonActive(bool active) {
        if (SkillButtonEnable) {
            transform.Find("control/btn_skill").gameObject.SetActive(active);
        }
    }

    private void OnEnable() {
        AddEvent();
    }

    private void OnDisable() {
        RemoveEvent();
    }

    public void SwitchSeasonButtons(bool show, bool isEnterIronTide) {
        var isComboGun = BattleData.data.CompareFactor(emBattleFactor.ComboGun);
        var showComboGun = isComboGun && show && !GameUtil.IsMultiGame();
        _btnComboGunSeasonEntry.gameObject.SetActive(showComboGun);
        var hasSeeNewContent = UIComboGunUIEntryWindow.HasSeeNewContent(1);
        _tfComboGunBubble.gameObject.SetActive(!hasSeeNewContent);
        if (showComboGun) {
            RefreshComboGunEntryRedPoint();
        }

        if (BattleData.data.IsIronTide) {
            _btnSeasonTask.gameObject.SetActive(show);
            _btnSeasonGear.gameObject.SetActive(show);

            if (isEnterIronTide) {
                _btnSeasonTask.GetComponent<PositionConstraint>().translationOffset += Vector3.up * 100;
                _btnSeasonGear.GetComponent<PositionConstraint>().translationOffset += Vector3.up * 100;
                return;
            }

            if (!show) {
                return;
            }

            if (UIWindowSeasonPrizeIronTide.HasUnclaimedAchievementRewards()) {
                UIWindowSeasonPrizeIronTide.ShowWindow();
            } else if (UIWindowSeasonPrizeIronTide.HasUnclaimedDailyRewards()) {
                UIWindowSeasonPrizeIronTide.ShowWindow(true);
            }
        }
    }

    private void RefreshComboGunEntryRedPoint()
    {
        _btnComboGunSeasonEntry.transform.Find("redPoint_ui").gameObject.SetActive(UICBIconWidget.HasRedPoints());
    }

    private static T GetControllerInCanvas<T>(Transform canvas) where T : RGScript.UI.MVC.Controller {
        if (!canvas) {
            return null;
        }
        
        for (var i = 0; i < canvas.childCount; ++i) {
            var child = canvas.GetChild(i);
            var component = child.GetComponent<T>();
            if (component) {
                return component;
            }
        }
        
        return null;
    }

    public T GetController<T>() where T : RGScript.UI.MVC.Controller {
        var controller = GetControllerInCanvas<T>(transform);
        if (controller) {
            return controller;
        }
        
        var canvasScreenSpace = GameObject.Find("canvas_screen_space")?.transform;
        return GetControllerInCanvas<T>(canvasScreenSpace);
    }

    private static void EnableEventSystem() {
        InputControl.Inst.EnableEventSystem();
    }

    private static void DisableEventSystem() {
        InputControl.Inst.DisableEventSystem();
    }

    private void InitRedPoints() {
        var isSeasonMode = BattleData.data.IsSeasonMode && RGGameSceneManager.GetInstance().select_hero;
        var redPoint1 = _btnSeasonStore.transform.Find("redPoint").gameObject;
        var redPoint2 = _btnSeasonGear.transform.Find("redPoint_ui").gameObject;
        redPoint1.SetActive(false);
        redPoint2.SetActive(false);
        if (SeasonGuideData.GetSeasonGuildStep() == 1 && isSeasonMode && redPoint1 != null) {
            redPoint1.SetActive(true);
        }

        if (SeasonGuideData.IsChipSchemeSlotHasEmptySlot() && isSeasonMode && redPoint2 != null) {
            redPoint2.SetActive(true);
        }

        DataMgr.RedPointData.SetRedPointNodeCallback(RedPointConst.itgShop, node => {
            if (isSeasonMode && redPoint1 != null) {
                redPoint1.SetActive(node.pointNum > 0);
            }
        });
        DataMgr.RedPointData.SetRedPointNodeCallback(RedPointConst.itgEquipButton, node => {
            if (isSeasonMode && redPoint2 != null) {
                redPoint2.SetActive(node.pointNum > 0);
            }
        });
    }

    public void DisableAndReactivateEventSystem(float timeOfReactivation) {
        DisableEventSystem();
        Loom.QueueOnMainThread(EnableEventSystem, timeOfReactivation, true);
    }

    public void AddEvent() {
        SimpleEventManager.AddEventListener<UpdateFactorBarEvent>(UpdaterFactorModeBar);
        SimpleEventManager.AddEventListener<OnSetBattleFactor>(UpdaterFactorModeBar);
        SimpleEventManager.AddEventListener<PlayerLeaveEvent>(ShowMsgPlayerLeave);
        SimpleEventManager.AddEventListener<HostEnterMultiRoom>(ShowHostEnterMultiRoom);
        SimpleEventManager.AddEventListener<ShowPauseWindow>(OnShowPauseWindow);
        SimpleEventManager.AddEventListener<CoinChangeEvent>(OnCoinChange);
        SimpleEventManager.AddEventListener<ArmorChangedEvent>(OnUpdateArmorBar);
        SimpleEventManager.AddEventListener<EnableJoystickControllerEvent>(OnEnableJoystickController);
        SimpleEventManager.AddEventListener<SeasonGuideCallback>(OnSeasonGuideCallback);
        SimpleEventManager.AddEventListener<HideHeroChoseEvent>(OnHideHeroChoseCallback);
        SimpleEventManager.AddEventListener<SetSeasonEvent>(OnSetSeasonCallback);
        SimpleEventManager.AddEventListener<UpgradeWeaponPressureLvEvent>(OnUpdatePressureLv);
        SimpleEventManager.AddEventListener<UIBanFactorEvent>(OnShowEffectBanFactor);
        SimpleEventManager.AddEventListener<MountEvent>(OnMount);
        SimpleEventManager.AddEventListener<GameSettingChangeEvent>(OnGameSettingChange);
        SimpleEventManager.AddEventListener<MallUnlockEvent>(OnMallUnlockEvent);
        SimpleEventManager.AddEventListener<UpdateComboGunRedPointEvent>(OnUpdateComboGunRedPointEvent);
        SimpleEventManager.AddEventListener<UpdatePassiveSkillBar>(OnUpdatePassiveSkillBar);
        SimpleEventManager.AddEventListener<IMNewEvent>(OnIMNewEvent);
        SimpleEventManager.AddEventListener<ActivityCurrencyChanged>(OnActivityCurrencyChanged);
        SimpleEventManager.AddEventListener<CreateTargetPointEvent>(OnCreateTargetPoint);
        SimpleEventManager.AddEventListener<CareerTaskCompleteEvent>(OnCareerTaskCompleteEventHandler);
        SimpleEventManager.AddEventListener<WeaponEvolutionRefreshRedPointEvent>(OnWeaponEvolutionRefreshRedPointEvent);
        SimpleEventManager.AddEventListener<BeginnerTaskRedPointEvent>(OnBeginnerTaskRedPointHandler);
    }

    public void RemoveEvent() {
        SimpleEventManager.RemoveListener<UpdateFactorBarEvent>(UpdaterFactorModeBar);
        SimpleEventManager.RemoveListener<OnSetBattleFactor>(UpdaterFactorModeBar);
        SimpleEventManager.RemoveListener<PlayerLeaveEvent>(ShowMsgPlayerLeave);
        SimpleEventManager.RemoveListener<HostEnterMultiRoom>(ShowHostEnterMultiRoom);
        SimpleEventManager.RemoveListener<ShowPauseWindow>(OnShowPauseWindow);
        SimpleEventManager.RemoveListener<CoinChangeEvent>(OnCoinChange);
        SimpleEventManager.RemoveListener<ArmorChangedEvent>(OnUpdateArmorBar);
        SimpleEventManager.RemoveListener<EnableJoystickControllerEvent>(OnEnableJoystickController);
        SimpleEventManager.RemoveListener<SeasonGuideCallback>(OnSeasonGuideCallback);
        SimpleEventManager.RemoveListener<HideHeroChoseEvent>(OnHideHeroChoseCallback);
        SimpleEventManager.RemoveListener<SetSeasonEvent>(OnSetSeasonCallback);
        SimpleEventManager.RemoveListener<UpgradeWeaponPressureLvEvent>(OnUpdatePressureLv);
        SimpleEventManager.RemoveListener<UIBanFactorEvent>(OnShowEffectBanFactor);
        SimpleEventManager.RemoveListener<MountEvent>(OnMount);
        SimpleEventManager.RemoveListener<GameSettingChangeEvent>(OnGameSettingChange);
        SimpleEventManager.RemoveListener<MallUnlockEvent>(OnMallUnlockEvent);
        SimpleEventManager.RemoveListener<UpdateComboGunRedPointEvent>(OnUpdateComboGunRedPointEvent);
        SimpleEventManager.RemoveListener<UpdatePassiveSkillBar>(OnUpdatePassiveSkillBar);
        SimpleEventManager.RemoveListener<IMNewEvent>(OnIMNewEvent);
        SimpleEventManager.RemoveListener<ActivityCurrencyChanged>(OnActivityCurrencyChanged);
        SimpleEventManager.RemoveListener<CreateTargetPointEvent>(OnCreateTargetPoint);
        SimpleEventManager.RemoveListener<CareerTaskCompleteEvent>(OnCareerTaskCompleteEventHandler);
        SimpleEventManager.RemoveListener<WeaponEvolutionRefreshRedPointEvent>(OnWeaponEvolutionRefreshRedPointEvent);
        SimpleEventManager.RemoveListener<BeginnerTaskRedPointEvent>(OnBeginnerTaskRedPointHandler);

    }

    private void OnIMNewEvent(IMNewEvent e) {
        var showRedPoint = e.hasNewMessage && DataUtil.IsGameScene();
        _redpoint.SetActive(showRedPoint);
    }

    void Start() {
        if (RGGameProcess.Inst.this_index == 0 && DataUtil.IsHeroRoom()) {
            RefreshCurrencyGroup(true);
            _currencyGroupWidget.SetActive(ShowCurrencyType.Gem, true);
            _currencyGroupWidget.SetActive(ShowCurrencyType.FishChip, true);
            // transform.Find("info_bar/gem").gameObject.SetActive(true);
            // transform.Find("info_bar/fish").gameObject.SetActive(true);
            bool unlock = RoomItemUnlockThroughBattle.GetUnlock(emRoomUnlockItem.season_mode);
            // _btnSeasonStore.gameObject.SetActive(unlock);
            _currencyGroupWidget.SetActive(ShowCurrencyType.SeasonCoin, unlock);
            Invoke(nameof(RefreshReenterButton), 0.2f);
            RefreshMallButton();
            RefreshSwitchFloorButton();
            Invoke(nameof(RefreshBeginnerButton), 0.2f);
            _switchFloorBtn.gameObject.SetActive(true);
        } else {
            RefreshCurrencyGroup(false);
            RefreshBeginnerTaskWidget();
            if (DataUtil.IsGameScene() && BattleData.data.HasActivityOn()) {
                var (sprite, actName) = ActivityEntryData.GetCurrentActivityCoinSprite();
                if (sprite != null && !string.IsNullOrEmpty(actName)) {
                    ShowActivityCoin(true);
                }
            }
        }

        transform.GetComponent<RectMask2D>().enabled = true;
        UpdateBadassmode();
        UpdateGemText();
        _tokenCoin.SetActive(false);
        CreateQuickAccessButton();
#if UNITY_SWITCH
        var c = transform.Find("control");
        var ts = c.gameObject.GetComponentsInChildren<Transform>();
        foreach (var item in ts) {
            if (item != c) {
                item.transform.localScale = Vector3.zero;
            }
        }

        //switch control
        var switchControl = GameObject.Instantiate(SwitchResourceManager.Inst.PrefabSwitchBettleUI, transform.Find("control"));
        switchBettleUI = switchControl.GetComponent<SwitchBettleUI>();
#endif
    }

    #region QuickAccessButton
    
    private void CreateQuickAccessButton() {
        if (!GameUtil.InHeroRoom) {
            return;
        }

        var proto = ResourcesUtil.Load<GameObject>("RGPrefab/UI/btn_quick_access.prefab");
        _quickAccessBtn = Instantiate(proto, stateBar.Find("vertical_bar_left/root")).GetComponent<Button>();
        _quickAccessBtn.transform.SetSiblingIndex(1);
        _quickAccessBtn.transform.Find("redPoint_ui").gameObject.SetActive(false);
        _quickAccessBtn.transform.Find("bubble").gameObject.SetActive(false);
        _quickAccessBtn.name = proto.name;
        _quickAccessBtn.onClick.AddListener(OpenQuickAccessView);
        RefreshQuickAccessRedPoint();
        InvokeRepeating(nameof(CheckCareerTask), 0, 0.5f);
    }

    private void OpenQuickAccessView() {
        TaskManager.Instance.CheckTasks(TaskTypes.Career);
        UIWindowQuickAccess.ShowWindow(view => {
            if (StatisticData.data.GetEventCount(RGGameConst.CAREER_TASK_BUBBLE_TIP) <= 0) {
                return;
            }

            var canGetReward = TaskManager.Instance.IsAnyTaskCanGetReward(TaskTypes.Career);
            if (!canGetReward) {
                return;
            }

            // Create finger
            var fingerProto = ResourcesUtil.Load<GameObject>("RGPrefab/UI/simple_target_finger.prefab");
            var fingerGo = Instantiate(fingerProto, transform);
            var root = fingerGo.transform.Find("parent");
            root.gameObject.name = "finger";

            var target = view.transform.Find("body/item_list/viewport/content/task_board");
            var rect = target.GetComponent<RectTransform>();
            root.SetParent(rect);
            root.GetComponent<RectTransform>().anchoredPosition = Vector3.zero;
            Destroy(fingerGo);

            StatisticData.data.AddEventCount(RGGameConst.CAREER_TASK_BUBBLE_TIP, -1, true);
        }, _ => {
            RefreshQuickAccessRedPoint();
        });

        TAUtil.Track("click_quick_access_button");

        var btn = stateBar.Find("vertical_bar_left/root/btn_quick_access");
        if (!btn) {
            return;
        }

        var finger = btn.Find("finger");
        if (!finger) {
            return;
        }

        Destroy(finger.gameObject);
    }

    private void CheckCareerTask() {
        TaskManager.Instance.CheckTasks(TaskTypes.Career);
    }

    public void RefreshQuickAccessRedPoint() {
        var btn = stateBar.Find("vertical_bar_left/root/btn_quick_access");
        if (!btn) {
            return;
        }

        // new
        bool hasNew = UIWindowQuickAccess.DataList.Any(UIWindowQuickAccess.IsNew);

        // 任务板
        TaskManager.Instance.CheckTasks(TaskTypes.Career);
        var careerTaskRedPoint = TaskManager.Instance.IsAnyTaskCanGetReward(TaskTypes.Career);
        
        // 扭蛋机
        var eggMachineRedPoint = false;
        var itemEggMachine = FindObjectOfType<ItemEggMachine>();
        if (itemEggMachine) {
            eggMachineRedPoint = itemEggMachine.NeedShowRedPoint();
        }

        var showRedPoint = hasNew || careerTaskRedPoint || eggMachineRedPoint || UIForgeWindow.HasRedPoint(); // 锻造台的放在这里节省一些计算量
        btn.transform.Find("redPoint_ui").gameObject.SetActive(showRedPoint);

        RefreshCareerTaskBubble();
    }

    private void OnCareerTaskCompleteEventHandler(CareerTaskCompleteEvent e) {
        RefreshQuickAccessRedPoint();
    }

    private void OnWeaponEvolutionRefreshRedPointEvent(WeaponEvolutionRefreshRedPointEvent e) {
        RefreshQuickAccessRedPoint();
    }

    private void RefreshCareerTaskBubble() {
        var btn = stateBar.Find("vertical_bar_left/root/btn_quick_access");
        if (!btn) {
            return;
        }
        var bubble = btn.Find("bubble").gameObject;

        var targetTasksID = TaskManager.Instance.GetTasksID(TaskTypes.Career);
        // 气泡提示仅限第一章任务
        var canGetReward = targetTasksID.Any(taskID => taskID < 100 && TaskManager.Instance.GetTask(taskID).CanGetReward);

        if (!canGetReward) {
            bubble.SetActive(false);
            return;
        }

        if (bubble.activeSelf) {
            return;
        }

        if (StatisticData.data.GetEventCount(RGGameConst.CAREER_TASK_BUBBLE_TIP) <= 0) {
            return;
        }

        bubble.SetActive(true);
    }

    #endregion
    
    Tweener gemShakeTweener;

    void GemShake() {
#if UNITY_SWITCH
        if (gemShakeTweener != null) {
            gemShakeTweener.Complete();
        }
        gemShakeTweener = gemParent.DOShakePosition(0.25f, 10, 50, 90, false, false).SetUpdate(true);
        InputManager.Inst.CallVibration(InputManager.VibrationType.hurt);
#endif
    }

    private void RefreshReenterButton() {
        _reenterBtn.onClick.RemoveAllListeners();
        if (GameUtil.IsSingleGame()) {
            _reenterBtn.gameObject.SetActive(true);
            _reenterBtn.onClick.AddListener(ReEnterHall);
        } else {
            _reenterBtn.gameObject.SetActive(false);
        }
    }

    private void RefreshBeginnerButton() {
        if (GameUtil.IsSingleGame() && TaskManager.Instance.IsBeginnerTaskActive) {
            if (_beginnerTaskBtn == null) {
                _beginnerTaskBtn = GameObject
                    .Instantiate(
                        ResourcesUtil.Load<GameObject>("RGPrefab/UI/TaskUI/btn_beginner_task_hero_room.prefab"),
                        _verticalBarLeft.transform.Find("root"))
                    .GetComponent<Button>();
                _beginnerTaskBtn.transform.SetSiblingIndex(1);
            }
            _beginnerTaskBtn.onClick.RemoveAllListeners();
            _beginnerTaskBtn.gameObject.SetActive(true);
            _beginnerTaskBtn.onClick.AddListener(OpenBeginnerTask);
            RefreshBeginnerTaskRedPoint();
        } else {
            if(_beginnerTaskBtn)
                _beginnerTaskBtn.gameObject.SetActive(false);
        }
    }
    private void OpenBeginnerTask() {
        UIManager.Inst.OpenUIView<UIBeginnerTaskPanel>("window_beginner_task");
        _beginnerTaskBtn.GetComponentInChildren<UIBeginnerTaskWidget>().ShowTask();
    }
    
    private void OnBeginnerTaskRedPointHandler(BeginnerTaskRedPointEvent e) {
        RefreshBeginnerTaskRedPoint();
    }

    private void RefreshBeginnerTaskRedPoint() {
        if(_beginnerTaskBtn != null)
            _beginnerTaskBtn.transform.Find("red_point").gameObject.SetActive(TaskManager.Instance.IsHasBeginnerTaskReady());
    }
    
    private void RefreshCurrencyGroup(bool hasSelectHero) {
        UIShowCurrencyGroupDesc desc = UIShowCurrencyGroupDesc.GetHeroRoomDefault(hasSelectHero);
        _currencyGroupWidget.StartUp(desc);
    }

    public void RefreshCurrencyGroup(UIShowCurrencyGroupDesc desc) {
        _currencyGroupWidget.StartUp(desc);
    }

    private void RefreshBeginnerTaskWidget() {
        var canShow = TaskManager.Instance.IsBeginnerTaskActive && TaskManager.Instance.CanCreateInGameTask();
        var widget = _verticalBarLeft.transform.Find("beginner_task_widget");
        if (canShow) {
            if (widget == null) {
                widget = GameObject
                    .Instantiate(
                        ResourcesUtil.Load<GameObject>("RGPrefab/UI/TaskUI/beginner_task_widget.prefab"),
                        _verticalBarLeft).transform;
                widget.name = "beginner_task_widget";
            }
        }
        if (widget != null) {
            widget.gameObject.SetActive(canShow);
        }
    }
    
    private void RefreshMallButton() {
        // if (GameUtil.IsSingleGame()) {
        var enable = MallData.IsFeatureEnable;

        _mallBtn.gameObject.SetActive(enable);

        var position = GameUtil.IsSingleGame() ? new Vector3(0, -235, 0) : new Vector3(-0.2f, -359, 0);
        _mallBtn.GetComponent<RectTransform>().localPosition = position;
        // } else {
        // _mallBtn.gameObject.SetActive(false);
        // }
    }

    public void RefreshSwitchFloorButton() {
        _switchFloorBtn.onClick.RemoveAllListeners();
        _switchFloorBtn.transform.Find("bubble").gameObject.SetActive(false);
        _switchFloorBtn.onClick.AddListener(SwitchFloor);
        var ctrl = RGGameSceneManager.Inst.controller;
        if(!ctrl) {
            return;
        }

        bool inSecondHall = RoomObjectManager.IsPositionInSecondHall(ctrl.transform.position);
        _switchFloorBtn.transform.Find("up").gameObject.SetActive(!inSecondHall);
        _switchFloorBtn.transform.Find("down").gameObject.SetActive(inSecondHall);
    }

    private Tween _secondHallMaskTween;
    private bool SecondHallIsShowing { get; set; }
    public void RefreshSecondHallMask(bool showSecondHall) {
        if (showSecondHall == SecondHallIsShowing) {
            return;
        }

        const float fadeTime = 0.75f;
        var mask = RoomObjectManager.Inst.transform.Find(RoomObjectManager.SecondHallMaskPath).GetComponent<SpriteRenderer>();
        _secondHallMaskTween?.Kill();
        if (showSecondHall) {
            SecondHallIsShowing = true;
            mask.color = Color.black;
            _secondHallMaskTween = mask.DOFade(0, fadeTime);
        } else {
            SecondHallIsShowing = false;
            mask.color = mask.color.Alpha(0);
            _secondHallMaskTween = mask.DOFade(1, fadeTime);
        }
    }

    public Transform GetActivityTf() {
        return _btnActivityReward;
    }
    #region CG Mode
    
    private Button _cgInventoryBtn;
    public void RefreshInventoryButton() {
        _cgInventoryBtn ??=
            Instantiate(ResourcesUtil.Load<GameObject>("ModeSeason/ComboGun/Prefabs/UI/btn_inventory.prefab"),
                transform.Find("control")).GetComponent<Button>();
        _cgInventoryBtn.name = "btn_inventory";
        _cgInventoryBtn.GetComponent<Image>().alphaHitTestMinimumThreshold = .01f;
        _cgInventoryBtn.onClick.RemoveAllListeners();
        _cgInventoryBtn.onClick.AddListener(delegate {
            UIManager.Inst.OpenUIView<UIInventory>(
                ResourcesUtil.Load<GameObject>("ModeSeason/ComboGun/Prefabs/UI/ui_inventory.prefab"));
            RGMusicManager.GetInstance().PlayEffect(8);
        });
        // _cgInventoryBtn.RectTransform().anchoredPosition = SettingData.data.inventoryPosition;
    }
    
    #endregion
    
    private string[] _ignoreUI = new[] {"window_shop", "UIArtifactUpgrade", "UIArtifactsUpgrade", "window_shop_limit"};
    void OnApplicationPause(bool pauseStatus) {
        var findIgnoreUI = false; 
        foreach (var ignoreUI in _ignoreUI) {
            findIgnoreUI = transform.Find(ignoreUI);
            if (findIgnoreUI) {
                break;
            }
        }
        //todo 回到游戏特殊处理了 宝石界面 新手礼包界面 神器升级界面
        if (!pauseStatus && anim.GetBool("show") && !findIgnoreUI) {
            ShowWindowPause();    
        }
        
    }

    public static UICanvas Inst {
        get {
            return GetInstance();
        }
    }

    public static UICanvas GetInstance() {
        if (instance == null) {
            instance = (UICanvas)FindObjectOfType(typeof(UICanvas));
        }

        return instance;
    }

    public void SetUpJoyController() {
        anim.SetBool("show", true);
        joy_stick.SwitchControl(control_mode);
        joy_stick.SwitchAxis(axis_mode);
        joy_stick.SetInputCallback((input)=>{
            if(RGGameSceneManager.Inst != null && RGGameSceneManager.Inst.controller != null){
                RGGameSceneManager.Inst.controller.RoleMove(input);
            }
        });
        joy_control.SetupControl();
        if (GameUtil.IsDefenceMode() && ui_defence) ui_defence.animator.SetBool("show", true);
    }

    public void UnUseJoyController() {
        anim.SetBool("show", false);
        joy_stick.UnsetCallback();
        joy_control.UnUseController();
    }

    public void SetUpNetJoyController() {
        anim.SetBool("show", true);
        joy_stick.SwitchControl(control_mode);
        joy_stick.SwitchAxis(axis_mode);
    }

    /// <summary>
    /// 暂停输入
    /// </summary>
    public void PauseInput() {
        anim.SetBool("show", false);
        joy_stick.PauseInput();
        joy_control.PauseInput();
    }

    public void ResetInput() {
        joy_stick.ResetInput();
    }

    /// <summary>
    /// 恢复输入
    /// </summary>
    public void ContinueInput() {
        anim.SetBool("show", true);
        joy_stick.ContinueInput();
        joy_control.ContinueInput();
    }

    private GameObject _btnUltimate;
    public void ShowBtnUltimate(bool show) {
        _btnUltimate ??= transform.Find("control/btn_special/img_ultimate_skill").gameObject;
        _btnUltimate.SetActive(show);
    }

    private Dictionary<string, bool> btnSpecialMap = new Dictionary<string, bool>();

    public void ShowBtnSpecial(int mode, bool isFusion = false, string btnSpecialId = "default") {
        var isShow = mode >= 0;
        if (btnSpecialId == null) return;
        if (!isFusion) {
            btnSpecialMap[btnSpecialId] = isShow;
            isShow = btnSpecialMap.Aggregate(isShow, (current, pair) => current | pair.Value);
        }

        btnSpecial.gameObject.SetActive(isShow);
        btnSpecial.transform.Find("img_special").GetComponent<UISpecialButton>().InitSpecialMode(mode);
        
        GameObject special = btnSpecial.Find("img_special").gameObject;
        GameObject fusion = btnSpecial.Find("img_fusion").gameObject;
        var type = RGJoyControl.SpecialBtnType;
        if (isShow) {
            type = RGJoyControl.SpecialBtnType = isFusion ? emSpecialBtnType.Fusion : emSpecialBtnType.Special;
            special.SetActive(!isFusion);
            fusion.SetActive(isFusion);
        } else {
            RGJoyControl.SpecialBtnType = emSpecialBtnType.None;
            fusion.SetActive(false);
            special.SetActive(false);
        }

        SimpleEventManager.Raise(ShowSpecialButtonEvent.UseCache(isShow, type));
    }

    public void ShowUISettingBar(bool show) {
        _uiSettingBar.ShowBar(show);
    }

    public int SwitchControlMode() {
        if (control_mode == 0) {
            control_mode = 1;
        } else if (control_mode == 1) {
            control_mode = 2;
        } else if (control_mode == 2) {
            control_mode = 0;
        }

        joy_stick.SwitchControl(control_mode);
        PlayerSaveData.Inst.control_mode = control_mode;
        return control_mode;
    }

    public int SetControlMode(int mode) {
        control_mode = mode;
        joy_stick.SwitchControl(control_mode);
        PlayerSaveData.Inst.control_mode = control_mode;
        return control_mode;
    }
    
    public int SwitchAxisMode() {
        if (axis_mode == 0) {
            axis_mode = 1;
        } else {
            axis_mode = 0;
        }

        joy_stick.SwitchAxis(axis_mode);
        PlayerSaveData.Inst.axis_mode = axis_mode;
        return axis_mode;
    }
    
    public int SetAxisMode(int value) {
        axis_mode = value;
        joy_stick.SwitchAxis(axis_mode);
        PlayerSaveData.Inst.axis_mode = axis_mode;
        return axis_mode;
    }

    public void SetDamageNumberStyles(int style) {
        _damageNumberStyle = style;
    }
    
    public void ShowWindowPause() {
        if(ui_window_pause != null){
            ui_window_pause.ShowWindow();
        }
    }

    public void HideWindowPause() {
        if(ui_window_pause != null){
            ui_window_pause.Resume();
        }
    }

    public bool IsWindowPauseAwake() {
        return ui_window_pause != null && ui_window_pause.IsAwake();
    }

    public void HideBtnRec() { }

    public void SetWindowPausePlayer() {
        ui_window_pause.UpdateInfo();
    }

    public void ShowGem() {
        _animGem.anchoredPosition = new Vector2(-100, 80);
        DOTween.To(
            () => _animGem.anchoredPosition,
            anchoredPosition => _animGem.anchoredPosition = anchoredPosition,
            new Vector2(-100, -85),
            0.25f).SetUpdate(true);
        
        _btnSeasonGear.gameObject.SetActive(false);
        _btnSeasonTask.gameObject.SetActive(false);
    }

    public void ShowGemInView(float y) {
        _animGem.anchoredPosition = new Vector2(-100, 80);
        DOTween.To(
            () => _animGem.anchoredPosition,
            anchoredPosition => _animGem.anchoredPosition = anchoredPosition,
            new Vector2(-100, y),
            0.25f);
        _btnSeasonGear.gameObject.SetActive(false);
        _btnSeasonTask.gameObject.SetActive(false);
    }

    private void ShowSeasonPrize() {
        UIWindowSeasonPrizeIronTide.ShowWindow(true);
    }

    private void ShowSeasonEntry() {
        UIManager.Inst.OpenUIView<UIComboGunUIEntryWindow>(
            "ModeSeason/ComboGun/Prefabs/UI/window/window_cb_ui_entry.prefab");
        var hasSeeNewContent = UIComboGunUIEntryWindow.HasSeeNewContent(1);
        _tfComboGunBubble.gameObject.SetActive(!hasSeeNewContent);
    }

    private void OnClickBattlePass() {
        var ui = UIManager.Inst.OpenUIView<TaskWindowView>("TaskUI/window_task_new");
        ui.defaultTabName = "Activities";
        RGMusicManager.GetInstance().PlayEffect(10);
    }

    private static void OnSeasonGearButtonClicked() {
        var heroId = RGGameSceneManager.Inst.controller.GetHeroType();
        var isSuperHero = CharactersLevelUpConfigManager.IsHeroChar(heroId.ToInt());
        if (isSuperHero) {
            GetInstance().ShowTempMessage("tips/super_hero_cannot_use_this_function", 3.0f);
            return;
        }
        
        SeasonEquipmentsController.CreateAndShow();
    }
    
    public void HideGem() {
        DOTween.To(
            () => _animGem.anchoredPosition,
            anchoredPosition => _animGem.anchoredPosition = anchoredPosition,
            new Vector2(-100, 80),
            0.25f).SetUpdate(true);
        if (BattleData.data.IsIronTide && RGGameSceneManager.GetInstance().select_hero) {
            _btnSeasonGear.gameObject.SetActive(true);
            _btnSeasonTask.gameObject.SetActive(true);
        }
    }
    
    /// <summary>
    /// 不可点击的蓝币面板
    /// </summary>
    public void ShowGemCoin(bool show) {
        _currencyGroupWidget.SetActive(ShowCurrencyType.Gem, show);
    }

    public void ShowVoidCoin(bool show) {
        _currencyGroupWidget.SetActive(ShowCurrencyType.VoidCoin, show);
    }
    
    public void ShowActivityCoin(bool show) {
        _currencyGroupWidget.SetActive(ShowCurrencyType.ActivityCoin, show);
    }

    public void UpdateGemText() {
        var gem = "" + RGSaveManager.Inst.GetGem();
        text_gems.text = gem;
        // text_gems2.text = gem;
        _currencyGroupWidget.SetText(ShowCurrencyType.Gem, gem);
        onUpdateGemText?.Invoke();
    }

    public void ShowSeasonCoinReplaceGemText() {
        var gemLayout = text_gems.transform.parent;
        var gemImage = gemLayout.Find("gem_image").GetComponent<Image>();
        var seasonCoinImage = gemLayout.Find("season_coin_image").GetComponent<Image>();
        if (seasonCoinImage.gameObject.activeSelf) {
            return;
        }
        
        gemImage.gameObject.SetActive(false);
        seasonCoinImage.gameObject.SetActive(true);

        text_gems.text = SeasonDataUtil.coin.ToString();
    }

    public void RevertSeasonCoinToGem() {
        var gemLayout = text_gems.transform.parent;
        var gemImage = gemLayout.Find("gem_image").GetComponent<Image>();
        var seasonCoinImage = gemLayout.Find("season_coin_image").GetComponent<Image>();
        if (!seasonCoinImage.gameObject.activeSelf) {
            return;
        }
        
        gemImage.gameObject.SetActive(true);
        seasonCoinImage.gameObject.SetActive(false);
        
        text_gems.text = RGSaveManager.Inst.GetGem().ToString();
    }

    public void RefreshSeasonCoinText() {
        var gemLayout = text_gems.transform.parent;
        var seasonCoinImage = gemLayout.Find("season_coin_image").GetComponent<Image>();
        if (!seasonCoinImage.gameObject.activeSelf) {
            return;
        }
        
        text_gems.text = SeasonDataUtil.coin.ToString();
    }

    public void UpdatePauseInfo() {
        if(ui_window_pause != null){
            ui_window_pause.UpdateInfo();
        }
    }

    public void ShowWarn(int index, bool value) {
        if (index == 0) {
            transform.Find("state_bar/hp_bar/warn").gameObject.SetActive(value);
        } else if (index == 1) {
            transform.Find("state_bar/armor_bar/warn").gameObject.SetActive(value);
        } else if (index == 2) {
            transform.Find("state_bar/energy_bar/warn").gameObject.SetActive(value);
        }
    }

    private Sequence _atkBtnSwitchProcess; // 攻击按钮切换过程

    public void SwitchAtkBtn(bool is_atk, Sprite customSprite = null) {
        if (_atkBtnSwitchProcess != null)
            _atkBtnSwitchProcess.Kill(false);
        _atkBtnSwitchProcess = DOTween.Sequence();
        _atkBtnSwitchProcess.SetUpdate(true);
        var btnAtkImg = transform.Find("control/btn_atk/Image");
        if (btnAtkImg) {
            _atkBtnSwitchProcess.Append(btnAtkImg.DOScale(0, 0.1f));
            _atkBtnSwitchProcess.AppendCallback(() => {
                btnAtkImg.GetComponent<Image>().sprite = customSprite != null ? customSprite : (is_atk ? ButtonAtkImageNormal : ButtonEventImageNormal);
            });
            _atkBtnSwitchProcess.Append(btnAtkImg.DOScale(1, 0.1f));
        }
#if UNITY_SWITCH
        switchBettleUI.SwitchAtkBtn(is_atk);
#endif
    }

    private void ShowMsgPlayerLeave(EventBase e) {
        var eParam = e as PlayerLeaveEvent;
        var netCtrl = NetControllerManager.Inst.GetNetCtrlByNetId((uint)eParam.netId);
        bool needShow = true;
        needShow = needShow & (null == netCtrl);
        if (null != netCtrl) {
            needShow = needShow & !netCtrl.isLocalPlayer;
        }

        if (needShow) {
            ShowTempMessage("multi/player_leave", 2f);
        }
    }

    void ShowHostEnterMultiRoom(EventBase e) {
        if (DataUtil.IsHeroRoom() && BattleData.data.SelectedHero()) {
            ShowTempMessage("multi/host_in_mulroom", 2f);
        }
    }

    private void UpdaterFactorModeBar(EventBase e) {
        UpdateBadassmode();
    }

    public void UpdateSeasonStoreButton() {
        bool unlock = RoomItemUnlockThroughBattle.GetUnlock(emRoomUnlockItem.season_mode);
        ShowSeasonStoreButton(unlock, false);
    }

    public void ShowSeasonStoreButton(bool show, bool isEnterIronTide) {
        // _btnSeasonStore.gameObject.SetActive(show);
        _currencyGroupWidget.SetActive(ShowCurrencyType.SeasonCoin, show);
        if (isEnterIronTide) {
            // _btnSeasonStore.GetComponent<RectTransform>().anchoredPosition = new Vector2(-150, -30);
            _currencyGroupWidget.SetActive(ShowCurrencyType.Coin, false);
        }
    }

    public void UpdateBadassmode() {
        Transform factors_container = _actAndFactorGroup.Find("battle_factors");
        GameUtil.RemoveAllChildren(factors_container);
        
        bool isBossRush = BattleData.data.IsBossRushMode || RGGameProcess.Inst.use_br_ticket_flag;
        if (isBossRush) {
            var item = Instantiate(_factorProto, factors_container);
            item.transform.SetAsFirstSibling();
            item.GetComponent<Image>().sprite = SpriteUtility.GetBossRushSprite(BossRushGameModeProcess.IsPureMode());
            item.gameObject.SetActive(true);
        }
        
        if (BattleData.data.isBadass) {
            var item = Instantiate(_factorProto, factors_container);
            item.transform.SetAsFirstSibling();
            item.GetComponent<Image>().sprite = SpriteUtility.GetBadassSprite();
            item.gameObject.SetActive(true);
        }
        
        if (StaticCustomFactorManager.HasFactor) {
            AddCustomFactor(factors_container);
        }

        // 已开启的活动
        var activityBtnPath = DataMgr.ActivityEntryData.GetActivedActivityBtnPath();
        var actRoot = _actAndFactorGroup.Find("activity_btn");
        var showActBtn = !string.IsNullOrEmpty(activityBtnPath);
        if (showActBtn) {
            var btnName = "in_game_act_btn";
            var btn = actRoot.Find(btnName);
            if (btn != null) {
                Destroy(btn.gameObject);
            }
            var actBtnProto = ResourcesUtil.Load<GameObject>(activityBtnPath);
            var obj = Instantiate(actBtnProto, actRoot);
            obj.name = btnName;
        }
        actRoot.gameObject.SetActive(showActBtn);

        if (GameUtil.IsNormalModeWithDifficulty()) {
            var btnProto = ResourcesUtil.Load<GameObject>("Level/difficulty/LevelObject/Other/ui/btn_weapon_affix.prefab");
            var obj = Instantiate(btnProto, actRoot);
            obj.name = btnProto.name;
            actRoot.gameObject.SetActive(true);
        }

        // 挑战因子
        UpdateBattleFactorIcon(factors_container);
    }

    private void AddCustomFactor(Transform parent) {
        var iconTransform = parent.Find("custom_factor");
        var sprite = StaticCustomFactorManager.GetCurrentSprite();
        if (iconTransform == null) {
            var item = Instantiate(_factorProto, parent);
            iconTransform = item.transform;
        }
        iconTransform.GetComponent<Image>().sprite = sprite;
        iconTransform.name = "custom_factor";
        iconTransform.gameObject.SetActive(true);
    }

    private void UpdateBattleFactorIcon(Transform factors_container) {
        if (BattleData.data.BattleFactorCount() > 0 || BattleData.data.WeeklyMissionFactorCount() > 0) {
            var factors = DataUtil.GetBattleFactors();
            var existsFactors = new HashSet<emBattleFactor>();
            foreach (Transform child in factors_container) {
                if (child.name.StartsWith("bf_") && Enum.TryParse<emBattleFactor>(child.name.Substring(3), out var factor)) {
                    existsFactors.Add(factor);
                }
            }
            foreach (var currentBattleFactor in factors) {
                if (existsFactors.Contains(currentBattleFactor)) {
                    continue;
                }
                var item = Instantiate(_factorProto, factors_container);
                item.name = "bf_" + currentBattleFactor;
                item.GetComponent<Image>().sprite = StaticCustomFactorManager.GetCurrentSprite();
                item.gameObject.SetActive(true);
                var img = item.GetComponent<Image>();
                if (currentBattleFactor == emBattleFactor.ReturnPlayer) {
                    img.sprite = DataMgr.ReturnPlayerData.GetReturnPlayerFactorIcon();
                } else if (currentBattleFactor == emBattleFactor.ReturnPlayerH5) {
                    img.sprite = DataMgr.ReturnPlayerH5Data.GetReturnPlayerFactorIcon();
                } else {
                    img.sprite = ChallengeInfo.info.GetSprite(currentBattleFactor);
                }
                if (BattleFactorOmit.IsFactorValid(factors, currentBattleFactor) && !BattleData.data.omitFactors) {
                    img.color = Color.white;
                } else {
                    img.color = new Color(0.25f, 0.25f, 0.25f, 1); // 无效因子 变暗
                }

                var brSprite = item.transform.Find("bottomRightSprite").GetComponent<Image>();
                if (BattleData.data.IsARAM && (currentBattleFactor == emBattleFactor.ARAM || currentBattleFactor == emBattleFactor.ARAM2)) {
                    brSprite.sprite = CommonAssets.Assets.diffSprites[ARAMModeData.Data.GetDifficultyLevel()];
                    brSprite.gameObject.SetActive(true);
                } else {
                    brSprite.gameObject.SetActive(false);
                }

                if (DataUtil.GetCurSceneName() == RGGameConst.SCENE_HERO_ROOM) {
                    var seasonConfig = ConfigManager.GetCurrectUseConfig<SeasonConfig>();
                    if (seasonConfig != null && currentBattleFactor.ToString() == seasonConfig.Season.ToString()) {
                        var iconClickCallback = new FuncObj(typeof(SeasonPrizeExhibition),
                            nameof(SeasonPrizeExhibition.ShowSeasonPrizeWindow), seasonConfig.Season, true);
                        UIPointerEventHandle.SetClickCallback(img.transform, iconClickCallback);
                        UIRedPointControl.AddRedPointCtrl("Has" + seasonConfig.Season.ToString() + "Reward",
                            img.transform as RectTransform, 24, new Vector2(25.5f, 17f));
                    }
                }
            }
        }
    }

    public void UpdateHpBar() {
        if (null == RGGameSceneManager.GetInstance() || null == RGGameSceneManager.GetInstance().controller) {
            return;
        }

        if (UiUtil.CanShowFunctionalUI) {
            UpdateHpBarValid();
        } else {
            UpdateInvalidStateBar(_imgHp, _textHp);
        }
    }

    private void UpdateHpBarValid() {
        var attr = RGGameSceneManager.GetInstance().controller.attribute;
        int value1 = attr.hp;
        int value2 = attr.max_hp;
        float percentage = value2 > 0 ? Mathf.Min(1, (float)value1 / value2) : 1;
        _imgHp.rectTransform.sizeDelta = new Vector2(_stateSliderWidth * percentage, _stateSliderHeight);
        _imgHp.color = Color.white;
        stateStringBuilder.Clear();
        stateStringBuilder.Append(value1.ToString());
        stateStringBuilder.Append("/");
        stateStringBuilder.Append(value2.ToString());
        _textHp.text = stateStringBuilder.ToString();
        ShowWarn(0, 1.0f * value1 / value2 < 0.5f);
    }

    public void UpdateEnergyBar() {
        if (null == RGGameSceneManager.Inst || null == RGGameSceneManager.Inst.controller) {
            return;
        }

        if (UiUtil.CanShowFunctionalUI) {
            UpdateEnergyBarValid();
        } else {
            UpdateInvalidStateBar(_imgEnergy, _textEnergy);
        }
    }

    private void UpdateEnergyBarValid() {
        var attr = RGGameSceneManager.GetInstance().controller.attribute;
        int value1 = attr.energy;
        int value2 = attr.max_energy;
        float percentage = value2 > 0 ? Mathf.Min(1, (float)value1 / value2) : 1;
        _imgEnergy.rectTransform.sizeDelta = new Vector2(_stateSliderWidth * percentage, _stateSliderHeight);
        _imgEnergy.color = Color.white;
        stateStringBuilder.Clear();
        stateStringBuilder.Append(value1.ToString());
        stateStringBuilder.Append("/");
        stateStringBuilder.Append(value2.ToString());
        _textEnergy.text = stateStringBuilder.ToString();
        ShowWarn(2, 1.0f * value1 / value2 < 0.5f);
    }

    private void OnUpdateArmorBar(ArmorChangedEvent e) {
        UpdateArmorBar();
    }

    public void UpdateArmorBar() {
        if (null == RGGameSceneManager.GetInstance() || null == RGGameSceneManager.GetInstance().controller) {
            return;
        }

        if (UiUtil.CanShowFunctionalUI) {
            UpdateArmorBarValid();
        } else {
            UpdateInvalidStateBar(_imgArmor, _textArmor);
        }
    }

    private void OnEnableJoystickController(EnableJoystickControllerEvent e) {
        if (e.isJoystickActive) {
            return;
        }
        string key = e.isJoystickActive ? "ui/enter_joypad_mod" : "ui/exit_joypad_mod";
        GetInstance().ShowTempMessage(key, 2f, true);
    }

    private StringBuilder stateStringBuilder = new StringBuilder();

    private void UpdateArmorBarValid() {
        var attribute = RGGameSceneManager.GetInstance().controller.role_attribute;
        int value1 = attribute.armor;
        int value2 = attribute.Max_armor;
        float percentage = value2 > 0 ? Mathf.Min(1, (float)value1 / value2) : 1;
        _imgArmor.rectTransform.sizeDelta = new Vector2(_stateSliderWidth * percentage, _stateSliderHeight);
        _imgArmor.color = Color.white;
        int coinAddArmor = attribute.MaxArmorValue.GetAdditionValue(
            RoleAttributeValueEnum.BuffCoinAddArmor, 0);

        stateStringBuilder.Clear();
        stateStringBuilder.Append(value1.ToString());
        stateStringBuilder.Append("/");
        stateStringBuilder.Append(coinAddArmor > 0 ? $"<color=#FFFF00>{value2}</color>" : value2.ToString());
        _textArmor.text = stateStringBuilder.ToString();

        ShowWarn(1, value2 > 0 && value1 == 0);
    }

    private void OnUpdatePassiveSkillBar(UpdatePassiveSkillBar e) {
        if (e == null) {
            return;
        }

        if (!e.controller) {
            return;
        }

        if (e.controller.IsDemonstrationCharacter) {
            return;
        }

        if (UIManager.Inst.HasUIViewOpened<UIMallWindow>()) {
            return;
        }
        
        var currentValue = e.currentValue;
        var maxValue = e.maxValue;
        var controller = e.controller;
        var heroStyle = e.heroStyle;
        var usePercentage = e.usePercentage;
        var hideText = e.hideText;
        var maxCount = e.maxCount;
        UpdatePassiveSkillBar(currentValue, maxValue, controller, heroStyle, usePercentage, hideText, maxCount);
    }

    public void UpdatePassiveSkillBar(float currentValue, float maxValue, RGController controller, string heroStyle = null,
        bool usePercentage = false, bool hideText = false, int maxCount = 1) {
        if (!controller.IsLocalPlayer()) {
            return;
        }
        
        if (!_passiveBar.gameObject.activeSelf) {
            _passiveBar.gameObject.SetActive(true);
            _stateBarBottom.GetComponent<RectTransform>().anchoredPosition = new Vector2(0, -91);
            _verticalBarLeft.GetComponent<RectTransform>().anchoredPosition = new Vector2(15, -115);
            if (GameUtil.IsMultiGame()) {
                if (transform.GetComponentInChildren<TeamInfoCtrl>() is { } teamInfoCtrl && teamInfoCtrl != null) {
                    var teamRT = teamInfoCtrl.GetComponent<RectTransform>();
                    teamRT.anchoredPosition = new Vector2(teamRT.anchoredPosition.x, -200f);
                }
            }
            if (!heroStyle.IsNullOrEmpty()) {
                var uiAtlas = ResourcesUtil.Load<SpriteAtlas>($"RGTexture/sprite_atlas/ui/common.spriteatlasv2");
                _passiveBarIcon = uiAtlas.GetSprite($"ui_state_passive_icon_{heroStyle}");
                _passiveBarSlider = uiAtlas.GetSprite($"ui_state_passive_slider_{heroStyle}");
                var passiveFrame = uiAtlas.GetSprite($"ui_state_passive_frame_{heroStyle}");
                if (_passiveBarIcon) {
                    _imgPassiveIcon.sprite = _passiveBarIcon;
                }
                if(_passiveBarSlider)
                {
                    _imgPassive.sprite = _passiveBarSlider;
                }
                if (passiveFrame) {
                    this._imgPassiveFrame.enabled = true;
                    _imgPassiveFrame.sprite = passiveFrame;
                }
            }
            
            if (maxCount > 1) {
                _imgPassives = new Image[maxCount];
                _imgPassive.rectTransform.sizeDelta = new Vector2(_stateSliderWidth * (1 / (float)maxCount), _stateSliderHeight);
                var imgsParent = _imgPassive.transform.parent.Find("layout");
                _imgPassive.transform.SetParent(imgsParent);
                _imgPassives[0] = _imgPassive;
                for (int i = 1; i < maxCount; i++) {
                    _imgPassives[i] = Instantiate(_imgPassive, imgsParent).GetComponent<Image>();
                }
            }
        }
        
        _passiveCurrentValue = currentValue;
        _passiveMaxValue = maxValue;
        _passiveIsPercent = usePercentage;
        _passiveBarStyle = heroStyle;

        var percentage = currentValue > 0 ? Mathf.Min(1, currentValue / maxValue) : 0;
        
        _imgPassive.color = Color.white;
        if (hideText) {
            _textPassive.enabled = false;
        } 
        else {
            if (usePercentage) {
                stateStringBuilder.Clear();
                stateStringBuilder.Append((percentage * 100).ToString("N0"));
                stateStringBuilder.Append("%");
                _textPassive.text = stateStringBuilder.ToString();
            } else {
                stateStringBuilder.Clear();
                stateStringBuilder.Append(currentValue.ToInt().ToString());
                stateStringBuilder.Append("/");
                stateStringBuilder.Append(maxValue.ToInt().ToString());
                _textPassive.text = stateStringBuilder.ToString();
            }
        }

        
        if (maxCount == 1) {
            _imgPassive.rectTransform.sizeDelta = new Vector2(_stateSliderWidth * percentage, _stateSliderHeight);
        } else{
            var delta = 1 / (float)maxCount;
            for (int i = 0; i < maxCount; i++) {
                var tempPercentage = Mathf.Clamp01((percentage - delta * i) / delta);
                _imgPassives[i].fillAmount = tempPercentage;
            }
        }

        if (!UiUtil.CanShowFunctionalUI) {
            UpdateInvalidPassiveSkillBar(_imgPassive, _imgPassiveIcon, _textPassive);
        }
    }
    
    private void UpdateInvalidStateBar(Image stateImg, Text stateText) {
        stateImg.color = stateBarInvalidColor;
        stateText.text = stateBarInvalidText;
        stateImg.rectTransform.sizeDelta = new Vector2(_stateSliderWidth, _stateSliderHeight);
    }

    private void UpdateInvalidPassiveSkillBar(Image stateImg, Image icon, Text stateText) {
        stateImg.color = stateBarInvalidColor;
        stateText.text = stateBarInvalidText;
        stateImg.rectTransform.sizeDelta = new Vector2(_stateSliderWidth, _stateSliderHeight);
        if (!_passiveBarIcon || !_passiveBarSlider) {
            return;
        }

        stateImg.sprite = _passiveBarSlider;
        icon.sprite = _passiveBarIcon;
    }

    public Image[] GetPassiveImgs() {
        return _imgPassives;
    }

    public void ShowCoin(bool show) {
        _currencyGroupWidget.SetActive(ShowCurrencyType.Coin, show);
        if (BattleData.data.IsDefenceMode) {
            _currencyGroupWidget.SetActive(ShowCurrencyType.TokenCoin, show);
        }
    }

    public void UpdateCoin() {
        UpdateCoin(RGGameProcess.Inst.coin_value);
    }

    public void UpdateCoin(int value) {
        if (UiUtil.CanShowFunctionalUI) {
            _currencyGroupWidget.SetText(ShowCurrencyType.Coin, "" + value);
        } else {
            _currencyGroupWidget.SetText(ShowCurrencyType.Coin, coinInvalidText);
        }
    }

    public void UpdateTokenCoin() {
        if (UiUtil.CanShowFunctionalUI) {
            _currencyGroupWidget.SetText(ShowCurrencyType.TokenCoin, "" + BattleData.data.tokenCoin);
        } else {
            _currencyGroupWidget.SetText(ShowCurrencyType.TokenCoin, coinInvalidText);
        }
    }

    public void UpdateSeasonCoin() {
        if (!_currencyGroupWidget) {
            return;
        }

        _currencyGroupWidget.SetText(ShowCurrencyType.SeasonCoin, "" + SeasonData.data.coin);
    }
    
    private void OnActivityCurrencyChanged(ActivityCurrencyChanged e) {
        if (!_currencyGroupWidget) {
            return;
        }

        var changeActName = e.actName;
        var (sprite, actName) = ActivityEntryData.GetCurrentActivityCoinSprite();
        if (changeActName == actName && !string.IsNullOrEmpty(actName)) {
            var actInGameCoin = ActivityEntryData.GetActivityInGameCoin(actName);
            _currencyGroupWidget.SetText(ShowCurrencyType.ActivityCoin, "" + actInGameCoin);
        }
    }
    
    public void SetStateBarInvalid() {
        // _textCoin.text = coinInvalidText;
        _currencyGroupWidget.SetText(ShowCurrencyType.Coin, coinInvalidText);
        UpdateInvalidStateBar(_imgHp, _textHp);
        UpdateInvalidStateBar(_imgEnergy, _textEnergy);
        UpdateInvalidStateBar(_imgArmor, _textArmor);
        if (_passiveBar.gameObject.activeSelf) {
            UpdateInvalidPassiveSkillBar(_imgPassive, _imgPassiveIcon, _textPassive);
        }
    }

    public void SetStateBarValid() {
        _currencyGroupWidget.SetText(ShowCurrencyType.Coin, "" + RGGameProcess.Inst.coin_value);
        // _textCoin.text = "" + RGGameProcess.Inst.coin_value;
        UpdateHpBarValid();
        UpdateEnergyBarValid();
        UpdateArmorBarValid();
        if (!_passiveBar.gameObject.activeSelf) {
            return;
        }

        var controller = RGGameSceneManager.Inst.controller;
        UpdatePassiveSkillBar(_passiveCurrentValue, _passiveMaxValue, controller, _passiveBarStyle,
            _passiveIsPercent);
    }

    private readonly Color powerUpSkillMaskReadyColor = new Color(1f, 0f, 40f / 255f);
    private readonly Color buffSkillMaskReadyColor = new Color(0/255f, 108/255f, 0f);

    
    

    public void UpdateSkillMask(float thisSkillTime, float cd, int multiCount = -1, emSkillType skillType = emSkillType.Normal) {
        

        if (multiCount == -1) {

            if (text_skill_count.transform.parent.gameObject.activeSelf) {
                text_skill_count.transform.parent.gameObject.SetActive(false);
#if UNITY_SWITCH
                switchBettleUI.text_skill_count.transform.parent.gameObject.SetActive(false);
                switchBettleUI.ShowSkillConsume(false);
#endif
            }
        } else {
            //可积累次数技能
            if (!text_skill_count.transform.parent.gameObject.activeSelf) {
                text_skill_count.transform.parent.gameObject.SetActive(true);
#if UNITY_SWITCH
                switchBettleUI.text_skill_count.transform.parent.gameObject.SetActive(true);
                switchBettleUI.ShowSkillConsume(true);
#endif
            }

            if (!text_skill_count.enabled) {
                text_skill_count.enabled = true;
#if UNITY_SWITCH
                switchBettleUI.text_skill_count.enabled = true;
#endif
            }


            text_skill_count.text = multiCount.ToString();
#if UNITY_SWITCH
            switchBettleUI.text_skill_count.text = multiCount.ToString();
#endif
        }

        if (skillType == emSkillType.PowerUp) {
            image_skill_count.color = powerUpSkillMaskReadyColor;
#if UNITY_SWITCH
            switchBettleUI.image_skill_count.color = powerUpSkillMaskReadyColor;
#endif
        } else if (skillType == emSkillType.BuffAccumulate) {
            image_skill_count.color = buffSkillMaskReadyColor;
#if UNITY_SWITCH
            switchBettleUI.image_skill_count.color = buffSkillMaskReadyColor;
#endif
        }

        if(RGGameSceneManager.Inst != null || RGGameSceneManager.Inst.controller != null){
            var controller = RGGameSceneManager.Inst.controller;
            if(controller && controller.skills != null && controller.skillIndex >= 0 && controller.skillIndex < controller.skills.Count) {
                _skillBtn.SetSkillCooldown(thisSkillTime / cd, multiCount);
            }
            
        }
        
#if UNITY_SWITCH
        switchBettleUI.skill_icon_mask.fillAmount = thisSkillTime / cd;
#endif
    }

    public void UpdateSkillEnergyCost(int energyCostValue,bool open = true) {
        var energyCostIcon = _textSkillEnergy.transform.parent.gameObject;
        energyCostIcon.SetActive(open);
        _textSkillEnergy.text = energyCostValue.ToString();
    }
    
    // 改变武器图标
    public void ClearWeaponIcon() {
        ChangeWeaponIcon(null, 0);
        FlushWeaponSlot(null);
        ShowBtnSpecial(-1);
    }

    public void ChangeWeaponIcon(Sprite icon, int consume) {
        ChangeWeaponIcon(icon, consume, Color.white);
    }

    public void ChangeWeaponDisplayObj(RGWeapon weapon, int consume) {
        var weaponCustomDisplay = weapon.GetComponent<IWeaponCustomDisplay>();
        if (weaponCustomDisplay == null || weaponCustomDisplay.UseDefaultUIImage) {
            SpriteRenderer spriteRenderer = null;
            if (weapon.transform.Find("w_icon") != null) {
                // 用于图标展示
                spriteRenderer = weapon.transform.Find("w_icon").GetComponent<SpriteRenderer>();
            } else {
                var w = weapon.transform.Find("w");
                if (w) spriteRenderer = w.GetComponent<SpriteRenderer>();
                if (!spriteRenderer) {
                    w = weapon.transform.GetChild(0);
                    if (w) spriteRenderer = w.GetComponent<SpriteRenderer>();
                }
            }

            ChangeWeaponIcon(
                spriteRenderer ? spriteRenderer.sprite : null, consume, Color.white);
        } else {
            weapon_icon.enabled = false;
            weaponCustomDisplayer.Display(weapon_icon.transform as RectTransform, weaponCustomDisplay);
            ChangeWeaponConsume(consume, Color.white);
        }
    }

    public void ChangeWeaponIconImageMaterial(Material material) {
        weapon_icon.material = material;
    }

    private WeaponCustomDisplayer weaponCustomDisplayer = new WeaponCustomDisplayer();

    public void ChangeWeaponIcon(Sprite icon, int consume, Color color) {
        weaponCustomDisplayer.Clean();
        weapon_icon.enabled = icon != null;
        weapon_icon.sprite = icon;
        weapon_icon.color = color;
        weapon_icon.SetNativeSize();
        if (weapon_icon.rectTransform.sizeDelta.x > 200 || weapon_icon.rectTransform.sizeDelta.y > 200) {
            weapon_icon.rectTransform.sizeDelta = Vector2.one * 200;
        }

        weapon_icon.material = weaponIconInitMaterial;
        //这里重置weapon_icon的锚点和位置，方便sandbox模式下假人对位置进行调整以保证居中（因为角色图片锚点不可调）
        weapon_icon.rectTransform.pivot = new Vector2(0.5f, 0.5f);
        weapon_icon.rectTransform.anchoredPosition = new Vector2(2.8f, 3.3f);
#if UNITY_SWITCH
        //TODO 皮肤换八帧后，GunBoxCharactor.cs 沙盒模式假人武器在ui上的位置会偏移，需要处理。
        switchBettleUI.weapon_icon.enabled = icon != null;
        switchBettleUI.weapon_icon.sprite = icon;
        switchBettleUI.weapon_icon.color = color;
        switchBettleUI.weapon_icon.SetNativeSize();
        switchBettleUI.weapon_icon.rectTransform.sizeDelta /= 2.0f;
#endif
        ChangeWeaponConsume(consume, Color.white);
    }

    public void ChangeWeaponIconPivot(Vector2 pivot) {
        weapon_icon.rectTransform.pivot = pivot;
        weapon_icon.rectTransform.anchoredPosition = new Vector2(2.8f, 3.3f);
    }

    // public void ChangeWeaponItemIcon(WeaponItemBase item) {
    //     weapon_item_icon.RefreshSlots(item ? new List<Sprite> {item.weapon_item_sprite} : new List<Sprite>());
    // }

    public void FlushWeaponSlot(RGWeapon weapon) {
        if (!weapon) {
            weapon_item_icon.RefreshSlots(null);
        } else if (WeaponAffixLevelManager.Inst) {
            var sprites = weapon.GetWeaponAffixSprites();
            weapon_item_icon.RefreshSlots(sprites);
        } else {
            weapon_item_icon.RefreshSlots(weapon.weapon_item ? new List<Sprite> {weapon.weapon_item.weapon_item_sprite} : new List<Sprite>());
        }
    }

    public void ChangeWeaponConsume(int consume, Color color) {
        const int weaponConsumeRandom = -1;
        weapon_consume.text = consume == weaponConsumeRandom ? "?" : consume.ToString();
        weapon_consume.color = color;
#if UNITY_SWITCH
        switchBettleUI.weapon_consume.text = "" + consume;
        switchBettleUI.weapon_consume.color = color;
#endif
    }

    public void ChangeWeaponCorner(bool enable, int value, Color color) {
        const int weaponConsumeRandom = -1;
        weapon_corner.text = value == weaponConsumeRandom ? "?" : value.ToString();
        weapon_corner.color = color;
        weapon_corner.gameObject.SetActive(enable);
    }

    /// <summary>
    /// 显示武器攻击按键的icon和信息
    /// </summary>
    /// <param name="rgWeapon"></param>
    public void ShowWeaponBtnIconInfo(RGWeapon rgWeapon) {
        if (null == rgWeapon) {
            return;
        }
        if (BattleData.data.IsLoopTravel) {
            _weaponPressureLv.enabled = true;
            _weaponPressureLv.text = rgWeapon.PressureLv.ToString();
            if (ChannelConfig.IsGP_VNM) {
                _weaponPressureLv.fontSize = 20;
            }
        } else {
            _weaponPressureLv.enabled = false;
        }
        
    }

    //显示警官一技能叠加的层数,字体没有负数，小于0则不显示
    public void ShowOfficerSkill0Count(int value) {
        _weaponLeftBottomText.enabled = value >= 0;
        _weaponLeftBottomText.text = value.ToString();
    }
    
    #region 小地图相关
    private MiniMapUICtrl _newMiniMapCtrl;

    public void InitNewMiniMap() {
        GameObject miniMapPrefab = ResourcesUtil.Load<GameObject>("RGPrefab/Other/scene_object/MiniMap/miniMap.prefab");
        Transform parent = transform.Find("map_info_root");
        GameObject miniMap = Instantiate(miniMapPrefab, parent);
        _newMiniMapCtrl = miniMap.GetComponent<MiniMapUICtrl>();
        _newMiniMapCtrl.InitMap();
        _newMiniMapCtrl.ShowMap();
    }
    
    public void UpdateMap() {
        if (_newMiniMapCtrl != null) {
            _newMiniMapCtrl.RefreshMap();
        }
    }

    public void HideMap(bool immediately = false) {
        if (_newMiniMapCtrl != null) {
            if(immediately) {
                _newMiniMapCtrl.gameObject.SetActive(false); 
            }
            _newMiniMapCtrl.HideMap();
        }
    }
    #endregion

    

    public void ShowTempUI() {
        GameManager.Inst.PauseGame();
        anim.SetBool("show_temp_ui", true);
        anim.SetBool("show", false);
    }

    public void HideTempUI() {
        GameManager.Inst.ContinueGame();
        anim.SetBool("show_temp_ui", false);
        anim.SetBool("show", true);
        Invoke("DestroyTempUI", 0.5f);
    }

    void DestroyTempUI() {
        if (temp_ui.childCount != 0) {
            Destroy(temp_ui.GetChild(0).gameObject);
        }
    }

    /// <summary>
    /// 展示详细信息栏
    /// </summary>
    /// <param name="iconIndexes">要展示的小图标索引</param>
    /// <param name="texts">要展示的文本</param>
    /// <param name="bgIndex">背景索引.0普通框, 1银框,2金框</param>
    public void ShowItemInfo(int[] iconIndexes, string[] texts, int bgIndex = 0, Sprite custonIcon = null) {
        ui_item_info.SetItemInfo(iconIndexes, texts, bgIndex, custonIcon);
        anim.SetBool(AnimShowItemInfo, true);
    }

    public void SetPressureLv(int pressureLv) {
        ui_item_info.SetPressureLv(pressureLv);
    }
    
    public void SetWeaponEvolutionDescribe(RGWeapon weapon,bool needAnim = false) {
        ui_item_info.SetWeaponEvolutionDesc(weapon);
        if (needAnim) {
            anim.SetBool(AnimShowItemInfo, true);
        }
    }

    public void ShowWeaponEvolutionDescribeAndInfo(RGWeapon weapon) {
        weapon.ShowItemInfo();
        SetWeaponEvolutionDescribe(weapon);
    }

    public void SetWeaponAffixLevel(List<WeaponAffixBase> affixes) {
        ui_item_info.SetWeaponAffixLevel(affixes);
        anim.SetBool(AnimShowItemInfo, true);
    }

    public void HideWeaponAffixLevel() {
        ui_item_info.HideWeaponAffixLevel();
    }

    public void SetWeaponAffix(string otherText) {
        ui_item_info.SetWeaponAffixDesc(otherText);
        anim.SetBool(AnimShowItemInfo, true);
    }
    
    public void HideWeaponAffix() {
        ui_item_info.HideWeaponAffixDesc();
    }
    
    public void ShowItemInfo(WeaponInfoIcon.emIcon[] iconEnums, string[] texts, int bgIndex = 0, Sprite custonIcon = null) {
        int[] iconIndexes = new int[iconEnums.Length];
        for (int i = 0; i < iconEnums.Length; i++) {
            iconIndexes[i] = (int)iconEnums[i];
        }

        this.ShowItemInfo(iconIndexes, texts, bgIndex);
    }

    public void ShowItemInfo(string desc, int bgIndex = 0) {
        ui_item_info.SetItemInfo(desc, bgIndex);
        anim.SetBool(AnimShowItemInfo, true);
    }

    public void ShowItemInfo(Sprite[] sprites) {
        ui_item_info.SetItemInfo(sprites);
    }

    public void HideItemInfo() {
        if (object_tap == null) {
            return;
        }
        anim.SetBool(AnimShowItemInfo, false);
        ui_item_info.Hide();
        var text = object_tap.Find("Text");
        var outLine = text.GetComponent<Outline>();
        var shadow = text.GetComponent<Shadow>();
        if (text && outLine) {
            outLine.enabled = false;
        }
        if (text && shadow) {
            shadow.enabled = true;
        }
        HideMythicWeaponUI();
        HideWeaponObjectTap();
        HideWeaponEvolutionDescription();
        HideWeaponAffix();
        HideWeaponAffixLevel();
    }
    
    public void LateUpdate()
    {
        if (_mythicWeapon != null && _uiMythic != null && _uiMythic.gameObject.activeSelf) {
            Vector2 point = Camera.main!.WorldToScreenPoint(_mythicWeapon.transform.position + Vector3.up * CommonConfig.Config.uiMythicHeight);
            _uiMythic.transform.position = point;
        }
    }

    private IMythicWeapon _mythicWeapon;
    private RectTransform _uiMythic;

    public void ShowMythicWeaponUI(IMythicWeapon mythicWeapon) {
        if (_uiMythic == null) {
            var prefab = ResourcesUtil.Load<GameObject>("RGPrefab/Other/scene_object/ui_mythic_weapon.prefab"); 
            _uiMythic = Instantiate(prefab).transform as RectTransform;
            _uiMythic!.parent = transform;
            _uiMythic.SetSiblingIndex(transform.childCount - 5);
            _uiMythic.localScale = prefab.transform.localScale;
        }

        _uiMythic.Find("name").GetComponent<Text>().text = mythicWeapon.GetName();
        _uiMythic.Find("ScrollView").GetComponent<ScrollRect>().verticalNormalizedPosition = 1;
        _uiMythic.Find("ScrollView/Viewport/Content/desc").GetComponent<Text>().text = mythicWeapon.GetDesc(mythicWeapon.MythicLevelInBattle, "\n\n", "\n");
        _uiMythic.gameObject.SetActive(true);
        _mythicWeapon = mythicWeapon;
    }

    public void HideMythicWeaponUI() {
        if (_uiMythic != null) {
            _uiMythic.gameObject.SetActive(false);
        }

        _mythicWeapon = null;
    }
    
    public void HideWeaponObjectTap() {
        if (_weaponObjectTap == null) {
            return;
        }
        _weaponObjectTap.gameObject.SetActive(false);
    }

    public void HideWeaponEvolutionDescription() {
        ui_item_info.HideWeaponEvolutionDesc();
    }
    
    public void ShowLevelBuff(List<RGLevelBuff> buffs) {
        _uiLevelBuffDock.SetLevelBuff(buffs);
        anim.SetBool("show_level_buff_ui", true);
    }

    public void ShowReReadGuildInfo(UnityAction action, string titleKey = null) {
        _uiReReadGuildInfo.SetInfo(action, titleKey);
        anim.SetBool("show_re_read_guild_info", true);
    }

    public void HideReReadGuildInfo() {
        _uiReReadGuildInfo.ClearInfo();
        anim.SetBool("show_re_read_guild_info", false);
    }

    public void ShowTempMessage(string value, float e_time, bool needLocalized = true) {
        var msgGo = GameObject.Find("/Canvas/temp_message");
        if (msgGo == null) {
            GameObject temp_obj = Instantiate(ResourcesUtil.Load(
                "RGPrefab/UI/temp_message.prefab")) as GameObject;
            temp_obj.transform.SetParent(UICanvas.GetInstance().transform, false);
            temp_obj.name = "temp_message";
            temp_obj.GetComponent<UITempMessage>().SetMessage(value, e_time, needLocalized);
        } else {
            msgGo.GetComponent<UITempMessage>().SetMessage(value, e_time, needLocalized);
            msgGo.transform.SetAsLastSibling();
        }
    }

    public void EndTempMessage() {
        if (GameObject.Find("/Canvas/temp_message") != null) {
            GameObject.Find("/Canvas/temp_message").GetComponent<UITempMessage>().EndMessage();
        }
    }

    public void ShowMessage(string message, float time) {
        _messageBar.ShowMessage(message, time);
    }

    public void ShowLevelMessage(int branchIndex, string message, float time) {
        _messageBar.ShowLevelIndex(branchIndex, message, time);
    }

    public void ShowLevelTip(string message, Vector2 offset, float duration) {
        var proto = ResourcesUtil.Load<GameObject>("RGPrefab/UI/level_tip.prefab");
        var tip = Instantiate(proto, transform);
        var text = tip.transform.Find("Text").GetComponent<Text>();
        text.color = new Color(1, 1, 1, 0);
        text.text = message;
        text.rectTransform.localPosition += (Vector3)offset;
        var se = DOTween.Sequence();
        const float animTime = 0.8f;
        se.Append(text.DOColor(Color.white, animTime / 2));
        se.AppendInterval(duration - animTime);
        se.Append(text.DOColor(new Color(1, 1, 1, 0), animTime / 2));
        Destroy(tip, duration);
    }

    /// <summary>
    /// 房间清理后显示"完成"字样
    /// </summary>
    public void ShowSignpost() {
        _messageBar.ShowSignpost();
    }

    /// <summary>
    /// 显示"全灭"字样
    /// </summary>
    public void ShowAllDead() {
        _messageBar.ShowAllDead();
    }

    public void ShowRoomStart() {
        _messageBar.ShowRoomStart();
    }

    /// <param name="target">跟随的目标.</param>
    /// <param name="damage">数值.</param>
    /// <param name="offset">offset.</param>
    /// <param name="targetName">名字.</param>
    public void ShowTextHurt(Transform target, HurtInfo hurtInfo, float offset, string targetName) {
        if (!RGGameProcess.Inst.modeProcess.EnableDamageTextDisplaying()) {
            return;
        }

        SimpleEventManager.Raise(ShowTextHurtEvent.UseCache(target, hurtInfo, offset, targetName));
        if (ShowTextHurtEvent.IsCustomHandled) {// 自定义处理伤害字体 则直接调用自定义处理然后返回
            if (ShowTextHurtEvent.CustomTextHurt == null) {
                Debug.LogError("CustomTextHurt is null, but IsCustomHandled is true.");
            } else {
                ShowTextHurtEvent.CustomTextHurt.ShowHurtText(target, hurtInfo, offset);
                return;
            }
        }
        
        if (_damageNumberStyle == 0 || hurtInfo.Dodged) {
            UIDamageText.CreateInstance(target, hurtInfo, offset);
            return;
        }

        if (GameObject.Find("/Canvas_world/hurt_" + targetName) == null) {
            GameObject temp_obj = PrefabPool.Inst.Take(PrefabManager.GetPrefab(PrefabName.text_info),
                target.position, Quaternion.identity) as GameObject;
            temp_obj.transform.SetParent(canvas_world, false);
            temp_obj.GetComponent<UITextInfo>().ShowTextHurt(target, hurtInfo.Damage, offset);
            temp_obj.transform.localScale = new Vector3(1, 1, 1);
            temp_obj.name = "hurt_" + targetName;
        } else {
            GameObject.Find("/Canvas_world/hurt_" + targetName).GetComponent<UITextInfo>()
                .AddHurtValue(hurtInfo.Damage);
        }
    }

    public void ShowTextEnergy(Vector3 value1, int value2, float value3, string value4) {
        if (GameObject.Find("/Canvas_world/energy_" + value4) == null) {
            value1.y += value3;
            GameObject temp_obj = PrefabPool.Inst.Take(PrefabManager.GetPrefab(PrefabName.text_info), value1, Quaternion.identity) as GameObject;
            temp_obj.transform.SetParent(canvas_world, false);
            temp_obj.GetComponent<UITextInfo>().ShowTextEnergy(value2);
            temp_obj.transform.localScale = new Vector3(1, 1, 1);
            temp_obj.name = "energy_" + value4;
        } else {
            GameObject.Find("/Canvas_world/energy_" + value4).GetComponent<UITextInfo>().AddValue(value2, 0.3f);
        }
    }

    public void ShowTextCoin(Vector3 value1, int value2, float value3, string value4, ItemLevel currencyType = ItemLevel.GoldCoin) {
        if (GameObject.Find("/Canvas_world/coin_" + value4) == null) {
            value1.y += value3;
            GameObject temp_obj = PrefabPool.Inst.Take(PrefabManager.GetPrefab(PrefabName.text_info), value1, Quaternion.identity) as GameObject;
            temp_obj.transform.SetParent(canvas_world, false);
            temp_obj.GetComponent<UITextInfo>().ShowTextCoin(value2, currencyType);
            temp_obj.transform.localScale = new Vector3(1, 1, 1);
            temp_obj.name = "coin_" + value4;
        } else {
            GameObject.Find("/Canvas_world/coin_" + value4).GetComponent<UITextInfo>().AddValue(value2, 0.3f);
        }
    }

    public void ShowTextTokenCoin(Vector3 value1, int value2, float value3, string value4, Color color) {
        if (GameObject.Find("/Canvas_world/coin_" + value4) == null) {
            value1.y += value3;
            GameObject temp_obj = PrefabPool.Inst.Take(PrefabManager.GetPrefab(PrefabName.text_info), value1, Quaternion.identity) as GameObject;
            temp_obj.transform.SetParent(canvas_world, false);
            temp_obj.GetComponent<UITextInfo>().ShowTextTokenCoin(value2, color);
            temp_obj.transform.localScale = new Vector3(1, 1, 1);
            temp_obj.name = "coin_" + value4;
        } else {
            GameObject.Find("/Canvas_world/coin_" + value4).GetComponent<UITextInfo>().AddValue(value2, 0.3f);
        }
    }

    public void ShowTextArmor(Vector3 value1, int value2, float value3, string value4) {
        if (GameObject.Find("/Canvas_world/armor_" + value4) == null) {
            value1.y += value3;
            GameObject temp_obj = PrefabPool.Inst.Take(PrefabManager.GetPrefab(PrefabName.text_info), value1, Quaternion.identity) as GameObject;
            temp_obj.transform.SetParent(canvas_world, false);
            temp_obj.GetComponent<UITextInfo>().ShowTextArmor(value2);
            temp_obj.transform.localScale = new Vector3(1, 1, 1);
            temp_obj.name = "armor_" + value4;
        } else {
            GameObject.Find("/Canvas_world/armor_" + value4).GetComponent<UITextInfo>().AddValue(value2, 0.3f);
        }
    }

    public void ShowTextHp(Vector3 value1, int value2, float value3, string value4, bool newText = false) {
        if (GameObject.Find("/Canvas_world/hp_" + value4) == null || newText) {
            value1.y += value3;
            GameObject temp_obj = PrefabPool.Inst.Take(PrefabManager.GetPrefab(PrefabName.text_info), value1, Quaternion.identity) as GameObject;
            temp_obj.transform.SetParent(canvas_world, false);
            temp_obj.GetComponent<UITextInfo>().ShowTextHp(value2);
            temp_obj.transform.localScale = new Vector3(1, 1, 1);
            temp_obj.name = "hp_" + value4;
        } else {
            GameObject.Find("/Canvas_world/hp_" + value4).GetComponent<UITextInfo>().AddValue(value2, 0.3f);
        }
    }
    
    public void ShowTextMountHp(Vector3 value1, int value2, float value3, string value4, bool newText = false) {
        if (GameObject.Find("/Canvas_world/hp_" + value4) == null || newText) {
            value1.y += value3;
            GameObject temp_obj = PrefabPool.Inst.Take(PrefabManager.GetPrefab(PrefabName.text_info), value1, Quaternion.identity) as GameObject;
            temp_obj.transform.SetParent(canvas_world, false);
            temp_obj.GetComponent<UITextInfo>().ShowTextMountHp(value2);
            temp_obj.transform.localScale = new Vector3(1, 1, 1);
            temp_obj.name = "hp_mount_" + value4;
        } else {
            GameObject.Find("/Canvas_world/hp_mount_" + value4).GetComponent<UITextInfo>().AddValue(value2, 0.3f);
        }
    }

    public void ShowTextHpAndMp(Vector3 value1, int hp, int mp, float value3, string value4) {
        value1.y += value3;
        GameObject temp_obj = PrefabPool.Inst.Take(ResourcesUtil.Load<GameObject>(
            "RGPrefab/Register/text_hp_mp.prefab"), value1, Quaternion.identity) as GameObject;
        temp_obj.transform.SetParent(canvas_world, false);
        temp_obj.transform.GetChild(0).GetComponent<UITextInfo>().ShowTextHp(hp);
        temp_obj.transform.GetChild(1).GetComponent<UITextInfo>().ShowTextEnergy(mp);
        temp_obj.transform.localScale = new Vector3(1, 1, 1);
        temp_obj.name = "hp_mp_" + value4;
        GameObject.Destroy(temp_obj, 0.6f);
    }

    public void ShowTextArmorAndMp(Vector3 value1, int armor, int mp, float value3, string value4) {
        value1.y += value3;
        GameObject temp_obj = PrefabPool.Inst.Take(ResourcesUtil.Load<GameObject>(
            "RGPrefab/Register/text_armor_mp.prefab"), value1, Quaternion.identity) as GameObject;
        temp_obj.transform.SetParent(canvas_world, false);
        temp_obj.transform.GetChild(0).GetComponent<UITextInfo>().ShowTextArmor(armor);
        temp_obj.transform.GetChild(1).GetComponent<UITextInfo>().ShowTextEnergy(mp);
        temp_obj.transform.localScale = new Vector3(1, 1, 1);
        temp_obj.name = "hp_mp_" + value4;
        GameObject.Destroy(temp_obj, 0.6f);
    }

    public void ShowTextCritical(Transform target, float offset) {
        ShowTextInfo(target.transform.position, ScriptLocalization.Get("I_critical"), offset, 0.2f);
    }

    public void ShowTextInfo(Vector3 value1, string value2, float value3, float value4) {
        value1.y += value3;
        GameObject temp_obj = PrefabPool.Inst.Take(PrefabManager.GetPrefab(PrefabName.text_info), value1, Quaternion.identity) as GameObject;
        temp_obj.transform.SetParent(canvas_world, false);
        temp_obj.GetComponent<UITextInfo>().ShowTextInfo(value2, value4);
        temp_obj.transform.localScale = new Vector3(1, 1, 1);
    }

    public void ShowTextItem(Vector3 value1, Sprite sprite, Sprite flag, string value2, float value3, float value4) {
        value1.y += value3;
        GameObject temp_obj = PrefabPool.Inst.Take(ResourcesUtil.Load<GameObject>(
            "RGPrefab/Register/text_item.prefab"), value1, Quaternion.identity) as GameObject;
        temp_obj.transform.SetParent(canvas_world, false);
        temp_obj.GetComponent<UITextInfo>().ShowTextItem(sprite, flag, value2, value4);
        temp_obj.transform.localScale = new Vector3(1, 1, 1);
    }

    public void ShowObjectWithImage(Transform parent, Sprite sprite, float offset, float duration, float scale = 1, bool whiteOutline = false) {
        var prefabName = "RGPrefab/Register/text_buff.prefab";
        if(whiteOutline){
            prefabName = "RGPrefab/Register/text_buff_outline.prefab";
        }
        GameObject temp_obj = PrefabPool.Inst.Take(ResourcesUtil.Load<GameObject>(prefabName), parent.position + new Vector3(0, offset, 0), Quaternion.identity) as GameObject;
        temp_obj.transform.SetParent(canvas_world, false);
        temp_obj.GetComponent<UITextInfo>().ShowBuff(parent, sprite, offset, duration);
        temp_obj.transform.localScale = Vector3.one * scale;
    }

    public void ShowBuff(Transform parent, emBuff buff, float offset, float duration) {
        GameObject temp_obj = PrefabPool.Inst.Take(ResourcesUtil.Load<GameObject>(
            "RGPrefab/Register/text_buff.prefab"), parent.position + new Vector3(0, offset, 0), Quaternion.identity) as GameObject;
        temp_obj.transform.SetParent(canvas_world, false);
        var sprite = BuffInfoTable.info.GetInfo(buff).icon;
        temp_obj.GetComponent<UITextInfo>().ShowBuff(parent, sprite, offset, duration);
        temp_obj.transform.localScale = new Vector3(1, 1, 1);
    }

    public void OnShowEffectBanFactor(UIBanFactorEvent e) {
        ShowEffectBanFactor(e.target, e.Factor, e.offset);
    }

    public void OnMount(MountEvent e) {
        if (!SkillButtonEnable) {
            return;
        }

        if (e.MountController.driver == null || e.MountController.driver != RGGameSceneManager.Inst.controller) {
            return;
        }

        if (e.mount && e.MountController != null && !e.MountController.isSkillForm) {
            var btnMount = transform.Find("control/btn_unmount");
            SetSkillButtonActive(false);
            btnMount.gameObject.SetActive(true);
            UIPointerEventHandle.SetPointerDownCallback(btnMount as RectTransform, () => {
                transform.GetComponent<RGJoyControl>().OnSkillBtnClick(true);
            });
        } else {
            SetSkillButtonActive(true);
            transform.Find("control/btn_unmount").gameObject.SetActive(false);
        }
    }

    void OnGameSettingChange(GameSettingChangeEvent e) {
        if (e.category == GameSettingChangeEvent.Category.JoyStick) {
            if(RGGameSceneManager.Inst == null || RGGameSceneManager.Inst.controller == null || _skillBtn == null){
                return;
            }
            _skillBtn.OnSettingsChanged();
        }
    }

    private void OnMallUnlockEvent(MallUnlockEvent e) {
        RefreshMallButton();
    }

    private void OnUpdateComboGunRedPointEvent(UpdateComboGunRedPointEvent e) {
        var isComboGun = BattleData.data.CompareFactor(emBattleFactor.ComboGun);
        if (isComboGun) {
            RefreshComboGunEntryRedPoint();
        }
    }

    public void ShowEffectBanFactor(Transform target, emBattleFactor factor, float offset) {
        GameObject banFactor = PrefabPool.Inst.Take(ResourcesUtil.Load<GameObject>(
                "RGPrefab/Register/ui_effect_ban_factor.prefab"), target.position + new Vector3(0, offset, 0),
            Quaternion.identity);
        banFactor.transform.SetParent(canvas_world, false);
        var uiEffectBanFactorCom = banFactor.GetComponent<UIEffectBanFactor>();
        Sprite icon = ChallengeInfo.info.GetSprite(factor);
        uiEffectBanFactorCom.Show(target, icon, offset);
    } 

    public Sprite GetBuffSprite(emBuff buff) {
        return BuffInfoTable.info.GetInfo(buff).icon;
    }

    public Sprite GetUnKnownSprite() {
        if(ui_window_pause is UIWindowPause traditionalPause){
            return traditionalPause.unKnown_buff_sprite;
        }
        return null;
    }

    public void ShowPickable(Transform parent, PickableInfo info, float offset = 2.25f, float duration = 1.5f) {
        var path = $"RGPrefab/Register/text_material.prefab";
        var pos = parent.position + new Vector3(0, offset, 0);
        pos.z = 0;
        var tempObj = PrefabPool.Inst.Take(
            ResourcesUtil.Load<GameObject>(path), pos, Quaternion.identity);
        tempObj.transform.SetParent(canvas_world, false);
        tempObj.GetComponent<UITextInfo>().ShowPickable(parent, info, offset, duration);
        tempObj.transform.localScale = new Vector3(1, 1, 1);
    }
    
    public void ShowPickableWithSprite(Transform parent, PickableInfo info, Sprite icon,float offset = 2.25f, float duration = 1.5f) {
        var path = $"RGPrefab/Register/text_material_info.prefab";
        var pos = parent.position + new Vector3(0, offset, 0);
        pos.z = 0;
        var tempObj = PrefabPool.Inst.Take(
            ResourcesUtil.Load<GameObject>(path), pos, Quaternion.identity);
        tempObj.transform.SetParent(canvas_world, false);
        var iconObj = tempObj.transform.Find("Image/item/preview/icon").gameObject;
        iconObj.SetActive(true);
        iconObj.GetComponent<Image>().sprite = icon;
        tempObj.GetComponent<UITextInfo>().ShowPickable(parent, info, offset, duration);
        tempObj.transform.localScale = new Vector3(1, 1, 1);
    }

    /// <summary>
    /// Shows the text talk.
    /// </summary>
    /// <param name="root">目标物体.</param>
    /// <param name="text">对话.</param>
    /// <param name="yOffset">offset.</param>
    /// <param name="duration">显示时间.</param>
    public void ShowTextTalk(Transform root, string text, float yOffset, float duration) {
        if (GameObject.Find("/Canvas_world/talk_" + root.name) == null) {
            //Debug.Log ("talk_"+value2);
            GameObject temp_obj =
                PrefabPool.Inst.Take(PrefabManager.GetPrefab(PrefabName.text_info), (root.position + new Vector3(0, yOffset, 0)), Quaternion.identity) as GameObject;
            temp_obj.transform.SetParent(canvas_world, false);
            temp_obj.GetComponent<UITextInfo>().ShowTalk(root, text, yOffset, duration);
            temp_obj.transform.localScale = new Vector3(1, 1, 1);
            temp_obj.name = "talk_" + root.name;
        } else {
            GameObject.Find("/Canvas_world/talk_" + root.name).GetComponent<UITextInfo>().ChangeTalk(text, duration);
        }
    }

    /// <summary>
    /// Shows the image talk.
    /// </summary>
    /// <param name="root">目标物体.</param>
    /// <param name="sprite">对话.</param>
    /// <param name="offset">offset.</param>
    /// <param name="duration">显示时间.</param>
    public void ShowImageTalk(Transform root, Sprite sprite, float offset, float duration) {
        GameObject temp_obj =
            PrefabPool.Inst.Take(PrefabManager.GetPrefab(PrefabName.image_info),
                (root.position + new Vector3(0, offset, 0)), Quaternion.identity) as GameObject;
        temp_obj.transform.SetParent(canvas_world, false);
        temp_obj.GetComponent<UIImageInfo>().ShowTalk(root, sprite, offset, duration);
        temp_obj.transform.localScale = new Vector3(1, 1, 1);
        temp_obj.name = "talk_image_" + root.name;
    }

    public void ShowWeaponTextTalk(Transform root, string text, RGWeapon weapon, float yOffset, float duration,
        int level, string id) {
        string textName = string.IsNullOrEmpty(id) ? "weapon_talk_" + root.name : "weapon_talk_" + id;
        string path = "/Canvas_world/" + textName;
        var color = level.ToItemLevel().ToColor();
        if (GameObject.Find(path) == null) {
            GameObject temp_obj =
                PrefabPool.Inst.Take(PrefabManager.GetPrefab(PrefabName.weapon_text_info),
                    (root.position + new Vector3(0, yOffset, 0)), Quaternion.identity) as GameObject;
            temp_obj.transform.SetParent(canvas_world, false);
            temp_obj.GetComponent<UIWeaponTextInfo>().ShowTalk(root, weapon, level, text, yOffset, duration, color);
            temp_obj.transform.localScale = new Vector3(1, 1, 1);
            temp_obj.name = textName;
        } else {
            GameObject.Find(path).GetComponent<UIWeaponTextInfo>().ChangeTalk(text, weapon, level, duration, color);
        }
    }

    public void ShowWeaponTextTalk(Transform root, string text, RGWeapon weapon, float yOffset, float duration,
        int level) {
        ShowWeaponTextTalk(root, text, weapon, yOffset, duration, level, String.Empty);
    }

    public void ShowTextTalk(Transform root, string text, float yOffset, float duration, int level, string id) {
        string textName = string.IsNullOrEmpty(id) ? "talk_" + root.name : "talk_" + id;
        string path = "/Canvas_world/" + textName;
        if (GameObject.Find(path) == null) {
            GameObject temp_obj =
                PrefabPool.Inst.Take(PrefabManager.GetPrefab(PrefabName.text_info), (root.position + new Vector3(0, yOffset, 0)), Quaternion.identity) as GameObject;
            temp_obj.transform.SetParent(canvas_world, false);
            temp_obj.GetComponent<UITextInfo>().ShowTalk(root, text, yOffset, duration, level.ToItemLevel().ToColor());
            temp_obj.transform.localScale = new Vector3(1, 1, 1);
            temp_obj.name = textName;
        } else {
            GameObject.Find(path).GetComponent<UITextInfo>().ChangeTalk(text, duration, level.ToItemLevel().ToColor());
        }
    }

    public void ShowTextTalk(Transform root, string text, float yOffset, float duration, int level) {
        ShowTextTalk(root, text, yOffset, duration, level, String.Empty);
    }

    public void ShowTextTalk(string text, float offset, float duration, int level) {
        if (GameObject.Find("/Canvas_world/talk_" + RGGameSceneManager.GetInstance().controller.name) == null) {
            GameObject temp_obj = PrefabPool.Inst.Take(PrefabManager.GetPrefab(PrefabName.text_info)) as GameObject;
            temp_obj.transform.SetParent(canvas_world);
            temp_obj.GetComponent<UITextInfo>().ShowTalk(RGGameSceneManager.GetInstance().controller.transform, text, offset, duration, level.ToItemLevel().ToColor());
            temp_obj.transform.localScale = new Vector3(1, 1, 1);
            temp_obj.name = "talk_" + RGGameSceneManager.GetInstance().controller.name;
        } else {
            GameObject.Find("/Canvas_world/talk_" + RGGameSceneManager.GetInstance().controller.name).GetComponent<UITextInfo>().ChangeTalk(text, duration, level.ToItemLevel().ToColor());
        }
    }
    
    public void HideTextTalk(Transform root) {
        var obj = GameObject.Find("/Canvas_world/talk_" + root.name);
        if (obj != null) {
            var uiTextInfo = obj.GetComponent<UITextInfo>();
            if (uiTextInfo) {
                uiTextInfo.EndShowTime();
            } else {
                Destroy(obj);
            }
        }
    }

    public void ShowObjectInfo(Vector3 value1, string value2,bool needOutLine=false) {
        HideItemInfo();
        value1.z = 0;
        object_tap.position = value1;
        var text = object_tap.Find("Text");
        text.GetComponent<Text>().color = ItemLevel.Common.ToColor();
        text.GetComponent<Text>().text = value2;
        object_tap.GetComponent<Animator>().SetBool("show", true);
        
        var outLine = text.GetComponent<Outline>();
        var shadow = text.GetComponent<Shadow>();
        if (text && outLine) {
            outLine.enabled = needOutLine;
        }
        if (text && shadow) {
            shadow.enabled = !needOutLine;
        }
        
    }

    public void ShowWeaponTap(Vector3 value1, RGWeapon weapon, int itemLevel, int fontSize = 32) {
        if (_weaponObjectTap == null) {
            return;
        }

        HideItemInfo();
        _weaponObjectTap.gameObject.SetActive(true);
        _weaponObjectTap.ShowWeaponInfo(value1, weapon, itemLevel, fontSize);
    }

    public void ShowWeaponTap(Vector3 value1, RGWeapon weapon, string text, int itemLevel, int fontSize = 32) {
        if (_weaponObjectTap == null) {
            return;
        }

        HideItemInfo();
        _weaponObjectTap.gameObject.SetActive(true);
        _weaponObjectTap.ShowWeaponInfo(value1, weapon, text, itemLevel, fontSize);
    }

    public void ShowObjectInfo(Vector3 value1, string value2, int itemLevel, int fontSize = 32) {
        HideItemInfo();
        value1.z = 0;
        object_tap.position = value1;
        var text = object_tap.Find("Text").GetComponent<Text>();
        text.color = itemLevel.ToItemLevel().ToColor();
        text.text = value2;
        text.fontSize = fontSize;
        text.rectTransform.sizeDelta = new Vector2(450, 800);
        object_tap.GetComponent<Animator>().SetBool("show", true);
    }

    public Transform ShowObjectInfoMulti(Vector3 value1, string value2) {
        value1.z = 0;
        var tap = Instantiate(object_taps.GetChild(0).gameObject, object_taps).transform;
        tap.gameObject.SetActive(true);
        tap.position = value1;
        tap.Find("Text").GetComponent<Text>().color = ItemLevel.Common.ToColor();
        tap.Find("Text").GetComponent<Text>().text = value2;
        if (tap.GetComponent<Animator>()) {
            tap.GetComponent<Animator>().SetBool("show", true);
        }
        return tap;
    }

    /// <summary>
    /// 展示特殊的价格
    /// </summary>
    public void ShowSpecialPriceInfo(Vector3 offset, Sprite icon, string price) {
        HideItemInfo();
        object_tap.position = offset;
        object_tap.GetComponentInParent<Text>().text = price;
        object_tap.Find("Text").GetComponent<Text>().color = ItemLevel.Common.ToColor();
        object_tap.Find("Text").GetComponent<Text>().text = price;
        object_tap.GetComponent<Animator>().SetBool("show", true);
    }

    public void HideObjectInfo() {
        if (null == object_tap) {
            return;
        }

        var textTf = object_tap.Find("Text");
        if (null == textTf) {
            return;
        }

        var text = textTf.GetComponent<Text>();
        if (null != text) {
            text.text = "";
        }
        
        var animator = object_tap.GetComponent<Animator>();
        if (null != animator) {
            animator.SetBool("show", false);
        }
    }

    //商店窗口 主要是买宝石
    public static BaseUIView ShowUIWindowShop(BaseUIView value, Action onBuyRebornCard = null,
        ConsumeStatistics.BuyWay buyWay = ConsumeStatistics.BuyWay.NONE) {
#if UNITY_SWITCH
        GemShake();
        return;
#endif
        var window = MallUtility.OpenUIBuyGem(value, onBuyRebornCard, buyWay);
        if (ABTest.Get630FishchipSeasonStoreJumpMallTestGroup() != ABTestGroup.B) {
            TAUtil.Track("ui_btn_gem");
        }

        if (buyWay == ConsumeStatistics.BuyWay.GemShop_UI_Btn) {
            BehaviourPathStatistics.TrackBehaviour(emBehaviourPoint.UI_Gem_Btn);
        }

        return window;
    }

    public static BaseUIView ShowUIWindowShop(ConsumeStatistics.BuyWay buyWay) {
#if UNITY_SWITCH
        GemShake();
        return;
#endif
        var window = MallUtility.OpenUIBuyGem(null, null, buyWay);
        if (ABTest.Get630FishchipSeasonStoreJumpMallTestGroup() != ABTestGroup.B) {
            TAUtil.Track("ui_btn_gem");
        }

        return window;
    }

    [Obsolete("Use ShowUIWindowShop(BuyWay) instead.")]
    public void ShowUIWindowShop() {
        ShowUIWindowShop(null, null, ConsumeStatistics.BuyWay.GemShop_UI_Btn);
    }

    public bool reEnteringHall;
    private void ReEnterHall() {
        if (MapManager.Instance && MapManager.Instance.EnterNextMap) {
            // 正在进入地牢时，不能重进客厅
            // TODO Game State FSM
            return;
        }
        
        if (DataUtil.IsPlayerFirstReenterHall()) {
            GameManager.Inst.PauseGame();
            UiUtil.ShowDialogWindow(transform.parent, ScriptLocalization.Get("reenter_hall_tip_title", "更换角色提示"),
                ScriptLocalization.Get("reenter_hall_tip_desc", "更换角色会重新刷新骑士之家，确定要重新选择角色吗？"), () => {
                    GameManager.Inst.ContinueGame();
                    DataUtil.RecordPlayerFirstReenterHall();
                    ReEnterHall();
                }, () => {
                    GameManager.Inst.ContinueGame();
                }, 0);
            return;
        }

        reEnteringHall = true;
        _reenterBtn.onClick.RemoveAllListeners();

        StaticBtnRetryClick();
        
        BehaviourPathStatistics.TrackBehaviour(
            emBehaviourPoint.ReenterFromHeroRoom, new Dictionary<string, object> {
                {"game_type", emGameType.Single.ToString()},
                {"enable_pure_multi_game", BattleData.data.netSyncData.enablePureMultiGame},
            }
        );
        
        RGGameProcess.Inst.ReSetInfo(true, false);
        BattleData.Reset();

        MultiGameManager.Inst.ContinueGame = false;
        StartCoroutine(UITitle.StartingNextScene("HeroRoom"));
        
        TAUtil.Track("reenter_hall", new Dictionary<string, object> {
            { "hero_index", BattleData.data.playerIndex },
            { "skin_index", BattleData.data.skinIndex },
            { "skill_index", BattleData.data.skillIndex },
        });
    }

    
    private void SwitchFloor() {
        var ctrl = RGGameSceneManager.Inst.controller;
        if (!ctrl) {
            return;
        }

        if (ctrl.mount != null) {
            ctrl.mount.Land();
        }

        bool inSecondHall = RoomObjectManager.IsPositionInSecondHall(ctrl.transform.position);
        var from = inSecondHall ? FloorEnum.Second : FloorEnum.First;
        var to = inSecondHall ? FloorEnum.First : FloorEnum.Second;
        var items = FindObjectsOfType<ItemSwitchFloor>().ToList();
        var item = items.Find(i => i.from == (inSecondHall ? FloorEnum.Second : FloorEnum.First));
        ctrl.transform.position = item.TargetPosition;
        TAUtil.Track("click_switch_floor_button", new Dictionary<string, object>() {
            {"switch_floor_from", from.ToString()},
            {"switch_floor_to", to.ToString()},
            {"choose_hero", false},
        });
        
        RefreshFloor();
        OnBtnSwitchFloorClick?.Invoke();
    }

    public void RefreshFloor() {
        bool inSecondHall = RoomObjectManager.IsPositionInSecondHall(RGGameSceneManager.Inst.controller.transform.position);
        RefreshSwitchFloorButton();
        RefreshSecondHallMask(inSecondHall);
        RefreshTargetPointsSwitchFloor();
    }

    public void RefreshTargetPointsSwitchFloor() {
        bool inSecondHall = RoomObjectManager.IsPositionInSecondHall(RGGameSceneManager.Inst.controller.transform.position);
        var doors = FindObjectsOfType<ItemSwitchFloor>().ToList();
        // 如果有target_point在工作，则根据需要切换显示逻辑
        var targetPoints = FindObjectsOfType<TargetPoint>();
        foreach (var targetPoint in targetPoints) {
            if (inSecondHall) {
                if (RoomObjectManager.IsPositionInSecondHall(targetPoint.transform.position)) {
                    // 角色在二楼，目标点在二楼，取消穿门
                    targetPoint.CancelThroughDoor();
                } else {
                    // 角色在二楼，目标点在一楼，设置穿门
                    var door = doors.Find(i => i.from == FloorEnum.Second);
                    targetPoint.SetThroughDoor(door.transform.position, door.TargetPosition);
                }
            } else {
                if (RoomObjectManager.IsPositionInSecondHall(targetPoint.transform.position)) {
                    // 角色在一楼，目标点在二楼，设置穿门
                    var door = doors.Find(i => i.from == FloorEnum.First);
                    targetPoint.SetThroughDoor(door.transform.position, door.TargetPosition);
                } else {
                    // 角色在一楼，目标点在一楼，取消穿门
                    targetPoint.CancelThroughDoor();
                }
            }
        }
    }

    private void OnCreateTargetPoint(CreateTargetPointEvent e) {
        if (!GameUtil.InHeroRoom) {
            return;
        }

        var ctrl = RGGameSceneManager.Inst.controller;
        if (ctrl == null) {
            return;
        }

        RefreshTargetPointsSwitchFloor();
    }
    
    //获得奖励物品界面
    public static UIWindowShowObject ShowUIWindowObject(
        Sprite objectImg, string objectName, float spriteY = 0, Transform parent = null) {
        var openUIView = UIManager.Inst.OpenUIView<UIWindowShowObject>("window_show_object");
        openUIView.SetUpWindow(objectImg, objectName, 0, true, spriteY, parent);
        return openUIView;
    }
    
    //获得奖励物品界面 闪光版
    public static UIWindowShowObject ShowUIWindowObjectFlash(
        Sprite objectImg, string objectName, float spriteY = 0, Transform parent = null, string itemId = "") {
        var openUIView = UIManager.Inst.OpenUIView<UIWindowShowObject>("window_show_object_flash");
        openUIView.SetUpWindow(objectImg, objectName, 0, true, spriteY, parent);
        return openUIView;
    }
    
    //皮肤碎片合成皮肤界面
    public static UIWindowShowObject ShowUIWindowObjectFragmentToSkin(
        Sprite objectImg, string objectName, float spriteY = 0) {
        var openUIView = UIManager.Inst.OpenUIView<UIWindowShowObject>("window_fragment_merge_skin");
        openUIView.SetUpWindow(objectImg, objectName, 0, false, spriteY);
        return openUIView;
    }
    
    //获得饰品蓝图界面
    public static UIWindowShowObject ShowUIWindowJewelry(Sprite object_img, string object_name) {
        var ui = UIManager.Inst.OpenUIView<UIWindowShowObject>(
            "ModeRift/Prefabs/window_show_jewelry_blueprint.prefab");
        ui.SetUpWindow(object_img, object_name);
        return ui;
    }

    //获得奖励物品界面(有角标)
    public static UIWindowShowObject ShowUIWindowObjectWithFlag(
        Sprite objectImg, Sprite flagImg, Vector3 flagAngle, string objectName) {
        return UIManager.Inst
            .OpenUIView<UIWindowShowObject>("window_show_object")
            .SetUpWindowWithFlag(objectImg, flagImg, flagAngle, objectName);
    }

    //获得奖励物品界面(蓝图)
    public static UIWindowShowObject ShowUIWindowObjectBlueprint(Sprite objectImg, Sprite flagImg, string objectName) {
        return UIManager.Inst
            .OpenUIView<UIWindowShowObject>("window_show_blueprint")
            .SetUpWindowWithFlag(objectImg, flagImg, Vector3.zero, objectName);
    }

    // 获得宠物界面
    public static UIWindowShowObject ShowUIWindowObjectPet(int petIndex) {
        var sprite = RGSaveManager.Inst.pet_list[27].icon;
        var petStr = ScriptLocalization.Get("G_Pet", "Pet");
        var petName = ScriptLocalization.Get("Pet_name_" + petIndex, petIndex.ToString());
        return ShowUIWindowObject(sprite, petStr + "-" + petName);
    }
    
    // 宠物动作，宠物技能解锁界面
    public static UIWindowShowObject ShowUIWindowObjectPetSkill(Sprite objectImg, string objectName) {
        var ui = UIManager.Inst.OpenUIView<UIWindowShowObject>("window_show_object_pet");
        ui.SetUpWindow(objectImg, objectName);
        return ui;
    }

    public static void ShowUIWindowReborn() {
        UIManager.Inst.OpenUIView<UIWindowReborn>("window_reborn");

    }
    public static void ShowUIWindowRebornTwice() {
        UIManager.Inst.OpenUIView<UIWindowReborn>("window_reborn", new object[]{RGGameConst.RebornTwice});
    }
    
    public static void ShowUIWindowRebornDefence() {
        UIManager.Inst.OpenUIView<UIWindowRebornDefence>(
            "ModeDefence/Prefabs/ui/window_reborn_defence.prefab");
    }

    public void BtnRetryClick() {
        StaticBtnRetryClick();
    }

    public static void StaticBtnRetryClick() {
        if (BattleData.data.IsSandbox) {
            GameManager.Inst.ContinueGame();
            if (!BattleData.data.IsSandboxEditing) {
                //不再编辑模式,则返回编辑模式
                BattleData.data.IsSandboxEditing = true;
                SceneManager.LoadScene("Loading");
                return;
            } else {
                //在编辑模式,重置数据返回主界面
                SandboxMaps.data.ReloadEditingMap();
            }
        }

        if (BattleData.data.IsDefenceMode) {
            try {
                ModeDefenceData.Data.SaveData();
                RGMusicManager.Inst.EndBGMFadeMode();
            } catch (Exception e) {
                Debug.LogError(e);
            }
        }

        if (BattleData.data.saveOnExit) {
            try {
                RGGameProcess.Inst.SetUpPlayerInfo(RGGameProcess.SetUpPlayerInfoReason.ExitGameSave);
            } catch (Exception e) {
                Debug.LogError(e);
            }
        }

        TAUtil.Track("click_home_btn", new Dictionary<string, object> {
            {"game_type", BattleData.data.gametype},
            {"enable_pure_multi_game", BattleData.data.netSyncData.enablePureMultiGame},
            
        });

        if (GameUtil.InFirstTutorial()) {
            TAUtil.Track("click_home_btn_tourial");    
        }

        if (NetControllerManager.Inst.playerCount == 0) {
            AssetBundleLoader.Inst.LoadScene(RGGameConst.SCENE_TITLE, ()=>{
                GameManager.Inst.ContinueGame();
            });
        } else if (NetControllerManager.Inst.isServer) {
            AssetBundleLoader.Inst.LoadSceneBundle("Title", () => {
                GameManager.Inst.ContinueGame();
                RGNetWorkManager.singleton.StopHost(NetworkConnection.DisconnectReasonEnum.ServerSelfQuit);
                if (MultiGameManager.Inst.Lan_Game) {
                    MyDiscovery.Inst.StopBroadcast();    
                }
            });
        } else {
            MessageManager.SendForceDisConnect();
            AssetBundleLoader.Inst.LoadSceneBundle("Title", () => {
                GameManager.Inst.ContinueGame();
                MyDiscovery.Inst.StopDiscovering();
                StaticStopClient();
            });
        }
        
        SimpleEventManager.Raise(new StaticBtnRetryClickEvent());
    }

    public static void StaticStopClient() {
        if(LogUtil.IsShowLog){LogUtil.Log("StopClient to Title Scene");}
        NetworkClient.OnDisconnectedEvent?.Invoke();
        RGNetWorkManager.singleton.StopClient(NetworkConnection.DisconnectReasonEnum.ResetStatics);
        RGNetWorkManager.singleton.networkAddress = "";
    }

    public void StopClient() {
        StaticStopClient();
    }

    public void ShowBattleFactors(bool isShow) {
        Transform factors_container = _actAndFactorGroup.Find("battle_factors");
        if (BattleData.data.factors.Count > 0) {
            if (!isShow) {
                factors_container.DestroyAllChildrenWithPrefix("bf_");
            } else {
                UpdateBattleFactorIcon(factors_container);
            }
        }

        if (StaticCustomFactorManager.HasFactor) {
            if (!isShow) {
                factors_container.DestroyAllChildrenWithPrefix("custom_factor");   
            } else {
                AddCustomFactor(factors_container);
            }
        }
    }

    public void RefreshSelfHonoraryTitleList() {
        var localCtrl = NetControllerManager.Inst.localController;
        transform.Find("state_bar/bottom/honorary_title_list").GetComponent<UIHonoraryTitleList>().Refresh((int)localCtrl.netId,localCtrl.netPlayerInfo.honoraryTitleList);
        transform.Find("state_bar/bottom/honorary_title_list").gameObject.SetActive(true);
    }
    
    public void HandleExtra() {
        HandleUISandboxIfNeeded();
        HandleUIDefenceIfNeeded();
    }

    internal UIWindowSandbox windowSandbox;

    private void HandleUISandboxIfNeeded() {
        if (BattleData.data.IsSandboxEditing) {
            windowSandbox = Instantiate<GameObject>(ResourcesUtil.Load<GameObject>(
                "ModeSandbox/Prefabs/window_sandbox.prefab"), transform).GetComponent<UIWindowSandbox>();
            windowSandbox.name = "window_sandbox";
            //windowSandbox.transform.SetAsFirstSibling();
            anim.SetBool("is_sandbox", true);
            infoBar.gameObject.SetActive(false);
            stateBar.gameObject.SetActive(false);
        }
    }

    public void ShowUiSandbox(bool isShow) {
        if (BattleData.data.IsSandbox) {
            if (isShow) {
                windowSandbox.ShowWindow(isShow);
            }
        }
    }

    internal UIWindowDefence windowDefence;
    internal Transform defence_canvas;
    internal Transform mask_canvas;
    internal UIDefence ui_defence;
    

    public void HandleUIDefenceIfNeeded() {
        if (ui_defence) return;
        if (BattleData.data.gameMode == emGameMode.Defence) {
            defence_canvas = Instantiate(DefenceModeAssets.Load<GameObject>("DefenceCanvas")).transform;
            defence_canvas.name = "DefenceCanvas";
            defence_canvas.GetComponent<Canvas>().worldCamera = Camera.main;
            mask_canvas = Instantiate(DefenceModeAssets.Load<GameObject>("MaskCanvas")).transform;
            mask_canvas.name = "MaskCanvas";
            mask_canvas.GetComponent<Canvas>().sortingOrder = 10;
            ui_defence = defence_canvas.transform.Find("ui_defence").GetComponent<UIDefence>();
            ui_defence.name = "ui_defence";
        }
    }

    /// <summary>
    /// /// 展示波次信息
    /// </summary>
    public void ShowSignpostZombie() {
        windowDefence.ShowSignpostZombie();
    }

    //快门效果
    public void CanvasShutter() {
        anim.SetTrigger("shutter_back");
    }

    /// <summary>
    /// Android的OnConfiguration调用. 适配折叠屏用
    /// </summary>
#if UNITY_EDITOR
    [Sirenix.OdinInspector.Button(Name = "测试折叠屏翻转回调")]
#endif
    public static void CameraSizeReconfiguration() {
        // GameUtil.EnlargeCamera();
    }

    public void SetCameraSize(float size) {
        var cameraAnim = Camera.main.GetComponent<Animator>();
        if (null != cameraAnim) {
            cameraAnim.enabled = false;
        }

        Camera.main.orthographicSize = size;
    }

    public void BtnKtPlayClick() {
    }

    public void OnClick_RealNameInfo() {
    }

    /// <summary>
    /// 到主机到延迟
    /// 已转移到 UIShowCurrencyWithLatencyWidget
    /// </summary>
    /// <param name="latency">毫秒</param>
    public void UpdateNetInfo(int latency) {
        var showColor = RGGameConst.NetInfoColorGreen;
        var netSp = netSps[0];

        if (latency >= 100 && latency < 300) {
            showColor = RGGameConst.NetInfoColorYellow;
            netSp = netSps[1];
        } else if (latency >= 300) {
            showColor = RGGameConst.NetInfoColorRed;
            netSp = netSps[2];
        }
        
        if (netInfoText) {
            netInfoText.color = showColor;
            latency = Math.Min(latency, 9999);
            netInfoText.text = latency + "ms";
        }

        if (netInfoImg) {
            netInfoImg.sprite = netSp;
        }
    }
    
    public void SendEvent(int i) {
        SimpleEventManager.Raise(SeasonGuideCallback.UseCache(i));

        const int clickSlot = 1;
        const int equipChip = 2;
        switch (i) {
            case clickSlot: {
                var seasonEquipmentsController = Inst.GetController<SeasonEquipmentsController>();
                seasonEquipmentsController.DispatchMessage(new Message {
                    Command = SeasonEquipmentsCommand.ClickSlot,
                    ExtraParams = 0
                });
                break;
            }
            case equipChip: {
                var seasonEquipmentsController = Inst.GetController<SeasonEquipmentsController>();
                seasonEquipmentsController.DispatchMessage(new Message {
                    Command = SeasonEquipmentsCommand.EquipChipInTutorial
                });
                break;
            }
            case 100: {
                var ui = UIManager.Inst.OpenUIView<UIInventory>(
                    ResourcesUtil.Load<GameObject>("ModeSeason/ComboGun/Prefabs/UI/ui_inventory.prefab"));
                RGMusicManager.GetInstance().PlayEffect(8);
                ui.transform.Find("finger").gameObject.SetActive(true);
                break;
            }
        }
    }

    private void OnSeasonGuideCallback(SeasonGuideCallback e) {
        if (e.index == 0) {
            OnSeasonGearButtonClicked();
        }
    }
    
    private void OnHideHeroChoseCallback(HideHeroChoseEvent e) {
        // InitRedPoints();
    }

    private void OnSetSeasonCallback(SetSeasonEvent e) {
        // InitRedPoints();
    }

    private void OnUpdatePressureLv(UpgradeWeaponPressureLvEvent e) {
        var controller = RGGameSceneManager.GetInstance().controller;
        if (null == controller) {
            return;
        }

        var frontWeapon = controller.hand.front_weapon;
        if (null == frontWeapon) {
            return;
        }

        ShowWeaponBtnIconInfo(frontWeapon);
    }

    private GameObject emoticonPanel;

    public void ShowEmoticonPanel() {
        if (null == emoticonPanel) {
            return;
        }

        var panel = emoticonPanel.GetComponent<UIPanelEmoticon>();
        panel.OnEmoticonBtnClick();
    }

    private bool _emoticonButtonEnable = true;

    public bool EmoticonButtonEnable {
        get {
            return _emoticonButtonEnable;
        }
        set {
            _emoticonButtonEnable = value;
            if (emotion_btn_rect != null) {
                emotion_btn_rect.gameObject.SetActive(_emoticonButtonEnable);
            }
        }
    }

    public void ShowEmoticonBtn(bool show = true) {
        if (!EmoticonButtonEnable || null == emotion_btn_rect) {
            return;
        }

        emotion_btn_rect.gameObject.SetActive(show);
    }

    private UIPanelTeamInfo teamInfoPanel;
    public UIPanelTeamInfo GetTeamInfoPanel => teamInfoPanel;

    [Sirenix.OdinInspector.Button]
    public void ShowTeamInfoPanel() {
        GameObject teamInfoPanelGo;
        if (BattleData.data.gameMode == emGameMode.ComboGun && GameUtil.InGameScene) {
            teamInfoPanelGo = Instantiate(ResourcesUtil.Load<GameObject>(
                "RGPrefab/UI/TeamInfo/team_info_combo_gun.prefab"), transform);
        } else {
            teamInfoPanelGo = Instantiate(ResourcesUtil.Load<GameObject>(
                "RGPrefab/UI/TeamInfo/team_info.prefab"), transform);
        }
        
        var joyConTransform = transform.Find("control");
        if (joyConTransform) {
            teamInfoPanelGo.transform.SetSiblingIndex(joyConTransform.GetSiblingIndex() + 1);
        } else {
            teamInfoPanelGo.transform.SetAsFirstSibling();
        }

        teamInfoPanel = teamInfoPanelGo.GetComponent<UIPanelTeamInfo>();
        teamInfoPanel.Init();
        teamInfoPanel.Show();
    }

    void OnShowPauseWindow(EventBase e) {
        // return;
        var eParam = e as ShowPauseWindow;
        if (GameUtil.IsDefenceMode() && ui_defence) {
            ui_defence.animator.SetBool("show", !eParam.show);
        }

        var finger = transform.Find("info_bar/btn_pause/finger");
        if (finger) {
            Destroy(finger.gameObject);
        }

        if (null == teamInfoPanel) {
            return;
        }

        if (eParam.show) {
            teamInfoPanel.Hide();
        } else {
            teamInfoPanel.Show();
        }
    }

    void OnCoinChange(EventBase e) {
        this.UpdateCoin();
    }

    public void OnBtnRoomIdClick() {
#if ENABLE_REMOTE_MULTI_GAME
        GameUtil.CopyToClipboard(WebRTCInstance.Inst.RoomId);
#endif
        ShowTempMessage("multi_remote/invitecode_copy_succ_tips", 5, true);
    }


    private UINextLevel uiNextLevel;
    private static readonly int AnimShowItemInfo = Animator.StringToHash("show_item_info");

    public void ShowUINextLevel(bool isSpecial, string branchIdx, bool isUserCtrl) {
        if (null == uiNextLevel) {
            uiNextLevel = Instantiate(ResourcesUtil.Load<GameObject>(
                "RGPrefab/UI/NextLevel/next_level.prefab"), transform).GetComponent<UINextLevel>();
            uiNextLevel.transform.SetAsFirstSibling();
        }

        uiNextLevel.Show(isSpecial, branchIdx, isUserCtrl);
    }

    public void HideUINextLevel() {
        if (null == uiNextLevel) {
            return;
        }

        if (!uiNextLevel.gameObject.activeSelf) {
            return;
        }

        uiNextLevel.Hide();
    }

    public static void ShowLoading() {
        var uiLoading = GameObject.Find("Canvas/ui_loading");
        if (uiLoading) {
            return;
        }

        uiLoading = Instantiate(
            ResourcesUtil.Load<GameObject>("RGPrefab/UI/ui_loading.prefab"),
            GetInstance().transform,
            false
        );
        uiLoading.name = "ui_loading";
    }

    public static void HideLoading() {
        var uiLoading = GameObject.Find("Canvas/ui_loading");
        if (uiLoading) {
            Destroy(uiLoading);
        }
    }

    public void ShowUI(bool b){
        try{
            transform.Find("control")?.gameObject.SetActive(b);
            transform.Find("state_bar")?.gameObject.SetActive(b);
            transform.Find("map_info_root")?.gameObject.SetActive(b);
            transform.Find("info_bar")?.gameObject.SetActive(b);
        }
        catch{}
    }

    public Transform GetCurrencyIcon(ShowCurrencyType currencyType) {
        return _currencyGroupWidget.GetCurrencyIcon(currencyType);
    }
    
    public void UpdateCurrencyWithAnim(ShowCurrencyType currencyType, int addCount) {
        _currencyGroupWidget.SetTextWithAnim(currencyType, addCount.ToString());
    }

    /// <summary>
    /// 右下角海报展示/关闭调用
    /// </summary>
    /// <param name="show"></param>
    public void ShowPosterView(bool show) {
        if (_uiposterTR != null) {
            _uiposterTR.GetComponent<UIPosterScrollView>().ShowView(show);
        }
    }
}
