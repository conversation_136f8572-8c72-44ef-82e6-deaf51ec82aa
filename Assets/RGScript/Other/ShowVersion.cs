using CoreKit.Config;
using Generated.ChillyRoomSdkClient;
using RGScript.Other;
using RGScript.Other.NewSDK;
using RGScript.Util.LifeCycle;
using UnityEngine;
using UnityEngine.UI;

public class ShowVersion : MonoBehaviour {
    private Text _versionText;
    void Start() {
        _versionText = GetComponent<Text>();
        SimpleEventManager.AddEventListener<DoPatchSuccessEvent>(UpdateVersionText);
        SimpleEventManager.AddEventListener<CodePatchFinishEvent>(OnCodePatchFinish);
        LifeCycleManager.Instance.OnAccountLogin += OnAccountLogined;
        UpdateVersionText(null);
    }

    private void OnCodePatchFinish(CodePatchFinishEvent e) {
        UpdateVersionText(null);
    }

    private void OnDestroy() {
        SimpleEventManager.RemoveListener<DoPatchSuccessEvent>(UpdateVersionText);
        SimpleEventManager.RemoveListener<CodePatchFinishEvent>(OnCodePatchFinish);
        LifeCycleManager.Instance.OnAccountLogin -= OnAccountLogined;
    }

    private void OnAccountLogined(string obj) {
        UpdateVersionText(null);
    }

    private void UpdateVersionText(DoPatchSuccessEvent e) {
        var text = string.Empty;
        var chillyUid = LifeCycleManager.Instance.GetChillyUid;
        if (!string.IsNullOrEmpty(chillyUid)) {
            text = $"UID:{chillyUid} " ;
        }

        if (NewSDKManager.IsLogined() && ChillyRoomService.GetSdk().Accounts.StateManager.State.Session.LoginBy ==
            UniversalSdkType.Guest) {
            text = $"G UID:{chillyUid} " ;
        }
        
        if (ChannelConfig.IsShangYou) {
            var shangYouId = ChannelUtil.GetShangYouId();
            if (!string.IsNullOrEmpty(shangYouId)) {
                text += $"SID:{shangYouId} ";
            }
        }
        
#if UNITY_EDITOR
        if (UnityEditor.EditorPrefs.GetBool("HideLogin", true)) {
            text = $"" ;
        }
#endif

        text += GameUtil.GetShowVersion();
        _versionText.text = text;
    }
}