using Activities.Dream.Scripts;
using RGScript.Data;
using SoulKnight.Runtime.Config2Code.Config;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// 创建地窖的走道
/// </summary>
public class CellerAisleCreator : MonoBehaviour {
    public enum emExhibite {
        Weapon,
        Enemy,
        Charactor,
        Achievement,
        Plant,
        Npc,
        SeasonPrize,
    }
    public emExhibite exhibiteType;
    public int directSign = 1;
    public GameObject barrier;//路障
    public Transform[] fixPositionTrans;
    CellerMuseum museum => GetComponentInParent<CellerMuseum>();
    GameObject booth => museum.booth;
    GameObject boothLarge => museum.boothLarge;
    GameObject seasonPrize => museum.seasonPrize;

    static float offsetHeight = 0.875f;
    static float lengthNormal = 3f;
    static float lengthLarge = 3.5f;

    float length;
    bool isLastLarge;//上一个是否是大号展框
    bool createDone;//展板创建完毕
    int fixPositionLength;//因为插入而需要移动的位置
    
    public static readonly Dictionary<(emExhibite type, string key), GameObject> ExhibitMap = new();
    
    private void RegisterExhibit(emExhibite type, string key, GameObject boothObj)
    {
        ExhibitMap[(type, key)] = boothObj;
    }
    
    public static bool TryGetExhibit(emExhibite type, string key, out GameObject booth)
        => ExhibitMap.TryGetValue((type, key), out booth);

    public static bool TryGetPosition(emExhibite type, string key, out Vector3 pos)
    {
        if (TryGetExhibit(type, key, out var ga))
        {
            pos = ga.transform.position;
            return true;
        }
        pos = default;
        return false;
    }
    
    private void Awake() {
        if (!barrier) {
            var tran = transform.Find("barricade");
            if (null != tran) {
                barrier = tran.gameObject;
            }
        }
        MoveFixPosition();
    }

    public void MoveFixPosition() {
        if (exhibiteType == emExhibite.SeasonPrize) {
            fixPositionLength = ((int)Season.Count - 1) * 3;
            floorOffset = new Vector3(3 + fixPositionLength, 0f);
        }
        //移动需要改变位置的Transform
        if (null != fixPositionTrans && fixPositionTrans.Length > 0) {
            foreach (var tran in fixPositionTrans) {
                if (null != tran) {
                    tran.position = tran.position + new Vector3(fixPositionLength, 0, 0);
                }
            }
        }
    }

    void Start() {
        StartCoroutine(CreateAisle());
    }

    IEnumerator CreateAisle() {
        StartCoroutine(CreateFloors());

        switch (exhibiteType) {
            case emExhibite.Weapon:
                yield return CreateWeapons();
                break;
            case emExhibite.Enemy:
                yield return CreateEnemy();
                break;
            case emExhibite.Charactor:
                yield return CreateCharacter();
                break;
            case emExhibite.Achievement:
                yield return CreateAchievement();
                break;
            case emExhibite.Plant:
                yield return CreatePlant();
                break;
            case emExhibite.Npc:
                yield return CreateNpc();
                break;
            case emExhibite.SeasonPrize:
                yield return CreateSeasonPrize();
                break;
            default:
                break;
        }
        createDone = true;

    }

    IEnumerator CreateWeapons() {
        //创建展框
        foreach (var weaponName in GetExhibiteWeaponNames()) {
            if (weaponName == RGGameConst.WEAPON_NULL) {
                var boothObj = CreateBooth(true);
                boothObj.AddComponent<WeaponExhibition>().forgeItem = null;
                boothObj.SetActive(true);
            } else if (WeaponInfo.info.name2Weapon.TryGetValue(weaponName, out var info) && info != null) {
                if (DataMgr.UnlockConditionData.IsWeaponLockedByConfig(weaponName)) {
                    continue;
                }

                GameObject weaponPrefab = ResourcesUtil.LoadWeapon(info.name);
                if (weaponPrefab) {
                    bool isLarge = info.level >= 4;
                    var boothObj = CreateBooth(isLarge);
                    var exh = boothObj.AddComponent<WeaponExhibition>();
                    exh.gameObject.name = info.name;
                    exh.forgeItem = weaponPrefab.GetComponent<IForgeItem>();
                    RegisterExhibit(emExhibite.Weapon, weaponName, boothObj);
                    boothObj.SetActive(true);
                } else {
                    Debug.Log($"Weapon Prefab NotFount:{info.name}");
                }
                yield return null;
            }
        }
    }

    private static List<emWeaponType> weaponOrderList = new List<emWeaponType> {
        emWeaponType.Default,
        emWeaponType.Mythic,
        emWeaponType.Gun,
        emWeaponType.ShotGun,
        emWeaponType.Sword,
        emWeaponType.Axe,
        emWeaponType.Spear,
        emWeaponType.Bow,
        emWeaponType.Staff,
        emWeaponType.Laser,
        emWeaponType.ShortLaser,
        emWeaponType.Hold,
        emWeaponType.Strange,
        emWeaponType.Rocket,
        emWeaponType.Throw,
        emWeaponType.Pistol,
        emWeaponType.Rifle,
        emWeaponType.Token,
    };
    static List<string> weaponsNames;
    /// <summary>
    /// 获取展示的武器名
    /// </summary>
    public static List<string> GetExhibiteWeaponNames(bool forCountingPurpose = false, bool clearCache = false) {
        if (!clearCache && weaponsNames != null) {
            return weaponsNames;
        }
        weaponsNames = new List<string>();
        //武器顺序: 1.角色武器
        weaponsNames.Add((DataUtil.GetHeroLevel(emHero.Knight) < 7) ? "weapon_000" : "weapon_000x");
        for (emHero i = emHero.Ranger; i < emHero.Count; i++) {
            weaponsNames.Add($"weapon_init_{i.ToString().ToLower()}" + (DataUtil.GetHeroLevel(i) < 7 ? "" : "x"));
        }
        weaponsNames.Add(RGGameConst.WEAPON_NULL);
        foreach (emWeaponType weaponType in weaponOrderList) {
            bool containsWeaponType = WeaponInfo.info.type2Weapon.ContainsKey(weaponType);
            if (!containsWeaponType) {
                continue;
            }

            var pair = WeaponInfo.info.type2Weapon.FirstOrDefault(x => x.Key == weaponType);

            if (null == pair.Value) {
                continue;
            }
            
            if (forCountingPurpose) {
                foreach (var weapon in pair.Value) {
                    if (!weapon.name.EndsWith("x") &&
                        !weapon.tags.Contains(emWeaponTag.CellerInvisible) &&
                        !weapon.tags.Contains(emWeaponTag.InitWeapon)) {
                        weaponsNames.Add(weapon.name);
                    }
                }
            } else {
                List<WeaponInfoRow> tempWeaponLarge = new List<WeaponInfoRow>();
                List<WeaponInfoRow> tempWeapon = new List<WeaponInfoRow>();
                foreach (var weapon in pair.Value) {
                    if (!weapon.name.EndsWith("x") &&
                        !weapon.tags.Contains(emWeaponTag.CellerInvisible) &&
                        !weapon.tags.Contains(emWeaponTag.InitWeapon)) {
                        if (weapon.level >= 4) {
                            tempWeaponLarge.Add(weapon);
                        } else {
                            tempWeapon.Add(weapon);
                        }
                    }
                }
                //对展框进行排序
                tempWeapon.Sort((a, b) => {
                    if (a.level == b.level) {
                        return string.Compare(a.name, b.name);
                    } else {
                        return a.level - b.level;
                    }
                });
                tempWeaponLarge.Sort((a, b) => {
                    if (a.level == b.level) {
                        return string.Compare(a.name, b.name);
                    } else {
                        return a.level - b.level;
                    }
                });
                int tempIndex = 0;
                int tempIndexLarge = 0;
                float gap = tempWeaponLarge.Count > 0 ?
                    (tempWeapon.Count + tempWeaponLarge.Count) / (float)tempWeaponLarge.Count : 1024;
                for (int i = 1; i <= tempWeapon.Count + tempWeaponLarge.Count; i++) {
                    try {
                        bool addLarge = (tempWeaponLarge.Count > 0) && (i % gap < 1);
                        if (tempIndexLarge == tempWeaponLarge.Count - 1) {
                            addLarge = i == tempWeapon.Count + tempWeaponLarge.Count;
                        }
                        if (addLarge) {
                            weaponsNames.Add(tempWeaponLarge[tempIndexLarge++].name);
                        } else {
                            weaponsNames.Add(tempWeapon[tempIndex++].name);
                        }
                    } catch (System.Exception e) {
                        Debug.LogError(e);
                        BuglyUtil.ReportException("CreateCellar", e);
                    }
                }
            }
        }
        
        return weaponsNames;
    }

    public static int GetUnlockedWeaponCount(StatisticData data) {
        int unlock_count = 0;
        foreach (var w in CellerAisleCreator.GetExhibiteWeaponNames(true)) {
            if (w != RGGameConst.WEAPON_NULL) {
                string weaponName = RGWeapon.WeaponNameNormalized(w);
                if (data.GetObtainTime(weaponName) != 0) {
                    unlock_count++;
                }
            }
        }
        return unlock_count;
    }

    IEnumerator CreateEnemy() {
        List<emLevelScene> levelScenes = EnumExtension.GetEnums<emLevelScene>();

        foreach (var scene in levelScenes) {
            if (!EnemyInfos.info.scene2Enemy.TryGetValue(scene, out var infos)) {
                continue;
            }

            foreach (var info in infos) {
                bool isInCorrectScene = true;
                // ReSharper disable once ForeachCanBeConvertedToQueryUsingAnotherGetEnumerator
                foreach (var data in DataMgr.ConfigData.Tables.TbLevelEnemy.DataList) {
                    if (data.EnemyId != info.enemyId) {
                        continue;
                    }

                    if (data.Handbook.LevelScene == scene.ToString()) {
                        break;
                    }

                    isInCorrectScene = false;
                    break;
                }

                if (!isInCorrectScene) {
                    continue;
                }
                
                var enemyConfig = EnemyTable.GetEnemyData(info.enemyId);
                var sprite = enemyConfig?.CellarSprite.GetSprite();
                if (!sprite || enemyConfig.SummonedEnemy) {
                    continue;
                }

                bool isLarge = info.rank >= 2;
                var boothObj = CreateBooth(isLarge);
                var exh = boothObj.AddComponent<ObjectExhibition>();
                exh.SetEnemyInfo(info, isLarge);
                RegisterExhibit(emExhibite.Enemy, info.enemyId, boothObj);
                boothObj.SetActive(true);
                yield return null;
            }
        }
    }
    
    IEnumerator CreateCharacter() {
        //角色展板
        for (emHero hero = 0; hero < emHero.Count; hero++) {
            var skinList = RGSaveManager.Inst.char_list[(int)hero].skin_list;
            for (int skinIndex = 0; skinIndex < skinList.Length; skinIndex++) {
                if (skinList[skinIndex] == -3) { continue; }
                if (skinList[skinIndex] == -8) { continue; }
                if (ActivityDreamManager.NeedHideSkinInCeller(hero, skinIndex)) { continue; }
                if (DataMgr.UnlockConditionData.IsHeroSkinLockedByConfig(hero, skinIndex)) { continue; }

                bool isLarge = skinIndex == 0;
                var boothObj = CreateBooth(isLarge);
                var exh = boothObj.AddComponent<SkinExhibition>();
                exh.hero = (int)hero;
                exh.skin_index = skinIndex;
                exh.isLarge = isLarge;
                RegisterExhibit(emExhibite.Charactor, $"{hero}{skinIndex}", boothObj);
                boothObj.SetActive(true);
                var hasMasterSkin = TroopGeneralInfo.info.masterSkinIndex.TryGetValue(hero, out var index) &&
                                    skinIndex == index;
                if (skinIndex == 0 || hasMasterSkin) {
                    string suffix = skinIndex != 0 ? "_ex" : "";
                    string countKey = $"hire_{hero}{suffix}";
                    if (hero == emHero.Engineer && skinIndex != 0) {
                        if (LogUtil.IsShowLog) { LogUtil.Log("a"); }
                    }

                    bool hasGot = StatisticData.data.GetEventCount(countKey) > 0;
                    if (hasGot) {
                        GameObject troopDecorate = new GameObject("troop_mark");
                        troopDecorate.transform.SetParent(boothObj.transform);
                        troopDecorate.transform.localPosition = Vector3.up * (isLarge ? 2.75f : 1.75f);
                        var renderer = troopDecorate.AddComponent<SpriteRenderer>();
                        string normalKey = $"pass_{hero}_{emPassGameLevel.Normal.ToString().ToLower()}{suffix}";
                        string badassKey = $"pass_{hero}_{emPassGameLevel.Badass.ToString().ToLower()}{suffix}";
                        int normalCount = StatisticData.data.GetEventCount(normalKey);
                        int badassCount = StatisticData.data.GetEventCount(badassKey);
                        int spriteIndex = badassCount >= 1 ? 2 : normalCount >= 1 ? 1 : 0;
                        renderer.sprite = TroopGeneralInfo.info.cellerSprites[spriteIndex];
                        renderer.sortingLayerName = "Wall";
                        renderer.sortingOrder = 5;
                    }
                }

                yield return null;
            }
        }
        //宠物展板
        for (emPet pet = 0; pet < emPet.Count; pet++) {
            var petData = RGSaveManager.Inst.pet_list[(int)pet];
            if ((petData.unlock_gem == -3 && !petData.unlock) ||
                petData.hideInCeller ||
                pet.isHidePet()) {
                continue;
            }
            
            if (ActivityDreamManager.NeedHidePetInCeller(pet))
                continue;

            bool isLarge = (int)pet == 0;
            var boothObj = CreateBooth(isLarge);
            var exh = boothObj.AddComponent<SkinExhibition>();
            exh.hero = (int)emHero.Count;
            exh.skin_index = (int)pet;
            exh.isLarge = isLarge;
            boothObj.SetActive(true);
            yield return null;
        }
        
        //亲卫展板
        var followers = DataMgr.CGFollowerData.GetAllFollowers();
        for (var i = 0; i < followers.Count; i++) {
            var followerData = followers[i];
            bool isLarge = true;
            var boothObj = CreateBooth(isLarge);
            var exh = boothObj.AddComponent<SkinExhibition>();
            exh.hero = (int)emPet.Count + (int)emHero.Count;
            exh.skin_index = 0;
            exh.followerId = followerData.followerConfigId;
            exh.isLarge = isLarge;
            boothObj.SetActive(true);
            yield return null;
        }
    }

    IEnumerator CreateAchievement() {
        for (int i = 0; i < AchieveInfos.info.achievement_Infos.Count; i++) {
            var info = AchieveInfos.info.achievement_Infos[i];
            bool isLarge = i > 0 && i % 4 == 0;
            var boothObj = CreateBooth(isLarge);
            var exh = boothObj.AddComponent<AchievementExhibition>();
            exh.SetAchievementInfo(info, isLarge);
            RegisterExhibit(emExhibite.Achievement, info.name, boothObj);
            boothObj.SetActive(true);
            yield return null;
        }
    }

    public IEnumerator CreatePlant() {
        for (int i = 0; i < ItemInfo.Info.Seeds.Count; i++) {
            var info = ItemInfo.Info.Seeds[i];
            bool isLarge = (i + 1) % 4 == 0;
            var boothObj = CreateBooth(isLarge);
            var exh = boothObj.AddComponent<PlantExhibition>();
            exh.SetSeedInfo(info, isLarge);
            RegisterExhibit(emExhibite.Plant, info.Name, boothObj);
            boothObj.SetActive(true);
            yield return null;
        }
    }

    IEnumerator CreateNpc() {
        for (int i = 0; i < NPCInfoTable.info.npcs.Count; i++) {
            var info = NPCInfoTable.info.npcs[i];
            bool isLarge = (i + 1) % 4 == 0;
            var boothObj = CreateBooth(isLarge);
            var exh = boothObj.AddComponent<NpcExhibition>();
            exh.SetNpcInfo(info, isLarge);
            RegisterExhibit(emExhibite.Npc, info.name, boothObj);
            boothObj.SetActive(true);
            yield return null;
        }
    }

    IEnumerator CreateSeasonPrize() {
        //更新一次赛季模式奖杯条件
        TroopAchievement.CheckSeasonPrize();
        ArtifactsAchievement.CheckSeasonPrize();
        for (Season season = 0; season < Season.Count; season++) {
            // bool isLarge = (season + 1) % 4 == 0;//可以考虑把当前赛季的放大
            var prizeGa = createSeasonPrizeGa();
            var exh = prizeGa.AddComponent<SeasonPrizeExhibition>();
            exh.SetSeasonPrizeInfo(season);
            RegisterExhibit(emExhibite.SeasonPrize, season.ToString(), prizeGa);
            prizeGa.SetActive(true);
            yield return null;
        }
    }

    /// <summary>
    /// 创建展板
    /// </summary>
    /// <param name="isLarge">展板是否是大号的</param>
    /// <returns></returns>
    GameObject CreateBooth(bool isLarge) {
        //var deltaLength = (isLarge || isLastLarge) ? lengthLarge : lengthNormal;
        var deltaLength = lengthNormal + (isLarge ? 0.5f : 0f) + (isLastLarge ? 0.5f : 0f) + (isLarge && isLastLarge ? 0.5f : 0f);
        length += deltaLength;
        var position = new Vector3(length * directSign, offsetHeight);
        var go = Instantiate<GameObject>(isLarge ? boothLarge : booth, transform);
        go.transform.localPosition = position;
        isLastLarge = isLarge;
        return go;
    }

    /// <summary>
    /// 创建奖杯
    /// </summary>
    /// <param name="isLarge">展板是否是大号的</param>
    /// <returns></returns>
    GameObject createSeasonPrizeGa() {
        length += lengthNormal;
        var position = new Vector3(length * directSign, offsetHeight);
        var go = Instantiate<GameObject>(seasonPrize, transform);
        go.transform.localPosition = position;
        return go;
    }


    Vector3 floorOffset = new Vector3(3, -2);
    private WaitForEndOfFrame wait = new WaitForEndOfFrame();
    /// <summary>
    /// 创建地板
    /// </summary>
    IEnumerator CreateFloors() {
        float extendLength = 24f;//地板延伸的长度
        // var wait = new WaitForSeconds(0.5f);
        int floorLength = 0;
        int createCount = 0;
        int createFloorEnableColliderCount = 12;//unity2020 tile的行为更改了，创建n次地板触发一次collider重建
        while (!createDone || floorLength < length + extendLength) {
            for (int i = floorLength; i < length + extendLength; i += 3) {
                museum.CreateFloor(new Vector3(transform.position.x + (i + floorOffset.x) * directSign,
                    transform.position.y + floorOffset.y));
                floorLength = i + 3;
                createCount++;
                if (createCount > createFloorEnableColliderCount) {
                    createCount = 0;
                    yield return wait;
                }
            } 

            if (barrier) {
                barrier.transform.position =
                    new Vector3(transform.position.x + (length + (isLastLarge ? 4 : 3)) * directSign,
                        barrier.transform.position.y);
            }
            
            yield return wait;
        }

        yield return wait;
    }
}
