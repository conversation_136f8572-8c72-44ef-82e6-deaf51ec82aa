using ChillyRoom.Payment.V1;
using ChillyRoom.RealName;
using ChillyRoom.Services.Accounts.Features;
using ChillyRoom.Services.Accounts.Utils;
using ChillyRoom.Services.Core;
using ChillyRoom.Services.Soulknight;
using CoreKit.Agent;
using CoreKit.Config;
using Generated.ChillyRoomSdkClient;
using I2.Loc;
using Newtonsoft.Json;
using RGScript.Config.Manager;
using RGScript.Data;
using RGScript.Other.LegencyAdapter;
using RGScript.Util.ForcePatch;
using RGScript.Util.LifeCycle;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using UniRx;
using UnityEngine;
using Util.TAUtil;
using Utils.CommonUtils;
using Order = ChillyRoom.Payment.V1.Order;
using OrderStatus = ChillyRoom.Payment.V1.OrderStatus;

namespace RGScript.Other.NewSDK {
    /// <summary>
    /// 支付模块
    /// </summary>
    public partial class NewSDKManager {
        // sku熔断机制
        private bool _skuFetcherRunning = false;
        private int skuRetryCount = 0;
        private const int skuMaxRetryCount = 5;
        private List<SKU> _skus = new List<SKU>();
        private Action<bool> onPayCompleted;
        private IObservable<IResult<Order, Failure<PurchaseErrorKind>>> _payListener;

        public SKU[] Skus => _skus?.ToArray();
        // abtest的sku不再警告
        private static readonly List<string> ABTestSkus = new List<string>() {
            "new_pkg_25",
        };

        private CommonGameConfig _config;
        CommonGameConfig Config {
            get {
                if (_config == null) {
                    _config = ConfigManager.GetCurrectUseConfig<CommonGameConfig>();
                    LogUtil.Log($"[CommonConfig]获取配置路径 : {ConfigManager.LocalChannelConfigPath}");
                    LogUtil.Log($"[CommonConfig]获取配置路径 : {ConfigManager.LocalBranchConfigPath}");
                    LogUtil.Log($"[CommonConfig]获取配置 : {JsonConvert.SerializeObject(_config)}");
                    return _config;
                }

                return _config;
            }
        }

        /// <summary>
        /// 老订单（可能为空）
        /// </summary>
        public static List<Payment.LegacyOrder> LegacyOrders { get; private set; }
        /// <summary>
        /// 迁移过的订单（可能为空）
        /// </summary>
        public static List<ChillyRoom.Services.Soulknight.Order> MigratedOrders { get; private set; }
        #region SKU

        /// <summary>
        /// 依赖于sku到手后才能执行的逻辑
        /// </summary>
        private void AfterFetchedSKUS() {
            RestoreAllOrders(null);
            if (_cacheDict != null) {
                _cacheDict.Clear();
            }
            CheckSKUMatches();
        }

        /// <summary>
        /// 项目配置的sku与接口数据校对，避免缺漏
        /// </summary>
        private void CheckSKUMatches() {
            if (!LogUtil.ShowDebugLog) {
                return;
            }

            if (_skus == null || _skus.Count == 0) {
                LogUtil.LogError("CheckSKUMatches sku数据为空，请确认是否已配置");
                return;
            }

            foreach (var pair in SkuConfigManager.products) {
                var productKey = pair.Key;
                if (ABTestSkus.Contains(productKey)) {
                    continue;
                }
                
                var sku = GetSKUByKey(productKey);
                if (sku == null) {
                    LogUtil.LogError("CheckSKUMatches product:" + productKey + " sku not found!!!");
                }
            }
        }

        private void InitSKUS() {
            // TapTap等chilly登录体系可以editor模式拉取skus
            if (Application.isEditor && !IsLoginedByChilly()) return;

            StartCoroutine(ChannelConfig.IsAllIOS ? DoIOSFetchSKU() : DoFetchSKUTimer());
        }

        private IEnumerator DoFetchSKUTimer() {
            skuRetryCount = 0;
            _skuFetcherRunning = true;
            while (skuRetryCount < skuMaxRetryCount) {
                if (_skus.Count == 0) {
                    skuRetryCount++;
                    FetchSKUS();
                    yield return new WaitForSecondsRealtime(5f * skuRetryCount);
                } else {
                    yield break;
                }
            }
            _skuFetcherRunning = false;
        }

        private IEnumerator DoIOSFetchSKU() {
            LogUtil.Log("[DoIOSFetchSKU]Fetch SKU Start");
            if (!IsLogined()) {
                LogUtil.Log("[DoIOSFetchSKU]IsLogined false");
                yield break;
            }

            var task = FetchSKUS();
            yield return new WaitUntil(() => task.IsCompleted);
            if (task.Result) {
                LogUtil.Log("[DoIOSFetchSKU]Fetch SKU Success");
                yield break;
            }

            LogUtil.Log("[DoIOSFetchSKU]Fetch SKU Failed, keep trying...");
            StartCoroutine(DoIOSFetchSKU());
        }

        /// <summary>
        /// 进入客厅，如果sku还没好，且skuFetchTimer已经执行完。则再尝试拉取一次，仅仅一次!
        /// </summary>
        private void TryFetchSkuAgain() {
            if (Application.isEditor && !IsLoginedByChilly()) return;
            if (_skus.Count > 0) return;
            if (_skuFetcherRunning) return;
            if (ChannelConfig.IsAllIOS) return;// iOS生命周期内会一直拉取
            FetchSKUS();
        }

#if UNITY_IOS
        private const string IOSProductIdPrefix = "ios.";
#endif

        // sku正式数据
        // Id:c058e18b-d755-4863-8ab8-4a0990525514,Type:Consumable,Name:角色 - 狼人,Desc:角色 - 狼人,Price:1000,Loc_price:¥10.00,Item_config:{"count": 1, "productId": 9, "propId": "werewolf"}
        private SKU GetSKUByKey(string productKey) {
#if UNITY_EDITOR
            if (UnityEditor.EditorPrefs.GetBool("HideLogin", true)) {
                return null;
            }
#endif
            
            if (_skus.Count == 0) {
                Debug.LogError("GetSKU: InitSKUS error: _skus is null");
                return null;
            }

            foreach (var sku in _skus) {
                var config = GetSKUConfig(sku);
                if (config.PropId == productKey) {
#if UNITY_IOS
                    // iOS迁移，把所有计费点重新提了一遍作为消耗品，中台控制真正的类型。新提的计费点都用“ios.”做前缀。skus中，目前是新旧都有。这里用前缀去筛选，确保返回的是新计费点。
                    if (!config.ProductId.StartsWith(IOSProductIdPrefix)) {
                        continue;
                    }
#endif
                    return sku;
                }
            }

            if (LogUtil.IsShowLog) {
                LogUtil.LogError("uuuuuuuu GetSKUByKey not found!!!!: productKey:" + productKey);
            }
            return null;
        }

        public SKU GetSKUByPID(string productId) {
            if (_skus.Count == 0) {
                Debug.LogError("GetSKU: InitSKUS error: _skus is null");
                return null;
            }

            foreach (var sku in _skus) {
                var config = GetSKUConfig(sku);
                if (config.ProductId == productId) {
                    return sku;
                }
            }
            
            // 兼容需要修改product id的订单
            foreach (var sku in _skus) {
                var config = GetSKUConfig(sku);
                if (config.ProductId == FixProductId(productId)) {
                    return sku;
                }
            }

            LogUtil.LogError("uuuuuuuu GetSKUByPID not found!!!!: productId:" + productId);
            return null;
        }
        
        /// <summary>
        /// 兼容特定渠道的product的id差异
        /// </summary>
        /// <param name="productId"></param>
        /// <returns></returns>
        private string FixProductId(string productId) {
            if (ChannelConfig.IsHuaWei) {
                if (productId == "2") {
                    return "10002";
                } else if (productId == "3") {
                    return "10003";
                }
            }

            return productId;
        }
        private SKU GetSKUById(System.Guid skuId) {
            if (_skus.Count == 0) {
                Debug.LogError("GetSKU: InitSKUS error: _skus is null");
                return null;
            }

            foreach (var sku in _skus) {
                if (sku.Id == skuId) {
                    return sku;
                }
            }

            LogUtil.LogError("uuuuuuuu GetSKUById not found!!!!: skuId:" + skuId);
            return null;
        }

        private static bool useRegex = true;
        private static Dictionary<Guid, GenericItemConfig> _cacheDict;
        // 预编译正则表达式，避免每次匹配时重新编译
        private static readonly Regex ProductIdRegex = new Regex(@"""productId""\s*:\s*""([^""]*)""", RegexOptions.Compiled);
        private static readonly Regex PropIdRegex = new Regex(@"""propId""\s*:\s*""([^""]*)""", RegexOptions.Compiled);

        private static bool UseRegex() {
            // if (LogUtil.IsShowLog) {
            //     LogUtil.Log($"UseRegex {useRegex}");
            // }
            return useRegex;
        }
        public GenericItemConfig GetSKUConfig(SKU sku) {
            if (UseRegex()) {
                return RegexGetGenericItemConfig(sku);
            } else {
                return JsonConvert.DeserializeObject<GenericItemConfig>(sku.Item_config);
            }
        }

        private static GenericItemConfig RegexGetGenericItemConfig(SKU sku) {
            if (_cacheDict == null) {
                _cacheDict = new Dictionary<Guid, GenericItemConfig>();
            }

            if (_cacheDict.TryGetValue(sku.Id, out GenericItemConfig cacheConfig)) {
                return cacheConfig;
            }

            var config = new GenericItemConfig();
            // 匹配 productId
            var productIdMatch = ProductIdRegex.Match(sku.Item_config);
            if (productIdMatch.Success) {
                config.ProductId = productIdMatch.Groups[1].Value;
            }

            // 匹配 propId
            var propIdMatch = PropIdRegex.Match(sku.Item_config);
            if (propIdMatch.Success) {
                config.PropId = propIdMatch.Groups[1].Value;
            }

            if (!string.IsNullOrEmpty(config.ProductId) && !string.IsNullOrEmpty(config.PropId)) {
                _cacheDict[sku.Id] = config;
            }

            return config;
        }

        /// <summary>
        /// 游戏启动全量拉取一次skus
        /// </summary>
        private async Task<bool> FetchSKUS() {
            if (LogUtil.IsShowLog) {
                LogUtil.Log("fetch sku ..." + skuRetryCount, "FetchSKUS");
            }
            var res = await ChillyRoomService.GetSdk().Payment.ListSku()
                .Timeout(TimeSpan.FromSeconds(10))
                .Catch<IResult<List<SKU>, Failure<ListSkuErrorKind>>, TimeoutException>(ex => Observable.Return(ListSkuResult.Err(new Failure<ListSkuErrorKind> {
                        Error = ListSkuErrorKind.UnknownError, //其实是超时，因为中台偷懒没加
                        Exception = ex,
                    })))
                .ToTask();
            
            if (res.IsOk()) {
                if (LogUtil.IsShowLog) {
                    LogUtil.Log($"[FetchSKUS] Success");
                }

                _skus = res.Unwrap();

                if (LogUtil.IsShowLog) {
                    foreach (var sku in _skus) {
                        if (LogUtil.IsShowLog) {
                            LogUtil.Log($"uuuuuuuuu {sku.Name} : {StrUtil.GetAllProperties(sku)}");
                        }
                    }
                }

                OnFetchedSKUS?.Invoke();
                OnFetchedSKUS = null;
                return true;
            }

            if (LogUtil.IsShowLog) {
                LogUtil.Log($"[FetchSKUS] Failed");
            }
            try {
                Debug.Log("FetchSKUS.error msg:" + res.Error.Exception);
                BuglyUtil.ReportException("FetchSKUS_exp", res.Error.Exception);
            } catch (Exception e) {
                Debug.Log("FetchSKUS.error exp:" + e);
            }

            return false;
        }

        #endregion

        #region 支付

        // 除了catch的超时异常，也都走这里
        private void OnPayNext(IResult<Order, Failure<PurchaseErrorKind>> res) {
            Debug.Log("uuuuuuuu NewSDKManager OnPayNext res:" + res.IsOk());
            if (!res.IsOk()) {
                Debug.LogError($"OnPayNext Error:{res.Error.Error} Description:{res.Error.Description}");
            }
            if (res.IsOk()) {
                var order = res.Unwrap();
                var payload = JsonConvert.DeserializeObject<Payload>(order.Payload);
                var orderId_ProductId = string.Format("{0};{1}", order.Id, payload.ProductId);
                ConfirmProductShipped(order);

                GenericPayProcesser.Inst.ProcessPurchase(payload.PropId, true);
                SimpleEventManager.Raise(PaySuccessEvent.UseCache(orderId_ProductId, ChannelConfig.GetChannelName()));

                // LogUtil.Log("uuuuuuuuu NewSDKManager.Pay 购买成功 order:\n" + StrUtil.GetAllProperties(order));
            } else if (ChannelConfig.IsAllGP){
                var detail = res.Error?.Details;
                if (detail != null && detail.TryGetValue("Reason", out var reason) && !string.IsNullOrEmpty(reason)) {
                    try {
                        BasePayAgent.PurchaseFailDetail failDetail =
                            JsonConvert.DeserializeObject<BasePayAgent.PurchaseFailDetail>(reason);
                        if (failDetail?.ErrorCode == 10001) {//有老订单，还未绑定
                            Debug.Log("failDetail 10001");
                            SimpleEventManager.Raise(new UnBindOrderEvent());
                        }
                    } catch (Exception ex) {
                        Debug.LogException(ex);
                    }
                }
                LogUtil.Log("uuuuuuuu NewSDKManager.Pay 购买失败 Error:" + res.Error);
            }

            onPayCompleted?.Invoke(res.IsOk());
            onPayCompleted = null;
        }

        // 这里只调起支付，在ApplicationFocus处理结果
        private void Pay(SKU sku) {
            LogUtil.Log("uuuuuuuuu NewSDKManager.Pay sku:\n" + StrUtil.GetAllProperties(sku));
#if UNITY_IOS
            UiUtil.ShowLoadingWindow(UICanvasRoot.Inst.transform, 130);
#else
            UiUtil.ShowLoadingWindow(UICanvasRoot.Inst.transform, 60);
#endif
            // 此处用配置决定等候时间，默认10秒
            var timeout = Config?.purchaseTimeout ?? 10;
            
#if UNITY_IOS
            // iOS根据苹果返回，不设超时
            timeout=9999;
#endif
            if (ChannelConfig.IsChannel("HuaWeiOH")) {
                _config.purchaseTimeout = 120;// 鸿蒙渠道超时改为120s
            }
            Debug.Log($"uuuuuuuuu NewSDKManager.Pay 超时时间 : {timeout}");
            var focusStream = Observable.EveryApplicationFocus()
                .Where(focus => _payListener != null).Select(focus => {
                    // timescale为0的时候Observable.Timer不会执行
                    Time.timeScale = 1;
                    var timespan = TimeSpan.FromSeconds(focus ? timeout : 9999);
                    return Observable.Timer(timespan)
                        .Select(_ => Observable.Throw<string>(new TimeoutException()))
                        .Switch();
                })
                .Switch();

            _payListener = ChillyRoomService.GetSdk().Payment.Purchase(sku.Id, 1, sku.Price);
            _payListener.TakeUntil(focusStream)
                .Catch<IResult<Order, Failure<PurchaseErrorKind>>, TimeoutException>(ex => {
                    Debug.Log($"uuuuuuuuu NewSDKManager.Pay TimeoutException");
                    onPayCompleted?.Invoke(false);
                    onPayCompleted = null;
                    return Observable.Empty<IResult<Order, Failure<PurchaseErrorKind>>>();
                })
                .Finally(() => {
                    // catch的超时异常也会走到这里
                    SimpleEventManager.Raise(new RefreshCloudPackageEvent());
                    UiUtil.HideLoadingWindow(UICanvasRoot.Inst.transform);
                    _payListener = null;
                })
                .Subscribe(OnPayNext).AddTo(this);
        }

        /// <summary>
        /// 商品发货后调用，删除设备缓存，避免重启游戏重复发货
        /// </summary>
        /// <param name="order"></param>
        public void ConfirmProductShipped(Order order) {
            LogUtil.Log("uuuuuuuuu NewSdkManager.ConfirmProductShipped: 确认发货\n" + StrUtil.GetAllProperties(order));
            ChillyRoomService.GetSdk().Payment.ConfirmOrder(order);
        }


        public class PurchaseParams {
            public string PropId;
            public Action<bool> OnPurchaseDone;
            public ConsumeStatistics.BuyWay BuyWay;
            
            private static PurchaseParams cache= new PurchaseParams();
            
            public static PurchaseParams UseCache(string propId, System.Action<bool> onPurchaseDone, ConsumeStatistics.BuyWay buyWay) {
                cache.PropId = propId;
                cache.OnPurchaseDone = onPurchaseDone;
                cache.BuyWay = buyWay;
                return cache;
            }
        }
        public event Func<PurchaseParams, bool> BreakPay;

        public void BuyProduct(PurchaseParams purchaseParams) {
            BuyProduct(purchaseParams.PropId, purchaseParams.OnPurchaseDone, purchaseParams.BuyWay);
        }

        /// <summary>
        /// 所有购买统一入口
        /// </summary>
        /// <param name="propId">商品key: purchase_gem_3</param>
        /// <param name="onPurchaseDone"></param>
        public void BuyProduct(string propId, System.Action<bool> onPurchaseDone, ConsumeStatistics.BuyWay buyWay = ConsumeStatistics.BuyWay.NONE) {
            if (BreakPay != null && BreakPay.Invoke(PurchaseParams.UseCache(propId, onPurchaseDone, buyWay))) {
                return;
            }

            if (!GameUtil.ForHotFixCanPay()) {
                return;
            }
            
            if (Application.isEditor) {
                // 编辑器下凉屋登录的可以走完整逻辑，比如TapTap
                if (IsLoginedByChilly() && _skus.Count == 0) {
                    CommonUiUtil.ShowMsg(UICanvasRoot.Inst.transform, ScriptLocalization.Get("Err/Net_err_try_again", "网络异常，请稍后尝试"), 1.5f);
                    return;
                }
                
                // 编辑器下直接发货
                GenericPayProcesser.Inst.ProcessPurchase(propId, true, buyWay);
                var orderId_ProductId = string.Format("{0};{1}", DateTime.UtcNow.ToString(), propId);
                SimpleEventManager.Raise(PaySuccessEvent.UseCache(orderId_ProductId, ChannelConfig.GetChannelName()));
                onPurchaseDone?.Invoke(true);
                return;
            }

            if (!LifeCycleManager.Instance.SupportPay) return;
            if (_payListener != null) return;

            if (_skus.Count == 0) {
                CommonUiUtil.ShowMsg(UICanvasRoot.Inst.transform, ScriptLocalization.Get("Err/Net_err_try_again", "网络异常，请稍后尝试") + " , 10001", 1.5f);
                return;
            }

            var sku = GetSKUByKey(propId);
            if (sku == null) {
                CommonUiUtil.ShowMsg(UICanvasRoot.Inst.transform, ScriptLocalization.Get("Err/Net_err_try_again", "网络异常，请稍后尝试") + " , 10002", 2.5f);
                Debug.LogError("sku not found! productKey:" + propId);
                BuglyUtil.ReportException("GetSKU_NotFound", new Exception($"sku not found, productKey:{propId}"));
                return;
            }

            // sku.price以分为单位
            var realPrice = sku.Price / 100;
            LogUtil.Log("uuuuuuuuuu NewSdkManager.BuyProduct: productKey:" + propId + " realPrice:" + realPrice);
            if (ChannelConfig.IsAllGP && LegacyOrders != null && LegacyOrders.Any(_=>_.ProductId == GetSKUConfig(sku).ProductId)) {
                // 比对迁移过的订单，如果是没迁移过的则弹出提示
                if (MigratedOrders != null) {
                    if (MigratedOrders.All(_ => GetSKUById(_.Sku_id) != sku)) {
                        NewSDKManager.Inst.ShowMigrateOrdersUI(false, LegacyOrders);
                        return;
                    }
                } else {
                    // 没迁移过 则弹出提示
                    NewSDKManager.Inst.ShowMigrateOrdersUI(false, LegacyOrders);
                    return;
                }
            }

            onPayCompleted = onPurchaseDone;
            Pay(sku);

            GenericPayProcesser.Inst.order2Time[propId] = System.DateTime.Now;
            TAUtil.Track("pre_order", new Dictionary<string, object>() {
                {"item_id", propId},
                {"item_name", sku.Name},
                {"price", realPrice},
                {"buy_way", buyWay}
            });
        }

        #endregion

        #region 补单逻辑

        /// <summary>
        /// 是否尝试过恢复订单
        /// </summary>
        private bool _alreadyTryGetHistoryOrders;
        private void RestoreOrdersIfNeverTryGetHistoryOrders(Payment.RestoreOrdersResult? tmpResult) {
            if (!_alreadyTryGetHistoryOrders) {
                RestoreAllOrders(tmpResult);
            }
        }
        // 调用此接口的时候，中台会检查Paid状态的订单，然后合法的转为Claimed
        public async void RestoreAllOrders(Payment.RestoreOrdersResult? tmpResult) {
            LogUtil.Log($"uuuuuuuuuu NewSdkManager.RestoreAllOrders SupportRestore : {LifeCycleManager.Instance.SupportRestore}");
            if (!LifeCycleManager.Instance.SupportRestore) {
                return;
            }

            if (!GameUtil.ForHotFixCanRestore()) {
                return;
            }
            
            if (_skus == null || _skus.Count == 0) {
                Debug.LogError("RestoreAllOrders ignore because skus == null !!!");
                return;
            }

            // 云背包数据是否拉取到
            if (DataMgr.CloudPackageData == null || !DataMgr.CloudPackageData.IsReady()) {
                return;
            }
            
            // 设置尝试拉取过历史订单，防止云背包接口拉取到后也会会重复调用GetHistoryOrders
            if (tmpResult == null) {
                _alreadyTryGetHistoryOrders = true;
            }

            var ordersResult = tmpResult ?? await GetHistoryOrders();
            if (ordersResult == null) {
                return;
            }

            if (LogUtil.IsShowLog) {
                LogUtil.Log($"LegacyOrders : {ordersResult?.LegacyOrders?.Count} canRestore : {DataMgr.CloudPackageData.CanRestoreChannelLegacyOrder()}");
            }
            if (ordersResult?.LegacyOrders != null) {
                if (LogUtil.IsShowLog) {
                    foreach (var legacyOrder in ordersResult?.LegacyOrders) {
                        var orderId = string.IsNullOrEmpty(legacyOrder.OrderId) ? "空" : legacyOrder.OrderId;
                        LogUtil.Log($"[RestoreAllOrders]输出老订单 : ProductId : {legacyOrder.ProductId}, ThirdPartyOrderId : {legacyOrder.ThirdPartyOrderId}, OrderId : {orderId}");
                    }
                }

                if (ordersResult?.LegacyOrders != null && ordersResult?.LegacyOrders.Count > 0) {
                    LegacyOrders = ordersResult?.LegacyOrders;
                }

                // 迁移成功的订单需要在老订单里去重
                if (MigratedOrders != null 
                    && MigratedOrders.Count > 0
                    && ordersResult?.LegacyOrders!=null 
                    && ordersResult?.LegacyOrders.Count>0) {
                    Payment.RestoreOrdersResult tmpOrderResult = (Payment.RestoreOrdersResult)ordersResult;
                    for (int i = tmpOrderResult.LegacyOrders.Count - 1; i >= 0; i--) {
                        if (!string.IsNullOrWhiteSpace(tmpOrderResult.LegacyOrders[i].ThirdPartyOrderId) 
                            && MigratedOrders.Any(_ =>
                                _.Third_party_order_id == ordersResult?.LegacyOrders[i].ThirdPartyOrderId)) {
                            LogUtil.Log($"[RestoreAllOrders]迁移成功的老订单去重 {ordersResult?.LegacyOrders[i].ThirdPartyOrderId}");
                            ordersResult?.LegacyOrders.RemoveAt(i);
                        }
                    }
                }
            } else {
                LogUtil.Log($"[RestoreAllOrders]迁移成功的老订单为空");
            }

            // 新订单
            foreach (var clientOrder in ordersResult?.Orders) {
                SKU sku = GetSkuByOrder(clientOrder);
                if (sku == null) {
                    var e = new Exception($"sku not found, Order:{StrUtil.GetAllProperties(clientOrder.Order)}");
                    BuglyUtil.ReportException("RestoreAllOrders_SKU_NULL", e);
                    continue;
                }

                if (clientOrder.Order.Status != OrderStatus.Claimed) {
                    // 未确认的订单
                    continue;
                }

                // 有些订单Id为空，有些可能Third_party_order_id为空，但是不可能两者都为空
                var orderIdMark = $"{clientOrder.Order.Id}_{clientOrder.Order.Third_party_order_id}";
                if (GenericPayProcesser.Inst.HasDoShipped(orderIdMark) && sku.Type != SKUType.OneTime) {
                    LogUtil.Log($"HasDoShipped {orderIdMark}");
                    // 已发货过，避免重复发货
                    continue;
                }

                GenericPayProcesser.Inst.AddShippedOrderId(orderIdMark);

                var skuConfig = GetSKUConfig(sku);

                if (string.IsNullOrEmpty(skuConfig.PropId)) {
                    LogUtil.LogError($"{sku.Id} 的PropId为空，跳过恢复");
                    continue;
                }
                
                if (sku.Type == SKUType.OneTime) {
                    // 非消耗型商品，则恢复商品
                    var result = RestoreRole.RestoreRoleByKey(skuConfig.PropId);
                    if (result == RestoreRole.RestoreResult.Success) {
                        CommonUiUtil.ShowMsg(UICanvasRoot.Inst.transform, sku.Name + " " + I2.Loc.ScriptLocalization.Get("ui/restore_suc","恢复订单成功"), 1.5f);
                        ConfirmProductShipped(clientOrder.Order);
                    }
                } else if (sku.Type == SKUType.Consumable) {
                    // 消耗型商品，如果本地有缓存（即没有确认过发货）订单，则发货
                    if (!clientOrder.Received) {
                        var result = RestoreRole.RestoreComodityByKey(skuConfig.PropId);
                        if (result == RestoreRole.RestoreResult.Success) {
                            CommonUiUtil.ShowMsg(UICanvasRoot.Inst.transform, sku.Name + " " + I2.Loc.ScriptLocalization.Get("ui/restore_suc","恢复订单成功"), 1.5f);
                            ConfirmProductShipped(clientOrder.Order);
                        }
                    }
                }
            }
            
            // 老订单，都是已支付过的非消耗品
            if (!ChannelConfig.IsNeedMigrateOrders) {
                // 处理注销后的玩家不支持恢复老订单
                if (DataMgr.CloudPackageData.CanRestoreChannelLegacyOrder()) {
                    foreach (var legacyOrder in ordersResult?.LegacyOrders) {
                        var sku = GetSKUByPID(legacyOrder.ProductId);
                        if (sku == null) {
                            var e = new Exception($"sku not found, legacy Order:{StrUtil.GetAllProperties(legacyOrder)}");
                            BuglyUtil.ReportException("RestoreAllOrders_SKU_NULL", e);
                            continue;
                        }

                        var skuConfig = GetSKUConfig(sku);
                        if (sku.Type == SKUType.OneTime) {
                            LogUtil.Log("uuuuuuuuuuu NewSdkManager.RestoreAllOrders 恢复非消耗品：" + sku.Name);
                            // 非消耗型商品，则恢复商品
                            var result = RestoreRole.RestoreRoleByKey(skuConfig.PropId);
                            if (result == RestoreRole.RestoreResult.Success) {
                                CommonUiUtil.ShowMsg(UICanvasRoot.Inst.transform, sku.Name + " " + I2.Loc.ScriptLocalization.Get("ui/restore_suc","恢复订单成功"), 1.5f);
                            }
                        }
                    }
                }
            } else if (ordersResult?.LegacyOrders != null && ordersResult?.LegacyOrders.Count > 0) {
                //订单迁移逻辑
                LogUtil.Log("有不属于此账号的老订单，尝试自动调用api", "MigrateOrders");
                TryMigrateOrders(ordersResult?.LegacyOrders);
            }
        }

        private SKU GetSkuByOrder(Payment.ClientOrder clientOrder) {
            // 所有渠道先根据payload来获取sku 然后在通过匹配sku id来获取sku
            SKU sku = null;
            if (!string.IsNullOrEmpty(clientOrder.Order.Payload)) {
                sku = GetSKUByKey(JsonConvert.DeserializeObject<Payload>(clientOrder.Order.Payload).PropId);
            } else {
                sku = GetSKUById(clientOrder.Order.Sku_id);
            }

            return sku;
        }

        #region MigrateOrders

        private const string NoLongerPopUpMigrateOrders = "NoLongerPopUpMigrateOrders";

        private async Task TryMigrateOrders(List<Payment.LegacyOrder> legacyOrders) {
            TaskCompletionSource<MoveCloudSaveManager.MoveStep> taskCompletionSource = null;
            MoveCloudSaveManager.MoveStep step = MoveCloudSaveManager.MoveStep.UnKnown;
            var loginBy = ChillyRoomService.GetSdk().Accounts?.StateManager?.State?.Session?.LoginBy;
            // gp、ios走的是默认迁移流程（自动迁移），其他登录方式(邮箱、手机号、游客)走的是可选迁移流程
            taskCompletionSource = loginBy is UniversalSdkType.Apple or UniversalSdkType.GooglePlay ? LegencyAdapter.MoveCloudSaveManager.Instance.MoveTaskCompletionSource : LegencyAdapter.MoveCloudSaveManager.Instance.OptionalMoveCloudSaveManager.MoveTaskCompletionSource;
            if (taskCompletionSource != null) {
                step = await taskCompletionSource.Task;
            }
            if (step != MoveCloudSaveManager.MoveStep.Moved 
                && step != MoveCloudSaveManager.MoveStep.Cancel
                && step != MoveCloudSaveManager.MoveStep.Success
                && step != MoveCloudSaveManager.MoveStep.LegencyNoMove) {
                LogUtil.Log($"迁移未完成，不触发api调用，当前迁移状态 : {step} {LegencyAdapter.MoveCloudSaveManager.Instance.CurrentMoveStep} {LegencyAdapter.MoveCloudSaveManager.Instance.OptionalMoveCloudSaveManager?.CurrentMoveStep}", "MigrateOrders");
                return;
            }
            
            if (StatisticData.data.IsEventRecord(NoLongerPopUpMigrateOrders)) {
                LogUtil.Log("事件已记录，不触发api调用", "MigrateOrders");
                return;
            }

            if (DataUtil.GetCurSceneName() != RGGameConst.SCENE_TITLE) {
                LogUtil.Log("当前场景不是Title，不触发api调用", "MigrateOrders");
                return;
            }

            if (loginBy is UniversalSdkType.Guest) {
                if (!BindEmailAndPhoneManager.Instance.AlreadyGetUserProfile) {
                    LogUtil.Log("当前玩家是游客，没有获取到是否绑定邮箱手机号", "MigrateOrders");
                    return;
                }

                if (BindEmailAndPhoneManager.Instance.NeedBind) {
                    LogUtil.Log("当前玩家是游客，没有绑定邮箱手机号", "MigrateOrders");
                    return;
                }
            }
            
            ShowMigrateOrdersUI(true, legacyOrders);
        }

        public void ShowMigrateOrdersUI(bool autoPopUp, List<Payment.LegacyOrder> legacyOrders) {
            LogUtil.Log("打开调用api的提示弹窗", "MigrateOrders");
            var dialog = UiUtil.ShowDialogWindowWithCloseBtn(UICanvasRoot.Inst.transform,
                ScriptLocalization.Get("I_tip"),
                ScriptLocalization.Get("bind_order_tip_0", "#检测到部分非消耗品订单未绑定账号，未绑定账号的非消耗品订单无法恢复，是否将这些订单绑定至当前帐号？"),
                (ScriptLocalization.Get("account/yes"), () => {
                    CallApiMigrateOrders(legacyOrders);
                }),
                (autoPopUp ? ScriptLocalization.Get("ui/not_show") : ScriptLocalization.Get("account/no"), WriteNoLongerPopUpMigrateOrdersToSave));
            var dialogMask = dialog.transform.Find("mask");
            if (!dialogMask) {
                return;
            }

            var maskBtn = dialogMask.GetComponent<UnityEngine.UI.Button>();
            maskBtn.enabled = false;
            maskBtn.interactable = false;
            maskBtn.onClick.RemoveAllListeners();
        }

        private static void WriteNoLongerPopUpMigrateOrdersToSave() {
            LogUtil.Log("记录事件，不再自动调用api", "MigrateOrders");
            StatisticData.data.RecordEvent(NoLongerPopUpMigrateOrders, true);
        }

        private async void CallApiMigrateOrders(List<Payment.LegacyOrder> legacyOrders) {
            foreach (var legacyOrder in legacyOrders) {
                var orderId = string.IsNullOrEmpty(legacyOrder.OrderId) ? "空" : legacyOrder.OrderId;
                LogUtil.Log($"输出老订单 : ProductId : {legacyOrder.ProductId}, ThirdPartyOrderId : {legacyOrder.ThirdPartyOrderId}, OrderId : {orderId}", "MigrateOrders");
            }
            
            LogUtil.Log("开始调用api", "MigrateOrders");
            UiUtil.ShowLoadingWindow(UICanvasRoot.Inst.transform, 60);
            WriteNoLongerPopUpMigrateOrdersToSave();

            try {
                MigrateOrdersReply reply = await ChillyRoomService.GetSdk().MigrateOrders(legacyOrders.ToArray());
                if (ChannelConfig.IsAllGP && reply != null && reply.Orders != null) {
                    MigratedOrders = reply.Orders.ToList();
                    foreach (var order in reply.Orders) {
                        LogUtil.Log("开始处理老单子", $"callConsumeChannelOrder {order.Third_party_order_id}");
                        AgentHub.Instance.Pay.callConsumeChannelOrder(order.Third_party_order_id);
                        // 迁移订单后的老订单发货
                        DealMigrationOrder(order);
                    }
                }

                UiUtil.HideLoadingWindow(UICanvasRoot.Inst.transform);
                LogUtil.Log("api调用完毕", "MigrateOrders");
                await Task.Delay(500);
                RestoreAllOrders(null);

                var successTipStr = ScriptLocalization.Get("ui/x_bind_suc");
                successTipStr = successTipStr.Replace("{0}", "");
                UiUtil.ShowDialogWindow(UICanvasRoot.Inst.transform, ScriptLocalization.Get("I_tip"), successTipStr, null, null, 2);
                LegacyOrders = null;
            } catch (ChillyRoom.Services.Core.Errors.ApiException<ChillyRoom.Services.Core.Errors.ErrorResponse> e) {                
                UiUtil.HideLoadingWindow(UICanvasRoot.Inst.transform);
                switch (e.StatusCode) {
                    case (int)HttpStatusCode.BadRequest:
                        // 是业务错误
                        switch (e.Result.Error) {
                            case (int)ErrorKind.SUCCESS:
                                break;
                            case (int)ErrorKind.INVALID_INPUT:
                                CommonUiUtil.ShowMsg(UICanvasRoot.Inst.transform, ScriptLocalization.Get("cloudsave/server_unknow_error"), 3.5f);
                                break;
                            case (int)ErrorKind.ALREADY_EXISTS:
                                UiUtil.ShowDialogWindow(UICanvasRoot.Inst.transform,
                                    ScriptLocalization.Get("I_tip"),
                                    ScriptLocalization.Get("bind_order_tip_1", "#非消耗品订单绑定失败，请联系客服。"),
                                    null,
                                    null,
                                    1
                                );
                                break;
                            case (int)ErrorKind.INTERNAL_ERROR:
                                CommonUiUtil.ShowMsg(UICanvasRoot.Inst.transform, ScriptLocalization.Get("ui/net_err"), 3.5f);
                                break;
                            default:
                                CommonUiUtil.ShowMsg(UICanvasRoot.Inst.transform, ScriptLocalization.Get("multi_remote/client_unknowerror"), 3.5f);
                                break;
                        }
                        break;
                    default:
                        // 是通用错误
                        ErrorUtil.HandleApiException(e);
                        break;
                }
            }
        }

        #endregion

        #endregion

        /// <summary>
        /// 查询所有历史订单（90d合规时长）
        /// 包含原生层 + 服务器订单
        /// 调用此接口的时候会再次尝试Claime订单，把已支付未确认状态转为Claimed，以便补单
        /// </summary>
        /// <returns></returns>
        public async Task<Payment.RestoreOrdersResult?> GetHistoryOrders() {
            var oldChillyUid = GetChillyUid();
            var res = await ChillyRoomService.GetSdk().Payment.RestoreOrders().ToTask();
            if (res.IsOk()) {
                if (CheckAccountChange(oldChillyUid)) {
                    return null;
                }

                return res.Unwrap();
            }

            return null;
        }

        private bool CheckAccountChange(uint oldChillyUid) {
            var nowChillyUid = GetChillyUid();
            if (oldChillyUid != 0 && nowChillyUid != 0 && oldChillyUid != GetChillyUid()) {
                //检查到切换了账号则返回空数据
                Debug.LogError("found account change");
                return true;
            }

            return false;
        }

        // 对于pingxx渠道，需要额外展示本地缓存的订单
        public List<LocalOrder> GetLocalHistoryOrders() {
            List<LocalOrder> localOrders = new List<LocalOrder>();
            if (SdkConfigManager.gameConfig.Distro.BuildConfig.channelType != ChannelType.PingXX) {
                return localOrders;
            }

            // 老包本地订单也跟随迁移本地数据的账号
            if (!NewSDKManager.NeedMoveLocalData()) {
                return localOrders;
            }

            string jsonOrderStr;
            if (Application.isEditor) {
                jsonOrderStr = "[" +
                               "{\"orderId\":\"1002304193234062727\",\"isPayed\":true,\"isCanceled\":false,\"productName\":\"800宝石\",\"timeTick\":*************}," +
                               "{\"orderId\":\"1002304193234072728\",\"isPayed\":false,\"isCanceled\":true,\"productName\":\"25000宝石\",\"timeTick\":1681904823458}," +
                               "{\"orderId\":\"1002304199234072728\",\"isPayed\":false,\"isCanceled\":false,\"productName\":\"35000宝石\",\"timeTick\":1681907823458}" +
                               "]";
            } else {
                jsonOrderStr = AgentHub.Instance.callGetOldHistoryOrderList();
            }

            LogUtil.Log("GetLocalHistoryOrders jsonStr:" + jsonOrderStr);
            if (!string.IsNullOrEmpty(jsonOrderStr)) {
                try {
                    localOrders = JsonConvert.DeserializeObject<List<LocalOrder>>(jsonOrderStr);
                    // 对列表按 TimeTick 降序排列
                    return localOrders.OrderByDescending(o => o.TimeTick).ToList();
                } catch (Exception e) {
                    Debug.LogError(e);
                }
            }

            return localOrders;
        }

        public string GetPriceDescription(string propId, bool useNumberFont = false) {
            var sku = GetSKUByKey(propId);
            if (sku == null) {
                return "-";
            }

            var result = sku.Loc_price;
            if (LanguageUtil.IsChineseSensitive()) {
                result = LanguageUtil.FilterChineseSymbol(result, useNumberFont);
            }

            return result;
        }

        void DealMigrationOrder(ChillyRoom.Services.Soulknight.Order order) {
            var sku = GetSKUById(order.Sku_id);
            if (sku == null) {
                var e = new Exception($"DealMigrationOrder sku not found, Order:{StrUtil.GetAllProperties(order)}");
                BuglyUtil.ReportException("RestoreAllOrders_SKU_NULL", e);
                return;
            }

            if (order.Status != ChillyRoom.Services.Soulknight.OrderStatus.Claimed) {
                // 未确认的订单
                return;
            }

            // 有些订单Id为空，有些可能Third_party_order_id为空，但是不可能两者都为空
            var orderIdMark = $"{order.Id}_{order.Third_party_order_id}";
            if (GenericPayProcesser.Inst.HasDoShipped(orderIdMark) && sku.Type != SKUType.OneTime) {
                LogUtil.Log($"DealMigrationOrder HasDoShipped {orderIdMark}");
                // 已发货过，避免重复发货
                return;
            }

            GenericPayProcesser.Inst.AddShippedOrderId(orderIdMark);

            var skuConfig = GetSKUConfig(sku);

            if (string.IsNullOrEmpty(skuConfig.PropId)) {
                LogUtil.LogError($"DealMigrationOrder {sku.Id} 的PropId为空，跳过恢复");
                return;
            }
            
            if (sku.Type == SKUType.OneTime) {
                // 非消耗型商品，则恢复商品
                var result = RestoreRole.RestoreRoleByKey(skuConfig.PropId);
                if (result == RestoreRole.RestoreResult.Success) {
                    CommonUiUtil.ShowMsg(UICanvasRoot.Inst.transform, sku.Name + " " + I2.Loc.ScriptLocalization.Get("ui/restore_suc","恢复订单成功"), 1.5f);
                }
            }
        }
        
        private void OnCloudPackageUpdateEvent(CloudPackageUpdateEvent e) {
            // 拉取到云背包后再尝试恢复历史订单，因为玩家可能注销过就不需要恢复老订单，所以需要再次尝试恢复订单（如果之前没有恢复过）
            RestoreOrdersIfNeverTryGetHistoryOrders(null);
        }

        private static bool EnableVvPayFunc() {
#if UNITY_EDITOR
            var ret = UnityEditor.EditorPrefs.GetBool("EnableVvPayInit");
            if (ret) {
                return true;
            }
#endif
        
            if (!string.IsNullOrEmpty(ForcePatchManager.vvpayMaxTakeEffectBundleVersion) 
                && int.TryParse(ForcePatchManager.vvpayMaxTakeEffectBundleVersion , NumberStyles.Any, CultureInfo.InvariantCulture,out int umpVersion)) {
                if (CurrentBundleVersion.currentBundleVersion.bundleVersionCode >= umpVersion) {
                    return false;
                }
            }
        
            return ForcePatchManager.IsEntryOpen(ForcePatchEntry.Vvpay, false, false);
        }

        private void SetVNMPayType() {
            if (!ChannelConfig.IsAllVNM) {
                return;
            }

            PayAgentExtra payAgentExtra = new PayAgentExtra();
            List<SkuPayType> supportedPayTypes = new List<SkuPayType>();
            supportedPayTypes.Add(SkuPayType.GooglePlay);
            // Debug.Log($"SetVNMPayType {EnableVvPayFunc()} {ForcePatchManager.vvpayTakeEffectBundleVersion} {ForcePatchManager.IsEntryOpen(ForcePatchEntry.Vvpay, false, false)}");
            if (EnableVvPayFunc()) {
                supportedPayTypes.Add(SkuPayType.Vv);
            }
            payAgentExtra.supportedPayTypes = supportedPayTypes;
            AgentHub.Instance.Pay.callSetExtra(JsonConvert.SerializeObject(payAgentExtra));
        }
    }

    // {"count": 5000, "productId": "5", "propId": "purchase_gem_1"}
    public class Payload {
        [JsonProperty("count")] public int count { get; set; }
        [JsonProperty("productId")] public string ProductId { get; set; }
        [JsonProperty("propId")] public string PropId { get; set; }
    }

    /// <summary>
    /// 老包本地缓存订单
    /// {"orderId":"1002304193234062727","isPayed":false,"isCanceled":true,"productName":"800宝石","timeTick":*************}
    /// </summary>
    public class LocalOrder {
        [JsonProperty("orderId")] public string OrderId { get; set; }

        [JsonProperty("isPayed")] public bool IsPayed { get; set; }

        [JsonProperty("isCanceled")] public bool IsCanceled { get; set; }

        [JsonProperty("productName")] public string ProductName { get; set; }

        [JsonProperty("timeTick")] public long TimeTick { get; set; }
    }
}
