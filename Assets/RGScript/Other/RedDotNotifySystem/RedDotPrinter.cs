using UnityEngine;

public class RedDotPrinter {
    private static string TAG = "RedDotTree";

    public static void PrintTreeManager(RedDotManager manager) {
        if (manager == null || manager.Root == null) {
            Debug.Log("Tree is empty");
            return;
        }

        string treeString = BuildTreeWithDepth(manager.Root, 0);
        Debug.Log(treeString);
    }

    public static string BuildTreeWithDepth(RedDotNode node, int depth) {
        if (node == null) return string.Empty;

        // 根据深度缩进
        string indent = depth > 0 ? new string(' ', (depth -1) * 6) + "|---" : "";
        string status = node.IsRedDotActive ? $"[Active {node.GetMarkCount()}]" : $"[Inactive {node.GetMarkCount()}]";
        string nodeString = $"{indent}{node.Name} {status}--{node.UIPath}\n";

        // 递归拼接子节点
        foreach (var child in node.Children) {
            nodeString += BuildTreeWithDepth(child, depth + 1);
        }

        return nodeString;
    }
}