using Activities.BugFeatures.Scripts;
using ModeSeason.ComboGun;
using RGScript.Battle;
using RGScript.Data;
using System;
using System.Collections.Generic;
using UnityEngine;
using RGScript.Mode.ModeMapLevelController;

/// <summary>
/// 针对不同的模式处理
/// </summary>
public abstract class BaseModeProcess {
    public static float ExtraBoxDropItemRate = 0;
    public virtual bool IsGameFinished() {
        return false;
    }

    /// <summary>
    /// 检测当前level是否需要跳转到结算
    /// </summary>
    /// <param name="levelIndex">关卡</param>
    /// <returns></returns>
    public abstract bool NeedGotoStatement(int levelIndex);

    public virtual string GetStatementSceneName() {
        return RGGameConst.SCENE_STATEMENTS;
    }

    public virtual GameObject CreateSpecificMapManager(string name) {
        return null;
    }

    /// <summary>
    /// 处理当前levelindex
    /// </summary>
    /// <param name="levelIndex">关卡</param>
    public virtual void ProcessLevelIndex(ref int levelIndex) {
        levelIndex++;
        // LogUtil.Log($"ProcessLevelIndex battleData level: {BattleData.data.localLevelIndex}");
        AfterProcessLevelIndex(ref levelIndex);
    }

    /// <summary>
    /// 针对levelIndex后处理
    /// </summary>
    /// <param name="levelIndex"></param>
    protected virtual void AfterProcessLevelIndex(ref int levelIndex) { }

    public virtual void GotoNextScene(string targetScene) {
        AssetBundleLoader.Inst.LoadScene(targetScene); //Loading或者结算
    }

    /// <summary>
    /// 更新战斗时长
    /// </summary>
    /// <param name="add_battle_time"></param>
    public virtual void UpdateBattleTime(float add_battle_time) {
        BattleData.data.battle_time += add_battle_time;
    }

    public static void InitDefaultBuffConfig() {
        var buffExe = new DefaultBuffExecution();
        buffExe.LoadDefaultConfig();
        BattleData.data.SetBuffExecution(buffExe);
    }

    public virtual void GameProcessInit() {
        InitDefaultBuffConfig();
        ContinuousCommonBuff.OnLevelStart();
        if (BattleData.data.BattleStatementsData == null) {
            BattleData.data.BattleStatementsData = new BattleStatementsData();
        }
        var r = new RGRandom();
        r.SetRandomSeed(RGGameInfo.Inst.map_random_seed);
        CGRandom.SetRandom(r);

        if (BattleData.data.IsLoopTravel) {
            BattleData.data.ResetFloatMark(ModeLoopTravelController.REDUCE_TIME_DEFEATING_FEL_LORD);
        }
    }

    public virtual bool CustomizedSceneInitialization { get; }

    public virtual void OnCustomizedSceneInitialization() { }

    public virtual GameObject OnCreatePlayerInstance(uint netId) {
        return null;
    }

    public virtual bool HasMount() {
        return true;
    }

    /// <summary>
    /// 游戏场景开始
    /// </summary>
    public virtual void GameSceneStart() {
        SimpleEventManager.Raise(new CoinChangeEvent {
            curCoinVal = RGGameProcess.Inst.coin_value
        });
    }

    /// <summary>
    /// 游戏场景结束，清理
    /// </summary>
    public virtual void CleanUp() {
        ContinuousCommonBuff.OnLevelEnd();
        SimpleEventManager.RemoveListener<PlayerGetHurtEvent>(OnPlayerGetHurtShakeCamera);
        SimpleEventManager.RemoveListener<CharacterRebornEvent>(OnPlayerResurrect);
        foreach (var timer in _registeredTimer) {
            timer.Cancel();
        }

        _registeredTimer.Clear();
        CGAudio.audioInstanceCount = 0;
    }

    public virtual void ResetModeConfig() {
    }

    /// <summary>
    /// 本模式中是否有宠物
    /// </summary>
    /// <returns></returns>
    public virtual bool HasPetInThisMode() {
        return true;
    }

    /// <summary>
    /// 创建角色回调（未创建坐骑）
    /// </summary>
    /// <param name="controller"></param>
    public virtual void CreatingPlayer(RGController controller) {
    }

    /// <summary>
    /// 创建玩家角色之后的回调
    /// </summary>
    /// <param name="controllers"></param>
    public virtual void AfterCreatePlayer(RGController controller) {
        SimpleEventManager.RemoveListener<PlayerGetHurtEvent>(OnPlayerGetHurtShakeCamera);
        SimpleEventManager.RemoveListener<CharacterRebornEvent>(OnPlayerResurrect);
        SimpleEventManager.AddEventListener<PlayerGetHurtEvent>(OnPlayerGetHurtShakeCamera);
        SimpleEventManager.AddEventListener<CharacterRebornEvent>(OnPlayerResurrect);
        if (controller != null) {
            BattleData.data.buffExecution.PostCreatedPlayerInGameScene(controller);
        }
    }

    public virtual void AfterCreateAllPlayers() {
    }

    public virtual void AfterCharacterSetUp(RoleAttributePlayer playerAttribute) {
    }

    public virtual GameObject OnReplaceCreatingRoom(RGScript.Map.MapManagerLevel.RoomCreateType roomType, GameObject originRoomPrefab) {
        return null;
    }

    public virtual void OnRoomObjectInstantiated(RGRoomX room) { }

    public virtual void OnStartRoom(RGRoomX room, int roomType) {
    }

    public virtual void OnEnemyCreate(RGEController rgeController, RGRandom rgRandom, RGRoomXInfo roomXInfo) {
        if (BattleData.data.HasBuff(emBuff.EnemyHpUpSpeedDown)) {
            var param = BuffStackCfgEnemyHpUpSpeedDown.GetParam(0.2f, -0.2f);
            if (param.hpFactor != 0 && param.speedFactor != 0) {
                rgeController.onSetupAttribute += e => {
                    rgeController.attributeProxy.MaxHpValue.AddMultiplicationValue(emBuff.EnemyHpUpSpeedDown.ToString(),
                        1 + param.hpFactor);
                    rgeController.attributeProxy.hp = rgeController.attributeProxy.max_hp;
                    rgeController.attribute.addSpeedRate.AddAdditionValue(emBuff.EnemyHpUpSpeedDown.ToString(),
                        param.speedFactor);
                };
            }
        }
    }

    public virtual void OnPlayerResurrect(CharacterRebornEvent evData) {
        // 补一遍天赋
        foreach (var kv in BattleData.data.buffTimesDic) {
            if (kv.Value > 0) {
                BattleData.data.buffExecution.OnBuffUpdated(evData.controller, kv.Key);
            }
        }
        
        foreach (var kv in BattleData.data.additionBuffTimesDic) {
            if (kv.Value > 0) {
                BattleData.data.buffExecution.OnBuffUpdated(evData.controller, kv.Key);
            }
        }
        
        // 补角色天赋
        foreach (var kv in BattleData.data.buffsCharactor) {
            BattleData.data.buffExecution.OnBuffUpdated(evData.controller, (emBuff)kv);
        }
    }

    protected virtual void OnPlayerGetHurtShakeCamera(PlayerGetHurtEvent e) {
        if (e.rgController != RGGameSceneManager.Inst.controller) {
            return;
        }

        switch (e.hurtInfo.Damage) {
            case 0:
                break;
            case < 2:
                GameUtil.CameraShake(1);
                break;
            case < 3:
                GameUtil.CameraShake(2);
                break;
            default:
                GameUtil.CameraShake(3);
                break;
        }
    }

    /// <summary>
    /// 游戏进程更新
    /// </summary>
    /// <param name="deltaTime"></param>
    public virtual void Update(float deltaTime) { }

    public virtual void FixedUpdate(float deltaTime) { }

    /// <summary>
    /// 角色使用技能的拦截回调
    /// </summary>
    /// <param name="controller"></param>
    /// <returns></returns>
    public virtual bool InterceptCharacterSkill(RGController controller, Action funcUseSkill) {
        bool interception = false; // 返回true拦截
        return interception;
    }

    /// <summary>
    /// 角色使用技能按钮弹起的拦截回调
    /// </summary>
    /// <param name="controller"></param>
    /// <returns></returns>
    public virtual bool InterceptCharacterSkillButtonUp(RGController controller, Action funcUseSkill) {
        bool interception = false; // 返回true拦截
        return interception;
    }

    /// <summary>
    /// 角色拾取武器的拦截回调
    /// </summary>
    /// <param name="controller"></param>
    /// <param name="weapon"></param>
    /// <param name="funcPickupWeapon"></param>
    /// <returns></returns>
    public virtual bool InterceptPickupWeapon(RGController controller, RGWeapon weapon, Action funcPickupWeapon) {
        return false;
    }

    /// <summary>
    /// 角色攻击的拦截回调
    /// </summary>
    /// <param name="controller"></param>
    /// <returns></returns>
    public virtual bool InterceptCharacterAttack(RGController controller, bool value) {
        return false;
    }

    /// <summary>
    /// 触碰/离开物体的拦截回调
    /// </summary>
    /// <param name="controller"></param>
    /// <param name="item"></param>
    /// <returns></returns>
    public virtual bool InterceptEnterOrExitItem(RGController controller, Transform item, Action funcEnterItem) {
        return false;
    }

    /// <summary>
    /// 在坐骑上切换武器的拦截回调
    /// </summary>
    /// <param name="mountController"></param>
    /// <param name="funcSwitchWeapon"></param>
    /// <returns></returns>
    public virtual bool InterceptMountSwitchWeapon(RGMountController mountController, Action funcSwitchWeapon) {
        return false;
    }

    public virtual IWindowPause GetWindowPause() {
        return null;
    }

    public virtual bool EnableDamageTextDisplaying() {
        return true;
    }

    public virtual bool CanResurrection() {
        return true;
    }

    public virtual GameObject ExpertBoxPotCreatePotion(Transform box) {
        if (box != null) {
            var tempObj =
                UnityEngine.Object.Instantiate(ResourcesUtil.Load("RGPrefab/LevelObject/Item/health_pot_box.prefab"),
                    GameUtil.TempRoot) as GameObject;
            if (tempObj != null) {
                tempObj.transform.position = box.transform.position;
                return tempObj;
            }
        }

        return null;
    }

    public virtual int BoxCrashDropItemRate() {
        var rate = 10;
        if (BattleData.data.HasBuff(emBuff.ExpertBoxPot)) {
            rate = 15;
            var overrideRate = BattleData.data.GetMark("BoxCrashDropItemRate");
            if (overrideRate > 0) {
                rate = overrideRate;
            }
            return rate + (int)ExtraBoxDropItemRate + DataUtil.CalBuffExpertBoxPotExtraRate();
        }

        return rate + (int)ExtraBoxDropItemRate;
    }

    public virtual string OnPreCreateNormalRoomChest(string prefabPath) {
        return prefabPath;
    }

    public virtual string OnPreCreateEliteRoomChest(string prefabPath) {
        return prefabPath;
    }

    public virtual string OnPreCreatBossChest(string prefabPath) {
        return prefabPath;
    }

    public virtual bool OnOverrideEnemyWave(RGRoomX room, int eliteProbability, BeforeCreatePartEvent waveInfo, ref int enemyPoints) {
        return false;
    }

    public virtual void OnStartTimeLimitEnemyWave(RGRoomX room, EnemyGenerator.EnemyMaker enemyMaker) { }

    public virtual float OnGetTimeLimitEnemyWaveProgress(RGRoomX room, EnemyGenerator.EnemyMaker enemyMaker, float time, float timeLimit) {
        return time / timeLimit;
    }

    public virtual void OnTimeLimitEnemyWaveEnd(RGRoomX room, EnemyGenerator.EnemyMaker enemyMaker) {  }

    public virtual IReadOnlyList<BeforeCreatePartEvent.EnemyData> OnGetTimeLimitEnemyWave(RGRoomX room, EnemyGenerator.EnemyMaker enemyMaker, float progress, float amount) {
        return null;
    }

    public virtual float OnOverrideRepelForce(float value) {
        return value;
    }

    public virtual Sprite OnOverrideRoomLogo(RGRoomX room, Sprite defaultLogo) {
        return defaultLogo;
    }


    private HashSet<Timer> _registeredTimer = new HashSet<Timer>();

    private void OnTimerExpire(Timer timer, Action func) {
        _registeredTimer.Remove(timer);
        try {
            func?.Invoke();
        } catch (Exception e) {
            Debug.LogError(e.Message);
            Debug.LogError(e.StackTrace);
        }
    }

    public Timer StartTimer(float delay, Action func, bool unscaleTime = false) {
        Timer timer = null;
        timer = Timer.Register(delay, false, unscaleTime, () => {
            OnTimerExpire(timer, func);
        });
        _registeredTimer.Add(timer);
        return timer;
    }

    public Timer StartTimer(float delay, Action<float> updateFunc, bool unscaleTime = false) {
        Timer timer = null;
        timer = Timer.Register(delay, false, unscaleTime, () => {
            OnTimerExpire(timer, null);
        }, updateFunc);
        _registeredTimer.Add(timer);
        return timer;
    }

    public void CancelTimer(Timer timer) {
        if (timer == null) {
            return;
        }

        _registeredTimer.Remove(timer);
        timer.Cancel();
    }

#if UNITY_EDITOR
    public virtual void DebugWeaponDamage9999() { }

    public virtual bool DebugGetWeapon(GameObject weaponobj) { return false; }

#endif
}