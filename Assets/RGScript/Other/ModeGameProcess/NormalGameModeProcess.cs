using MapSystem;
using RGScript.Data;
using RGScript.Other;
using UnityEngine;
using System.Collections.Generic;
using EnemySystem;

public interface INormalSeasonModeProcess {
    object TryCallMethod(string methodName, params object[] args);
    void Update(float deltaTime);
}



/// <summary>
/// 关卡模式的处理
/// </summary>
public class NormalGameModeProcess : BaseModeProcess {

    public interface IMapCreateCompleteHandler { 
        // public static void OnMapCreateComplete(MapManager);
    }

    private LevelBuffEnemySpawner _levelBuffEnemySpawner = new LevelBuffEnemySpawner();

    INormalSeasonModeProcess _seasonModeProcess;

    void TrySetupSeasonModeProcess() {
        if (BattleData.data.season != Season.None) {
            var seasonModeProcessType = System.Type.GetType($"{BattleData.data.season}SeasonGameProcess");
            if (seasonModeProcessType != null) {
                var seasonModeProcess = (INormalSeasonModeProcess)System.Activator.CreateInstance(seasonModeProcessType);
                if (seasonModeProcess != null) {
                    var res = seasonModeProcess.TryCallMethod("IsDisabled");
                    if (res == null || (res is bool b && !b)) {
                        _seasonModeProcess = seasonModeProcess;
                    }
                }
            }
        }
    }

    public override void GameProcessInit() {
        _seasonModeProcess = null;
        TrySetupSeasonModeProcess();
        if (_seasonModeProcess != null) {
            _seasonModeProcess.TryCallMethod(nameof(GameProcessInit));
        }
        base.GameProcessInit();

        SimpleEventManager.AddEventListener<MapCreateCompleteEvent>(OnMapCreateComplete);
    }

    public override void GameSceneStart() {
        base.GameSceneStart();
        if (_seasonModeProcess != null) {
            _seasonModeProcess.TryCallMethod(nameof(GameSceneStart));
        }
    }

    public override void CleanUp() {
        if (_seasonModeProcess != null) {
            _seasonModeProcess.TryCallMethod(nameof(CleanUp));
        }
        base.CleanUp();

        SimpleEventManager.RemoveEventListener<MapCreateCompleteEvent>(OnMapCreateComplete);
    }

    public override void ProcessLevelIndex(ref int levelIndex) {
        if (GameUtil.InReturnPlayerIntro()) {
            levelIndex = 0;
            DataMgr.ReturnPlayerData.ProcessReturnPlayerBattleFactor();
        }
        base.ProcessLevelIndex(ref levelIndex);
    }

    public override GameObject OnReplaceCreatingRoom(RGScript.Map.MapManagerLevel.RoomCreateType roomType, GameObject originRoomPrefab) {
        if (_seasonModeProcess != null) {
            var result = _seasonModeProcess.TryCallMethod(nameof(OnReplaceCreatingRoom), roomType, originRoomPrefab);
            if (result is GameObject) {
                return (GameObject)result;
            }
        }
        return base.OnReplaceCreatingRoom(roomType, originRoomPrefab);
    }

    public override bool NeedGotoStatement(int levelIndex) {
        if (BattleData.data.CompareFactor(emBattleFactor.Loop)) {
            return false;
        }

        var ret = levelIndex > LevelSelector.GetMaxLevelIndex(emGameMode.Normal) + 1;
        if (_seasonModeProcess != null) {
            var result = _seasonModeProcess.TryCallMethod(nameof(NeedGotoStatement), ret, levelIndex);
            if (result is bool) {
                ret = (bool)result;
            }
        }
        return ret;
    }

    public string GetMapManagerName(string branch, int levelIdx) {
        var ret = "map_" + branch + levelIdx;
        if (_seasonModeProcess != null) {
            var result = _seasonModeProcess.TryCallMethod(nameof(GetMapManagerName), ret, branch, levelIdx);
            if (result is string) {
                return (string)result;
            }
        }
        return ret;
    }

    public override void AfterCreateAllPlayers() {
        if (_seasonModeProcess != null) {
            _seasonModeProcess.TryCallMethod(nameof(AfterCreateAllPlayers));
        }
    }

    public override void AfterCharacterSetUp(RoleAttributePlayer playerAttribute) {
        if (_seasonModeProcess != null) {
            _seasonModeProcess.TryCallMethod(nameof(AfterCharacterSetUp), playerAttribute);
        }
    }

    public override void OnRoomObjectInstantiated(RGRoomX room) {
        if (_seasonModeProcess != null) {
            _seasonModeProcess.TryCallMethod(nameof(OnRoomObjectInstantiated), room);
        }
    }

    public override void OnStartRoom(RGRoomX room, int roomType) {
        if (_seasonModeProcess != null) {
            _seasonModeProcess.TryCallMethod(nameof(OnStartRoom), room, roomType);
        }
        if (!BattleData.data.IsLoopTravel) {
            _levelBuffEnemySpawner.OnStartRoom(roomType);
        }
    }

    public override void OnEnemyCreate(RGEController rgeController, RGRandom rgRandom, RGRoomXInfo roomXInfo) {
        if (_seasonModeProcess != null) {
            _seasonModeProcess.TryCallMethod(nameof(OnEnemyCreate), rgeController, rgRandom, roomXInfo);
        }
        base.OnEnemyCreate(rgeController, rgRandom, roomXInfo);
        if (!BattleData.data.IsLoopTravel) {
            _levelBuffEnemySpawner.OnEnemyCreate(rgeController, rgRandom, roomXInfo);
        }
    }

    public override string OnPreCreateNormalRoomChest(string prefabPath) {
        if (_seasonModeProcess != null) {
            prefabPath = (string)_seasonModeProcess.TryCallMethod(nameof(OnPreCreateNormalRoomChest), prefabPath);
        }
        return prefabPath;
    }

    public override string OnPreCreateEliteRoomChest(string prefabPath) {
        if (_seasonModeProcess != null) {
            prefabPath = (string)_seasonModeProcess.TryCallMethod(nameof(OnPreCreateEliteRoomChest), prefabPath);
        }
        return prefabPath;
    }

    public override string OnPreCreatBossChest(string prefabPath) {
        if (_seasonModeProcess != null) {
            prefabPath = (string)_seasonModeProcess.TryCallMethod(nameof(OnPreCreatBossChest), prefabPath);
        }
        return prefabPath;
    }

    public override bool OnOverrideEnemyWave(RGRoomX room, int eliteProbability, BeforeCreatePartEvent waveInfo, ref int enemyPoints) {
        if (_seasonModeProcess != null) {
            var res = _seasonModeProcess.TryCallMethod(nameof(OnOverrideEnemyWave), room, eliteProbability, waveInfo, enemyPoints);
            if (res != null && res is int) {
                enemyPoints = (int)res;
                return true;
            }
        } else if (EnemyWaveGenerator.Enabled()) {
            EnemyWaveGenerator.GlobalInstance.CreateNextWave(room, waveInfo);
            enemyPoints = EnemyWaveGenerator.GlobalInstance.RemainPoints(room);
            return true;
        }
        return false;
    }

    public override void OnStartTimeLimitEnemyWave(RGRoomX room, EnemyGenerator.EnemyMaker enemyMaker) {
        if (_seasonModeProcess != null) {
            _seasonModeProcess.TryCallMethod(nameof(OnStartTimeLimitEnemyWave), room, enemyMaker);
        }
    }

    public override float OnGetTimeLimitEnemyWaveProgress(RGRoomX room, EnemyGenerator.EnemyMaker enemyMaker, float time, float timeLimit) {
        if (_seasonModeProcess != null) {
            var res = _seasonModeProcess.TryCallMethod(nameof(OnGetTimeLimitEnemyWaveProgress), room, enemyMaker, time, timeLimit);
            if (res != null && res is float) {
                return (float)res;
            }
        }
        return time / timeLimit;
    }

    public override void OnTimeLimitEnemyWaveEnd(RGRoomX room, EnemyGenerator.EnemyMaker enemyMaker) {
        if (_seasonModeProcess != null) {
            _seasonModeProcess.TryCallMethod(nameof(OnTimeLimitEnemyWaveEnd), room, enemyMaker);
        }
    }

    public override IReadOnlyList<BeforeCreatePartEvent.EnemyData> OnGetTimeLimitEnemyWave(RGRoomX room, EnemyGenerator.EnemyMaker enemyMaker, float progress, float amount) {
        if (_seasonModeProcess != null) {
            var res = _seasonModeProcess.TryCallMethod(nameof(OnGetTimeLimitEnemyWave), room, enemyMaker, progress, amount);
            if (res != null && res is IReadOnlyList<BeforeCreatePartEvent.EnemyData> list) {
                return list;
            }
        }
        return null;
    }

    public override float OnOverrideRepelForce(float value) {
        if (_seasonModeProcess != null) {
            var res = _seasonModeProcess.TryCallMethod(nameof(OnOverrideRepelForce), value);
            if (res != null && res is float) {
                return (float)res;
            }
        }
        return value;
    }

    public override Sprite OnOverrideRoomLogo(RGRoomX room, Sprite defaultLogo) {
        if (_seasonModeProcess != null) {
            var res = _seasonModeProcess.TryCallMethod(nameof(OnOverrideRoomLogo), room, defaultLogo);
            if (res != null && res is Sprite) {
                return (Sprite)res;
            }
        }
        return defaultLogo;
    }

    public override void Update(float deltaTime) {
        if (_seasonModeProcess != null) {
            _seasonModeProcess.Update(deltaTime);
        }
    }

    public override string GetStatementSceneName() {
        if (_seasonModeProcess != null) {
            var res = _seasonModeProcess.TryCallMethod(nameof(GetStatementSceneName));
            if (res != null && res is string) {
                return (string)res;
            }
        }
        return RGGameConst.SCENE_STATEMENTS;
    }

    public virtual void OnMapCreateComplete(MapCreateCompleteEvent evData) {
        FuncObj.CallStaticMethodOfTypes<IMapCreateCompleteHandler>("OnMapCreateComplete", evData.mapManager);
    }
}