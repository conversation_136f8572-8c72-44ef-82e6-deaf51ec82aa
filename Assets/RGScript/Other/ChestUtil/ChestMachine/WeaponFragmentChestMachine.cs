using cfg.Store;
using cfg.WeaponUpgrade;
using IronTide;
using RGScript.Data;
using RGScript.Data.Mall;
using RGScript.WeaponEvolution;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace RGScript.Other.ChestUtil.ChestMachine {
    public class WeaponFragmentChestItem : IChestMachineItem<WeaponEvolutionConfig> {
        public WeaponFragmentChestItem(float weight, WeaponEvolutionConfig config, int maxCount, int roundMaxCount) {
            Data = config;
            Weight = weight;
            MaxCount = maxCount;
            RoundMaxCount = roundMaxCount;
        }

        public override string ToString() {
            return $"id: {Data}, max: {MaxCount}";
        }

        public float Weight { get; set; }

        /// <summary>
        /// 当前抽取最大数量 计算了精炼等级
        /// </summary>
        public int MaxCount { get; set; }

        /// <summary>
        /// 当前轮次最大数量
        /// </summary>
        public int RoundMaxCount { get; set; }

        public bool Ban { get; set; }
        public WeaponEvolutionConfig Data { get; set; }
        public bool IsGuarantee { get; set; }
    }


    /// <summary>
    /// 武器进化 武器碎片盲盒
    /// </summary>
    public class WeaponFragmentChestMachine : BaseChestMachine<WeaponEvolutionConfig> {
        private StoreGood _packageConfig;

        private Dictionary<WeaponRarity, List<WeaponEvolutionConfig>> _rarityGroupDict =
            new Dictionary<WeaponRarity, List<WeaponEvolutionConfig>>();
        private List<WeaponEvolutionConfig> _weaponFragmentList = new List<WeaponEvolutionConfig>();

        private Dictionary<WeaponRarity, float> _rarityGroupWeightDict = new Dictionary<WeaponRarity, float>();

        private void InitConstants(int goodId) {
            _packageConfig = DataMgr.ConfigData.Tables.TbGood[goodId];
            var itemLists = MallUtility.GetGoodItemList(_packageConfig);
            // Dictionary<WeaponRarity, List<string>> weaponRarityDict = new Dictionary<WeaponRarity, List<string>>();
            Dictionary<string, WeaponFragment> weaponFragmentDict = new Dictionary<string, WeaponFragment>();
            foreach (var fragment in DataMgr.ConfigData.Tables.TbWeaponFragment.DataList) {
                weaponFragmentDict[fragment.Item] = fragment;
            }

            // 初始化稀有度组的固定概率
            _rarityGroupWeightDict[WeaponRarity.White] = 30;
            _rarityGroupWeightDict[WeaponRarity.Green] = 25;
            _rarityGroupWeightDict[WeaponRarity.Blue] = 15;
            _rarityGroupWeightDict[WeaponRarity.Purple] = 15;
            _rarityGroupWeightDict[WeaponRarity.Orange] = 10;
            _rarityGroupWeightDict[WeaponRarity.Red] = 5;

            // 将碎片按稀有度分组
            foreach (var fragment in itemLists) {
                var fragmentName = fragment.id;
                if (!weaponFragmentDict.TryGetValue(fragmentName, out var weaponFragment)) {
                    Debug.LogError($"商城的武器碎片配置中找不到 {fragmentName} 的配置");
                    continue;
                }

                var rarity = (WeaponRarity)weaponFragment.Type;
                if (!_rarityGroupDict.ContainsKey(rarity)) {
                    _rarityGroupDict[rarity] = new List<WeaponEvolutionConfig>();
                }

                var weaponName = fragmentName.Replace(WeaponEvolutionModule.WeaponBlueprintFragmentPrefix, "");
                if (!WeaponEvolutionModule.ConfigDict.TryGetValue(weaponName, out var weaponConfig)) {
                    Debug.LogError($"WeaponEvolution配置找不到对应的武器 {weaponName}");
                    continue;
                }

                _rarityGroupDict[rarity].Add(weaponConfig);
                _weaponFragmentList.Add(weaponConfig);
            }
        }

        public WeaponFragmentChestMachine(RGRandom rgRandom, int goodId) {
            Init(rgRandom, goodId);
        }

        private RGRandom InitRandom(bool getResult = false) {
            int extraGap = 0;
            if (_packageConfig != null) {
                var id = _packageConfig.GoodID;
                extraGap = id - 7010000;
                extraGap *= 7;
            }

            return TreasureBoxData.GetRandom(extraGap, getResult);
        }

        private void Init(RGRandom rgRandom, int goodId) {
            RgRandom = InitRandom(false);
            _weaponFragmentList.Clear();
            _rarityGroupDict.Clear();
            InitConstants(goodId);
        }

        protected override float Range(float min, float max) {
            RgRandom = InitRandom(true);
            return base.Range(min, max);
        }

        protected override void OnGetResultSuccess(IChestMachineItem<WeaponEvolutionConfig> item) {
            base.OnGetResultSuccess(item);
        }

        public override IChestMachineItem<WeaponEvolutionConfig> GetResult() {
            // 第一步：筛选出至少有一个可获取碎片的稀有度组
            var availableRarityGroups = _rarityGroupDict.Keys
                .Where(rarity => HasAvailableFragmentInGroup(rarity))
                .ToList();

            if (availableRarityGroups.Count == 0) {
                Debug.LogError("没有可用的稀有度组，所有碎片均不可获取。");
                return null;
            }

            // 第二步：计算可用稀有度组的总权重
            float totalRarityWeight = availableRarityGroups.Sum(rarity => _rarityGroupWeightDict[rarity]);

            // 第三步：根据权重随机选择一个稀有度组
            float randomRarityValue = RgRandom.Range(0, totalRarityWeight);
            WeaponRarity selectedRarity = availableRarityGroups[0];
            foreach (var rarity in availableRarityGroups) {
                randomRarityValue -= _rarityGroupWeightDict[rarity];
                if (randomRarityValue <= 0) {
                    selectedRarity = rarity;
                    break;
                }
            }

            // 第四步：从选中的稀有度组中随机选择一个碎片
            var fragmentsInGroup = _rarityGroupDict[selectedRarity];
            var availableFragments = fragmentsInGroup.Where(f => !IsFragmentBanned(f)).ToList();

            if (availableFragments.Count == 0) {
                Debug.LogError($"选中的稀有度组 {selectedRarity} 中没有可获取的碎片。");
                return null;
            }

            Debug.Log("抽奖前 Random Seed: " + RgRandom.seed);

            // 计算组内可用碎片的总权重
            float totalFragmentWeight = availableFragments.Sum(f => GetWeight(f));
            float randomFragmentValue = Range(0, totalFragmentWeight);
            Debug.Log("抽奖后 Random Seed: " + RgRandom.seed);
            WeaponEvolutionConfig selectedFragment = null;
            foreach (var fragment in availableFragments) {
                randomFragmentValue -= GetWeight(fragment);
                if (randomFragmentValue <= 0) {
                    selectedFragment = fragment;
                    break;
                }
            }

            // 创建并返回结果
            var chestItem = new WeaponFragmentChestItem(
                GetWeight(selectedFragment),
                selectedFragment,
                GetMaxCountByGetLimitation(selectedFragment, GetTheoreticalMaxCount()),
                GetTheoreticalMaxCount()
            );
            OnGetResultSuccess(chestItem);
            return chestItem;
        }

        private List<WeaponDto> GetOwnedFragments() {
            List<WeaponDto> list = new List<WeaponDto>();
            foreach (WeaponDto value in DataMgr.WeaponEvolutionModule.GetAllWeapons()) {
                list.Add(value);
            }

            return list;
        }

        private List<WeaponEvolutionConfig> GetDroppableFragments() {
            return _weaponFragmentList;
        }

        public void InitAllWeights() {
            var droppable = GetDroppableFragments();
            foreach (var config in droppable) {
                // var fragmentName = WeaponEvolutionModule.WeaponBlueprintFragmentPrefix + config.WeaponName;
                var maxCount = GetTheoreticalMaxCount();
                AddHistoryItem(config, maxCount);
            }
        }

        private void AddHistoryItem(WeaponEvolutionConfig config, int maxCount, int count = 0) {
            float weight = GetWeight(config);
            if (weight > 0) {
                int maxCountByRefineLevel = GetMaxCountByGetLimitation(config, maxCount);
                int maxGetLeft = maxCount - count;
                int maxCanGetCount = Mathf.Min(maxCountByRefineLevel, maxGetLeft);
                if (maxCountByRefineLevel > 0) {
                    AddHistoryItem(new WeaponFragmentChestItem(weight, config, maxCanGetCount, maxCount), count);
                }
            }
        }

        private bool HasAvailableFragmentInGroup(WeaponRarity rarity) {
            return _rarityGroupDict[rarity].Any(f => !IsFragmentBanned(f));
        }

        private bool IsFragmentBanned(WeaponEvolutionConfig fragment) {
            var fragmentName = WeaponEvolutionModule.WeaponBlueprintFragmentPrefix + fragment.WeaponName;
            int canGetCount = MallUtility.GetWeaponFragmentMaterialCanGetCount(fragmentName, 1);
            return canGetCount <= 0;
        }

        public float GetWeight(WeaponEvolutionConfig config) {
            var fragmentName = WeaponEvolutionModule.WeaponBlueprintFragmentPrefix + config.WeaponName;
            var weaponFragment = DataMgr.ConfigData.Tables.TbWeaponFragment.DataList
                .FirstOrDefault(f => f.Item == fragmentName);
            if (weaponFragment != null) {
                return weaponFragment.Weight;
            }

            Debug.LogError($"没有找到 {fragmentName} 的权重");
            return 0;
        }

        /// <summary>
        /// 计算了最大可获取数量
        /// </summary>
        private int GetMaxCountByGetLimitation(WeaponEvolutionConfig config, int maxCount) {
            var weaponName = config.WeaponName;
            var haveWeapon = DataMgr.WeaponEvolutionModule.HasWeapon(weaponName);
            var configMaxCount = WeaponEvolutionModule.GetFragmentNeedCount(weaponName);
            if (!haveWeapon) {
                return Mathf.Min(maxCount, configMaxCount);
            }

            var ownedWeaponData = DataMgr.WeaponEvolutionModule.GetWeapon(weaponName);
            if (ownedWeaponData.IsMaxUpgradeLevel) {
                return 0;
            }

            var fragmentName = WeaponEvolutionModule.WeaponBlueprintFragmentPrefix + weaponName;
            var currentFragmentCount = ItemData.data.GetMaterialCount(fragmentName);
            int gap = configMaxCount - currentFragmentCount;
            if (gap <= 0) {
                return 0;
            }
            return Mathf.Min(maxCount, gap);
        }


        public void SetHistoryResult(Dictionary<WeaponEvolutionConfig, int> dictionary) {
            var maxCount = GetTheoreticalMaxCount();
            // 添加已经开过的 item
            foreach (var pair in dictionary) {
                var materialName = pair.Key;
                var count = pair.Value;
                AddHistoryItem(materialName, maxCount, count);
            }

            var droppable = GetDroppableFragments();

            // 添加没开过的item
            foreach (var config in droppable) {
                if (!dictionary.ContainsKey(config)) {
                    AddHistoryItem(config, maxCount);
                }
            }
        }

        public Dictionary<WeaponEvolutionConfig, int> GetHistoryResult() {
            var result = new Dictionary<WeaponEvolutionConfig, int>();
            foreach (var pair in IdCountDict) {
                var config = pair.Key;
                result.Add(config, pair.Value);
            }

            return result;
        }


        public int GetLeftOpenCount() {
            var droppable = GetDroppableFragments();
            int count = 0;
            foreach (var config in droppable) {
                var fragmentName = WeaponEvolutionModule.WeaponBlueprintFragmentPrefix + config.WeaponName;
                var canGetCount = MallUtility.GetWeaponFragmentMaterialCanGetCount(fragmentName);
                count += canGetCount;
            }

            return count;
        }


        /// <summary>
        /// 商店理论上每轮盲盒可以无限买
        /// </summary>
        /// <returns></returns>
        public int GetTheoreticalMaxCount() {
            return int.MaxValue;
        }

        public bool HasGetChip(WeaponEvolutionConfig chipName) {
            return IdCountDict.ContainsKey(chipName);
        }

        protected override void BanLogic(IChestMachineItem<WeaponEvolutionConfig> item) {
            WeaponFragmentChestItem chestItem = (WeaponFragmentChestItem)item;
            var condition1 = chestItem.RoundMaxCount != 0 && ItemCountDict[item] >= chestItem.RoundMaxCount;
            var condition2 = item.MaxCount != 0 && CurItemCountDict[item] >= item.MaxCount;
            if (condition1 || condition2) {
                BanItem(item);
                return;
            }

            var fragmentName = WeaponEvolutionModule.WeaponBlueprintFragmentPrefix + item.Data.WeaponName;
            int canGetCount = MallUtility.GetWeaponFragmentMaterialCanGetCount(fragmentName, 1);
            if (canGetCount <= 0) {
                BanItem(item);
            }
        }

//         public override bool DebugGetSpecificItem(out WeaponEvolutionConfig id) {
// #if UNITY_EDITOR
//             bool hasSet = UnityEditor.EditorPrefs.GetBool("WeaponFragmentDebugSetOpenChip", false);
//             ChipName findDebugChip = ChipName.None;
//             foreach (var item in ItemList) {
//                 bool isDebugOpenChip = hasSet && !item.Ban;
//                 if (isDebugOpenChip) {
//                     ChipName chip = (ChipName)UnityEditor.EditorPrefs.GetInt("WeaponFragmentDebugOpenChipName", 1000);
//                     if (chip != ChipName.None) {
//                         if (item.Data == chip) {
//                             findDebugChip = chip;
//                             break;
//                         }
//                     }
//                 }
//             }
//
//             id = findDebugChip;
//             return findDebugChip != ChipName.None;
// #else
//             id = default;
//             return false;
// #endif
//         }
    }
}