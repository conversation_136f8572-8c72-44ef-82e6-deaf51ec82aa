using GG.Extensions;
using System.Collections;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class RGJoyStick : MonoBehaviour {
    public enum JoyStickId {
        None,
        Left,
        Right
    }

    public JoyStickId joyStickId = JoyStickId.Left;
    public float joyStickRadius; //摇杆的半径，世界空间中的半径
    public float maxStaticTouchRange = 3;
    public float maxStaticMoveRange = 2;
    public bool hideStaticStickWhenNoInput = false;
    private float _staticStickContinuousInputTime = 0;
    private Transform centerBall; //摇杆中心球
    private int lastFingerID = -1; //最后一次触摸的手指id
    private bool centerBallMoving = false; //摇杆中心球移动开关
    private float move_value = 0;
    private Image img_ball;
    private Image img_bg;
    private RectTransform bg_recttransform;
    private bool free_mode = true;
    private bool axis_mode = true; //轴模式，true 不限制，false 8轴
    private bool track_finger = false;
    private StandaloneInputModule inputModule;

    /// <summary>
    /// 是否暂停了输入
    /// </summary>
    private bool _isPauseInput = false;

    public int LastFingerId {
        get {
            return lastFingerID;
        }
    }

    void Awake() {
        img_ball = transform.GetComponent<Image>();
        img_bg = transform.parent.GetComponent<Image>();
        centerBall = transform;
        bg_recttransform = transform.parent.GetComponent<RectTransform>();
        inputModule = GameObject.FindObjectOfType<StandaloneInputModule>();
    }

    void Start() {
        if (hideStaticStickWhenNoInput) {
            img_ball.enabled = img_bg.enabled = false;
        }
    }

    // void FixedUpdate() {
    void Update() {
        if (_isPauseInput) {
            return;
        }
        
        if (_onGetInput != null && joyStickId != JoyStickId.None) {
            if (free_mode /* && !BattleData.data.IsSandboxEditing*/ && joyStickId != JoyStickId.Right)
                JoyStickFree();
            else {
                var hasInput = JoyStickStatic();
                if (hideStaticStickWhenNoInput) {
                    img_ball.enabled = img_bg.enabled = hasInput || _staticStickContinuousInputTime > 0.5f;
                }
            }
        }
    }

    private System.Action<MovementInput> _onGetInput;

    public void SetInputCallback(System.Action<MovementInput> onGetInput) {
        _onGetInput = onGetInput;
    }


    public void UnsetCallback() {
        _onGetInput = null;
    }

    private Coroutine _pauseCoroutine;

    /// <summary>
    /// 暂停输入
    /// </summary>
    public void PauseInput() {
        if (_isPauseInput) {
            return;
        }

        lastFingerID = -1;
        _isPauseInput = true;
        _pauseCoroutine = StartCoroutine(Pause());
    }

    private IEnumerator Pause() {
        Input.ResetInputAxes();
        yield return new WaitForEndOfFrame();
    }

    /// <summary>
    /// 暂停输入恢复
    /// </summary>
    public void ContinueInput() {
        if (!_isPauseInput) {
            return;
        }

        StopCoroutine(_pauseCoroutine);
        _isPauseInput = false;
    }

    bool IsTouchStartPositionValid(Vector2 pos) {
        if (joyStickId == JoyStickId.Left) {
            return pos.x < Screen.width / 3;
        }

        return pos.x > Screen.width * 0.5f;
    }

    Vector2 _beginTouchPosition;

    void JoyStickFree() {
        int count = Input.touchCount; //获取触摸数量

        bool keepTouching = false;
        for (int i = 0; i < count; i++) //逐个分析触摸
        {
            Touch touch = Input.GetTouch(i);
            if (touch.fingerId == lastFingerID) {
                keepTouching = true;
            }
        }

        for (int i = 0; i < count; i++) //逐个分析触摸
        {
            Touch touch = Input.GetTouch(i);
            Vector2 touch_position = touch.position;
            if (!keepTouching && touch.phase == TouchPhase.Began) {
                if (IsTouchStartPositionValid(touch.position)) {
                    Vector3 p = touch.position;
                    bg_recttransform.position = new Vector3(p.x, p.y, bg_recttransform.position.z);
                    _beginTouchPosition = touch_position;
                    lastFingerID = touch.fingerId; //记录该触摸的id
                    centerBallMoving = true; //摇杆中心球移动开关打开
                }
            }

            Vector2 temp = touch_position - (Vector2)bg_recttransform.position;
            if (touch.fingerId == lastFingerID && centerBallMoving) {
                if (Mathf.Round(temp.magnitude) <= joyStickRadius) {
                    Vector2 v = touch_position; // drop compoennt z
                    centerBall.GetComponent<RectTransform>().position = v;
                } else {
                    // 超出摇杆跟手移动
                    if (keepTouching && track_finger) {
                        Vector3 p = touch.position;
                        Vector3 tmp_vec3 = bg_recttransform.position - p;
                        tmp_vec3 = p + tmp_vec3.normalized * joyStickRadius * 1.01f;
                        bg_recttransform.position = new Vector3(tmp_vec3.x, tmp_vec3.y, bg_recttransform.position.z);
                    } //else

                    {
                        centerBall.GetComponent<RectTransform>().localPosition = joyStickRadius * temp.normalized;
                    }
                }

                move_value = Mathf.Round(temp.magnitude) / joyStickRadius * 2;
                if (move_value > 1) {
                    move_value = 1;
                }

                if (move_value > 0.2f) {
                    if (axis_mode) {
                        _onGetInput?.Invoke(new MovementInput {
                            dir = temp, aimDir = (touch_position - _beginTouchPosition).normalized,
                            weight = move_value, touchStartPos = _beginTouchPosition, touchPos = touch_position,
                            touchCenterDir = touch_position - (Vector2)transform.parent.position
                        });
                    } else {
                        _onGetInput?.Invoke(new MovementInput {
                            dir = ClampVec8Way(temp), aimDir = (touch_position - _beginTouchPosition).normalized,
                            weight = move_value, touchStartPos = _beginTouchPosition, touchPos = touch_position,
                            touchCenterDir = touch_position - (Vector2)transform.parent.position
                        });
                    }
                }

                //当释放触摸的时候中心球位置重置
                if (touch.phase == TouchPhase.Ended || touch.phase == TouchPhase.Canceled) {
                    centerBall.GetComponent<RectTransform>().localPosition = new Vector3(0, 0, 0);
                    _onGetInput?.Invoke(new MovementInput
                    {
                        dir = Vector2.zero, aimDir = Vector2.zero, weight = 0.0f, end = true,
                        touchCenterDir = Vector2.zero
                    });
                    centerBallMoving = false;
                    lastFingerID = -1;
                    move_value = 0;
                }
            }
        }
    }

    bool JoyStickStatic() {
        var hasInput = false;
        int count = Input.touchCount; //获取触摸数量

        bool keepTouching = false;
        for (int i = 0; i < count; i++) //逐个分析触摸
        {
            Touch touch = Input.GetTouch(i);
            if (touch.fingerId == lastFingerID) {
                keepTouching = true;
            }
        }

        for (int i = 0; i < count; i++) //逐个分析触摸
        {
            Touch touch = Input.GetTouch(i);
            Vector2 touch_position = touch.position;
            Vector2 temp = touch_position - (Vector2)bg_recttransform.position;
            if (!keepTouching && touch.phase == TouchPhase.Began) {
                if (Mathf.Round(temp.magnitude) <= joyStickRadius * maxStaticTouchRange /*joyStickRadius*2*/) {
                    lastFingerID = touch.fingerId; //记录该触摸的id
                    centerBallMoving = true; //摇杆中心球移动开关打开
                    _beginTouchPosition = touch_position;
                }
            }

            if (touch.fingerId == lastFingerID && centerBallMoving) {
                _staticStickContinuousInputTime += Time.deltaTime;
                if (Mathf.Round(temp.magnitude) <= joyStickRadius) {
                    move_value = Mathf.Round(temp.magnitude) / joyStickRadius * maxStaticMoveRange;
                    if (move_value > 1) {
                        move_value = 1;
                    }

                    Vector2 v = touch_position; // drop compoennt z
                    centerBall.GetComponent<RectTransform>().position = v;
                } else {
                    move_value = 1;
                    centerBall.GetComponent<RectTransform>().localPosition = joyStickRadius * temp.normalized;
                }

                if (move_value > 0.2f) {
                  
                    if (axis_mode) {
                        _onGetInput?.Invoke(new MovementInput {
                            dir = temp, aimDir = (touch_position - _beginTouchPosition).normalized,
                            weight = move_value, touchStartPos = _beginTouchPosition, touchPos = touch_position,
                            touchCenterDir = touch_position - (Vector2)transform.parent.position
                        });
                    } else {
                        _onGetInput?.Invoke(new MovementInput {
                            dir = ClampVec8Way(temp), aimDir = (touch_position - _beginTouchPosition).normalized,
                            weight = move_value, touchStartPos = _beginTouchPosition, touchPos = touch_position,                            
                            touchCenterDir = touch_position - (Vector2)transform.parent.position
                        });
                    }
                    hasInput = true;
                }
                //当释放触摸的时候中心球位置重置

                if (touch.phase == TouchPhase.Ended || touch.phase == TouchPhase.Canceled) {
                    centerBall.GetComponent<RectTransform>().localPosition = new Vector3(0, 0, 0);
                    _onGetInput?.Invoke(new MovementInput
                    {
                        dir = Vector2.zero, aimDir = Vector2.zero, weight = 0.0f, end = true,
                        touchCenterDir = Vector2.zero
                    });
                    centerBallMoving = false;
                    lastFingerID = -1;
                    _staticStickContinuousInputTime = 0;
                }
            }
        }
        return hasInput;
    }

#if UNITY_EDITOR
    public void OnBeginDrag(Vector2 pos) {
        centerBallMoving = true; //摇杆中心球移动开关打开
        _beginTouchPosition = pos;
        img_ball.enabled = img_bg.enabled = true;
        hideStaticStickWhenNoInput = false;
    }
    public void OnDrag(Vector2 pos) {
        var temp = pos - (Vector2)bg_recttransform.position;
        _staticStickContinuousInputTime += Time.deltaTime;
        if (Mathf.Round(temp.magnitude) <= joyStickRadius) {
            move_value = Mathf.Round(temp.magnitude) / joyStickRadius * maxStaticMoveRange;
            if (move_value > 1) {
                move_value = 1;
            }

            Vector2 v = pos; // drop compoennt z
            centerBall.GetComponent<RectTransform>().position = v;
        } else {
            move_value = 1;
            centerBall.GetComponent<RectTransform>().localPosition = joyStickRadius * temp.normalized;
        }

        if (move_value > 0.2f) {
            if (axis_mode) {
                _onGetInput?.Invoke(new MovementInput {
                    dir = temp, aimDir = (pos - _beginTouchPosition).normalized,
                    weight = move_value, touchStartPos = _beginTouchPosition, touchPos = pos,
                    touchCenterDir = pos - (Vector2)transform.parent.position
                });
            } else {
                _onGetInput?.Invoke(new MovementInput {
                    dir = ClampVec8Way(temp), aimDir = (pos - _beginTouchPosition).normalized,
                    weight = move_value, touchStartPos = _beginTouchPosition, touchPos = pos,
                    touchCenterDir = pos - (Vector2)transform.parent.position
                });
            }
        }
    }

    public void OnDragEnd() {
        //当释放触摸的时候中心球位置重置
        centerBall.GetComponent<RectTransform>().localPosition = new Vector3(0, 0, 0);
        _onGetInput?.Invoke(new MovementInput {
            dir = Vector2.zero, aimDir = Vector2.zero, weight = 0.0f, end = true,
            touchCenterDir = Vector2.zero
        });
        centerBallMoving = false;
        lastFingerID = -1;
        _staticStickContinuousInputTime = 0;
        hideStaticStickWhenNoInput = true;
    }
#endif

    public void ResetInput() {
        centerBall.GetComponent<RectTransform>().localPosition = new Vector3(0, 0, 0);
        _onGetInput?.Invoke(new MovementInput
        {
            dir = Vector2.zero, aimDir = Vector2.zero, weight = 0.0f, end = true,
            touchCenterDir = Vector2.zero
        });
        centerBallMoving = false;
        lastFingerID = -1;
    }

    public void SwitchControl(int value) {
        Vector2 pixelInRef = value == 0 ? new Vector2(260, 140) : SettingData.data.movePosition;
        free_mode = (value == 0 || value == 2);
        track_finger = (value == 2);
        if (free_mode) {
            bg_recttransform.RectTransform().anchoredPosition = pixelInRef;
        } else {
            bg_recttransform.RectTransform().anchoredPosition = pixelInRef;
            img_ball.enabled = true;
            img_bg.enabled = true;
        }
    }

    public void SwitchAxis(int value) {
        axis_mode = (value == 0);
    }

    static Vector2[] MoveTable = {
        Vector2.left,
        new Vector2(-1.0f, -1.0f).normalized,
        Vector2.down,
        new Vector2(1.0f, -1.0f).normalized,
        Vector2.right,
        new Vector2(1.0f, 1.0f).normalized,
        Vector2.up,
        new Vector2(-1.0f, 1.0f).normalized,
    };

    static float SignedAngle(Vector2 a, Vector2 b) {
        return Mathf.DeltaAngle(Mathf.Atan2(a.y, a.x) * Mathf.Rad2Deg,
            Mathf.Atan2(b.y, b.x) * Mathf.Rad2Deg);
    }

    public static Vector2 ClampVec8Way(Vector2 v) {
        float fullAngle = SignedAngle(Vector2.right, v);
        int index = (int)((fullAngle + 202.5f) / 45.0f) % 8;
        return MoveTable[index];
    }

    #if UNITY_EDITOR
    [Sirenix.OdinInspector.Button]
    public void TestInput(Vector2 dir, float weight, bool end) {
        _onGetInput?.Invoke(new MovementInput { dir = dir, aimDir = dir, weight = weight, end = end });
    }
    #endif
}