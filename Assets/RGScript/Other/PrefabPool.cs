using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using UnityEngine;

// ReSharper disable once CheckNamespace
public interface IPrefabPoolObject {
	void OnTaken();
}

public class PrefabPoolObjectData {
    public GameObject OriginalBody;
    public bool IsUsing;
}

public class PrefabPool : Singleton<PrefabPool> {
    private readonly Dictionary<GameObject, Stack<GameObject>> _unusedGameObjects = new();
    private readonly Dictionary<GameObject, PrefabPoolObjectData> _objectDataDict = new();
    private readonly Dictionary<Type, List<FieldInfo>> _cachedFields = new();

    private void Start() {
        SimpleEventManager.AddEventListener<EnemyCreateEvent>(OnEnemyCreate);
        SimpleEventManager.AddEventListener<WeaponPickedUpEvent>(OnWeaponPickedUp);
    }

    private void CacheBullet(GameObject bullet, bool isEnemyBullet) {
        if (!bullet) {
            return;
        }

        // Has cached
        if (_unusedGameObjects.ContainsKey(bullet) && _unusedGameObjects[bullet].Count > 0) {
            return;
        }

        var damageCarrier = bullet.GetComponent<DamageCarrier>();
        if (!damageCarrier) {
            return;
        }

        // Player melee: 4 remote: 8
        // Enemy melee: 8 remote: 16
        int preAllocBulletNum = damageCarrier.HasDamageType(emDamageType.Melle) ? 4 : 8;
        if (isEnemyBullet) {
            preAllocBulletNum *= 2;
        }

        _unusedGameObjects[bullet] = new Stack<GameObject>();
        for (int i = 0; i < preAllocBulletNum; ++i) {
            var cache = Instantiate(bullet, this.transform, true);
            _unusedGameObjects[bullet].Push(cache);
            cache.SetActive(false);
        }
    }

    private bool CheckFieldAndCacheBullet(RGEController controller, FieldInfo fieldInfo) {
        if (!fieldInfo.Name.Contains("bullet")) {
            return false;
        }

        try {
            var bullet = fieldInfo.GetValue(controller);
            if (bullet is not GameObject o) {
                return false;
            }

            var damageCarrier = o.GetComponent<DamageCarrier>();
            if (!damageCarrier) {
                return false;
            }

            CacheBullet(o, true);
        } catch (UnassignedReferenceException) {
        }

        return true;
    }

    private void OnEnemyCreate(EnemyCreateEvent e) {
        var enemy = e.enemy;

        // Bullet on a weapon
        var weaponNode = enemy.transform.Find("img/h1/weapon");
        if (weaponNode) {
            var weapon = weaponNode.GetComponent<RGEWeapon>();
            if (weapon) {
                CacheBullet(weapon.bullet, true);
                return;
            }
        }

        // Bullet on a script
        var controller = enemy.transform.GetComponent<RGEController>();
        var type = controller.GetType();

        if (_cachedFields.TryGetValue(type, out List<FieldInfo> fieldInfos)) {
            // List<FieldInfo>
            foreach (var fieldInfo in fieldInfos) {
                CheckFieldAndCacheBullet(controller, fieldInfo);
            }
        } else {
            // FieldInfo[]
            List<FieldInfo> fieldInfoCache = null;
            foreach (var fieldInfo in type.GetFields()) {
                if (!CheckFieldAndCacheBullet(controller, fieldInfo)) {
                    continue;
                }

                fieldInfoCache ??= new List<FieldInfo>();
                fieldInfoCache.Add(fieldInfo);
            }

            if (fieldInfoCache != null) {
                _cachedFields[type] = fieldInfoCache;
            }
        }
    }

    private void OnWeaponPickedUp(WeaponPickedUpEvent e) {
        var bulletsInfo = e.weapon.bulletsInfo;
        if (bulletsInfo == null) {
            return;
        }

        foreach (var bulletsInf in bulletsInfo) {
            CacheBullet(bulletsInf.bulletProto, false);
        }
    }

    public void Reset() {
        Debug.Log("Prefab Pool Reset");
        
        foreach (var o in _unusedGameObjects.SelectMany(pair => pair.Value)) {
            if (o) {
                Destroy(o);
            }
        }
        foreach ((GameObject o, _) in _objectDataDict) {
            if (o) {
                Destroy(o);
            }
        }
        
        _unusedGameObjects.Clear();
        _objectDataDict.Clear();
        _cachedFields.Clear();
    }

    public void ClearUnused<T>() where T : MonoBehaviour {
        var unused = new List<GameObject>();
        var removes = new List<GameObject>();
        foreach (var pair in _unusedGameObjects) {
            bool remove = false;
            // ReSharper disable once ForeachCanBePartlyConvertedToQueryUsingAnotherGetEnumerator
            foreach (var o in pair.Value) {
                // ReSharper disable once InvertIf
                if (o && o.GetComponent<T>()) {
                    unused.Add(o);
                    remove = true;
                }
            }

            if (remove) {
                removes.Add(pair.Key);
            }
        }

        foreach (var o in removes) {
            _unusedGameObjects.Remove(o);
        }

        foreach (var o in unused) {
            Debug.Log($"PrefabPool destroying unused object:{o.name}");
            _objectDataDict.Remove(o);
            Destroy(o);
        }
    }

    public void ClearAllUsing() {
        var usingObject = new List<GameObject>();
        // ReSharper disable once ForeachCanBeConvertedToQueryUsingAnotherGetEnumerator
        foreach (var pair in _objectDataDict) {
            if (pair.Value.IsUsing) {
                usingObject.Add(pair.Key);
            }
        }
        
        foreach (var o in usingObject) {
            _objectDataDict.Remove(o);
            Destroy(o);
        }
    }

    public void ClearInvalidObjects() {
        List<GameObject> invalidUsingGo = (from pair in _objectDataDict where !pair.Key select pair.Key).ToList();
        List<GameObject> invalidUnusedGo = (from pair in _unusedGameObjects where !pair.Key select pair.Key).ToList();
        foreach (var o in invalidUsingGo) {
            _objectDataDict.Remove(o);
            Destroy(o);
        }
        foreach (var o in invalidUnusedGo) {
            _unusedGameObjects.Remove(o);
            Destroy(o);
        }
    }

    static bool UsePool(GameObject origin) {
        if (!origin) {
            return false;
        }

        var bullet = origin.GetComponent<RGBullet>();
        if (!bullet) {
            return true;
        }

        if (!bullet.awake || origin.transform.childCount != 1) {
            return false;
        }

        var trigger = origin.transform.GetChild(0).GetComponent<RGBulletTrigger>();
        if (trigger) {
            return (trigger.destory_parent) && (!trigger.stop_parent);
        }

        return true;
    }

    public GameObject Take(
        GameObject origin, Vector3 position, Quaternion rotation, Transform parent, bool invokeOnTake = true) {
        var o = Take(origin, position, rotation, invokeOnTake);
        if (o) {
            o.transform.parent = parent;
        }

        return o;
    }

    public GameObject Take(
        GameObject origin, Vector3 position, Vector3 rotation, Transform parent, float lifetime = 0) {
        var o = Take(origin, position, Quaternion.Euler(rotation));
        if (!o) {
            return o;
        }

        o.transform.parent = parent;
        if (lifetime > 0) {
            Store(o, lifetime);
        }
        return o;
    }

    public GameObject Take(GameObject origin, Transform parent) {
        var o = Take(origin, Vector3.zero, Quaternion.identity);
        if (o) {
            o.transform.parent = parent;
        }
        return o;
    }

    public GameObject Take(GameObject origin, Vector3 position, Quaternion rotation, float lifetime) {
        var o = Take(origin, position, rotation);
        if (o && lifetime > 0) {
            Store(o, lifetime);
        }
        return o;
    }

    private static GameObject PopObjectFromStack(Stack<GameObject> stack) {
        if (stack == null) {
            return null;
        }
        
        while (stack.Count > 0) {
            var retObject = stack.Pop();
            if (retObject) {
                return retObject;
            }
        }

        return null;
    }

    public GameObject Take(GameObject origin, Vector3 position, Quaternion rotation, bool invokeOnTake = true) {
        GameObject retObject = null;
        if (UsePool(origin)) {
            if (_unusedGameObjects.TryGetValue(origin, out Stack<GameObject> stack)) {
                if (stack.Count > 0) {
                    retObject = PopObjectFromStack(stack);
                    if (retObject) {
                        retObject.transform.localPosition = position;
                        retObject.transform.rotation = rotation;
                        retObject.transform.localScale = origin.transform.localScale;
                        retObject.SetActive(true);
                        if (origin.GetComponent<RGBullet>() is { awake: true })
                            retObject.GetComponent<RGBullet>().awake = true;
                    } else {
                        retObject = null;
                    }
                }
            } else {
                _unusedGameObjects[origin] = new Stack<GameObject>();
            }

            if (!retObject) {
                retObject = Instantiate(origin, position, rotation);
            }
            
            // Record data
            if (!_objectDataDict.TryGetValue(retObject, out PrefabPoolObjectData data)) {
                data = new PrefabPoolObjectData {
                    OriginalBody = origin,
                    IsUsing = true
                };
                _objectDataDict.Add(retObject, data);
            } else {
                data.IsUsing = true;
            }
            
            if (!invokeOnTake) {
                return retObject;
            }

            InvokeTaken(retObject);
        } else {
            retObject = Instantiate(origin, position, rotation);
        }

        return retObject;
    }

    public static void InvokeTaken(GameObject obj) {
        if (obj == null) {
            return;
        }
        foreach (var item in obj.GetComponentsInChildren<IPrefabPoolObject>()) {
            item.OnTaken();
        }
    }

    public GameObject Take(GameObject origin, bool invokeOnTake = true) {
        return Take(origin, Vector3.zero, Quaternion.identity, invokeOnTake);
    }

    public IEnumerator Cache(GameObject origin, int count) {
        if (count <= 0) {
            yield break;
        }

        var caches = new GameObject[count];
        for (int i = 0; i < count; i++) {
            caches[i] = Take(origin, new Vector3(999999999, 999999999), Quaternion.identity, false);
            yield return null;
        }
        for (int i = 0; i < count; i++) {
            Store(caches[i]);
        }
    }

    public void Store(GameObject target) {
        if (target == null) {
            return;
        }
        
        if (!_objectDataDict.TryGetValue(target, out PrefabPoolObjectData data)) {
            Destroy(target);
            return;
        }

        if (!data.IsUsing) {
            return;
        }
        
        if (!_unusedGameObjects.TryGetValue(data.OriginalBody, out Stack<GameObject> objectStack)) {
            Debug.LogError($"Store a error object {target.name}");
            return;
        }

        data.IsUsing = false;
        objectStack.Push(target);
        target.SetActive(false);
        target.transform.SetParent(this.transform);

        if (target.GetComponent<RGBullet>()) {
            target.GetComponent<RGBullet>().CancelInvoke();
        }
    }

    public void Store(GameObject target, float delay) {
        if (delay >= 0) {
            StartCoroutine(RawInstance.DelayStore(target, delay));
        }
    }

    IEnumerator DelayStore(GameObject obj, float delay) {
        yield return new WaitForSeconds(delay);
        if (obj) {
            var psList = obj.GetComponentsInChildren<ParticleSystem>();
            foreach (var ps in psList) {
                ps.Clear();
            }
        }
        Store(obj);
    }

    public static void SafeStore(GameObject target, float delay = 0) {
        if (RawInstance && target) {
            RawInstance.Store(target, delay);
        }
    }

    public static void SafeStoreEfx(GameObject target, float delay = 0) {
        if (target) {
            var psList = target.GetComponentsInChildren<ParticleSystem>();
            foreach (var ps in psList) {
                ps.Stop();
            }
        }
        SafeStore(target, delay);
    }

    public void RemoveUsingObject(GameObject target) {
        if (_objectDataDict.Any(pair => pair.Key == target && pair.Value.IsUsing)) {
            _objectDataDict.Remove(target);
        }
    }
    
    public static GameObject CreateObjectInParent(GameObject prefab, Transform parent, Vector3 localPos,
        bool unique = true, float destroyDelay = -1) {
        if (!prefab) return null;
        if (unique && parent.transform.Find(prefab.name)) return null;
        var obj = Inst.Take(prefab, parent.position, Quaternion.Euler(0, 0, 0), destroyDelay);
        if (!obj) {
            return obj;
        }

        obj.transform.SetParent(parent);
        obj.transform.localPosition = localPos;
        return obj;
    }

    public static void RemoveObjectInParent(GameObject prefab, Transform parent) {
        if (!prefab) {
            return;
        }
        
        if (parent.transform.Find(prefab.name) is { } trans) {
            Inst.Store(trans.gameObject);
        }
    }
}
