using RGScript.Config.Manager;
using RGScript.Util.LifeCycle;
using ModeSeason.ComboGun;
using RGScript.Data;
using RGScript.Data.Mall;
using RGScript.Util.LifeCycle;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Globalization;
using UnityEngine;
using UnityEngine.SceneManagement;
using Util.TAUtil;

public partial class RestoreRole {
    public static Dictionary<string, int> key2Hero = new Dictionary<string, int>() {
        {"engineer", 5},
        {"vampire", 6},
        {"paladin", 7},
        {"werewolf", 9},
        {"druid", 11},
        {"viking", 13},
        {"necromancer", 14}, //角色 死灵法师
        {"taoist", 16}, //角色 道士
        {"warlock", 23}, //角色 恶魔术士
        {"trapmaster", 25}, //角色 陷阱大师
        {"doctor", 27}, //角色 博士
        {"lancer", 29}, //角色 枪客
        {"astrologist", 32}, //角色 占星师
        {"shooter", 36}, //角色 神枪手
        {"aigirl", 37}, //角色 机械姬
    };

    public static Dictionary<string, string> key2Skin = new Dictionary<string, string>() {
        {"vampire_skin_2", "6-2"},
        {"paladin_skin_2", "7-2"},
        {"elf_skin_2", "8-2"},
        {"werewolf_skin_2", "9-2"},
        {"pastor_skin_2", "10-2"},
        {"druid_skin_2", "11-2"},

        {"ranger_skin_4", "1-4"},
        {"rogue_skin_4", "1-4"},
        {"wizard_skin_5", "2-5"},
        {"assassin_skin_3", "3-3"},
        {"alchemist_skin_3", "4-3"},
        {"engineer_skin_3", "5-3"},
        {"paladin_skin_4", "7-4"},
        {"elf_skin_3", "8-3"},
        {"werewolf_skin_5", "9-5"},
        {"pastor_skin_3", "10-3"},
        {"druid_skin_4", "11-4"},

        {"werewolf_skin_7", "9-7"},
        {"viking_skin_1", "13-1"},

        {"rogue_skin_5", "1-5"},
        {"wizard_skin_6", "2-6"},
        {"assassin_skin_6", "3-6"},
        {"engineer_skin_6", "5-6"},
        {"vampire_skin_6", "6-6"},
        {"elf_skin_5", "8-5"},
        {"werewolf_skin_8", "9-8"},
        {"druid_skin_5", "11-5"},
        {"viking_skin_3", "13-3"},
        {"knight_skin_6", "0-6"},


        {"knight_skin_8", "0-8"}, //骑士皮肤 大骑士
        {"robot_skin_4", "12-4"}, //机器人皮肤 巨像 祖兰
        {"viking_skin_5", "13-5"}, //狂战士皮肤 李小龙
        {"alchemist_skin_5", "4-5"}, //炼金皮肤 瓦克恩头目

        {"assassin_skin_7", "3-7"}, //刺客皮肤 骷髅王
        {"engineer_skin_7", "5-7"}, //工程师 镭射幽浮
        {"druid_skin_6", "11-6"}, //德鲁伊 年兽

        {"paladin_skin_9", "7-9"}, //3周年，圣骑士 15
        {"rogue_skin_7", "1-7"}, //3周年，游侠 15
        {"assassin_skin_8", "3-8"}, //3周年，刺客 15
        {"elf_skin_7", "8-7"}, //3周年，精灵 15
        {"wizard_skin_9", "2-9"}, //3周年，法师 15
        {"pastor_skin_7", "10-7"}, //3周年，牧师 15
        {"vampire_skin_8", "6-8"}, //3周年，吸血鬼 15
        {"druid_skin_7", "11-7"}, //3周年，德鲁伊 15
        {"alchemist_skin_6", "4-6"}, //3周年，炼金术士 15

        {"rogue_skin_8", "1-8"}, //抽象派，游侠 10
        {"necromancer_skin_4", "14-4"}, //三周年, 死灵法15
        {"officer_skin_2", "15-2"}, //锦衣卫, 警官15
        {"taoist_skin_3", "16-3"}, //天君, 道士15

        {"viking_skin_10", "13-10"}, //狂战士 皇帝的新衣
        {"elf_skin_8", "8-8"}, //精灵 童话皮肤 梅糖仙子
        //2021春节
        {"viking_skin_11", "13-11"}, //4周年 狂战士  武松
        {"paladin_skin_10", "7-10"}, //4周年 圣骑士 关羽
        {"rogue_skin_10", "1-10"}, //4周年 游侠 川剧变脸
        {"vampire_skin_9", "6-9"}, //4周年 吸血鬼 黑白无常
        {"werewolf_skin_13", "9-13"}, //4周年 哪吒 狼人
        {"necromancer_skin_5", "14-5"}, //4周年 青蛇 死灵法师
        {"pastor_skin_10", "10-10"}, //4周年 白蛇 牧师
        {"taoist_skin_4", "16-4"}, //4周年 太上老君 道士
        {"assassin_skin_10", "3-10"}, //4周年 伍子胥 刺客

        //马戏团惊魂夜
        {"robot_skin_10", "12-10"}, //马戏团惊魂夜 提线木偶 机器人
        {"engineer_skin_11", "5-11"}, //马戏团惊魂夜 小丑 工程师

        //星辰大海 2021暑假
        {"taoist_skin_7", "16-7"}, //雷霆 道士
        {"assassin_skin_12", "3-12"}, //欺诈 刺客
        {"werewolf_skin_14", "9-14"}, //天狼 狼人
        {"vampire_skin_10", "6-10"}, //湮灭 吸血鬼
        {"elf_skin_10", "8-10"}, //人鱼公主 精灵
        {"officer_skin_6", "15-6"}, //幽灵船长 警官
        {"engineer_skin_12", "5-12"}, //罐装大王 工程师

        //蒸汽时代 2021国庆
        {"paladin_skin_13", "7-13"}, //锈蚀战甲 圣骑士
        {"druid_skin_10", "11-10"}, //大发明家 德鲁伊
        {"viking_skin_13", "13-13"}, //钢铁焚化者 狂战士

        //2022年春节
        {"knight_skin_14", "0-14"}, // 骑士 首席百夫长
        {"paladin_skin_14", "7-14"}, // 圣骑士 斯巴达
        {"assassin_skin_13", "3-13"}, // 刺客 波斯王子 
        {"taoist_skin_9", "16-9"}, // 道士 描仙郎 
        {"druid_skin_11", "11-11"}, // 德鲁伊 神鹿
        {"rogue_skin_13", "1-13"}, // 游侠 汉谟拉比
        {"engineer_skin_13", "5-13"}, // 工程师 三星堆
        {"alchemist_skin_10", "4-10"}, // 炼金术士 巫医
        {"wizard_skin_14", "2-14"}, // 法师 巫女

        //2022年暑假
        { "assassin_skin_14", "3-14" }, // 刺客 南十字星 
        { "elf_skin_12", "8-12" }, //精灵 闪回
        { "werewolf_skin_16", "9-16" }, //狼人 惊骇
        { "druid_skin_12", "11-12" }, // 德鲁伊 悖论
        { "warlock_skin_1", "23-1" }, // 恶魔术士 关于我转生成一个少女在异世界修炼恶魔法术这档事
        { "airbender_skin_1", "22-1" }, // 气宗 魔道巨擘
        
        //2022年 元气动画
        {"knight_skin_15", "0-15"}, // 骑士 圣骑士我们走
        //2022年 国庆
        {"wizard_skin_15", "2-15"},//法师 幻纶
        {"taoist_skin_10", "16-10"},//道士 汐羽
        {"necromancer_skin_9", "14-9"},//死灵法师 冷月
        {"pastor_skin_14", "10-14"},//牧师 圣洁
        
        //2022年 冬季版本
        {"viking_skin_16", "13-16"}, //狂战士 利爪
        {"engineer_skin_14", "5-14"}, //工程师 伪装战甲
        
        //2023年 春节版本
        {"airbender_skin_3", "22-3"}, //气宗 空
        {"rogue_skin_16", "1-16"}, //游侠 玉龙三太子
        {"vampire_skin_13", "6-13"}, //吸血鬼 晶晶
        {"miner_skin_1", "24-1"}, //矿工 玉兔
        { "druid_skin_13", "11-13" }, // 德鲁伊 我不是德鲁伊
        
        //2023年 愚人节版本
        {"werewolf_skin_17","9-17"}, //狼人 牛魔王
        {"taoist_skin_11","16-11"},  //道士 玉帝
        
        //2023年5月版本
        {"wizard_skin_17","2-17"}, //法师 孔雀公主
        {"viking_skin_18","13-18"}, //狂战士 大鹏金翅雕
        
        //2023年 暑假版本
        {"wizard_skin_19","2-19"}, //法师 天鹅座
        {"rogue_skin_19","1-19"}, //游侠 双子座
        {"paladin_skin_19","7-19"},//圣骑士 盾牌座
        {"assassin_skin_19","3-19"},// 刺客 天蝎座
        {"elf_skin_15","8-15"},// 精灵 射手座
        
        //2023年 8月版本
        {"pastor_skin_17", "10-17"}, //牧师 处女座
        {"warlock_skin_6", "23-6" }, // 恶魔术士 小熊座
        
        //2023年国庆版本
        {"costumeprince_skin_1", "26-1" },  // 皮套王子 欧博士的火箭
        {"necromancer_skin_12", "14-12" },  // 死灵法师 仙后座
        {"vampire_skin_16", "6-16" },  // 吸血鬼 司马懿
        {"druid_skin_17", "11-17" }, // 德鲁伊 祝融
        
        //2023年11月版本
        {"viking_skin_20", "13-20"}, //狂战士皮肤 狮子座
        {"doctor_skin_1", "27-1"}, //博士皮肤 高级指挥官
        
        //2024年春节版本
        {"assassin_skin_20","3-20"}, //刺客 吕布
        {"necromancer_skin_14","14-14"}, //死灵法师 貂蝉
        {"lancer_skin_1","29-1"}, //枪客 赵云
        {"swordmaster_skin_1","28-1"}, //剑宗 孙尚香
        {"miner_skin_6","24-6"}, //矿工 大乔
        {"engineer_skin_19","5-19"}, //工程师 华佗
        {"wizard_skin_20","2-20"}, //法师 宝箱法师
        //2024春季修复包
        {"knight_skin_22","0-22"}, //骑士亚伦
        
        //2024三月版本
        {"arcaneknight_skin_1","31-1"},//咒法骑士 周瑜
        {"warliege_skin_4","30-4"}, //领主 影月
        
        //2024四月版本
        {"costumeprince_skin_6","26-6"},//皮套王子 大家饿
        {"rogue_skin_22","1-22"}, //游侠 刘备
        {"doctor_skin_4", "27-4"}, //博士 罗刹姬
        
        //2024六月版本
        {"trapmaster_skin_6","25-6"},//陷阱大师 星算子
        
        //2024七月版本
        {"astrologist_skin_1","32-1"},//占星师 诸葛亮
        {"wizard_skin_23","2-23"},//法师 卞玲珑
        {"viking_skin_22","13-22" }, //狂战士 关羽
        {"fighter_skin_1","33-1" }, //武斗家 关银屏
        {"pastor_skin_19","10-19" }, //牧师 蔡文姬
        {"warlock_skin_8","23-8" },//恶魔术士 小乔
        {"miner_skin_8","24-8" }, //蜜糖 矿工
        
        //2024 八月版本
        {"taoist_skin_16","16-16" }, //道士 陆逊
        {"lancer_skin_5","29-5" }, //枪客 骸星
        {"rogue_skin_23","1-23" }, //游侠 夜游神
        
        //2024 九月
        {"fighter_skin_3","33-3"}, //武斗家 玉面狐狸
        {"elf_skin_18","8-18"}, //精灵 步练师
        {"astrologist_skin_3","32-3"}, //占星师 极光圣骑
        
        //2024 十月
        {"warliege_skin_5","30-5"}, //领主皮肤 -寅将军
        {"arcaneknight_skin_4","31-4"},  //咒法骑士皮肤-铁扇公主
        
        //2024 十一月
        {"druid_skin_20", "11-20"}, //德鲁伊 风纪委员
        {"necromancer_skin_17", "14-17"}, //死灵法师 手机党
        {"swordmaster_skin_5", "28-5"}, //剑宗 青锋
        
        //2025 一月
        {"bard_skin_1", "35-1"},//吟游诗人皮肤-猫语歌者·妮芙蒂
        {"warlock_skin_9", "23-9"},//恶魔术士皮肤-混沌魔王·暗月
        {"pastor_skin_20", "10-20"},//牧师皮肤-辉洁星使·伊丽莎白
        {"wizard_skin_24", "2-24"},//法师皮肤-古灵精怪·幽铃
        {"alchemist_skin_19", "4-19"},//炼金术士皮肤-实验室·叶梨
        {"rogue_skin_24", "1-24"},//游侠皮肤-猎魔男爵·克雷顿 
        {"warliege_skin_6", "30-6"},//领主皮肤-魔械统帅·奥古斯都
        {"shooter_skin_1", "36-1"},//枪手皮肤-炽焰亡徒·卢西恩
        {"officer_skin_17", "15-17"},//警官皮肤 - 赤色暴君·里昂 
        
        {"taoist_skin_20", "16-20"},//道士西部皮肤
        {"druid_skin_22", "11-22"},//德鲁伊魔法少女
        {"elf_skin_19", "8-19"},//精灵 校园皮肤
        
        //2025 四月
        {"swordmaster_skin_7", "28-7"}, //剑宗-绯蔷之主·伊芙琳
        {"knight_skin_29", "0-29"},//骑士-狱火审判·杰洛特
        {"bard_skin_4", "35-4"}, //吟游诗人皮肤-啦啦队·晴空
        {"aigirl_skin_1", "37-1"},//机械姬皮肤-煌蜂

        //2025 六月
        {"fighter_skin_6", "33-6"}, //武斗家皮肤-新闻社·知夏
        {"warliege_skin_8", "30-8"},//领主皮肤-摇滚社·佐克
        {"vampire_skin_21", "6-21"},//吸血鬼皮肤-红影裁决·维恩
        
        //2025 七月
        { "assassin_skin_26", "3-26" }, //刺客（海神波塞冬）
        { "wizard_skin_27", "2-27" }, //法师（学园偶像·歌穗）
        { "elf_skin_21", "8-21" }, // 精灵（云裳灵羽·琉璃）
        { "necromancer_skin_20", "14-20" }, // 死灵法师（奇迹乐章 · 奏）
        { "airbender_skin_11", "22-11" }, // 气宗（苍风掠霄·岚枭）
        { "captain_skin_1", "38-1" }, // 船长（幽冥船长·斯派克）
        { "aigirl_skin_2", "37-2" }, // 机械姬（沧海遗珠·蓝歌）
        { "viking_skin_26", "13-26" }, // 狂战士（冲浪社·凌涛）
        
        //2025 八月
        {"fighter_skin_8", "33-8"}, //武斗家皮肤 - 炎之魔使·伊格尼丝
        {"ninja_skin_1", "20-1"},//超时空忍者皮肤 - 青空之翼·鸢
        {"pastor_skin_24", "10-24"}, //牧师皮肤 - 校医室·云蓁
        {"paladin_skin_26", "7-26"}, //圣骑士皮肤 - 海焰战姬·希格
        {"swordmaster_skin_9", "28-9"}, //剑宗皮肤 - 罪恶佳人·罗娜
    };

    // 宠物
    public static Dictionary<string, int> key2Pet = new Dictionary<string, int>() {
        {"pet_30", 30}, //泡泡
        {"pet_31", 31}, //波奇
    };

    public static Dictionary<string, int> fish2Count = new Dictionary<string, int>() {
        {"fish_chips_5", 5},
        {"fish_chips_10", 12},
        {"fish_chips_20", 28},
        {"fish_chips_45", 63},
    };

    public static Dictionary<string, int> seasonCoin2Count = new Dictionary<string, int>() {
        { "season_coin_5", 500 },
        { "season_coin_15", 1700 },
        { "season_coin_30", 3500 },
        { "season_coin_68", 8000 },
    };
    
    //花圃
    public static Dictionary<string, int> key2PlantPot = new Dictionary<string, int>() {
        {"plantpot_1", 4},
        {"plantpot_2", 5},
        {"plantpot_3", 7},
    };

    /// <summary>
    /// 英雄之家皮肤
    /// </summary>
    public static Dictionary<string, string> key2HeroRoomSkin = new Dictionary<string, string>() {
        { "hall_skin_1", "0-1" },
        { "hall_skin_3", "0-3" },
        { "hall_skin_4", "0-4" },
        { "hall_skin_5", "0-5" }
    };

    public static bool IsFish(string productName) {
        //这两个计费点已经废弃,此段代码已废弃.不要添加新鱼干的key!
        return productName == "fish1" || productName == "fishchip";
    }

    public enum RestoreResult {
        Success, //恢复购买成功
        AlreadyOwned, //已经拥有此物品
        Fail, //失败
    }

    /// <summary>
    /// RMB英雄解锁事件
    /// </summary>
    public static event Action<emHero> RMB_HeroUnlockEvent;

    /// <summary>
    /// RMB皮肤解锁事件
    /// </summary>
    public static event Action<emHero, int> RMB_SkinUnlockEvent;

    /// <summary>
    /// RMB技能解锁事件
    /// </summary>
    public static event Action<emHero, int> RMB_SkillUnlockEvent;


    /// <summary>
    /// RMB宠物解锁事件
    /// </summary>
    public static event Action<emPet> RMB_PetUnlockEvent;

    /// <summary>
    /// RMB骑士之家皮肤解锁事件
    /// </summary>
    public static event Action<HeroRoomSkinType, int> RMB_HeroRoomSkinUnlockEvent;

    /// <summary>
    /// 恢复通用商品
    /// </summary>
    /// <param name="key"></param>
    public static RestoreResult RestoreComodityByKey(string key, bool record = true, string orderId = null) {
        RestoreResult result = RestoreResult.Success;
        Debug.Log("NewSdkManager.RestoreComodityByKey key:" + key + " orderId:" + orderId);

        try {
            if (SkuConfigManager.IsGem(key)) {
                int count = SkuConfigManager.GetSkuCount(key);
                RGSaveManager.Inst.AddGem(count, emObtainGemType.GemStore);
            } else if (IsFish(key)) {
                PlayerSaveData.Inst.fish_chips_count += 1;
                DataUtil.SetPetUnlock(emPet.Panda, true);
                if (PlayerSaveData.Inst.fish_chips_count >= 2) {
                    DataUtil.SetSkinUnlock(emHero.Knight, 7, true);
                }
            } else if (fish2Count.ContainsKey(key)) {
                //新鱼干
                ItemData.data.cumulativeFishCount += fish2Count[key]; // 鱼干购买记录添加，用于进行充值赠送, 先进行购买记录的操作，鱼干操作会保存itemdata，购买记录操作不会
                ItemData.data.AddFishCount(fish2Count[key], emObtainGemType.Mall);
            } else if (seasonCoin2Count.ContainsKey(key)) {
                //新鱼干
                SeasonDataUtil.AddCoin(seasonCoin2Count[key], SeasonCoinChangeReason.RestoreComodity, key, null);
                SeasonData.Save();
            } else if (key.StartsWith("pkg")) {
                //礼包
                var subStrs = key.Split('_');
                if (subStrs.Length == 2) {
                    int pkgIndex = int.Parse(subStrs[1], CultureInfo.InvariantCulture);
                    ResotrePackage(pkgIndex);
                }
            } else if (SkuConfigManager.IsNewPkg(key)) {
                result = RestoreNewPackage(key, true);
            } else if (key == CGTreasureBoxData.MonthCardKey) {
                result = DataMgr.CGTreasureBoxData.RestoreNewPackage(key);
            } else {
                result = RestoreRoleByKey(key, record);
            }
            MallReloadData.data.AddBuyCurrencyCount(key);
        } catch (Exception e) {
            result = RestoreResult.Fail;
            Debug.LogError(e);
            BuglyUtil.ReportException($"RestoreExp_RestoreComodityByKey_{LifeCycleManager.Instance.AccountId}_{key}_{orderId}", e);
        }
        Debug.Log("NewSdkManager.RestoreComodityByKey result:" + result);

        return result;
    }

    /// <summary>
    /// 恢复不可重复购买商品
    /// </summary>
    /// <param name="key"></param>
    public static RestoreResult RestoreRoleByKey(string key, bool record = true) {
        RestoreResult result = RestoreResult.Fail;
        if(LogUtil.IsShowLog){LogUtil.Log($"RestoreRoleByKey {key} {record} {LifeCycleManager.Instance.SupportRestore} {LifeCycleManager.Instance.SupportPay}");}
        if (!LifeCycleManager.Instance.SupportRestore || !LifeCycleManager.Instance.SupportPay ) {
            return RestoreResult.Fail;
        }

        try {
            //新鱼干、新手礼包不计入恢复购买计费点
            if (record && !fish2Count.ContainsKey(key) && !seasonCoin2Count.ContainsKey(key) && !key.StartsWith("pkg") &&
                !key.StartsWith(SkuConfigManager.NewPkg)) {
                ItemData.data.AddRestoreInfos(key);
            }

            if (key2Hero.ContainsKey(key)) {
                //角色
                result = DataUtil.GetHeroUnlock((emHero)key2Hero[key]) ? RestoreResult.AlreadyOwned : RestoreResult.Success;
                RGSaveManager.Inst.HeroUnLock(key2Hero[key]);

                if (String.Equals("HeroRoom", SceneManager.GetActiveScene().name)) {
                    var unlockedChar = GameObject.Find("/characters/c" + (key2Hero[key]).ToString().PadLeft(2, '0'));
                    if (null != unlockedChar) {
                        //避免试用角色时去商城中解锁角色时报错的问题
                        unlockedChar.GetComponent<RGCharGray>().UnLockChar();
                    }
                }

                if (result == RestoreResult.Success) {
                    //解锁才会触发英雄解锁事件回调
                    RMB_HeroUnlockEvent?.Invoke((emHero)key2Hero[key]);
                }
            } else if (key2Skin.ContainsKey(key)) {
                //皮肤
                var indexes = key2Skin[key].Split('-');
                emHero hero = (emHero)int.Parse(indexes[0], CultureInfo.InvariantCulture);
                int skinIndex = int.Parse(indexes[1], CultureInfo.InvariantCulture);
                result = DataUtil.GetSkinUnlock(hero, skinIndex) ? RestoreResult.AlreadyOwned : RestoreResult.Success;
                RGSaveManager.Inst.HeroSkinUnlock((int)hero, skinIndex);
                if (result == RestoreResult.Success) {
                    //解锁才会触发皮肤解锁事件回调
                    RMB_SkinUnlockEvent?.Invoke(hero, skinIndex);
                }
            } else if (key.StartsWith("skill")) {
                //技能
                var subStrs = key.Split('_');
                if (subStrs.Length == 3) {
                    var heroName = CompareNameToEmHero(subStrs[1]);
                    emHero hero = (emHero)Enum.Parse(typeof(emHero), heroName, true);
                    int skillIndex = int.Parse(subStrs[2], CultureInfo.InvariantCulture);
                    result = DataUtil.GetSkillUnlock(hero, skillIndex) ? RestoreResult.AlreadyOwned : RestoreResult.Success;
                    RGSaveManager.Inst.SetSkillUnlock(hero, skillIndex, true);
                    if (result == RestoreResult.Success) {
                        //解锁才会触发技能解锁事件回调
                        RMB_SkillUnlockEvent?.Invoke(hero, skillIndex);
                    }
                }
            } else if (String.Equals("reborn_card", key)) {
                //复活卡
                result = PlayerSaveData.Inst.reborn_card == 1 ? RestoreResult.AlreadyOwned : RestoreResult.Success;
                PlayerSaveData.Inst.reborn_card = 1;
            } else if (String.Equals("plantpot_1", key)) {
                //花圃 1
                result = ItemData.data.IsItemUnlock("plant_pot" + 4) ? RestoreResult.AlreadyOwned : RestoreResult.Success;
                ItemData.data.UnlockItem("plant_pot" + 4);
            } else if (String.Equals("plantpot_2", key)) {
                //花圃 2
                result = ItemData.data.IsItemUnlock("plant_pot" + 5) ? RestoreResult.AlreadyOwned : RestoreResult.Success;
                ItemData.data.UnlockItem("plant_pot" + 5);
            } else if (String.Equals("plantpot_3", key)) {
                //花圃 3 
                result = ItemData.data.IsItemUnlock("plant_pot" + 7) ? RestoreResult.AlreadyOwned : RestoreResult.Success;
                ItemData.data.UnlockItem("plant_pot" + 7);
            } else if (ShouldRestoreNewPackage(key)) {
                result = RestoreNewPackage(key, false);
            } else if (key2Pet.ContainsKey(key)) {
                //宠物
                result = DataUtil.GetPetUnlock((emPet)key2Pet[key]) ? RestoreResult.AlreadyOwned : RestoreResult.Success;
                RGSaveManager.Inst.PetUnLock(key2Pet[key]);
                if (result == RestoreResult.Success) {
                    RMB_PetUnlockEvent?.Invoke((emPet)key2Pet[key]);
                }
            } else if (key2HeroRoomSkin.ContainsKey(key)) {
                var split = key2HeroRoomSkin[key].Split('-');
                var typeInt = int.Parse(split[0], CultureInfo.InvariantCulture);
                var skinType = (HeroRoomSkinType)typeInt;
                var skinIndex = int.Parse(split[1], CultureInfo.InvariantCulture);
                result = DataUtil.GetHeroRoomSkinUnlock(skinType, skinIndex) ? RestoreResult.AlreadyOwned : RestoreResult.Success;
                DataUtil.SetHeroRoomSkinUnlock(skinType, skinIndex, "in_app_purchase");
                if (result == RestoreResult.Success) {
                    RMB_HeroRoomSkinUnlockEvent?.Invoke(skinType, skinIndex);
                }
            }
        } catch (Exception e) {
            Debug.LogError(e);
            BuglyUtil.ReportException($"RestoreExp_RestoreRoleByKey_{LifeCycleManager.Instance.AccountId}_{key}", e);
        }
        Debug.Log("NewSdkManager.RestoreRoleByKey result:" + result);

        return result;
    }

    public static string CompareNameToEmHero(string heroName) {
        var lowerHeroName = heroName.ToLower();
        foreach (var emHero in Enum.GetValues(typeof(emHero))) {
            var emHeroName = emHero.ToString();
            if (lowerHeroName.Equals(emHeroName,StringComparison.InvariantCultureIgnoreCase)) {
                return emHeroName;
            }
        }

        return null;
    }

    private static bool ShouldRestoreNewPackage(string key) {
        if (!key.StartsWith(SkuConfigManager.NewPkg)) {
            return false;
        }

        
        string replace = key.Replace(SkuConfigManager.NewPkg, "");
        if (!int.TryParse(replace, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out int index)) {
            return false;
        }

        var id = 7010000 + index;
        var find = DataMgr.ConfigData.Tables.TbGood.DataMap.TryGetValue(id, out var good);
        if (find) {
            // 盲盒礼包不能恢复
            return good.GoodType != 7;
        }

        return MallData.shouldRestoreNewPackageIndex.Contains(index);
    }
    
    /// <summary>
    /// 粗略计算已购买非消耗品次数
    /// </summary>
    /// <returns></returns>
    public static int GetBuyTimesByUnlocked() {
        int _count = 0;
        foreach (var productRole in SkuConfigManager.products) {
            bool unlocked = false;
            if (RestoreRole.key2Hero.ContainsKey(productRole.Key)) {
                // 角色
                unlocked = DataUtil.GetHeroUnlock((emHero)RestoreRole.key2Hero[productRole.Key]);
            } else if (RestoreRole.key2Skin.ContainsKey(productRole.Key)) {
                //皮肤
                var indexes = RestoreRole.key2Skin[productRole.Key].Split('-');
                emHero hero = (emHero)int.Parse(indexes[0], CultureInfo.InvariantCulture);
                int skinIndex = int.Parse(indexes[1]);
                unlocked = DataUtil.GetSkinUnlock(hero, skinIndex);
            } else if (productRole.Key.StartsWith("skill")) {
                //技能
                var subStrs = productRole.Key.Split('_');
                if (subStrs.Length == 3) {
                    var heroName = subStrs[1][0].ToString().ToUpper() + subStrs[1].Substring(1);
                    emHero hero = (emHero)Enum.Parse(typeof(emHero), heroName, true);
                    int skillIndex = int.Parse(subStrs[2], CultureInfo.InvariantCulture);
                    unlocked = DataUtil.GetSkillUnlock(hero, skillIndex);
                }
            } else if (String.Equals("reborn_card", productRole.Key)) {
                //复活卡
                unlocked = PlayerSaveData.Inst.reborn_card == 1;
            } else if (String.Equals("plantpot_1", productRole.Key)) {
                //花圃 1
                unlocked = ItemData.data.IsItemUnlock("plant_pot" + 4);
            } else if (String.Equals("plantpot_2", productRole.Key)) {
                //花圃 2
                unlocked = ItemData.data.IsItemUnlock("plant_pot" + 5);
            } else if (String.Equals("plantpot_3", productRole.Key)) {
                //花圃 3 
                unlocked = ItemData.data.IsItemUnlock("plant_pot" + 7);
            } else if (key2Pet.ContainsKey(productRole.Key)) {
                //宠物
                unlocked = DataUtil.GetPetUnlock((emPet)key2Pet[productRole.Key]);
            } else if (key2HeroRoomSkin.ContainsKey(productRole.Key)) {
                var split = key2HeroRoomSkin[productRole.Key].Split('-');
                var typeInt = int.Parse(split[0], CultureInfo.InvariantCulture);
                var skinType = (HeroRoomSkinType)typeInt;
                var skinIndex = int.Parse(split[1], CultureInfo.InvariantCulture);
                unlocked = DataUtil.GetHeroRoomSkinUnlock(skinType, skinIndex);
            }

            if (unlocked) {
                _count++;
            }
        }
        if(LogUtil.IsShowLog){LogUtil.Log($"total unlocked.count:{_count}", "checkUnlock");}

        return _count;
    }

}
