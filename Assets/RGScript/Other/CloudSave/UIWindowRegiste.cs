using I2.Loc;
using System.Collections;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using System;
using ChillyRoom.Utils;
using UnityEngine.Events;

/// <summary>
/// 云存档注册账号界面管理类
/// </summary>
public class UIWindowRegiste : MonoBehaviour {
    UIWindowAccount uIWindowAccount;
    UITitle uITitle;

    InputField inputAccount;
    InputField inputPassword;
    InputField confirmInputPassword;
    Button btnPasswordSeenOrUnSeen;
    Sprite showEye, hideEye;
    UIWindowRegisteConfirm uIWindowRegisteConfirm;
    DelayConfirm registe_confirm_email_yours;
    public InputField InputRegistePhonePassword,
        InputRegistePhoneConfirmPassword;

    Transform trSignup;
    public Button RegisteBtn;
    public GameObject EmailRegisteGa,
        PhoneRegisteGa,
        PhoneRegistSendSMSCodeGa,
        PhoneRegistSetPasswordGa;
    public Text GuideText;
    // Use this for initialization
    void Awake () {
        Init();
    }
    bool inited = false;
    void Init() {
        if (!inited) {
            uIWindowAccount = transform.parent.GetComponentInChildren<UIWindowAccount>(true);
            uITitle = GetComponentInParent<UITitle>();
            inputAccount = transform.Find("registeContent/EmailRegiste/account/input").GetComponent<InputField>();
            inputPassword = transform.Find("registeContent/EmailRegiste/password/input").GetComponent<InputField>();
            confirmInputPassword = transform.Find("registeContent/EmailRegiste/confirm_password/input").GetComponent<InputField>();
            btnPasswordSeenOrUnSeen = transform.Find("registeContent/EmailRegiste/password/btn_pw_seen_or_unseen").GetComponent<Button>();
            trSignup = transform.Find("registeContent/win_signup");
            showEye = btnPasswordSeenOrUnSeen.GetComponent<Image>().sprite;
            hideEye = transform.Find("loginContent/password/btn_pw_unseen").GetComponent<Image>().sprite;

            inputAccount.textComponent.horizontalOverflow = HorizontalWrapMode.Wrap;
            uIWindowRegisteConfirm = transform.parent.Find("window_email_verify/window_user_notice").GetComponent<UIWindowRegisteConfirm>();
            registe_confirm_email_yours = transform.Find("registeContent/registe_confirm_email_yours").GetComponent<DelayConfirm>();
            registe_confirm_email_yours.afterDisagreeClick.AddListener(HideConfirmEmailYours);
            ShowView();
            uIWindowAccount.OnClick_LoginView();
            inited = true;
        }
    }

    public void ShowView() {
        if (SdkTagUtil.HasRegisteTag(AccountType.Email)) {
            EmailRegisteGa.gameObject.SetActive(true);
            PhoneRegisteGa.gameObject.SetActive(false);
            RegisteBtn.onClick.AddListener(ClickEmailRegiste);
        } else if (SdkTagUtil.HasRegisteTag(AccountType.PhoneNumber)) {
            EmailRegisteGa.gameObject.SetActive(false);
            PhoneRegisteGa.gameObject.SetActive(true);
            //如果已经发送过密码 则仍然展示设置密码界面
            if (currectPhoneRegisteView == PhoneRegisteView.SetPassword) {
                SetPhoneRegisteView(PhoneRegisteView.SetPassword);
            } else {
                SetPhoneRegisteView(PhoneRegisteView.SendSMSCode);
                EventSystem.Broadcast<string>(CloudSaveUIEventEnum.SetPhoneGuideText
                    ,  "<b><color=#EE3B3B>1.输入手机号</color></b>>2.输入验证码>3.设置密码");
            }
        }
    }
    private void OnEnable() {
        LoginApi.Inst.onAlreadySendNeedVarifyEmail += OnAlreadySendNeedVarifyEmail;
        EventSystem.AddListener<Err>(CloudSaveEventEnum.SendSMSCodeResult, EventSendSMSResult);
    }
    private void OnDisable() {
        if (LoginApi.Inst != null) {
            LoginApi.Inst.onAlreadySendNeedVarifyEmail -= OnAlreadySendNeedVarifyEmail;
        }
        EventSystem.RemoveListener<Err>(CloudSaveEventEnum.SendSMSCodeResult, EventSendSMSResult);
    }
    
    #region 手机验证码
    /// <summary>
    /// 手机号注册的界面状态
    /// </summary>
    public enum PhoneRegisteView {
        SendSMSCode,
        SetPassword,
    }
    private void EventSendSMSResult(Err errCode) {
        //ShowMessage($"发送验证码结果 {errCode}");
        if(LogUtil.IsShowLog){LogUtil.Log($"Registe-SendSMSResult {errCode},{errCode.GetHashCode()}");}
        if (errCode == Err.Success) {
            GuideText.text= "1.输入手机号>2.输入验证码><b><color=#EE3B3B>3.设置密码</color></b>";
        } 
    }
    #endregion
    /// <summary>
    /// 刷新ip重新注册
    /// </summary>
    public void RefleshIPRegiste() {
        UiUtil.ShowLoadingWindow(UIWindowCloudSave.ShowLoadingTransformParent, 15);
        LoginApi.Inst.CallNewIp((success) => {
            UiUtil.HideLoadingWindow(UIWindowCloudSave.ShowLoadingTransformParent);
            if (success&&LoginApi.isRegistering) {
                LoginApi.isRegistering = false;
                if (Debug.isDebugBuild||LogUtil.IsShowLog) {
                    Debug.Log("重新拉取到ip 重新注册");
                }
                ClickEmailRegiste();
            }
        });
    }
    internal PhoneRegisteView currectPhoneRegisteView;
    /// <summary>
    /// 设置手机号注册流程的界面
    /// </summary>
    /// <param name="view"></param>
    public void SetPhoneRegisteView(PhoneRegisteView view) {
        GameObject showContent = null;
        String btnContent = string.Empty;
        UnityAction clickAction = null;
        switch (view) {
            case PhoneRegisteView.SendSMSCode:
                currectPhoneRegisteView = PhoneRegisteView.SendSMSCode;
                EventSystem.Broadcast<string>(CloudSaveUIEventEnum.PhoneRegAccount
                    ,"1.输入手机号>2.输入验证码>3.设置密码");
                GuideText.gameObject.SetActive(false);
                RegisteBtn.gameObject.SetActive(false);
                break;
            case PhoneRegisteView.SetPassword:
                GuideText.gameObject.SetActive(true);
                currectPhoneRegisteView = PhoneRegisteView.SetPassword;
                EventSystem.Broadcast(CloudSaveUIEventEnum.ClosePhoneVerifyWindow);
                showContent = PhoneRegistSetPasswordGa;
                btnContent = ScriptLocalization.Get("cloudsave/registe", "注册");
                clickAction = ClickPhoneRegiste;
                RegisteBtn.gameObject.SetActive(true);
                RegisteBtn.onClick.RemoveAllListeners();
                RegisteBtn.onClick.AddListener(clickAction);
                RegisteBtn.GetComponentInChildren<Text>().text = btnContent;
                break;
        }

        GameObject[] registeStepGas = new[]
            {PhoneRegistSendSMSCodeGa, PhoneRegistSetPasswordGa};
        foreach (var stepGa in registeStepGas) {
            stepGa.SetActive(stepGa == showContent);
        }

   
    }
    /// <summary>
    /// 点击手机号注册
    /// </summary>
    private void ClickPhoneRegiste() {
        var account = CloudSaveUtil.LastSendSMSPhoneNumber;
        var password = InputRegistePhonePassword.text;
        var confirmPassword = InputRegistePhoneConfirmPassword.text;

        if (confirmPassword != password) {
            Transform differentPassword =
                InputRegistePhoneConfirmPassword.transform.parent.Find("DifferentPassword");
            differentPassword.transform.GetComponentInChildren<Text>().text = ScriptLocalization.Get("cloudsave/different_password"
                , "两次输入密码不一致，请检查密码。");
            differentPassword.gameObject.SetActive(true);
            differentPassword.DOKill();
            Sequence quence = DOTween.Sequence();
            quence.AppendInterval(2f).OnComplete(() => differentPassword.gameObject.SetActive(false));
            return;
        }
        if (!RegexUtil.IsPhoneNumber(account)) {
            UIWindowCloudSave.ShowMessage(ScriptLocalization.Get("cloudsave/phone_format_invalid", "手机号格式不正确，请输入正确的手机号格式"));
        } else if (CheckPasswordValid(password)) {
            //如果检查到频繁点击 则跑猫 补发消息返回
            if (UIWindowAccount.CheckIsFrequentInvalid()) {
                UiUtil.ShowLoadingWindow(UIWindowCloudSave.ShowLoadingTransformParent, ChillyRoomAccount.EmailUrl.emailUrl.freezeTime);
                return;
            }
            //todo 如果检查到频繁点击 则跑猫 补发消息返回
            UiUtil.ShowLoadingWindow(UIWindowCloudSave.ShowLoadingTransformParent, 15);
            Reg(account, password);
            //点击注册后 会刷新回发送验证码状态
            currectPhoneRegisteView = PhoneRegisteView.SendSMSCode;
        }
    }
    /// <summary>
    /// 注册按钮方法
    /// </summary>
    public void ClickEmailRegiste() {
        var account = inputAccount.text;
        account = EmailToLowerCase(account);
        var password = inputPassword.text;
        var confirmPassword = confirmInputPassword.text;

        if (confirmPassword!= password) {
            //ShowMessage("两次输入密码不一致，请检查密码。此处没有本地化！！！");
            Transform differentPassword = confirmInputPassword.transform.parent.Find("DifferentPassword");
            differentPassword.gameObject.SetActive(true);
            //differentPassword.transform.GetComponent<Text>().text = "两次输入密码不一致，请检查密码。";
            differentPassword.DOKill();
            differentPassword.DOMoveZ(0, 2).OnComplete(() => differentPassword.gameObject.SetActive(false));
            return;
        }
        if (CheckAccountValid(account) && CheckPasswordValid(password)) {
            //如果检查到频繁点击 则跑猫 补发消息返回
            if (UIWindowAccount.CheckIsFrequentInvalid()) {
                UiUtil.ShowLoadingWindow(UIWindowCloudSave.ShowLoadingTransformParent, ChillyRoomAccount.EmailUrl.emailUrl.freezeTime);
                return;
            }
            Action regAction = () => {
                UiUtil.ShowLoadingWindow(UIWindowCloudSave.ShowLoadingTransformParent, 15);
                Reg(account, password);
                HideConfirmEmailYours();
            };
            ShowConfirmEmailYours(account,regAction);
        }
    }

    private void Reg(string account,string password) {
        LoginApi.Inst.RegAccount(account, password, (regErrCode) => {
            UiUtil.HideLoadingWindow(UIWindowCloudSave.ShowLoadingTransformParent);
            if (Debug.isDebugBuild || LogUtil.IsShowLog) {
                Debug.LogError("OnClick_Signup_Confirm " + regErrCode);
            }
            if (regErrCode == Err.Success) {
                ShowMessage(ScriptLocalization.Get("accout/reg_success"));
                uIWindowAccount.SetAccount(account);
                uIWindowAccount.ClearPassWord();
                OnClick_Close();
                //发送注册日志到服务器
                //RemoteLogManager.SendRemoteLogLoginServerData(this, string.Empty);
                //Login(account, password);
            } else if (regErrCode == Err.Account_repeat) {
                ShowMessage(ScriptLocalization.Get("accout/reg_already"));
            } else if (regErrCode == Err.Email_try_send_please_check) {
            } else if (regErrCode == Err.Data_repeat) {
                ShowMessage(ScriptLocalization.Get("accout/reg_fal") + ":" + "重复注册");
            } else if (regErrCode == Err.ConnectError) {
                RefleshIPRegiste();
            } else if (regErrCode == Err.Version_upgrade) {
                UiUtil.ShowDialogWindow(transform.parent.parent,I2.Loc.ScriptLocalization.Get("I_mul_tip6"),"当前游戏版本过低，请先更新游戏再使用云存档",null,null,2);
            } else {
                ShowMessage(ScriptLocalization.Get("accout/reg_fal") + ":" + CloudSaveLocalization.GetMessage(regErrCode));
            }
            //trSignup.gameObject.SetActive(false);
            ShowTips(0);
        }, (exp) => {
            UIWindowCloudSave.ShowMessage("连接服务器失败", 2.5f);
            RefleshIPRegiste();
        });
    }
    
    public void OnClick_Close() {
        Debug.Log("OnClick_Close");
        Init();
        EventSystem.Broadcast(CloudSaveUIEventEnum.ClosePhoneVerifyWindow);
        if (trSignup.gameObject.activeSelf) {
            trSignup.gameObject.SetActive(false);
            ShowTips(0);
        }
        inputAccount.text = string.Empty;
        inputPassword.text = string.Empty;
        confirmInputPassword.text = string.Empty;
        InputRegistePhonePassword.text = string.Empty;
        InputRegistePhoneConfirmPassword.text = string.Empty;
        uIWindowAccount.SetShowView(UIWindowAccount.ShowView.LoginView);
        HideConfirmEmailYours();
    }
    public void OnClickShowOrHidePassWord() {
        ShowOrHidePassWord(inputPassword, btnPasswordSeenOrUnSeen, confirmInputPassword);
    }
    public bool CheckAccountValid(string str, bool showMsg = true) {
        return UIWindowAccount.CheckAccountValid(str,showMsg);
    }
    public bool CheckPasswordValid(string str, bool showMsg = true) {
        return RegisteCheckPasswordValid(str, showMsg);
    }
    public bool RegisteCheckPasswordValid(string str, bool showMsg = true) {
        var result = RegexUtil.PasswordCheck(str);
        
        if (!result.Item1) {
            ShowMessage(result.Item2);
        }
        return result.Item1;
    }
    void ShowOrHidePassWord(InputField inputField,Button button,InputField input=null) {
        if(inputField.inputType == InputField.InputType.Standard) {
            inputField.inputType = InputField.InputType.Password;
            inputField.contentType = InputField.ContentType.Password;
            button.GetComponent<Image>().sprite = showEye;
            button.transform.Find("Image").GetComponent<Image>().sprite = showEye;
            if (input != null) {
                input.inputType = InputField.InputType.Password;
                input.contentType = InputField.ContentType.Password;
            }
        } else {
            inputField.inputType = InputField.InputType.Standard;
            inputField.contentType = InputField.ContentType.Standard;
            if (input != null) {
                input.inputType = InputField.InputType.Standard;
                input.contentType = InputField.ContentType.Standard;
            }
            button.GetComponent<Image>().sprite = hideEye;
            button.transform.Find("Image").GetComponent<Image>().sprite = hideEye;
        }
        string text = inputField.text;
        inputField.text = string.Empty;
        inputField.text = text;
        if (input != null) {
            string text1 = input.text;
            input.text = string.Empty;
            input.text = text1;
        }
    }
    void ShowMessage(string msg, float duration = 2.5f) {
        if (uITitle) {
            uITitle.ShowTempMessage(msg, duration, false);
        }
    }
    void ShowTips(int tipIndex) {
        var root = transform.Find("registeContent/tips");
        for (int i = 0; i < root.childCount; i++) {
            root.GetChild(i).gameObject.SetActive(i == tipIndex);
        }
    }
    void OnAlreadySendNeedVarifyEmail() {
        GetComponentInParent<UIWindowCloudSave>().ShowGoConfirm(EmailToLowerCase(inputAccount.text));
    }
    public void ShowAccountData() {
        var account = inputAccount.text;
        account = EmailToLowerCase(account);
        var password = inputPassword.text;
        if(account.Length==0&& password.Length == 0) {
            account = uIWindowAccount.GetCurrectAccount();
            password = uIWindowAccount.GetCurrectPW();
        }
        ShowMessage(ScriptLocalization.Get("accout/reg_success"));
        uIWindowRegisteConfirm
        .SetEmail(account)
        .SetPassword(password)
        .SetAfterClickAgreeAction(() => {
            uIWindowAccount.SetAccount(account);
            uIWindowAccount.ClearPassWord();
            OnClick_Close();

        }).OnShown();
    }
    internal string GetInputEmail() {
        return EmailToLowerCase(inputAccount.text);
    }
    /// <summary>
    /// 将邮箱@后的转小写
    /// </summary>
    /// <param name="email"></param>
    /// <returns></returns>
    string EmailToLowerCase(string email) {
        int index = email.LastIndexOf('@');
        string result = "";
        if (index > 0) {
            string emailMainString = email.Substring(index);
            result = email.Substring(0, index) + emailMainString.ToLower();
        }
        return result;
    }
    /// <summary>
    /// 展示确认邮箱的界面
    /// </summary>
    void ShowConfirmEmailYours(string email,Action confirmAction) {
        registe_confirm_email_yours.gameObject.SetActive(true);
        registe_confirm_email_yours.transform.Find("content/Text").GetComponent<Text>().text =$"<color=#ffc952>邮箱： {email}</color>";
        registe_confirm_email_yours.afterAgreeClick.RemoveAllListeners();
        registe_confirm_email_yours.afterAgreeClick.AddListener(()=>confirmAction());
    }
    /// <summary>
    /// 关闭确认邮箱的界面
    /// </summary>
    public void HideConfirmEmailYours() {
        registe_confirm_email_yours.gameObject.SetActive(false);
        registe_confirm_email_yours.afterAgreeClick.RemoveAllListeners();
    }
}
