namespace ChillyRoomAccount {
    using UnityEngine;
    using System.Collections;
    using System;
    using System.Collections.Generic;
    using System.IO;

    [Serializable]
    public class EmailUrl {
        private static EmailUrl _emailUrl;
        //跳转的邮箱主页信息
        public Dictionary<string, string> emailUrlData;
        //客服qq
        public List<string> ContactQQs;
        //是否自动跳转url 到外部，改为false会自动百度 邮箱关键字
        public bool isAutoGotoUrl;
        //是否做频繁点击检查
        public bool isCheckFrequentClick;
        //检测点击的时间间隔 单位s
        public int checkLockTimeDistance;
        //点击次数上限
        public int checkLockCount;
        //冻结时间
        public int freezeTime;
        //是否在下载存档时候合并计费点
        public bool IsDownloadMergeCharegePoint;
        //是否在上传存档的时候合并计费点
        public bool IsUploadMergeCharegePoint;
        //重新拉取服务器地址时候ping服务器的概率 100为必定发生 0为不发生
        public int PingRate;
        //IPManager重新请求登陆服地址信息的间隔天数
        public int pingInterval;
        //是否需要修复渠道的uid重叠问题
        public bool isNeedChangeOverlap;
        //发送验证码的间隔秒数
        public int sendSMSInterval=60;
        public string phoneRegex = @"^[1][0,1,2,3,4,5,6,7,8,9][0-9]{9}$";
        public static EmailUrl emailUrl {
            get {
                if (_emailUrl == null) {
                    try {
                        if (File.Exists(EmailUrlConst.Path + "/config.json")) {
                            _emailUrl = Abo.JsonUtil.LoadJson<EmailUrl>(EmailUrlConst.Path + "/config.json");
                        } else {
                            _emailUrl = Default;
                        }
                    } catch (System.Exception e) {
                        Debug.LogError(e);
                    }
                }
                return _emailUrl;
            }
        }
        public static EmailUrl Default = new EmailUrl {
            emailUrlData = new Dictionary<string, string>() {
                { "qq", "https://mail.qq.com/"},
                { "163", "https://mail.163.com/"},
                { "gmail", "https://mail.google.com"},
                { "126","https://www.126.com/" },
                { "sina","https://mail.sina.com.cn/" },
                { "sohu","https://mail.sohu.com/" },
                { "hotmail","https://outlook.live.com/" },
                { "Outlook","https://outlook.live.com/" }
            },
            ContactQQs = new List<string>() { "800179233" },
            isAutoGotoUrl = true,
            isCheckFrequentClick = true,
            checkLockTimeDistance = 5,
            checkLockCount = 3,
            freezeTime = 5,
            IsDownloadMergeCharegePoint = false,
            IsUploadMergeCharegePoint = false,
            PingRate = 0,
            pingInterval = 15,
            isNeedChangeOverlap = false,
            sendSMSInterval=60,
            phoneRegex=@"^[1][0,1,2,3,4,5,6,7,8,9][0-9]{9}$",
        };
        public static void SetConfigEmail(EmailUrl emailUrl) {
            _emailUrl = emailUrl;
        }
        public string GetQQString() {
            string[] result = new string[2];
            if (ContactQQs.Count > 2) {
                for (int i = 0; i < 2; i++) {
                    int index = UnityEngine.Random.Range(0, ContactQQs.Count - i);
                    result[i] = ContactQQs[index];
                    if (index != ContactQQs.Count - i - 1) {
                        ContactQQs.RemoveAt(index);
                        ContactQQs.Insert(ContactQQs.Count - i, result[i]);
                    }
                }
            } else {
                result = ContactQQs.ToArray();
            }
            return string.Join("或", result);
        }
    }
    public class EmailUrlConst {
        public static string ServerPath {
            get {
                return "account";
            }
        }
        public static string Path {
            get {
                return Abo.FileUtil.InternalPersistentPath + "/" + ServerPath;
            }
        }
        public static readonly string LocalFileRoot = Abo.FileUtil.InternalPersistentPath + "/";//客户端根目录
        public static readonly string FileSvrIP = "**************";
        public static readonly ushort FileSvrPort = 7071;
    }
}