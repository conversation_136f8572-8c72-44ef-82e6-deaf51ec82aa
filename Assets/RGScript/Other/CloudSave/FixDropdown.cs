using UnityEngine;
using System.Collections;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using UnityEngine.Events;

public class FixDropdown : MonoBeh<PERSON>our, ISelectHandler {

    public Dropdown drop;
    public InputField  inputField;
    private int lastIndex;

    public UnityAction action;

    public void OnSelect(BaseEventData eventData) {
        //Debug.Log(drop.value);
        if (drop.options.Count > 0) {
            inputField.text = drop.options[drop.value].text;
        }
    }

}
