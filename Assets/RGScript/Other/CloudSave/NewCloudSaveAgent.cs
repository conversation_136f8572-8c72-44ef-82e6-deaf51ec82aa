using ChillyRoom.Services.Accounts;
using ChillyRoom.Services.Accounts.Sdk;
using ChillyRoom.Services.Accounts.Sdk.UI;
using ChillyRoom.Services.Accounts.Utils;
using ChillyRoom.Services.Core;
using ChillyRoom.Services.Core.Errors;
using ChillyRoom.Utils;
using ChillyRoomAccount;
using CoreKit.Agent;
using CoreKit.Config;
using Generated.ChillyRoomSdkClient;
using I2.Loc;
using NewDynamicConfig;
using Newtonsoft.Json;
using RGScript.Data;
using RGScript.Manager.SDK;
using RGScript.Other.CloudSave;
using RGScript.Other.LegencyAdapter;
using RGScript.Other.NewSDK;
using RGScript.Util.ForcePatch;
using RGScript.Util.LifeCycle;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.UI;
using Util.TAUtil;

namespace RGScript.Other {
    public static class NewCloudSaveAgent {
        public static int ClickLegencyLoginFailTotalCount {
            get {
                return PlayerSaveData.GetIntLocal("ClickLegencyLoginFailTotalCount", 0);
            }
            set {
                PlayerSaveData.SetIntLocal("ClickLegencyLoginFailTotalCount", value);
            }
        }
        private static bool _hasInit;
        public static bool HasFetchData { get; private set; }

        private static Transform _cloudSaveCanvas;
        //在凉屋服务器有存档
        public static bool ChillyHasCloudSaveData => LoadedGameData != null;
        public static event Action<bool ,ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode , CloudSaveGameData, Exception> AfterFetchCloudSaveEvent;

        internal static Action BeforeUpLoadGameAction;
        /// <summary>
        /// 上传存档回调
        /// </summary>
        internal static Action<bool> UpLoadGameAction;
        internal static Action<bool, string> AfterUpLoadGameAction;

        internal static Action<bool> BeforeDownloadGameAction;

        /// <summary>
        /// 下载存档回调
        /// </summary>
        internal static Action<bool, CloudSaveGameData> DownLoadGameAction;

        /// <summary>
        /// 本地存档变更回调
        /// </summary>
        internal static Action<bool, CloudSaveGameData> LocalDataChangeAction;

        internal static event Action AfterShowView;
        internal static event Action AfterHideView;

        public static event Action<string> SetLoadTextAction;
        internal static event Action<bool> AfterLegencyLoginSuccessAction;
        
        public static Action ManualGetUserProfileAction;
        public static CloudSaveGameData LoadedGameData { get; private set; }
        public enum SaveDataState {
            UnKnown,
            NotExist,
            Exist,
        }
        public static SaveDataState CloudSaveDataState { get; private set; }
        
        public enum HarmonyLoginMethod {
            UnKnown,
            bind,
            transfer,
            official
        }
        
        public static HarmonyLoginMethod HarmonyLoginType {
            get {
                return (HarmonyLoginMethod)PlayerSaveData.GetIntLocal("HarmonyLoginType", 0);
            }
            set {
                PlayerSaveData.SetIntLocal("HarmonyLoginType", (int)value);
            } 
        }
        public static bool HarmonyOfficalLogin {           
            get {
                return PlayerSaveData.GetIntLocal("HarmonyOfficalLogin", 0) > 0;
            }
            set {
                PlayerSaveData.SetIntLocal("HarmonyOfficalLogin", value ? 1 : 0);
            } 
        }

        public static async Task Init() {
            if (!_hasInit) {
                _hasInit = true;
                //设置资源层代理
                ResourcesHelper.Instance.Inject(new ConcreteResources());
                _cloudSaveCanvas = AccountSdkUIFacade.GetCloudSaveCanvas();
                CommonUiUtil.ShowLoadingWindow(UICanvasRoot.Inst.transform, 8);
                SetCanvasOrder(3);
                CanvasScaler canvasScaler = _cloudSaveCanvas.GetComponent<CanvasScaler>();
                canvasScaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
                canvasScaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
                canvasScaler.referenceResolution = new Vector2(1280, 720);
                canvasScaler.matchWidthOrHeight = 1;
                GameObject.DontDestroyOnLoad(_cloudSaveCanvas.gameObject);
                
                if (AccountStateManager.Instance == null) {
                    SdkEvents.OnInitialized += HideRightBtn;
                } else {
                    HideRightBtn();
                }
                ChillyRoomService.GetSdk().Accounts.SwitchLanguage(LocalizationManager.CurrentLanguage);
                SimpleEventManager.AddEventListener<ChangeLanguageEvent>(_ => {
                    //设置语言
                    ChillyRoomService.GetSdk().Accounts.SwitchLanguage(_.ToLanguage);
                });
                
                // 监听关闭登录弹窗事件：关闭云存档界面，取消登录关联动作
                ChillyRoom.Services.Accounts.Utils.EventSystem.AddListener(NewUIEventEnum.CloseLoginView, () => {
                    HideView();
                    NewSDKManager.Inst.doAfterLoginAction = null;
                });
                SimpleEventManager.AddEventListener<EnterSceneEvent>(scene => {
                    var eParam = scene as EnterSceneEvent;
                    var curScene = eParam.enterScene;
                    if (curScene != emScene.Title) {
                        HideView();
                    }
                });
                SimpleEventManager.AddEventListener<AfterReloadDataEvent>(_ => {
                    // 云存档界面处于开启状态，则刷新下本地存档数据
                    if (AccountSdkUIFacade.GetAccountSdkUIFacade().Active) {
                        RefreshLocalData();
                    }
                });
                
                SdkEvents.RenewResultAction += OnRenewResultAction;
                SdkEvents.LoginResultAction += OnLoginResultAction;
                SdkEvents.LogoutAction += OnLogoutAction;
                SdkEvents.OnGetLocalDeviceId += GetDeviceId;
                SdkEvents.OnGetLocalDataId += GetLocalDataId;
                SdkEvents.OnGetLegencyAvailable += GetLegencyAvailable;
                SdkEvents.LogoffAction += OnLogoffAction;
                AfterFetchCloudSaveEvent += DealBindBtnAndCloudSaveDataState;
                AfterFetchCloudSaveRpcEvent += DealBindBtnAndCloudSaveDataRpcState;
                //处理i2的脚本依赖
                CloudSaveUiUtil.AddLocalizeAction += (x, y) => {
                    x.gameObject.AddComponent<Localize>().Term = y;
                };
                CommonUiUtil.HideLoadingWindow(UICanvasRoot.Inst.transform);
                //已经绑定gp，ios后，获取存档后往gp ios上传一份
                AfterFetchCloudSaveEvent += DealGPAndIosSendDataToLegencyWhenBinded;
                AfterFetchCloudSaveRpcEvent += DealGPAndIosSendDataToLegencyRpcWhenBinded;
                SdkEvents.SignUpResultAction += SignUpResultAction;
                TAUtil.AfterInit += () => {
                    TAUtil.Track("gms_available", new Dictionary<string, object>() {
                        { "gms_a", GameUtil.GetLegencyAvailable() }
                    });
                };
                SdkEvents.OnDisAgreePrivacy += _ => {
                    if (DataUtil.CurIsTargetScene(RGGameConst.SCENE_LOGIN)) {
                        Application.Quit();
                    } else {
                        SimpleEventManager.AddEventListener<EnterSceneEvent>(_ => {
                            if (_.enterScene == emScene.SceneLogin) {
                                Application.Quit();
                            }
                        });
                    }
                };
                NewCloudSaveAgent.UpLoadGameAction += RecordUploadGameCount;
                NewCloudSaveAgent.UpLoadGameRpcAction += RecordUploadGameRpcCount;
                NewCloudSaveAgent.DownLoadGameAction += RecordDownloadSaveCount;
                AgentHub.Instance.Callback.onLogin += SdkOnLogin;
                AgentHub.Instance.Callback.onLoginFail += SdkOnLoginFail;
                AgentHub.Instance.Callback.onConfigurationChanged += OnConfigurationChanged;
                BeforeDownloadGameAction += FuncForHotFixBeforeDownload;
                UpLoadGameAction += FuncForHotFixAfterUpload;
                UpLoadGameRpcAction += FuncForHotFixAfterUploadRpc;
                AfterFetchCloudSaveEvent += FuncForHotFixAfterFetch;
                AfterFetchCloudSaveRpcEvent += FuncForHotFixAfterFetchRpc;
                ManualGetUserProfileAction += OnManualGetUserProfileAction;
                AfterUpLoadGameAction += SaveUtil.AfterUploadGame;
                AfterUpLoadGameRpcAction += SaveUtil.AfterUploadGameRpc;
            }
        }

        private static async void DealGPAndIosSendDataToLegencyWhenBinded(bool suc, ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode err, CloudSaveGameData data, Exception exception) {
            AutoUploadLegencySave autoUploadLegencySave =
                ConfigManager.GetCurrectUseConfig<AutoUploadLegencySave>();
            var loginBy = ChillyRoomService.GetSdk().Accounts?.StateManager?.State?.Session?.LoginBy;
            if (loginBy is UniversalSdkType.Apple or UniversalSdkType.GooglePlay && suc && autoUploadLegencySave.AutoUpload) {
                bool canSend = false;
                if (autoUploadLegencySave.NeedInspect) {
                    var canUpload = await LegencyLoginAndCloudSaveManager.Instance.Agent.PayInspect();
                    canSend = canUpload.Item1;
                } else {
                    canSend = true;
                }

                if (canSend) {
                    MoveCloudSaveManager.Instance.SendDataToLegency(data);
                }
            }
        }
        
        private static void DealBindBtnAndCloudSaveDataState(bool suc, ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode err, CloudSaveGameData data, Exception exception) {
            var btnContent = AccountSdkUIFacade.GetAccountSdkUIFacade().transform.Find("AccountInformationView").transform
                .Find("BtnContent");
            //关闭绑定gp、ios的按钮
            btnContent.Find("BindAppleBtn").gameObject.SetActive(false);
            btnContent.Find("BindGoogleBtn").gameObject.SetActive(false);

            if (suc) {
                CloudSaveDataState = SaveDataState.Exist;
            } else if (err == ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode.SaveDataNotFound) {
                CloudSaveDataState = SaveDataState.NotExist;
            } else {
                CloudSaveDataState = SaveDataState.UnKnown;
            }
        }

        private static bool GetLegencyAvailable() {
            Debug.Log("OnGetLegencyAvailable");
            return GameUtil.GetLegencyAvailable();
        }

        private static string GetDeviceId() {
            string mac = CloudSaveUtil.mac;
            if (!string.IsNullOrEmpty(mac)) {
                mac = mac.Replace("OPPO", "O");
            }

            return string.IsNullOrEmpty(mac) ? null : mac;
        }

        private static string GetLocalDataId() {
            try {
                // 本地存档标识与账号挂钩，没有登录则不展示
                if (!NewSDKManager.IsLogined()) {
                    return string.Empty;
                }

                string saveLocalID = CloudSaveUtil.GetLocalSaveId();
                bool definedChannel = CloudSaveUtil.TryGetDefineCloudSaveChannelId(saveLocalID,
                    out string channelName, out string defindeCloudSaveId);
                if (definedChannel) {
                    saveLocalID = defindeCloudSaveId.Replace("_id", saveLocalID.Substring(channelName.Length));
                }

                return saveLocalID.Replace("OPPO", "O");
            }
            catch (Exception ex) {
                LogUtil.LogError(ex);
            }

            return string.Empty;
        }
        private static void OnLogoffAction(bool suc, LogOffExtra extra) {
            PlayerDataDeactivationManager.OnLogoffAction(suc, extra);
        }

        private static void OnLogoutAction(bool _) {
            if (_) {
                LogUtil.Log("AccountSdkController LogOutAction");
                CloudSaveUtil.Reset();
                UIWindowAccount.defaultAccount = "";
                UIWindowAccount.defaultPassword = "";
                LoginApi.Inst.SetState(LoginApi.State.UnLogin);
                HasFetchData = false;
                LoadedGameData = null;
                LoginApi.Inst.IsLogin = false;
                UIWindowCloudSave.currectUID = String.Empty;
                LifeCycleManager.IsActive = false;
                UITitle.HasFetchActive = false;
                hasGuideBindPhone = false;

                LifeCycleManager.Instance.SetSupportRestore(false);
                LifeCycleManager.Instance.SetSupportCloudSaveDownload(false);
                LifeCycleManager.Instance.SetSupportCloudSaveUpload(false);

                // 渠道登录的先隐藏云存档，然后切换当前状态为待登录状态
                if (NewSDKManager.Inst.HasChannelLogin) {
                    HideView();
                }
                CommonStatistics.SetIsGuest(false);
            }
        }

        private static void SignUpResultAction(IResult<AccountSuccessResult, Failure<AccountFailResult>> loginResult) {
            if (loginResult == null) {
                LogUtil.Log($"[NewCloudSaveAgent]loginResult is null");
                return;
            }

            string regAccount = string.Empty;
            if (loginResult.Error != null && loginResult.Error.Details != null) {
                loginResult.Error.Details.TryGetValue("account", out regAccount);
            }

            string expName = loginResult.Error?.Exception == null
                ? string.Empty
                : loginResult.Error.Exception.GetType().Name;
            int login_by;
            if (loginResult.IsOk()) {
                login_by = (int)loginResult.Data.SdkType;
            } else {
                login_by = (int)loginResult.Error.Error.SdkType;
            }
            TAUtil.Track("option_move",new Dictionary<string, object>() {
                {"new_cr_player", loginResult.IsOk()},
                {"login_by", login_by},
                {"origin_legacy_fail_total",NewCloudSaveAgent.ClickLegencyLoginFailTotalCount}
            });
            TAUtil.Track("sign_up", new Dictionary<string, object>() {
                { "reg_suc", loginResult.IsOk() },
                { "exp_name", expName },
                { "rg_err_code", loginResult.Error?.Error.ErrCode },
                { "err_msg", loginResult.Error?.Error.ErrMsg },
                { "alogin_by", loginResult.Error?.Error.SdkType.GetHashCode() },
                { "register_type", (int)AccountStateManager.Instance.State.View.RegisterPlaneType },
                { "reg_account", regAccount }
            });
        }

        private static void OnRenewResultAction(IResult<AccountSuccessResult, Failure<AccountFailResult>> result) {
            if(result.IsOk()) {
                if (!_isManualGetUserProfile) {
                    //有登陆缓存 默认renew过
                    CommonUiUtil.ShowMsg(
                        AccountSdkController.ShowLoadingTransformParent,
                        ScriptLocalization.Get("cloudsave/login_success", "登录成功"),
                        2f);
                }
            } else {
                TrackAccessGoogleOrApple();
                TrackAccessChilly();
                if (result.Error == null) {
                    return;
                }
                //默认登陆失败
                string originErrCodeName = ChannelConfig.IsAllIOS ? "ios_origin_err_codestr" : "gp_origin_err_code";
                string expName = result.Error.Exception == null ? string.Empty : result.Error.Exception.GetType().Name;
                TAUtil.Track("renew_fail",new Dictionary<string, object>(){
                    {"exp_name",expName},
                    {"err_code",result.Error.Error.ErrCode},
                    {"err_msg",result.Error.Error.ErrMsg},
                    {"alogin_by",result.Error.Error.SdkType.GetHashCode()}
                });
                BuglyUtil.ReportException("renew_fail",new Exception($"err_code:{result.Error.Error.ErrCode},{originErrCodeName},{expName},{result.Error.Error.SdkType.GetHashCode()}"));
            }

            _isManualGetUserProfile = false;
        }

        private static void OnLoginResultAction(IResult<AccountSuccessResult, Failure<AccountFailResult>> result) {
            if (result.IsOk()) {
                //设置远联的client
                ChillyRoomService.GetSdk().ArenaIntegration();

                SetUidSundry();

                //有非密码登陆（如果是iOS或GP的原生登陆，则触发一些专有逻辑）
                if (NewSDKManager.Inst.HasChannelLogin) {
                    AfterLegencyLoginSuccessAction?.Invoke(
                        (ChillyRoomService.GetSdk().Accounts.StateManager.State.Session.LoginBy != UniversalSdkType.Email
                         && ChillyRoomService.GetSdk().Accounts.StateManager.State.Session.LoginBy != UniversalSdkType.Phone) &&
                        !result.Data.IsRenew);
                }

                // LifeCycleManager.Instance.SetChillyLogin(AccountType.UnKnown, ChillyRoomService.GetSdk().Accounts.StateManager.State.User.Id.ToString());

                //设置android端的凉屋userid和playerid
                string userId = AccountStateManager.Instance.State.User.Id.ToString();
                string playerId = AccountStateManager.Instance.State.User.PlayerId.ToString();
                LogUtil.Log($"LoginResultAction setChillyUserId {userId} , setChillyPlayerId {playerId}");
                if (!Application.isEditor) {
                    AgentHub.Instance.GetAgent<GooglePlayPayAgent>()?.callSetChillyUserId(userId);
                    AgentHub.Instance.GetAgent<GooglePlayPayAgent>()?.callSetChillyPlayerId(playerId);
                }

                UseOldLogic();
                TAUtil.Track("login_success", new Dictionary<string, object>() {
                    { "login_by", ChillyRoomService.GetSdk().Accounts.StateManager.State.Session.LoginBy.GetHashCode() },
                    { "is_renew", result.Data.IsRenew },
                });
                CommonStatistics.SetIsGuest(ChillyRoomService.GetSdk().Accounts.StateManager.State.Session.LoginBy == UniversalSdkType.Guest);
                if (ChillyRoomService.GetSdk().Accounts.StateManager.State.Session.LoginBy == UniversalSdkType.Email
                    || ChillyRoomService.GetSdk().Accounts.StateManager.State.Session.LoginBy ==
                    UniversalSdkType.Phone) {
                    TAUtil.Track("option_move", new Dictionary<string, object>() {
                        { "cr_login_suc", result.IsOk() },
                        {"origin_legacy_fail_total",NewCloudSaveAgent.ClickLegencyLoginFailTotalCount}
                    });
                }

                LifeCycleManager.Instance.SetSupportPay(true);
                LifeCycleManager.Instance.SetSupportCloudSaveDownload(true);
                LifeCycleManager.Instance.SetSupportCloudSaveUpload(true);

                //关闭绑定gp、ios的按钮
                var btnContent = AccountSdkUIFacade.GetAccountSdkUIFacade().transform.Find("AccountInformationView").transform
                    .Find("BtnContent");
                btnContent.Find("BindAppleBtn").gameObject.SetActive(false);
                btnContent.Find("BindGoogleBtn").gameObject.SetActive(false);
            } else if (result.Error.Error.SdkType == UniversalSdkType.Email ||
                       result.Error.Error.SdkType == UniversalSdkType.Phone ||
                       result.Error.Error.SdkType == UniversalSdkType.Guest) {
                TrackAccessGoogleOrApple();
                TrackAccessChilly();
                TAUtil.Track("option_move",new Dictionary<string, object>() {
                    {"login_fail_reason", result.Error.Error.ErrMsg},
                    {"login_fail_err_code", result.Error.Error.ErrCode},
                    {"login_by", result.Error.Error.SdkType},
                    {"origin_legacy_fail_total",NewCloudSaveAgent.ClickLegencyLoginFailTotalCount}
                });
                //邮箱手机号登录失败打点
                string originErrCodeName = ChannelConfig.IsAllIOS ? "ios_origin_err_codestr" : "gp_origin_err_code";
                if (result.Error.Exception != null) {
                    TAUtil.Track("login_exp", new Dictionary<string, object>() {
                        { "exp_name", result.Error.GetType().Name },
                        { "err_code", result.Error.Error.ErrCode },
                        { originErrCodeName, result.Error.Error.OriginErrCode },
                        { "err_reason", result.Error.Error.OriginErrMsg },
                        { "err_msg", result.Error.Error.ErrMsg },
                        { "alogin_by", result.Error.Error.SdkType.GetHashCode() }
                    });
                    BuglyUtil.ReportException("login_exp",
                        new Exception(
                            $"err_code:{result.Error.Error.ErrCode},{originErrCodeName}:{result.Error.Error.OriginErrCode},err_reason:{result.Error.Error.OriginErrMsg},{result.Error.Exception.GetType().Name},{result.Error.Error.SdkType.GetHashCode()}"));
                } else {
                    TAUtil.Track("login_fail", new Dictionary<string, object>() {
                        { "err_code", result.Error.Error.ErrCode },
                        { originErrCodeName, result.Error.Error.OriginErrCode },
                        { "err_reason", result.Error.Error.OriginErrMsg },
                        { "err_msg", result.Error.Error.ErrMsg },
                        { "alogin_by", result.Error.Error.SdkType.GetHashCode() }
                    });
                    BuglyUtil.ReportException("login_fail",
                        new Exception(
                            $"err_code:{result.Error.Error.ErrCode},{originErrCodeName}:{result.Error.Error.OriginErrCode},err_reason:{result.Error.Error.OriginErrMsg},{result.Error.Error.SdkType.GetHashCode()}"));
                }
                TAUtil.Track("option_move", new Dictionary<string, object>() {
                    { "cr_login_suc", result.IsOk() },
                    {"origin_legacy_fail_total",NewCloudSaveAgent.ClickLegencyLoginFailTotalCount}
                });
            }

            SetAccountLogin(result);
        }

        static void SetAccountLogin(IResult<AccountSuccessResult, Failure<AccountFailResult>> result) {
            LogUtil.Log("uuuuuuuuu OnLoginResultAction res.isOk:" + result.IsOk());
            if (result.IsOk()) {
                LifeCycleManager.Instance.SetSupportRestore(true);
                var accountId = NewSDKManager.Inst.GetAccountId();
                bool hideLogin = false;
                #if UNITY_EDITOR
                hideLogin = UnityEditor.EditorPrefs.GetBool("HideLogin", true);
                #endif
                if (string.IsNullOrWhiteSpace(accountId) &&
                    !((ChannelConfig.IsShangYou //ignore 编辑器的mock登录
                       || ChannelConfig.Fn4399
                       || hideLogin) 
                      && Application.isEditor)) {
                    var dialogTitleText = ScriptLocalization.Get("ui/login_fail", "?登录失败");
                    var errorText = ScriptLocalization.Get("force_patch/error_unknown", "?未知错误，请联系客服");
                    UIWindowDialog.ShowDialog(UICanvasRoot.Inst.transform, dialogTitleText, errorText,
                        AfterLoginError, null, 2, true, useAb: false, showMask: true, maskBlockClick:true);
                }
                LifeCycleManager.Instance.SetAccountLogin(accountId);
                // 检测当玩家在冷静期内中止注销流程时调用 用于清除延迟删除玩家本地存档的逻辑
                PlayerDataDeactivationManager.CancelDelayedDeletion();
            }
            BuglyAgent.SetUserId(RGScript.Other.NewSDK.NewSDKManager.Inst.GetChillyUid().ToString());
        }

        static void AfterLoginError() {
            // 点确定后退出游戏
            QuitApp("AccountIdNull");
        }
        public static void QuitApp(string tag, string extraInfo = null, bool sendTrack = true) {
            if (extraInfo == null) {
                extraInfo = "UserCancel";
            }

            if (sendTrack) {
                TAUtil.TrackToMainThread("force_patch_cancel_retry", new Dictionary<string, object>() {
                    { "force_patch_tag", tag },
                    { "force_patch_result_type", extraInfo },
                });
            }
#if UNITY_EDITOR
            Debug.LogError("[ForcePatchManager] 退出游戏");
            UnityEditor.EditorApplication.ExitPlaymode();
#else
            NewSDKManager.Inst.ShowExitDialogWithOutCancel();
#endif
        }
        static void HideRightBtn() {
            // 默认隐藏右下角按钮
            var accountSdkUIFacade = AccountSdkUIFacade.GetAccountSdkUIFacade();
            accountSdkUIFacade.HideRightBottom();
        }

        public static void TrackAccessGoogleOrApple() {
            if (ChannelConfig.IsAllGP) {
                TrackAccess("https://www.google.com/", "google");
            }else if (ChannelConfig.IsAllIOS) {
                TrackAccess("https://www.apple.com/", "apple");
            }
        }
        
        public static void TrackAccessChilly() {
            TrackAccess("https://api-gateway.soulknight.chillyroom.com/GameWorld/Time", "chilly");
        }
        private static async Task<bool> TrackAccess(string url,string accessSvrName) {
            bool result = false;
            CancellationTokenSource source = new CancellationTokenSource();
            source.CancelAfter(TimeSpan.FromSeconds(15f));
            int statusCode = 0;
            string expName = string.Empty;
            try {
                System.Net.Http.HttpClient client;
                client = LoginApi.CommonHttpClientWithoutPDNS;
                var data= await client.GetAsync(url, source.Token);
                statusCode= (int)data.StatusCode;
                var content = await data.Content.ReadAsStringAsync();
                result = statusCode==200 && !string.IsNullOrWhiteSpace(content);
            } catch (Exception exception) {
                expName = GameUtil.CutString(exception.ToString(),1000);
                TAUtil.Track("track_access",new Dictionary<string, object>() {
                    {"access_svr",accessSvrName},
                    {"status_code",statusCode},
                    {"exp_name",expName},
                    {"has_net",NetStateUtil.HasNet().Item1},
                    {"has_unet",NetworkUtil.NetworkCanUse()},
                });
                BuglyUtil.ReportException("track_access" + accessSvrName, exception);
                result = false;
            }
            return result;
        }

        public static void onSaveFailCallback() {
            SetLoadTextAction?.Invoke(I2.Loc.ScriptLocalization.Get("cloudsave_undetected"));
        }

        public static void onLoadGameSuccess(string args) {
            CommonUiUtil.HideLoadingWindow(_cloudSaveCanvas);
            HasFetchData = true;
            string json = args; //ZipHelper.GZipDecompressString(args);
            //Debug.Log(json);
            if (string.IsNullOrEmpty(json)) {
                // windowSave.SetLoadText(I2.Loc.ScriptLocalization.Get("cloudsave_undetected"));
                AfterFetchCloudSaveEvent?.Invoke(false, ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode.UnknowError, null, null);
            } else {
                try {
                    // json = System.IO.File.ReadAllText(@"C:\Users\<USER>\Downloads\download_save_data (5)\download_save_data (5).json");
                    LoadedGameData = NewtonJsonUtil.ParseJson<CloudSaveGameData>(json);
                    if (LoadedGameData == null) {
                        // 存档损坏
                        ShowMsgAfterLoginSuccessUiHide(string.Format(
                            ScriptLocalization.Get("cloudsave/fetch_fail_extra", "获取存档失败：{0}"),
                            CloudSaveLocalization.GetMessage(Err.remoteCloudSaveDataBroke)));
                        AfterFetchCloudSaveEvent?.Invoke(false, ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode.UnknowError, null, null);
                        return;
                    }
                    // Debug.LogError(LoadedGameData.cloudSaveId);
                    if (ChannelConfig.IsTapTapTest && LoadedGameData.cloudSaveId.StartsWith("TapTap_")) {
                        LoadedGameData.cloudSaveId = LoadedGameData.cloudSaveId.Replace("TapTap_", "TapTapTest_");
                    } 
                    
                    if (ChannelConfig.IsGP_VNM) {
                        var matchResult = Regex.Match(LoadedGameData.cloudSaveId, @"^GP_(?<id>\d+)$");
                        if (matchResult.Success) {
                            LoadedGameData.cloudSaveId = $"GP_VNM_{matchResult.Groups[1]}";
                        }
                    }

                    AfterFetchCloudSaveEvent?.Invoke(true, ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode.UnknowError, LoadedGameData, null);
                } catch (Exception e) {
                    if (Debug.isDebugBuild || LogUtil.IsShowLog) {
                        Debug.LogError(e);
                    }
                    AfterFetchCloudSaveEvent?.Invoke(false, ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode.UnknowError, null, null);
                }

                if (ChannelConfig.IsTapTapTest) {
                    UITitle.SolveTatTapTestLimit();
                    //再次拉取配置
                    if (!ConfigManager.FetcheConfigSuccess) {
                        ConfigManager.FetchConfig();
                    }

                    CloudSaveRunner.Instance.ShowAutoUploadTips();
                    CloudSaveRunner.Instance.AutoDnload(CloudSaveGameData.CreateFromPlayerPref());
                }
            }
        }

        static void OnLoadGameTimeOutOrResponseNull() {
            ShowMsgAfterLoginSuccessUiHide(string.Format(
                ScriptLocalization.Get("cloudsave/fetch_fail_extra", "获取存档失败：{0}"),
                CloudSaveLocalization.GetMessage(Err.Unknow_error)));
        }
        static void onLoadGameFail(ErrorResponse responce, Exception exception) {
            if (Debug.isDebugBuild || LogUtil.IsShowLog) {
                Debug.Log("onLoadGameFail" + responce.Error);
            }

            ParseErrorCodeToMsg(true, responce);
        }

        public static void ParseErrorCodeToMsg(bool isFetchOrUploadData ,ErrorResponse errorResponse) {
            string describe = CloudSaveLocalization.GetMessage(Err.Unknow_error); //"未知错误，请稍后尝试"
            bool dataNotFound = false;
            var errCode = (ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode)errorResponse.Error;
            switch (errCode) {
                case ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode.LOSS_LOGIN:
                    // 服务器异常，请稍后重新登录
                    describe = CloudSaveLocalization.GetMessage(Err.Offline);
                    break;
                case ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode.UnbindThrottle:
                    // 解绑失败，操作频繁
                    if (TryGetRebindSeconds(errorResponse, "timeWait", out int unbindWaitTime)) {
                        TimeSpan waitTime = TimeSpan.FromSeconds(unbindWaitTime);
                        TimeUtil.TimeUnit timeUnit = TimeUtil.GetTimeMaxUnitFromDay2Minute(waitTime, out int units);
                        if (timeUnit == TimeUtil.TimeUnit.Day) {
                            describe = string.Format(
                                ScriptLocalization.Get("account/record_unbind_device_limit_day",
                                    "该设备已绑定其他账号且解绑失败, 请在{0}天后重试"), units);
                        } else if (timeUnit == TimeUtil.TimeUnit.Hour) {
                            describe = string.Format(
                                ScriptLocalization.Get("account/record_unbind_device_limit_hour",
                                    "该设备已绑定其他账号且解绑失败, 请在{0}小时后重试"), units);
                        } else if (timeUnit == TimeUtil.TimeUnit.Minute) {
                            describe = string.Format(
                                ScriptLocalization.Get("account/record_unbind_device_limit_minute",
                                    "该设备已绑定其他账号且解绑失败, 请在{0}分钟后重试"), units);
                        } else {
                            describe = ScriptLocalization.Get("Err/BindInfo_had_been_bound", "绑定冲突");                        }
                    } else {
                        describe = ScriptLocalization.Get("Err/BindInfo_had_been_bound", "绑定冲突");
                    }
                    break;
                case ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode.DeviceHasAlreadyBind:
                    // 当前设备已经绑定了一个账号
                    describe = CloudSaveLocalization.GetMessage(Err.Record_mac_already_bind);
                    break;
                case ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode.SaveDataNotFound:
                    // 存档未找到 （玩家没有上传过云存档）
                    dataNotFound = true;
                    HasFetchData = true;
                    break;
                case ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode.DistroNoConfig:
                case ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode.DeviceParamsMissing:
                    // 操作异常，请联系客服人员寻求更多支持
                    describe = CloudSaveLocalization.GetMessage(Err.Invalid)+ ", " + errCode; 
                    break;
                case ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode.VersionLowerThanBlobSave:
                    // 客户端版本低于存档版本
                    describe = I2.Loc.ScriptLocalization.Get("multi_remote/game_version_invalid", "当前游戏版本过低，请升级游戏版本。");
                    break;
                case ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode.DeviceHasBindMax:
                    // 更换设备绑定已达上限
                    if (TryGetRebindSeconds(errorResponse, "timeWait", out int bindMaxWaitTime)) {
                        TimeSpan waitTime = TimeSpan.FromSeconds(bindMaxWaitTime);
                        TimeUtil.TimeUnit timeUnit = TimeUtil.GetTimeMaxUnitFromDay2Minute(waitTime, out int units);
                        if (timeUnit == TimeUtil.TimeUnit.Day) {
                            describe = string.Format(
                                ScriptLocalization.Get("account/record_limit_day", "更换设备绑定已达上限，请在{0}天后重试"), units);
                        } else if (timeUnit == TimeUtil.TimeUnit.Hour) {
                            describe = string.Format(
                                ScriptLocalization.Get("account/record_limit_hour", "更换设备绑定已达上限，请在{0}小时后重试"), units);
                        } else if (timeUnit == TimeUtil.TimeUnit.Minute) {
                            describe = string.Format(
                                ScriptLocalization.Get("account/record_limit_minute", "更换设备绑定已达上限，请在{0}分钟后重试"), units);
                        } else {
                            describe = ScriptLocalization.Get("Err/BindInfo_had_been_bound", "绑定冲突");
                        }
                    } else {
                        describe = ScriptLocalization.Get("Err/BindInfo_had_been_bound", "绑定冲突");
                    }
                    break;
                case ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode.DeviceBindThrottle:
                case ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode.ExistTimeInfo:
                    // 绑定新设备频率过高
                    if (TryGetRebindSeconds(errorResponse, errCode == ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode.ExistTimeInfo ? "bindDeviceWaitSeconds" : "timeWait", out int rebindSeconds)) {
                        TimeSpan waitTime = TimeSpan.FromSeconds(rebindSeconds);
                        TimeUtil.TimeUnit timeUnit = TimeUtil.GetTimeMaxUnitFromDay2Minute(waitTime, out int units);
                        if (timeUnit == TimeUtil.TimeUnit.Day) {
                            describe = string.Format(
                                ScriptLocalization.Get("account/record_bind_device_limit_day",
                                    "绑定新设备频率过高,请{0}天后重试"), units);
                        } else if (timeUnit == TimeUtil.TimeUnit.Hour) {
                            describe = string.Format(
                                ScriptLocalization.Get("account/record_bind_device_limit_hour",
                                    "绑定新设备频率过高,请{0}小时后重试"), units);
                        } else if (timeUnit == TimeUtil.TimeUnit.Minute) {
                            describe = string.Format(
                                ScriptLocalization.Get("account/record_bind_device_limit_minute",
                                    "绑定新设备频率过高,请{0}分钟后重试"), units);
                        } else {
                            describe = ScriptLocalization.Get("Err/BindInfo_had_been_bound", "绑定冲突");
                        }
                    } else {
                        describe = ScriptLocalization.Get("Err/BindInfo_had_been_bound", "绑定冲突");
                    }
                    break;
                case ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode.UnknowError:
                    // "未知错误，请稍后尝试"
                    describe = CloudSaveLocalization.GetMessage(Err.Unknow_error); 
                    break;
                case ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode.BlobSaveClosed:
                    // "此功能暂时关闭"
                    describe = CloudSaveLocalization.GetMessage(Err.Closed_by_gm); 
                    break;
                default:
                    // "未知错误，请稍后尝试"
                    describe = CloudSaveLocalization.GetMessage(Err.Unknow_error) + ", " + errCode; 
                    break;
            }

            if (!dataNotFound) {
                string finalContent = isFetchOrUploadData
                    ? string.Format(ScriptLocalization.Get("cloudsave/fetch_fail_extra", "获取存档失败：{0}"),
                        describe)//获取存档失败：
                    : (I2.Loc.ScriptLocalization.Get("cloudsave_save_fail") + ":" + describe);//上传存档失败:
                ShowMsgAfterLoginSuccessUiHide(finalContent);
            }
        }

        private static bool TryGetRebindSeconds(ErrorResponse errorResponse, string key, out int rebindSeconds) {
            rebindSeconds = 0;
            if (errorResponse.Errordetails.TryGetValue(key, out string bindDeviceWaitSeconds)) {
                if (int.TryParse(bindDeviceWaitSeconds, out rebindSeconds)) {
                    return true;
                }
            }
            return false;
        }

        public static async Task ShowView() {
            await Init();
            bool isFirstLoginSuccess = false;
            var accountSdkUIFacade = AccountSdkUIFacade.GetAccountSdkUIFacade();
            var acountSdkUIFacadeTrans = accountSdkUIFacade.transform;
            acountSdkUIFacadeTrans.SetAsLastSibling();
            
            Debug.Log($"Starting... HashLoggedIn {ChillyRoomService.GetSdk().Accounts.StateManager.HasLoggedIn} {accountSdkUIFacade.Active}");
            
            // 这里先执行，否则云存档按钮因为层级变化导致一闪
            accountSdkUIFacade.ShowView();
            
            CloudSaveDataView cloudSaveDataView = CloudSaveDataView.ShowView(acountSdkUIFacadeTrans);
            cloudSaveDataView.transform.SetAsFirstSibling();
            
            // 渠道登录的隐藏掉chilly云存档介绍按钮
            var hideInfoBtn = NewSDKManager.Inst.LoginByChannel();
            acountSdkUIFacadeTrans.Find("CloudSaveInfoBtn").gameObject.SetActive(!hideInfoBtn);
            acountSdkUIFacadeTrans.Find("AccountInformationBtn").gameObject.SetActive(true);
            
            if (!ChannelConfig.HasMultiLoginWay) {
                Button AgeBtn = UiUtil.InitAgeBtn(accountSdkUIFacade.transform.Find("AccountLoginView"));
                if (AgeBtn != null) {
                    AgeBtn.transform.SetAsLastSibling();
                    RectTransform rectTransform = AgeBtn.GetComponent<RectTransform>();
                    rectTransform.anchorMin = new Vector2(1, 1);
                    rectTransform.anchorMax = new Vector2(1, 1);
                    rectTransform.anchoredPosition = new Vector2(-30, -159.5f);
                    rectTransform.sizeDelta = new Vector2(101, 129.5f);
                }
            }

            AfterShowView?.Invoke();
            if (!HasFetchData) {
                UseOldLogic();
            }
        }

        public static void HideView() {
            var accountSdkUIFacade = AccountSdkUIFacade.GetAccountSdkUIFacade();
            var acountSdkUIFacadeTrans = accountSdkUIFacade.transform;
            
            // 确保叠加在底层的云存档界面以及右下角按钮和云存档按钮一并隐藏
            CloudSaveDataView cloudSaveDataView = CloudSaveDataView.GetView(acountSdkUIFacadeTrans);
            if (cloudSaveDataView != null) {
                cloudSaveDataView.HideView();
            }
            
            accountSdkUIFacade.HideRightBottom();
            accountSdkUIFacade.HideView();
            AfterHideView?.Invoke();
        }

        private static bool hasGuideBindPhone = false;

        static void UseOldLogic() {
            LogUtil.Log("Host:" + ChillyRoomService.GetSdk().Accounts.StateManager.State.User.LegacyGateway.Host);
            LogUtil.Log("Tk:" + ChillyRoomService.GetSdk().Accounts.StateManager.State.User.LegacyGateway.Token);
            LogUtil.Log("Id:" + ChillyRoomService.GetSdk().Accounts.StateManager.State.User.Id);
            LogUtil.Log(ChillyRoomService.GetSdk().Accounts.StateManager.State.User.BetaAccess);
            // AsyncHttpAgentConfig.ProxySetting = ("************", 8888);
            if (ChillyRoomService.GetSdk().Accounts.StateManager.State.User.LegacyGateway.Host == null
                || ChillyRoomService.GetSdk().Accounts.StateManager.State.User.LegacyGateway.Token == null) {
                ShowMessage(ScriptLocalization.Get("cloudsave/server_unknow_error", "服务器异常，请稍后重试"));
                return;
            }
            
            #region UNITY_EDITOR

            LifeCycleManager.IsActive = true;

            #endregion

            if (ChannelConfig.UseChillyAccount && !ChannelConfig.SupportChannelLogin) {
                //如果是凉屋账号则设置android端的uid
                string targetUID = string.IsNullOrEmpty(PlayerSaveData.Inst.last_account_id)
                    ? CloudSaveUtil.mac
                    : PlayerSaveData.Inst.last_account_id;
                if (!String.IsNullOrEmpty(targetUID)) {
                    //Debug.Log($"PlayerSaveData.Inst.last_account_id {PlayerSaveData.Inst.last_account_id},CloudSaveUtil.mac:{CloudSaveUtil.mac}");
                    CloudSaveUtil.SetAndroidUid(targetUID);
                }
            }

            if (LifeCycleManager.IsActive && ChannelConfig.IsTapTapTest) {
                TapTapTestUtil.ShowTapTapTestDialog();
            }

            CommonUiUtil.ShowLoadingWindow(_cloudSaveCanvas, 15);
            // LoginApi.Inst.DnloadSave(onLoadGameSuccess, onLoadGameFail);
            FuncForHotFixBeforeFetch();
            if (BlobCloudSaveAndNewLogOffUtil.UseBlobCloudSave()) {
                FetchData();
            } else {
                //查绑定
                LoginApi.Inst.CheckSaveMac(onCheckSaveMac);
            }
        }

        /// <summary>
        /// 拉取云端存档
        /// </summary>
        public static async Task<(bool, ChillyRoom.Services.Core.Errors.ErrorResponse)> FetchData(bool showUI = false) {
            ChillyRoom.Services.Core.Errors.ErrorResponse errorResponse = null;
#if UNITY_EDITOR
            if (UnityEditor.EditorPrefs.GetBool("HideLogin", true)) {
                CommonUiUtil.HideLoadingWindow(_cloudSaveCanvas);
                AfterFetchCloudSaveEvent?.Invoke(false, ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode.UnknowError,
                    null, new Exception("HideLogin"));
                ShowMsgAfterLoginSuccessUiHide(
                    string.Format(ScriptLocalization.Get("cloudsave/fetch_fail_extra", "获取存档失败：{0}"),
                        CloudSaveLocalization.GetMessage(Err.Unknow_error))); // 未知错误
                return (false, errorResponse);
            }
#endif
            try {
                CancellationTokenSource source = new CancellationTokenSource();
                source.CancelAfter(TimeSpan.FromSeconds(30f));
                var result = await ChillyRoomService.GetSdk().BlobSaveClient.DownloadAsync(source.Token);
                if (result != null && result.Data != null) {
                    onLoadGameSuccess(result.Data);
                } else {
                    OnLoadGameTimeOutOrResponseNull();
                }
            } catch (ChillyRoom.Services.Core.Errors.ApiException<ChillyRoom.Services.Core.Errors.ErrorResponse>
                     exception) {
                Debug.LogError($"FetchData error {exception?.Message} {exception?.Result?.Error}");
                errorResponse = exception?.Result;
                AfterFetchCloudSaveEvent?.Invoke(false,
                    (ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode)errorResponse?.Error, null, exception);
                switch (exception.StatusCode) {
                    case -1: //请求没有发出去
                        ShowMsgAfterLoginSuccessUiHide(
                            string.Format(ScriptLocalization.Get("cloudsave/fetch_fail_extra", "获取存档失败：{0}"),
                                CloudSaveLocalization.GetMessage(Err.ConnectError) + ", -1")); // 连接失败，请稍后再尝试
                        break;
                    case 200:
                    case 400:
                        onLoadGameFail(errorResponse, exception);
                        break;
                    case 401:
                        ShowMsgAfterLoginSuccessUiHide(
                            string.Format(ScriptLocalization.Get("cloudsave/fetch_fail_extra", "获取存档失败：{0}"),
                                CloudSaveLocalization.GetMessage(Err.Unknow_error))); // 未知错误
                        break;
                    case 500:
                        ShowMsgAfterLoginSuccessUiHide(
                            string.Format(ScriptLocalization.Get("cloudsave/fetch_fail_extra", "获取存档失败：{0}"),
                                ScriptLocalization.Get("cloudsave/server_unknow_error", "服务器异常，请稍后重试"))); //服务器异常，请稍后重试
                        break;
                    default:
                        ShowMsgAfterLoginSuccessUiHide(
                            string.Format(ScriptLocalization.Get("cloudsave/fetch_fail_extra", "获取存档失败：{0}"),
                                CloudSaveLocalization.GetMessage(Err.Unknow_error) +
                                $" {exception.StatusCode} {errorResponse?.Error}"));
                        break;
                }

                CommonUiUtil.HideLoadingWindow(_cloudSaveCanvas);
            } catch (Exception exception) {
                AfterFetchCloudSaveEvent?.Invoke(false, ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode.UnknowError,
                    null, exception);
                ShowMsgAfterLoginSuccessUiHide(
                    string.Format(ScriptLocalization.Get("cloudsave/fetch_fail_extra", "获取存档失败：{0}"),
                        CloudSaveLocalization.GetMessage(Err.Unknow_error))); // 未知错误
                Debug.LogError($"FetchData error {exception}");
            }

            return (false, errorResponse);
        }

        private static void ShowMsgAfterLoginSuccessUiHide(string msg) {
            bool needDelayShow = false;
            var accountCanvas = AccountSdkUIFacade.GetCloudSaveCanvas();
            if (accountCanvas != null) {
                int childCount = accountCanvas.childCount;
                for (int i = 0; i < childCount; i++) {
                    var child = accountCanvas.GetChild(i);
                    if (child.name == "temp_message(Clone)") {
                        var tempMsg = child.transform.Find("temp_message");
                        if (tempMsg != null) {
                            var text = tempMsg.GetComponent<Text>();
                            if (text.text == ScriptLocalization.Get("cloudsave/login_success", "登录成功")) {
                                needDelayShow = true;
                                StartDelayShowMsgTimer(1f, msg);
                            }
                        }
                    }
                }
            }

            if (!needDelayShow) {
                DoShowMsg(msg);
            }
        }

        private static void StartDelayShowMsgTimer(float delay,string msg) {
            Timer.Register(delay, false, true, () => { DoShowMsg(msg); });
        }

        private static void DoShowMsg(string msg) {
            CommonUiUtil.ShowMsg(AccountSdkController.ShowLoadingTransformParent, msg);
        }

        private static void SetUidSundry() {
            string[] data;
            if (ChillyRoomService.GetSdk().Accounts.StateManager.State.User.LegacyGateway.Host.Contains(":")) {
                data = ChillyRoomService.GetSdk().Accounts.StateManager.State.User.LegacyGateway.Host.Split(':');
                LoginApi.LastGatewaySvrIP_Port = ChillyRoomService.GetSdk().Accounts.StateManager.State.User.LegacyGateway.Host
                    .Replace(":", "_");
            } else {
                if (NewSDKManager.Inst.IsServiceHttps()) {
                    data = new[] { ChillyRoomService.GetSdk().Accounts.StateManager.State.User.LegacyGateway.Host, "443" };
                    LoginApi.LastGatewaySvrIP_Port =
                        ChillyRoomService.GetSdk().Accounts.StateManager.State.User.LegacyGateway.Host + "_443";
                } else {
                    data = new[] { ChillyRoomService.GetSdk().Accounts.StateManager.State.User.LegacyGateway.Host, "80" };
                    LoginApi.LastGatewaySvrIP_Port =
                        ChillyRoomService.GetSdk().Accounts.StateManager.State.User.LegacyGateway.Host + "_80";
                }
            }

            CloudSaveUtil.uid = ChillyRoomService.GetSdk().Accounts.StateManager.State.User.Id.ToString();
            CloudSaveUtil.chilly_uid = (uint)ChillyRoomService.GetSdk().Accounts.StateManager.State.User.Id;
            PlayerSaveData.Inst.last_account_id = CloudSaveUtil.uid;
            PlayerSaveData.Inst.last_chilly_uid = ChillyRoomService.GetSdk().Accounts.StateManager.State.User.Id.ToString();
            PlayerSaveData.Inst.last_pid = ChillyRoomService.GetSdk().Accounts.StateManager.State.User.PlayerId.ToString();
            LoginApi.Inst.m_gatewaySvr = LoginApi.GetClient(data[0], ushort.Parse(data[1]));
            ChillyRoomService.GetSdk().BlobSaveAddToken();
            var token = ChillyRoomService.GetSdk().Accounts.StateManager.State.User.LegacyGateway.Token; 
            if (!string.IsNullOrEmpty(token)) {
                LoginApi.Inst.m_gatewaySvr.AsHttpClient().DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            }
            LoginApi.Inst.m_accountId = (uint)ChillyRoomService.GetSdk().Accounts.StateManager.State.User.Id;
            LoginApi.Inst.m_token = ChillyRoomService.GetSdk().Accounts.StateManager.State.User.LegacyGateway.Token;
            LifeCycleManager.IsActive = ChillyRoomService.GetSdk().Accounts.StateManager.State.User.BetaAccess;
        }
        
        static void ShowMessage(string msg, float duration = 2.5f) {
            CommonUiUtil.ShowMsg(AccountSdkController.ShowLoadingTransformParent, msg, duration);
        }

        private static CloudSaveGameData savingData;

        public static void SaveGame() {
            if (Debug.isDebugBuild || LogUtil.IsShowLog) {
                Debug.Log("SaveGame");
            }

            if (LoginApi.Inst.IsSaveServerNull()) {
                TAUtil.Track("upload_save",new Dictionary<string, object>() {
                    {"upload_suc",false},
                    {"upload_fail_res","server is null"},
                });
                return;
            }
            if (!LifeCycleManager.Instance.SupportCloudSaveUpload) {
                Debug.Log("Not SupportCloudSaveUpload");
                TAUtil.Track("upload_save",new Dictionary<string, object>() {
                    {"upload_suc",false},
                    {"upload_fail_res","not SupportCloudSaveUpload"},
                });
                return;
            }

            savingData = CloudSaveGameData.CreateFromPlayerPref();
            Action saveAction = async () => {
                //测试服先往gp、ios服务器发存档，之后往我们服务器发
                if ((ChannelConfig.IsGPTest || ChannelConfig.IsIOSTest)
                    && (ChillyRoomService.GetSdk().Accounts.StateManager.State.Session.LoginBy == UniversalSdkType.GooglePlay
                        || ChillyRoomService.GetSdk().Accounts.StateManager.State.Session.LoginBy == UniversalSdkType.Apple)) {
                    if (savingData.cloudSaveId != CloudSaveUtil.GetCurrentAccountSaveId()) {
                        ShowMessage( I2.Loc.ScriptLocalization.Get("cloudsave/upload_fail_different_account","上传失败:该存档不属于此账号"));
                        if (Debug.isDebugBuild||LogUtil.IsShowLog) {
                            Debug.LogFormat("LocalId:{0}, NetId:{1}", savingData.cloudSaveId, CloudSaveUtil.GetCurrentAccountSaveId());
                        }
                        UiUtil.HideLoadingWindow(UICanvasRoot.Inst.transform);
                        TAUtil.Track("upload_save",new Dictionary<string, object>() {
                            {"upload_suc",false},
                            {"upload_fail_res","上传失败:该存档不属于此账号"},
                            {"extra_msg",$"wid:{savingData.cloudSaveId} rid:{CloudSaveUtil.GetCurrentAccountSaveId()}"},
                        });
                        return;
                    }
                    //迁移失败不允许上传存档 因为可能污染线上存档
                    if (!MoveCloudSaveManager.Instance.HasMoveSuccess) {
                        string msg = $"{ScriptLocalization.Get("cloudsave/upload_fail", "上传失败")}" +
                                     $":{ScriptLocalization.Get("ui/move_data_fail_try_restart", "存档迁移失败，请检查互联网或重启游戏后重试")},{MoveCloudSaveManager.Instance.CurrentMoveStep.GetHashCode()}" +
                                     (string.IsNullOrEmpty(MoveCloudSaveManager.Instance.MoveErrMsg) ? string.Empty : $",{MoveCloudSaveManager.Instance.MoveErrMsg}");
                        ShowMessage(msg);
                        TAUtil.Track("upload_save",new Dictionary<string, object>() {
                            {"upload_suc",false},
                            {"upload_fail_res","test not movesuc"},
                        });
                        return;                        
                    }
                    UiUtil.ShowLoadingWindow(UICanvasRoot.Inst.transform, 8);
                    var SendDataToLegencyResult = await MoveCloudSaveManager.Instance.SendDataToLegency(savingData);
                    if (!SendDataToLegencyResult.success) {
                        if (Debug.isDebugBuild||LogUtil.IsShowLog) {
                            Debug.Log("onSaveGameFail SendDataToLegency");
                        }
                        string reason = CloudSaveLocalization.GetMessage(Err.Net_err_try_again)+"，origin err:"+SendDataToLegencyResult.msg;
                        ShowMessage(I2.Loc.ScriptLocalization.Get("cloudsave_save_fail") + ":" +reason);
                        UiUtil.HideLoadingWindow(UICanvasRoot.Inst.transform);
                        TAUtil.Track("upload_save",new Dictionary<string, object>() {
                            {"upload_suc",false},
                            {"upload_fail_res","test send legacy fail"},
                        });
                        return;
                    }
                }
                
                SetLoadTextAction?.Invoke(I2.Loc.ScriptLocalization.Get("cloudsave_saving"));
                FuncForHotFixBeforeUpload();
                CloudSaveRunner.Instance.AutoUpload(savingData);
            };
            
            Action compareAndUploadAction = () => {
                string isBackUpContent = "";
                if (LoadedGameData != null && savingData.GetProgressScope() < LoadedGameData.GetProgressScope()) {
                    isBackUpContent = ScriptLocalization.Get("cloudsave/local_cloudsave_fall_behind","本地存档进度落后于云端存档，");
                }

                //对比云端存档和本地存档购买过的计费点差别
                int loadedGameData_restoreKeys = LoadedGameData == null ? 0 : LoadedGameData.itemData.restoreKeys.Count;
                if (savingData.itemData.restoreKeys.Count > loadedGameData_restoreKeys &&
                    savingData.cloudSaveId == CloudSaveUtil.GetCurrentAccountSaveId()) {
                    List<string> resultRestore = LoadedGameData == null
                        ? savingData.itemData.restoreKeys
                        : savingData.itemData.restoreKeys.Except(LoadedGameData.itemData.restoreKeys).ToList();
                    int heroNum = resultRestore.Intersect(RestoreRole.key2Hero.Keys).Count();
                    int skinNum = resultRestore.Intersect(RestoreRole.key2Skin.Keys).Count();
                    int heroSkillNum = resultRestore.Where(_ => _.StartsWith("skill")).Count();
                    int rebornCard = resultRestore.Where(_ => _.Equals("reborn_card")).Count();
                    int plantpotCount = resultRestore.Where(_ => _.Contains("plantpot_")).Count();
                    List<string> describe = new List<string>();
                    if (heroNum > 0)
                        describe.Add(string.Format(ScriptLocalization.Get("cloudsave/char_x_count","角色{0}个") , heroNum));
                    if (skinNum > 0)
                        describe.Add(string.Format(ScriptLocalization.Get("cloudsave/skin_x_count","皮肤{0}个"), skinNum));
                    if (heroSkillNum > 0)
                        describe.Add(string.Format(ScriptLocalization.Get("cloudsave/skill_x_count","技能{0}个"), heroSkillNum));
                    if (plantpotCount > 0)
                        describe.Add(string.Format(ScriptLocalization.Get("cloudsave/plantpot_x_count","花圃{0}个"), plantpotCount));
                    if (rebornCard > 0)
                        describe.Add(ScriptLocalization.Get("RevivalCard","超级复活卡"));
                    // 游戏里的宠物没有rmb直接购买的，要么是宝石要么是小鱼干，所以不统计宠物
                    string result = string.Join(LanguageUtil.GetSeparator().ToString(), describe.ToArray());
                    UiUtil.ShowDialogWindow(_cloudSaveCanvas, I2.Loc.ScriptLocalization.Get("I_mul_tip6"),
                        string.Format(ScriptLocalization.Get("cloudsave/local_more_payment_point_upload","本地新增付费项目：{0}，请上传本地存档数据到云端") , result),
                        () => {
                            if (LoadedGameData == null ||
                                savingData.GetProgressScope() >= LoadedGameData.GetProgressScope()) {
                                saveAction();
                            } else {
                                UiUtil.ShowDialogWindow(_cloudSaveCanvas, I2.Loc.ScriptLocalization.Get("I_mul_tip6"),
                                    I2.Loc.ScriptLocalization.Get("cloudsave_second_confirm_save","本地存档进度落后于云端存档，确定要覆盖云端存档吗？"), () => {
                                        saveAction();
                                    }, () => {
                                        TAUtil.Track("upload_save",new Dictionary<string, object>() {
                                            {"upload_suc",false},
                                            {"upload_fail_res","cancel 1"},
                                        });
                                    });
                            }
                        }, null, 2);
                } else {
                    string content = ScriptLocalization.Get("cloudsave/confirm_save_easy", "#确定覆盖云端存档且上传本地数据吗？");
                    if (!string.IsNullOrEmpty(isBackUpContent)) {
                        content = string.Format(
                            ScriptLocalization.Get("cloudsave/confirm_save", "确定覆盖云端存档且上传本地数据吗？{0}所有行为需自行负责"),
                            isBackUpContent);
                    }
                    UiUtil.ShowDialogWindow(_cloudSaveCanvas, I2.Loc.ScriptLocalization.Get("I_mul_tip6"),content,
                        () => {
                            saveAction();
                        }, () => {
                            TAUtil.Track("upload_save",new Dictionary<string, object>() {
                                {"upload_suc",false},
                                {"upload_fail_res","cancel 2"},
                            });
                        });
                }
            };

            if ((ChannelConfig.IsAllGP || ChannelConfig.IsAllIOS)) {
                bool moveStepMatchSuc = MoveCloudSaveManager.Instance.CurrentMoveStep ==
                                        MoveCloudSaveManager.MoveStep.LegencyMoveUploadLeFail ||
                                        MoveCloudSaveManager.Instance.CurrentMoveStep ==
                                        MoveCloudSaveManager.MoveStep.LegencyMoveUploadCRFail ||
                                        MoveCloudSaveManager.Instance.CurrentMoveStep ==
                                        MoveCloudSaveManager.MoveStep.LegencyFetchFail ||
                                        MoveCloudSaveManager.Instance.CurrentMoveStep ==
                                        MoveCloudSaveManager.MoveStep.LegencyAccountChange;
                bool optionMoveStepMatchSuc = MoveCloudSaveManager.Instance.OptionalMoveCloudSaveManager.CurrentMoveStep ==
                                              MoveCloudSaveManager.MoveStep.LegencyMoveUploadLeFail ||
                                              MoveCloudSaveManager.Instance.OptionalMoveCloudSaveManager.CurrentMoveStep ==
                                              MoveCloudSaveManager.MoveStep.LegencyMoveUploadCRFail ||
                                              MoveCloudSaveManager.Instance.OptionalMoveCloudSaveManager.CurrentMoveStep ==
                                              MoveCloudSaveManager.MoveStep.LegencyFetchFail || 
                                              MoveCloudSaveManager.Instance.OptionalMoveCloudSaveManager.CurrentMoveStep ==
                                              MoveCloudSaveManager.MoveStep.LegencyAccountChange;
                if ((moveStepMatchSuc || optionMoveStepMatchSuc)
                    && !MoveCloudSaveManager.Instance.OptionalMoveCloudSaveManager.IsNewPlayer) {
                    //迁移失败的给提示玩家 再次引导迁移
                    UIWindowDialogTextSelect.TextSelectDialogParam param =
                        new UIWindowDialogTextSelect.TextSelectDialogParam {
                            title = ScriptLocalization.Get("I_tip", "提示"),
                            content = ScriptLocalization.Get("account/upload_cannot_move", "上传存档后无法再迁移存档，确认要上传吗?"),
                            text1 = ScriptLocalization.Get("account/upload_save_data", "上传存档"),
                            text2 = ScriptLocalization.Get("account/try_move_again", "再次尝试迁移"),
                        };

                    UIWindowDialogTextSelect.ShowDialogTextSelect(UICanvasRoot.Inst.transform, param,
                        () => {
                            if (moveStepMatchSuc) {
                                MoveCloudSaveManager.Instance.TryMoveAgain();
                            } else if(optionMoveStepMatchSuc){
                                MoveCloudSaveManager.Instance.OptionalMoveCloudSaveManager.TryMoveAgain();
                            }
                            TAUtil.Track("upload_save",new Dictionary<string, object>() {
                                {"upload_suc",false},
                                {"upload_fail_res","try move again"},
                            });
                        },
                        () => {
                            compareAndUploadAction();
                        });
                } else {
                    compareAndUploadAction();
                }
            } else {
                compareAndUploadAction();
            }
        }

        public static void onSaveGameSuccess() {
            if (Debug.isDebugBuild || LogUtil.IsShowLog) {
                Debug.Log("onSaveGameSuccess");
            }

            UiUtil.HideLoadingWindow(_cloudSaveCanvas);
            ShowMessage(I2.Loc.ScriptLocalization.Get("cloudsave_save_success"));
            //FetchGame();
            if (!string.IsNullOrEmpty(savingData.cloudSaveId) &&
                string.IsNullOrEmpty(CloudSaveUtil.GetLocalSaveId())) {
                CloudSaveUtil.SetLocalSaveId(savingData.cloudSaveId);
            }
        }

        public static void onSaveGameFail(Err code) {
            if (Debug.isDebugBuild || LogUtil.IsShowLog) {
                Debug.Log("onSaveGameFail");
            }

            string reason = string.Empty;
            if (code == Err.Invalid) {
                reason = I2.Loc.ScriptLocalization.Get("Err/DataHashChanged","存档完整性校验异常，请稍后重试");
            } else {
                reason = CloudSaveLocalization.GetMessage(code);
            }

            ShowMessage(I2.Loc.ScriptLocalization.Get("cloudsave_save_fail") + ":" + reason);
            UiUtil.HideLoadingWindow(_cloudSaveCanvas);
        }

        /// <summary>
        /// 本地的存档ID是否与账号相同
        /// </summary>
        static bool saveIdValid {
            get {
                string localSaveId = CloudSaveUtil.GetLocalSaveId();
                string accountSaveId = CloudSaveUtil.GetCurrentAccountSaveId();
                return string.IsNullOrEmpty(localSaveId) || localSaveId == accountSaveId;
            }
        }

        public static void LoadGame() {
            if (!LifeCycleManager.Instance.SupportCloudSaveDownload) {
                Debug.Log("Not SupportCloudSaveDownload");
                return;
            }
            
            CloudSaveGameData data= LoadedGameData;
            if (data == null) {
                data = CloudSaveGameData.CreateNew();
                // if (saveIdValid) {
                //     ShowMessage(I2.Loc.ScriptLocalization.Get("cloudsave_fetch_fail"));
                //     return;
                // } else {
                //     data = CloudSaveGameData.CreateNew();
                // }
            }

            if (data != null) {
                CloudSaveExtraData extraRemoteData = CloudSaveExtraData.CreateExtraData(data);
                CloudSaveGameData tempLocalData = CloudSaveGameData.CreateFromPlayerPref();
                CloudSaveExtraData extraLocalData = CloudSaveExtraData.CreateExtraData(tempLocalData);
                CloudSaveExtraData different = extraLocalData - extraRemoteData;
                string content = "";
                string isBackUpContent = "";
                if (data.GetProgressScope() < CloudSaveExtraData.GetPassTimes()) {
                    isBackUpContent = I2.Loc.ScriptLocalization.Get("cloudsave/net_cloudsave_fall_behind_local_cloudsave","云端存档进度落后于本地存档，");
                }

                //如果是开启了合并计费点 并且是同一个人的账号 则给的比对信息是合并计费点的 反之不是 
                string exceptDes = EmailUrl.emailUrl.IsDownloadMergeCharegePoint &&
                                   (tempLocalData.cloudSaveId == String.Empty ||
                                    tempLocalData.cloudSaveId == data.cloudSaveId)
                    ? different.GetExceptRMBPayDescribe()
                    : different.ToString();
                if (different.GetHasPositiveValue() && !string.IsNullOrWhiteSpace(exceptDes)) {
                    content = string.Format(ScriptLocalization.Get("cloudsave/confirm_dnload_will_lost_ex","确定下载云端存档吗？{0}此操作将会失去：{1}，这将覆盖本地的进度和扣除当前继续游戏状态存档中已消耗的材料") , isBackUpContent,
                        exceptDes);
                } else {
                    content = string.Format(ScriptLocalization.Get("cloudsave/confirm_dnload_will_cover_ex","确定下载云端存档吗？{0}这将覆盖本地的进度和扣除当前继续游戏状态存档中已消耗的材料"), isBackUpContent);
                }

                UiUtil.ShowDialogWindow(_cloudSaveCanvas, I2.Loc.ScriptLocalization.Get("I_mul_tip6"), content,
                    () => {
                        CloudSaveRunner.Instance.AutoDnload(tempLocalData);
                        FuncForHotFixAfterDownload();
                    }, null);
            } else {
                ShowMessage(I2.Loc.ScriptLocalization.Get("cloudsave_undetected"));
            }
        }

        public static void ClearLocalData() {
            var data = CloudSaveGameData.CreateNew();
            data.CoverPlayerPref();
            LocalDataChangeAction?.Invoke(true, data);
        }
        
        /// <summary>
        /// 账号相关，刷新其本地存档
        /// </summary>
        public static void RefreshLocalData() {
            LogUtil.Log("RefreshLocalData...", NewSDKManager.Move_Tag);
            var data = CloudSaveGameData.CreateFromPlayerPref();
            LocalDataChangeAction?.Invoke(true, data);
        }

        public static void OnAfterFetchCloudSave(bool result, ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode err, CloudSaveGameData data) {
            LoadedGameData = data;
            AfterFetchCloudSaveEvent?.Invoke(result, err, data, null);
        }

        public static void SetCanvasOrder(int order) {
            if (_cloudSaveCanvas != null) {
                var canvas=_cloudSaveCanvas.GetComponent<Canvas>();
                if (canvas != null) {
                    canvas.sortingOrder = order;
                }
            }
        }
        private static void RecordUploadGameCount(bool suc) {
            if (suc) {
                PlayerSaveData.Inst.upload_save_count++;
            }
        }
        
        private static void RecordUploadGameRpcCount(bool suc, Err err) {
            if (suc) {
                PlayerSaveData.Inst.upload_save_count++;
            }
        }

        private static void RecordDownloadSaveCount(bool suc, CloudSaveGameData data) {
            if (suc) {
                PlayerSaveData.Inst.download_save_count++;
                SaveUtil.DealRijAndJsonTest();
            }
        }

        private static void SdkOnLogin(string data, CoreKit.Agent.SdkAgent agent) {
            TAUtil.Track("option_move",new Dictionary<string, object>() {
                {"login_by", (int)agent.agentConfig.universalType},
                {"pull_legacy_login", true },
                {"origin_legacy_fail_total",NewCloudSaveAgent.ClickLegencyLoginFailTotalCount}
            });
        }
        private static void SdkOnLoginFail(SdkError error, CoreKit.Agent.SdkAgent agent) {
            TAUtil.Track("option_move",new Dictionary<string, object>() {
                {"login_by", (int)agent.agentConfig.universalType},
                {"pull_legacy_login", false },
            });
        }

        private static void OnConfigurationChanged(string data) {
            UICanvas.CameraSizeReconfiguration();
        }
        
        public static void RefreshFetchDataTag() {
            HasFetchData = false;
            SetLoadedDataToNull();
        }

        public static void SetLoadedDataToNull() {
            LoadedGameData = null;
        }

        #region 老的RPC协议拉取存档逻辑
        /// <summary>
        /// 老的拉取存档回调
        /// </summary>
        public static event Action<bool , Err, CloudSaveGameData, Exception> AfterFetchCloudSaveRpcEvent;
        /// <summary>
        /// 老的上传回调
        /// </summary>
        internal static Action<bool,Err> UpLoadGameRpcAction;
        /// <summary>
        /// 老的上传后回调
        /// </summary>
        internal static Action<bool,Err,byte[]> AfterUpLoadGameRpcAction;
        
        private static void DealBindBtnAndCloudSaveDataRpcState(bool suc, Err err, CloudSaveGameData data, Exception exception) {
            var btnContent = AccountSdkUIFacade.GetAccountSdkUIFacade().transform.Find("AccountInformationView").transform
                .Find("BtnContent");
            //关闭绑定gp、ios的按钮
            btnContent.Find("BindAppleBtn").gameObject.SetActive(false);
            btnContent.Find("BindGoogleBtn").gameObject.SetActive(false);

            if (suc) {
                CloudSaveDataState = SaveDataState.Exist;
            } else if (err == Err.Record_cannot_find) {
                CloudSaveDataState = SaveDataState.NotExist;
            } else {
                CloudSaveDataState = SaveDataState.UnKnown;
            }
        }
        
        private static async void DealGPAndIosSendDataToLegencyRpcWhenBinded(bool suc, Err err, CloudSaveGameData data, Exception exception) {
            AutoUploadLegencySave autoUploadLegencySave =
                ConfigManager.GetCurrectUseConfig<AutoUploadLegencySave>();
            var loginBy = ChillyRoomService.GetSdk().Accounts?.StateManager?.State?.Session?.LoginBy;
            if (loginBy is UniversalSdkType.Apple or UniversalSdkType.GooglePlay && suc && autoUploadLegencySave.AutoUpload) {
                bool canSend = false;
                if (autoUploadLegencySave.NeedInspect) {
                    var canUpload = await LegencyLoginAndCloudSaveManager.Instance.Agent.PayInspect();
                    canSend = canUpload.Item1;
                } else {
                    canSend = true;
                }

                if (canSend) {
                    MoveCloudSaveManager.Instance.SendDataToLegency(data);
                }
            }
        }
        
        public static void onLoadGameRpcSuccess(string args) {
            CommonUiUtil.HideLoadingWindow(_cloudSaveCanvas);
            HasFetchData = true;
            string json = args; //ZipHelper.GZipDecompressString(args);
            //Debug.Log(json);
            if (string.IsNullOrEmpty(json)) {
                // windowSave.SetLoadText(I2.Loc.ScriptLocalization.Get("cloudsave_undetected"));
                AfterFetchCloudSaveRpcEvent?.Invoke(false, Err.UnExpectedException, null, null);
            } else {
                try {
                    // json = System.IO.File.ReadAllText(@"C:\Users\<USER>\Downloads\download_save_data (5)\download_save_data (5).json");
                    LoadedGameData = SaveUtil.OpenNewtonJsonTest
                        ? NewtonJsonUtil.ParseJson<CloudSaveGameData>(json)
                        : Abo.JsonUtil.ParseJson<CloudSaveGameData>(json);
                    // Debug.LogError(LoadedGameData.cloudSaveId);
                    if (ChannelConfig.IsTapTapTest && LoadedGameData.cloudSaveId.StartsWith("TapTap_")) {
                        LoadedGameData.cloudSaveId = LoadedGameData.cloudSaveId.Replace("TapTap_", "TapTapTest_");
                    } 
                    
                    if (ChannelConfig.IsGP_VNM) {
                        var matchResult = Regex.Match(LoadedGameData.cloudSaveId, @"^GP_(?<id>\d+)$");
                        if (matchResult.Success) {
                            LoadedGameData.cloudSaveId = $"GP_VNM_{matchResult.Groups[1]}";
                        }
                    }

                    AfterFetchCloudSaveRpcEvent?.Invoke(true, Err.Success, LoadedGameData, null);
                } catch (Exception e) {
                    if (Debug.isDebugBuild || LogUtil.IsShowLog) {
                        Debug.LogError(e);
                    }
                    AfterFetchCloudSaveRpcEvent?.Invoke(false, Err.UnExpectedException, null, null);
                }

                if (ChannelConfig.IsTapTapTest) {
                    UITitle.SolveTatTapTestLimit();
                    //再次拉取配置
                    if (!ConfigManager.FetcheConfigSuccess) {
                        ConfigManager.FetchConfig();
                    }

                    CloudSaveRunner.Instance.ShowAutoUploadTips();
                    CloudSaveRunner.Instance.AutoDnload(CloudSaveGameData.CreateFromPlayerPref());
                }
            }
        }
        
        static void onLoadGameFail(Err code, Exception exception) {
            if (Debug.isDebugBuild || LogUtil.IsShowLog) {
                Debug.Log("onLoadGameFail" + code);
            }

            CommonUiUtil.HideLoadingWindow(_cloudSaveCanvas);
            if (code == Err.Record_cannot_find) {
                // if (!saveIdValid) {
                //     windowSave.btnLoad.gameObject.SetActive(true);
                // } else {
                //     windowSave.btnLoad.gameObject.SetActive(false);
                // }
                SetLoadTextAction?.Invoke(I2.Loc.ScriptLocalization.Get("cloudsave_undetected"));
            } else if (code == Err.Version_not_match) {
                ShowMessage(ScriptLocalization.Get("cloudsave_fetch_fail") + ":" + ScriptLocalization.Get("cloudsave/version_not_match","线上云存档版本高于本机，请更新客户端") );
            } else if (code == Err.remoteCloudSaveDataBroke) {
                SetLoadTextAction?.Invoke(ScriptLocalization.Get("cloudsave/cloudsave_broke"));
                ShowMessage(ScriptLocalization.Get("cloudsave_fetch_fail") + ":" +
                            CloudSaveLocalization.GetMessage(code));
            } else {
                ShowMessage(ScriptLocalization.Get("cloudsave_fetch_fail") + ":" +
                            CloudSaveLocalization.GetMessage(code));
            }

            AfterFetchCloudSaveRpcEvent?.Invoke(false, code, null, exception);
        }

        public static async Task<(bool,Err)> ForceFetchData() {
            LogUtil.Log("ForceFetchData");
            TaskCompletionSource<(bool,Err)> tcs = new TaskCompletionSource<(bool,Err)>();
            LoginApi.Inst.DnloadSave(_=> {
                tcs.TrySetResult((true,Err.Success));
                onLoadGameRpcSuccess(_);
            }, (_,exp) => {
                tcs.TrySetResult((false,_));
                onLoadGameFail(_,exp);
            });
            var task = tcs.Task;
            var delayTask = DelayedTask();
            if (await Task.WhenAny(task, delayTask) == task) {
                return await task;
            }

            return (false,Err.Net_err_try_again);
        }
        private static async Task<(bool,Err)> DelayedTask() {
            await Task.Delay(30000);
            return (false,Err.Net_err_try_again);
        }
        
        public static void onCheckSaveMac(Err errCode, Exception exception) {
            if (Debug.isDebugBuild || LogUtil.IsShowLog) {
                Debug.Log("onCheckSaveMac" + errCode);
            }


            if (errCode != Err.Success) {
                AfterFetchCloudSaveRpcEvent?.Invoke(false, errCode, null, exception);
            }

            if (errCode == Err.Success) {
                SetLoadTextAction?.Invoke(ScriptLocalization.Get("cloudsave_fetching","正在从云端获取存档"));
                LoginApi.Inst.DnloadSave(onLoadGameRpcSuccess, onLoadGameFail);
            } else if (errCode == Err.Record_mac_already_bind) {
                LoginApi.Inst.UnbindMac2((unbindErrCode, waitSeconds) => {
                    if (unbindErrCode == Err.Success) {
                        SetLoadTextAction?.Invoke(ScriptLocalization.Get("cloudsave_fetching","正在从云端获取存档"));
                        LoginApi.Inst.DnloadSave(onLoadGameRpcSuccess, onLoadGameFail);
                    } else if (unbindErrCode == Err.Operate_too_often) {
                        TimeSpan waitTime = TimeSpan.FromSeconds(waitSeconds);
                        TimeUtil.TimeUnit unit = TimeUtil.GetTimeMaxUnitFromDay2Minute(waitTime, out int units);
                        string describe;
                        if (unit == TimeUtil.TimeUnit.Day) {
                            describe = string.Format(
                                ScriptLocalization.Get("account/record_unbind_device_limit_day",
                                    "该设备已绑定其他账号且解绑失败, 请在{0}天后重试"), units);
                        } else if (unit == TimeUtil.TimeUnit.Hour) {
                            describe = string.Format(
                                ScriptLocalization.Get("account/record_unbind_device_limit_hour",
                                    "该设备已绑定其他账号且解绑失败, 请在{0}小时后重试"), units);
                        } else if (unit == TimeUtil.TimeUnit.Minute) {
                            describe = string.Format(
                                ScriptLocalization.Get("account/record_unbind_device_limit_minute",
                                    "该设备已绑定其他账号且解绑失败, 请在{0}分钟后重试"), units);
                        } else {
                            describe = string.Format(ScriptLocalization.Get("cloudsave/wait_time_invalid","等待时间异常：{0}") ,waitTime);
                        }

                        CommonUiUtil.ShowMsg(AccountSdkController.ShowLoadingTransformParent,
                            string.Format(ScriptLocalization.Get("cloudsave/fetch_fail_extra","获取存档失败：{0}"),describe));
                        CommonUiUtil.HideLoadingWindow(_cloudSaveCanvas);
                        //CloudSaveUtil.Reset();
                    } else {
                        CommonUiUtil.ShowMsg(AccountSdkController.ShowLoadingTransformParent,
                            string.Format(ScriptLocalization.Get("cloudsave/fetch_fail_extra","获取存档失败：{0}"),CloudSaveLocalization.GetMessage(Err.Record_mac_already_bind)));
                        CommonUiUtil.HideLoadingWindow(_cloudSaveCanvas);
                        //CloudSaveUtil.Reset();
                    }
                });
            } else if (errCode == Err.Record_bind_limit) {
                //绑定受限
                LoginApi.Inst.GetSaveTimeInfo(
                    (Err getSaveTimeInfoErrCode, long uploadTicks, int rebindSeconds, int resetSeconds) => {
                        string describe = "未知错误，请稍后尝试";
                        if (getSaveTimeInfoErrCode == Err.Success) {
                            TimeSpan waitTime = TimeSpan.FromSeconds(rebindSeconds);
                            TimeUtil.TimeUnit timeUnit = TimeUtil.GetTimeMaxUnitFromDay2Minute(waitTime, out int units);
                            if (timeUnit == TimeUtil.TimeUnit.Day) {
                                describe = string.Format(
                                    ScriptLocalization.Get("account/record_bind_device_limit_day",
                                        "绑定新设备频率过高,请{0}天后重试"), units);
                            } else if (timeUnit == TimeUtil.TimeUnit.Hour) {
                                describe = string.Format(
                                    ScriptLocalization.Get("account/record_bind_device_limit_hour",
                                        "绑定新设备频率过高,请{0}小时后重试"), units);
                            } else if (timeUnit == TimeUtil.TimeUnit.Minute) {
                                describe = string.Format(
                                    ScriptLocalization.Get("account/record_bind_device_limit_minute",
                                        "绑定新设备频率过高,请{0}分钟后重试"), units);
                            } else {
                                describe = string.Format(ScriptLocalization.Get("cloudsave/wait_time_invalid","等待时间异常：{0}") ,waitTime);
                            }
                        } else if (getSaveTimeInfoErrCode == Err.Unknow_error) {
                            describe = ScriptLocalization.Get("cloudsave/server_unknow_error", "服务器异常，请稍后重试");
                        } else if (getSaveTimeInfoErrCode == Err.Record_cannot_find) {
                            describe = ScriptLocalization.Get("cloudsave_undetected","未检测到云端存档");
                        } else {
                            describe = CloudSaveLocalization.GetMessage(getSaveTimeInfoErrCode);
                        }

                        CommonUiUtil.ShowMsg(AccountSdkController.ShowLoadingTransformParent,
                            string.Format(ScriptLocalization.Get("cloudsave/fetch_fail_extra","获取存档失败：{0}"),describe));
                        CommonUiUtil.HideLoadingWindow(_cloudSaveCanvas);
                    });
                //CloudSaveUtil.Reset();
            } else if (errCode == Err.Record_cannot_find) {
                HasFetchData = true;
                // CommonUiUtil.ShowMsg(AccountSdkController.ShowLoadingTransformParent,"登录成功");
                CommonUiUtil.HideLoadingWindow(_cloudSaveCanvas);
                if (ChannelConfig.IsTapTapTest) {
                    //taptaptest因为taptap的存档迁移发生在DnloadSave的过程中，所以得去拉
                    SetLoadTextAction?.Invoke(ScriptLocalization.Get("cloudsave_fetching","正在从云端获取存档"));
                    LoginApi.Inst.DnloadSave(onLoadGameRpcSuccess, onLoadGameFail);

                    UITitle.SolveTatTapTestLimit();
                    //再次拉取配置
                    if (!ConfigManager.FetcheConfigSuccess) {
                        ConfigManager.FetchConfig();
                    }

                    CloudSaveRunner.Instance.ShowAutoUploadTips();
                    string localSaveId = CloudSaveUtil.GetLocalSaveId();
                    string accountSaveId = CloudSaveUtil.GetCurrentAccountSaveId();
                    if (!string.IsNullOrEmpty(localSaveId) && localSaveId != accountSaveId) {
                        //直接清空本地存档
                        ClearLocalData();
                    }
                }
            } else if (errCode == Err.Record_bind_max) {
                //绑定达到最大次数
                LoginApi.Inst.GetSaveTimeInfo(
                    (Err getSaveTimeInfoErrCode, long uploadTicks, int rebindSeconds, int resetSeconds) => {
                        string describe = "未知错误，请稍后尝试";
                        if (getSaveTimeInfoErrCode == Err.Success) {
                            TimeSpan waitTime = TimeSpan.FromSeconds(resetSeconds);
                            TimeUtil.TimeUnit timeUnit = TimeUtil.GetTimeMaxUnitFromDay2Minute(waitTime, out int units);
                            if (timeUnit == TimeUtil.TimeUnit.Day) {
                                describe = string.Format(
                                    ScriptLocalization.Get("account/record_limit_day", "更换设备绑定已达上限，请在{0}天后重试"), units);
                            } else if (timeUnit == TimeUtil.TimeUnit.Hour) {
                                describe = string.Format(
                                    ScriptLocalization.Get("account/record_limit_hour", "更换设备绑定已达上限，请在{0}小时后重试"),
                                    units);
                            } else if (timeUnit == TimeUtil.TimeUnit.Minute) {
                                describe = string.Format(
                                    ScriptLocalization.Get("account/record_limit_minute", "更换设备绑定已达上限，请在{0}分钟后重试"),
                                    units);
                            } else {
                                describe = string.Format(ScriptLocalization.Get("cloudsave/wait_time_invalid","等待时间异常：{0}") ,waitTime);
                            }
                        } else if (getSaveTimeInfoErrCode == Err.Unknow_error) {
                            describe = ScriptLocalization.Get("cloudsave/server_unknow_error", "服务器异常，请稍后重试");
                        } else if (getSaveTimeInfoErrCode == Err.Record_cannot_find) {
                            describe = ScriptLocalization.Get("cloudsave_undetected","未检测到云端存档");
                        } else {
                            describe = CloudSaveLocalization.GetMessage(getSaveTimeInfoErrCode);
                        }

                        CommonUiUtil.ShowMsg(AccountSdkController.ShowLoadingTransformParent,
                            string.Format(ScriptLocalization.Get("cloudsave/fetch_fail_extra", "获取存档失败：{0}"),
                                describe));
                        CommonUiUtil.HideLoadingWindow(_cloudSaveCanvas);
                    });
                //CloudSaveUtil.Reset();
            } else {
                CommonUiUtil.ShowMsg(AccountSdkController.ShowLoadingTransformParent,
                    string.Format(ScriptLocalization.Get("cloudsave/fetch_fail_extra","获取存档失败：{0}"), 
                        CloudSaveLocalization.GetMessage(errCode)));
                CommonUiUtil.HideLoadingWindow(_cloudSaveCanvas);
                //CloudSaveUtil.Reset();
            }
        }
        #endregion
        
        
        private static void FuncForHotFixBeforeFetch() {
        }
        private static void FuncForHotFixAfterFetch(bool suc, ChillyRoom.SoulKnight.BlobSaveService.V1.ErrorCode err, CloudSaveGameData data, Exception exception) {
        }
        private static void FuncForHotFixAfterFetchRpc(bool suc, Err err, CloudSaveGameData data, Exception exception) {
        }
        private static void FuncForHotFixBeforeDownload(bool suc) {
        }
        private static void FuncForHotFixAfterDownload() {
        }
        private static void FuncForHotFixBeforeUpload() {
        }
        private static void FuncForHotFixAfterUpload(bool suc) {
        }
        
        private static void FuncForHotFixAfterUploadRpc(bool suc, Err err) {
        }

        private static bool _isManualGetUserProfile;
        private static void OnManualGetUserProfileAction() {
            _isManualGetUserProfile = true;
        }
    }
}