using RGScript.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using UIFramework;
using UIFramework.Utils;
using UnityEditor;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace RGScript.Other.InputControl {
    public enum ControllerType {
        /// <summary>A keyboard.</summary>
        Keyboard = 0,

        /// <summary>A mouse.</summary>
        Mouse = 1,

        /// <summary>A joystick.</summary>
        Joystick = 2,

        /// <summary>A Custom Controller.</summary>
        Custom = 20, // 0x00000014
    }

    public class InputControlHandler : IReference {
        /// <summary>
        /// InputControlHandler 模块的设置类 
        /// </summary>
        public class ControlSetting {
            /// <summary>
            /// 目标状态
            /// </summary>
            public InputControllerPreference targetStatus = InputControllerPreference.JoyStickOnly;

            /// <summary>
            /// 如果目标状态不满足，就还原成的状态
            /// </summary>
            public InputControllerPreference fallbackStatus = InputControllerPreference.All;
        }

        public BaseInputControlProvider provider;

        public InputControllerPreference currentControllerPreference;


        public ViewWrapper currentUIPointerViewWrapper;
        public UIOperationMode currentUIOperationMode;
        public InputControllerPreference lastControllerPreference = InputControllerPreference.All;

        /// <summary>
        /// 玩家当前激活的控制器
        /// </summary>
        public List<ControllerType> playerActiveControllerType;


        public ControlSetting controlSetting;

        public InputControlButtonProxy buttonProxy;
        public Canvas uiPointerCanvas;

        private GameObject console;
        private GameObject graphy;

        /// <summary>
        /// 当前的鼠标UI
        /// </summary>
        public UIPointer currentPointer = null;

        /// <summary>
        /// 当前的控制模式，操作人物、绑定UI、还是虚拟鼠标
        /// </summary>
        public InputOperationMode currentOperationMode = InputOperationMode.None;

        /// <summary>
        /// 前一个控制模式，操作人物、绑定UI、还是虚拟鼠标
        /// </summary>
        public InputOperationMode lastOperationMode = InputOperationMode.None;

        /// <summary>
        /// 显不显示手柄键位图
        /// </summary>
        public bool ShowIntroWindow {
            get {
                return PlayerSaveData.Inst.controller_show_intro_window;
            }
        }

        public InputControlHandler(BaseInputControlProvider provider, ControlSetting setting = null) {
            this.provider = provider;
            buttonProxy = new InputControlButtonProxy();
            Init();
            SetTargetControlStatus(setting ?? new ControlSetting());
        }

        #region 生命周期

        private void Init() {
            var initInfo = new InputControlProviderInitInfo {
                OnControllerConnected = OnControllerConnected,
                OnControllerDisconnected = OnControllerDisconnected
            };
            provider.Init(initInfo);
            provider.Start();

            RefreshPlayerMouse();
            RefreshPlayerMouseSpeed();
            RefreshConnectedController();
            AddEvents();
        }

        public void Clear() {
            RemoveEvents();
            provider.Clear();
        }

        public void Update() {
            provider?.Update();

            if (_joystickConnectStatus == JoystickConnectState.WaitingForInput) {
                CheckJoystickPressedUpdate();
            }

            if (currentControllerPreference == InputControllerPreference.JoyStickOnly) {
                DPadButtonUpdate();
            }

            buttonProxy.Update();
        }

        private void CheckJoystickPressedUpdate() {
            if (GetAnyUIButtonDown()) {
                var tempStatus = _joystickConnectStatus;
                SetJoystickConnectStatus(JoystickConnectState.Connected);
                RefreshEnableController();
                if (playerActiveControllerType.Contains(ControllerType.Joystick)) {
                    Debug.Log("[InputControlHandler] joystickConnectStatus = JoystickConnectState.Connected");
                    if (UIManager.Inst.HasOpenedUIPointerViews()) {
                        SwitchOperationModeGuard(InputOperationMode.UIPointer);
                    } else {
                        SwitchOperationModeGuard(InputOperationMode.ControlHero);
                    }

                    SimpleEventManager.Raise(EnableJoystickControllerEvent.UseCache(true));

                    hasShowJoystickDescUI = false;
                    OpenJoyStickDescUIGuard();
                } else {
                    SetJoystickConnectStatus(tempStatus);
                }
            }
        }

        public bool GetAnyUIButtonDown() {
            JoystickButton button = JoystickButton.Confirm | JoystickButton.Cancel | JoystickButton.LeftShoulder1 |
                                    JoystickButton.RightShoulder1;
            return buttonProxy.GetUIButtonFlagsDown(button);
        }

        /// <summary>
        /// D-Pad 按钮监听
        /// </summary>
        private void DPadButtonUpdate() {
            // 按 D-Pad Down 是切换 UI 模式，让虚拟鼠标出现
            if (provider.GetButtonDown(
                    InputControlButtonBindingSetting.joyStickNameToActionDict[JoystickButton.DPadDown])) {
                SwitchOperationMode();
            }

            if (provider.GetButtonDown(
                    InputControlButtonBindingSetting.joyStickNameToActionDict[JoystickButton.DPadLeft])) {
                buttonProxy.SwitchOpenEmotionPanel();
            }

            // Option 键唤出暂停界面
            if (provider.GetButtonDown("Center2")) {
                buttonProxy.SwitchOpenPauseView();
            }

#if UNITY_EDITOR
            if (provider.GetButtonDown(
                    InputControlButtonBindingSetting.joyStickNameToActionDict[JoystickButton.DPadRight])) {
                // if (console == null) {
                //     console = GameObject.Find("IngameDebugConsole");
                // }
                //
                // if (console != null) {
                //     console.SetActive(!console.activeSelf);
                // }
                //
                // if (graphy == null) {
                //     graphy = GameObject.Find("[Graphy]");
                // }
                //
                // if (graphy != null) {
                //     graphy.SetActive(!graphy.activeSelf);
                // }
            }
#endif
        }

        private void AddEvents() {
            SimpleEventManager.AddEventListener<SwitchControllerModeEvent>(OnSwitchControllerMode);
            SimpleEventManager.AddEventListener<OpenUIViewSuccessEvent>(OnOpenUIViewSuccess);
            SimpleEventManager.AddEventListener<CloseUIViewCompleteEvent>(OnCloseUIViewSuccess);
            SimpleEventManager.AddEventListener<OpenMvcViewSuccessEvent>(OnOpenMvcViewSuccess);
            SimpleEventManager.AddEventListener<CloseMvcViewCompleteEvent>(OnCloseMvcViewSuccess);
            SimpleEventManager.AddEventListener<CharacterCreateEvent>(OnCharacterCreate);
            SimpleEventManager.AddEventListener<SelectHeroEvent>(OnSelectHero);
            SimpleEventManager.AddEventListener<AfterUIManagerSceneLoadEvent>(OnAfterUIManagerSceneLoad);
            SimpleEventManager.AddEventListener<ScreenSizeChangeEvent>(OnScreenSizeChange);
            SimpleEventManager.AddEventListener<ForceRefreshModeByPeekViewEvent>(OnICRefreshModeByPeekView);
        }

        private void RemoveEvents() {
            SimpleEventManager.RemoveListener<SwitchControllerModeEvent>(OnSwitchControllerMode);
            SimpleEventManager.RemoveListener<OpenUIViewSuccessEvent>(OnOpenUIViewSuccess);
            SimpleEventManager.RemoveListener<CloseUIViewCompleteEvent>(OnCloseUIViewSuccess);
            SimpleEventManager.RemoveListener<OpenMvcViewSuccessEvent>(OnOpenMvcViewSuccess);
            SimpleEventManager.RemoveListener<CloseMvcViewCompleteEvent>(OnCloseMvcViewSuccess);
            SimpleEventManager.RemoveListener<CharacterCreateEvent>(OnCharacterCreate);
            SimpleEventManager.RemoveListener<SelectHeroEvent>(OnSelectHero);
            SimpleEventManager.RemoveListener<AfterUIManagerSceneLoadEvent>(OnAfterUIManagerSceneLoad);
            SimpleEventManager.RemoveListener<ScreenSizeChangeEvent>(OnScreenSizeChange);
            SimpleEventManager.RemoveListener<ForceRefreshModeByPeekViewEvent>(OnICRefreshModeByPeekView);
        }

        #endregion

        private void RefreshPlayerMouse() {
            // Transform canvas = GameObject.Find("Canvas").transform;
            // Debug.Log($"[InputControlHandler] RefreshPlayerMouse");
            if (uiPointerCanvas == null) {
                uiPointerCanvas = provider.GetUiPointerCanvas();
            }

            if (currentPointer == null && uiPointerCanvas != null) {
                currentPointer = uiPointerCanvas.transform.Find("UIPointer").GetComponent<UIPointer>();
            }

            var showCursor = ShouldShowCursor();

            if (uiPointerCanvas != null) {
                uiPointerCanvas.gameObject.SetActive(showCursor);
            }

            provider.RefreshPlayerMouse(showCursor);

            if (currentPointer != null) {
                var preActive = currentPointer.gameObject.activeSelf;
                if (!preActive && showCursor) {
                    ShowPointer(true);
                    RGGameProcess.StartTimer(0, () => {
                        var screenPosition = GetDefaultPlayerMouseScreenPosition();
                        provider.ShowPlayerMouse(screenPosition);
                    });
                } else if (preActive && !showCursor) {
                    ShowPointer(false);
                }

                // if (!preActive && showCursor) {
                //     playerMouse.screenPosition = Vector2.zero;
                // }
            }
        }

        private static Vector2 GetDefaultPlayerMouseScreenPosition() {
            return new Vector2(Screen.width / 2f, Screen.height / 2f);
            // return new Vector2(0, 0);
        }

        private void ShowPointer(bool isShow) {
            if (currentPointer == null) {
                Debug.LogError("[InputControlHandler] ShowPointer currentPointer is null");
                return;
            }

            int a = isShow ? 1 : 0;
            currentPointer.SetAlpha(a);
            currentPointer.currentMouseShouldShow = isShow;
            currentPointer.gameObject.SetActive(isShow);
            Debug.Log($"[InputControlHandler] ShowPointer isShow: {isShow}");
        }

        /// <summary>
        /// 按 D-Pad Down 是切换 UI 模式，让虚拟鼠标出现
        /// </summary>
        public void SwitchOperationMode() {
            if (currentOperationMode == InputOperationMode.UIPointer) {
                if (UIManager.Inst.HasOpenedUIPointerViews()) {
                    SwitchOperationModeGuard(InputOperationMode.UIPointerHide);
                } else {
                    SwitchOperationModeGuard(InputOperationMode.ControlHero);
                }
            } else if (currentOperationMode == InputOperationMode.ControlHero) {
                SwitchOperationModeGuard(InputOperationMode.UIPointer);
            } else if (currentOperationMode == InputOperationMode.UIPointerHide) {
                if (UIManager.Inst.HasOpenedUIPointerViews()) {
                    SwitchOperationModeGuard(InputOperationMode.UIPointer);
                } else {
                    SwitchOperationModeGuard(InputOperationMode.ControlHero);
                }
            }
        }

        private void SwitchOperationModeGuard(InputOperationMode mode) {
            // TODO 暂时不支持 selectable 
            if (mode == InputOperationMode.UISelectable) {
                mode = InputOperationMode.UIPointer;
            }

            // 不能控制人物了就只能鼠标了
            if (DataUtil.CurIsTargetScene(RGGameConst.SCENE_HERO_ROOM) &&
                RGGameSceneManager.Inst != null && RGGameSceneManager.Inst.controller == null) {
                if (!UIManager.Inst.HasOpenedUIPointerViews()) {
                    mode = InputOperationMode.UIPointer;
                }

                // Debug.Log(
                //     $"[InputControlHandler] SwitchControllerModeGuard mode = {mode} controller = null, currentControlStatus = {currentControllerPreference}");
            }

            lastOperationMode = currentOperationMode;
            currentOperationMode = mode;

            // Debug.Log(
            //     $"[InputControlHandler] SwitchControllerModeGuard mode = {mode} currentControlStatus = {currentControllerPreference}");
            if (CurrentUsingCustomMode()) {
                // Debug.Log("[InputControlHandler] SwitchControllerModeGuard use custom mode");
                RefreshPlayerMouse();
                SwitchOperationMode(mode);
            } else {
                // Debug.Log("[InputControlHandler] SwitchControllerModeGuard switch to control hero");
                RefreshPlayerMouse();
                SwitchOperationMode(InputOperationMode.ControlHero);
            }
        }

        /// <summary>
        /// 根据操作的模式，切换控制器 的 Controller Map
        /// </summary>
        /// <param name="mode"></param>
        private void SwitchOperationMode(InputOperationMode mode) {
            Debug.Log(
                $"[InputControlHandler] SwitchControllerMode curMode: {currentOperationMode} lastMode: {lastOperationMode}");
            switch (mode) {
                case InputOperationMode.ControlHero:
                    SetMapsEnabled(true, false, false);
                    break;
                case InputOperationMode.UISimpleOp:
                    SetMapsEnabled(false, false, true);
                    break;
                case InputOperationMode.UISelectable:
                    SetMapsEnabled(false, true, false);
                    break;
                case InputOperationMode.UIPointer:
                    SetMapsEnabled(false, true, false);
                    ShowPointer(true);
                    break;
                case InputOperationMode.UIPointerHide:
                    SetMapsEnabled(false, true, false);
                    ShowPointer(false);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 控制手柄输入 Controller map 开启和关闭，哪些分类的键位能用
        /// </summary>
        /// <param name="defaultMap">是否打开控制英雄的键位（攻击、技能、换武器等）</param>
        /// <param name="uiPointerMap">是否打开虚拟鼠标键位</param>
        /// <param name="uiSimpleOpMap">是否打开简单操作键位（L12 R12 Confirm 等）</param>
        private void SetMapsEnabled(bool defaultMap, bool uiPointerMap, bool uiSimpleOpMap) {
            if (_joystickConnectStatus == JoystickConnectState.WaitingForInput) {
                Debug.Log($"[InputControlHandler] SetMapsEnabled joystickStatus is waiting for input");
                uiSimpleOpMap = true;
            }

            provider.SetMapsEnabled(defaultMap, uiPointerMap, uiSimpleOpMap);
            Debug.Log(
                $"[InputControlHandler] SetMapsEnabled default = {defaultMap}, uiPointer = {uiPointerMap} simpleOp = {uiSimpleOpMap}");
        }

        // 设置目标
        private void SetTargetControlStatus(ControlSetting setting) {
            Debug.Log($"[InputControlHandler] SetTargetControlStatus targetStatus = {setting.targetStatus}");
            controlSetting = setting;
            RefreshEnableControllerAndOperation();
        }


        private Dictionary<InputControllerPreference, HashSet<ControllerType>> _statusDict =
            new Dictionary<InputControllerPreference, HashSet<ControllerType>> {
                {
                    InputControllerPreference.All,
                    new HashSet<ControllerType> {
                        ControllerType.Custom, ControllerType.Joystick, ControllerType.Keyboard, ControllerType.Mouse
                    }
                },
                { InputControllerPreference.None, new HashSet<ControllerType> { } },
                { InputControllerPreference.JoyStickOnly, new HashSet<ControllerType> { ControllerType.Joystick } },
                { InputControllerPreference.MouseOnly, new HashSet<ControllerType> { ControllerType.Mouse } },
                { InputControllerPreference.KeyboardOnly, new HashSet<ControllerType> { ControllerType.Keyboard } },
            };

        /// <summary>
        /// 如果有手柄 就屏蔽其他控制器，
        /// 如果没有手柄，就不屏蔽
        /// </summary>
        private void RefreshEnableController() {
            bool meetTargetStatus = false;
            var controllers = provider.GetAllControllers();
            foreach (Controller controller in controllers) {
                if (ShouldControllerIgnore(controller)) {
                    continue;
                }

                if (controller.isConnected && _statusDict[controlSetting.targetStatus].Contains(controller.type)) {
                    if (controller.type == ControllerType.Joystick) {
                        if (_joystickConnectStatus != JoystickConnectState.Connected) {
                            continue;
                        }
                    }

                    meetTargetStatus = true;
                    break;
                }
            }

            lastControllerPreference = currentControllerPreference;
            currentControllerPreference =
                meetTargetStatus ? controlSetting.targetStatus : controlSetting.fallbackStatus;

            Debug.Log(
                $"[InputControlHandler] RefreshEnableController currentControlStatus: {currentControllerPreference}");

            foreach (Controller controller in controllers) {
                if (controller.isConnected) {
                    var enabled = _statusDict[currentControllerPreference].Contains(controller.type);
                    provider.SetControllerEnabled(controller, enabled);
                }
            }

            playerActiveControllerType = provider.GetPlayerActiveControllerTypes();

            LogEnableControllers();

            RefreshPlayerMouse();
        }

        /// <summary>
        /// 暂时屏蔽 uinput-fpc 
        /// </summary>
        private bool ShouldControllerIgnore(Controller controller) {
            if (controller.name.ToLower() == "virtual") {
                Debug.Log(
                    $"[InputControlHandler] controller {controller.name} type {controller.type} should ignore: true");
                return true;
            }

            var ignoreControllerNames = provider.GetIgnoreControllerNames();
            foreach (string name in ignoreControllerNames) {
                if (controller.name.ToLower().Contains(name)) {
                    Debug.Log(
                        $"[InputControlHandler] controller {controller.name} type {controller.type} should ignore: true");
                    return true;
                }
            }
#if UNITY_EDITOR
            // 允许在编辑器忽略手柄模式
            if (EditorPrefs.GetBool("ignore_joystick", true)) {
                return true;
            }
#endif
            if (controller.type == ControllerType.Custom) {
                return true;
            }

            // Debug.Log(
            //     $"[InputControlHandler] controller {controller.name} type {controller.type} should ignore: false");
            return false;
        }

        public void LogEnableControllers() {
            provider.LogEnableControllers();
        }

        public string GetEnableJoystickName() {
            var joysticks = provider.GetEnableJoystickNames();
            return joysticks.Count > 0 ? string.Join(",", joysticks.Select(x => x)) : String.Empty;
        }

        private void RefreshEnableControllerAndOperation() {
            RefreshEnableController();
            SwitchOperationModeGuard(currentOperationMode);
        }

        public JoystickConnectState GetJoystickConnectStatus() {
            return _joystickConnectStatus;
        }

        /// <summary>
        /// 控制器偏好判断，是否开启虚拟鼠标功能
        /// </summary>
        /// <returns></returns>
        private bool CurrentUsingCustomMode() {
            return currentControllerPreference == InputControllerPreference.JoyStickOnly &&
                   _joystickConnectStatus == JoystickConnectState.Connected;
        }

        /// <summary>
        /// 是否应该显示虚拟鼠标
        /// </summary>
        /// <returns></returns>
        public bool ShouldShowCursor() {
            bool isTrueStatus = CurrentUsingCustomMode();
            bool isTrueMode = currentOperationMode == InputOperationMode.UIPointer;
            bool isTrueScene = !_notShowPointerScenesWhenNoUISet.Contains(SceneManager.GetActiveScene().name);
            bool showCursor = isTrueStatus && isTrueMode && isTrueScene;
            // if (Debug.isDebugBuild) {
            //     Debug.Log(
            //         $"[InputControlHandler] ShouldShowCursor showCursor: {showCursor} | isTrueStatus: {isTrueStatus} isTrueMode: {isTrueMode} isTrueScene: {isTrueScene} \n currentControlStatus={currentControllerPreference} currentControlMode={currentOperationMode} scene={SceneManager.GetActiveScene().name} UIInStack {UIManager.Inst.GetShowingViewInfo()}");
            // }
            return showCursor;
        }

        /// <summary>
        /// 没UI的时候显示虚拟鼠标的Scene
        /// </summary>
        private HashSet<string> _showPointerScenesWhenNoUISet = new HashSet<string>() {
            RGGameConst.SCENE_TITLE
        };

        /// <summary>
        /// 没UI的时候不显示虚拟鼠标的Scene
        /// </summary>
        private HashSet<string> _notShowPointerScenesWhenNoUISet = new HashSet<string>() {
            RGGameConst.SCENE_SPLASH
        };

        /// <summary>
        /// 根据 UIManager 最顶层 UI 来显示应该切换的模式
        /// </summary>
        private void RefreshModeByPeekView() {
            var peekViewWrapper = UIManager.Inst.PeekView();
            if (peekViewWrapper == null) {
                // 没UI的时候开始判断
                // currentUIOperationMode = UIOperationMode.UIPointer;
                bool isNotShowPointerScene =
                    _notShowPointerScenesWhenNoUISet.Contains(SceneManager.GetActiveScene().name);
                bool isShowPointerScene =
                    _showPointerScenesWhenNoUISet.Contains(SceneManager.GetActiveScene().name);
                if (isNotShowPointerScene) {
                    SwitchOperationModeGuard(InputOperationMode.ControlHero);
                } else if (isShowPointerScene) {
                    SwitchOperationModeGuard(InputOperationMode.UIPointer);
                } else {
                    SwitchOperationModeGuard(InputOperationMode.ControlHero);
                }

                return;
            }

            currentUIPointerViewWrapper = null;
            if (peekViewWrapper.UIOperationMode.HasFlag(UIOperationMode.UIPointer)) {
                currentUIPointerViewWrapper = peekViewWrapper;
            }

            UIOperationMode opMode = peekViewWrapper.UIOperationMode;
            if (opMode.HasFlag(UIOperationMode.ControlHero)) {
                SwitchOperationModeGuard(InputOperationMode.ControlHero);
            } else if (opMode.HasFlag(UIOperationMode.UIPointer)) {
                SwitchOperationModeGuard(InputOperationMode.UIPointer);
            } else if (opMode.HasFlag(UIOperationMode.UISelectable)) {
                SwitchOperationModeGuard(InputOperationMode.UISelectable);
            } else if (opMode.HasFlag(UIOperationMode.UISimpleOp)) {
                SwitchOperationModeGuard(InputOperationMode.UISimpleOp);
            }
        }

        public void CallVibration(InputManager.VibrationType type, bool stop = false) {
#if UNITY_SWITCH && !UNITY_EDITOR
        // switch 里面有宏控制
        InputManager.Inst.CallVibration(type, stop);
#else
#if !UNITY_SWITCH
            if (_joystickConnectStatus != JoystickConnectState.Connected) {
                return;
            }

            try {
                switch (type) {
                    case InputManager.VibrationType.hurt: {
                        // SetVibration(0.5f, 0.1f);
                    }
                        break;
                    case InputManager.VibrationType.dead: {
                        SetVibration(1f, 0.5f);
                    }
                        break;
                    case InputManager.VibrationType.shot: {
                        SetVibration(0.5f, 0.1f);
                    }
                        break;
                    case InputManager.VibrationType.loading: {
                        if (stop) {
                            StopVibration();
                        } else {
                            SetVibration(0.5f, 0);
                        }
                    }
                        break;
                }
            } catch (Exception e) {
                Debug.LogError($"[InputControlHandler] CallVibration {type} {stop}: {e}");
            }
#endif
#endif
        }

        /// <summary>
        /// 设置手柄震动
        /// </summary>
        /// <param name="motorLevel">Motor level [float: 0.0 - 1.0]</param>
        /// <param name="duration">Length of time in seconds to activate the motor before it stops. [0 = Infinite]</param>
        private void SetVibration(float motorLevel, float duration = 0.2f) {
            provider.SetVibration(motorLevel, duration);
        }

        private void StopVibration() {
            provider.StopVibration();
        }

        #region 事件处理

        private List<string> _showUIIgnoreSceneList = new List<string> {
            RGGameConst.SCENE_SPLASH,
            RGGameConst.SCENE_LOADING,
            RGGameConst.SCENE_TITLE,
            RGGameConst.SCENE_TUTORIAL,
            // RGGameConst.SCENE_GAME,
            "Switch"
        };

        public bool hasShowJoystickDescUI = false;

        public enum JoystickConnectState {
            // 没有连接手柄
            NotConnect,

            /// <summary>
            /// 等待手柄输入（连接了手柄但是还没有按按键，还没进入手柄模式）
            /// </summary>
            WaitingForInput,

            /// <summary>
            /// 成功连接手柄
            /// </summary>
            Connected
        }

        /// <summary>
        /// 手柄连接状态（新需求需要连了手柄还得等待按键输入才能进入手柄模式）
        /// </summary>
        private JoystickConnectState _joystickConnectStatus = JoystickConnectState.NotConnect;

        // This function will be called when a controller is connected
        // You can get information about the controller that was connected via the args parameter
        private void OnControllerConnected(string controllerName, int controllerId, ControllerType controllerType) {
            Debug.Log(
                $"[InputControlHandler] OnControllerConnected Name = {controllerName} Id = {controllerId} Type = {controllerType}");
            LogEnableControllers();
            RefreshConnectedController();
            TrackConnectedJoysticks();
        }

        private void TrackConnectedJoysticks() {
            provider.TrackConnectedJoysticks();
        }

        private void RefreshConnectedController() {
            Debug.Log("[InputControlHandler] RefreshConnectedController");
            if (currentControllerPreference != InputControllerPreference.JoyStickOnly &&
                _joystickConnectStatus != JoystickConnectState.WaitingForInput) {
                if (GetConnectedJoystickCount() > 0) {
                    SetJoystickConnectStatus(JoystickConnectState.WaitingForInput);
                }
            }

            if (_joystickConnectStatus == JoystickConnectState.WaitingForInput) {
                // 统计当前激活的控制器，尚未切换到手柄模式
                playerActiveControllerType = provider.GetPlayerActiveControllerTypes();

                // 打开 UISimpleOperation map 来监听手柄按键
                SetMapsEnabled(true, false, true);
            }
        }

        private void SetJoystickConnectStatus(JoystickConnectState status) {
            Debug.Log("[InputControlHandler] joystickConnectStatus from " + _joystickConnectStatus + " to " + status);
            _joystickConnectStatus = status;
        }

        public Vector2 GetMoveVec() {
            return provider.GetMoveVec();
        }

        public Vector2 GetAimVec() {
            return provider.GetAimVec();
        }

        public void SetAllMapsEnabled(bool state) {
            provider.SetAllMapsEnabled(state);
        }

        private void OpenJoyStickDescUIGuard() {
            // 如果游戏开始之前就插了手柄，那么就进入大厅的时候打开手柄键位界面
            if (currentControllerPreference == InputControllerPreference.JoyStickOnly) {
                // 展示信息：进入手柄模式
                if (ShowIntroWindow) {
                    OpenJoyStickDescUI();
                } else {
                    UIWindowShowJoystick.ShowEnterModeMsg();
                }
            }
        }

        private void OpenJoyStickDescUI() {
            // 为了在 temp_message 生成后再 UI 置顶
            var sceneName = SceneManager.GetActiveScene().name;
            if (!_showUIIgnoreSceneList.Contains(sceneName) && UICanvas.GetInstance() != null) {
                hasShowJoystickDescUI = true;
                RGGameProcess.StartTimer(0.1f, () => {
                    var uiView = UIManager.Inst.OpenUIView<UIWindowShowJoystick>("window_show_joystick_desc");
                    uiView.transform.SetAsLastSibling();
                    uiView.onAfterHideView += (_) => {
                        UIWindowShowJoystick.ShowEnterModeMsg();
                    };
                });
            }
        }

        // This function will be called when a controller is fully disconnected
        // You can get information about the controller that was disconnected via the args parameter
        private void OnControllerDisconnected(string controllerName, int controllerId, ControllerType controllerType) {
            Debug.Log(
                $"[InputControlHandler] OnControllerDisconnected Name = {controllerName} Id = {controllerId} Type = {controllerType} currentControllerPreference {currentControllerPreference}");
            int joystickCount = GetConnectedJoystickCount();

            if (joystickCount == 0 && _joystickConnectStatus == JoystickConnectState.Connected) {
                SetJoystickConnectStatus(JoystickConnectState.NotConnect);
                SimpleEventManager.Raise(EnableJoystickControllerEvent.UseCache(false));
            }

            RefreshEnableControllerAndOperation();
        }

        private int GetConnectedJoystickCount() {
            int joystickCount = 0;
            var controllers = provider.GetAllControllers();
            foreach (var controller in controllers) {
                if (ShouldControllerIgnore(controller)) {
                    continue;
                }

                if (!controller.isConnected) {
                    continue;
                }

                if (controller.type == ControllerType.Joystick) {
                    joystickCount++;
                }
            }

            return joystickCount;
        }

        private void OnSwitchControllerMode(SwitchControllerModeEvent e) {
            Debug.Log($"[InputControlHandler] OnSwitchControllerModeEvent {e.mode}");
            SwitchOperationModeGuard(e.mode);
        }

        private void OnOpenUIViewSuccess(OpenUIViewSuccessEvent e) {
            Debug.Log($"[InputControlHandler] OnOpenUIViewSuccess {UIManagerUtils.GetViewTypeName(e.UIView)}");
            RefreshModeByPeekView();
        }

        private void OnCloseUIViewSuccess(CloseUIViewCompleteEvent e) {
            Debug.Log($"[InputControlHandler] OnCloseUIViewSuccess {e.UIViewTypeName}");
            RefreshModeByPeekView();
        }

        private void OnOpenMvcViewSuccess(OpenMvcViewSuccessEvent e) {
            var controller = e.MvcView.GetComponent<RGScript.UI.MVC.Controller>();
            Debug.Log($"[InputControlHandler] OnOpenMvcViewSuccess {UIManagerUtils.GetViewTypeName(controller)}");
            RefreshModeByPeekView();
        }

        private void OnCloseMvcViewSuccess(CloseMvcViewCompleteEvent e) {
            Debug.Log($"[InputControlHandler] OnCloseMvcViewSuccess {e.UIViewTypeName}");
            RefreshModeByPeekView();
        }

        private void OnICRefreshModeByPeekView(ForceRefreshModeByPeekViewEvent e) {
            Debug.Log($"[InputControlHandler] OnICRefreshModeByPeekView");
            RefreshModeByPeekView();
        }

        private void OnCharacterCreate(CharacterCreateEvent e) {
            RefreshHeroRoomSceneStatus();
        }

        private void OnSelectHero(SelectHeroEvent e) {
            RefreshHeroRoomSceneStatus();
        }

        private void OnAfterUIManagerSceneLoad(AfterUIManagerSceneLoadEvent e) {
            UnityEngine.SceneManagement.Scene scene = e.scene;
            LoadSceneMode mode = e.mode;
            Debug.Log($"[InputControlHandler] OnSceneLoaded {scene.name} mode: {mode}");
            // currentPointer = null;
            if (_mustShowCursorScene.Contains(scene.name)) {
                SwitchOperationModeGuard(InputOperationMode.UIPointer);
            } else if (scene.name == RGGameConst.SCENE_LOADING) {
                SwitchOperationModeGuard(GameUtil.CanAddBuff()
                    ? InputOperationMode.UISelectable
                    : InputOperationMode.UIPointer);
            } else if (scene.name == RGGameConst.SCENE_HERO_ROOM) {
                OpenJoyStickDescUIGuard();
                RefreshHeroRoomSceneStatus();
            }
        }

        private void OnScreenSizeChange(ScreenSizeChangeEvent e) {
            RefreshPlayerMouseSpeed();
        }

        private void RefreshPlayerMouseSpeed() {
            provider.RefreshPlayerMouseSpeed();
        }

        /// <summary>
        /// 必定出现 Cursor 的 Scene
        /// </summary>
        private List<string> _mustShowCursorScene = new List<string> {
            RGGameConst.SCENE_TITLE
        };

        private void RefreshHeroRoomSceneStatus() {
            if (!UIManager.Inst.HasOpenedViews()) {
                SwitchOperationModeGuard(RGGameSceneManager.Inst.controller
                    ? InputOperationMode.ControlHero
                    : InputOperationMode.UIPointer);
            }
        }

        #endregion
    }
}