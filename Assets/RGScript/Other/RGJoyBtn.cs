using System;
using UnityEngine;
using UnityEngine.EventSystems;

public class RGJoyBtn : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerDownHandler, IPointerUpHandler {
    public IJoyControl joy_controller;

    void Start() {
        joy_controller = transform.GetComponentInParent<IJoyControl>();
    }

    public void OnPointerDown(PointerEventData eventData) {
        joy_controller.OnAtkBtnClick(true);
    }

    public void OnPointerUp(PointerEventData eventData) {
        joy_controller.OnAtkBtnClick(false);
    }
}