using UnityEngine;

namespace RGScript.WeaponAffix {
    /// <summary>
    /// [链式反应] 子弹击杀敌人时分裂成两个，呈现“Y”状，伤害减半，若剩余子弹仍然击杀了敌人，则继续循环
    /// </summary>
    public class WeaponAffixLevelChainReaction : WeaponAffixLevelBase {
        private const int SplitAngle = 45;
        private int _count;
        private float _damageDiscount;
        protected override void OnInit() {
            _damageDiscount = 1 - Config.Factor1 / 100f;
        }
        
        public override void OnKill(RGEController target, HurtInfo hurtInfo) {
            if (!hurtInfo.SourceBullet) {
                return;
            }

            var bullet = hurtInfo.SourceBullet.GetComponent<DamageCarrier>();
            if (!bullet) {
                return;
            }

            var originAngle = bullet.bulletInfo.directionAngle;
            var originDamage = bullet.damageInfo.damage;
            var damageInfo = bullet.damageInfo.SetDamage(Mathf.Max(1, (int)(originDamage * _damageDiscount)));
            var bulletInfo0 = bullet.bulletInfo.SetAngle(originAngle + SplitAngle).SetPosition(bullet.transform.position);
            var bulletInfo1 = bullet.bulletInfo.SetAngle(originAngle - SplitAngle).SetPosition(bullet.transform.position);

            BulletFactory.TakeBullet(bulletInfo0, damageInfo);
            BulletFactory.TakeBullet(bulletInfo1, damageInfo);
        }
    }
}