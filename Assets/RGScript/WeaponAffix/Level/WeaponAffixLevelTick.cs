using UnityEngine;

namespace RGScript.WeaponAffix {
    /// <summary>
    /// [滴答] 武器攻击，间隔{0}秒，必定触发一次暴击，如果该武器暴击杀死了敌人，则会在敌人位置产生爆炸，伤害{1}
    /// </summary>
    public class WeaponAffixLevelTick : WeaponAffixLevelBase {
        private const string BulletPath = "RGPrefab/Bullet/Explosion/explode_hit_enemy.prefab";
        private float _cdFactor;
        private int _damage;
        private bool _duringCd;
        private GameObject _bulletPrefab;

        protected override void OnInit() {
            _cdFactor = Config.Factor0;
            _damage = (int)Config.Factor1;
            _bulletPrefab = ResourcesUtil.Load<GameObject>(BulletPath);
        }

        public override void OnBeforeHit(PlayerBulletPreHitEnemyEvent e) {
            if (_duringCd) {
                return;
            }

            e.ModifyCritical(true, 100);
        }

        public override void OnHit(RGEController target, HurtInfo hurtInfo) {
            if (_duringCd) {
                return;
            }

            if (!hurtInfo.Critic) {
                return;
            }

            _duringCd = true;
            Timer.Register(_cdFactor, false, false, () => _duringCd = false);
        }

        public override void OnKill(RGEController target, HurtInfo hurtInfo) {
            if (!hurtInfo.Critic) {
                return;
            }

            CreateExplode(target);
        }

        private void CreateExplode(RGEController target) {
            var ctrl = Weapon.controller;
            var bulletInfo = new BulletInfo()
                .SetCamp(ctrl.camp)
                .SetPosition(target.transform.position)
                .SetBulletSize(1)
                .SetBullet(_bulletPrefab)
                .SetSourceObject(ctrl.gameObject);
            var damageInfo = new DamageInfo().SetCamp(ctrl.camp).SetDamage(_damage);
            BulletFactory.TakeBullet(bulletInfo, damageInfo);
        }
    }
}