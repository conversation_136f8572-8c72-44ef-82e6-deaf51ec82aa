using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class RotateByPoint : MonoBehaviour {
    public Transform point;
    public float speed;
    public float angle;
    private float _radian;

    private void Start() {
        _radian = angle * Mathf.Deg2Rad;
    }

    void Update() {
        var position = point.localPosition;
        var radius = (transform.localPosition - position).magnitude;
        var x = radius * Mathf.Cos(_radian);
        var y = radius * Mathf.Sin(_radian);
        var nextPosition = new Vector3(x, y, 0);
        transform.localPosition = nextPosition;
        _radian += Time.deltaTime * speed;
    }
}