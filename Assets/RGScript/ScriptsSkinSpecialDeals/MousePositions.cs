using System.Collections.Generic;
using UnityEngine;
/// <summary>
/// 嘴炮
/// </summary>
public class MousePositionsOnHand
{
    public List<SkinMouse> MousePosition=new List<SkinMouse>();
    public MousePositionsOnHand() {
        MousePosition.Add(new SkinMouse(emHero.Ranger,15,new Vector3(0.94f,0.28f,0)));
        MousePosition.Add(new SkinMouse(emHero.Priest,13,new Vector3(-0.32f,-0.09f,0)));
        MousePosition.Add(new SkinMouse(emHero.Mage, 17, new Vector3(0.11f, 0.18f, 0)));
        MousePosition.Add(new SkinMouse(emHero.Ranger, 18, new Vector3(0.07f, 1.2f, 0)));
        MousePosition.Add(new SkinMouse(emHero.Knight, 20, new Vector3(0, 0.3f, 0)));
        MousePosition.Add(new SkinMouse(emHero.Druid, 16, new Vector3(0.28f, 0.23f, 0)));
        MousePosition.Add(new SkinMouse(emHero.Paladin, 18, new Vector3(-0.48f, 0.1f, 0)));
        MousePosition.Add(new SkinMouse(emHero.Viking, 19, new Vector3(0, 0.4f, 0)));
        MousePosition.Add(new SkinMouse(emHero.Robot, 17, new Vector3(0.44f, 0.46f, 0)));
        MousePosition.Add(new SkinMouse(emHero.Lancer,4,new Vector3(0.51f,-0.1f,0)));
        MousePosition.Add(new SkinMouse(emHero.Knight, 26, new Vector3(0.38f, 0.42f, 0)));
        MousePosition.Add(new SkinMouse(emHero.Paladin, 24, new Vector3(-0.484f, 0.036f, 0)));
        MousePosition.Add(new SkinMouse(emHero.SwordMaster, 7, new Vector3(-0.068f, 0.15f, 0)));
    }
    //圣骑士嘴炮位置
    public Vector3 PaladinPosition = new Vector3(-0.484f, 0.036f, 0);
}
public class MousePositionsOnBody
{
    public List<SkinMouse> MousePosition=new List<SkinMouse>();
    public MousePositionsOnBody() {
        MousePosition.Add(new SkinMouse(emHero.Ranger,15,new Vector3(1.2f,1.2f,0)));
        MousePosition.Add(new SkinMouse(emHero.Priest,13,new Vector3(-0.32f,-0.09f,0)));
        MousePosition.Add(new SkinMouse(emHero.Mage, 17, new Vector3(0.35f, 0.9f, 0)));
        MousePosition.Add(new SkinMouse(emHero.Ranger, 18, new Vector3(0.35f, 2.1f, 0)));
        MousePosition.Add(new SkinMouse(emHero.Knight, 20, new Vector3(0.2f, 1.15f, 0)));
        MousePosition.Add(new SkinMouse(emHero.Druid, 16, new Vector3(0.5f, 1, 0)));
        MousePosition.Add(new SkinMouse(emHero.Paladin, 18, new Vector3(0.2f, 1, 0)));
        MousePosition.Add(new SkinMouse(emHero.Viking, 19, new Vector3(0.2f, 1, 0)));
        MousePosition.Add(new SkinMouse(emHero.Robot, 17, new Vector3(0.22f, 1.41f, 0)));
        MousePosition.Add(new SkinMouse(emHero.Lancer,4,new Vector3(0.779f,0.7f,0)));
        MousePosition.Add(new SkinMouse(emHero.Knight, 26, new Vector3(0.38f, 0.42f, 0)));
        MousePosition.Add(new SkinMouse(emHero.Paladin, 24, new Vector3(0.23f, 1, 0)));
        MousePosition.Add(new SkinMouse(emHero.SwordMaster, 7, new Vector3(0.212f, 0.75f, 0)));
        MousePosition.Add(new SkinMouse(emHero.SwordMaster, 7, new Vector3(0.212f, 0.75f, 0)));
    }
}
/// <summary>
/// 痛苦面具
/// </summary>
public class IceMaskPositionsOnHand
{
    public List<SkinMouse> MousePosition=new List<SkinMouse>();
    public IceMaskPositionsOnHand() {
        MousePosition.Add(new SkinMouse(emHero.Ranger,15,new Vector3(0.94f,0.28f,0)));
        MousePosition.Add(new SkinMouse(emHero.Ranger, 18, new Vector3(-0.19f, 0.98f, 0)));
        MousePosition.Add(new SkinMouse(emHero.Robot, 17, new Vector3(0.44f, 0.4f, 0)));
        MousePosition.Add(new SkinMouse(emHero.Lancer, 4, new Vector3(0.45f, 0, 0)));
    }
    //圣骑士面具位置
    public Vector3 PaladinPosition = new Vector3(-0.393f, 0, 0);
}
public class IceMaskPositionsOnBody
{
    public List<SkinMouse> MousePosition=new List<SkinMouse>();
    public IceMaskPositionsOnBody() {
        MousePosition.Add(new SkinMouse(emHero.Ranger,15,new Vector3(0.94f,1.68f,0)));
        MousePosition.Add(new SkinMouse(emHero.Warlock,5,new Vector3(0.046f,1f,0)));
        MousePosition.Add(new SkinMouse(emHero.Ranger, 18, new Vector3(-0.144f, 2.18f, 0)));
        MousePosition.Add(new SkinMouse(emHero.Robot, 17, new Vector3(0.01f, 1.77f, 0)));
        MousePosition.Add(new SkinMouse(emHero.Lancer,4,new Vector3(0.49f,1.22f,0)));
    }
    
}
/// <summary>
/// 黑暗残影
/// </summary>
public class DarkMaskPositionsOnHand
{
    public List<SkinMouse> MousePosition=new List<SkinMouse>();
    public DarkMaskPositionsOnHand() {
        MousePosition.Add(new SkinMouse(emHero.Ranger,15,new Vector3(0.94f,0.28f,0)));
        MousePosition.Add(new SkinMouse(emHero.Ranger, 18, new Vector3(0, 1.6f, 0)));
        MousePosition.Add(new SkinMouse(emHero.Robot, 17, new Vector3(0.46f, 0.7f, 0)));
        MousePosition.Add(new SkinMouse(emHero.Lancer,4,new Vector3(0.5f,0.5f,0)));
        MousePosition.Add(new SkinMouse(emHero.Knight, 26, new Vector3(0.31f, 0.63f, 0)));
    }
    //圣骑士面具位置
    public Vector3 PaladinPosition = new Vector3(-0.393f, 0, 0);
}
public class DarkMaskPositionsOnBody
{
    public List<SkinMouse> MousePosition=new List<SkinMouse>();
    public DarkMaskPositionsOnBody() {
        MousePosition.Add(new SkinMouse(emHero.Ranger,15,new Vector3(1.54f,-0.42f,0)));
        MousePosition.Add(new SkinMouse(emHero.Ranger, 18, new Vector3(0.6f, 1.4f, 0)));
        MousePosition.Add(new SkinMouse(emHero.Robot, 17, new Vector3(0.56f, 0.55f, 0)));
        MousePosition.Add(new SkinMouse(emHero.Lancer,4,new Vector3(1.12f,0.2f,0)));
        MousePosition.Add(new SkinMouse(emHero.Knight, 26, new Vector3(0.83f, 0.2f, 0)));
    }
}

public class SkinMouse {
    public emHero HeroID;
    public int SkinIndex;
    public Vector3 MousePosition;
    public SkinMouse(emHero _heroID,int _skinIndex,Vector3 _mousePosition) {
        HeroID = _heroID;
        SkinIndex= _skinIndex;
        MousePosition = _mousePosition;
    }

}