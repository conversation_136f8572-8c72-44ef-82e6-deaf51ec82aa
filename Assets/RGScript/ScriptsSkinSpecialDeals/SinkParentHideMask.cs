using UnityEngine;
using UnityEngine.Rendering;

namespace RGScript.ScriptsSkinSpecialDeals
{
    public class SinkParentHideMask : MonoBehaviour {
        private SpriteMask _spriteMask;

        private void OnEnable() {
            _spriteMask = transform.GetComponentInChildren<SpriteMask>();
        }

        public bool IsHide() {
            return !_spriteMask.gameObject.activeSelf;
        }
        
        public void HideMask(RGController controller) {
            if (!_spriteMask || !controller) {
                return;
            }

            _spriteMask.gameObject.SetActive(false);
            var body = controller.transform.Find("img/body");
            if (!body || !body.GetComponent<SpriteRenderer>()) {
                return;
            }

            body.GetComponent<SpriteRenderer>().maskInteraction = SpriteMaskInteraction.None;
        }

        public void ShowMask(RGController controller) {
            if (!_spriteMask || !controller) {
                return;
            }

            _spriteMask.gameObject.SetActive(true);
            var body = controller.transform.Find("img/body");
            if (!body || !body.GetComponent<SpriteRenderer>()) {
                return;
            }

            body.GetComponent<SpriteRenderer>().maskInteraction = SpriteMaskInteraction.VisibleOutsideMask;
        }

        public void IgnoreSortingGroup() {
            GetComponent<SortingGroup>().sortingOrder = 5;
        }
        
        public void ResetSortingGroup() {
            GetComponent<SortingGroup>().sortingOrder = 2;
        }
    }
}
