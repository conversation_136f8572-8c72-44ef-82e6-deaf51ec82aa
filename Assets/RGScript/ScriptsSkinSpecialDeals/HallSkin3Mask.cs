using UnityEngine;

namespace RGScript.ScriptsSkinSpecialDeals
{
    public class HallSkin3Mask : MonoBehaviour {
        public GameObject hallMask;
        private GameObject _mask;
        private void OnEnable() {
            SimpleEventManager.AddEventListener<AfterInitCharacterWeaponsEvent>(SetCharacterMask);
            SimpleEventManager.AddEventListener<ChangeHeroRoomSkinEvent>(SetCharacterMaskOnChangeRoomSkin);
        }

        private void OnDisable() {
            SimpleEventManager.RemoveListener<AfterInitCharacterWeaponsEvent>(SetCharacterMask);
            SimpleEventManager.RemoveListener<ChangeHeroRoomSkinEvent>(SetCharacterMaskOnChangeRoomSkin);
        }

        private void SetCharacterMaskOnChangeRoomSkin(ChangeHeroRoomSkinEvent e) {
            if (_mask) {
                return;
            }
            
            _mask = Instantiate(hallMask, RGGameSceneManager.Inst.controller.transform, true);
            _mask.transform.localPosition=Vector3.zero;
        }

        private void SetCharacterMask(AfterInitCharacterWeaponsEvent e) {
            if (_mask) {
                return;
            }
            
            if (e.character.IsDemonstrationCharacter) {
                return;
            }
            
            _mask = Instantiate(hallMask, e.character.transform, true);
            _mask.transform.localPosition=Vector3.zero;
        }
    }
}
