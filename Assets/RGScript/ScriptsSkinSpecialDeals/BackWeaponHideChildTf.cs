using UnityEngine;

public class BackWeaponHideChildTf : MonoBehaviour {
    private RGWeapon _weapon;
    public Transform targetTf;
    private void OnEnable() {
        _weapon = GetComponentInParent<RGWeapon>();
    }

    private void Update()
    {
        if (!_weapon || !targetTf) {
            return;
        }

        if (_weapon.IsFrontWeapon) {
            if (!targetTf.gameObject.activeSelf) {
                targetTf.gameObject.SetActive(true);
            }
        } else{
            if (targetTf.gameObject.activeSelf) {
                targetTf.gameObject.SetActive(false);
            }
        }
    }
}
