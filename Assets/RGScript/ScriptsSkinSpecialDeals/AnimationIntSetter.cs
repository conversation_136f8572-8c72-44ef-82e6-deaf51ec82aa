using UnityEngine;

namespace RGScript.ScriptsSkinSpecialDeals {
    public class AnimationIntSetter : StateMachineBehaviour {
        public string intName;

        public bool updateCheck;

        public int intToSet;

        public override void OnStateUpdate(Animator animator, AnimatorStateInfo stateInfo, int layerIndex) {
            if (!updateCheck) {
                return;
            }
            CheckIfHaveMount(animator);
        }

        public void CheckIfHaveMount(Animator animator) {
            RGController controller = animator.GetComponent<RGController>();
            if (controller && controller.mount != null) {
                animator.SetInteger(intName, intToSet);
            }
        }
    }
}
