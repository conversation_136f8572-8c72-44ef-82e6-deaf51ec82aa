using UnityEngine;

namespace RGScript.ScriptsSkinSpecialDeals
{
    public class VikingJumpEffect : StateMachineBehaviour {
        public GameObject effect;
        override public void OnStateEnter(Animator animator, AnimatorStateInfo stateInfo, int layerIndex) {
            var effectProto = PrefabPool.Inst.Take(effect);
            effectProto.transform.position = animator.transform.position;
        }

   
    }
}
