using RGScript.Weapon;
using UnityEngine;

public class SpecialStateMaskWeaponsPosition : StateMachineBehaviour {
    private void AdjustMousePosition(Animator animator) {
        if (!animator.GetComponent<RGController>()) {
            return;
        }

        var heroType = animator.GetComponent<RGController>().GetHeroType();
        var skinIndex = animator.GetComponent<RGController>().GetSkinIndex();
        var mousePositionsOnHand = new MousePositionsOnHand();
        var mousePositionsOnBody = new MousePositionsOnBody();
        var posOnHand = mousePositionsOnHand.MousePosition;
        var posOnBody = mousePositionsOnBody.MousePosition;
        if (!animator.transform.GetComponentInChildren<GunMouse>()) {
            return;
        }

        var mouse = animator.transform.GetComponentInChildren<GunMouse>();
        if (mouse.transform.parent.name == "h1") {
            foreach (var pos in posOnHand) {
                if (pos.HeroID == heroType && pos.SkinIndex == skinIndex) {
                    mouse.transform.localPosition = pos.MousePosition;
                    return;
                }
            }

            if (heroType == emHero.Paladin) {
                mouse.transform.localPosition = mousePositionsOnHand.PaladinPosition;
            }
        } else if (mouse.transform.parent.name == "back") {
            foreach (var pos in posOnBody) {
                if (pos.HeroID == heroType && pos.SkinIndex == skinIndex) {
                    var mouseOnBody = animator.transform.Find("img/mouse(Clone)");
                    mouseOnBody.transform.localPosition = pos.MousePosition;
                    return;
                }
            }
        }
    }

    private void ResetMousePosition(Animator animator) {
        if (!animator.transform.GetComponentInChildren<GunMouse>()) {
            return;
        }

        var mouse = animator.transform.GetComponentInChildren<GunMouse>();
        if (mouse.transform.parent.name == "h1") {
            mouse.transform.localPosition = Vector3.zero;
        }else if(mouse.transform.parent.name=="back") {
            var mouseOnBody = animator.transform.Find("img/mouse(Clone)");
            var followTransform = animator.transform.Find("img");
            var weaponPosition = followTransform.InverseTransformPoint(animator.transform.Find("img/h1").TransformPoint(GunMouse.H1Offset));
            mouseOnBody.transform.localPosition = weaponPosition;
        }
    }


    private void AdjustIceMaskPosition(Animator animator) {
        if (!animator.GetComponent<RGController>()) {
            return;
        }

        var heroType = animator.GetComponent<RGController>().GetHeroType();
        var skinIndex = animator.GetComponent<RGController>().GetSkinIndex();
        var gunIceWormPositionsOnHand = new IceMaskPositionsOnHand();
        var gunIceWormPositionsOnBody = new IceMaskPositionsOnBody();
        var maskPosOnHand = gunIceWormPositionsOnHand.MousePosition;
        var maskPosOnBody = gunIceWormPositionsOnBody.MousePosition;
        if (!animator.transform.GetComponentInChildren<GunIceWorm>()) {
            return;
        }

        var mouse = animator.transform.GetComponentInChildren<GunIceWorm>();
        if (mouse.transform.parent.name == "h1") {
            foreach (var pos in maskPosOnHand) {
                if (pos.HeroID == heroType && pos.SkinIndex == skinIndex) {
                    mouse.transform.localPosition = pos.MousePosition;
                    return;
                }
                if (heroType == emHero.Paladin) {
                    mouse.transform.localPosition = gunIceWormPositionsOnHand.PaladinPosition;
                }
            }
        } else if (mouse.transform.parent.name == "back") {
            foreach (var pos in maskPosOnBody) {
                if (pos.HeroID == heroType && pos.SkinIndex == skinIndex) {
                    var mouseOnBody = animator.transform.Find("img/wormHead(Clone)");
                    mouseOnBody.transform.localPosition = pos.MousePosition;
                    return;
                }
            }
        }
    }


    private void ResetIceMaskPosition(Animator animator) {
        if (!animator.transform.GetComponentInChildren<GunIceWorm>()) {
            return;
        }

        var iceHead = animator.transform.GetComponentInChildren<GunIceWorm>();
        if (iceHead.transform.parent.name == "h1") {
            iceHead.transform.localPosition = Vector3.zero;
        }else if(iceHead.transform.parent.name=="back") {
            var inceHeadOnBody = animator.transform.Find("img/wormHead(Clone)");
            var followTransform = animator.transform.Find("img");
            var weaponPosition = followTransform.InverseTransformPoint(animator.transform.Find("img/h1").TransformPoint(GunMouse.H1Offset));
            inceHeadOnBody.transform.localPosition = weaponPosition;
        }
    }

    private void AdjustMaskPosition(Animator animator) {
        if (!animator.GetComponent<RGController>()) {
            return;
        }

        var heroType = animator.GetComponent<RGController>().GetHeroType();
        var skinIndex = animator.GetComponent<RGController>().GetSkinIndex();
        var mask = animator.transform.GetComponentInChildren<WeaponBoss26>();
        var darkMaskPositionsOnHand = new DarkMaskPositionsOnHand();
        var darkMaskPositionsOnBody= new DarkMaskPositionsOnBody();
        var posOnHand = darkMaskPositionsOnHand.MousePosition;
        var posOnBody = darkMaskPositionsOnBody.MousePosition;
        if (!animator.transform.GetComponentInChildren<WeaponBoss26>()) {
            return;
        }
        
        if (mask.transform.parent.name == "h1") {
            foreach (var pos in posOnHand) {
                if (pos.HeroID == heroType && pos.SkinIndex == skinIndex) {
                    mask.transform.localPosition = pos.MousePosition;
                    return;
                }
                if (heroType == emHero.Paladin) {
                    mask.transform.localPosition = darkMaskPositionsOnHand.PaladinPosition;
                }
            }
        } else if (mask.transform.parent.name == "back") {
            foreach (var pos in posOnBody) {
                if (pos.HeroID == heroType && pos.SkinIndex == skinIndex) {
                    mask.transform.localPosition = pos.MousePosition;
                    return;
                }
            }
        }
    }
    
    private void ResetMaskPosition(Animator animator) {
        if (!animator.transform.GetComponentInChildren<WeaponBoss26>()) {
            return;
        }

        var darkMask = animator.transform.GetComponentInChildren<WeaponBoss26>();
        darkMask.transform.localPosition = new Vector3(0,0.5f,0);
    }
    override public void OnStateEnter(Animator animator, AnimatorStateInfo stateInfo, int layerIndex) {
        AdjustMousePosition(animator);
        AdjustIceMaskPosition(animator);
        AdjustMaskPosition(animator);
    }

    override public void OnStateUpdate(Animator animator, AnimatorStateInfo stateInfo, int layerIndex) {
        AdjustMousePosition(animator);  
        AdjustIceMaskPosition(animator);
        AdjustMaskPosition(animator);
    }

    public override void OnStateExit(Animator animator, AnimatorStateInfo stateInfo, int layerIndex) {
        ResetMousePosition(animator);
        ResetIceMaskPosition(animator);
        ResetMaskPosition(animator);
    }
}
