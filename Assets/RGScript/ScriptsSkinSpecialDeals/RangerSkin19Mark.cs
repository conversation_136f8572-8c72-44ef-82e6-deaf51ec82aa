using DG.Tweening;
using UnityEngine;

namespace RGScript.ScriptsSkinSpecialDeals
{
    public class RangerSkin19Mark : MonoBehaviour {
        public float redColorConstTime;
        public Transform[] marks;
        private Timer _redToBlueTimer;
    
        public void RefreshMarkColorToRed(int killNumber,int maxNumber) {
            _redToBlueTimer?.Cancel();
            ChangeRedColor(killNumber * 8 / maxNumber);
        }

        public void RefreshMarkColorToBlue() {
            _redToBlueTimer = Timer.Register(redColorConstTime / 8, true, false, ChangeBlueColor);
        }
    
        private void ChangeRedColor(int markNumber) {
            for (int i = 0; i < markNumber; i++) {
                var red = marks[i].Find("red").gameObject;
                var blue= marks[i].Find("blue").gameObject;
                if (red.activeSelf || !blue.activeSelf) {
                    continue;
                }

                blue.GetComponent<SpriteRenderer>().DOColor(new Color(0, 0, 0, 0), 0.5f).OnComplete(() => {
                    blue.SetActive(false);
                });
                red.SetActive(true);
                var redSprite = red.GetComponent<SpriteRenderer>();
                redSprite.color = new Color(1, 1, 1, 0);
                redSprite.DOColor(new Color(1, 1, 1, 1), 0.5f);
            }
        }

        private void ChangeBlueColor() {
            foreach (var mark in marks) {
                var red = mark.Find("red").gameObject;
                var blue= mark.Find("blue").gameObject;
                if (!red.activeSelf || blue.activeSelf) {
                    continue;
                }

                red.GetComponent<SpriteRenderer>().DOColor(new Color(0, 0, 0, 0), 0.5f).OnComplete(() => {
                    red.SetActive(false);
                });
                blue.SetActive(true);
                var blueSprite = blue.GetComponent<SpriteRenderer>();
                blueSprite.color = new Color(1, 1, 1, 0);
                blueSprite.DOColor(new Color(1, 1, 1, 1), 0.5f);
                break;
            }
        }

        private void OnDestroy() {
            _redToBlueTimer?.Cancel();
        }
    }
}
