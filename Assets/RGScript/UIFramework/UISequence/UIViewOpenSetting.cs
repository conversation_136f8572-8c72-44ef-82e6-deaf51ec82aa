using System;
using UnityCommon.UI;

namespace UIFramework {
    /// <summary>
    /// UI 打开设置
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class UIViewOpenSetting<T> where T : BaseUIView {
        public string uiViewAssetPath;
        public object[] userData = null;
        public bool callShowView = true;
        public Action<T> OnBeforeShow = null;
        public Action<T> OnAfterShow = null;
        public Action<T> OnBeforeHide = null;
        public Action<T> OnAfterHide = null;
    }
}