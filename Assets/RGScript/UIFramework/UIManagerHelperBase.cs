using RGScript.UI.MVC;
using UnityCommon.UI;
using UnityEngine;

namespace UIFramework {
    /// <summary>
    /// 暂时不用
    /// 这里暂时用 IView，以后换 IViewPro
    /// 界面辅助器基类。
    /// </summary>
    public abstract class UIManagerHelperBase : MonoB<PERSON><PERSON>our, IUIManagerHelper {
        // /// <summary>
        // /// 实例化界面。
        // /// </summary>
        // /// <param name="viewAsset">要实例化的界面资源。</param>
        // /// <returns>实例化后的界面。</returns>
        // public abstract object InstantiateUIView(object viewAsset);

        /// <summary>
        /// 创建界面。
        /// </summary>
        /// <param name="viewAssetFullPath"></param>
        /// <param name="parent">父节点</param>
        /// <param name="userData">用户自定义数据。</param>
        /// <returns>界面。</returns>
        public abstract T CreateUIView<T>(string viewAssetFullPath, Transform parent, object userData)
            where T : MonoBehaviour, IView; 
        
        /// <summary>
        /// 创建界面，反射用。
        /// </summary>
        /// <param name="viewAssetFullPath"></param>
        /// <param name="parent">父节点</param>
        /// <param name="userData">用户自定义数据。</param>
        /// <returns>界面。</returns>
        public abstract BaseUIView CreateUIView(string viewAssetFullPath, Transform parent, object userData);

        /// <summary>
        /// 创建界面。
        /// </summary>
        /// <param name="viewPrefab"></param>
        /// <param name="parent">父节点</param>
        /// <param name="userData">用户自定义数据。</param>
        /// <returns>界面。</returns>
        public abstract T CreateUIView<T>(GameObject viewPrefab, Transform parent, object userData)
            where T : MonoBehaviour, IView;

        /// <summary>
        /// 创建界面，反射用。
        /// </summary>
        /// <param name="viewPrefab"></param>
        /// <param name="parent">父节点</param>
        /// <param name="userData">用户自定义数据。</param>
        /// <returns>界面。</returns>
        public abstract BaseUIView CreateUIView(GameObject viewPrefab, Transform parent, object userData);
        
        /// <summary>
        /// 创建界面。
        /// </summary>
        /// <param name="viewAssetFullPath"></param>
        /// <param name="parent">父节点</param>
        /// <param name="userData">用户自定义数据。</param>
        /// <returns>界面。</returns>
        public abstract View<T> CreateMvcView<T>(string viewAssetFullPath, Transform parent, object userData);

        /// <summary>
        /// 释放界面。
        /// </summary>
        /// <param name="viewInstance">要释放的界面实例。</param>
        /// <param name="viewAsset">要释放的界面资源。</param>
        public abstract void ReleaseUIView(BaseUIView viewInstance, object viewAsset = null);

        /// <summary>
        /// 释放界面。
        /// </summary>
        /// <param name="viewInstance">要释放的界面实例。</param>
        /// <param name="viewAsset">要释放的界面资源。</param>
        public abstract void ReleaseMvcView(GameObject viewInstance, object viewAsset = null);
    }
}