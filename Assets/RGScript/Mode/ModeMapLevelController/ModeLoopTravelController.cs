using BattleFactor.Enemy;
using ModeDefence;
using ModeLoopTravel.Scripts;
using RGScript.Config;
using RGScript.Data;
using RGScript.Util.EventSystem.SimpleEventSystem.UI;
using System.Collections.Generic;
using System.Linq;
using RGScript.Map;
using UnityEngine;

namespace RGScript.Mode.ModeMapLevelController {
    public class ModeLoopTravelController : BaseModeController {
        private readonly LotteryMachine<DefenceEnemyGene.EnemyGene> _geneLotteryMachine;
        private readonly int _maxGeneEnemyCount;
        private int _geneEnemyCount;
        private readonly int _geneWeight;

        private float _curLevelTime;
        private float _levelTimeMax = 1f;
        private float _curPlayerTime;
        private float _lastTime;
        private GameObject uiCountDown;

        public const string TIME_ENOUGH_TIMES = "time_enough_times";
        private const float TIMER_UPDATE_INTERVAL = 1f;

        public ModeLoopTravelController(int seed) : base(seed) {
            _geneLotteryMachine = new LotteryMachine<DefenceEnemyGene.EnemyGene>(this.rgRandom);
            for (DefenceEnemyGene.EnemyGene i = DefenceEnemyGene.EnemyGene.strength;
                 i < DefenceEnemyGene.EnemyGene.count;
                 i++) {
                if (i == DefenceEnemyGene.EnemyGene.invisible || i == DefenceEnemyGene.EnemyGene.weaken) { // 排除隐身和虚弱
                    continue;
                }
                _geneLotteryMachine.AddItem(i, 10);
            }

            int loopCount = GameUtil.ModeLoopTravelLoopCount();
            _maxGeneEnemyCount = FormulaUtil.GetMaxCountGeneEnemy(loopCount);
            _geneWeight = (int)FormulaUtil.GetMaxCountGeneEnemyWeight(loopCount,
                BattleData.data.GetFactorTimes(emBattleFactor.MoreGeneEnemy));

            OnInit();
        }

        private Timer _timer;

        protected sealed override void OnInit() {
            base.OnInit();
            AddEvent();
            BattleData.data.buffCount = ModeLoopTravelConfig.Config.MAX_BUFF_COUNT;
            if (!GameUtil.InGameScene) {
                return;
            }

            uiCountDown = Object.Instantiate(
                ResourcesUtil.Load<GameObject>("ModeLoopTravel/Prefabs/UI/ui_demon_countdown.prefab"),
                UICanvas.GetInstance().transform);

            ReadTime();
            bool isMultiGame = GameUtil.IsMultiGame();
            _timer = Timer.Register(TIMER_UPDATE_INTERVAL, true, isMultiGame, UpdateTimeBarProgress);

            #region 成就检测

            AchieveInfos.info.CheckUnlock(AchieveInfos.AchievementType.LoopTravelPressureLv,
                GameUtil.ModeLoopTravelPressureLv(), "", emHero.None);

            var timeEnoughTimes = BattleData.data.GetMark(TIME_ENOUGH_TIMES);
            AchieveInfos.info.CheckUnlock(AchieveInfos.AchievementType.LoopTravelRunWithTime,
                timeEnoughTimes, "", emHero.None);

            #endregion

            AddPostProcessing();
            //防止先得到Addtion天赋再切换到无尽模式
            if (null == BattleData.data.buffsAddtion || BattleData.data.buffsAddtion.Count <= 0) {
                return;
            }

            var needRemoveAdditionBuffs = new List<int>(BattleData.data.buffsAddtion);

            foreach (var addBuffId in needRemoveAdditionBuffs) {
                BattleData.data.ForgetAdditionBuff((emBuff)addBuffId);
            }

            foreach (var addBuffId in needRemoveAdditionBuffs) {
                BattleData.data.LearnBuff((emBuff)addBuffId, true);
            }
        }

        void AddPostProcessing() {
            if (null == Camera.main) {
                return;
            }

            var comp = Camera.main.gameObject.AddComponent<BorderAurora>();
            var tex = ResourcesUtil.Load<Texture2D>("RGTexture/dungeon_common/FlowMap.png");
            comp.flowTexture = tex;
            var pressureLv = GameUtil.ModeLoopTravelPressureLv();
            var finalLv = Mathf.Min(10, pressureLv);
            var range = 0.95f - finalLv / 10f * 0.17f;
            comp.distortion = 0.3f;
            comp.speed = 0.1f;
            comp.scale = 1f;
            comp.range = range;
            comp.color = new Color(0.056f, 0.415f, 0f, 1f);
        }

        void AddEvent() {
            SimpleEventManager.AddEventListener<ShowPauseWindow>(OnShowPauseWindow);
        }

        void RemoveEvent() {
            SimpleEventManager.RemoveListener<ShowPauseWindow>(OnShowPauseWindow);
        }

        void UpdateTimeBarProgress() {
            if (GameUtil.IsMultiGame()) {
                var deltaTime = Time.realtimeSinceStartup - _lastTime;
                _lastTime = Time.realtimeSinceStartup;
                _curLevelTime += deltaTime;
            } else {
                _curLevelTime += TIMER_UPDATE_INTERVAL;
            }

            _curLevelTime = Mathf.Max(0, Mathf.Min(_levelTimeMax, _curLevelTime));
            var levelProgress = Mathf.Max(0, _curLevelTime - ReduceLevelTime) / _levelTimeMax;

            _curPlayerTime = Mathf.Min(_levelTimeMax, _curPlayerTime);
            var playerProgress = _curPlayerTime / _levelTimeMax;
            SimpleEventManager.Raise(UpdateLevelProgressEvent.UseCache(levelProgress));
            SimpleEventManager.Raise(UpdatePlayerProgressEvent.UseCache(playerProgress));
        }


        public const string CUR_LEVEL_TIME = "CUR_LEVEL_TIME";
        public const string CUR_PLAYER_TIME = "CUR_PLAYER_TIME";
        public const string REDUCE_TIME_DEFEATING_FEL_LORD = "REDUCE_TIME_DEFEATING_FEL_LORD";

        public static float CurLevelTime => BattleData.data.GetFloatMark(CUR_LEVEL_TIME);
        public static float CurPlayerTime => BattleData.data.GetFloatMark(CUR_PLAYER_TIME);
        public static float ReduceLevelTime => BattleData.data.GetFloatMark(REDUCE_TIME_DEFEATING_FEL_LORD);


        /// <summary>
        /// 是否需要显示因子抽奖界面
        /// </summary>
        /// <returns></returns>
        public static bool NeedShowFactor() {
            int lastFactorLvIdx = BattleData.data.GetMark(RGGameConst.LAST_LOOPTRAVEL_FACTOR_LV_IDX);
            int passLv = BattleData.data.levelIndex;
            if (lastFactorLvIdx >= passLv) {
                //防止刷因子
                return false;
            }

            var curLevelTime = ModeLoopTravelController.CurLevelTime;
            var curPlayerTime = ModeLoopTravelController.CurPlayerTime;
            var timeEnough = curLevelTime - ModeLoopTravelController.ReduceLevelTime < curPlayerTime;
            bool hasFactorCanRandom = false;
            if (timeEnough) {
                foreach (var factorData in ModeLoopTravelFactorConfig.Data.Values) {
                    if (factorData.Difficult > 0) {
                        continue;
                    }

                    if (GameUtil.IsSingleGame() && factorData.OnlyMultiGame > 0) {
                        continue;
                    }

                    if (BattleData.data.GetFactorTimes((emBattleFactor)factorData.Key) >= factorData.MaxCount) {
                        continue;
                    }

                    hasFactorCanRandom = true;
                    break;
                }
            } else {
                foreach (var factorData in ModeLoopTravelFactorConfig.Data.Values) {
                    if (factorData.Difficult < 0) {
                        continue;
                    }

                    if (GameUtil.IsSingleGame() && factorData.OnlyMultiGame > 0) {
                        continue;
                    }

                    if (BattleData.data.GetFactorTimes((emBattleFactor)factorData.Key) >= factorData.MaxCount) {
                        continue;
                    }

                    hasFactorCanRandom = true;
                    break;
                }
            }

            return passLv % 5 == 0 && passLv > 0 &&
                   // BattleData.data.factors.Count < ModeLoopTravelConfig.Config.MAX_FACTOR_COUNT &&
                   hasFactorCanRandom;
        }

        /// <summary>
        /// 关卡数是否满足显示天赋选择条件
        /// </summary>
        /// <returns></returns>
        public static bool LevelCanShowBuff(int levelIndex) {
            int passLv = levelIndex;
            int modPassLv = passLv % 5;
            //第2关结束选择天赋，第5关结束选择天赋
            return (modPassLv == 2 || modPassLv == 0) && passLv > 0;
        }

        /// <summary>
        /// 记录挑战时间
        /// </summary>
        void RecordTime() {
            BattleData.data.SetFloatMark(CUR_LEVEL_TIME, _curLevelTime);
            BattleData.data.SetFloatMark(CUR_PLAYER_TIME, _curPlayerTime);
        }

        /// <summary>
        /// 读取记录的挑战时间
        /// </summary>
        void ReadTime() {
            _lastTime = Time.realtimeSinceStartup;
            _levelTimeMax = FormulaUtil.CalcLevelCountdownTime(RGGameProcess.Inst.this_index, BattleData.data.isBadass);
            var thisLvIdx = BattleData.data.levelIndex;
            if (thisLvIdx % 5 == 0) {
                _curLevelTime = 0f;
                _curPlayerTime = 0f;
            } else {
                _curLevelTime = CurLevelTime;
                _curPlayerTime = CurPlayerTime;
            }
        }


        public override void OnDestroy() {
            base.OnDestroy();
            RemoveEvent();
            _timer?.Cancel();
        }


        public override void OnEnemyCreate(RGEController rgeCtrl) {
            base.OnEnemyCreate(rgeCtrl);
            if (null == rgeCtrl) {
                return;
            }

            if (_geneEnemyCount >= _maxGeneEnemyCount) {
                return;
            }

            if (rgeCtrl.GetComponent<EnemySplitHandler.SplitOrigin>() != null) {
                return;
            }

            if (!rgRandom.RandomShoot(0, 100, _geneWeight)) {
                return;
            }

            _geneEnemyCount++;
            var gene = _geneLotteryMachine.GetResult();
            rgeCtrl.gameObject.AddComponent<DefenceEnemyGene>().gene = gene;
        }

        public override void OnEnemyDead(RGEController rgeCtrl) {
            var tmpRecoverTimeFactor = ModeLoopTravelConfig.Config.KILL_NORMAL_ENEMY_RECOVER_FACTOR;
            if (null == rgeCtrl) {
                _curPlayerTime += tmpRecoverTimeFactor * _levelTimeMax;
                return;
            }

            if (rgeCtrl.temp_enemy) {
                return;
            }

            if (rgeCtrl.intensive) {
                tmpRecoverTimeFactor = ModeLoopTravelConfig.Config.KILL_BADASSS_ENEMY_RECOVER_FACTOR;
            }

            _curPlayerTime += tmpRecoverTimeFactor * _levelTimeMax;
        }

        // int LoopTravelHurtCallback(GameObject source, int dmg, bool addDmg) {
        //     var finalDmg = dmg;
        //
        //     return finalDmg;
        // }


        public override float GetEnemyCountFactor() {
            return 1 + MapManager.LoopCount * (BattleData.data.isBadass ? 0.25f : 0.5f);
        }

        public override int GetExtraBadassWeight() {
            return 10 * MapManager.LoopCount;
        }

        public override void OnNextLevelServer() {
            base.OnNextLevelServer();
            if (GameUtil.IsSingleGame() || GameUtil.IsMultiGame() && NetControllerManager.Inst.isServer) {
                RecordTime();
            }

            var subFactorList = BattleData.data.FactorExtraDataDic.Where(extraData => BattleData.data.levelIndex > extraData.Value.startMakeEffectLevelIndex).Select(extraData => extraData.Key).ToList();
            foreach (var factor in subFactorList) {
                BattleData.data.SubFactorRemainBanLevel(factor);
            }
        }

        void OnShowPauseWindow(ShowPauseWindow eParam) {
            if (null == uiCountDown) {
                return;
            }

            uiCountDown.SetActive(!eParam.show);
        }


        public const string LEVEL_USED_CRYSTAL_COUNT = "level_use_crystal_count";
        public const string DEFEAT_FEL_LORD_CRYSTAL = "defeat_fel_lord_crystal";


        public int GetCurCrystalCount() {
            var pressureLv = GameUtil.ModeLoopTravelPressureLv();
            var curCrystalCount = FormulaUtil.CalcCrystalCount(pressureLv, BattleData.data.isBadass);
            curCrystalCount -= BattleData.data.GetMark(LEVEL_USED_CRYSTAL_COUNT);
            curCrystalCount += BattleData.data.GetMark(DEFEAT_FEL_LORD_CRYSTAL);
            return curCrystalCount;
        }

        public bool IsCrystalEnough(int needCount) {
            var curCount = GetCurCrystalCount();
            return curCount >= needCount;
        } 
        
        public void ConsumeCrystal(int consumeCount) {
            BattleData.data.AddMark(LEVEL_USED_CRYSTAL_COUNT, consumeCount);
        }
    }
}