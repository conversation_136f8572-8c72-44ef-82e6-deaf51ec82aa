------------------------------------------------------
PlayMaker - Visual Scripting for Unity
------------------------------------------------------

NOTE: The PlayMaker/Asset Store license is for a single user at a time. If multiple people use PlayMaker, you need multiple licenses!

------------------------------------------------------
INSTALLATION:
------------------------------------------------------

PLEASE BACKUP PROJECTS BEFORE IMPORTING OR UPDATING PLAYMAKER!!

Import the latest version of PlayMaker from the Asset Store or Hutong Games Store.
This imports the PlayMaker Installer and various update tools.

To install <PERSON><PERSON><PERSON> select Install PlayMaker from the PlayMaker Welcome Screen.
The Welcome Screen should launch automatically, but it can also be found here:
Unity Main Menu > PlayMaker.

If the project has errors the Welcome Screen may not be available. Ideally, you should fix errors in the project first, but you can also manually import the PlayMaker unitypackage here: Assets\PlayMaker\Editor\Install\

Download Sample Scenes at www.hutonggames.com/tutorials.html

NOTE: IMPORT SAMPLE SCENES INTO A NEW PROJECT TO AVOID OVERWRITING YOUR PROJECT FILES!!

------------------------------------------------------
UPDATE NOTES:
------------------------------------------------------

To Update PlayMaker:

- Backup your project and close the PlayMaker editor.
- Import the latest version of PlayMaker from the Asset Store.
- Select Install PlayMaker from the PlayMaker Welcome Screen (see above)
- Restart Unity.

Obsolete Actions: 

After updating, you may get errors about obsolete actions in your saved projects. Obsolete actions should still work, but it is recommended that you upgrade them to the suggested action. You can turn off this check in Preferences. Obsolete actions are also hidden by default in the Action Browser. You can toggle this setting in the Action Browser settings menu.

Updated Actions:

After loading a project/scene you may get a notification in the Unity Console that actions have changed. Open the PlayMaker Editor Log to get more info on these changes. Most of the time Actions are updated automatically, and any new parameters are given default values. However, occasionally an action is incompatible and you will need to edit its settings. The Console window lets you quickly find and fix these Actions.

------------------------------------------------------

Thanks and have fun with Playmaker!

More info at www.hutonggames.com