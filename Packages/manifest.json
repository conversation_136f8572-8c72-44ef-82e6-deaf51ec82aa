{"scopedRegistries": [{"name": "凉屋内部仓库", "url": "http://repo.chilly.room/repository/unity-hosted", "scopes": ["com.chillyroom", "com.rlabrecque.steamworks.net", "com.neuecc.unirx", "<PERSON><PERSON><PERSON><PERSON>", "com.chillyroom.fair", "com.chillyroom.sdk.wrapper", "com.gilzoide", "me.qian<PERSON>", "extensions.unity.imageloader", "com.chillyroom.services"]}, {"name": "package.openupm.com", "url": "https://package.openupm.com", "scopes": ["com.coffee.ui-particle", "com.cysharp.unitask", "com.greener-games.sharp-zip-lib", "com.greener-games.unity-extensions", "com.laicasaane.miniexcel", "com.needle.compilation-visualizer", "com.openupm", "com.svermeulen.asyncawaitutil", "com.unity.editoriterationprofiler"]}], "dependencies": {"com.chillyroom.fair.postprocessing.borderaurora": "1.0.1", "com.chillyroom.fair.sprites.flowloop": "1.1.1", "com.chillyroom.fair.spritesflowobstacle": "1.0.0", "com.chillyroom.fair.spritesgradation": "1.0.0", "com.chillyroom.fair.spritespixelbeachwave": "1.0.0", "com.chillyroom.fair.spritespixelsnapswing": "1.1.0", "com.chillyroom.fair.ui.image.withparticlert": "2.0.0", "com.chillyroom.im": "0.1.5", "com.chillyroom.rpc": "2.0.14", "com.chillyroom.sdk.wrapper.androidoaid": "1.0.1", "com.chillyroom.sdk.wrapper.bytedanceanalyse": "1.0.1", "com.chillyroom.sdk.wrapper.pdns": "1.0.4", "com.chillyroom.sdk.wrapper.realname": "1.2.15", "com.chillyroom.sdk.wrapper.tradplusandroid": "0.4.32", "com.chillyroom.sdk.wrapper.ump": "1.1.0", "com.chillyroom.services.accounts": "1.29.75", "com.chillyroom.services.migration-tool": "0.0.16", "com.chillyroom.webrtc": "13.1.3", "com.coffee.ui-effect": "https://github.com/mob-sakai/UIEffect.git?path=Packages/src", "com.coffee.ui-particle": "4.6.2", "com.cysharp.unitask": "2.3.1", "com.esotericsoftware.spine.spine-csharp": "https://github.com/EsotericSoftware/spine-runtimes.git?path=spine-csharp/src#4.2", "com.esotericsoftware.spine.spine-unity": "https://github.com/EsotericSoftware/spine-runtimes.git?path=spine-unity/Assets/Spine#4.2", "com.greener-games.sharp-zip-lib": "1.3.303", "com.laicasaane.miniexcel": "1.26.5", "com.unity.2d.sprite": "1.0.0", "com.unity.2d.tilemap": "1.0.0", "com.unity.assetbundlebrowser": "1.7.0", "com.unity.ext.nunit": "1.0.6", "com.unity.ide.rider": "3.0.34", "com.unity.ide.visualstudio": "2.0.22", "com.unity.ide.vscode": "1.2.5", "com.unity.memoryprofiler": "1.1.1", "com.unity.mobile.notifications": "2.3.2", "com.unity.recorder": "4.0.3", "com.unity.shadergraph": "14.0.11", "com.unity.test-framework": "1.1.33", "com.unity.textmeshpro": "3.0.7", "com.unity.timeline": "1.7.6", "com.unity.modules.ai": "1.0.0", "com.unity.modules.androidjni": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.cloth": "1.0.0", "com.unity.modules.director": "1.0.0", "com.unity.modules.imageconversion": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.particlesystem": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.modules.physics2d": "1.0.0", "com.unity.modules.screencapture": "1.0.0", "com.unity.modules.terrain": "1.0.0", "com.unity.modules.terrainphysics": "1.0.0", "com.unity.modules.tilemap": "1.0.0", "com.unity.modules.ui": "1.0.0", "com.unity.modules.uielements": "1.0.0", "com.unity.modules.umbra": "1.0.0", "com.unity.modules.unityanalytics": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.unitywebrequestassetbundle": "1.0.0", "com.unity.modules.unitywebrequestaudio": "1.0.0", "com.unity.modules.unitywebrequesttexture": "1.0.0", "com.unity.modules.unitywebrequestwww": "1.0.0", "com.unity.modules.vehicles": "1.0.0", "com.unity.modules.video": "1.0.0", "com.unity.modules.vr": "1.0.0", "com.unity.modules.wind": "1.0.0", "com.unity.modules.xr": "1.0.0"}}