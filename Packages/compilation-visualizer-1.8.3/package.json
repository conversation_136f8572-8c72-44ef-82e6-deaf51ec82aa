{"name": "com.needle.compilation-visualizer", "displayName": "Compilation Visualizer", "version": "1.8.3", "author": {"name": "<PERSON>le", "email": "<EMAIL>", "url": "https://needle.tools"}, "repository": {"type": "git", "url": "https://github.com/needle-tools/compilation-visualizer.git"}, "unity": "2018.4", "description": "Provides console logging and a debug window for Assembly compilation. Allows to visualize compilation times and dependencies between assemblies.", "keywords": ["compilation", "visualization", "editor", "debugging", "tests"], "type": "tool", "dependencies": {}, "samples": []}